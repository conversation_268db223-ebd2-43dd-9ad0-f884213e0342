package com.sbtr.workflow.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.dto.FlowModelDto;
import com.sbtr.workflow.dto.FormBtnListDto;
import com.sbtr.workflow.dto.FormFieldListDto;
import com.sbtr.workflow.dto.ModleDto;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.model.ActFormConfigure;
import com.sbtr.workflow.model.ActMyForm;
import com.sbtr.workflow.model.ActMyModel;
import com.sbtr.workflow.model.ActMyNodeButton;
import com.sbtr.workflow.model.ActMyNodeCode;
import com.sbtr.workflow.model.ActMyNodeField;
import com.sbtr.workflow.model.ActMyNodeNotice;
import com.sbtr.workflow.service.ActFormConfigureService;
import com.sbtr.workflow.service.ActMyFormService;
import com.sbtr.workflow.service.ActMyModelService;
import com.sbtr.workflow.service.ActMyNodeButtonService;
import com.sbtr.workflow.service.ActMyNodeCodeService;
import com.sbtr.workflow.service.ActMyNodeFieldService;
import com.sbtr.workflow.service.ActMyNodeNoticeService;
import com.sbtr.workflow.service.ApiFlowableBpmnModelService;
import com.sbtr.workflow.service.ApiFlowableModelService;
import com.sbtr.workflow.service.BusinessSystemDataService;
import com.sbtr.workflow.utils.BaseUtils;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.Result;
import com.sbtr.workflow.utils.StringUtil.StringUtils;
import com.sbtr.workflow.vo.ActDeModelVo;
import com.sbtr.workflow.vo.FlowNodeFieldVo;
import com.sbtr.workflow.vo.FormDesignVo;
import com.sbtr.workflow.vo.ModelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.RepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 流程表单模型
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-21 02:37:50
 */
@Api(tags = {"流程表单模型"})
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH + "/flowModel")
public class ActMyModelController extends WorkflowBaseController {
    @Resource
    public BusinessSystemDataService businessSystemDataService;
    @Autowired
    private ActMyModelService flowModelService;

    @Autowired
    private ActMyNodeCodeService actMyNodeCodeService;

    @Autowired
    private ApiFlowableBpmnModelService apiFlowableBpmnModelService;

    @Autowired
    private ActMyNodeButtonService flowNodeButtonService;

    @Autowired
    private ActMyNodeFieldService flowNodeFieldService;

    @Autowired
    private ApiFlowableModelService apiFlowableModelService;

    @Autowired
    private ActMyNodeNoticeService flowNodeNoticeService;

    @Value("${spring.datasource.dynamic.datasource.master.driver-class-name:null}")
    public String driverClassName;

    @ResponseBody
    @RequestMapping(value = "/getDetailByUuid")
    public Object getDetailByUuid(String uuid) {
        ActMyModel flowModel = flowModelService.selectByPrimaryKey(uuid);
        return flowModel;
    }

    @Autowired
    private ActFormConfigureService actFormConfigureService;

    /**
     * @MethodName save
     * @Description 表单与流程一起保存
     * @Param flowModelDto
     * @Return java.lang.Object
     * <AUTHOR> @ sbtr.com>
     * @Date 2020-10-21 11:13
     */
    @Log(title = "流程表单模型-保存", module = LogModule.WORKFLOW, businessType = BusinessType.INSERT, method = "getLogMessage")
    @ApiOperation(value = "保存表单流程数据")
    @ApiImplicitParam(name = "flowModelDto", value = "flowModelDto", required = true, dataType = "FlowModelDto")
    @Transactional
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Object save(@ModelAttribute FlowModelDto flowModelDto) {
        if (StrUtil.hasBlank(flowModelDto.getModelId(), flowModelDto.getModelName())) {
            return failure("请检查【基础配置】中的流程名称，流程标识不能为空！！！");
        }
        if (StrUtil.isBlank(flowModelDto.getPermissionType())) {
            return failure("请先【权限配置中】配置权限设置！！！");
        }
        if (StrUtil.isBlank(flowModelDto.getFormFieldList()) || "[]".equals(flowModelDto.getFormFieldList())) {
//            return failure("请设计【用户任务】节点并在其对应的【表单配置】中添加表单！！！");
        }
        if (((!"all".equals(flowModelDto.getPermissionType()) && StrUtil.isBlank(flowModelDto.getPermissionValue()))) && (!"cannotInitiate".equals(flowModelDto.getPermissionType()) && StrUtil.isBlank(flowModelDto.getPermissionValue()))) {
            return failure("请先【权限配置中】配置权限设置！！！");
        }
        //根据modelKey去act_de_model查询数据
        List<ActDeModelVo> actDeModelVo = apiFlowableModelService.getDataByModelKey(flowModelDto.getModelKey());
        if (1 == actDeModelVo.size()) {
            return failure("模型key已存在,请检查或者关闭页面重新设计该流程！！！");
        }
        if (actDeModelVo.size() >= 2) {
            return failure("模型key:" + flowModelDto.getModelKey() + "，在act_de_model表存存在多条数据，请联系管理员！！！");
        }

        setLogMessage(flowModelDto.getModelName(), "");
        //按钮数据
        String btnListStr = flowModelDto.getFormBtnList().replace("\\", "");
        List<FormBtnListDto> formBtnLists = JSONObject.parseArray(btnListStr, FormBtnListDto.class);
        if (CollUtil.isEmpty(formBtnLists)) {
            return failure("按钮数据不能为空！！！");
        }

        //第二次保存走updateData
        //根据模型key以及流程定义Id为空查找数据,只会有一条数据如果有多条则提示有问题
        List<ActMyModel> lists = flowModelService.getListByModelKeyAndProcdefIdIsNull(flowModelDto.getModelKey());
        if (CollUtil.isNotEmpty(lists) && lists.size() > 1) {
            return failure("数据出现异常,请联系管理员进行删除！！！");
        }
        //判断是自己写的表单还是通过拖动出来的表单  1自己写的表单  2拖动设计的
        if (StrUtil.isNotBlank(flowModelDto.getFormFieldList()) && flowModelDto.getFormFieldList().length() > 2) {
            //开始校验 表单必须都是统一拖拉 或者 自己开发的页面
            String strs = flowModelDto.getFormFieldList().replace("\\", "");
            List<FormFieldListDto> formFieldListDtos = JSONObject.parseArray(strs, FormFieldListDto.class);
            if (CollUtil.isNotEmpty(formFieldListDtos)) {
                List<String> distinctNames = formFieldListDtos.stream().map(FormFieldListDto::getFormUuid).distinct().collect(Collectors.toList());
                if (1 != distinctNames.size()) {
                    return failure("请检查【表单配置】中添加表单必须每个任务节点都一样！！！");
                }
            }
            flowModelDto.setModelType(formFieldListDtos.get(0).getModelType());
        }
        if (CollUtil.isNotEmpty(lists) && lists.size() == 1) {
            ModleDto modleDto = new ModleDto();
            modleDto.setModelType(flowModelDto.getModelType());
            modleDto.setFormTableName(flowModelDto.getFormTableName());
            modleDto.setUuid(lists.get(0).getUuid());
            modleDto.setFormFieldList(flowModelDto.getFormFieldList());
            modleDto.setFormNoticeList(flowModelDto.getFormNoticeList());
            modleDto.setPermissionType(flowModelDto.getPermissionType());
            modleDto.setPermissionValue(flowModelDto.getPermissionType());
            modleDto.setActDeModelName(flowModelDto.getActDeModelName());
            modleDto.setFormDesign(flowModelDto.getFormJson());
            modleDto.setActDeModelKey(flowModelDto.getModelKey());
            modleDto.setActDeModelId(flowModelDto.getModelId());
            modleDto.setProcdefId(lists.get(0).getProcdefId());
            modleDto.setFormBtnList(flowModelDto.getFormBtnList());
            modleDto.setFlowDesign(flowModelDto.getFlowJson());
            modleDto.setProcessModelType(flowModelDto.getProcessModelType());
            return updateData(modleDto);
        }

        try {
            //1.处理流程数据
            ModelVo modelVo = new ModelVo();
            modelVo.setProcessId(flowModelDto.getModelId());
            modelVo.setProcessName(flowModelDto.getModelName());
            modelVo.setXml(flowModelDto.getFlowJson());
            modelVo.setProcessModelType(flowModelDto.getModelType());
            Object object = apiFlowableBpmnModelService.addModel(modelVo, flowModelDto.getFormFieldList(), flowModelDto.getFormBtnList());
            if (((HashMap) object).get("statusCode").equals(300)) {
                return failure("" + ((HashMap) object).get("message"));
            }
            String modelKey = ((HashMap) object).get("modelKey").toString();
            String modelIds = ((HashMap) object).get("modelId").toString();
            //2.处理流程与form表单数据
            ActMyModel flowModel = new ActMyModel();
            flowModel.setActDeModelId(modelIds);
            flowModel.setActDeModelKey(modelKey);
            flowModel.setFlowDesign(flowModelDto.getFlowJson());
            flowModel.setFormTableName(flowModelDto.getFormTableName());
            flowModel.setFormModel(flowModelDto.getFormModel());
            flowModel.setActDeModelName(flowModelDto.getModelName());
            flowModel.setModelType(flowModelDto.getModelType());
            flowModel.setPermissionType(flowModelDto.getPermissionType());
            flowModel.setPermissionValue(flowModelDto.getPermissionValue());
            flowModel.setFormUuid(flowModelDto.getActFormConfigureUuid());
            flowModel.setSign(flowModel.getSign());
            String uuid = BaseUtils.UUIDGenerator();
            flowModel.setUuid(uuid);
            int result = flowModelService.insertSelective(getSaveData(flowModel));
            //插入按钮数据
            ActMyNodeButton flowNodeButton = null;
            ActFormConfigure actFormConfigure = actFormConfigureService.selectByPrimaryKey(StringUtils.emptyToNull(flowModelDto.getActFormConfigureUuid()));
            for (int i = 0; i < formBtnLists.size(); i++) {
                flowNodeButton = new ActMyNodeButton();
                flowNodeButton.setActDeModelId(modelIds);
                flowNodeButton.setActDeModelKey(modelKey);
                flowNodeButton.setFlowModelUuid(uuid);
                flowNodeButton.setNodeId(formBtnLists.get(i).getId());
                flowNodeButton.setNodeButtonCode(formBtnLists.get(i).getNodeButtonCode());
                flowNodeButton.setNodeButtonName(formBtnLists.get(i).getNodeButtonName());
                flowNodeButton.setUuid(BaseUtils.UUIDGenerator());
                //系统表单
                if ("1".equals(flowModelDto.getModelType())) {
                    if (ObjectUtil.isNotEmpty(actFormConfigure)) {
                        flowNodeButton.setNodeFormPath(actFormConfigure.getNodeFormPath());
                        flowNodeButton.setAppPagePath(actFormConfigure.getAppPagePath());
                        flowNodeButton.setNodeFormEditPath(actFormConfigure.getNodeFormEditPath());
                        flowNodeButton.setNodeFormUpdatePath(actFormConfigure.getNodeFormUpdatePath());
                        flowNodeButton.setNodeFormSavePath(actFormConfigure.getNodeFormSavePath());
                        flowNodeButton.setFormUuid(actFormConfigure.getUuid());
                        flowNodeButton.setTablename(actFormConfigure.getTablename());
                        flowNodeButton.setPrimarykey(actFormConfigure.getPrimarykey());
                    }
                }
                flowNodeButton.setWhetherUpdate(formBtnLists.get(i).getWhetherUpdate());
                //系统表单 自定表单uuid
                String strs = flowModelDto.getFormFieldList().replace("\\", "");
                List<FormFieldListDto> formFieldListDtos = JSONObject.parseArray(strs, FormFieldListDto.class);
                for (int j = 0; j < formFieldListDtos.size(); j++) {
                    if (formFieldListDtos.get(j).getId().equals(flowNodeButton.getNodeId())) {
                        flowNodeButton.setFormUuid(formFieldListDtos.get(j).getFormUuid());
                        break;
                    }
                }
                flowNodeButtonService.insertSelective(getSaveData(flowNodeButton));
            }

            //插入字段可编辑
            if ("2".equals(flowModelDto.getModelType())) {
                if (StrUtil.isNotBlank(flowModelDto.getFormFieldList()) && flowModelDto.getFormFieldList().length() > 2) {
                    String strs = flowModelDto.getFormFieldList().replace("\\", "");
                    List<FormFieldListDto> formFieldListDtos = JSONObject.parseArray(strs, FormFieldListDto.class);
                    ActMyNodeField flowNodeField = null;
                    for (int i = 0; i < formFieldListDtos.size(); i++) {
                        flowNodeField = new ActMyNodeField();
                        flowNodeField.setModelKey(modelKey);
                        flowNodeField.setModelId(modelIds);
                        flowNodeField.setFlowModelUuid(uuid);
                        flowNodeField.setIsEdit(formFieldListDtos.get(i).getIsEdit());
                        flowNodeField.setIsLook(formFieldListDtos.get(i).getIsLook());
                        flowNodeField.setId(formFieldListDtos.get(i).getId());
                        flowNodeField.setFiled(formFieldListDtos.get(i).getFiled());
                        flowNodeField.setFiledName(formFieldListDtos.get(i).getFiledName());
                        flowNodeField.setFormJson(flowModelDto.getActFormConfigureUuid());
                        flowNodeField.setFormUuid(formFieldListDtos.get(i).getFormUuid());
                        flowNodeField.setModelType(formFieldListDtos.get(i).getModelType());
                        flowNodeField.setFieldIndex(formFieldListDtos.get(i).getFieldIndex());
                        flowNodeField.setFormLayout(formFieldListDtos.get(i).getFormLayout());
                        flowNodeFieldService.insertSelective(getSaveData(flowNodeField));
                    }
                }
            }
            //插入处理节点通知
            if (StrUtil.isNotBlank(flowModelDto.getFormNoticeList()) && flowModelDto.getFormNoticeList().length() > 2) {
                String strs = flowModelDto.getFormNoticeList().replace("\\", "");
                List<ActMyNodeNotice> formFieldListDtos = JSONObject.parseArray(strs, ActMyNodeNotice.class);
                for (int i = 0; i < formFieldListDtos.size(); i++) {
                    formFieldListDtos.get(i).setActDeModelKey(modelKey);
                    formFieldListDtos.get(i).setActDeModelId(modelIds);
                    formFieldListDtos.get(i).setFlowModelUuid(uuid);
                    flowNodeNoticeService.insertSelective(getSaveData(formFieldListDtos.get(i)));
                }
            }
            //6.节点权限码保存
            List<ActMyNodeCode> actMyNodeCodes = JSONObject.parseArray(flowModelDto.getNodeCodeList(), ActMyNodeCode.class);
            actMyNodeCodeService.saveOrUpdateNodeCode(actMyNodeCodes, modelKey, "");
            //新建的流程直接部署版本
//            apiFlowableModelService.processDeployment(modelIds, flowModelDto.getCategory(),"");
            return result > 0 ? success("保存成功！") : failure("保存失败！");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            logger.error("流程保存出现异常，原因为{}", e.getMessage());
            return failure("流程保存出现异！！！");
        }
    }

    @Autowired
    private RepositoryService repositoryService;

    /**
     * @MethodName save
     * @Description 表单与流程一起 获取详情
     * @Param modelKey  模型key
     * @Param procdefId  流程定义Id
     * @Param type  区分发起流程与 模型点击办理
     * @Return java.lang.Object
     * <AUTHOR> @ sbtr.com>
     * @Date 2020-10-21 11:13
     */
    @ResponseBody
    @RequestMapping(value = "/getMajorModelByKey", method = RequestMethod.POST)
    public Object getMajorModelByKey(String modelKey, String procdefId) {
        HashMap<String, Object> map = new HashMap<>();
        if (StrUtil.isBlank(modelKey)) {
            return failure("模型key/流程定义Id不能为空,请检查！！！");
        }
        Example example = new Example(ActMyModel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modelKey);
        List<ActMyModel> lists = flowModelService.getMajorModelByKey(modelKey);
        return lists;
    }

    @ResponseBody
    @RequestMapping(value = "/getDetailByModelKey", method = RequestMethod.POST)
    public Object getDetailByModelKey(String modelKey, String procdefId, String type) {
        HashMap<String, Object> map = new HashMap<>();
        if (StrUtil.isBlank(modelKey)) {
            return failure("模型key不能为空,请检查！！！");
        }
        Example example = new Example(ActMyModel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modelKey);
        if (!StringUtils.isNullOrEmpty(procdefId)) {
            criteria.andEqualTo("procdefId", procdefId);
        }
        List<ActMyModel> lists = flowModelService.selectByExample(example);
        if (CollectionUtils.isEmpty(lists)) {
            return failure("查找不到数据,请删除该数据或速度联系管理员！！！");
        }
        List<FlowNodeFieldVo> flowNodeField = null;
        try {
            if (!StrUtil.isBlank(procdefId)) {
                String nodeId = getNodeIdByProcdefId(procdefId);
                if (StrUtil.isBlank(nodeId)) {
                    return failure("查找不到任务节点Id,请联系管理员！！！");
                }
                //处理第一个任务节点可编辑
                flowNodeField = flowNodeFieldService.selectByModelKeyAndId(modelKey, nodeId, procdefId);
                map.put("flowNodeField", flowNodeField);
            } else {
                map.put("flowNodeField", null);
            }
        } catch (Exception e) {
            map.put("flowNodeField", null);
        }
        if (!"mode".equals(type)) {
            if (StringUtils.isNullOrEmpty(type)) {
                if (StrUtil.isBlank(lists.get(0).getPermissionType())) {
                    return failure("运行启动流程权限控制不能为空！！！");
                }
                //判断用户是否拥有启动流程的权限
                Boolean beStarted = flowModelService.canTheProcessBeStarted(businessSystemDataService.getUserNameId(), lists.get(0).getPermissionType(), lists.get(0).getPermissionValue());
                if (!beStarted) {
                    return failure("对不起，您没有权限发起该流程请联系管理员！！！");
                }
            }
            //判断是自己写的表单还是通过拖动出来的表单  1自己写的表单  2拖动设计的
            if ("1".equals(lists.get(0).getModelType())) {
                //判断是外置表单 自己手写的页面
                Example examples = new Example(ActMyNodeButton.class);
                Example.Criteria criterias = examples.createCriteria();
                criterias.andEqualTo("actDeModelKey", modelKey);
                String nodeId = getNodeIdByProcdefId(procdefId);
                criterias.andEqualTo("nodeId", nodeId);
                if (org.apache.commons.lang.StringUtils.isNotBlank(procdefId)) {
                    criterias.andEqualTo("procdefId", procdefId);
                }
                List<ActMyNodeButton> flowNodeButtons = flowNodeButtonService.selectByExample(examples);
                ActFormConfigure actFormConfigure = actFormConfigureService.selectByPrimaryKey(StringUtils.emptyToNull(flowNodeButtons.get(0).getFormUuid()));
                if (ObjectUtil.isEmpty(actFormConfigure)) {
                    return failure("第一个任务节点未绑定系统表单，请检查");
                }
                if (ObjectUtil.isNotEmpty(actFormConfigure)) {
                    lists.get(0).setFormDesign(actFormConfigure.getNodeFormPath());
                    lists.get(0).setAppPagePath(actFormConfigure.getAppPagePath());
                }

            }
            //判断是自己写的表单还是通过拖动出来的表单  1自己写的表单  2拖动设计的
            if ("2".equals(lists.get(0).getModelType())) {
                String formJson = flowModelService.getDesignJson(flowNodeField.get(0).getFormUuid());
                if (StrUtil.isEmpty(formJson)) {
                    return failure("第一个任务节点未绑定自定义表单，请检查");
                }
                lists.get(0).setFormDesign(formJson);
                lists.get(0).setFormUuid(flowNodeField.get(0).getFormUuid());
            }
        }
        //流程标题先默认
        String title = businessSystemDataService.getUserName();
        map.put("title", title);
        map.put("statusCode", 200);
        map.put("flowModel", lists.get(0));
        return map;
    }

    private String getNodeIdByProcdefId(String procdefId) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(procdefId);
        Collection<FlowElement> flowElements = bpmnModel.getMainProcess().getFlowElements();
        //获取一个人任务节点Id
        if (ObjectUtil.isEmpty(flowElements.stream().filter(flowElement -> flowElement instanceof UserTask).findFirst().get())) {
            return "";
        } else {
            return flowElements.stream().filter(flowElement -> flowElement instanceof UserTask).findFirst().get().getId();
        }
    }


    /**
     * @MethodName updateData
     * @Description 表单与流程更新
     * @Param modelKey  模型key
     * @Return java.lang.Object
     * <AUTHOR> @ sbtr.com>
     * @Date 2020-10-21 11:13
     */
    @Log(title = "流程表单模型-更新", module = LogModule.WORKFLOW, businessType = BusinessType.UPDATE, method = "getLogMessage")
    @ApiOperation(value = "更新表单流程数据")
    @ApiImplicitParam(name = "modleDto", value = "modleDto", required = true, dataType = "ModleDto")
    @Transactional
    @ResponseBody
    @RequestMapping(value = "/updateData", method = RequestMethod.POST)
    public Object updateData(@Valid ModleDto modleDto) {
        if (StrUtil.hasBlank(modleDto.getActDeModelId(), modleDto.getActDeModelName())) {
            return failure("请检查基础配置的流程名称，流程标识不能为空！！！");
        }
        if (StrUtil.isBlank(modleDto.getPermissionType())) {
            return failure("请先配置权限设置！！！");
        }
        if (StrUtil.isBlank(modleDto.getFormFieldList()) || "[]".equals(modleDto.getFormFieldList())) {
//            return failure("请先设计【用户任务】并在对于在【表单配置】中添加表单！！！");
        }
        if (((!"all".equals(modleDto.getPermissionType()) && StrUtil.isBlank(modleDto.getPermissionValue()))) && (!"cannotInitiate".equals(modleDto.getPermissionType()) && StrUtil.isBlank(modleDto.getPermissionValue()))) {
            return failure("请先配置权限设置！！！");
        }
        //判断是自己写的表单还是通过拖动出来的表单  1自己写的表单  2拖动设计的
        if (StrUtil.isNotBlank(modleDto.getFormFieldList()) && modleDto.getFormFieldList().length() > 2) {
            //开始校验 表单必须都是统一拖拉 或者 自己开发的页面
            String strs = modleDto.getFormFieldList().replace("\\", "");
            List<FormFieldListDto> formFieldListDtos = JSONObject.parseArray(strs, FormFieldListDto.class);
            if (CollUtil.isNotEmpty(formFieldListDtos)) {
                List<String> distinctNames = formFieldListDtos.stream().map(FormFieldListDto::getFormUuid).distinct().collect(Collectors.toList());
                if (1 != distinctNames.size()) {
                    return failure("请检查【表单配置】中添加表单必须每个任务节点都一样！！！");
                }
            }
            modleDto.setModelType(formFieldListDtos.get(0).getModelType());
        }

        setLogMessage(modleDto.getActDeModelName(), "");
        String procdefId = modleDto.getProcdefId();
        //1.流程model
        ModelVo modelVo = new ModelVo();
        modelVo.setProcessId(modleDto.getActDeModelId());
        modelVo.setProcessName(modleDto.getActDeModelName());
        modelVo.setXml(modleDto.getFlowDesign());
        modelVo.setProcessModelType(modleDto.getProcessModelType());
        Object object = apiFlowableBpmnModelService.addModel(modelVo, modleDto.getFormFieldList(), modleDto.getFormBtnList());
        if (((HashMap) object).get("statusCode").equals(300)) {
            return failure(Convert.toStr(((HashMap) object).get("message")));
        }
        String modelKey = Convert.toStr(((HashMap) object).get("modelKey"));
        String modelIds = Convert.toStr(((HashMap) object).get("modelId"));
        String modelxmlName = Convert.toStr(((HashMap) object).get("modelName"));
        //2.流程与表单
        ActMyModel flowModel = new ActMyModel();
        flowModel.setUuid(modleDto.getUuid());
        flowModel.setActDeModelName(modelxmlName);
        flowModel.setActDeModelId(modelIds);
        flowModel.setFormModel(modleDto.getFormModel());
        flowModel.setFormDesign(modleDto.getFormDesign());
        flowModel.setFlowDesign(modleDto.getFlowDesign());
        flowModel.setFormTableName(modleDto.getFormTableName());
        flowModel.setActDeModelKey(modelKey);
        flowModel.setProcdefId(procdefId);
        flowModel.setModelType(modleDto.getModelType());
        flowModel.setPermissionType(modleDto.getPermissionType());
        flowModel.setPermissionValue(modleDto.getPermissionValue());
        flowModel.setSign(modleDto.getSign());
        Integer integer = flowModelService.updateByPrimaryKeySelective(getUpdateData(flowModel));


//        if ("2".equals(modleDto.getModelType())) {
        //2.2 更新表单设计表数据
//            Integer integer1 = flowModelService.updateFormFieldJson(modleDto.getFormTableName(), modleDto.getFormDesign(), modleDto.getFormModel());
//        }


        //3.流程与按钮
        //直接根据模型key删除所有数据
        Example example = new Example(ActMyNodeButton.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modleDto.getActDeModelKey());
        if (!StringUtils.isNullOrEmpty(procdefId)) {
            criteria.andEqualTo("procdefId", procdefId);
        }
        flowNodeButtonService.deleteByExample(example);
        //再新增qjwzbd
        List<FormBtnListDto> formBtnLists = JSONObject.parseArray(modleDto.getFormBtnList(), FormBtnListDto.class);
        List<ActMyNodeButton> list = new ArrayList<>();
        ActMyNodeButton flowNodeButton = null;
        if (StringUtils.isNullOrEmpty(formBtnLists.get(0).getId())) {
            return failure("节点Id不能为空");
        }

        ActFormConfigure actFormConfigure = actFormConfigureService.selectByPrimaryKey(StringUtils.emptyToNull(modleDto.getActFormConfigureUuid()));

        for (int i = 0; i < formBtnLists.size(); i++) {
            flowNodeButton = new ActMyNodeButton();
            flowNodeButton.setActDeModelId(modleDto.getActDeModelId());
            flowNodeButton.setActDeModelKey(modleDto.getActDeModelKey());
            flowNodeButton.setFlowModelUuid(modleDto.getUuid());
            flowNodeButton.setNodeId(formBtnLists.get(i).getId());
            flowNodeButton.setNodeButtonCode(formBtnLists.get(i).getNodeButtonCode());
            flowNodeButton.setNodeButtonName(formBtnLists.get(i).getNodeButtonName());
            flowNodeButton.setUuid(BaseUtils.UUIDGenerator());
            flowNodeButton.setProcdefId(procdefId);

            if ("1".equals(modleDto.getModelType())) {
                if (ObjectUtil.isNotEmpty(actFormConfigure)) {
                    flowNodeButton.setNodeFormPath(actFormConfigure.getNodeFormPath());
                    flowNodeButton.setAppPagePath(actFormConfigure.getAppPagePath());
                    flowNodeButton.setNodeFormEditPath(actFormConfigure.getNodeFormEditPath());
                    flowNodeButton.setNodeFormUpdatePath(actFormConfigure.getNodeFormUpdatePath());
                    flowNodeButton.setNodeFormSavePath(actFormConfigure.getNodeFormSavePath());
                    flowNodeButton.setFormUuid(actFormConfigure.getUuid());
                    flowNodeButton.setTablename(actFormConfigure.getTablename());
                    flowNodeButton.setPrimarykey(actFormConfigure.getPrimarykey());
                }
            }
            flowNodeButton.setWhetherUpdate(formBtnLists.get(i).getWhetherUpdate());
            //系统表单 自定表单uuid
            String strs = modleDto.getFormFieldList().replace("\\", "");
            List<FormFieldListDto> formFieldListDtos = JSONObject.parseArray(strs, FormFieldListDto.class);
            for (int j = 0; j < formFieldListDtos.size(); j++) {
                if (formFieldListDtos.get(j).getId().equals(flowNodeButton.getNodeId())) {
                    flowNodeButton.setFormUuid(formFieldListDtos.get(j).getFormUuid());
                    break;
                }
            }
            flowNodeButtonService.insertSelective(getSaveData(flowNodeButton));
        }
        //插入字段可编辑
        if (StrUtil.isNotBlank(modleDto.getFormFieldList()) && modleDto.getFormFieldList().length() > 2) {
            Example examples = new Example(ActMyNodeField.class);
            Example.Criteria criterias = examples.createCriteria();
            criterias.andEqualTo("modelKey", modleDto.getActDeModelKey());
            if (!StringUtils.isNullOrEmpty(modleDto.getProcdefId())) {
                criterias.andEqualTo("procdefId", modleDto.getProcdefId());
            }
            flowNodeFieldService.deleteByExample(examples);
            String strs = modleDto.getFormFieldList().replace("\\", "");
            List<FormFieldListDto> formFieldListDtos = JSONObject.parseArray(strs, FormFieldListDto.class);
            ActMyNodeField flowNodeField = null;
            for (int i = 0; i < formFieldListDtos.size(); i++) {
                flowNodeField = new ActMyNodeField();
                flowNodeField.setModelKey(modleDto.getActDeModelKey());
                flowNodeField.setModelId(modleDto.getActDeModelId());
                flowNodeField.setFlowModelUuid(modleDto.getUuid());
                flowNodeField.setIsEdit(formFieldListDtos.get(i).getIsEdit());
                flowNodeField.setIsLook(formFieldListDtos.get(i).getIsLook());
                flowNodeField.setFiled(formFieldListDtos.get(i).getFiled());
                flowNodeField.setId(formFieldListDtos.get(i).getId());
                flowNodeField.setFiledName(formFieldListDtos.get(i).getFiledName());
                flowNodeField.setProcdefId(modleDto.getProcdefId());
                flowNodeField.setFormUuid(formFieldListDtos.get(i).getFormUuid());
                flowNodeField.setModelType(formFieldListDtos.get(i).getModelType());
                flowNodeField.setFieldIndex(formFieldListDtos.get(i).getFieldIndex());
                flowNodeField.setFormLayout(formFieldListDtos.get(i).getFormLayout());
                if (StrUtil.isNotBlank(flowNodeField.getFiled())) {
                    flowNodeFieldService.insertSelective(getSaveData(flowNodeField));
                }
            }
        }
        //插入处理节点通知
        if (StrUtil.isNotBlank(modleDto.getFormNoticeList())) {
            if (StrUtil.isNotBlank(modleDto.getFormNoticeList()) && modleDto.getFormNoticeList().length() > 2) {
                //直接根据模型key删除所有数据
                Example examples = new Example(ActMyNodeNotice.class);
                Example.Criteria criterias = examples.createCriteria();
                criterias.andEqualTo("actDeModelKey", modleDto.getActDeModelKey());
                if (!StringUtils.isNullOrEmpty(procdefId)) {
                    criterias.andEqualTo("procdefId", procdefId);
                }
                flowNodeNoticeService.deleteByExample(examples);
                String strs = modleDto.getFormNoticeList().replace("\\", "");
                List<ActMyNodeNotice> formFieldListDtos = JSONObject.parseArray(strs, ActMyNodeNotice.class);
                for (int i = 0; i < formFieldListDtos.size(); i++) {
                    formFieldListDtos.get(i).setActDeModelKey(modleDto.getActDeModelKey());
                    formFieldListDtos.get(i).setActDeModelId(modleDto.getActDeModelId());
                    formFieldListDtos.get(i).setFlowModelUuid(modleDto.getUuid());
                    formFieldListDtos.get(i).setProcdefId(modleDto.getProcdefId());
                    flowNodeNoticeService.insertSelective(getSaveData(formFieldListDtos.get(i)));
                }
            }
        }

        //6 流程节点权限码更新
        List<ActMyNodeCode> actMyNodeCodes = JSONObject.parseArray(modleDto.getNodeCodeList(), ActMyNodeCode.class);
        actMyNodeCodeService.saveOrUpdateNodeCode(actMyNodeCodes, modelKey, procdefId);
        //假如修改了流程名称
        //修改流程定义表act_re_procdef name字段 根据key和主版本
        flowModelService.updateActReProcdefNameByKey(modelKey, "是", modelxmlName);


        return integer > 0 ? success("更新成功！") : failure("更新失败！");
    }


    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Object update(@ModelAttribute ActMyModel flowModel) {
        int result = flowModelService.updateByPrimaryKeySelective(getUpdateData(flowModel));
        return result > 0 ? success("更新成功！") : failure("更新失败！");
    }

    @Log(title = "流程表单模型-删除", module = LogModule.WORKFLOW, businessType = BusinessType.DELETE, method = "getLogMessage")
    @ApiOperation(value = "删除FlowModel模块数据")
    @ApiImplicitParam(name = "flowModel", value = "删除FlowModel模块数据", required = true, dataType = "FlowModel")
    @ResponseBody
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.POST)
    public Object deleteBatch(@RequestParam String[] uuid) {
        if (uuid != null && uuid.length > 0) {
            StringBuilder logStr = new StringBuilder();
            for (String s : uuid) {
                ActMyModel actMyModel = flowModelService.selectByPrimaryKey(s);
                if (actMyModel != null)
                    logStr.append(actMyModel.getActDeModelName() + ":" + actMyModel.getFormTableName() + ",");
            }
            setLogMessage(logStr.toString(), "");
        }

        int result = flowModelService.executeDeleteBatch(uuid);
        return result > 0 ? success("删除成功！") : failure("删除失败！");
    }

    @Autowired
    private ActMyFormService actMyFormService;

    // 同意完后去更新form表单数据
    @ResponseBody
    @RequestMapping(value = "/updateFormDesign", method = RequestMethod.POST)
    public Object updateFormDesign(ActMyForm actMyForm) {
        if (StringUtils.isNullOrEmpty(actMyForm.getProcessInstanceId()) || StringUtils.isNullOrEmpty(actMyForm.getActDeModelKey()) || StringUtils.isNullOrEmpty(actMyForm.getProcdefId())) {
            return failure("流程标识/流程定义Id/流程实例Id为空!!!");
        }
        actMyForm.setUuid(null);
        Integer integer = 0;
        ActMyForm myForm = actMyFormService.selectByProcessInstanceId(actMyForm.getProcessInstanceId(), actMyForm.getActDeModelKey(), actMyForm.getProcdefId());
        if (ObjectUtil.isEmpty(myForm)) {
            FormDesignVo formDesignVo = JSON.parseObject(actMyForm.getFormDesign(), FormDesignVo.class);
            List<FormDesignVo.ListDTO> listDTOS = formDesignVo.getList();
            for (int i = 0; i < listDTOS.size(); i++) {
                if ("serialNumber".equals(listDTOS.get(i).getType())) {
//                    String defaultValue = systemClient.getRuleOutputNumberByRuleCode(listDTOS.get(i).getOptions().getRuleCode());
                    listDTOS.get(i).getOptions().setDefaultValue(null);
                }
            }
            String json = JSON.toJSONString(formDesignVo);
            actMyForm.setFormDesign(json);
            integer = actMyFormService.insertSelective(getSaveData(actMyForm));
        } else {
            integer = actMyFormService.updateFormByPrimaryKeySelective(myForm.getUuid(), actMyForm.getFormDesign());
        }
        return integer > 0 ? success("更新成功！") : failure("更新失败！");
    }

    @ResponseBody
    @RequestMapping(value = "/formUpdateFormDesign", method = RequestMethod.POST)
    public Object formUpdateFormDesign(@RequestBody ActMyForm actMyForm) {
        return updateFormDesign(actMyForm);
    }

    @ApiOperation(value = "获取节点权限码")
    @ResponseBody
    @RequestMapping("/getNodeCodes")
    public Result getNodeCodes(String procDefId) {
        Map nodeCods = businessSystemDataService.getViewCode(procDefId);
        return Result.ofSuccess(nodeCods);
    }

    /**
     * 表单与流程一起保存
     *
     * @param flowModelDto flowModelDto
     * @return java.lang.Object
     */
    @Log(title = "流程表单模型-保存(课件流程图)", module = LogModule.WORKFLOW, businessType = BusinessType.INSERT, method = "getLogMessage")
    @ApiOperation(value = "保存表单流程数据(课件流程图)")
    @ApiImplicitParam(name = "flowModelDto", value = "flowModelDto", required = true, dataType = "FlowModelDto")
    @Transactional
    @ResponseBody
    @RequestMapping(value = "/saveCourseWareFlowchart", method = RequestMethod.POST)
    public Object saveCourseWareFlowchart(@RequestBody FlowModelDto flowModelDto) {
        if (StrUtil.hasBlank(flowModelDto.getModelId(), flowModelDto.getModelName())) {
            return failure("请检查【基础配置】中的流程名称，流程标识不能为空！！！");
        }
        //根据modelKey去act_de_model查询数据
        List<ActDeModelVo> actDeModelVo = apiFlowableModelService.getDataByModelKey(flowModelDto.getModelKey());
        if (1 == actDeModelVo.size()) {
            return failure("模型key已存在,请检查或者关闭页面重新设计该流程！！！");
        }
        if (actDeModelVo.size() >= 2) {
            return failure("模型key:" + flowModelDto.getModelKey() + "，在act_de_model表存存在多条数据，请联系管理员！！！");
        }
        setLogMessage(flowModelDto.getModelName(), "");
        //第二次保存走updateData
        //根据模型key以及流程定义Id为空查找数据,只会有一条数据如果有多条则提示有问题
        List<ActMyModel> lists = flowModelService.getListByModelKeyAndProcdefIdIsNull(flowModelDto.getModelKey());
        if (CollUtil.isNotEmpty(lists) && lists.size() > 1) {
            return failure("数据出现异常,请联系管理员进行删除！！！");
        }
        if (CollUtil.isNotEmpty(lists) && lists.size() == 1) {
            ModleDto modleDto = new ModleDto();
            modleDto.setModelType(flowModelDto.getModelType());
            modleDto.setFormTableName(flowModelDto.getFormTableName());
            modleDto.setUuid(lists.get(0).getUuid());
            modleDto.setFormFieldList(flowModelDto.getFormFieldList());
            modleDto.setFormNoticeList(flowModelDto.getFormNoticeList());
            modleDto.setPermissionType(flowModelDto.getPermissionType());
            modleDto.setPermissionValue(flowModelDto.getPermissionType());
            modleDto.setActDeModelName(flowModelDto.getActDeModelName());
            modleDto.setFormDesign(flowModelDto.getFormJson());
            modleDto.setActDeModelKey(flowModelDto.getModelKey());
            modleDto.setActDeModelId(flowModelDto.getModelId());
            modleDto.setProcdefId(lists.get(0).getProcdefId());
            modleDto.setFormBtnList(flowModelDto.getFormBtnList());
            modleDto.setFlowDesign(flowModelDto.getFlowJson());
            modleDto.setProcessModelType(flowModelDto.getProcessModelType());
            return updateData(modleDto); // todo
        }
        try {
            //1.处理流程数据
            ModelVo modelVo = new ModelVo();
            modelVo.setProcessId(flowModelDto.getModelId());
            modelVo.setProcessName(flowModelDto.getModelName());
            modelVo.setXml(flowModelDto.getFlowJson());
            modelVo.setProcessModelType(flowModelDto.getModelType());
            Object object = apiFlowableBpmnModelService.addModel(modelVo, flowModelDto.getFormFieldList(), flowModelDto.getFormBtnList());
            if (((HashMap) object).get("statusCode").equals(300)) {
                return failure("" + ((HashMap) object).get("message"));
            }
            String modelKey = ((HashMap) object).get("modelKey").toString();
            String modelIds = ((HashMap) object).get("modelId").toString();
            //2.处理流程与form表单数据
            ActMyModel flowModel = new ActMyModel();
            flowModel.setActDeModelId(modelIds);
            flowModel.setActDeModelKey(modelKey);
            flowModel.setFlowDesign(flowModelDto.getFlowJson());
            flowModel.setFormTableName(flowModelDto.getFormTableName());
            flowModel.setFormModel(flowModelDto.getFormModel());
            flowModel.setActDeModelName(flowModelDto.getModelName());
            flowModel.setModelType(flowModelDto.getModelType());
            flowModel.setPermissionType(flowModelDto.getPermissionType());
            flowModel.setPermissionValue(flowModelDto.getPermissionValue());
            flowModel.setFormUuid(flowModelDto.getActFormConfigureUuid());
            flowModel.setSign(flowModel.getSign());
            String uuid = BaseUtils.UUIDGenerator();
            flowModel.setUuid(uuid);
            int result = flowModelService.insertSelective(getSaveData(flowModel));
            //节点权限码保存
            //List<ActMyNodeCode> actMyNodeCodes = JSONObject.parseArray(flowModelDto.getNodeCodeList(), ActMyNodeCode.class);
            //actMyNodeCodeService.saveOrUpdateNodeCode(actMyNodeCodes, modelKey, "");
            return result > 0 ? success("保存成功！") : failure("保存失败！");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            logger.error("流程保存出现异常，原因为{}", e.getMessage());
            return failure("流程保存出现异！！！");
        }
    }

    /**
     * 更新表单流程数据(课件流程图)
     * @param modleDto 模型对象
     * @return java.lang.Object
     */
    @Log(title = "流程表单模型-更新(课件流程图)", module = LogModule.WORKFLOW, businessType = BusinessType.UPDATE, method = "getLogMessage")
    @ApiOperation(value = "更新表单流程数据(课件流程图)")
    @ApiImplicitParam(name = "modleDto", value = "modleDto", required = true, dataType = "ModleDto")
    @Transactional
    @ResponseBody
    @RequestMapping(value = "/updateCourseWareFlowchart", method = RequestMethod.POST)
    public Object updateCourseWareFlowchart(@RequestBody ModleDto modleDto) {
        if (StrUtil.hasBlank(modleDto.getActDeModelId(), modleDto.getActDeModelName())) {
            return failure("请检查基础配置的流程名称，流程标识不能为空！！！");
        }
        setLogMessage(modleDto.getActDeModelName(), "");
        String procdefId = modleDto.getProcdefId();
        //1.流程model
        ModelVo modelVo = new ModelVo();
        modelVo.setProcessId(modleDto.getActDeModelId());
        modelVo.setProcessName(modleDto.getActDeModelName());
        modelVo.setXml(modleDto.getFlowDesign());
        modelVo.setProcessModelType(Optional.ofNullable(modleDto.getProcessModelType()).orElse("1"));
        Object object = apiFlowableBpmnModelService.addModel(modelVo, modleDto.getFormFieldList(), modleDto.getFormBtnList());
        if (((HashMap) object).get("statusCode").equals(300)) {
            return failure(Convert.toStr(((HashMap) object).get("message")));
        }
        String modelKey = Convert.toStr(((HashMap) object).get("modelKey"));
        String modelIds = Convert.toStr(((HashMap) object).get("modelId"));
        String modelxmlName = Convert.toStr(((HashMap) object).get("modelName"));
        //2.流程与表单
        ActMyModel flowModel = new ActMyModel();
        flowModel.setUuid(modleDto.getUuid());
        flowModel.setActDeModelName(modelxmlName);
        flowModel.setActDeModelId(modelIds);
        flowModel.setFormModel(modleDto.getFormModel());
        flowModel.setFormDesign(modleDto.getFormDesign());
        flowModel.setFlowDesign(modleDto.getFlowDesign());
        flowModel.setFormTableName(modleDto.getFormTableName());
        flowModel.setActDeModelKey(modelKey);
        flowModel.setProcdefId(procdefId);
        flowModel.setModelType(modleDto.getModelType());
        flowModel.setPermissionType(modleDto.getPermissionType());
        flowModel.setPermissionValue(modleDto.getPermissionValue());
        flowModel.setSign(modleDto.getSign());
        Integer integer = flowModelService.updateByPrimaryKeySelective(getUpdateData(flowModel));
        //3.流程与按钮
        //直接根据模型key删除所有数据
        Example example = new Example(ActMyNodeButton.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modleDto.getActDeModelKey());
        if (!StringUtils.isNullOrEmpty(procdefId)) {
            criteria.andEqualTo("procdefId", procdefId);
        }
        flowNodeButtonService.deleteByExample(example);
        //流程节点权限码更新
        //List<ActMyNodeCode> actMyNodeCodes = JSONObject.parseArray(modleDto.getNodeCodeList(), ActMyNodeCode.class);
        //actMyNodeCodeService.saveOrUpdateNodeCode(actMyNodeCodes, modelKey, procdefId);
        //假如修改了流程名称
        //修改流程定义表act_re_procdef name字段 根据key和主版本
        flowModelService.updateActReProcdefNameByKey(modelKey, "是", modelxmlName);
        return integer > 0 ? success("更新成功！") : failure("更新失败！");
    }


    @ResponseBody
    @RequestMapping(value = "/getCourseWareFlowchart", method = RequestMethod.POST)
    public Object getCourseWareFlowchart(String modelKey) {
        String type = "mode";
        PageParam pageParam = new PageParam();
        pageParam.setPageSize(10);
        pageParam.setPageNo(1);
        PageSet<ActDeModelVo> page = apiFlowableModelService.getPageSet(pageParam, "", modelKey, "");
        List<ActDeModelVo> rows = page.getRows();
        ActDeModelVo actDeModelVo = null;
        if (CollectionUtils.isNotEmpty(rows)){
            actDeModelVo = rows.get(0);
        }
        String procdefId = "";
        if (actDeModelVo != null) {
            procdefId = actDeModelVo.getProcdefId();
        } else {
            return failure("获取不到历史模型数据！！！");
        }
        return getDetailByModelKey(modelKey, procdefId, type);
    }



}
