package com.sbtr.workflow.mapper;

import cn.ewsd.common.mapper.BaseMapper;
import com.sbtr.workflow.model.ActForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Date;

/**
 * 常用外置表单数据主表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:33:52
 */
public interface ActFormMapper extends tk.mybatis.mapper.common.Mapper<ActForm> {

    //分页
    List<ActForm> getPageSet(@Param("filterSort") String filterSort);

    //删除
    int executeDeleteBatch(Object[] var1);

}
