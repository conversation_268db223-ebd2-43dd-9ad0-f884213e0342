package com.sbtr.workflow.service.impl;

import com.sbtr.workflow.mapper.ApiRunFlowActinstMapper;
import com.sbtr.workflow.service.ApiRunFlowActinstService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("apiRunFlowActinstServiceImpl")
public class ApiRunFlowActinstServiceImpl extends BaseServiceImpl implements ApiRunFlowActinstService {

    @Autowired
    private ApiRunFlowActinstMapper apiRunFlowActinstMapper;

    @Override
    public void deleteRunActinstsByIds(List<String> ids) {
        apiRunFlowActinstMapper.deleteRunActinstsByIds(ids);
    }
}
