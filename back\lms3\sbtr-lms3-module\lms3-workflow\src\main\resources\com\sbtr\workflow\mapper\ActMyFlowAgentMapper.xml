<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ActMyFlowAgentMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sbtr.workflow.model.ActMyFlowAgent" id="flowAgentMap">
        <result property="uuid" column="uuid"/>
        <result property="createTime" column="create_time"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorOrgId" column="creator_org_id"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="agent" column="agent"/>
        <result property="mandator" column="mandator"/>
        <result property="agentName" column="agent_name"/>
        <result property="mandatorName" column="mandator_name"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="modelKeys" column="model_keys"/>
        <result property="groups" column="groups"/>
    </resultMap>

    <sql id="Base_Column_List" >
        uuid,
        create_time,
        creator,
        creator_id,
        creator_org_id,
        modifier_id,
        modifier,
        modify_time,
        agent,
        mandator,
        start_time,
        end_time,
        model_keys,
        groups
    </sql>


    <delete id="executeDeleteBatch">
		delete from act_my_flowagent where uuid in
		<foreach item="uuid" collection="array" open="(" separator="," close=")">
			#{uuid}
		</foreach>
	</delete>

    <select id="getMandatorsByAgent" resultType="java.lang.String">
        SELECT mandator
        FROM ACT_MY_FLOWAGENT
        where agent = #{agent}
        <if test="currentTime!='' and currentTime!=null">
           and TO_DATE(#{currentTime},'yyyy-mm-dd hh24:mi:ss') BETWEEN TO_DATE(coalesce(case when length(start_time)>0 then start_time else null end,'1900-01-01'),'yyyy-mm-dd hh24:mi:ss') and TO_DATE(coalesce(case when length(end_time)>0 then end_time else null end,'9999-01-01'),'yyyy-mm-dd hh24:mi:ss')
        </if>
    </select>
</mapper>
