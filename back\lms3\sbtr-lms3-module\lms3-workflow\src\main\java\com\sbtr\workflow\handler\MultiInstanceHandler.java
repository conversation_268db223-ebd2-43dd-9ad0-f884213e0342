package com.sbtr.workflow.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.sbtr.workflow.service.BusinessSystemDataService;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;


/**
 * 多实例处理类
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
@Component("multiInstanceHandler")
public class MultiInstanceHandler {
    @Resource
    private BusinessSystemDataService businessSystemDataService;
    public HashSet<String> getUserIds(DelegateExecution execution) {
        HashSet<String> candidateUserIds = new LinkedHashSet<>();
        FlowElement flowElement = execution.getCurrentFlowElement();
        if (ObjectUtil.isNotEmpty(flowElement) && flowElement instanceof UserTask) {
            UserTask userTask = (UserTask) flowElement;
            //扩展流程属性 去拿到每个任务节点是选择的人 选择的角色等等 # 还拿不到
            String dataType = userTask.getAttributeValue("http://flowable.org/bpmn", "dataType");
            //判断审批人类型  多实例只允许自定义变量、指定人员、指定角色、指定岗位
            String assignee = "";
            List<String> candidateUsers = userTask.getCandidateUsers();

            if (CollUtil.isNotEmpty(candidateUsers)) {
                String firstAid = candidateUsers.get(0);
                if(firstAid.startsWith("${")){//自定义变量的用户会签
                    firstAid = firstAid.replace("${","");
                    firstAid = firstAid.replace("}","");
                    firstAid = firstAid.trim();
                    String users = execution.getVariable(firstAid).toString();
                    candidateUserIds.addAll(Arrays.asList(users.split(",")));
                }else{
                    //指定候选人员会签
                    candidateUserIds.addAll(candidateUsers);
                }
            } else if (CollUtil.isEmpty(userTask.getCandidateUsers()) && CollUtil.isNotEmpty(userTask.getCandidateGroups())) {
                //指定候选角色会签
                assignee = businessSystemDataService.getUserNameIdByRoleId(String.join(",", userTask.getCandidateGroups()));
                //指定岗位会签
                if (StrUtil.isBlank(assignee)) {
                    assignee = businessSystemDataService.getUserNameIdByPost(String.join(",", userTask.getCandidateGroups()));
                }
                if (StrUtil.isNotBlank(assignee)) {
                    candidateUserIds.addAll(Arrays.asList(assignee.split(",")));
                }
            }
        }
        return candidateUserIds;
    }
}
