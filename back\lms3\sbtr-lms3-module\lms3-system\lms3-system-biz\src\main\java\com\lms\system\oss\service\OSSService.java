package com.lms.system.oss.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.*;
import com.lms.common.util.StringHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class OSSService {

    @Autowired
    private OSS ossClient;

    @Value("${OSS.endpoint}")
    private String endpoint;

    @Value("${OSS.bucketName}")
    private String defaultBucket;

    // 文件大小限制：100MB
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024;
    
    // 允许的文件类型
    private static final String[] ALLOWED_EXTENSIONS = {
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",
        ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
        ".txt", ".csv", ".zip", ".rar", ".7z", ".tar", ".gz", ".vrp", ".vrpc"
    };

    // ==================== 私有工具方法 ====================

    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        
        //if (file.getSize() > MAX_FILE_SIZE) {
        //    throw new IllegalArgumentException("文件大小不能超过100MB");
        //}
        
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        
        // 检查文件扩展名
        String extension = getFileExtension(originalFilename);
        boolean isValidExtension = false;
        for (String allowedExt : ALLOWED_EXTENSIONS) {
            if (allowedExt.equalsIgnoreCase(extension)) {
                isValidExtension = true;
                break;
            }
        }
        
        if (!isValidExtension) {
            throw new IllegalArgumentException("不支持的文件类型: " + extension);
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex).toLowerCase() : "";
    }
    
    /**
     * 生成可访问的文件URL
     */
    private String generateFileUrl(String bucketName, String objectName) {
        try {
            return String.format("https://%s.%s/%s", bucketName, endpoint.replace("https://", ""), objectName);
        } catch (Exception e) {
            log.error("生成文件URL出错: " + e.getMessage());
            return String.format("https://%s.%s/%s",
                    bucketName, endpoint.replace("https://", ""), objectName);
        }
    }

    // ==================== 存储桶管理 ====================

    /**
     * 创建存储桶
     */
    public String makeBucket(String bucketName) throws Exception {
        if (StringUtils.isEmpty(bucketName)) {
            throw new IllegalArgumentException("存储桶名称不能为空");
        }

        boolean isExist = bucketExists(bucketName);
        if (!isExist) {
            ossClient.createBucket(bucketName);
            log.info("创建存储桶成功: {}", bucketName);
        }
        return bucketName;
    }

    public String makeBucket() throws Exception {
        return makeBucket(defaultBucket);
    }

    /**
     * 检查存储桶是否存在
     */
    public boolean bucketExists(String bucketName) throws Exception {
        if (StringUtils.isEmpty(bucketName)) {
            return false;
        }
        return ossClient.doesBucketExist(bucketName);
    }

    public boolean bucketExists() throws Exception {
        return bucketExists(defaultBucket);
    }

    /**
     * 查看所有桶
     */
    public List<Bucket> listBuckets() throws Exception {
        return ossClient.listBuckets();
    }

    /**
     * 列出所有存储桶名称
     */
    public List<String> listBucketNames() throws Exception {
        List<Bucket> bucketList = listBuckets();
        List<String> bucketListName = new ArrayList<>();
        for (Bucket bucket : bucketList) {
            bucketListName.add(bucket.getName());
        }
        return bucketListName;
    }

    /**
     * 删除桶
     */
    public boolean removeBucket(String bucketName) throws Exception {
        if (StringUtils.isEmpty(bucketName)) {
            throw new IllegalArgumentException("存储桶名称不能为空");
        }
        
        boolean flag = bucketExists(bucketName);
        if (flag) {
            ObjectListing objectListing = listObjects(bucketName);
            if (objectListing != null && !objectListing.getObjectSummaries().isEmpty()) {
                // 有对象文件，则删除失败
                log.warn("删除存储桶失败，桶{}不为空", bucketName);
                return false;
            }
            // 删除存储桶，注意，只有存储桶为空时才能删除成功。
            ossClient.deleteBucket(bucketName);
            log.info("删除存储桶成功: {}", bucketName);
            return true;
        }
        return false;
    }

    public boolean removeBucket() throws Exception {
        return removeBucket(defaultBucket);
    }

    // ==================== 文件上传 ====================

    /**
     * 上传文件(返回文件名)
     */
    public String upload(String bucketName, MultipartFile file) throws Exception {
        // 验证文件
        validateFile(file);
        
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = defaultBucket;
        }
        
        if (!bucketExists(bucketName)) {
            makeBucket(bucketName);
        }
        
        log.info("开始向桶 {} 上传文件", bucketName);
        String objectName = file.getOriginalFilename();
        
        try (InputStream inputStream = file.getInputStream()) {
            // 设置元数据
            ObjectMetadata meta = new ObjectMetadata();
            meta.setContentLength(file.getSize());
            meta.setContentType(file.getContentType());

            // 使用PutObjectRequest直接上传流
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, inputStream, meta);
            ossClient.putObject(putObjectRequest);
        } catch (Exception e) {
            log.error("文件上传失败: " + e.getMessage(), e);
            throw new RuntimeException("文件上传失败", e);
        }
        
        log.info("文件上传成功，文件名为：{}", objectName);
        return objectName;
    }

    public String upload(MultipartFile file) throws Exception {
        return upload(defaultBucket, file);
    }

    /**
     * 上传文件(返回详细信息)
     */
    public Map<String, String> uploadFile(MultipartFile file, String objectName) throws Exception {
        // 验证文件
        validateFile(file);
        
        if (StringUtils.isEmpty(objectName)) {
            objectName = file.getOriginalFilename();
        }
        
        try (InputStream inputStream = file.getInputStream()) {
            // 设置元数据
            ObjectMetadata meta = new ObjectMetadata();
            meta.setContentLength(file.getSize());
            meta.setContentType(file.getContentType());

            // 使用PutObjectRequest直接上传流
            PutObjectRequest putObjectRequest = new PutObjectRequest(defaultBucket, objectName, inputStream, meta);
            PutObjectResult putObjectResult = ossClient.putObject(putObjectRequest);
            
            // 公开Bucket直接拼接URL
            String fileUrl = generateFileUrl(defaultBucket, objectName);
            log.info("文件流上传成功: " + objectName);
            
            // 返回结果
            Map<String, String> result = new HashMap<>();
            result.put("fileName", file.getOriginalFilename());
            result.put("objectName", objectName);
            result.put("bucketName", defaultBucket);
            result.put("fileUrl", fileUrl);
            result.put("eTag", putObjectResult.getETag());
            result.put("size", String.valueOf(file.getSize()));
            result.put("fileType", meta.getContentType());

            return result;
        } catch (OSSException oe) {
            log.error("OSS异常: {}", oe.getErrorMessage());
            throw new RuntimeException("OSS上传异常: " + oe.getErrorMessage(), oe);
        } catch (Throwable ce) {
            log.error("客户端异常: {}", ce.getMessage());
            throw new RuntimeException("客户端异常: " + ce.getMessage(), ce);
        }
    }

    /**
     * 上传本地文件
     */
    public void uploadLocalFile(String fileLocation, String bucketName, String objectName) throws Exception {
        if (StringUtils.isEmpty(fileLocation) || StringUtils.isEmpty(bucketName) || StringUtils.isEmpty(objectName)) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        try {
            ObjectMetadata meta = new ObjectMetadata();
            UploadFileRequest uploadFileRequest = new UploadFileRequest(bucketName, objectName);
            uploadFileRequest.setUploadFile(fileLocation);
            uploadFileRequest.setTaskNum(5);
            uploadFileRequest.setPartSize(1024 * 1024);
            uploadFileRequest.setEnableCheckpoint(true);
            uploadFileRequest.setCheckpointFile(fileLocation);
            uploadFileRequest.setObjectMetadata(meta);
            ossClient.uploadFile(uploadFileRequest);
            log.info("本地文件上传成功: fileLocation={}, bucket={}, object={}", fileLocation, bucketName, objectName);
        } catch (OSSException oe) {
            log.error("OSS异常: {}", oe.getErrorMessage());
            throw new RuntimeException("OSS上传异常: " + oe.getErrorMessage(), oe);
        } catch (Throwable ce) {
            log.error("客户端异常: {}", ce.getMessage());
            throw new RuntimeException("客户端异常: " + ce.getMessage(), ce);
        }
    }

    // ==================== 文件下载 ====================

    /**
     * 下载文件
     */
    public void download(String bucketName, String fileName, HttpServletResponse response) {
        if (StringHelper.isEmpty(fileName)) {
            log.error("文件名为空！");
            return;
        }
        
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = defaultBucket;
        }

        OSSObject ossObject = null;
        try {
            // 获取文件流
            ossObject = ossClient.getObject(bucketName, fileName);
            InputStream inputStream = ossObject.getObjectContent();
            if (inputStream != null) {
                response.reset();
                response.setHeader("Content-Disposition", "attachment;filename=" +
                        URLEncoder.encode(fileName.substring(fileName.lastIndexOf("/") + 1), String.valueOf(StandardCharsets.UTF_8)));
                response.setContentType("application/octet-stream");
                response.setCharacterEncoding("UTF-8");

                // 获取输出流
                try (ServletOutputStream servletOutputStream = response.getOutputStream()) {
                    int len;
                    byte[] buffer = new byte[1024];
                    while ((len = inputStream.read(buffer)) > 0) {
                        servletOutputStream.write(buffer, 0, len);
                    }
                    servletOutputStream.flush();
                }
                log.info("文件{}下载成功", fileName);
            }
        } catch (Exception e) {
            log.error("文件名: {}下载文件时出现异常: {}", fileName, e.getMessage(), e);
        } finally {
            // 确保资源被正确关闭
            if (ossObject != null) {
                try {
                    ossObject.close();
                } catch (IOException e) {
                    log.error("关闭OSSObject失败", e);
                }
            }
        }
    }

    public void download(String fileName, HttpServletResponse response) {
        download(defaultBucket, fileName, response);
    }

    /**
     * 以流的形式下载一个文件
     */
    public InputStream download(String bucketName, String objectName) throws Exception {
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = defaultBucket;
        }
        
        OSSObject ossObject = ossClient.getObject(bucketName, objectName);
        return ossObject.getObjectContent();
    }

    public InputStream download(String objectName) throws Exception {
        return download(defaultBucket, objectName);
    }

    /**
     * 根据文件URL下载文件
     */
    public InputStream downloadFile(String fileLocation) {
        if (StringUtils.isEmpty(fileLocation)) {
            log.error("文件地址不能为空");
            return null;
        }
        
        OSSObject ossObject = null;
        try {
            URI uri = new URI(fileLocation);
            String host = uri.getHost();
            // 解析BucketName（假设标准格式：bucket-name.oss-region.aliyuncs.com）
            String bucketName = host.substring(0, host.indexOf('.'));
            // 解析ObjectName（去除路径开头的/）
            String objectName = uri.getPath().substring(1);
            ossObject = ossClient.getObject(bucketName, objectName);
            InputStream stream = ossObject.getObjectContent();
            // 注意：这里不能关闭ossObject，因为需要返回stream
            return stream;
        } catch (OSSException oe) {
            log.error("OSS异常: {}", oe.getErrorMessage());
        } catch (Throwable ce) {
            log.error("客户端异常: {}", ce.getMessage());
        }
        return null;
    }

    // ==================== 文件删除 ====================

    /**
     * 删除文件
     */
    public boolean removeObject(String bucketName, String fileName) {
        try {
            if (StringHelper.isEmpty(fileName)) {
                log.error("删除文件失败，文件名为空！");
                return false;
            }
            
            if (StringUtils.isEmpty(bucketName)) {
                bucketName = defaultBucket;
            }
            
            // 判断桶是否存在
            boolean isExist = ossClient.doesBucketExist(bucketName);
            // 桶存在
            if (isExist) {
                ossClient.deleteObject(bucketName, fileName);
                log.info("删除文件成功: bucket={}, fileName={}", bucketName, fileName);
                return true;
            } else { // 桶不存在
                log.error("删除文件失败，桶{}不存在", bucketName);
                return false;
            }
        } catch (Exception e) {
            log.error("删除文件时出现异常: " + e.getMessage(), e);
            return false;
        }
    }

    public boolean removeObject(String fileName) {
        return removeObject(defaultBucket, fileName);
    }

    // ==================== 文件查询 ====================

    /**
     * 列出存储桶中的所有对象
     */
    public ObjectListing listObjects(String bucketName) throws Exception {
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = defaultBucket;
        }

        boolean flag = bucketExists(bucketName);
        if (flag) {
            return ossClient.listObjects(bucketName);
        }
        return null;
    }

    public ObjectListing listObjects() throws Exception {
        return listObjects(defaultBucket);
    }

    /**
     * 列出存储桶中的所有对象名称
     */
    public List<String> listObjectNames(String bucketName) throws Exception {
        List<String> listObjectNames = new ArrayList<>();
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = defaultBucket;
        }

        boolean flag = bucketExists(bucketName);
        if (flag) {
            ObjectListing objectListing = listObjects(bucketName);
            if (objectListing != null) {
                objectListing.getObjectSummaries().forEach(objectSummary -> {
                    listObjectNames.add(objectSummary.getKey());
                });
            }
        }
        return listObjectNames;
    }

    public List<String> listObjectNames() throws Exception {
        return listObjectNames(defaultBucket);
    }

    /**
     * 判断文件是否存在
     */
    public Boolean checkFileIsExist(String bucketName, String objectName) {
        try {
            if (StringUtils.isEmpty(bucketName)) {
                bucketName = defaultBucket;
            }
            return ossClient.doesObjectExist(bucketName, objectName);
        } catch (Exception e) {
            log.error("检查文件是否存在时出现异常: {}", e.getMessage(), e);
            return false;
        }
    }

    public Boolean checkFileIsExist(String objectName) {
        try {
            return checkFileIsExist(defaultBucket, objectName);
        } catch (Exception e) {
            return false;
        }
    }

    // ==================== 文件预览 ====================

    /**
     * 获取文件预览url
     */
    public String getPreviewUrl(String bucketName, String fileName) {
        // 获取桶名
        bucketName = StringHelper.isEmpty(bucketName) ? defaultBucket : bucketName;
        String presignedUrl = null;
        try {
            if (StringHelper.isEmpty(fileName)) {
                log.error("获取文件预览url失败，文件名为空！");
                return presignedUrl;
            }
            // 判断桶是否存在
            boolean isExist = ossClient.doesBucketExist(bucketName);
            // 桶存在
            if (isExist) {
                // 生成预签名URL，默认1小时过期
                java.util.Date expiration = new java.util.Date(System.currentTimeMillis() + 3600 * 1000);
                presignedUrl = ossClient.generatePresignedUrl(bucketName, fileName, expiration).toString();
                return presignedUrl;
            } else {  // 桶不存在
                log.error("获取文件预览url失败，桶{}不存在", bucketName);
            }
        } catch (Exception e) {
            log.error("获取文件预览url时出现异常: " + e.getMessage(), e);
        }
        return presignedUrl;
    }

    public String getPreviewUrl(String fileName) {
        return getPreviewUrl(defaultBucket, fileName);
    }

} 