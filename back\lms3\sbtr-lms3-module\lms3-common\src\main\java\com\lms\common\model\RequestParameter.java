package com.lms.common.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class RequestParameter implements Serializable {
    private List<Map> orders; //排序
    private List<JSONObject> list; //接受list对象参数
    private Integer pageIndex; //当前页 (默认1)
    private Integer pageSize; //显示条数 (默认10)
    private JSONObject queryParam; //高级查询条件(对象)
    private JSONObject main; // 实体主数据
    private JSONObject details;//明细表数据集合，支持多个明细表 eg:{"a":[{"p1":"1","p2":"2"}],"b":[{"p3":"1","p4":"2"}]}，a和b名称由后端定义，建议定义为Java实体名
    private String processInstId;// 流程实例Id
    private String processKey; // 流程编码(提交、审批流程操作为必要项)
    private JSONObject approveData; // 流程审批相关信息(如果提交流程时该项不为空，后台自动调用审批流程接口，否则调用提交流程接口)
    private JSONObject asistParam; //业务之外需要传递到后端的辅助信息

}
