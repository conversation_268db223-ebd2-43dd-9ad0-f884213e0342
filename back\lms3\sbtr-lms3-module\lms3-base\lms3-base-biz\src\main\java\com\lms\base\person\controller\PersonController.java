/**
 * FileName:PersonController.java
 * Author:<PERSON><PERSON><PERSON><PERSON>
 * Time  :2013-3-7下午4:18:10
 * CopyRight: SBTR LTD.
 * decription:to-do
 */
package com.lms.base.person.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.lms.base.component.service.ComponentService;
import com.lms.base.department.service.DepartmentService;
import com.lms.base.person.service.PersonService;
import com.lms.base.person.vo.Student;
import com.lms.base.person.vo.Teacher;
import com.lms.base.feign.model.Selectitem;
import com.lms.base.selectitem.service.SelectitemService;
import com.lms.base.subject.service.WrongsubjectService;
import com.lms.common.feign.dto.Setting;
import com.lms.common.feign.dto.Users;
import com.lms.common.model.SettingConfig;
import com.lms.common.util.DateHelper;
import com.lms.common.util.ExcelUtils;
import com.lms.system.feign.api.LmsSystemApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lms.base.feign.model.Department;
import com.lms.base.feign.model.Person;
import com.lms.common.enums.RoleEum;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.dao.query.Parameter;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.EncryptHelper;
import com.lms.common.util.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 人员信息操作控制层
 */
@RestController
@RequestMapping("/person")
@Api(value = "人员信息管理", tags = "人员信息管理")
public class PersonController extends BaseController<Person> {
    @Resource
    private PersonService personService;
    @Resource
    private DepartmentService departmentService;
    @Resource
    private WrongsubjectService wrongsubjectService;
    @Resource
    private SelectitemService selectitemService;
    @Resource
    private ComponentService componentService;
    @Resource
    private LmsSystemApi lmsSystemApi;
    protected static final Logger logger = LoggerFactory
            .getLogger(PersonController.class);

    /*
     * 获取所有有效人员信息(分页)
     */
    @RequestMapping(value = {"/listpageAll"}, method = RequestMethod.GET)
    @ApiOperation(value = "获取所有有效人员信息", httpMethod = "GET")
    public Result<List<Person>> getAllPerson() {
        List<Person> persons = this.personService.list();
        return Result.OK(persons);
    }

    @LMSLog(desc = "查询人员信息", otype = LogType.List, objname = "人员信息列表")
    @PostMapping(value = {"/listpage"})
    @ApiOperation(value = "查询人员信息", httpMethod = "POST")
    public Result getPersonList(@RequestBody JSONObject request) {
        // 设置分页参数,查询参数
        PageInfo pageInfo = super.getPageInfo(request);
        Page<Person> persons = personService.listByCondition(pageInfo);
        personService.adaptPersonModel(persons.getRecords());
        personService.adaptTeacheingModel(persons.getRecords());
        personService.adaptManageModel(persons.getRecords());
        for (Person person : persons.getRecords()) {
//            personService.setPersonImage(person);
            if (ObjectUtil.isNotEmpty(person.getDepartmentid())) {
                Department department = departmentService.getById(person.getDepartmentid());
                String departName = ObjectUtil.isNotEmpty(department) ? department.getName() : "";
                person.setDepartmentname(departName);
                person.setDepartment(department);
            }
            if (!StringHelper.isEmpty(person.getDepartmentids())) {
                Set<Department> departments = new HashSet<Department>();
                String[] departmentids = person.getDepartmentids().split(",");
                for (int i = 0; i < departmentids.length; i++) {
                    String departmentid = departmentids[i];
                    Department d = departmentService.getById(departmentid);
                    departments.add(d);
                }
                person.setDepartments(departments);
            }
        }
        return Result.OK(persons);
    }

    @RequestMapping(value = {"/teacherAnalysis"}, method = RequestMethod.GET)
    @ApiOperation(value = "统计讲师信息", httpMethod = "GET")
    public Result getTeachAnalysisByType(@ApiParam(value = "统计方式") @RequestParam("analysetype") String analysetype) {
        // 设置分页参数,查询参数
        List persons = personService.getTeacherAnalysis(analysetype);
        return Result.OK(persons);
    }

    /*
     * 获取所有有效人员信息
     */
    @RequestMapping(value = {"/list"}, method = RequestMethod.GET)
    @ApiOperation(value = "获取所有有效人员信息", httpMethod = "GET")
    public Result<List<Person>> getValidPerson() {
        List<Person> persons = this.personService.getValidPersons();
        // List<Person> persons = this.personService.findAll();
        return Result.OK(persons);
    }

    /*
     * 根据ID获取人员信息
     */
    @GetMapping(value = {"/getInfo/{id}"})
    @ApiOperation(value = "根据ID获取人员信息", httpMethod = "GET")
    public Result<Person> getInfo(@ApiParam(value = "人员id") @PathVariable("id") String id) {
        Person person = personService.getById(id);
        personService.adaptPersonModel(Collections.singletonList(person));
        personService.adaptTeacheingModel(Collections.singletonList(person));
        personService.adaptManageModel(Collections.singletonList(person));
//        if (person != null) {
//            personService.setPersonImage(person);
//        }
        if (person != null && ObjectUtil.isNotEmpty(person.getDepartmentid())) {
            Department department = departmentService.getById(person.getDepartmentid());
            String departName = ObjectUtil.isNotEmpty(department) ? department.getName() : "";
            person.setDepartmentname(departName);
            person.setDepartment(department);
        }
        return Result.OK(person);
    }

    /*
     * 根据ID获取人员信息
     */
    @GetMapping(value = {"/getPersonById"})
    @ApiOperation(value = "根据ID获取人员信息", httpMethod = "GET")
    public Result<Person> getPersonById(@ApiParam(value = "人员id") @RequestParam("id") String id) {
        Person person = this.personService.getById(id);
        personService.adaptPersonModel(Collections.singletonList(person));
        personService.adaptTeacheingModel(Collections.singletonList(person));
        personService.adaptManageModel(Collections.singletonList(person));
//        if (person != null) {
//            personService.setPersonImage(person);
//        }
        if (person != null && ObjectUtil.isNotEmpty(person.getDepartmentid())) {
            Department department = departmentService.getById(person.getDepartmentid());
            String departName = ObjectUtil.isNotEmpty(department) ? department.getName() : "";
            person.setDepartmentname(departName);
            person.setDepartment(department);
        }
        return Result.OK(person);
    }

    @PostMapping("/setPersonImage")
    @ApiOperation(value = "设置人员头像", httpMethod = "POST")
    @LMSLog(desc = "设置人员头像", otype = LogType.Update)
    public Result<Person> setPersonImage(@RequestBody Person person) {
        if (person != null) {
            personService.setPersonImage(person);
        }
        return Result.OK(person);
    }

    // 人员新增的处理
    @LMSLog(desc = "新增人员、用户信息", otype = LogType.Save)
    @RequestMapping(value = {"/add"}, method = RequestMethod.POST)
    @ApiOperation(value = "新增人员、用户信息", httpMethod = "POST")
    public Result<Person> savePerson(@RequestBody Person person) {
        if (person != null && StringUtils.isNotEmpty(person.getDepartmentid())){
            Department department = departmentService.getById(person.getDepartmentid());
            person.setDepartmentidfullpath(department.getFullpath());
        }
        if (StringHelper.isEmpty(person.getCardNum())) {
            // 没有证件号，可能是培训学员，不需要登录
            person.setStatus(1);
            this.personService.saveOrUpdate(person);
            personService.saveRoleLink(person);
            return Result.OK(person);
        }
        if (personService.existPersonByCardNum(person.getCardNum(), "")) {
            //当前部门目录下存在重复证件号信息；新增失败
            String prompMsg = "证件号编号:" + person.getCardNum() + "已存在，存在重复证件号信息，请联系管理员!";
            return Result.error(prompMsg, null);
        } else {
            //如果其他目录下存在人员信息，将其状态设置为无效；
            //证件号重复的很少。
            person.setStatus(1);
            person.setSlevel(80);
            this.personService.saveOrUpdate(person);
            String userName = StringHelper.null2String(person.getUsername(), person.getCardNum());
            String defaultPassword = "Sbtr.123";
            Result<Setting> defaultPasswordResult = commonSystemApi.getById(SettingConfig.CONFIG_DEFAULT_PASSWORD);
            if (defaultPasswordResult != null && defaultPasswordResult.getResult() != null) {
                Setting defaultPasswordSetting = defaultPasswordResult.getResult();
                defaultPassword = defaultPasswordSetting.getItemvalue();
            }
            String userPassword = EncryptHelper.md5Password(StringHelper.null2String(person.getPassword(), defaultPassword)); //改到nacos配置中
            String roleId = person.getPersontype().split(",")[0];
            Users user = Optional.ofNullable(lmsSystemApi.getUsersByUserName(userName).getResult()).orElse(new Users());
            user.setName(userName);
            user.setPassword(userPassword);
            user.setPsdupdatedate(DateHelper.getCurrentDate());
            user.setIpaddr(person.getIpaddr());
            user.setClientagent(person.getClientagent());
            user.setPersonid(person.getId());//关联信息为证件号：证件号唯一！
            user.setIslocked(false);
            user.setEnable(false);
            user.setIsfirstlogin(true);
            user.setStatus(1);
            user.setRoleid(roleId);
            user.setPsderrortimes(0);
            lmsSystemApi.saveUser(user);
            personService.saveRoleLink(person);
            return Result.OK(person);
        }
    }

    // 更新Action
    @LMSLog(desc = "修改人员信息", otype = LogType.Update)
    @PostMapping(value = {"/modify"})
    @ApiOperation(value = "修改人员信息并设置角色关系", httpMethod = "POST")
    public Result<Person> updatePerson(@RequestBody Person person) {
        if (person != null && StringUtils.isNotEmpty(person.getDepartmentid())){
            Department department = departmentService.getById(person.getDepartmentid());
            person.setDepartmentidfullpath(department.getFullpath());
        }
        if (personService.existPersonByCardNum(person.getCardNum(), person.getId())) {
            //当前部门目录下存在重复证件号信息；新增失败
            String prompMsg = "证件号编号:" + person.getCardNum() + "已存在，存在重复证件号信息，请联系管理员!";
            return Result.error(prompMsg, null);
        } else {
            personService.saveRoleLink(person);
            personService.saveOrUpdate(person);
        }
        return Result.OK(person);
    }

    // 更新Action
    @LMSLog(desc = "修改人员信息", otype = LogType.Update)
    @PostMapping(value = {"/savePersonalInfo"})
    @ApiOperation(value = "修改人员信息", httpMethod = "POST")
    public Result savePersonalInfo(@RequestBody Person person) {
        if (person != null && StringUtils.isNotEmpty(person.getDepartmentid())){
            Department department = departmentService.getById(person.getDepartmentid());
            person.setDepartmentidfullpath(department.getFullpath());
        }
        String id = person.getId();
        if (personService.existPersonByCardNum(person.getCardNum(), id)) {
            //存在重复证件号信息；新增失败
            String prompMsg = "证件号编号:" + person.getCardNum() + "已存在，存在重复证件号信息，请联系管理员!";
            return Result.error(prompMsg);
        } else {
            Person p = this.personService.getById(id);
            if (p != null) {
                p.setTel(person.getTel());
                personService.saveOrUpdate(person);
                return Result.OK(person);
            }
            return Result.error("人员不存在，无法修改！");
        }
    }

    // 更新Action
    @LMSLog(desc = "人员级别设置", otype = LogType.Update)
    @PostMapping(value = {"/setlevel/{id}"})
    @ApiOperation(value = "人员级别设置", httpMethod = "POST")
    public Result setlevel(@RequestBody Person person, @PathVariable("id") String id) {
        if (personService.existPersonByCardNum(person.getCardNum(), id)) {
            String prompMsg = "证件号编号:" + person.getCardNum() + "已存在，存在重复证件号信息，请联系管理员!";
            return Result.error(prompMsg);
        } else {
            Person p = this.personService.getById(id);
            if (p != null) {
                String roleId = person.getPersontype().split(",")[0];
                int usertype = RoleEum.getCodeByValue(roleId);
                Optional<Users> usersOptional = Optional.ofNullable(lmsSystemApi.getUsersByPersonId(id).getResult());
                if(usersOptional.isPresent()){
                    Users user = usersOptional.get();
                    user.setRoleid(roleId);
                    user.setUsertype(usertype);
                    lmsSystemApi.saveUser(user);
                }
                personService.saveOrUpdate(person);
                personService.saveRoleLink(person);
                return Result.OK(person);
            }
            return Result.error("人员不存在，无法设置！");
        }
    }

    // 批量修改
    @LMSLog(desc = "批量设置人员级别", otype = LogType.BatchUpdate)
    @PostMapping(value = {"/batchSetlevel"})
    @Transactional
    @ApiOperation(value = "批量设置人员级别", httpMethod = "POST")
    public Result batchSetlevel(@RequestBody JSONArray jsonArray) {
        List<Person> persons = new ArrayList<Person>();
        List<Users> users = new ArrayList<Users>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String persontype = jsonObject.getString("persontype");
            Person person = personService.getById(jsonObject.getString("id"));
            person.setPersontype(persontype);
            person.setSlevel(jsonObject.getInteger("slevel"));
            String roleId = persontype.split(",")[0];
            int usertype = RoleEum.getCodeByValue(roleId);
            Optional<Users> usersOptional = Optional.ofNullable(lmsSystemApi.getUsersByPersonId(person.getId()).getResult());
            if(usersOptional.isPresent()){
                Users user = usersOptional.get();
                user.setRoleid(roleId);
                user.setUsertype(usertype);
                users.add(user);
            }
            persons.add(person);
        }
        personService.saveBatch(persons);
        personService.saveBatch(persons);
        lmsSystemApi.saveAllUser(users);
        return Result.OK(persons);
    }

    // 删除
    @LMSLog(desc = "删除人员、用户信息", otype = LogType.Delete)
    @DeleteMapping(value = {"/del/{id}"})
    @ApiOperation(value = "删除人员、用户信息", httpMethod = "DELETE")
    public Result deletePerson(@PathVariable("id") String id) {
        this.personService.removeById(id);
        lmsSystemApi.batchDeleteByPersonId(id);
        return Result.OK();
    }

    // 批量删除
    @LMSLog(desc = "删除人员、用户信息", otype = LogType.Delete)
    @DeleteMapping(value = {"/batchremove"})
    @ApiOperation(value = "删除人员、用户信息(批量删除)", httpMethod = "DELETE")
    public Result batchDeletePerson(@RequestParam("ids") String ids) {
        String[] idarray = ids.split(",");
        List<String> personIds = Arrays.asList(idarray);
        personService.removeBatchByIds(personIds);
        lmsSystemApi.batchDeleteByPersonId(ids);
        return Result.OK();
    }

    @RequestMapping(value = {"/exsitPersonName"}, method = RequestMethod.POST)
    @ApiOperation(value = "根据名称判断人员是否存在", httpMethod = "POST")
    public Result exsitPersonName(@ApiParam(value = "人员id") @RequestParam("id") String id,
                                  @ApiParam(value = "人员姓名") @RequestParam("name") String name,
                                  @ApiParam(value = "部门id") @RequestParam("deptid") String deptid) {
        List<Person> persons = this.personService.findByPersonName(name);
        if (persons.size() > 0) {
            String msg = commonSystemApi.translateContent("该人员姓名已存在，是否继续新增？");
            if (persons.size() == 1) {
                if (persons.get(0).getId().equals(id)) {
                    msg = "";
                } else if (persons.get(0).getDepartmentid().equals(deptid)) {
                    msg = commonSystemApi.translateContent("当前部门下存在该姓名的人员，是否继续新增？");
                }
            } else {
                for (Person p : persons) {
                    if (p.getId().equals(id))
                        continue;
                    if (p.getDepartmentid().equals(deptid)) {
                        msg = commonSystemApi.translateContent("当前部门下存在该姓名的人员，是否继续新增？");
                        break;
                    }
                }
            }
            if (!StringHelper.isEmpty(msg)) {
                return Result.error(msg);
            }
        }
        return Result.OK();
    }

    //一卡通卡号唯一
    @RequestMapping(value = {"/exsitPersonCardNum"}, method = RequestMethod.POST)
    @ApiOperation(value = "根据证件号判断人员是否存在", httpMethod = "POST")
    public Result exsitPersonCardNum(@RequestParam("id") String id,
                                     @RequestParam("cardNum") String cardNum) {
        //cardNum存贮的是一卡通卡号信息；
        PageInfo pageInfo = new PageInfo();
        Parameter cardNumParam = Parameter.getParameter(
                "S_EQ_cardNum", cardNum);
        pageInfo.getParameters().add(cardNumParam);
        List<Person> personList = this.personService.listByCondition(pageInfo).getRecords();
        if (StringHelper.isEmpty(id)) {
            if (personList.size() > 0) {
                return Result.OK(true);
            }
            return Result.OK(false);
        } else {
            if (personList.size() == 1 && personList.get(0).getId().equals(id)) {
                return Result.OK(false);
            } else if (personList.size() == 0) {
                return Result.OK(false);
            } else {
                return Result.OK(true);
            }
        }
    }

    //通过一卡通号码获取人员信息；
    @PostMapping(value = {"/getPersonByCardNum"})
    @ApiOperation(value = "通过证件号获取人员信息", httpMethod = "POST")
    public Result<Person> findPersonByCardNum(@RequestParam("cardNum") String cardNum) {
        //cardNum存贮的是一卡通卡号信息；
        PageInfo pageInfo = new PageInfo();
        Parameter cardNumParam = Parameter.getParameter(
                "S_EQ_cardNum", cardNum);
        Parameter statusParam = Parameter.getParameter(
                "I_EQ_status", 1);
        pageInfo.getParameters().add(statusParam);
        pageInfo.getParameters().add(cardNumParam);
        List<Person> personList = this.personService.listByCondition(pageInfo).getRecords();
        if (personList.size() == 1) {
            Person person = personList.get(0);
//            if (person != null) {
//                personService.setPersonImage(person);
//            }
            return Result.OK(person);
        } else if (personList.size() == 0) {
            return Result.error(commonSystemApi.translateContent("当前一卡通卡号获取人员信息为空"));
        } else {
            return Result.error(commonSystemApi.translateContent("当前一卡通卡号对应多个人员信息，请联系管理员"));
        }

    }

    @RequestMapping(value = {"/importPersonData"}, method = RequestMethod.POST)
    @ApiOperation(value = "导入人员数据", httpMethod = "POST")
    public Result importPersonData(@RequestParam("file") MultipartFile file,
                                   @RequestParam("departmentId") String departmentId) {
        if (file != null && !file.isEmpty()) {
            //开始调用底层函数，读取excel表里面的内容
            List<Object> result = personService.readExcelFile(file, departmentId);
            if (result.size() == 0) {
                return Result.OK("导入成功");
            } else {
                return Result.error("导入文件上传失败", result);
            }
        }
        return Result.error("导入文件上传失败");
    }

    public String setPersonLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.Save)) { //新增数据日志
            Person p = (Person) (args[0]);
            objname = StringHelper.null2String(p.getName());
        } else if (lmslog.otype().equals(LogType.Update)) { //编辑数据日志
            Person p = (Person) (args[0]);
            objname = StringHelper.null2String(p.getName());
        } else if (lmslog.otype().equals(LogType.Delete)) { //删除数据日志
            String ids = (String) (args[0]);
            String[] idarray = ids.split(",");
            for (String id : idarray) {
                Person p = personService.getPersonById(id);
                objname = objname.isEmpty() ? StringHelper.null2String(p.getName()) : objname + "," + StringHelper.null2String(p.getName());
            }
        }
        return objname;
    }


    @PostMapping(value = {"/listByCondition"})
    @ApiOperation(value = "条件查询人员信息", httpMethod = "POST")
    public Result listByConditionPersons(@RequestBody PageInfo pageInfo) {
        return Result.OK(personService.listByCondition(pageInfo));
    }


    @PostMapping("/exportTeacherByPersonIds")
    @ApiOperation(value = "导出讲师信息", httpMethod = "POST")
    @LMSLog(desc = "导出讲师信息", otype = LogType.Export, objname = "导出讲师信息")
    public void exportTeacherByPersonIds(@RequestBody List<String> ids, HttpServletResponse response) {
        // 1. 获取人员信息列表  未选择人员则导出全部
        List<Person> personList = new ArrayList<>();
        //导出全部
        if (CollectionUtils.isEmpty(ids)) {
            PageInfo pageInfo = new PageInfo(9999, 1);
            Parameter parameter = Parameter.getParameter(
                    "S_Like_persontype", RoleEum.teacher.getValue()); //教员过滤
            pageInfo.getParameters().add(parameter);
            Page<Person> page = personService.listByCondition(pageInfo);
            if (page != null) {
                personList = page.getRecords();
            }
            // 根据id列表导出
        } else {
            personList = personService.listIds(ids);
        }
        personService.adaptTeacheingModel(personList);
        // 2. 构建教员信息返回
        List<Teacher> teacherList = new ArrayList<>();
        if (CollectionUtils.isEmpty(personList)) {
            return;
        }
        // 2.1 获取所有数据字典
        Set<String> typeIds = new HashSet<>();
        personList.forEach(p -> {
            typeIds.add(p.getSpecialityid());
            typeIds.add(p.getDuty());
            typeIds.add(p.getSex());
            String teachercourse = p.getTeachercourse();
            if (StringUtils.isNotEmpty(teachercourse)) {
                String[] split = StringUtils.split(teachercourse, ",");
                typeIds.addAll(Arrays.asList(split));
            }
            typeIds.add(p.getTeachercategory());
            typeIds.add(p.getTeacherlevel());
        });
        Map<String, Selectitem> selectitemMap = new HashMap<>();
        List<Selectitem> selectitemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(typeIds)) {
            selectitemList = selectitemService.listIds(new ArrayList<>(typeIds));
        }
        if (CollectionUtils.isNotEmpty(selectitemList)) {
            selectitemMap = selectitemList.stream().collect(Collectors.toMap(Selectitem::getId, s -> s));
        }
        List<Selectitem> mlevelList = selectitemService.getAllSelectitemsByType("047DA36C359F4FB5ABF276A7008438F4");
        Map<String, Selectitem> mlevelMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(mlevelList)) {
            mlevelMap = mlevelList.stream().collect(Collectors.toMap(Selectitem::getCode, s -> s, (s1, s2) -> s1));
        }
        for (Person person : personList) {
            Teacher teacher = new Teacher();
            teacher.setMlevel(Optional.ofNullable(mlevelMap.get(ObjectUtils.toString(person.getMlevel()))).orElse(new Selectitem()).getObjname());
            teacher.setName(person.getName());
            teacher.setSex(Optional.ofNullable(selectitemMap.get(person.getSex())).orElse(new Selectitem()).getObjname());
            teacher.setBirthday(person.getBirthday());
            teacher.setSeniority(person.getYears());
            teacher.setGraduate(person.getGraduate());
            teacher.setMajor(Optional.ofNullable(selectitemMap.get(person.getSpecialityid())).orElse(new Selectitem()).getObjname()); // 所学专业
            Department department = null;
            if (StringUtils.isNotEmpty(person.getDepartmentid())) {
                department = departmentService.getById(person.getDepartmentid());
            }
            if (null != department) {
                teacher.setDepartmentname(department.getName()); // 工作单位
            }
            teacher.setDuty(Optional.ofNullable(selectitemMap.get(person.getDuty())).orElse(new Selectitem()).getObjname()); // 岗位
            teacher.setJob(person.getJob()); // 职务 ?
            teacher.setModelnum(person.getTeachingmodelname());
            teacher.setCategory(Optional.ofNullable(selectitemMap.get(person.getTeachercategory())).orElse(new Selectitem()).getObjname());
            teacher.setLevel(Optional.ofNullable(selectitemMap.get(person.getTeacherlevel())).orElse(new Selectitem()).getObjname()); //教员等级
            if (StringUtils.isNotEmpty(person.getTeachercourse())) {
                String[] split = StringUtils.split(person.getTeachercourse(), ",");
                String[] result = new String[split.length];
                for (int i = 0; i < split.length; i++) {
                    result[i] = Optional.ofNullable(selectitemMap.get(split[i])).orElse(new Selectitem()).getObjname();
                }
                teacher.setCourse(StringUtils.join(result, ",")); // 教学课程
            }
            teacher.setAchievement(person.getMajors()); //专业及主要成果
            teacherList.add(teacher);
        }
        ExcelUtils.exportExcel(teacherList, "教员导入模板", "教员列表", Teacher.class, "教员导入模板" + DateHelper.getCurDateTime(), response);
    }


    @PostMapping("/exportStudentByPersonIds")
    @ApiOperation(value = "导出学员信息", httpMethod = "POST")
    @LMSLog(desc = "导出学员信息", otype = LogType.Export, objname = "导出学员信息")
    public void exportStudentByPersonIds(@RequestBody List<String> ids, HttpServletResponse response) {
        // 1. 获取人员信息列表  未选择人员则导出全部
        List<Person> personList = new ArrayList<>();
        // 导出全部
        if (CollectionUtils.isEmpty(ids)) {
            PageInfo pageInfo = new PageInfo(9999, 1);
            Parameter parameter = Parameter.getParameter(
                    "S_Like_persontype", RoleEum.student.getValue()); //学员过滤
            pageInfo.getParameters().add(parameter);
            Page<Person> page = personService.listByCondition(pageInfo);
            if (page != null) {
                personList = page.getRecords();
            }
            // 根据id列表导出
        } else {
            personList = personService.listIds(ids);
        }
        personService.adaptPersonModel(personList);
        List<Student> studentList = new ArrayList<>();
        if (CollectionUtils.isEmpty(personList)) {
            return;
        }
        // 2.1 获取所有数据字典
        Set<String> typeIds = new HashSet<>();
        personList.forEach(p -> {
            typeIds.add(p.getSex());
            typeIds.add(p.getJob());
            typeIds.add(p.getEducation());
            typeIds.add(p.getSpecialityid());
        });
        Map<String, Selectitem> selectitemMap = new HashMap<>();
        List<Selectitem> selectitemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(typeIds)) {
            selectitemList = selectitemService.listIds(new ArrayList<>(typeIds));
        }
        if (CollectionUtils.isNotEmpty(selectitemList)) {
            selectitemMap = selectitemList.stream().collect(Collectors.toMap(Selectitem::getId, s -> s));
        }
        List<Selectitem> mlevelList = selectitemService.getAllSelectitemsByType("047DA36C359F4FB5ABF276A7008438F4");
        Map<String, Selectitem> mlevelMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(mlevelList)) {
            mlevelMap = mlevelList.stream().collect(Collectors.toMap(Selectitem::getCode, s -> s, (s1, s2) -> s1));
        }
        for (Person person : personList) {
            Student student = new Student();
            student.setName(person.getName());
            student.setSex(Optional.ofNullable(selectitemMap.get(person.getSex())).orElse(new Selectitem()).getObjname());
            student.setBirthday(person.getBirthday());
            student.setStudentName(person.getCardNum());
            Department department = null;
            if (StringUtils.isNotEmpty(person.getDepartmentid())) {
                department = departmentService.getById(person.getDepartmentid());
            }
            if (null != department) {
                student.setDepartmentname(department.getName());
            }
            student.setStudentName(person.getCardNum());
            student.setSeniority(person.getSeniority());
            student.setSpeciality(Optional.ofNullable(selectitemMap.get(person.getSpecialityid())).orElse(new Selectitem()).getObjname());
            student.setEducation(Optional.ofNullable(selectitemMap.get(person.getEducation())).orElse(new Selectitem()).getObjname());
            student.setJob(person.getJob());
            student.setPhone(person.getTel());
            String equipmentname = person.getEquipmentname();
            if (StringUtils.isNotEmpty(equipmentname)) {
                if (equipmentname.contains("/")) {
                    String[] split = equipmentname.split("/");
                    student.setEquipment(split[split.length - 1]);
                } else {
                    student.setEquipment(equipmentname);
                }
            }
            student.setRemark(person.getRemark());
            student.setMlevel(Optional.ofNullable(mlevelMap.get(ObjectUtils.toString(person.getMlevel()))).orElse(new Selectitem()).getObjname());
            studentList.add(student);
        }
        ExcelUtils.exportExcel(studentList, "学员导入模板", "学员列表", Student.class, "学员导入模板" + DateHelper.getCurDateTime(), response);
    }

    /**
     * 学员导入
     */
    @PostMapping("/importStudent")
    @ApiOperation(value = "导入学员信息", httpMethod = "POST")
    public Result importStudent(@RequestParam("file") MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return Result.error("导入文件上传失败");
        }
        List<Student> studentList = new ArrayList<>();
        try {
            studentList = ExcelUtils.importExcel(file, 1, 1, 0, Student.class);
        } catch (Exception e) {
            return Result.error("导入文件上传失败");
        }
        List<String> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(studentList)) {
            result = personService.importStudent(studentList);
        }
        if (result.size() != 0) {
            return Result.error("导入文件上传失败", result);
        }
        return Result.OK("导入成功");
    }

    @GetMapping("/getUserNamesBySysRoleLink")
    @ApiOperation(value = "根据角色获取用户名称列表", httpMethod = "GET")
    public Result<List<String>> getUserNamesBySysRoleLink(@RequestParam("roleid") String roleid) {
        List<String> userNames = this.personService.getUserNamesBySysRoleLink(roleid);
        return Result.OK(userNames);
    }

    @GetMapping("/getRoleIdByUserName")
    @ApiOperation(value = "根据用户名查询用户角色id列表", httpMethod = "GET")
    public Result<List<String>> getRoleIdByUserName(@RequestParam("userName") String userName) {
        List<String> roleIds = this.personService.getRoleIdByUserName(userName);
        return Result.OK(roleIds);
    }
}
