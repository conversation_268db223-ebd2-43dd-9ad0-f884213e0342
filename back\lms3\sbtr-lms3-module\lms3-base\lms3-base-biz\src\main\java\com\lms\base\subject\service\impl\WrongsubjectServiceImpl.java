/**
 * FileName:	ClientWrongsubject.java
 * Author:		ji<PERSON><PERSON><PERSON>
 * Time:		2013-3-4上午11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.base.subject.service.impl;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.base.feign.model.Subject;
import com.lms.base.subject.mapper.SubjectMapper;
import com.lms.base.subject.mapper.WrongsubjectMapper;
import com.lms.base.feign.model.Wrongsubject;
import com.lms.base.subject.service.WrongsubjectService;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.DBSqlUtil;
import com.sun.org.apache.bcel.internal.generic.NEW;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 保存项目的相关信息
 */
@Service("WrongsubjectService")
public class WrongsubjectServiceImpl extends BaseServiceImpl<WrongsubjectMapper, Wrongsubject> implements WrongsubjectService {

    @Resource
    private WrongsubjectMapper wrongsubjectMapper;
    @Resource
    private SubjectMapper subjectMapper;
    @Resource
    private DBSqlUtil dbSqlUtil;

    public int getMaxSeqno(String personid) throws SQLException {
        String hql = "select "
                + dbSqlUtil.getIfNullFlag()
                + "(max(k.seqno),0) as maxseqno from u_wrongsubject k where k.personid= '" + personid + "'";
        Map map = wrongsubjectMapper.getMaxSeqno(hql);
        BigDecimal maxseqno = (BigDecimal) map.get("maxseqno");
        return maxseqno.intValue();
    }


    public boolean existSubject(String personid, String subjectid) {
        LambdaQueryWrapper<Wrongsubject> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Wrongsubject::getPersonid, personid);
        wrapper.eq(Wrongsubject::getSubjectid, subjectid);
        Long count = wrongsubjectMapper.selectCount(null);
        return count > 0;
    }


    public List<Wrongsubject> getByPersonAndSubject(String personid, String id) {
        LambdaQueryWrapper<Wrongsubject> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Wrongsubject::getPersonid, personid);
        wrapper.eq(Wrongsubject::getSubjectid, id);
        return this.list(wrapper);
    }

    public List<Wrongsubject> getWrongSubjectList(List<String> courseidList) {
        LambdaQueryWrapper<Wrongsubject> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Wrongsubject::getPersonid, ContextUtil.getPersonId());
        if (courseidList.size() > 0) {
            List<Subject> subjects = subjectMapper.selectList(new LambdaQueryWrapper<Subject>().in(Subject::getComponentid, courseidList));
            if (CollectionUtils.isNotEmpty(subjects)) {
                List<String> subjectIds = subjects.stream().map(Subject::getId).collect(Collectors.toList());
                wrapper.in(Wrongsubject::getSubjectid, subjectIds);
            }
        }
        return wrongsubjectMapper.selectList(wrapper);
    }


}
