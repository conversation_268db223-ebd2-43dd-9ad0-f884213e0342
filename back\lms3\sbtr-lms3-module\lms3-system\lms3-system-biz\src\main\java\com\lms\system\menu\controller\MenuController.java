/**
 * 类文件说明：
 * Title:		CoursewareController.java
 * Author:		zhouweirong
 * Time:		2017-11-15 下午04:32:41
 * CopyRight: 	SBTR LTD.
 * description:	TODO
 */
package com.lms.system.menu.controller;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.lms.system.feign.model.Menu;
import com.lms.system.feign.model.MenuModel;
import com.lms.system.feign.model.MenuRoleLink;
import com.lms.system.menu.service.MenuRoleLinkService;
import com.lms.system.menu.service.MenuService;
import com.lms.system.feign.model.Role;
import com.lms.system.role.service.RoleService;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.StringHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 类说明：
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/menu")
@Api(value = "菜单管理", tags = "菜单管理")
public class MenuController extends BaseController {

    protected static final Logger logger = LoggerFactory
            .getLogger(MenuController.class);

    @Resource
    private MenuService menuService;

    @Resource
    private MenuRoleLinkService menuRoleLinkService;

    @Resource
    private RoleService roleService;

    @PostMapping(value = {"/listpage"})
    @ApiOperation(value = "分页查询菜单", httpMethod = "POST")
    public Result<Page> listpage(@RequestBody JSONObject request) {
        PageInfo pageInfo = getPageInfo(request);
        pageInfo.setOrderName("seqno");
        pageInfo.setSort(pageInfo.ASC);
        Page<Menu> menus = menuService.listByCondition(pageInfo);
        return Result.OK(menus);
    }

    @GetMapping(value = {"/getRoleLinks"})
    @ApiOperation(value = "查询", httpMethod = "GET")
    public Result<List> getRoleLinks() {
        List menuRoleLinks = menuRoleLinkService.getRoleLinks();
        return Result.OK(menuRoleLinks);
    }

    // 批量删除
    @LMSLog(desc = "删除菜单", otype = LogType.Delete)
    @ApiOperation(value = "删除菜单", httpMethod = "DELETE")
    @RequestMapping(value = {"/batchremove"}, method = RequestMethod.DELETE)
    public Result batchDeleteMenu(@RequestParam("ids") String ids) {
        String[] idarray = ids.split(",");
        List<String> idList = new ArrayList<String>();
        for (int i = 0; i < idarray.length; i++) {
            String id = idarray[i];
            idList.add(id);
        }
        this.menuService.removeBatchByIds(idList);
        return Result.OK();
    }

    @LMSLog(desc = "新增菜单", otype = LogType.Save)
    @RequestMapping(value = {"/add"}, method = RequestMethod.POST)
    @ApiOperation(value = "新增菜单", httpMethod = "POST")
    public Result<Menu> saveMenu(@RequestBody Menu menu) {
        int isleaf = ObjectUtil.isNotEmpty(menu.getPid()) ? 1 : 0;
        menu.setIsleaf(isleaf);
        menu.setMenutype(1);
        this.menuService.saveOrUpdate(menu);
        return Result.OK(menu);
    }

    @LMSLog(desc = "修改菜单", otype = LogType.Update)
    @RequestMapping(value = {"/modify"}, method = RequestMethod.POST)
    @ApiOperation(value = "修改菜单", httpMethod = "POST")
    public Result<Menu> updateMenu(@RequestBody Menu menu) {
        int isleaf = ObjectUtil.isNotEmpty(menu.getPid()) ? 1 : 0;
        menu.setIsleaf(isleaf);
        menuService.saveOrUpdate(menu);
        menu.setMenutype(1);
        return Result.OK(menu);
    }

    // 根据当前用户的角色获取菜单树
    @RequestMapping(value = {"/getmenurighttree"}, method = RequestMethod.GET)
    @ApiOperation(value = "根据当前用户的角色获取菜单树", httpMethod = "GET")
    public Result<List<MenuModel>> getMenuRightTree(@RequestParam("roleid") String roleid) {
        List<MenuModel> menuList = this.menuService.getMenuRightTree(StringHelper.null2String(roleid, ContextUtil.getRoleId()));
        return Result.OK(menuList);
    }

    //@LMSLog(desc = "查询菜单列表", otype = LogType.List, order = 1, method ="getMenuTreeByRole")
    @RequestMapping(value = {"/getMenuTree"}, method = RequestMethod.GET)
    @ApiOperation(value = "查询菜单列表", httpMethod = "GET")
    public Result<List<MenuModel>> getMenuTreeByRole() {
        List<MenuModel> menuList = this.menuService.getMenuTree();
        return Result.OK(menuList);
    }

    @RequestMapping(value = {"/getMenuTreeByRole"}, method = RequestMethod.GET)
    @ApiOperation(value = "根据角色获取菜单树", httpMethod = "GET")
    public Result<List<String>> getMenuTreeByRole(@RequestParam("roleid") String roleid) {
        List<String> menuList = this.menuService.getMenuTreeByRole(roleid);
        return Result.OK(menuList);
    }

    @RequestMapping(value = {"/setRoleMenuRight"}, method = RequestMethod.POST)
    @ApiOperation(value = "修改用户角色", httpMethod = "POST")
    @LMSLog(desc = "修改用户角色", otype = LogType.Update, objname = "修改用户角色")
    public Result setRoleMenuRight(@RequestParam("id") String id,
                                   @RequestParam("roleid") String roleid,
                                   @RequestParam("ischecked") Boolean ischecked) {
        MenuRoleLink link = new MenuRoleLink();
        link.setMenuid(id);
        link.setRoleid(roleid);
        if (StringHelper.isEmpty(id) || StringHelper.isEmpty(roleid)) {
            return Result.error("无法获取要修改的用户信息或角色信息!");
        }
        if (ischecked) {
            this.menuRoleLinkService.saveOrUpdate(link);
        } else {
            this.menuRoleLinkService.deleteMenuRole(id, roleid);
        }

        Role role = roleService.getById(roleid);
        Menu menu = menuService.getById(id);
        commonSystemApi.saveLog(StringHelper.null2String("角色菜单列表"), "修改" + role.getName() + "的（" + menu.getMenuname() + "）菜单权限为" + ischecked, LogType.Update, "操作成功！");
        return Result.OK();
    }

}
