<template>
  <div class="courseware-management">
    <TrTable
      :columns="columns"
      :dataSource="dataSource"
      :queryItems="queryItems"
      :operationButtons="operationButtons"
      :loading="loading"
      :showImport="false"
      :showView="false"
      rowKey="id"
      :pagination="pagination"
      @query="handleQuery"
      @reset="handleReset"
      @refresh="handleRefresh"
      @add="handleAdd"
      @edit="handleEdit"
      @delete="handleDelete"
      @batchDelete="handleBatchDelete"
      @export="handleExport"
      @operationButton="handleOperationButton"
      @paginationChange="handlePaginationChange"
    >
      <!-- 自定义操作列 -->
      <template #operation="{ record }">
        <a-space>
          <a-button type="link" size="small" @click="handleEdit(record)">
            <template #icon><EditOutlined /></template>
            编辑
          </a-button>
          <a-button type="link" size="small" @click="handlePlay(record)">
            <template #icon><PlayCircleOutlined /></template>
            播放
          </a-button>
          <a-button
            type="link"
            size="small"
            danger
            @click="handleDelete(record)"
          >
            <template #icon><DeleteOutlined /></template>
            删除
          </a-button>
          <a-button type="link" size="small" @click="handleDesign(record)">
            <template #icon><AntDesignOutlined /></template>
            流程设计
          </a-button>
        </a-space>
      </template>
    </TrTable>

    <!-- 编辑/新增弹窗 -->
    <TrDialog
      v-model:open="dialogVisible"
      :title="dialogTitle"
      :width="800"
      :draggable="true"
      :confirm-loading="dialogLoading"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <edit-form
        ref="editFormRef"
        :model="formModel"
        :mode="dialogMode"
        @submit="handleFormSubmit"
        @cancel="handleDialogCancel"
      />
    </TrDialog>

    <!-- 播放预览弹窗 -->
    <TrDialog
      v-model:open="playDialogVisible"
      title="课件预览"
      :width="1000"
      :draggable="true"
      :show-footer="false"
    >
      <play-dialog
        :courseware="currentCourseware"
        @close="playDialogVisible = false"
      />
    </TrDialog>
    <TrDialog
      v-model:open="visibleDesign"
      :title="translateText('流程设计')"
      :show-footer="false"
      :fullscreen="true"
      destroyOnClose
      @cancel="visibleDesign = false"
    >
      <bpmn-editor :cid="currentCourseware.id" />
    </TrDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { message, Modal } from "ant-design-vue";
import {
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  AntDesignOutlined
} from "@ant-design/icons-vue";
import TrTable from "@/components/TrTable/index.vue";
import TrDialog from "@/components/TrDialog/index.vue";
import BpmnEditor from "./workflow/bpmnEditor.vue";

import {
  getCoursewareList,
  addCourseware,
  updateCourseware,
  deleteCourseware,
  exportCourseware
} from "@/api/mrPlatform";
import EditForm from "./modules/editForm.vue";
import PlayDialog from "./modules/playDialog.vue";
import type { PaginationState, SortState } from "@/components/TrTable/types";

import { coursewaretypeOptions } from "@/data/mrPlatform";
import { translateText } from "@/utils/translation";

// 查询表单项配置
const queryItems = [
  {
    key: "name",
    label: "课件名称",
    component: "a-input",
    props: { placeholder: "请输入课件名称" }
  },
  {
    key: "number",
    label: "课件编号",
    component: "a-input",
    props: { placeholder: "请输入课件编号" }
  },
  {
    key: "coursewaretype",
    label: "课件类型",
    component: "a-select",
    props: {
      placeholder: "请选择课件类型",
      allowClear: true,
      options: coursewaretypeOptions
    }
  }
];

// 表格列配置
const columns = [
  { title: "课件名称", dataIndex: "name", key: "name", width: 200 },
  { title: "课件编号", dataIndex: "number", key: "number", width: 150 },
  {
    title: "课件类型",
    dataIndex: "coursewaretype",
    key: "coursewaretype",
    width: 120,
    render: (text: string) => {
      const found = coursewaretypeOptions.find(opt => opt.value === text);
      return found ? found.label : text || "未知";
    }
  },
  { title: "责任人", dataIndex: "principal", key: "principal", width: 120 },
  { title: "责任单位", dataIndex: "dutyunit", key: "dutyunit", width: 150 },
  { title: "版本", dataIndex: "version", key: "version", width: 80 },
  {
    title: "修改时间",
    dataIndex: "modifydate",
    key: "modifydate",
    width: 180
  }
];

// 操作列扩展按钮
const operationButtons = [
  {
    key: "play",
    label: "播放",
    props: {
      type: "link",
      icon: PlayCircleOutlined
    }
  }
];

// 响应式数据
const loading = ref(false);
const dataSource = ref<any[]>([]);
const dialogVisible = ref(false);
const dialogMode = ref<"add" | "edit">("add");
const dialogLoading = ref(false);
const playDialogVisible = ref(false);
const currentCourseware = ref<any>(null);
const editFormRef = ref();

const visibleDesign = ref(false);
// 新分页状态，交由TrTable管理
const pagination = reactive<PaginationState>({
  pageIndex: 1,
  pageSize: 10,
  total: 0
});

// 表单数据
const formModel = reactive<any>({
  name: "",
  number: "",
  coursewaretype: "",
  principal: "",
  dutyunit: "",
  version: "",
  description: "",
  keyword: "",
  language: "",
  status: 1
});

// 计算属性
const dialogTitle = computed(() => {
  return dialogMode.value === "add" ? "新增课件" : "编辑课件";
});

// 查询事件
async function handleQuery(form: any = {}) {
  try {
    loading.value = true;

    // 移除模拟API调用延迟
    // await mockDelay(300);

    // 合并分页参数
    const params = {
      ...form,
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize
    };

    const response = await getCoursewareList(params);

    if (response && response.success && response.result) {
      dataSource.value = response.result.records || [];
      // 更新total
      if (typeof response.result.total === "number") {
        pagination.total = response.result.total;
      } else if (Array.isArray(response.result.records)) {
        pagination.total = response.result.records.length;
      }
    } else {
      dataSource.value = [];
      pagination.total = 0;
      message.warning("未查询到数据");
    }
  } catch (error) {
    console.error("查询课件列表失败:", error);
    message.error("查询课件列表失败");
    dataSource.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
}

// 重置查询
async function handleReset() {
  try {
    loading.value = true;

    // 移除模拟API调用延迟
    // await mockDelay(200);

    // 重置分页
    pagination.pageIndex = 1;
    pagination.pageSize = 10;

    const response = await getCoursewareList({
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize
    });

    if (response && response.success && response.result) {
      dataSource.value = response.result.records || [];
      if (typeof response.result.total === "number") {
        pagination.total = response.result.total;
      } else if (Array.isArray(response.result.records)) {
        pagination.total = response.result.records.length;
      }
    }
  } catch (error) {
    console.error("重置查询失败:", error);
    message.error("重置查询失败");
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
}

// 刷新数据
async function handleRefresh() {
  await handleQuery({});
}

// 分页变化事件
function handlePaginationChange(newPagination: PaginationState) {
  pagination.pageIndex = newPagination.pageIndex;
  pagination.pageSize = newPagination.pageSize;
  handleQuery();
}

// 新增课件
function handleAdd() {
  dialogMode.value = "add";
  resetFormModel();
  dialogVisible.value = true;
}

// 编辑课件
function handleEdit(record: any) {
  dialogMode.value = "edit";
  Object.assign(formModel, record);
  dialogVisible.value = true;
}

// 删除课件
function handleDelete(record: any) {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除课件"${record.name}"吗？此操作不可恢复。`,
    okText: "确定",
    cancelText: "取消",
    okType: "danger",
    async onOk() {
      try {
        const response = await deleteCourseware(record.id);
        if (response && response.success) {
          message.success("删除成功");
          await handleRefresh();
        } else {
          message.error(response?.message || "删除失败");
        }
      } catch (error) {
        console.error("删除课件失败:", error);
        message.error("删除课件失败");
      }
    }
  });
}

// 批量删除
function handleBatchDelete(keys: any[], rows: any[]) {
  if (keys.length === 0) {
    message.warning("请选择要删除的课件");
    return;
  }

  Modal.confirm({
    title: "确认批量删除",
    content: `确定要删除选中的 ${keys.length} 个课件吗？此操作不可恢复。`,
    okText: "确定",
    cancelText: "取消",
    okType: "danger",
    async onOk() {
      try {
        // keys为id数组，拼接为逗号分隔字符串
        const ids = keys.map(item => item.id).toString();
        const response = await deleteCourseware(ids);
        if (response && response.success) {
          message.success("批量删除成功");
          await handleRefresh();
        } else {
          message.error(response?.message || "批量删除失败");
        }
      } catch (error) {
        console.error("批量删除课件失败:", error);
        message.error("批量删除课件失败");
      }
    }
  });
}

// 导出课件
async function handleExport(keys: any[], rows: any[]) {
  try {
    // 移除模拟API调用延迟
    // await mockDelay(1000);

    const exportData = keys.length > 0 ? keys : null;
    const response = await exportCourseware(exportData);

    if (response && response.success) {
      message.success("导出成功");
      // 处理文件下载
      if (response.result && response.result.downloadUrl) {
        window.open(response.result.downloadUrl, "_blank");
      }
    } else {
      message.error(response?.message || "导出失败");
    }
  } catch (error) {
    console.error("导出课件失败:", error);
    message.error("导出课件失败");
  }
}

// 播放课件
function handlePlay(record: any) {
  currentCourseware.value = record;
  playDialogVisible.value = true;
}

// 操作按钮事件
function handleOperationButton({ key, record }: { key: string; record: any }) {
  switch (key) {
    case "play":
      handlePlay(record);
      break;
    default:
      console.log("未知的操作按钮:", key);
  }
}

// 弹窗确认
async function handleDialogOk() {
  if (editFormRef.value) {
    await editFormRef.value.submit();
  }
}

// 弹窗取消
function handleDialogCancel() {
  dialogVisible.value = false;
  resetFormModel();
}

// 表单提交
async function handleFormSubmit(formData: any) {
  try {
    dialogLoading.value = true;

    // 移除模拟API调用延迟
    // await mockDelay(600);

    let response;
    if (dialogMode.value === "add") {
      response = await addCourseware(formData);
    } else {
      response = await updateCourseware(formData);
    }

    if (response && response.success) {
      message.success(dialogMode.value === "add" ? "新增成功" : "更新成功");
      dialogVisible.value = false;
      await handleRefresh();
    } else {
      message.error(
        response?.message ||
          (dialogMode.value === "add" ? "新增失败" : "更新失败")
      );
    }
  } catch (error) {
    console.error("保存课件失败:", error);
    message.error("保存课件失败");
  } finally {
    dialogLoading.value = false;
  }
}

// 重置表单数据
function resetFormModel() {
  Object.assign(formModel, {
    name: "",
    number: "",
    coursewaretype: "",
    principal: "",
    dutyunit: "",
    version: "",
    description: "",
    keyword: "",
    language: "",
    status: 1
  });
}

function handleDesign(record) {
  currentCourseware.value = record;
  visibleDesign.value = true;
}

// 初始化数据
async function initData() {
  await handleQuery({});
}

// 组件挂载时初始化
onMounted(() => {
  initData();
});
</script>

<style lang="scss" scoped>
.courseware-management {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background: #fff;
}

:deep(.tr-table-wrapper) {
  height: 100%;
}
</style>
