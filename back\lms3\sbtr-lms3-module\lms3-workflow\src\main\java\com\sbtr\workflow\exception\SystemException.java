package com.sbtr.workflow.exception;

import java.io.Serializable;

/**
 * 系统异常
 *
 * <AUTHOR>
 * @Version V5.4.21
 * @Email <EMAIL>
 * @Date 2023/03/08
 */
public class SystemException extends RuntimeException implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private Integer statusCode;

    /**
     * 错误提示
     */
    private String message;

    public SystemException(String message) {
        this.message = message;
    }

    public SystemException(Integer statusCode) {
        this.statusCode = statusCode;
    }

    public SystemException(Integer statusCode, String message) {
        this.statusCode = statusCode;
        this.message = message;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
