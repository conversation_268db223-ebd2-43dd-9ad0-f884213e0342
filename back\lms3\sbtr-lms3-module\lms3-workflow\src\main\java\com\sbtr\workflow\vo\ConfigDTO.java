package com.sbtr.workflow.vo;


import java.util.List;

public class ConfigDTO {
    private String layout;
    private String labelAlign;
    private LabelColDTO labelCol;
    private WrapperColDTO wrapperCol;
    private Boolean hideRequiredMark;
    private String customStyle;
    private String columns;
    private Boolean print;
    private List<?> implicitRules;
    private List<EventDataDTO> eventData;

    public String getLayout() {
        return layout;
    }

    public void setLayout(String layout) {
        this.layout = layout;
    }

    public String getLabelAlign() {
        return labelAlign;
    }

    public void setLabelAlign(String labelAlign) {
        this.labelAlign = labelAlign;
    }

    public LabelColDTO getLabelCol() {
        return labelCol;
    }

    public void setLabelCol(LabelColDTO labelCol) {
        this.labelCol = labelCol;
    }

    public WrapperColDTO getWrapperCol() {
        return wrapperCol;
    }

    public void setWrapperCol(WrapperColDTO wrapperCol) {
        this.wrapperCol = wrapperCol;
    }

    public Boolean getHideRequiredMark() {
        return hideRequiredMark;
    }

    public void setHideRequiredMark(Boolean hideRequiredMark) {
        this.hideRequiredMark = hideRequiredMark;
    }

    public String getCustomStyle() {
        return customStyle;
    }

    public void setCustomStyle(String customStyle) {
        this.customStyle = customStyle;
    }

    public String getColumns() {
        return columns;
    }

    public void setColumns(String columns) {
        this.columns = columns;
    }

    public Boolean getPrint() {
        return print;
    }

    public void setPrint(Boolean print) {
        this.print = print;
    }

    public List<?> getImplicitRules() {
        return implicitRules;
    }

    public void setImplicitRules(List<?> implicitRules) {
        this.implicitRules = implicitRules;
    }

    public List<EventDataDTO> getEventData() {
        return eventData;
    }

    public void setEventData(List<EventDataDTO> eventData) {
        this.eventData = eventData;
    }

    public static class LabelColDTO {
        private Integer xs;
        private Integer sm;
        private Integer md;
        private Integer lg;
        private Integer xl;
        private Integer xxl;

        public Integer getXs() {
            return xs;
        }

        public void setXs(Integer xs) {
            this.xs = xs;
        }

        public Integer getSm() {
            return sm;
        }

        public void setSm(Integer sm) {
            this.sm = sm;
        }

        public Integer getMd() {
            return md;
        }

        public void setMd(Integer md) {
            this.md = md;
        }

        public Integer getLg() {
            return lg;
        }

        public void setLg(Integer lg) {
            this.lg = lg;
        }

        public Integer getXl() {
            return xl;
        }

        public void setXl(Integer xl) {
            this.xl = xl;
        }

        public Integer getXxl() {
            return xxl;
        }

        public void setXxl(Integer xxl) {
            this.xxl = xxl;
        }
    }

    public static class WrapperColDTO {
        private Integer xs;
        private Integer sm;
        private Integer md;
        private Integer lg;
        private Integer xl;
        private Integer xxl;

        public Integer getXs() {
            return xs;
        }

        public void setXs(Integer xs) {
            this.xs = xs;
        }

        public Integer getSm() {
            return sm;
        }

        public void setSm(Integer sm) {
            this.sm = sm;
        }

        public Integer getMd() {
            return md;
        }

        public void setMd(Integer md) {
            this.md = md;
        }

        public Integer getLg() {
            return lg;
        }

        public void setLg(Integer lg) {
            this.lg = lg;
        }

        public Integer getXl() {
            return xl;
        }

        public void setXl(Integer xl) {
            this.xl = xl;
        }

        public Integer getXxl() {
            return xxl;
        }

        public void setXxl(Integer xxl) {
            this.xxl = xxl;
        }
    }
}
