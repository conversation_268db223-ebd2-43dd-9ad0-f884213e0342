package com.lms.common.feign.fallback;

import com.lms.common.feign.api.CommonSystemApi;
import com.lms.common.feign.dto.Log;
import com.lms.common.feign.dto.Setting;
import com.lms.common.model.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * misboot-mdata相关接口 模块降级处理
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
@Component
public class CommonSystemApiFallbackFactory implements FallbackFactory<CommonSystemApi> {

    private static final Logger log = LoggerFactory.getLogger(CommonSystemApiFallbackFactory.class);

    @Override
    public CommonSystemApi create(Throwable throwable) {
        log.error("CommonSystemApi服务调用失败:{}", throwable.getMessage());

        return new CommonSystemApi() {
            @Override
            public Result saveLog(String objname, String desc, String logtype, String result) {
                return null;
            }

            @Override
            public Result saveLog(Log log) {
                return null;
            }

            @Override
            public String translateContent(String keyword, String module, String variables) {
                return null;
            }

            @Override
            public String translateContent(String keyword, String module, String[] variables) {
                return null;
            }

            @Override
            public String translateContent(String keyword) {
                return null;
            }

            @Override
            public String translateContentNotCreated(String keyword, String module) {
                return null;
            }

            @Override
            public Result<String> getSettingValueById(String id) {
                return null;
            }

            @Override
            public Result<Setting> getById(String id) {
                return null;
            }

            @Override
            public String getNewCode(String key) {
                return "";
            }
        };
    }
}
