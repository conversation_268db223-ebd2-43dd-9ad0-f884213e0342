/*
 * Copyright (c) 2019- 2019 threefish(https://gitee.com/threefish https://github.com/threefish) All Rights Reserved.
 * 本项目完全开源，商用完全免费。但请勿侵犯作者合法权益，如申请软著等。
 * 最后修改时间：2019/10/07 18:26:07
 * 源 码 地 址：https://gitee.com/threefish/NutzFw
 */

package com.sbtr.workflow.utils;

import org.flowable.bpmn.model.ExtensionElement;
import org.flowable.editor.language.json.converter.BpmnJsonConverter;

/**
 * <AUTHOR> <EMAIL>
 * @date: 2019/4/9
 */
public class FlowUtils {

    public static ExtensionElement buildExtensionElement(String name, String textValue) {
        ExtensionElement extensionElement = new ExtensionElement();
        extensionElement.setName(name);
        extensionElement.setNamespacePrefix("flowable");
        extensionElement.setNamespace(BpmnJsonConverter.MODELER_NAMESPACE);
        extensionElement.setElementText(textValue);
        return extensionElement;
    }



}
