package com.sbtr.workflow.service;

import com.sbtr.workflow.model.ActMyForm;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.vo.FormLayoutVo;

import java.util.List;

/**
 * 表单信息
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-10 11:10:49
 */
public interface ActMyFormService extends WorkflowBaseService<ActMyForm, String> {

    /**
     * 分页
     *
     * @return
     * @Param pageParam 分页参数
     * @Param filterSort 过滤排序字段
     */
    PageSet<ActMyForm> getPageSet(PageParam pageParam, String filterSort);

    /**
     * 批量删除
     *
     * @return
     * @Param uuid 主键
     */
    int executeDeleteBatch(String[] uuid);

    ActMyForm selectByProcessInstanceId(String processInstanceId, String modelKey, String processDefinitionId);

    Integer updateFormByPrimaryKeySelective(String uuid, String formDesign);

    List<ActMyForm> getListDataByModelKeyAndProcdefId(String modelKey, String procdefId);

    String getFormLayoutFormJSON(String formLayou);

    List<FormLayoutVo> getFormLayoutForm(String formUuid);

    Integer updateFormDesignByUuid(String json, String uuid);
}
