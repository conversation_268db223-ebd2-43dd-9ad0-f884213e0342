package com.sbtr.workflow.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.model.ActMyButton;
import com.sbtr.workflow.service.ActMyButtonService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.StringUtil.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * 流程按钮表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-23 08:10:59
 */
@Api(tags = {"流程按钮"})
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/flowButton")
public class ActMyButtonController extends WorkflowBaseController {

    @Autowired
    private ActMyButtonService actMyButtonService;

    //系统默认按钮标识不允许删除修改
    public static final String BUTTON_CODE = "agree,reject,update,transfer,returnAnyNode,rejectPreviousStep,delegate," +
            "signatureBefore,signatureAfter,termination,withdraw";


    @ResponseBody
    @RequestMapping(value = "/getPageSet", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam, String buttonCode, String buttonName) {
        PageSet<ActMyButton> pageSet = actMyButtonService.getPageSet(pageParam, buttonCode, buttonName);
        return pageSet;
    }

    @ApiOperation(value = "获得FlowButton模块详细数据")
    @ApiImplicitParam(name = "uuid", value = "获得FlowButton模块详细数据", required = true, dataType = "String")
    @ResponseBody
    @RequestMapping(value = "/getDetailByUuid")
    public Object getDetailByUuid(String uuid) {
        ActMyButton flowButton = actMyButtonService.selectByPrimaryKey(uuid);
        return flowButton;
    }

    @Log(title = "流程按钮-保存模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @ApiOperation(value = "保存FlowButton模块数据")
    @ApiImplicitParam(name = "flowButton", value = "保存FlowButton模块数据", required = true, dataType = "FlowButton")
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Object save(@ModelAttribute ActMyButton flowButton) {
        if (StrUtil.isBlank(flowButton.getButtonCode())) {
            return failure("必要参数不能为空！！！");
        }
        Boolean bol = StringUtils.isAlphanumeric(flowButton.getButtonCode());
        if (!bol) {
            return failure("标识只能包含数字或字母，且长度要在1-50位之间");
        }
        Example example = new Example(ActMyButton.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("buttonCode", flowButton.getButtonCode());
        List<ActMyButton> lists = actMyButtonService.selectByExample(example);
        if (lists.size() > 0) {
            return failure("标识已存在!!!");
        }

        setLogMessage(flowButton.getButtonName(),"");
        int result = actMyButtonService.insertSelective(getSaveData(flowButton));
        return result > 0 ? success(CommonConstants.SAVE_SUCCESS_MSG) : failure(CommonConstants.SAVE_FAILURE_MSG);
    }

    @Log(title = "流程按钮-更新模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.UPDATE, method = "getLogMessage")
    @ApiOperation(value = "更新FlowButton模块数据")
    @ApiImplicitParam(name = "flowButton", value = "更新FlowButton模块数据", required = true, dataType = "FlowButton")
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Object update(@ModelAttribute ActMyButton flowButton) {
        if (StrUtil.isAllBlank(flowButton.getButtonCode(), flowButton.getUuid())) {
            return failure("必要参数不能为空！！！");
        }
        //系统默认标识不允许修改
        ActMyButton actMyButton = actMyButtonService.selectByPrimaryKey(flowButton.getUuid());
        if (!flowButton.getButtonCode().equals(actMyButton.getButtonCode())   && BUTTON_CODE.contains(actMyButton.getButtonCode()) ) {
            return failure("该条数据标识不予许修改！！！");
        }

        Boolean bol = StringUtils.isAlphanumeric(flowButton.getButtonCode());
        if (!bol) {
            return failure("标识只能包含数字或字母，且长度要在1-50位之间");
        }
        Boolean isCodeExist = actMyButtonService.getListByCodeAndUuid(flowButton.getButtonCode(), flowButton.getUuid());
        if (isCodeExist) {
            return failure("更新失败！标识已存在");
        }
        setLogMessage(flowButton.getButtonName(),"");
        int result = actMyButtonService.updateByPrimaryKeySelective(getUpdateData(flowButton));
        return result > 0 ? success(CommonConstants.UPDATE_SUCCESS_MSG) : failure(CommonConstants.UPDATE_FAILURE_MSG);
    }


    @Log(title = "流程按钮-删除模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ApiOperation(value = "删除FlowButton模块数据")
    @ApiImplicitParam(name = "flowButton", value = "删除FlowButton模块数据", required = true, dataType = "FlowButton")
    @ResponseBody
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.POST)
    public Object deleteBatch(@RequestParam String[] uuid) {
        if (ArrayUtil.isEmpty(uuid)) {
            return failure("必要参数uuid不能为空！！！");
        }
        //自带按钮标识不能删除
        StringBuilder logStr=new StringBuilder();
        for (int i = 0; i < uuid.length; i++) {
            ActMyButton actMyButton = actMyButtonService.selectByPrimaryKey(uuid[i]);
            if (ObjectUtil.isNotEmpty(actMyButton) && BUTTON_CODE.contains(actMyButton.getButtonCode())) {
                return failure("该条数据标识不予许删除！！！");
            }
            logStr.append(actMyButton.getButtonName()+",");
        }
        setLogMessage(logStr.toString(),"");
        int result = actMyButtonService.executeDeleteBatch(uuid);
        return result > 0 ? success(CommonConstants.DELETE_SUCCESS_MSG) : failure(CommonConstants.DELETE_FAILURE_MSG);
    }


}
