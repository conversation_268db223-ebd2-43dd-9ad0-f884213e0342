package com.lms.examine.examinepage.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.examine.feign.model.Examinepage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.repository.query.Param;

@Mapper
public interface ExaminePageMapper extends BaseMapper<Examinepage> {

    @Select("${sql}")
    List<Map<String, Object>> listQuery(@Param("sql") String sql);

    @Select("from Examinepage where planid=?1 and status=1")
	List<Examinepage> listExaminepage(String planId);

	@Select(value = "update u_examinepage set status = '2' where id in (:ids)")
	void batchReleaseExaminepage(@Param(value = "ids") List<String> ids);
}
