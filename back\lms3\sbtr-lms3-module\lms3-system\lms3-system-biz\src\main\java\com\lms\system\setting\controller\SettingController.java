/**
 * FileName:SettingController.java
 * Author:<PERSON><PERSON><PERSON><PERSON>
 * Time  :2013-3-7下午4:18:10
 * CopyRight: SBTR LTD.
 * decription:to-do
 */
package com.lms.system.setting.controller;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.system.feign.model.Setting;
import com.lms.common.model.SettingConfig;
import com.lms.system.setting.service.SettingService;
import com.lms.system.setting.service.impl.SettingServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;


/**
 * <AUTHOR> 系统设置控制层
 */
@RestController
@RequestMapping("/setting")
@Api(value = "系统配置",tags = {"系统配置"})
public class SettingController extends BaseController<Setting> {

    @Resource
    private SettingService settingService;

    @Resource
    private RestTemplate restTemplate;

    private static final Logger log = LoggerFactory.getLogger(SettingController.class);

    @ApiOperation(value = "系统配置列表查询")
    @PostMapping(value = {"/listpage"})
    public Result getSettingList(@RequestBody JSONObject jsonObject) {
        // 设置分页参数,查询参数
        PageInfo pageInfo = getPageInfo(jsonObject);
        Page<Setting> settings = settingService.listByCondition(pageInfo);
        return Result.OK(settings);
    }

    // 获取系统名称
    @ApiOperation(value = "获取系统名称")
    @RequestMapping(value = {"/getSystemName"}, method = RequestMethod.GET)
    public Result getSyetemName() {
        Setting setting = settingService.getById(SettingConfig.CONFIG_SYSTEMNAME);
        return Result.OK(setting.getItemvalue());
    }

    // 获取系统语言名称
    @ApiOperation(value = "获取系统语言名称")
    @GetMapping(value = {"/getSystemLanguage"})
    public Result getSyetemLanguage() {
        Setting setting = settingService.getById(SettingConfig.CONFIG_SYSTEMLANGUAGE);
        return Result.OK(setting.getItemvalue());
    }

    @ApiOperation(value = "根据ID获取系统配置的值")
    @GetMapping(value = {"/getSettingValueById"})
    public Result<String> getSettingValueById(@RequestParam String id) {
        Setting setting = settingService.getById(id);
        return Result.OK(setting.getItemvalue());
    }

    @LMSLog(desc = "修改系统配置", otype = LogType.Update)
    @ApiOperation(value = "修改系统配置")
    @PostMapping(value = {"/modify"})
    public Result update(@RequestBody Setting setting) {
        settingService.saveOrUpdate(setting);
        return Result.OK(setting);
    }

    @LMSLog(desc = "删除系统配置", otype = LogType.Delete)
    @ApiOperation(value = "删除系统配置")
    @DeleteMapping(value = {"/del/{id}"})
    public Result delete(@PathVariable("id") String id) {
        this.settingService.removeById(id);
        // 删除用户时，需要修改settingCache
        return Result.OK();
    }


    @LMSLog(desc = "批量删除系统配置", otype = LogType.Delete)
    @ApiOperation(value = "批量删除系统配置")
    @GetMapping(value = {"/batchremove"})
    public Result batchDelete(@RequestParam("ids") String ids) {
        String[] idarray = ids.split(",");
        List<String> idList = new ArrayList<String>();
        for (int i = 0; i < idarray.length; i++) {
            String id = idarray[i];
            idList.add(id);
        }
        this.settingService.removeBatchByIds(idList);
        return Result.OK();
    }

    @LMSLog(desc = "新增系统配置", otype = LogType.Save)
    @ApiOperation(value = "新增系统配置")
    @PostMapping(value = {"/add"})
    public Result save(Setting setting) {
        setting.setStatus(1);
        this.settingService.saveOrUpdate(setting);
        return Result.OK(setting);
    }

    @ApiOperation(value = "根据ID获取系统配置对象")
    @GetMapping(value = {"/getById"})
    public Result getById(@RequestParam String id) {
        Setting setting = settingService.getById(id);
        return Result.OK(setting);
    }

    @GetMapping(value = {"/addEsFile"})
    @ApiOperation(value = "添加es的file索引")
    public Result addEsFile(@RequestParam String ip, @RequestParam String port) throws IOException {
        ClassPathResource resource = new ClassPathResource("file.json");
        String json = FileCopyUtils.copyToString(new InputStreamReader(resource.getInputStream()));
        String url = "http://" + ip + ":" + port + "/file";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(json, headers);
        ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.PUT, request, String.class);
        return Result.OK(exchange);
    }

}
