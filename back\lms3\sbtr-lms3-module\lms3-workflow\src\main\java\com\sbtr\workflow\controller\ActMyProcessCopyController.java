package com.sbtr.workflow.controller;

import cn.ewsd.common.bean.LoginInfo;
import cn.hutool.core.util.StrUtil;
import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.model.ActMyProcessCopy;
import com.sbtr.workflow.service.ActMyProcessCopyService;
import com.sbtr.workflow.service.BusinessSystemDataService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * 流程抄送表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
@Api(tags = {"流程抄送"})
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/actMyProcessCopy")
public class ActMyProcessCopyController extends WorkflowBaseController {
    @Resource
    public BusinessSystemDataService businessSystemDataService;
    @Autowired
    private ActMyProcessCopyService actMyProcessCopyService;

    @ApiOperation(value = "获取流程抄送分页数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String"),
    })
    @ResponseBody
    @RequestMapping(value = "/getPageSet", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam, String formName) {
        PageSet<ActMyProcessCopy> pageSet = actMyProcessCopyService.getPageSet(pageParam, formName, businessSystemDataService.getUserNameId());
        return pageSet;
    }

    @ApiOperation(value = "获得ActMyProcessCopy模块详细数据")
    @ApiImplicitParam(name = "uuid", value = "获得ActMyProcessCopy模块详细数据", required = true, dataType = "String")
    @ResponseBody
    @RequestMapping(value = "/getDetailByUuid")
    public Object getDetailByUuid(String uuid) {
        ActMyProcessCopy actMyProcessCopy = actMyProcessCopyService.selectByPrimaryKey(uuid);
        return actMyProcessCopy;
    }

    @Log(title = "流程抄送-保存ActMyProcessCopy模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @ApiOperation(value = "保存ActMyProcessCopy模块数据")
    @ApiImplicitParam(name = "actMyProcessCopy", value = "保存ActMyProcessCopy模块数据", required = true, dataType = "ActMyProcessCopy")
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Object save(@ModelAttribute ActMyProcessCopy actMyProcessCopy) {
        setLogMessage(actMyProcessCopy.getTaskName(),"");
        int result = actMyProcessCopyService.insertSelective(getSaveData(actMyProcessCopy));
        return result > 0 ? success("抄送成功！") : failure("抄送失败！");
    }

    @Log(title = "流程抄送-更新ActMyProcessCopy模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.UPDATE, method = "getLogMessage")
    @ApiOperation(value = "更新ActMyProcessCopy模块数据")
    @ApiImplicitParam(name = "actMyProcessCopy", value = "更新ActMyProcessCopy模块数据", required = true, dataType = "ActMyProcessCopy")
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Object update(@ModelAttribute ActMyProcessCopy actMyProcessCopy) {
        setLogMessage(actMyProcessCopy.getTaskName(),"");
        int result = actMyProcessCopyService.updateByPrimaryKeySelective(getUpdateData(actMyProcessCopy));
        return result > 0 ? success("更新成功！") : failure("更新失败！");
    }

    @Log(title = "流程抄送-删除",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ApiOperation(value = "删除流程抄送数据")
    @ApiImplicitParam(name = "actMyProcessCopy", value = "删除ActMyProcessCopy模块数据", required = true, dataType = "ActMyProcessCopy")
    @ResponseBody
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.POST)
    public Object deleteBatch(@RequestParam String[] uuid) {
        if(uuid!=null&&uuid.length>0){
            StringBuilder logStr=new StringBuilder();
            for (String s : uuid) {
                ActMyProcessCopy actMyProcessCopy = actMyProcessCopyService.selectByPrimaryKey(s);
                if(actMyProcessCopy!=null)
                    logStr.append(actMyProcessCopy.getTaskName()+",");
            }
            setLogMessage(logStr.toString(),"");
        }
        int result = actMyProcessCopyService.executeDeleteBatch(uuid);
        return result > 0 ? success("删除成功！") : failure("删除失败！");
    }

    @ResponseBody
    @RequestMapping(value = "/clickDetail", method = RequestMethod.POST)
    public Object clickDetail(String taskId, String processInstanceId, String modelKey, String processDefinitionId, String nodeId) throws Exception {
        Object object = actMyProcessCopyService.clickDetail(taskId, processInstanceId, modelKey, processDefinitionId, nodeId);
        return object;
    }

    @ApiOperation(value = "获取我抄送给别人的数据")
    @ResponseBody
    @RequestMapping(value = "/getMyPageSet", method = RequestMethod.POST)
    public Object getMyPageSet(PageParam pageParam, String formName) {
        PageSet<ActMyProcessCopy> pageSet = actMyProcessCopyService.getMyPageSet(pageParam, formName, businessSystemDataService.getUserNameId());
        return pageSet;
    }


    @Log(title = "流程抄送-修改状态为已阅",module = LogModule.WORKFLOW,businessType = BusinessType.UPDATE, method = "getLogMessage")
    @ApiOperation(value = "修改状态为已阅")
    @ResponseBody
    @RequestMapping(value = "/updateReviewStatusByUuid", method = RequestMethod.POST)
    public Result updateReviewStatusByUuid(String uuid) {
        if (StrUtil.isBlank(uuid)) {
            return Result.ofFailMsg("必要参数不能为空！！！");
        }
        ActMyProcessCopy actMyProcessCopy = actMyProcessCopyService.selectByPrimaryKey(uuid);
        if(actMyProcessCopy!=null)
            setLogMessage(actMyProcessCopy.getTaskName(),"");
        String reviewStatus = "review_status.02";
        Integer integer = actMyProcessCopyService.updateReviewStatusByUuid(uuid, reviewStatus);
        return 1 == integer ? Result.ofSuccessMsg("修改成功！！！") : Result.ofFailMsg("修改失败！！！");
    }
}
