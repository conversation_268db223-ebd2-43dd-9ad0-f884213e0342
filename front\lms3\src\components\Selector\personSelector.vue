<template>
  <TrDialog
    v-model:open="isOpen"
    destroyOnClose
    :width="dialogWidth"
    :body-style="dialogBodyStyle"
    :title="translateText(`人员选择器`)"
    :show-footer="true"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <TrTable
      ref="personbrowser"
      :columns="columns"
      :loading="loading"
      :dataSource="personnel"
      :show-btns="false"
      :queryItems="queryItems"
      :show-add="false"
      :show-batch-delete="false"
      :show-export="false"
      :showOperation="false"
      :scroll="tableScroll"
      @query="handleSearch"
    />
  </TrDialog>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, computed, onMounted, onUnmounted } from "vue";
import { message } from "ant-design-vue";
import type { Personnel } from "@/types/system";
import { getPersonListPage } from "@/api/system.ts";
import { systemDataManager } from "@/utils/SystemDataManager.ts";
import {
  genderOptions,
  positionOptions,
  specialtyOptions
} from "@/data/system";
import DepartmetTree from "@/components/Selector/departmetTree.vue";
import TrTable from "@/components/TrTable/index.vue";
import { translateText } from "@/utils/translation";
import TrDialog from "@/components/TrDialog/index.vue";

export interface Props {
  visible: boolean;
  multiple: boolean;
  object?: object | null;
}
export interface Emits {
  (e: "close", visible: boolean): void;
  (e: "confirm", data: object): void;
}
const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const isOpen = ref(false);
const loading = ref(false);
const personnel = ref<Personnel[]>([]);
const multSelected = ref(false);

// 响应式视口尺寸
const viewportWidth = ref(window.innerWidth);
const viewportHeight = ref(window.innerHeight);

// 响应式对话框宽度计算
const dialogWidth = computed(() => {
  if (viewportWidth.value < 768) {
    return '95vw';
  } else if (viewportWidth.value < 1200) {
    return '90vw';
  } else {
    return Math.min(1200, viewportWidth.value * 0.8);
  }
});

// 响应式对话框高度和滚动配置
const dialogBodyStyle = computed(() => {
  let maxHeight;
  if (viewportHeight.value < 600) {
    maxHeight = viewportHeight.value * 0.6;
  } else if (viewportHeight.value < 900) {
    maxHeight = viewportHeight.value * 0.65;
  } else {
    maxHeight = viewportHeight.value * 0.7;
  }

  return {
    height: `${maxHeight}px`,
    overflow: 'hidden',
    padding: '0'
  };
});

// 响应式表格滚动配置
const tableScroll = computed(() => {
  let maxHeight;
  if (viewportHeight.value < 600) {
    maxHeight = viewportHeight.value * 0.6 - 140;
  } else if (viewportHeight.value < 900) {
    maxHeight = viewportHeight.value * 0.65 - 150;
  } else {
    maxHeight = viewportHeight.value * 0.7 - 160;
  }

  return {
    x: 'max-content', // 水平滚动
    y: Math.max(200, maxHeight - 100) // 垂直滚动，最小200px
  };
});

// 监听视口变化
const handleResize = () => {
  viewportWidth.value = window.innerWidth;
  viewportHeight.value = window.innerHeight;
};

// 表格列定义
const columns = [
  {
    title: "姓名",
    dataIndex: "name",
    key: "name",
    sorter: true,
    width: 150,
    fixed: 'left' as const
  },
  {
    title: "证件号",
    dataIndex: "cardNum",
    width: 120,
    sorter: true,
    align: "center"
  },
  {
    title: "性别",
    dataIndex: "sex",
    key: "sex",
    width: 80,
    align: "center",
    render: (current: string) => {
      return systemDataManager.translateDict(genderOptions, current);
    }
  },
  {
    title: "出生日期",
    dataIndex: "birthday",
    key: "birthday",
    width: 120,
    sorter: true,
    align: "center"
  },
  {
    title: "工作单位",
    dataIndex: "departmentname",
    key: "departmentname",
    width: 150
  },
  {
    title: "所在岗位",
    dataIndex: "duty",
    key: "duty",
    width: 120,
    sorter: true,
    render: (current: string) => {
      return systemDataManager.translateDict(positionOptions, current);
    }
  },
  {
    title: "专业",
    dataIndex: "specialityid",
    key: "specialityid",
    width: 100,
    align: "center",
    sorter: true,
    render: (current: string) => {
      return systemDataManager.translateDict(specialtyOptions, current);
    }
  }
];

// 表格查询条件
const queryItems = [
  {
    key: "PARAMETER_S_Like_name",
    label: translateText("人员名称"),
    component: "a-input",
    props: {
      placeholder: translateText("请输入人员名称"),
      allowClear: true
    }
  },
  {
    key: "PARAMETER_S_Like_cardNum",
    label: translateText("证件号"),
    component: "a-input",
    props: {
      placeholder: translateText("请输入证件号"),
      allowClear: true
    }
  },
  {
    key: "PARAMETER_S_EQ_departmentid",
    label: translateText("工作单位"),
    componentInstance: DepartmetTree,
    props: {
      placeholder: "请选择工作单位"
    }
  }
];

const personbrowser = ref<InstanceType<typeof TrTable>>();

// 获取人员列表
async function handleSearch(form) {
  try {
    await nextTick();
    let tableRef = personbrowser.value.getTableState();
    loading.value = true;
    let para = {
      pageIndex: tableRef.pagination.pageIndex,
      pageSize: tableRef.pagination.pageSize,
      orderName: tableRef.sort.field,
      sortType: tableRef.sort.order
    };
    para = Object.assign(para, form);
    getPersonListPage(para).then(response => {
      if (response.success) {
        personnel.value = response.result.records;
        personbrowser.value.setTotal(response.result.total);
        personbrowser.value.clearSelection();
      } else {
        message.error(response.message);
      }
    });
  } catch (error) {
    console.error("获取人员列表失败:", error);
    message.error(error);
  } finally {
    loading.value = false;
  }
}

const handleSubmit = () => {
  try {
    let selectedRows = personbrowser.value.getSelectedRows();
    if (selectedRows.length === 0) {
      message.info("请选择至少一个人员");
      return;
    }
    if (!multSelected.value && selectedRows.length > 1) {
      message.info("只能选择一个人员");
      return;
    }
    emit("confirm", selectedRows);
    handleCancel();
  } catch (error) {
    console.error("选择人员失败:", error);
  } finally {
    loading.value = false;
  }
};

const handleCancel = () => {
  emit("close", false);
};

watch(
  () => props.visible,
  visible => {
    isOpen.value = visible;
    multSelected.value = props.multiple;
  }
);

// 生命周期钩子
onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.personnel-selector {
  height: 100%;
}

/* 响应式样式 */
@media (max-width: 768px) {
  :deep(.ant-modal-body) {
    padding: 12px;
  }

  :deep(.tr-table-query-section) {
    padding: 12px 12px 0 12px;
  }

  :deep(.tr-table-table-section) {
    padding: 12px;
  }

  /* 确保底部按钮可见 */
  :deep(.ant-modal-footer) {
    position: sticky;
    bottom: 0;
    background: #fff;
    z-index: 10;
    border-top: 1px solid #f0f0f0;
    padding: 12px 24px;
  }
}

@media (max-width: 480px) {
  :deep(.ant-modal-body) {
    padding: 8px;
  }

  :deep(.tr-table-query-section) {
    padding: 8px 8px 0 8px;
  }

  :deep(.tr-table-table-section) {
    padding: 8px;
  }

  /* 小屏幕设备底部按钮样式 */
  :deep(.ant-modal-footer) {
    padding: 8px 16px;
  }
}

/* 通用底部按钮样式 */
:deep(.ant-modal-footer) {
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 10;
  border-top: 1px solid #f0f0f0;
  margin-top: 0;
}

/* 确保弹窗边框完整 */
:deep(.ant-modal-content) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}
</style>
