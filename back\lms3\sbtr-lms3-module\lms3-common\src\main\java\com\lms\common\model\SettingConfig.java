package com.lms.common.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Pattern;

public  class SettingConfig {
    //-------------------资质资料的状态-------//
    public static final String CONFIG_SYSTEMNAME = "4aeb61e4ba3011ec83453ac739c533ad";  //系统名称设置
    public static final String CONFIG_SYSTEMLANGUAGE = "90f77810bad911ec85425c4f7a74d8de";  //系统语言设置
    public static final String CONFIG_PASSWORDCHANGE = "90f77810bad911ec87565c4f7a77a95f";  //密码更换周期
    public static final String CONFIG_PASSWORDLOCKTIME = "4aeb61e4ba3011eq77603ac739c594we";  //密码锁定次数
    public static final String CONFIG_PASSWORDLENGTH = "4aeb61e4b88a79eq77603ac739c594bm";  //密码长度
    public static final String CONFIG_PASSWORDRULE = "4aeb61e4b88a79eq77603w2598c577ae";  //密码规则
    public static final String CONFIG_DEFAULT_PASSWORD = "4aeb61e4b88a79eq77603w2598c99755";  //默认密码
}
