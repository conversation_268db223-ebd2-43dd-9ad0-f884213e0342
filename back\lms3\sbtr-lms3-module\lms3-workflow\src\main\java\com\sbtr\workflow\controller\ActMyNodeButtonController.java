package com.sbtr.workflow.controller;

import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.model.ActFormConfigure;
import com.sbtr.workflow.model.ActMyNodeButton;
import com.sbtr.workflow.service.ActMyNodeButtonService;
import com.sbtr.workflow.utils.StringUtil.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 流程每个节点所对应按钮
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-23 07:12:25
 */
@Api(tags = {"流程节点按钮" })
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/flowNodeButton")
public class ActMyNodeButtonController extends WorkflowBaseController {

    @Autowired
    private ActMyNodeButtonService flowNodeButtonService;

    @ApiOperation(value = "获得ActMyForm分页集数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String"),
    })
    @ResponseBody
    @RequestMapping(value = "/getPageSet", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam) {
        PageSet<ActMyNodeButton> pageSet = flowNodeButtonService.getPageSet(pageParam);
        return pageSet;
    }

    @ApiOperation(value = "获得FlowNodeButton模块详细数据")
    @ApiImplicitParam(name = "uuid", value = "获得FlowNodeButton模块详细数据", required = true, dataType = "String")
    @ResponseBody
    @RequestMapping(value = "/getDetailByUuid")
    public Object getDetailByUuid(String uuid) {
        ActMyNodeButton flowNodeButton =flowNodeButtonService.selectByPrimaryKey(uuid);
        return flowNodeButton;
    }


    @Log(title = "流程节点按钮-保存FlowNodeButton模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @ApiOperation(value = "保存FlowNodeButton模块数据")
    @ApiImplicitParam(name = "flowNodeButton", value = "保存FlowNodeButton模块数据", required = true, dataType = "FlowNodeButton")
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Object save(@ModelAttribute ActMyNodeButton flowNodeButton) {
        setLogMessage(flowNodeButton.getNodeName()+":"+flowNodeButton.getNodeButtonName(),"");
        int result = flowNodeButtonService.insertSelective(getSaveData(flowNodeButton));
        return result > 0 ? success("保存成功！") : failure("保存失败！");
    }


    @Log(title = "流程节点按钮-更新FlowNodeButton模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.UPDATE, method = "getLogMessage")
    @ApiOperation(value = "更新FlowNodeButton模块数据")
    @ApiImplicitParam(name = "flowNodeButton", value = "更新FlowNodeButton模块数据", required = true, dataType = "FlowNodeButton")
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Object update(@ModelAttribute ActMyNodeButton flowNodeButton) {
        setLogMessage(flowNodeButton.getNodeName()+":"+flowNodeButton.getNodeButtonName(),"");
        int result = flowNodeButtonService.updateByPrimaryKeySelective(getUpdateData(flowNodeButton));
        return result > 0 ? success("更新成功！") : failure("更新失败！");
    }

    @Log(title = "流程节点按钮-删除FlowNodeButton模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ApiOperation(value = "删除FlowNodeButton模块数据")
    @ApiImplicitParam(name = "flowNodeButton", value = "删除FlowNodeButton模块数据", required = true, dataType = "FlowNodeButton")
    @ResponseBody
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.POST)
    public Object deleteBatch(@RequestParam String[] uuid) {
        if(uuid!=null&&uuid.length>0){
            StringBuilder logStr=new StringBuilder();
            for (String s : uuid) {
                ActMyNodeButton actMyNodeButton = flowNodeButtonService.selectByPrimaryKey(s);
                if(actMyNodeButton!=null)
                    logStr.append(actMyNodeButton.getNodeName()+":"+actMyNodeButton.getNodeButtonName()+",");
            }
            setLogMessage(logStr.toString(),"");
        }
        int result = flowNodeButtonService.executeDeleteBatch(uuid);
        return result > 0 ? success("删除成功！") : failure("删除失败！");
    }



    @ApiOperation(value = "根据modelKey获得路径参数")
    @ApiImplicitParam(name = "modelKey", value = "根据modelKey获得路径参数", required = true, dataType = "String")
    @ResponseBody
    @RequestMapping(value = "/getDetailByModelKey", method = RequestMethod.GET)
    public Map<String, Object> getDetailByModelKey(@RequestParam String modelKey) {
        return flowNodeButtonService.getDetailByModelKey(StringUtils.emptyToNull(modelKey));
    }



}
