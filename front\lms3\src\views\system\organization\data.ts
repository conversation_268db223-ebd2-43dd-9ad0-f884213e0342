export interface Organization {
  id?: string;
  name: string;
  shortname: string;
  code: string;
  seqno: number;
  pid?: string;
  status?: number;
  parentname?: string;
  fullpath?: string;
  fullpathname?: string;
  orgFlag?: number;
  children?: Organization[];
}

export interface OrganizationForm {
  name: string;
  shortname: string;
  code: string;
  seqno: number;
  pid?: string;
}

export const mockOrganizations: Organization[] = [
  {
    id: "1",
    name: "A级",
    shortname: "A",
    code: "001",
    seqno: 1,
    children: [
      {
        id: "2",
        name: "B级",
        shortname: "B",
        code: "002",
        seqno: 2,
        pid: "1"
      }
    ]
  },
  {
    id: "3",
    name: "C组",
    shortname: "C",
    code: "003",
    seqno: 3
  }
];

export const findOrganizationById = (
  organizations: Organization[],
  id: string
): null | Organization => {
  for (const org of organizations) {
    if (org.id === id) {
      return org;
    }
    if (org.children) {
      const found = findOrganizationById(org.children, id);
      if (found) return found;
    }
  }
  return null;
};
