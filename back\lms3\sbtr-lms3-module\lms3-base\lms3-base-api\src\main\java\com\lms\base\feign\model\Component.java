package com.lms.base.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import com.lms.common.model.LogObjname;
import com.lms.common.model.SearchContent;
import com.lms.common.model.SearchName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * BComponent entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)
@TableName("b_component")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "型号实体类")
public class Component extends BaseModel {

	@TableId(type= IdType.ASSIGN_ID)
	@ApiModelProperty("主键id")
	private String id;

	@TableField("PARENTID")
	@ApiModelProperty("父型号id")
	private String parentid;

	@TableField(exist = false)
	@ApiModelProperty("父型号名称")
	@SearchContent
	private String parentname;

	@TableField("SEQNO")
	@ApiModelProperty("序号")
	private Integer seqno;

	@TableField("NAME")
	@LogObjname
	@ApiModelProperty("名称")
	@SearchContent
	private String name;

	@TableField("CODE")
	@ApiModelProperty("编号")
	@SearchName
	private String code;

	@TableField("FULLCODE")
	@ApiModelProperty("编号全路径")
	private String fullcode;

	@ApiModelProperty("id全路径")
	private String fullpath;

	@ApiModelProperty("名称全路径")
	@SearchContent
	private String fullpathname;

	@TableField("COURSEFLAG")
	@ApiModelProperty("是否是课程节点")
	private String courseFlag;

	@TableField("STATUS")
	@ApiModelProperty("状态")
	private Integer status;

	public void setFullcode(String parentFullCode, String code) {
		this.fullcode = parentFullCode + "-" + code;
	}
}
