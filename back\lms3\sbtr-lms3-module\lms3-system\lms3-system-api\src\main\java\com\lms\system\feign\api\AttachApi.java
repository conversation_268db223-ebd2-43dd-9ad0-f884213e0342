package com.lms.system.feign.api;

import com.lms.common.config.FeignConfig;
import com.lms.common.model.Result;
import com.lms.system.feign.fallback.AttachApiFallbackFactory;
import com.lms.system.feign.model.Attach;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.util.List;

/**
 * 其他服务由调用attachService,在attachController先增加暴露方法
 */
@FeignClient(contextId = "attachApi",value = "lms-system", path = "/attach",configuration = FeignConfig.class,fallbackFactory = AttachApiFallbackFactory.class)
public interface AttachApi {

    @GetMapping("/get")
    Result<Attach> get(@RequestParam("id") String id);

    @PostMapping("/saveOrUpdate")
    Result saveOrUpdate(@RequestBody Attach attach);

    @PostMapping("/uploadFile")
    Result<String> uploadFile(@RequestParam("file") MultipartFile file);

    @RequestMapping("/saveAttachByFilePath")
    Result<String> saveAttachByFilePath(File file);

    @GetMapping("/checkFileIsExist")
    Result<Boolean> checkFileIsExist(@RequestParam("objName") String objName);

    @GetMapping(value = {"/downloadFile"})
    void downLoadFile(HttpServletResponse response, @RequestParam("attachid") String attachid);

    @GetMapping(value = {"/downloadStream"})
    InputStream downLoadStream(@RequestParam("attachid") String attachid);

    @PostMapping(value = {"/getByIdList"})
    Result<List<Attach>> getByIdList(@RequestBody List<String> attachIdList);

    @PostMapping(value = {"/deleteFile"})
    Result deleteFile(@RequestBody Attach attach);

    @GetMapping(value = {"/delete"})
    Result delete(@RequestParam String id);
}
