package com.lms.system.minio.service;

import com.lms.common.util.StringHelper;
import io.minio.BucketExistsArgs;
import io.minio.GetObjectArgs;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.ListObjectsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveBucketArgs;
import io.minio.RemoveObjectArgs;
import io.minio.Result;
import io.minio.StatObjectArgs;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import io.minio.messages.Item;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class MinioService {

    @Resource
    private MinioClient minioClient;

    @Value(value = "${minio.bucket}")
    private String defaultBucket; // 默认的桶

    @Value("${minio.previewUrlExpiry}")
    private String previewUrlExpiry;

    /**
     * 创建存储桶
     *
     * @param bucketName
     * @return
     * @throws Exception
     */
    public String makeBucket(String bucketName)
            throws Exception {
        boolean isExist = bucketExists(bucketName);
        if(!isExist){
            minioClient.makeBucket(MakeBucketArgs
                    .builder()
                    //设置桶的名字
                    .bucket(bucketName)
                    .build());
        }
        return bucketName;
    }
    public String makeBucket() throws Exception {return makeBucket(defaultBucket);}
    /**
     * 查看所有桶
     *
     * @return
     * @throws Exception
     */
    public List<Bucket> listBuckets()
            throws Exception {
        return minioClient.listBuckets();
    }


    /**
     * 列出所有存储桶名称
     *
     * @return
     * @throws Exception
     */
    public List<String> listBucketNames()
            throws Exception {
        List<Bucket> bucketList = listBuckets();
        List<String> bucketListName = new ArrayList<>();
        for (Bucket bucket : bucketList) {
            bucketListName.add(bucket.name());
        }
        return bucketListName;
    }

    /**
     * 检查存储桶是否存在
     *
     * @param bucketName
     * @return
     * @throws Exception
     */
    public boolean bucketExists(String bucketName) throws Exception {
        return minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
    }
    public boolean bucketExists()throws Exception {return bucketExists(defaultBucket);}
    /**
     * 列出存储桶中的所有对象
     *
     * @param bucketName 存储桶名称
     * @return
     * @throws Exception
     */
    public Iterable<Result<Item>> listObjects(String bucketName) throws Exception {
        boolean flag = bucketExists(bucketName);
        if (flag) {
            String prefixes = ""; // 可以添加前缀来过滤对象，这里为空表示列出所有对象
            boolean recursive = false; // 是否递归列出子目录中的对象，这里设置为不递归
            ListObjectsArgs args = ListObjectsArgs.builder()
                    .bucket(bucketName)// 指定存储桶名称
                    .prefix(prefixes) // 可以设置前缀来过滤对象，这里为空表示列出所有对象
                    .recursive(recursive) // 设置是否递归列出子目录中的对象，这里设置为不递归
                    .build();
            return minioClient.listObjects(args);
        }
        return null;
    }
    public Iterable<Result<Item>> listObjects() throws Exception {return listObjects(defaultBucket);}

    /**
     * 列出存储桶中的所有对象名称
     *
     * @param bucketName 存储桶名称
     * @return
     * @throws Exception
     */
    public List<String> listObjectNames(String bucketName) throws Exception {
        List<String> listObjectNames = new ArrayList<>();
        boolean flag = bucketExists(bucketName);
        if (flag) {
            Iterable<Result<Item>> myObjects = listObjects(bucketName);
            for (Result<Item> result : myObjects) {
                Item item = result.get();
                listObjectNames.add(item.objectName());
            }
        }
        return listObjectNames;
    }
    public List<String> listObjectNames() throws Exception {return listObjectNames(defaultBucket);}
    /**
     * 上传文件
     *
     * @param file 文件
     * @return 文件名
     */
    public String upload(String bucketName,MultipartFile file) throws Exception {
        if (StringUtils.isEmpty(bucketName)){
            bucketName = "lms-bucket"; //默认桶名称
        }
        if (!bucketExists(bucketName)){
            makeBucket(bucketName);
        }
        log.info("开始向桶 {} 上传文件", bucketName);
        //给文件生成一个唯一名称  当日日期-uuid.后缀名
        String folderName = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss"));
        String fileName = String.valueOf(UUID.randomUUID());
        String extName = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));//文件后缀名
        String objectName = folderName + "-" + fileName + extName;
        try (InputStream inputStream = file.getInputStream()){
            // 配置参数
            PutObjectArgs objectArgs = PutObjectArgs.builder().bucket(bucketName).object(objectName)
                    .stream(inputStream, file.getSize(), -1).contentType(file.getContentType()).build();
            //文件名称相同会覆盖
            minioClient.putObject(objectArgs);
        } catch (Exception e) {
            log.error("文件上传失败: " + e);
            throw new RuntimeException(e);
        }
        log.info("文件上传成功，文件名为：{}", objectName);
        return objectName;
    }
    public String upload(MultipartFile file) throws Exception {return upload(defaultBucket,file);}
    /**
     * 下载文件
     *
     * @param fileName 文件名
     * @param response HttpServletResponse
     */
    public void download(String bucketName, String fileName, HttpServletResponse response) {
        if (StringHelper.isEmpty(fileName)) {
            log.error("文件名为空！");
            return;
        }
        try {
            // 获取文件流
            InputStream file = minioClient.getObject(GetObjectArgs.builder().bucket(bucketName).object(fileName).build());
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" +
                    URLEncoder.encode(fileName.substring(fileName.lastIndexOf("/") + 1), String.valueOf(StandardCharsets.UTF_8)));
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            // 获取输出流
            ServletOutputStream servletOutputStream = response.getOutputStream();
            int len;
            byte[] buffer = new byte[1024];
            while ((len = file.read(buffer)) > 0) {
                servletOutputStream.write(buffer, 0, len);
            }
            servletOutputStream.flush();
            file.close();
            servletOutputStream.close();
            log.info("文件{}下载成功", fileName);
        } catch (Exception e) {
            log.error("文件名: " + fileName + "下载文件时出现异常: " + e);
        }
    }

    public void download(String fileName, HttpServletResponse response) { download(defaultBucket,fileName,response);}

    /**
     * 以流的形式下载一个文件
     */
    public InputStream download(String bucketName, String objectName) throws Exception {
        InputStream stream = minioClient.getObject(
                GetObjectArgs.builder().bucket(bucketName).object(objectName).build());
        return stream;
    }

    public InputStream download(String objectName) throws Exception {
        return download(defaultBucket,objectName);
    }

    /**
     * 删除文件
     *
     * @param fileName 文件名
     */
    public boolean removeObject(String bucketName,String fileName) {
        try {
            if (StringHelper.isEmpty(fileName)) {
                log.error("删除文件失败，文件名为空！");
                return false;
            }
            // 判断桶是否存在
            boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            // 桶存在
            if (isExist) {
                minioClient.removeObject(RemoveObjectArgs.builder().bucket(bucketName).object(fileName).build());
            } else { // 桶不存在
                log.error("删除文件失败，桶{}不存在", bucketName);
            }
        } catch (Exception e) {
            log.error("删除文件时出现异常: " + e.getMessage());
        }
        return true;
    }

    public boolean removeObject(String fileName) {
        return removeObject(defaultBucket,fileName);
    }

    /**
     * 删除桶
     *
     * @param bucketName
     * @return
     * @throws Exception
     */
    public boolean removeBucket(String bucketName)
            throws Exception {
        boolean flag = bucketExists(bucketName);
        if (flag) {
            Iterable<Result<Item>> myObjects = listObjects(bucketName);
            for (Result<Item> result : myObjects) {
                Item item = result.get();
                // 有对象文件，则删除失败
                if (item.size() > 0) {
                    return false;
                }
            }
            // 删除存储桶，注意，只有存储桶为空时才能删除成功。
            minioClient.removeBucket(RemoveBucketArgs.builder().bucket(bucketName).build());
            flag = bucketExists(bucketName);
            if (!flag) {
                return true;
            }

        }
        return false;
    }

    public boolean removeBucket() throws Exception {
        return removeBucket(defaultBucket);
    }

    /**
     * 获取文件预览url
     *
     * @param fileName 文件名
     * @return 文件预览url
     */
    public String getPreviewUrl(String bucketName,String fileName) {
        // 获取桶名
        bucketName = StringHelper.isEmpty(bucketName)?defaultBucket:bucketName;
        String presignedUrl = null;
        try {
            if (StringHelper.isEmpty(fileName)) {
                log.error("获取文件预览url失败，文件名为空！");
                return presignedUrl;
            }
            // 判断桶是否存在
            boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            // 桶存在
            if (isExist) {
                presignedUrl = minioClient.getPresignedObjectUrl(
                        GetPresignedObjectUrlArgs.builder()
                                .method(Method.GET)
                                .bucket(bucketName)
                                .object(fileName)
                                .expiry(Integer.parseInt(previewUrlExpiry), TimeUnit.HOURS) // 一小时过期时间
                                .build());
                return presignedUrl;
            } else {  // 桶不存在
                log.error("获取文件预览url失败，桶{}不存在", bucketName);
            }
        } catch (Exception e) {
            log.error("获取文件预览url时出现异常: " + e.getMessage());
        }
        return presignedUrl;
    }

    public String getPreviewUrl(String fileName) {
        return getPreviewUrl(defaultBucket,fileName);
    }


    /**
     * 判断文件是否存在
     */
    public Boolean checkFileIsExist(String bucketName, String objectName) {
        try {
            minioClient.statObject(
                    StatObjectArgs.builder().bucket(bucketName).object(objectName).build()
            );
        } catch (Exception e) {
            return false;
        }
        return true;
    }


    /**
     * 判断文件是否存在
     */
    public Boolean checkFileIsExist(String objectName) {
        try {
            minioClient.statObject(
                    StatObjectArgs.builder().bucket(defaultBucket).object(objectName).build()
            );
        } catch (Exception e) {
            return false;
        }
        return true;
    }
}
