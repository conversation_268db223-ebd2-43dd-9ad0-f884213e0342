package com.lms.base.model.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lms.base.feign.model.Component;
import com.lms.base.feign.model.Courseware;
import com.lms.base.feign.model.Selectitem;
import com.lms.base.model.service.ModelService;
import com.lms.base.model.vo.EditorVo;
import com.lms.base.model.vo.EditorVoConverter;
import com.lms.base.model.vo.FormDataParser;
import com.lms.base.model.vo.ModelVo;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.DateHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/model")
@Api(value = "模型维护", tags = "模型维护")
public class ModelController {

    @Resource
    private ModelService modelService;

    @ApiOperation(value = "保存模型", httpMethod = "POST")
    @PostMapping("/add")
    public Result<ModelVo> saveModel(@RequestBody ModelVo modelVo) throws Exception {
        modelService.saveOrUpdateModel(modelVo, false);
        return Result.OK(modelVo);
    }

    @PostMapping("/batchAdd")
    @ApiOperation(value = "批量保存模型", httpMethod = "POST")
    public Result batchAddCourseware(@RequestBody List<ModelVo> modelVos) throws Exception {
        for (ModelVo modelVo : modelVos) {
            saveModel(modelVo);
        }
        return Result.OK(modelVos);
    }

    @ApiOperation(value = "查询所有模型", httpMethod = "POST")
    @PostMapping("/list")
    public Result<List<ModelVo>> modelList() {
        return Result.OK(modelService.listAll());
    }

    @ApiOperation(value = "分页查询所有模型", httpMethod = "POST")
    @PostMapping("/listPage")
    public Result<Page<ModelVo>> listPage(@RequestBody JSONObject jsonObject) throws Exception {
        Page<ModelVo> page = modelService.listPage(jsonObject);
        return Result.OK(page);
    }

    @ApiOperation(value = "根据id查询模型", httpMethod = "GET")
    @GetMapping("/getById/{id}")
    public Result<ModelVo> getById(@PathVariable String id) throws IOException {
        ModelVo modelVo = modelService.getById(id);
        return Result.OK(modelVo);
    }

    @ApiOperation(value = "修改模型", httpMethod = "POST")
    @PostMapping("/update")
    public Result<ModelVo> updateModel(@RequestBody ModelVo modelVo) throws Exception {
        modelService.saveOrUpdateModel(modelVo, false);
        return Result.OK(modelVo);
    }

    /**
     * 修改或保存模型  编辑器调用
     * @param params 模型数据
     * @return  Result
     */
    @ApiOperation(value = "修改模型,编辑器调用", httpMethod = "POST")
    @PostMapping("/updateModelByEditor")
    public Result<ModelVo> updateModelByEditor(@RequestParam Map<String, String> params) throws Exception {
        log.info("=== 编辑器调用开始 ===");
        log.info("原始参数: {}", params);
        // 解析FormData
        EditorVo editorVo = FormDataParser.parseFormData(params);
        log.info("解析结果: {}", editorVo);

        if (editorVo.get_children() != null) {
            log.info("子模型详情:");
            for (int i = 0; i < editorVo.get_children().size(); i++) {
                EditorVo child = editorVo.get_children().get(i);
                log.info("  [{}] id={}, name={}", i, child.getId(), child.getName());
            }
        }
        // 转换为ModelVo
        ModelVo modelVo = EditorVoConverter.convertToModelVo(editorVo);
        // 调用服务保存
        modelService.updateModelByEditor(modelVo);
        log.info("=== 编辑器调用结束 ===");
        return Result.OK(modelVo);
    }

    @ApiOperation(value = "删除模型", httpMethod = "DELETE")
    @DeleteMapping("/delete")
    public Result<String> deleteModel(
            @ApiParam(value = "模型id以,分隔") @RequestParam("ids") String ids) throws Exception {
        modelService.deleteModel(ids);
        return Result.OK(ids);
    }

    @ApiOperation(value = "查询此模型关联关系图谱", httpMethod = "GET")
    @GetMapping("/queryTuGraph")
    public Result<Map<String, Object>> queryTuGraph(String modelId) throws Exception {
        return modelService.queryTuGraph(modelId);
    }

    @ApiOperation(value = "构建模型之间的关系", httpMethod = "POST")
    @PostMapping("/createModelRelationship")
    public Result<Void> createModelRelationship(String startModelId, String endModelId, String relationship) throws Exception {
        modelService.createModelRelationship(startModelId, endModelId, relationship);
        return Result.OK(null);
    }


}
