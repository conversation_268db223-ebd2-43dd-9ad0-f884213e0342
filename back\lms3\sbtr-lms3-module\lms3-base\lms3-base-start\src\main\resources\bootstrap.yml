spring:
  application:
    name: lms-base
  profiles:
    # 当前激活环境
    active: @profile.name@
  main:
    allow-bean-definition-overriding: true #当遇到同样名字的时候，是否允许覆盖注册
  cloud:
    #配置Bus id(远程推送事件)
    #    bus:
    #      id: ${spring.application.name}:${server.port}
    nacos:
      config:
        # Nacos 认证用户
        username: @config.username@
        # Nacos 认证密码
        password: @config.password@
        # 命名空间 常用场景之一是不同环境的配置的区分隔离，例如开发测试环境和生产环境的资源（如配置、服务）隔离等
        namespace: @config.namespace@
        # 配置中心地址
        server-addr: @config.server-addr@
        # 配置对应的分组
        group: @config.group@
        # 配置文件后缀
        file-extension: yaml
        shared-configs[0]:
          data-id: lms-common.yaml # 配置文件名-Data Id
          #group: @config.group@   # 默认为DEFAULT_GROUP
          #refresh: false   # 是否动态刷新，默认为false
      discovery:
        namespace: @config.namespace@
        server-addr: @config.server-addr@
        # Nacos 认证用户
        username: @config.username@
        # Nacos 认证密码
        password: @config.password@
        group: @config.group@
        watch: #nacos类似长连接推送服务变化的功能
          enabled: true
        watch-delay: 15000
#ribbon的超时时间
#ribbon:
#  ReadTimeout: 30000
#  ConnectTimeout: 30000
#  eureka:
#    enabled: true # 开启eureka负载均衡策略

---
spring:
  config:
    activate:
      on-profile: prod
#linux
logging:
  file:
    name: base
    path:  /usr/local/lms3/logs/base

---
spring:
  config:
    activate:
      on-profile:
        - test
        - dev
        - qjh
#windows
logging:
  file:
    name: base
    path: C:\lms3\logs\base

