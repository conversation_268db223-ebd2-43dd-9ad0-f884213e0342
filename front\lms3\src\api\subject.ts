import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";
// 试题列表查询（分页）
export const getSubjectListPage = (data?: any) => {
  return http.request<any>("post", baseUrlApi("subject/listpage"), {
    data
  });
};
// 新增试题
export const saveSubject = (data?: any) => {
  return http.request<any>("post", baseUrlApi("subject/save"), {
    data
  });
};
// 删除试题
export const deleteSubject = (data?: any) => {
  return http.request<any>("delete", baseUrlApi("subject/delete"), {
    params: data
  });
};
