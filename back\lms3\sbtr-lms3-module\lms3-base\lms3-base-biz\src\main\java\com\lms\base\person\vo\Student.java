package com.lms.base.person.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 学员 导出
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Student implements Serializable {

    @Excel(name = "姓名", width = 15)
    private String name;

    @Excel(name = "性别", width = 15)
    private String sex;

    @Excel(name = "出生年月", width = 15)
    private String birthday;

    @Excel(name = "单位", width = 15)
    private String departmentname;

    @Excel(name = "工作年份", width = 15)
    private String seniority;

    @Excel(name = "从事专业", width = 15)
    private String speciality;

    @Excel(name = "学历", width = 15)
    private String education;

    @Excel(name = "专业技术职务", width = 15)
    private String job;

    @Excel(name = "用户名称", width = 15)
    private String studentName;

    @Excel(name = "联系电话", width = 15)
    private String phone;

    @Excel(name = "从事型号", width = 15)
    private String equipment;

    @Excel(name = "备注", width = 15)
    private String remark;

    @Excel(name = "密级", width = 15)
    private String mlevel;
}
