package com.sbtr.workflow.enums;

import com.sbtr.workflow.utils.StringUtil.StringUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum ApiTableMappingEnum {

    //PLAN("u_textbookplan", TextBookPlanApi.class, ApiTableMappingEnum.METHODNAME), // 培训教材计划
    //CHANGE("u_textbookchange", TextBookChangeApi.class, ApiTableMappingEnum.METHODNAME),  // 培训教材更改
    //NEED("u_trainingneed", TrainingNeedApi.class, ApiTableMappingEnum.METHODNAME), // 培训需求
    //REVIEW("u_textbookreview",TextBookReviewApi.class, ApiTableMappingEnum.METHODNAME), // 培训教材审核
    //TRAININGPLAN("u_dutytrainingplan", DutyTrainingPlanApi.class, "getAllTrainFlowProcessById"), // 培训计划申请
    //TEACHERABILITYTRAININGPLAN("u_teacherabilitytrainingplan", TeacherAbilityTrainingPlanApi.class, ApiTableMappingEnum.METHODNAME), // 教员能力培养计划
    //TEACHERPROMOTIONASSESSMENT("u_teacherpromotionassessment", TeacherPromotionAssessmentApi.class, ApiTableMappingEnum.METHODNAME), // 教员晋升考核
    //FACILITYPLAN("p_facility_plan",FacilityPlanApi.class, ApiTableMappingEnum.METHODNAME); // 设备设施更新计划
;
    private String TableName;

    private Class apiClazz;

    private String methodName;

    private static final String METHODNAME = "getById";


    ApiTableMappingEnum(String tableName, Class apiClazz, String methodName) {
        TableName = tableName;
        this.apiClazz = apiClazz;
        this.methodName = methodName;
    }

    public static Class getApiByTableName(String tableName) {
        List<ApiTableMappingEnum> list = Arrays.stream(ApiTableMappingEnum.values()).filter(t -> StringUtils.equals(t.getTableName(), tableName)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0).getApiClazz();
        }
        return null;
    }

    public static String getMethodNameByTableName(String tableName) {
        List<ApiTableMappingEnum> list = Arrays.stream(ApiTableMappingEnum.values()).filter(t -> StringUtils.equals(t.getTableName(), tableName)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0).getMethodName();
        }
        return null;
    }

    public static ApiTableMappingEnum getByTableName(String tableName){
        List<ApiTableMappingEnum> list = Arrays.stream(ApiTableMappingEnum.values()).filter(t -> StringUtils.equals(t.getTableName(), tableName)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }


    public String getTableName() {
        return TableName;
    }

    public void setTableName(String tableName) {
        TableName = tableName;
    }

    public Class getApiClazz() {
        return apiClazz;
    }

    public void setApiClazz(Class apiClazz) {
        this.apiClazz = apiClazz;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }
}

