package com.lms.common.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.lms.common.feign.dto.TreeNode;
import com.lms.common.model.LogObjname;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.*;

/**
 * 访问在当前类声明的private/protected成员变量及private/protected函数的BeanUtils.
 * 注意,因为必须为当前类声明的变量,通过继承获得的protected变量将不能访问,
 * 需要转型到声明该变量的类才能访问.
 * 反射的其他功能请使用Apache Jarkarta Commons BeanUtils
 */
public class BeanUtils {
    /**
     * 获取当前类声明的private/protected变量
     */
    static public Object getPrivateProperty(Object object, String propertyName) throws IllegalAccessException, NoSuchFieldException {
        Assert.notNull(object);
        Assert.hasText(propertyName);
        Field field = object.getClass().getDeclaredField(propertyName);
        field.setAccessible(true);
        return field.get(object);
    }

    /**
     * 设置当前类声明的private/protected变量
     */
    static public void setPrivateProperty(Object object, String propertyName, Object newValue) throws IllegalAccessException, NoSuchFieldException {
        Assert.notNull(object);
        Assert.hasText(propertyName);

        Field field = object.getClass().getDeclaredField(propertyName);
        field.setAccessible(true);
        field.set(object, newValue);
    }

    /**
     * 调用当前类声明的private/protected函数
     */
    static public Object invokePrivateMethod(Object object, String methodName, Object[] params) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        Assert.notNull(object);
        Assert.hasText(methodName);
        Class[] types = new Class[params.length];
        for (int i = 0; i < params.length; i++) {
            types[i] = params[i].getClass();
        }
        Method method = object.getClass().getDeclaredMethod(methodName, types);
        method.setAccessible(true);
        return method.invoke(object, params);
    }

    /**
     * 调用当前类声明的private/protected函数
     */
    static public Object invokePrivateMethod(Object object, String methodName, Object param) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        return invokePrivateMethod(object, methodName, new Object[]{param});
    }

    // 检测对象是否包含某个属性名称
    public static boolean existedField(Object obj, String fieldText) {
        boolean isExisted = false;
        if (obj != null) {
            Class<?> clazz = obj.getClass();
            while (clazz != null) {
                for (Field field : clazz.getDeclaredFields()) {
                    field.setAccessible(true);
                    // 设置为可访问 // 排除静态和合成字段
                    field.getName().equals(fieldText);
                    isExisted = true;
                    break;
                }
                clazz = clazz.getSuperclass();
                // 继续获取父类的属性
            }
        }
        return isExisted;
    }

    public static List getObjectFieldNames(Object obj) {
        if (obj != null) {
            Class<?> clazz = obj.getClass();
            return getClassFieldNames(clazz);
        }
        return new ArrayList();
    }

    // 检测对象是否包含某个属性名称
    public static List getClassFieldNames(Class<?> clazz) {
        List fieldNames = new ArrayList();
        while (clazz != null) {
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                if (!java.lang.reflect.Modifier.isStatic(field.getModifiers()) && !field.isSynthetic()) {
                    fieldNames.add(field.getName());
                }
            }
            clazz = clazz.getSuperclass();
            // 继续获取父类的属性
        }
        return fieldNames;
    }

    public static void copyProperties(Object source, Object target) {
        org.springframework.beans.BeanUtils.copyProperties(source,target);
    }

    public Map<String, Object> fields2CamelCase(Map<String, Object> object) {
        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<String, Object> field : object.entrySet()) {
            Object fieldValue = field.getValue();
            result.put(StrUtil.toCamelCase(field.getKey()), fieldValue);
        }
        return result;
    }

    /**
     * 根据对象和属性名返回属性值
     *
     * @param obj,fieldName
     * @return
     */
    public static Object getValueByName(Object obj, String fieldName) {
        Object fieldValue = null;
        if (obj != null) {
            Class<?> clazz = obj.getClass();
            try {
                Field[] fields = clazz.getFields();
                Optional<Field> field = Arrays.stream(fields).filter(f -> f.getName().equals(fieldName)).findFirst();
                if(!field.isPresent()){
                    fields = clazz.getDeclaredFields();
                    field = Arrays.stream(fields).filter(f -> f.getName().equals(fieldName)).findFirst();
                }
                if(field.isPresent()){
                    Field fieldObject = field.get();
                    fieldObject.setAccessible(true);
                    fieldValue = fieldObject.get(obj);
                }
            } catch (Exception e) {
            }
        }
        return fieldValue;
    }

    public static String getPropertyContentByName(Object obj, String fieldName) {
        Object fieldValue = null;
        String objectName = fieldName;
        if (obj != null) {
            Class<?> clazz = obj.getClass();
            try {
                Field[] fields = clazz.getFields();
                Optional<Field> field = Arrays.stream(fields).filter(f -> f.getName().equals(fieldName)).findFirst();
                if(!field.isPresent()){
                    fields = clazz.getDeclaredFields();
                    field = Arrays.stream(fields).filter(f -> f.getName().equals(fieldName)).findFirst();
                }
                if(field.isPresent()){
                    Field fieldObject = field.get();
                    fieldObject.setAccessible(true);
                    fieldValue = fieldObject.get(obj);
                    if(fieldValue == null) return null;
                    ApiModelProperty apiModelProperty = fieldObject.getAnnotation(ApiModelProperty.class);
                    objectName = ObjectUtil.isEmpty(apiModelProperty) ? objectName : apiModelProperty.value();
                }
            } catch (Exception e) {
            }
        }
        return "【"+objectName+":"+fieldValue+"】";
    }

    public static String getPropertyValueByName(Object obj, String fieldName) {
        Object fieldValue = null;
        String objectName = fieldName;
        if (obj != null) {
            Class<?> clazz = obj.getClass();
            try {
                Field[] fields = clazz.getFields();
                Optional<Field> field = Arrays.stream(fields).filter(f -> f.getName().equals(fieldName)).findFirst();
                if(!field.isPresent()){
                    fields = clazz.getDeclaredFields();
                    field = Arrays.stream(fields).filter(f -> f.getName().equals(fieldName)).findFirst();
                }
                if(field.isPresent()){
                    Field fieldObject = field.get();
                    fieldObject.setAccessible(true);
                    fieldValue = fieldObject.get(obj);
                    if(fieldValue == null) return null;
                    ApiModelProperty apiModelProperty = fieldObject.getAnnotation(ApiModelProperty.class);
                    objectName = ObjectUtil.isEmpty(apiModelProperty) ? objectName : apiModelProperty.value();
                }
            } catch (Exception e) {
            }
        }
        return "" + fieldValue;
    }

    /**
     * 根据属性值和属性名为对象赋值
     *
     * @param obj,fieldName
     * @return
     */
    public static void setValueByName(Object obj, String fieldName, Object value) {
        if (obj != null) {
            Class<?> clazz = obj.getClass();
            try {
                Field[] fields = clazz.getFields();
                Optional<Field> field = Arrays.stream(fields).filter(f -> f.getName().equals(fieldName)).findFirst();
                if(!field.isPresent()){
                    fields = clazz.getDeclaredFields();
                    field = Arrays.stream(fields).filter(f -> f.getName().equals(fieldName)).findFirst();
                }
                if(field.isPresent()){
                    Field fieldObject = field.get();
                    fieldObject.setAccessible(true);
                    fieldObject.set(obj, value);
                }
            } catch (Exception e) {
            }
        }
    }

    /**
     * 获取对象的所有日志属性值,从LMSLOG注解中定义的logClazz对象获取含有lmslog注解的属性字段值，作为对象值
     *
     * @param obj
     * @return
     */
    public static List<String> getLogFieldValues(Object obj) throws Exception {
        List<String> fieldValues = new ArrayList<>();
        Class<?> clazz = obj.getClass();
        if (clazz != null) {
            for (Field field : clazz.getDeclaredFields()) {
                // 设置为可访问
                field.setAccessible(true);
                if (!java.lang.reflect.Modifier.isStatic(field.getModifiers()) && !field.isSynthetic() && field.isAnnotationPresent(LogObjname.class)) {
                    fieldValues.add(StringHelper.null2String(field.get(obj)));
                }
            }
        }
        return fieldValues;
    }

    /**
     * 将map转换为对象,必须保证属性名称相同
     *
     * @return
     */
    public static Object map2Object(Map<String, Object> map, Class<?> clzz) {
        try {
            Object target = clzz.newInstance();
            if (CollectionUtils.isEmpty(map)) {
                return target;
            }
            Field[] fields = clzz.getDeclaredFields();
            if (!CollectionUtils.isEmpty(Arrays.asList(fields))) {
                Arrays.stream(fields).filter((Field field) -> map.containsKey(StringHelper.null2String(field.getName()))).forEach(var -> {
                    //获取属性的修饰符
                    int modifiers = var.getModifiers();
                    if (Modifier.isStatic(modifiers) || Modifier.isFinal(modifiers)) {
                        //在lambada中结束本次循环是用return,它不支持continue和break
                        return;
                    }
                    //设置权限
                    var.setAccessible(true);
                    try {
                        Object objValue = map.get(var.getName());
                        if (var.getType().getName().equals("java.util.Date")) {
                            String dateStr = StringHelper.null2String(map.get(var.getName()));
                            if (!dateStr.isEmpty()) {
                                objValue = DateHelper.parseDate(dateStr);
                            }
                        }
                        var.set(target, objValue);
                    } catch (IllegalAccessException e) {
                        //属性类型不对,非法操作,跳过本次循环,直接进入下一次循环
                        throw new RuntimeException(e);
                    }
                });
            }
            return target;
        } catch (InstantiationException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return null;
    }

    /*
     * 将Object转换成List
     */
    public static List<String> obj2List(Object obj, List<String> retval) {
        if (obj instanceof ArrayList<?>) {
            for (Object o : (List<?>) obj) {
                retval.add(StringHelper.null2String(o));
            }
        }
        return retval;
    }

    public static void main(String[] args) {
        System.out.println(PinyinUtil.getFirstLetter("李德生",""));
    }
}
