<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.cepreitrframework.boot</groupId>
    <artifactId>sbtr-lms3-boot-parent</artifactId>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>sbtr-lms3-module</module>
    </modules>
    <packaging>pom</packaging>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.14</version>
    </parent>

    <properties>
        <lmsboot.version>1.0-SNAPSHOT</lmsboot.version>
        <java.version>1.8</java.version>
        <springboot.version>2.7.14</springboot.version>
        <spring-cloud.version>2021.0.6</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.6.0</spring-cloud-alibaba.version>
        <!--        <hibernate-core.version>5.6.15.Final</hibernate-core.version>-->
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <fastjson.version>1.2.83</fastjson.version>
        <fastjson2.version>2.0.38</fastjson2.version>
        <!-- 数据库驱动 -->
        <ojdbc7.version>12.1.0.2</ojdbc7.version>
        <Dm8JdbcDriver18.version>8.1.2.192</Dm8JdbcDriver18.version>
        <mysql-connector-java.version>8.0.15</mysql-connector-java.version>
        <!-- 动态数据源-->
        <dynamic-datasource-spring-boot-starter.version>4.3.0</dynamic-datasource-spring-boot-starter.version>
        <!-- 分页-->
        <pagehelper-spring-boot-starter.version>1.4.6</pagehelper-spring-boot-starter.version>
        <hutool.version>5.8.5</hutool.version>
        <commons-beanutils.version>1.9.3</commons-beanutils.version>
        <guava.version>31.0.1-jre</guava.version>
        <mybatis.version>2.2.2</mybatis.version>
        <tomcat-embed-core.version>9.0.78</tomcat-embed-core.version>
        <druid.version>1.2.11</druid.version>
        <dom4j.version>1.6.1</dom4j.version>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>

    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.17.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.11.1</version>
        </dependency>

        <dependency>
            <groupId>com.antgroup.tugraph</groupId>
            <artifactId>tugraph-db-java-rpc-client</artifactId>
            <version>1.4.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-pool2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.cepreitrframework.boot</groupId>-->
        <!--            <artifactId>lms3-common</artifactId>-->
        <!--            <version>1.0-SNAPSHOT</version>-->
        <!--            <scope>compile</scope>-->
        <!--        </dependency>-->
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- spring-cloud-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- json -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>1.21</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <!-- dom4j -->
            <dependency>
                <groupId>dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>
            <!-- guava工具类 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- hutool工具类-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- commons-beanutils -->
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.4</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.11.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat-embed-core.version}</version>
            </dependency>
            <!-- mybatis -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <!-- druid -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 动态数据源 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource-spring-boot-starter.version}</version>
            </dependency>

            <!-- 数据库驱动 -->
            <!--mysql-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
                <scope>runtime</scope>
            </dependency>

            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc7</artifactId>
                <version>${ojdbc7.version}</version>
            </dependency>
            <!--达梦-->
            <dependency>
                <!--达梦JDBC-->
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${Dm8JdbcDriver18.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmDialect-for-hibernate5.6</artifactId>
                <version>${Dm8JdbcDriver18.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-spring-boot-starter</artifactId>
                <version>4.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>7.17.9</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>7.17.9</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-smile</artifactId>
                <version>2.13.4</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-cbor</artifactId>
                <version>2.13.4</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>7.17.9</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${maven.compiler.encoding}</encoding>
                </configuration>
            </plugin>
            <!-- 打包跳过测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <!-- 避免font文件的二进制文件格式压缩破坏 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <encoding>${maven.compiler.encoding}</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                        <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>otf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jpg</nonFilteredFileExtension>
                        <nonFilteredFileExtension>png</nonFilteredFileExtension>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>doc</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>map</nonFilteredFileExtension>
                        <nonFilteredFileExtension>js</nonFilteredFileExtension>
                        <nonFilteredFileExtension>css</nonFilteredFileExtension>
                        <nonFilteredFileExtension>html</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>cbt/**</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.json</include>
                    <include>**/*.ftl</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>
    <!-- 环境 -->
    <profiles>
        <!-- 正式 -->
        <profile>
            <id>prod</id>
            <properties>
                <!--当前环境-->
                <profile.name>prod</profile.name>
                <!--            &lt;!&ndash;配置文件前缀&ndash;&gt;-->
                <!--            <prefix.name>lms</prefix.name>-->
                <!--Nacos配置中心地址-->
                <config.server-addr>localhost:8448</config.server-addr>
                <!--Nacos配置中心命名空间,用于支持多环境.这里必须使用ID，不能使用名称,默认为空-->
                <config.namespace>prod</config.namespace>
                <!--Nacos用户名-->
                <config.username>nacos</config.username>
                <!--Nacos密码-->
                <config.password>Sbtr.123456</config.password>
                <!--Nacos配置分组名称-->
                <config.group>DEFAULT_GROUP</config.group>
                <!--Nacos服务发现地址-->
                <discovery.server-addr>localhost:8448</discovery.server-addr>
            </properties>
        </profile>
        <!-- 开发 -->
        <profile>
            <id>dev</id>
            <properties>
                <!--当前环境-->
                <profile.name>dev</profile.name>
                <!--            &lt;!&ndash;配置文件前缀&ndash;&gt;-->
                <!--            <prefix.name>lms</prefix.name>-->
                <!--Nacos配置中心地址-->
                <config.server-addr>127.0.0.1:8848</config.server-addr>
                <!--Nacos配置中心命名空间,用于支持多环境.这里必须使用ID，不能使用名称,默认为空-->
                <config.namespace>bf1899c3-676d-43bc-8272-3435124e0b37</config.namespace>
                <!--Nacos用户名-->
                <config.username>nacos</config.username>
                <!--Nacos密码-->
                <config.password>nacos</config.password>
                <!--Nacos配置分组名称-->
                <config.group>DEFAULT_GROUP</config.group>
                <!--Nacos服务发现地址-->
                <discovery.server-addr>127.0.0.1:8848</discovery.server-addr>
            </properties>
        </profile>
        <profile>
            <id>qjh</id>
            <properties>
                <!--当前环境-->
                <profile.name>qjh</profile.name>
                <!--            &lt;!&ndash;配置文件前缀&ndash;&gt;-->
                <!--            <prefix.name>lms</prefix.name>-->
                <!--Nacos配置中心地址-->
                <config.server-addr>10.44.1.12:8848</config.server-addr>
                <!--Nacos配置中心命名空间,用于支持多环境.这里必须使用ID，不能使用名称,默认为空-->
                <config.namespace>qjh</config.namespace>
                <!--Nacos用户名-->
                <config.username>nacos</config.username>
                <!--Nacos密码-->
                <config.password>nacos</config.password>
                <!--Nacos配置分组名称-->
                <config.group>DEFAULT_GROUP</config.group>
                <!--Nacos服务发现地址-->
                <discovery.server-addr>10.44.1.12:8848</discovery.server-addr>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <!--当前环境-->
                <profile.name>test</profile.name>
                <!--            &lt;!&ndash;配置文件前缀&ndash;&gt;-->
                <!--            <prefix.name>lms</prefix.name>-->
                <!--Nacos配置中心地址-->
                <config.server-addr>10.44.1.12:8448</config.server-addr>
                <!--Nacos配置中心命名空间,用于支持多环境.这里必须使用ID，不能使用名称,默认为空-->
                <config.namespace>eed8f53c-039f-477b-8ea7-6b5205af3019</config.namespace>
                <!--Nacos用户名-->
                <config.username>nacos</config.username>
                <!--Nacos密码-->
                <config.password>Sbtr.123456</config.password>
                <!--Nacos配置分组名称-->
                <config.group>DEFAULT_GROUP</config.group>
                <!--Nacos服务发现地址-->
                <discovery.server-addr>10.44.1.12:8448</discovery.server-addr>
            </properties>
        </profile>
    </profiles>
</project>


