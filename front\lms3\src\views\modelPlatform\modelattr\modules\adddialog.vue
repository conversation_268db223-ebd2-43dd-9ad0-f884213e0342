<template>
  <TrDialog
    v-model:open="isOpen"
    destroyOnClose
    :width="600"
    :title="translateText(`新增模型属性`)"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :label-col="{ span: 4 }"
    >
      <a-row>
        <a-col :span="24">
          <a-form-item
            :label="translateText('属性名')"
            :label-col="{ span: 4 }"
            name="name"
          >
            <a-input v-model:value="formData.name" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item
            :label="translateText('属性值')"
            :label-col="{ span: 4 }"
            name="value"
          >
            <a-input v-model:value="formData.value" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </TrDialog>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import TrDialog from "@/components/TrDialog/index.vue";
import { translateText } from "@/utils/translation";
import { rules } from "@/views/modelPlatform/modelattr/data.js";
import type { FormInstance } from "ant-design-vue";
import { addModelAttribute, updateModelAttribute } from "@/api/model.ts";
import { message } from "ant-design-vue";
export interface Props {
  visible: boolean;
  object?: object | null;
}
export interface Emits {
  (e: "close", visible: boolean): void;
  (e: "submit", data: object): void;
}
const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const isOpen = ref(props.visible);
const isEdit = ref(false);
const formRef = ref<FormInstance>();
const loading = ref(false);
const formData = ref<any>({});

// 监听弹窗显示状态，初始化表单数据
watch(
  () => props.visible,
  visible => {
    isOpen.value = visible;
    if (visible) {
      if (props.object) {
        isEdit.value = true;
        formData.value = props.object;
      } else {
        isEdit.value = false;
        formData.value = {};
      }
    }
  }
);
// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;
    let para = Object.assign({}, formData.value);
    let res = isEdit.value
      ? await updateModelAttribute(para)
      : await addModelAttribute(para);
    if (res.success) {
      message.success(res.message);
      emit("submit");
      handleCancel();
    } else {
      message.error(res.message);
    }
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    loading.value = false;
  }
};

const handleCancel = () => {
  emit("close", false);
};
</script>
