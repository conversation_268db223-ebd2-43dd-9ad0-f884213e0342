package com.lms.system.storage.strategy;

import com.lms.common.enums.StorageTypeEnum;
import io.minio.*;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * MinIO存储策略实现
 */
@Slf4j
@Component("minioStorageStrategy")
public class MinIOStorageStrategy extends AbstractFileStorageStrategy {

    @Autowired
    private MinioClient minioClient;

    @Value("${minio.host}")
    private String minioHost;

    @Value("${lms.bucketName:lms-bucket}")
    private String defaultBucket;

    @Override
    protected StorageTypeEnum getStorageTypeEnum() {
        return StorageTypeEnum.MINIO;
    }

    @Override
    public String upload(MultipartFile file) throws Exception {
        return upload(defaultBucket, file);
    }

    @Override
    public String upload(String bucketName, MultipartFile file) throws Exception {
        validateFile(file);
        bucketName = safeBucketName(bucketName, defaultBucket);
        if (!bucketExists(bucketName)) {
            makeBucket(bucketName);
        }
        String objectName = generateUniqueFileName(file.getOriginalFilename());
        try (InputStream inputStream = file.getInputStream()) {
            PutObjectArgs objectArgs = PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(inputStream, file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build();
            minioClient.putObject(objectArgs);
            logOperation("文件上传", objectName, true, "上传到存储桶: " + bucketName);
            return objectName;
        } catch (Exception e) {
            logOperation("文件上传", objectName, false, e.getMessage());
            throw new RuntimeException("MinIO文件上传失败", e);
        }
    }

    @Override
    public Map<String, String> uploadFile(MultipartFile file, String objectName) throws Exception {
        validateFile(file);
        if (StringUtils.isEmpty(objectName)) {
            objectName = generateUniqueFileName(file.getOriginalFilename());
        }
        try (InputStream inputStream = file.getInputStream()) {
            PutObjectArgs objectArgs = PutObjectArgs.builder()
                    .bucket(defaultBucket)
                    .object(objectName)
                    .stream(inputStream, file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build();
            ObjectWriteResponse response = minioClient.putObject(objectArgs);
            String fileUrl = generateFileUrl(defaultBucket, objectName);
            Map<String, String> result = new HashMap<>();
            result.put("fileName", objectName);
            result.put("fileUrl", fileUrl);
            result.put("fileSize", String.valueOf(file.getSize()));
            result.put("contentType", file.getContentType());
            result.put("etag", response.etag());
            logOperation("文件上传", objectName, true, "返回详细信息");
            return result;
        } catch (Exception e) {
            logOperation("文件上传", objectName, false, e.getMessage());
            throw new RuntimeException("MinIO文件上传失败", e);
        }
    }

    @Override
    public void uploadLocalFile(String fileLocation, String bucketName, String objectName) throws Exception {
        if (StringUtils.isEmpty(fileLocation) || StringUtils.isEmpty(objectName)) {
            throw new IllegalArgumentException("文件路径和对象名称不能为空");
        }
        bucketName = safeBucketName(bucketName, defaultBucket);
        try {
            UploadObjectArgs uploadArgs = UploadObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .filename(fileLocation)
                    .build();
            minioClient.uploadObject(uploadArgs);
            logOperation("本地文件上传", objectName, true, "从路径: " + fileLocation);
        } catch (Exception e) {
            logOperation("本地文件上传", objectName, false, e.getMessage());
            throw new RuntimeException("MinIO本地文件上传失败", e);
        }
    }

    @Override
    public void download(String fileName, HttpServletResponse response) throws Exception {
        download(defaultBucket, fileName, response);
    }

    @Override
    public void download(String bucketName, String fileName, HttpServletResponse response) throws Exception {
        if (StringUtils.isEmpty(fileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        bucketName = safeBucketName(bucketName, defaultBucket);
        try {
            GetObjectArgs getObjectArgs = GetObjectArgs.builder()
                    .bucket(bucketName)
                    .object(fileName)
                    .build();
            try (InputStream inputStream = minioClient.getObject(getObjectArgs);
                 OutputStream outputStream = response.getOutputStream()) {
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }
            logOperation("文件下载", fileName, true, "从存储桶: " + bucketName);
        } catch (Exception e) {
            logOperation("文件下载", fileName, false, e.getMessage());
            throw new RuntimeException("MinIO文件下载失败", e);
        }
    }

    @Override
    public InputStream download(String fileName) throws Exception {
        return download(defaultBucket, fileName);
    }

    @Override
    public InputStream download(String bucketName, String fileName) throws Exception {
        if (StringUtils.isEmpty(fileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        bucketName = safeBucketName(bucketName, defaultBucket);
        try {
            GetObjectArgs getObjectArgs = GetObjectArgs.builder()
                    .bucket(bucketName)
                    .object(fileName)
                    .build();
            InputStream inputStream = minioClient.getObject(getObjectArgs);
            logOperation("文件流下载", fileName, true, "从存储桶: " + bucketName);
            return inputStream;
        } catch (Exception e) {
            logOperation("文件流下载", fileName, false, e.getMessage());
            throw new RuntimeException("MinIO文件下载失败", e);
        }
    }

    @Override
    public boolean checkFileIsExist(String fileName) {
        return checkFileIsExist(defaultBucket, fileName);
    }

    @Override
    public boolean checkFileIsExist(String bucketName, String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return false;
        }
        bucketName = safeBucketName(bucketName, defaultBucket);
        try {
            StatObjectArgs statObjectArgs = StatObjectArgs.builder()
                    .bucket(bucketName)
                    .object(fileName)
                    .build();
            minioClient.statObject(statObjectArgs);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean deleteFile(String fileName) {
        return deleteFile(defaultBucket, fileName);
    }

    @Override
    public boolean deleteFile(String bucketName, String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return false;
        }
        bucketName = safeBucketName(bucketName, defaultBucket);
        try {
            RemoveObjectArgs removeObjectArgs = RemoveObjectArgs.builder()
                    .bucket(bucketName)
                    .object(fileName)
                    .build();
            minioClient.removeObject(removeObjectArgs);
            logOperation("文件删除", fileName, true, "从存储桶: " + bucketName);
            return true;
        } catch (Exception e) {
            logOperation("文件删除", fileName, false, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean bucketExists(String bucketName) {
        if (StringUtils.isEmpty(bucketName)) {
            return false;
        }
        try {
            BucketExistsArgs bucketExistsArgs = BucketExistsArgs.builder()
                    .bucket(bucketName)
                    .build();
            return minioClient.bucketExists(bucketExistsArgs);
        } catch (Exception e) {
            log.error("检查存储桶是否存在失败: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public boolean makeBucket(String bucketName) {
        if (StringUtils.isEmpty(bucketName)) {
            return false;
        }
        try {
            if (!bucketExists(bucketName)) {
                MakeBucketArgs makeBucketArgs = MakeBucketArgs.builder()
                        .bucket(bucketName)
                        .build();
                minioClient.makeBucket(makeBucketArgs);
                logOperation("存储桶创建", bucketName, true, "新建存储桶");
                return true;
            }
            return true;
        } catch (Exception e) {
            logOperation("存储桶创建", bucketName, false, e.getMessage());
            return false;
        }
    }

    @Override
    public String getPreviewUrl(String fileName) {
        return getPreviewUrl(defaultBucket, fileName);
    }

    @Override
    public String getPreviewUrl(String bucketName, String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return "";
        }
        bucketName = safeBucketName(bucketName, defaultBucket);
        try {
            GetPresignedObjectUrlArgs getPresignedObjectUrlArgs = GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(bucketName)
                    .object(fileName)
                    .expiry(7, TimeUnit.DAYS)
                    .build();
            return minioClient.getPresignedObjectUrl(getPresignedObjectUrlArgs);
        } catch (Exception e) {
            log.error("生成预览URL失败: {}", e.getMessage());
            return generateFileUrl(bucketName, fileName);
        }
    }

    @Override
    public String generateFileUrl(String bucketName, String objectName) {
        if (StringUtils.isEmpty(bucketName) || StringUtils.isEmpty(objectName)) {
            return "";
        }
        try {
            return String.format("%s/%s/%s", minioHost, bucketName, objectName);
        } catch (Exception e) {
            log.error("生成文件URL出错: {}", e.getMessage());
            return String.format("%s/%s/%s", minioHost, bucketName, objectName);
        }
    }
}
