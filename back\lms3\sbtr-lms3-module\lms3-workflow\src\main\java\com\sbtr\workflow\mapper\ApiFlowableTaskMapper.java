package com.sbtr.workflow.mapper;

import com.sbtr.workflow.dto.CommonTaskDto;
import com.sbtr.workflow.dto.ProcessDefinitionDto;
import com.sbtr.workflow.dto.StartedDto;
import com.sbtr.workflow.vo.ActHiIdentitylinkVo;
import com.sbtr.workflow.vo.CommentVo;
import com.sbtr.workflow.vo.TaskVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;


public interface ApiFlowableTaskMapper {


    List<TaskVo> getToDoTasksOracle(@Param("taskId") String taskId, @Param("businessKey") String businessKey, @Param("userNameId") String userNameId, @Param("formName") String formName, @Param("modelName") String modelName,
                                    @Param("processModelType") String processModelType,
                                    @Param("startTime") String startTime,
                                    @Param("startDate") String startDate,
                                    @Param("endDate") String endDate,
                                    @Param("list") List<String> list,
                                    @Param("mlevel") Integer mlevel);

    List<TaskVo> getToDoTasksMySql(@Param("taskId") String taskId, @Param("businessKey") String businessKey, @Param("userNameId") String userNameId, @Param("formName") String formName, @Param("modelName") String modelName,
                                   @Param("processModelType") String processModelType,
                                   @Param("startTime") String startTime,
                                   @Param("startDate") String startDate,
                                   @Param("endDate") String endDate,
                                   @Param("list") List<String> list,
                                   @Param("mlevel") Integer mlevel);

    List<String> getFinanceMySql(@Param("userNameId") String userNameId, @Param("formName") String formName, @Param("modelName") String modelName,
                                 @Param("processModelType") String processModelType,
                                 @Param("startTime") String startTime,
                                 @Param("startDate") String startDate,
                                 @Param("endDate") String endDate);

    List<String> getFinanceOracle(@Param("userNameId") String userNameId, @Param("formName") String formName, @Param("modelName") String modelName,
                                  @Param("processModelType") String processModelType,
                                  @Param("startTime") String startTime,
                                  @Param("startDate") String startDate,
                                  @Param("endDate") String endDate);

    List<String> getFinanceSqlServer(@Param("userNameId") String userNameId, @Param("formName") String formName, @Param("modelName") String modelName,
                                     @Param("processModelType") String processModelType,
                                     @Param("startTime") String startTime,
                                     @Param("startDate") String startDate,
                                     @Param("endDate") String endDate);


    TaskVo getToDoTaskByUuid(@Param("userNameId") String userNameId, @Param("uuid") String uuid);

    List<CommonTaskDto> getMyHistoryPageSetOracle(@Param("userNameId") String userNameId, @Param("name") String name, @Param("procDefName") String procDefName,
                                                  @Param("processModelType") String processModelType,
                                                  @Param("startTime") String startTime,
                                                  @Param("startDate") String startDate,
                                                  @Param("endDate") String endDate);

    List<CommonTaskDto> getMyHistoryPageSetMySql(@Param("userNameId") String userNameId, @Param("name") String name, @Param("procDefName") String procDefName,
                                                 @Param("processModelType") String processModelType,
                                                 @Param("startTime") String startTime,
                                                 @Param("startDate") String startDate,
                                                 @Param("endDate") String endDate);

    List<TaskVo> getMyNoEndProcessPageSetDataOracle(@Param("userNameId") String userNameId, @Param("formName") String formName,
                                                    @Param("modelName") String modelName, @Param("processModelType") String processModelType,
                                                    @Param("startTime") String startTime,
                                                    @Param("startDate") String startDate,
                                                    @Param("endDate") String endDate
    );

    List<TaskVo> getMyNoEndProcessPageSetDataSqlServer(@Param("userNameId") String userNameId, @Param("formName") String formName,
                                                       @Param("modelName") String modelName, @Param("processModelType") String processModelType,
                                                       @Param("startTime") String startTime,
                                                       @Param("startDate") String startDate,
                                                       @Param("endDate") String endDate
    );

    List<TaskVo> getMyNoEndProcessPageSetDataMySql(@Param("userNameId") String userNameId, @Param("formName") String formName,
                                                   @Param("modelName") String modelName, @Param("processModelType") String processModelType,
                                                   @Param("startTime") String startTime,
                                                   @Param("startDate") String startDate,
                                                   @Param("endDate") String endDate
    );

    List<ProcessDefinitionDto> getDeployedPageSetOracle(@Param("modelName") String modelName, @Param("modelKey") String modelKey);

    List<ProcessDefinitionDto> getDeployedPageSetMySql(@Param("modelName") String modelName, @Param("modelKey") String modelKey);

    List<CommonTaskDto> getTaskPageSetOracle(@Param("formName") String formName, @Param("modelName") String modelName);

    List<CommonTaskDto> getTaskPageSetMySql(@Param("formName") String formName, @Param("modelName") String modelName);

    List<CommonTaskDto> getTaskPageSetSqlServer(@Param("taskId") String taskId, @Param("procDefName") String procDefName);

    List<StartedDto> getStartedPageSet(@Param("modelName") String modelName, @Param("modelKey") String modelKey);

    List<TaskVo> getApplyedTasksOracle(@Param("userNameId") String userNameId, @Param("formName") String formName, @Param("modelName") String modelName, @Param("processModelType") String processModelType);

    List<TaskVo> getApplyedTasksMySql(@Param("userNameId") String userNameId, @Param("formName") String formName, @Param("modelName") String modelName, @Param("processModelType") String processModelType);

    List<CommentVo> getTaskCommentsByTaskId(@Param("taskId") String taskId);

    List<ActHiIdentitylinkVo> getListByTaskId(@Param("taskId") String taskId);

    List<String> getListByUserNmaeId(@Param("groupId") String groupId);

    List<String> getListPostByUserNmaeId(@Param("groupId") String groupId);

    Integer updateBusinessData(@Param("sql") String sql);

    List<TaskVo> getToDoTasksSqlServer(@Param("taskId") String taskId, @Param("userNameId") String userNameId, @Param("businessKey") String businessKey, @Param("formName") String formName, @Param("modelName") String modelName,
                                       @Param("processModelType") String processModelType,
                                       @Param("startTime") String startTime,
                                       @Param("startDate") String startDate,
                                       @Param("endDate") String endDate);

    List<TaskVo> getApplyedTasksSqlserver(@Param("userNameId") String userNameId, @Param("formName") String formName, @Param("modelName") String modelName, @Param("processModelType") String processModelType);

    List<CommonTaskDto> getMyHistoryPageSetSqlServer(@Param("userNameId") String userNameId, @Param("name") String name, @Param("procDefName") String procDefName,
                                                     @Param("processModelType") String processModelType,
                                                     @Param("startTime") String startTime,
                                                     @Param("startDate") String startDate,
                                                     @Param("endDate") String endDate);

    List<CommonTaskDto> getCompletedTaskPageSet(@Param("formName") String formName, @Param("modelName") String modelName);

    List<CommentVo> getTaskCommentsByProcessInstanceId(@Param("processInstanceId") String processInstanceId);

    CommentVo getActHiTaskinstById(@Param("id") String id);

    List<TaskVo> getTaskRefByParam(String processInstanceId, String businessKey, String taskId);

    List<TaskVo> getActHiTaskVoById(String processInstanceId, String businessKey, String taskId);

    List<Map> getCurrentAssigneeByTask(String taskId);

    List<Map> getOtherAssigneeByTask(String pid, String kid);

    List<TaskVo> getAgentTasksOracle(@Param("taskId") String taskId, @Param("businessKey") String businessKey, @Param("userNameIds") List<String> userNameIds, @Param("formName") String formName, @Param("modelName") String modelName,
                                     @Param("processModelType") String processModelType,
                                     @Param("startTime") String startTime,
                                     @Param("startDate") String startDate,
                                     @Param("endDate") String endDate,
                                     @Param("groupIds") List<String> groupIds);

    List<TaskVo> getAgentTasksMysql(@Param("taskId") String taskId, @Param("businessKey") String businessKey, @Param("userNameIds") List<String> userNameIds, @Param("formName") String formName, @Param("modelName") String modelName,
                                    @Param("processModelType") String processModelType,
                                    @Param("startTime") String startTime,
                                    @Param("startDate") String startDate,
                                    @Param("endDate") String endDate,
                                    @Param("groupIds") List<String> groupIds);

    List<TaskVo> getToDoTaskBybusinessKeysOracle(@Param("businessKeys") List<String> businessKeys, @Param("userNameId") String userNameId, @Param("groupIds") List<String> groupIds);

    List<TaskVo> getToDoTaskBybusinessKeysMySql(@Param("businessKeys") List<String> businessKeys, @Param("userNameId") String userNameId, @Param("groupIds") List<String> groupIds);

    List<TaskVo> getApplyedTaskBybusinessKeysOracle(@Param("businessKeys") List<String> businessKeys, @Param("userNameId") String userNameId);

    List<TaskVo> getApplyedTaskBybusinessKeysMySql(@Param("businessKeys") List<String> businessKeys, @Param("userNameId") String userNameId);

    List<TaskVo> getToDoTaskByModelKeyOracle(@Param("modelKey") String modelKey, @Param("userNameId") String userNameId, @Param("groupIds") List<String> groupIds, @Param("mlevel") Integer mlevel);

    List<TaskVo> getToDoTaskByModelKeyMySql(@Param("modelKey") String modelKey, @Param("userNameId") String userNameId, @Param("groupIds") List<String> groupIds, @Param("mlevel") Integer mlevel);

    List<TaskVo> getApplyedTaskByModelKeyOracle(@Param("modelKey") String modelKey, @Param("userNameId") String userNameId);

    List<TaskVo> getApplyedTaskByModelKeyMySql(@Param("modelKey") String modelKey, @Param("userNameId") String userNameId);

    int updateTaskOpeningTimeByKey(@Param("taskId") String taskId, @Param("date") Date date);

    void updateMlevelByBusinessKey(@Param("businessUuid") String businessUuid, @Param("slevel") Integer slevel);

    List<Map<String, Object>> getActNameByProcessInstanceIdListOracle(@Param("processInstanceIdList") List<String> processInstanceIdList);

    List<Map<String, Object>> getActNameByProcessInstanceIdListMySql(@Param("processInstanceIdList") List<String> processInstanceIdList);

    List<TaskVo> getToDoTaskBybusinessKeysAndModelKeyOracle(@Param("businessKeys") List<String> businessKeys, @Param("userNameId") String userNameId, @Param("groupIds") List<String> groupIds, @Param("modelKey") String modelKey);

    List<TaskVo> getToDoTaskBybusinessKeysAndModelKeyMySql(@Param("businessKeys") List<String> businessKeys, @Param("userNameId") String userNameId, @Param("groupIds") List<String> groupIds, @Param("modelKey") String modelKey);

    List<TaskVo> getApplyedTaskBybusinessKeysAndModelKeyOracle(@Param("businessKeys") List<String> businessKeys, @Param("userNameId") String userNameId, @Param("modelKey") String modelKey);

    List<TaskVo> getApplyedTaskBybusinessKeysAndModelKeyMySql(@Param("businessKeys") List<String> businessKeys, @Param("userNameId") String userNameId, @Param("modelKey") String modelKey);

    List<TaskVo> getBusinessInfoByTaskIdOracle(@Param("userNameId") String taskId);

    List<TaskVo> getBusinessInfoByTaskIdMySql(@Param("taskId") String taskId);

    List<TaskVo> getBusinessInfoByTaskIdFromApplyedOracle(@Param("taskId") String taskId);

    List<TaskVo> getBusinessInfoByTaskIdFromApplyedMySql(@Param("taskId") String taskId);

}
