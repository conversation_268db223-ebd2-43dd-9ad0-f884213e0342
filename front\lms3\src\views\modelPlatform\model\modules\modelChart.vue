<template>
  <div ref="chartContainer" class="chart-container" />
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts/core";
import { GraphChart } from "echarts/charts";
import { CanvasRenderer } from "echarts/renderers";
import { TooltipComponent, LegendComponent } from "echarts/components";
import type { ECharts, EChartsOption } from "echarts";
import { getModelCharts } from "@/api/model";
import { message } from "ant-design-vue";
import type { Model } from "@/types/model";
// 注册必需组件
echarts.use([<PERSON><PERSON><PERSON><PERSON><PERSON>, CanvasRenderer, TooltipComponent, LegendComponent]);

// 类型定义
interface ModelNode {
  id: string;
  name: string;
  category: "模型" | "属性";
  symbol?: string;
  symbolSize?: object;
  value?: string;
  itemStyle?: object;
  label?: object;
}

interface ModelLink {
  source: string;
  target: string;
  value: "拥有" | "包含";
}

interface Props {
  model: Model | null;
}

const props = defineProps<Props>();
const chartContainer = ref<HTMLDivElement | null>(null);
let chartInstance;

// 响应式数据
const nodes = ref<ModelNode[]>([]);

const links = ref<ModelLink[]>([]);

// 初始化图表
const initChart = async () => {
  if (!chartContainer.value) return;
  const response = await getModelCharts(props.model.id);
  if (response.success && response.result !== null) {
    nodes.value = response.result.nodes;
    links.value = response.result.links;
    chartInstance = echarts.init(chartContainer.value);
    const option: EChartsOption = {
      tooltip: {
        formatter: (params: any) => {
          if (params.dataType === "node") {
            return params.data.value
              ? params.data.name + "：" + params.data.value
              : `${params.data.name}`;
          } else {
            return `${params.data.source} → ${params.data.target}<br/>关系: ${
              params.data.value
            }`;
          }
        }
      },
      legend: {
        data: [
          {
            name: "模型节点",
            icon: "rect",
            itemStyle: { color: "#2bce05" }
          },
          {
            name: "属性节点",
            icon: "circle",
            itemStyle: { color: "#73c0de" }
          }
        ]
      },
      animationDuration: 1500,
      series: [
        {
          name: "模型关系",
          type: "graph",
          layout: "force",
          force: {
            repulsion: 300,
            edgeLength: 100,
            gravity: 0.1,
            friction: 0.6
          },
          roam: true,
          draggable: true,
          focusNodeAdjacency: true,
          data: nodes.value.map(node => ({
            ...node,
            category: node.category === "模型" ? 0 : 1,
            symbol: node.category === "模型" ? "rect" : "circle",
            symbolSize: node.category === "模型" ? [100, 40] : 30,
            itemStyle: {
              color: node.category === "模型" ? "#2bce05" : "#73c0de"
            },
            label: {
              position: node.category === "模型" ? "inside" : "right",
              fontSize: 14
            }
          })),
          links: links.value.map(link => ({
            ...link,
            lineStyle: {
              color: link.value === "包含" ? "#2bce05" : "#73c0de",
              curveness: link.value === "包含" ? 0 : 0.5
            }
          })),
          categories: [{ name: "模型节点" }, { name: "属性节点" }],
          edgeSymbol: ["none", "arrow"],
          edgeSymbolSize: 8,
          label: {
            show: true,
            position: "right",
            formatter: (params: any) => {
              return params.data.category === 0
                ? params.data.name
                : params.data.name + "：" + params.data.value;
            }
          },
          emphasis: {
            focus: "adjacency",
            lineStyle: { width: 3 }
          },
          itemStyle: {
            borderColor: "#fff",
            borderWidth: 1,
            shadowBlur: 10,
            shadowColor: "rgba(0, 0, 0, 0.3)"
          }
        }
      ]
    };
    chartInstance.setOption(option);
  } else if (response.success && response.result === null) {
    message.success("无知识图谱数据");
  } else {
    message.error(response.message || "获取用户信息列表失败");
  }
};

// 响应窗口变化
const handleResize = () => {
  chartInstance?.resize();
};

// 生命周期
onMounted(() => {
  initChart();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  chartInstance?.dispose();
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 600px;
}
</style>
