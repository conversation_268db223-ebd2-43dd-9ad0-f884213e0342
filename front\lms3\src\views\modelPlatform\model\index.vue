<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { message } from "ant-design-vue";
import {
  CloudUploadOutlined,
  PlusOutlined,
  DeleteOutlined,
  UpOutlined,
  DownOutlined,
  SearchOutlined
} from "@ant-design/icons-vue";
import AntDesignFileTextOutlined from "~icons/ant-design/file-text-outlined";
import type { Model, ModelListParams, ModelFilter } from "@/types/model.d.ts";
import ModelCard from "./modules/card.vue";
import ModelAddForm from "./modules/addForm.vue";
import { getModelByPageList } from "@/api/model";

defineOptions({
  name: "model"
});

// 自定义筛选条件接口
interface CustomFilter {
  id: number;
  key: string;
  operator: string;
  value: string;
}

const loading = ref(false);
const models = ref<Model[]>([]);
const addModalVisible = ref(false);
const showAdvancedSearch = ref(false);
const customFilters = ref<CustomFilter[]>([]);
const nextFilterId = ref(1);
const allModels = ref<Model[]>([]); // 存储所有模型数据

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
  pages: 0
});
// 筛选条件
const filters = reactive<ModelFilter>({
  ATA章节: "",
  SNS代码: "",
  件号: ""
});

// 切换高级搜索显示状态
const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value;

  // 如果是首次打开且没有自定义条件，添加一个默认条件
  if (showAdvancedSearch.value && customFilters.value.length === 0) {
    addCustomFilter();
  }
};
// 添加自定义筛选条件
const addCustomFilter = () => {
  const newFilter: CustomFilter = {
    id: nextFilterId.value++,
    key: "",
    operator: "like", // 保持兼容性，但不再显示
    value: ""
  };
  customFilters.value.push(newFilter);
};

// 移除自定义筛选条件
const removeCustomFilter = (index: number) => {
  if (customFilters.value.length <= 1) {
    message.warning("至少需要保留一个搜索条件");
    return;
  }
  customFilters.value.splice(index, 1);
};

// 清空自定义筛选条件
const clearCustomFilters = () => {
  customFilters.value = [];
  message.success("已清空所有自定义搜索条件");
};

// 应用自定义筛选条件
const applyCustomFilters = () => {
  // 验证自定义条件
  const invalidFilters = customFilters.value.filter(
    filter => !filter.key.trim() || !filter.value.trim()
  );

  if (invalidFilters.length > 0) {
    message.error("请完善所有自定义搜索条件的字段名和值");
    return;
  }

  // 应用搜索
  pagination.current = 1;
  fetchModelList();
  message.success(`已应用 ${customFilters.value.length} 个自定义搜索条件`);
};

// 构建查询参数（包含自定义条件）
const buildQueryParams = (): ModelListParams => {
  const params: ModelListParams = {
    pageIndex: pagination.current,
    pageSize: pagination.size,
    ...filters
  };

  // 添加自定义筛选条件
  if (customFilters.value.length > 0) {
    const validCustomFilters = customFilters.value.filter(
      filter => filter.key.trim() && filter.value.trim()
    );

    if (validCustomFilters.length > 0) {
      // 将自定义条件转换为查询参数
      const customParams: Record<string, any> = {};

      validCustomFilters.forEach((filter, index) => {
        const paramKey = `${filter.key}`;
        // const paramOperator = `custom_${index}_operator`;

        customParams[paramKey] = filter.value;
        // customParams[paramOperator] = filter.operator; // 保持兼容性，默认使用like操作符
      });

      Object.assign(params, customParams);
    }
  }

  // 过滤空值
  Object.keys(params).forEach(key => {
    if (
      params[key as keyof ModelListParams] === "" ||
      params[key as keyof ModelListParams] === undefined
    ) {
      delete params[key as keyof ModelListParams];
    }
  });

  return params;
};

// 获取模型列表
const fetchModelList = async () => {
  try {
    loading.value = true;

    // const params: ModelListParams = {};
    const params = buildQueryParams();
    console.log("🔍 查询参数:", params);

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (
        params[key as keyof ModelListParams] === "" ||
        params[key as keyof ModelListParams] === undefined
      ) {
        delete params[key as keyof ModelListParams];
      }
    });
    const response = await getModelByPageList(JSON.stringify(params));
    if (response.success) {
      models.value = response.result.records;
      pagination.total = response.result.total;
    } else {
      message.error(response.message || "获取模型列表失败");
    }
  } catch (error) {
    console.error("获取模型列表失败:", error);
    message.error("获取模型列表失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  fetchModelList();
};

// 重置筛选条件
const handleReset = () => {
  Object.assign(filters, {
    ATA章节: "",
    SNS代码: "",
    件号: ""
  });
  pagination.current = 1;
  fetchModelList();
};

// 新增模型
const handleAdd = () => {
  addModalVisible.value = true;
};

// 处理新增提交
const handleAddSubmit = async (formData: any) => {
  try {
    fetchModelList();
  } catch (error) {
    console.error("新增模型失败:", error);
    // message.error("新增模型失败");
  }
};

// 处理删除成功
const handleDeleteSuccess = async () => {
  // 先获取当前页面的数据总数
  const currentPageDataCount = models.value.length;

  // 如果当前页面只有1条数据（即将被删除），且不是第一页，则跳转到上一页
  if (currentPageDataCount === 1 && pagination.current > 1) {
    pagination.current -= 1;
  }

  // 刷新模型列表
  await fetchModelList();
};

// 处理编辑成功
const handleEditSuccess = () => {
  // 刷新当前页面的模型列表
  fetchModelList();
};

// 分页变化
const handlePageChange = (page: number, pageSize: number) => {
  pagination.current = page;
  pagination.size = pageSize;
  fetchModelList();
};

// 页面大小变化
const handlePageSizeChange = (current: number, size: number) => {
  pagination.current = 1;
  pagination.size = size;
  fetchModelList();
};

// 监听筛选条件变化（可选：实现实时搜索）
// watch(
//   () => filters["ATA章节"],
//   () => {
//     // 可以实现防抖搜索
//     // debounce(() => {
//     //   pagination.current = 1
//     //   fetchModelList()
//     // }, 500)
//   }
// );

// 页面加载时获取数据
onMounted(() => {
  fetchModelList();
});
</script>

<template>
  <div class="model-management">
    <!-- 页面标题 -->
    <!-- <div class="page-header">
      <h1 class="page-title">模型管理</h1>
    </div> -->

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-grid">
        <!-- 第一行 -->
        <!-- <div class="filter-item">
          <label class="filter-label">ATA章节</label>
          <a-select
            v-model:value="filters.ATA章节"
            placeholder="请选择"
            class="filter-select"
            :options="ataMarkOptions"
            allow-clear
          />
        </div> -->

        <div class="filter-item">
          <label class="filter-label">ATA章节</label>
          <a-input
            v-model:value="filters.ATA章节"
            placeholder="请输入"
            class="filter-input"
            allow-clear
          />
        </div>

        <div class="filter-item">
          <label class="filter-label">SNS代码</label>
          <a-input
            v-model:value="filters.SNS代码"
            placeholder="请输入"
            class="filter-input"
            allow-clear
          />
        </div>

        <div class="filter-item">
          <label class="filter-label">件号</label>
          <a-input
            v-model:value="filters.件号"
            placeholder="请输入"
            class="filter-input"
            allow-clear
          />
        </div>

        <div class="filter-actions">
          <a-button class="reset-btn" @click="handleReset"> 重置 </a-button>
          <a-button
            type="default"
            class="advanced-search-btn"
            :class="{ active: showAdvancedSearch }"
            @click="toggleAdvancedSearch"
          >
            <!-- <UpOutlined v-if="showAdvancedSearch" />
            <DownOutlined v-else /> -->
            更多搜索条件
          </a-button>
          <a-button type="primary" class="search-btn" @click="handleSearch">
            查询
          </a-button>
        </div>
      </div>

      <!-- 高级搜索浮动面板 -->
      <div v-if="showAdvancedSearch" class="advanced-search-overlay" @click="toggleAdvancedSearch">
        <div class="advanced-search-panel" @click.stop>
          <div class="advanced-search-header">
            <h4 class="advanced-search-title">自定义搜索条件</h4>
            <a-button
              type="primary"
              size="small"
              class="add-filter-btn"
              @click="addCustomFilter"
            >
              <PlusOutlined />
              添加条件
            </a-button>
          </div>

          <div class="custom-filters">
            <div
              v-for="(filter, index) in customFilters"
              :key="filter.id"
              class="custom-filter-item"
            >
              <div class="filter-key">
                <a-input
                  v-model:value="filter.key"
                  placeholder="请输入字段名"
                  size="small"
                  class="key-input"
                />
              </div>

              <div class="filter-value">
                <a-input
                  v-model:value="filter.value"
                  placeholder="请输入值"
                  size="small"
                  class="value-input"
                />
              </div>

              <div class="filter-actions">
                <a-button
                  type="text"
                  danger
                  size="small"
                  :disabled="customFilters.length <= 1"
                  class="remove-filter-btn"
                  @click="removeCustomFilter(index)"
                >
                  <DeleteOutlined />
                </a-button>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="customFilters.length === 0" class="empty-filters">
              <SearchOutlined class="empty-icon" />
              <p>暂无自定义搜索条件</p>
              <a-button
                type="dashed"
                class="add-first-filter-btn"
                @click="addCustomFilter"
              >
                <PlusOutlined />
                添加第一个条件
              </a-button>
            </div>
          </div>

          <!-- 高级搜索操作按钮 -->
          <div class="advanced-search-actions">
            <a-button class="clear-btn" @click="clearCustomFilters">
              清空条件
            </a-button>
            <a-button
              type="primary"
              class="apply-btn"
              @click="applyCustomFilters"
            >
              应用条件
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="action-section">
      <a-button type="primary" class="upload-btn" @click="handleAdd">
        <CloudUploadOutlined />新增
      </a-button>
    </div>

    <!-- 模型网格 -->
    <div class="models-section">
      <div class="models-container">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <a-spin size="large" />
          <p>加载中...</p>
        </div>
        <!-- 模型列表 -->
        <div v-else-if="models.length > 0" class="models-grid">
          <ModelCard
            v-for="model in models"
            :key="model.id"
            :model="model"
            @delete-success="handleDeleteSuccess"
            @edit-success="handleEditSuccess"
          />
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <AntDesignFileTextOutlined class="empty-icon" />
          <p>暂无模型数据</p>
        </div>
      </div>
    </div>

    <!-- 分页栏 -->
    <div v-if="!loading && models.length > 0" class="pagination-section">
      <div class="pagination-container">
        <a-pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :show-size-changer="true"
          :show-quick-jumper="false"
          :show-total="
            (total, range) =>
              `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`
          "
          :page-size-options="['10', '20', '50', '100']"
          @change="handlePageChange"
          @show-size-change="handlePageSizeChange"
        >
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>

    <!-- 新增模型弹窗 -->
    <ModelAddForm
      :visible="addModalVisible"
      @update:visible="addModalVisible = $event"
      @submit="handleAddSubmit"
    />
  </div>
</template>

<style lang="scss" scoped>
.model-management {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--gray-50);
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.page-header {
  padding: var(--spacing-6);
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
}

.page-title {
  font-size: var(--text-xl);
  font-weight: var(--font-medium);
  color: var(--gray-900);
  margin: 0;
}

.filter-section {
  padding: var(--spacing-6);
  background-color: white;
  margin-bottom:0;
  // border-bottom: 1px solid var(--gray-200);
}

.filter-grid {
  display: grid;
  grid-template-columns: 250px 250px 250px 1fr;
  gap: var(--spacing-4);
  // margin-bottom: var(--spacing-4);

  &:last-child {
    margin-bottom: 0;
  }

  .filter-item:nth-child(1) {
    grid-column: 1;
  }

  .filter-item:nth-child(2) {
    grid-column: 2;
  }

  .filter-item:nth-child(3) {
    grid-column: 3;
  }

  .filter-actions {
    grid-column: 4;
    justify-self: end;
  }
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: var(--spacing-2);
}

.filter-label {
  font-size: var(--text-sm);
  color: var(--gray-600);
  white-space: nowrap;
}

.filter-select,
.filter-input {
  flex: 1;
}

.filter-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-3);
}

.search-btn,
.reset-btn,
.advanced-search-btn {
  padding: 0 var(--spacing-6);
}

.advanced-search-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  transition: all var(--transition-normal);

  &.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);

    &:hover {
      background-color: var(--primary-hover);
      border-color: var(--primary-hover);
    }
  }
}

// 高级搜索浮动面板
.advanced-search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 100px;
  animation: fadeIn 0.2s ease-out;
}

.advanced-search-panel {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-6);
  min-width: 600px;
  max-width: 800px;
  max-height: 70vh;
  overflow-y: auto;
  animation: slideDown 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.advanced-search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.advanced-search-title {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--gray-900);
  margin: 0;
}

.add-filter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-1);
}

.custom-filters {
  margin-bottom: var(--spacing-4);
}

.custom-filter-item {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: var(--spacing-3);
  align-items: center;
  padding: var(--spacing-3);
  background-color: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-2);
  transition: all var(--transition-normal);

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-key,
.filter-value {
  display: flex;
  align-items: center;
}

.key-input,
.value-input {
  width: 100%;
}

.filter-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-filter-btn {
  color: var(--error-color);

  &:hover {
    background-color: rgba(255, 77, 79, 0.1);
  }

  &:disabled {
    color: var(--gray-300);

    &:hover {
      background-color: transparent;
    }
  }
}

// 空状态
.empty-filters {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  text-align: center;
  color: var(--gray-500);

  .empty-icon {
    font-size: 48px;
    color: var(--gray-300);
    margin-bottom: var(--spacing-3);
  }

  p {
    margin: 0 0 var(--spacing-4) 0;
    font-size: var(--text-sm);
  }
}

.add-first-filter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
}

.advanced-search-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-3);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--gray-200);
}

.clear-btn,
.apply-btn {
  padding: 0 var(--spacing-6);
}

.action-section {
  padding: var(--spacing-3) var(--spacing-6);
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
}

.add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
}

.models-section {
  flex: 1;
  overflow: auto;
}

.models-container {
  padding: var(--spacing-6);
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: var(--spacing-4);

  @media (max-width: 1536px) {
    grid-template-columns: repeat(5, 1fr);
  }

  @media (max-width: 1280px) {
    grid-template-columns: repeat(4, 1fr);
  }

  @media (max-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 640px) {
    grid-template-columns: 1fr;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-16) 0;

  .empty-icon {
    width: 64px;
    height: 64px;
    color: var(--gray-300);
    margin-bottom: var(--spacing-4);
  }

  p {
    color: var(--gray-500);
    margin: 0;
  }
}

.pagination-section {
  padding: var(--spacing-1);
  background-color: white;
  border-top: 1px solid var(--gray-200);
}

.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
}

.pagination-nav-btn {
  background-color: white;
  color: var(--gray-600);
  border: none !important;
  box-shadow: none !important;
  padding: 0 var(--spacing-3);
  min-width: 40px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: var(--gray-50);
    border: none !important;
    box-shadow: none !important;
  }

  &:focus {
    border: none !important;
    box-shadow: none !important;
  }

  &:disabled {
    background-color: white;
    color: var(--gray-400);
    border: none !important;
  }
}

.inline-pagination {
  display: flex;
  margin: 0;

  :deep(.ant-pagination-prev) {
    margin: 2px 0;
  }

  :deep(.ant-pagination-next) {
    margin: 2px 0;
  }
  :deep(.ant-pagination-item) {
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    border: none !important;
    background: white;

    &:hover {
      background-color: var(--gray-50);
      border: none !important;
    }
  }

  :deep(.ant-pagination-item-active) {
    background-color: var(--primary-color);
    color: white;
    border: none !important;
  }

  :deep(.ant-pagination-jump-prev),
  :deep(.ant-pagination-jump-next) {
    color: var(--gray-600);
    border: none !important;
    background: white;

    &:hover {
      background-color: var(--gray-50);
      border: none !important;
    }
  }
}

.page-info {
  font-size: var(--text-sm);
  color: var(--gray-600);
  margin-left: var(--spacing-2);
}

.jump-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  margin-left: var(--spacing-4);
}

.jump-label {
  font-size: var(--text-sm);
  color: var(--gray-600);
}

.jump-input {
  width: 64px;
}

.size-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  margin-left: var(--spacing-4);
}

.size-label {
  white-space: nowrap;
  font-size: var(--text-sm);
  color: var(--gray-600);
}

.size-select {
  width: 80px;
}

// 统一字体颜色和对齐
:deep(.ant-input-number) {
  color: var(--gray-600);
}

:deep(.ant-select) {
  color: var(--gray-600);
  width: 100%;
}

:deep(.ant-select-selector) {
  color: var(--gray-600);
}

:deep(.ant-input) {
  width: 100%;
}
</style>
