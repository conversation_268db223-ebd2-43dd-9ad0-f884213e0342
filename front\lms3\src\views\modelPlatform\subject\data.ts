// 静态函数及变量定义
import { systemDataManager } from "@/utils/SystemDataManager.ts";

export const subjecttypeOptions =
  await systemDataManager.initOptions("examSubjectType");
export const levelOptions = await systemDataManager.initOptions("levelType");
export const onlyexamlOptions = [
  {
    value: 1,
    label: "仅用于考试"
  },
  {
    value: 2,
    label: "仅用于自测"
  },
  {
    value: 3,
    label: "考试和自测"
  }
];
export const itemOptions = [
  {
    value: "A",
    label: "A"
  },
  {
    value: "B",
    label: "B"
  },
  {
    value: "C",
    label: "C"
  },
  {
    value: "D",
    label: "D"
  }
];
// 表单验证规则
export const rules = {
  type: [{ required: true, message: "请选择题型" }],
  correctresponse: [{ required: true, message: "请维护参考答案" }],
  departmentidonlyexam: [{ required: true, message: "请选择使用访问" }],
  title: [{ required: true, message: "请输入标题" }]
};

// 表格列配置 - 展示所有渲染类型和高级配置
export const columns = [
  {
    title: "题目标题",
    dataIndex: "title",
    key: "title",
    width: 300
  },
  {
    title: "编号",
    dataIndex: "number",
    key: "number",
    align: "center",
    width: 120,
    sorter: true
  },
  {
    title: "题目类型",
    dataIndex: "type",
    key: "type",
    width: 100,
    align: "center",
    sorter: true,
    render: (current: string) => {
      return systemDataManager.translateDict(subjecttypeOptions, current);
    }
  },
  {
    title: "参考答案",
    dataIndex: "correctresponse",
    key: "correctresponse",
    renderType: "tag",
    tagColor: "blue",
    width: 80,
    align: "center",
    render: (current, record) => {
      return setAnswerLabel(current, record);
    }
  },
  {
    title: "使用范围",
    dataIndex: "onlyexam",
    key: "onlyexam",
    align: "center",
    width: 120,
    sorter: true,
    render: (current: string) => {
      return systemDataManager.translateDict(onlyexamlOptions, current);
    }
  },
  {
    title: "难易度",
    dataIndex: "levelid",
    key: "levelid",
    align: "center",
    width: 100,
    ellipsis: true,
    render: (current: string) => {
      return systemDataManager.translateDict(levelOptions, current);
    }
  },
  {
    title: "责任人",
    dataIndex: "principal",
    key: "principal",
    width: 100,
    align: "center"
  },
  {
    title: "责任单位",
    key: "principal",
    dataIndex: "dutyunit",
    width: 180
  }
];

export const queryItems = [
  {
    key: "PARAMETER_S_LIKE_title",
    label: "题目标题",
    component: "a-input",
    props: {
      placeholder: "请输入题目标题",
      allowClear: true
    }
  },
  {
    key: "PARAMETER_S_EQ_type",
    label: "题目类型",
    component: "a-select",
    props: {
      options: subjecttypeOptions,
      allowClear: true,
      placeholder: "请选择题目类型"
    }
  },
  {
    key: "PARAMETER_I_EQ_onlyexam",
    label: "使用范围",
    component: "a-select",
    props: {
      options: onlyexamlOptions,
      allowClear: true,
      placeholder: "请选择使用范围"
    }
  }
];

function setAnswerLabel(cellValue, row) {
  if (
    row.type === "23e162b9f27b4ebc9a5c93db09913693" ||
    row.type === "判断题"
  ) {
    if (cellValue === "1") {
      return "正确";
    } else {
      return "错误";
    }
  } else {
    return cellValue;
  }
}
