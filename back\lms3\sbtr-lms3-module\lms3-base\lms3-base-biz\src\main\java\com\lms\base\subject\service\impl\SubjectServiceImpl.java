package com.lms.base.subject.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.base.department.service.DepartmentService;
import com.lms.base.feign.model.Department;
import com.lms.base.feign.model.Subject;
import com.lms.base.feign.model.Person;
import com.lms.base.feign.model.Selectitem;
import com.lms.base.person.service.PersonService;
import com.lms.base.selectitem.service.SelectitemService;
import com.lms.base.subject.mapper.SubjectMapper;
import com.lms.base.subject.service.SubjectService;
import com.lms.common.model.LogType;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service("SubjectService")
public class SubjectServiceImpl extends BaseServiceImpl<SubjectMapper, Subject> implements SubjectService {

    @Resource
    private SubjectMapper subjectMapper;
    @Resource
    private SelectitemService selectitemService;
    @Resource
    private PersonService personService;
    @Resource
    private DepartmentService departmentService;
    @Resource
    private DBSqlUtil dbSqlUtil;
    @Value("${business.newCodePrefix.subject}")
    private String newCodePrefix;

    public List getSubjectAnalysisByType(String analysetype) {
        return this.subjectMapper.getSubjectAnalysisByType(analysetype);
    }


    public List<Subject> getPlacticeSubjectList(String ordertype, List<String> courseidList) {
        return this.subjectMapper.getPlacticeSubjectList(ordertype, courseidList);
    }

    private String r;
    private String w;

    public String checkImportData(Map map, int cklen) throws Exception {
        StringBuffer ckMsg = new StringBuffer();
        r = commonSystemApi.translateContent("正确");
        w = commonSystemApi.translateContent("错误");
        if (map.size() > 0) {
            for (int i = 0; i < map.size(); i++) {
                Map excelMap = (Map) map.get(i);
                String type = StringHelper.null2String(excelMap.get(0));
                String title = StringHelper.null2String(excelMap.get(1));
                String correctresponse = StringHelper.null2String(excelMap.get(6));
                String onlyexam = StringHelper.null2String(excelMap.get(7));
                String levelid = StringHelper.null2String(excelMap.get(8));
                String equipmentid = StringHelper.null2String(excelMap.get(9));
                String specialid = StringHelper.null2String(excelMap.get(10));
                String personlevel = StringHelper.null2String(excelMap.get(11));
                int rowNum = i + 3;
                if (title.isEmpty()) {
                    ckMsg.append(translateTips("标题", rowNum, 1));
//					ckMsg.append(commonSystemApi.translateContent("导入第([?])行数据出现错误，标题不能为空！", "", String.valueOf(rowNum))+"\r\n");
                }
                boolean validcorrectresponse = checkAnswer(correctresponse, type);
                if (correctresponse.isEmpty()) {
                    ckMsg.append(translateTips("参考答案", rowNum, 1));
                } else if (!validcorrectresponse) {
                    ckMsg.append(commonSystemApi.translateContent("导入第([?])行数据出现错误，判断题参考答案必须是‘[?]’或者‘[?]’！", "", new String[]{String.valueOf(rowNum), r, w}) + "\r\n");
                }
                boolean validtype = selectitemService.existName("", type,
                        "fc657149c8d84e79a5f3043c40d7baaa");
                if (type.isEmpty()) {
                    ckMsg.append(translateTips("题目类型", rowNum, 1));
                } else if (!validtype) {
                    ckMsg.append(translateTips("题目类型", rowNum, 2));
                }
                boolean validOnlyExam = isValidOnlyExam(onlyexam);
                if (onlyexam.isEmpty()) {
                    ckMsg.append(translateTips("使用范围", rowNum, 1));
                } else if (!validOnlyExam) {
                    ckMsg.append(translateTips("使用范围", rowNum, 2));
                }
                boolean validlevelid = selectitemService.existName("", levelid,
                        "10dcba700421446fb9dbd4bbf40ef5e6");
                if (!validlevelid && !levelid.isEmpty()) {
                    ckMsg.append(translateTips("难易度", rowNum, 2));
                }
                boolean validequipmentid = selectitemService.existName("",
                        equipmentid, "d89ac263bd614b93af0dd9a4a7faef25");
                if (!validequipmentid && !equipmentid.isEmpty()) {
                    ckMsg.append("导入第(" + rowNum + ")行数据出现错误，装备类型不存在！\r\n");
                }
                boolean validspecialid = selectitemService.existName("", specialid,
                        "bdce8a6593884df785dd26ec70c6f6ef");
                if (!validspecialid && !specialid.isEmpty()) {
                    ckMsg.append("导入第(" + rowNum + ")行数据出现错误，专业不存在！\r\n");
                }
                boolean validpersonlevel = selectitemService.existName("",
                        personlevel, "1c511c00199f4fc99da984bb17e31062");
                if (!validpersonlevel && !personlevel.isEmpty()) {
                    ckMsg.append("导入第(" + rowNum + ")行数据出现错误，人员层级不存在！\r\n");
                }
                if (cklen == 1) {
                    if (getLength(title) > 500) {
                        ckMsg.append(translateTips("标题长度", rowNum, 3));
                    }
                    if (getLength(correctresponse) > 500) {
                        ckMsg.append(translateTips("参考答案", rowNum, 3));
                    }
                }
            }
        } else {
            ckMsg.append(commonSystemApi.translateContent("导入失败，上传的数据为空!"));
        }
        return ckMsg.toString();
    }

    public String checkImportData(List<Subject> subjectList, int cklen) {
        StringBuffer ckMsg = new StringBuffer();
        r = commonSystemApi.translateContent("正确");
        w = commonSystemApi.translateContent("错误");
        if (CollectionUtils.isEmpty(subjectList)) {
            ckMsg.append(commonSystemApi.translateContent("导入失败，上传的数据为空!"));
            return ckMsg.toString();
        }
        List<Selectitem> mlevelList = selectitemService.getAllSelectitemsByType("047DA36C359F4FB5ABF276A7008438F4");
        Map<String, Selectitem> mlevelMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(mlevelList)) {
            mlevelMap = mlevelList.stream().collect(Collectors.toMap(Selectitem::getObjname, s -> s, (s1, s2) -> s1));
        }
        for (int i = 0; i < subjectList.size(); i++) {
            Subject subject = subjectList.get(i);
            String type = StringHelper.null2String(subject.getType());
            String title = StringHelper.null2String(subject.getTitle());
            String correctresponse = StringHelper.null2String(subject.getCorrectresponse());
            String onlyexamString = StringHelper.null2String(subject.getOnlyexamstring());
            String levelid = StringHelper.null2String(subject.getLevelid());
            String equipmentid = StringHelper.null2String(subject.getEquipmentid());
            String specialid = StringHelper.null2String(subject.getSpecialid());
            String personlevel = StringHelper.null2String(subject.getPersonlevelid());
            String mlevelForImport = StringHelper.null2String(subject.getMlevelForImport());
            int rowNum = i + 3;
            if (title.isEmpty()) {
                ckMsg.append(translateTips("标题", rowNum, 1));
//					ckMsg.append(commonSystemApi.translateContent("导入第([?])行数据出现错误，标题不能为空！", "", String.valueOf(rowNum))+"\r\n");
            }
            Selectitem selectitem = new Selectitem();
            selectitem.setCode("0");
            int mlevel = Integer.parseInt(Optional.ofNullable(mlevelMap.get(ObjectUtils.toString(subject.getMlevelForImport()))).orElse(selectitem).getCode());
            if (mlevelForImport.isEmpty()) {
                ckMsg.append(translateTips("密级", rowNum, 1));
            } else if (mlevel == 0) {
                ckMsg.append("导入第(" + rowNum + ")行数据出现错误，密级不存在！\r\n");
            }
            boolean validcorrectresponse = checkAnswer(correctresponse, type);
            if (correctresponse.isEmpty()) {
                ckMsg.append(translateTips("参考答案", rowNum, 1));
            } else if (!validcorrectresponse) {
                ckMsg.append(commonSystemApi.translateContent("导入第([?])行数据出现错误，判断题参考答案必须是‘[?]’或者‘[?]’！", "", new String[]{String.valueOf(rowNum), r, w}) + "\r\n");
            }
            boolean validtype = selectitemService.existName("", type,
                    "fc657149c8d84e79a5f3043c40d7baaa");
            if (type.isEmpty()) {
                ckMsg.append(translateTips("题目类型", rowNum, 1));
            } else if (!validtype) {
                ckMsg.append(translateTips("题目类型", rowNum, 2));
            }
            boolean validOnlyExam = isValidOnlyExam(onlyexamString);
            if (onlyexamString.isEmpty()) {
                ckMsg.append(translateTips("使用范围", rowNum, 1));
            } else if (!validOnlyExam) {
                ckMsg.append(translateTips("使用范围", rowNum, 2));
            }
            boolean validlevelid = selectitemService.existName("", levelid,
                    "10dcba700421446fb9dbd4bbf40ef5e6");
            if (!validlevelid && !levelid.isEmpty()) {
                ckMsg.append(translateTips("难易度", rowNum, 2));
            }
            boolean validequipmentid = selectitemService.existName("",
                    equipmentid, "d89ac263bd614b93af0dd9a4a7faef25");
            if (!validequipmentid && !equipmentid.isEmpty()) {
                ckMsg.append("导入第(" + rowNum + ")行数据出现错误，装备类型不存在！\r\n");
            }
            boolean validspecialid = selectitemService.existName("", specialid,
                    "bdce8a6593884df785dd26ec70c6f6ef");
            if (!validspecialid && !specialid.isEmpty()) {
                ckMsg.append("导入第(" + rowNum + ")行数据出现错误，专业不存在！\r\n");
            }
            boolean validpersonlevel = selectitemService.existName("",
                    personlevel, "1c511c00199f4fc99da984bb17e31062");
            if (!validpersonlevel && !personlevel.isEmpty()) {
                ckMsg.append("导入第(" + rowNum + ")行数据出现错误，人员层级不存在！\r\n");
            }
            if (cklen == 1) {
                if (getLength(title) > 500) {
                    ckMsg.append(translateTips("标题长度", rowNum, 3));
                }
                if (getLength(correctresponse) > 500) {
                    ckMsg.append(translateTips("参考答案", rowNum, 3));
                }
            }
        }
        return ckMsg.toString();
    }


    private String translateTips(String fieldname, int rowNum, int type) {
        String translateTips = "";
        if (type == 1) {
            translateTips = commonSystemApi.translateContent("导入第([?])行数据出现错误，'" + fieldname + "'不能为空！", "", String.valueOf(rowNum)) + "\r\n";
        } else if (type == 2) {
            translateTips = commonSystemApi.translateContent("导入第([?])行数据出现错误，'" + fieldname + "'不存在！", "", String.valueOf(rowNum)) + "\r\n";
        } else if (type == 3) {
            translateTips = commonSystemApi.translateContent("导入第([?])行数据出现错误，'" + fieldname + "'不能大于250！", "", String.valueOf(rowNum)) + "\r\n";
        }
        return translateTips;
    }

    private boolean checkAnswer(String correctresponse, String type) {
        if (type.equals(commonSystemApi.translateContent("判断题"))) { //判断题答案只能是正确或错误
            if (!correctresponse.equals(r) && !correctresponse.equals(w)) {
                return false;
            }
        }
        return true;
    }


    public int saveImportData(Map map, String courseId) throws Exception {
        r = commonSystemApi.translateContent("正确");
        w = commonSystemApi.translateContent("错误");
        int savenum = 0;
        for (int i = 0; i < map.size(); i++) {
            Map excelMap = (Map) map.get(i);
            Subject subject = new Subject();
            Selectitem ts = selectitemService.getSelectitemByTypeName(
                    StringHelper.null2String(excelMap.get(0)),
                    "fc657149c8d84e79a5f3043c40d7baaa");
            Selectitem ls = selectitemService.getSelectitemByTypeName(
                    StringHelper.null2String(excelMap.get(8)),
                    "10dcba700421446fb9dbd4bbf40ef5e6");
            Selectitem es = selectitemService.getSelectitemByTypeName(
                    StringHelper.null2String(excelMap.get(9)),
                    "d89ac263bd614b93af0dd9a4a7faef25");
            Selectitem ss = selectitemService.getSelectitemByTypeName(
                    StringHelper.null2String(excelMap.get(10)),
                    "bdce8a6593884df785dd26ec70c6f6ef");
            Selectitem cj = selectitemService.getSelectitemByTypeName(
                    StringHelper.null2String(excelMap.get(11)),
                    "1c511c00199f4fc99da984bb17e31062");
            //Component c = componentService.getComponetByName(StringHelper.null2String(excelMap.get(11)));
            subject.setType(StringHelper.null2String(ts.getId()));
            subject.setTitle(StringHelper.null2String(excelMap.get(1)));
            String A = StringHelper.null2String(excelMap.get(2));
            String B = StringHelper.null2String(excelMap.get(3));
            String C = StringHelper.null2String(excelMap.get(4));
            String D = StringHelper.null2String(excelMap.get(5));
            String content = setContext(subject, A, B, C, D);
            subject.setContenttext(content);
            subject.setContent(content);
            String correctresponse = StringHelper.null2String(excelMap.get(6));
            if (StringHelper.null2String(excelMap.get(0)).equals(commonSystemApi.translateContent("判断题"))) {
                if (correctresponse.equals(r))
                    correctresponse = "1";
                else if (correctresponse.equals(w))
                    correctresponse = "0";
            }
            subject.setCorrectresponse(correctresponse);
            subject.setOnlyexam(formatOnlyExam(StringHelper
                    .null2String(excelMap.get(7))));
            subject.setLevelid(StringHelper.null2String(ls.getId()));
            subject.setEquipmentid(StringHelper.null2String(es.getId()));
            subject.setSpecialid(StringHelper.null2String(ss.getId()));
            subject.setComponentid(courseId);
            subject.setCreatedate(DateHelper.getCurrentDate());
            subject.setPersonlevelid(cj.getId());
            Person person = personService.getById(ContextUtil.getPersonId());
            subject.setCreatorid(StringHelper.null2String(person.getId()));
            subject.setNumber(commonSystemApi.getNewCode(newCodePrefix));
            subject.setPrincipal(StringHelper.null2String(person.getName()));
            Department department = departmentService.getById(StringHelper.null2String(person.getDepartmentid()));
            subject.setDutyunit(StringHelper.null2String(department.getName()));
            this.saveOrUpdate(subject);
            savenum++;
        }
        return savenum;
    }


    public int saveImportData(List<Subject> subjectList, String courseId) {
        r = commonSystemApi.translateContent("正确");
        w = commonSystemApi.translateContent("错误");
        int savenum = 0;
        if (CollectionUtils.isEmpty(subjectList)) {
            return savenum;
        }
        List<Selectitem> mlevelList = selectitemService.getAllSelectitemsByType("047DA36C359F4FB5ABF276A7008438F4");
        Map<String, Selectitem> mlevelMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(mlevelList)) {
            mlevelMap = mlevelList.stream().collect(Collectors.toMap(Selectitem::getObjname, s -> s, (s1, s2) -> s1));
        }
        for (int i = 0; i < subjectList.size(); i++) {
            Subject subject = subjectList.get(i);
            Subject addSubject = new Subject();
            Selectitem ts = selectitemService.getSelectitemByTypeName(
                    StringHelper.null2String(subject.getType()),
                    "fc657149c8d84e79a5f3043c40d7baaa");
            Selectitem ls = selectitemService.getSelectitemByTypeName(
                    StringHelper.null2String(subject.getLevelid()),
                    "10dcba700421446fb9dbd4bbf40ef5e6");
            Selectitem es = selectitemService.getSelectitemByTypeName(
                    StringHelper.null2String(subject.getEquipmentid()),
                    "d89ac263bd614b93af0dd9a4a7faef25");
            Selectitem ss = selectitemService.getSelectitemByTypeName(
                    StringHelper.null2String(subject.getSpecialid()),
                    "bdce8a6593884df785dd26ec70c6f6ef");
            Selectitem cj = selectitemService.getSelectitemByTypeName(
                    StringHelper.null2String(subject.getPersonlevelid()),
                    "1c511c00199f4fc99da984bb17e31062");
            //Component c = componentService.getComponetByName(StringHelper.null2String(excelMap.get(11)));
            addSubject.setType(StringHelper.null2String(ts.getId()));
            addSubject.setTitle(StringHelper.null2String(subject.getTitle()));
            String A = StringHelper.null2String(subject.getItemA());
            String B = StringHelper.null2String(subject.getItemB());
            String C = StringHelper.null2String(subject.getItemC());
            String D = StringHelper.null2String(subject.getItemD());
            String content = setContext(addSubject, A, B, C, D);
            addSubject.setContenttext(content);
            addSubject.setContent(content);
            Selectitem selectitem = new Selectitem();
            selectitem.setCode("0");
            addSubject.setMlevel(Integer.parseInt(Optional.ofNullable(mlevelMap.get(ObjectUtils.toString(subject.getMlevelForImport()))).orElse(selectitem).getCode()));
            String correctresponse = StringHelper.null2String(subject.getCorrectresponse());
            if (StringHelper.null2String(subject.getType()).equals(commonSystemApi.translateContent("判断题"))) {
                if (correctresponse.equals(r))
                    correctresponse = "1";
                else if (correctresponse.equals(w))
                    correctresponse = "0";
            }
            addSubject.setCorrectresponse(correctresponse);
            addSubject.setOnlyexam(formatOnlyExam(StringHelper
                    .null2String(subject.getOnlyexamstring())));
            addSubject.setLevelid(StringHelper.null2String(ls.getId()));
            addSubject.setEquipmentid(StringHelper.null2String(es.getId()));
            addSubject.setSpecialid(StringHelper.null2String(ss.getId()));
            addSubject.setComponentid(courseId);
            addSubject.setCreatedate(DateHelper.getCurrentDate());
            addSubject.setPersonlevelid(cj.getId());
            Person person = personService.getById(ContextUtil.getPersonId());
            addSubject.setCreatorid(StringHelper.null2String(person.getId()));
            addSubject.setNumber(commonSystemApi.getNewCode(newCodePrefix));
            addSubject.setPrincipal(StringHelper.null2String(person.getName()));
            Department department = departmentService.getById(StringHelper.null2String(person.getDepartmentid()));
            addSubject.setDutyunit(StringHelper.null2String(department.getName()));
            this.saveOrUpdate(addSubject);
            savenum++;
        }
        return savenum;
    }

    private String setContext(Subject subject, String a, String b, String c,
                              String d) {
        StringBuffer content = new StringBuffer();
        content.append("<single>");
        if (a.isEmpty() && b.isEmpty() && c.isEmpty() && c.isEmpty())
            return "";
        if (!a.isEmpty())
            content.append("<item>" + a + "</item>");
        if (!b.isEmpty())
            content.append("<item>" + b + "</item>");
        if (!c.isEmpty())
            content.append("<item>" + c + "</item>");
        if (!d.isEmpty())
            content.append("<item>" + d + "</item>");
        content.append("</single>");

        return content.toString();
    }

    private int formatOnlyExam(String onlyexam) {
        if (onlyexam.equals(commonSystemApi.translateContent("仅用于考试"))) {
            return 1;
        } else if (onlyexam.equals(commonSystemApi.translateContent("仅用于自测"))) {
            return 2;
        } else {
            return 3;
        }
    }

    private boolean isValidOnlyExam(String onlyexam) {
        if (onlyexam.equals(commonSystemApi.translateContent("仅用于考试")) || onlyexam.equals(commonSystemApi.translateContent("仅用于自测"))
                || onlyexam.equals(commonSystemApi.translateContent("考试和自测"))) {
            return true;
        }
        return false;
    }

    private int getLength(String str) {
        if (str.isEmpty())
            return 0;
        char[] c = str.toCharArray();
        int len = 0;
        for (int i = 0; i < c.length; i++) {
            len++;
            if (!isLetter(c[i])) {
                len++;
            }
        }
        return len;
    }

    private static boolean isLetter(char c) {
        int k = 0x80;
        return c / k == 0 ? true : false;
    }

    public List<String> getRandomIds(Map conditonMap) throws Exception {
        int num = NumberHelper.string2Int(conditonMap.get("qty"), 0);
        String type = StringHelper.null2String(conditonMap
                .get("subjecttype"));
        int onlyexam = NumberHelper.string2Int(conditonMap.get("onlyexam"),
                3);
        String equipmentid = StringHelper.null2String(conditonMap
                .get("equipmentid"));
        String specialid = StringHelper.null2String(conditonMap
                .get("specialid"));
        List<String> courseids = BeanUtils.obj2List(conditonMap
                .get("courseids"), new ArrayList<>());
        String hql = "select id from (select id from u_subject where type= " + type + " and kekaodu='2c90d0af93bf117f0193cd68bc3e0004' and (onlyexam = 3 or onlyexam= " + onlyexam + ") ";
        if (!equipmentid.isEmpty())
            hql = hql + " and equipmentid like '%'|| " + equipmentid + " ||'%'";
        if (!specialid.isEmpty())
            hql = hql + " and specialid= " + specialid;
        if (courseids.size() > 0) {
            hql = hql + " and COMPONENTID in (" + conditonMap.get("courseids") + ") ";
        }
        hql = hql + " order by " + dbSqlUtil.getRandomFlag() + ") subject "
                + dbSqlUtil.getLEFlag() + num;
        return subjectMapper.getRandomIds(hql);
    }

    @Transactional
    public void calcSubjectKKD(List list) {
        List<String> ids = (List<String>) list.stream().map(o -> (Optional.ofNullable(((Map) o).get("sujectid").toString())).orElse("")).collect(Collectors.toList());
        List<Subject> subjectList = this.listIds(ids);
        String objname = "";
        for (Object c : list) {
            Map map = (Map) c;
            String sujectid = StringHelper.null2String(map.get("subjectid"));
            Subject subject = subjectList.stream().filter(s -> s.getId().equals(sujectid)).findFirst().get();
            String score = StringHelper.null2String(map.get("score"));
            String totalpoint = StringHelper.null2String(map.get("totalpoint"));
            String scorerate = NumberHelper.stringDivide(NumberHelper.stringMu(score, "100"), totalpoint) + "%";
            float rate = NumberHelper.string2Float(scorerate);
            String realLevel = "";
            if (rate < 20) {
                realLevel = "b7fb7d9044214e2a80e8aead9c7cbdb9";//困难
            } else if (rate >= 20 && rate < 50) {
                realLevel = "520bf544d5834f15ace57a1cbd44dab4";//一般
            } else if (rate >= 50) {
                realLevel = "6e4db8c879f7476c88cb41f572956e00";//简单
            }
            subject.setReallevel(realLevel);
            subject.setScorerate(scorerate);
            resetKKD(subject);
            objname = objname.isEmpty() ? StringHelper.null2String(subject.getTitle()) : objname + "," + StringHelper.null2String(subject.getTitle());
        }
        commonSystemApi.saveLog(objname, "更新试题可靠度", LogType.Update, "操作成功");
        this.listIds(subjectList);
    }

    public void resetKKD(Subject subject) {
        String levelid = StringHelper.null2String(subject.getLevelid());
        String realLevel = StringHelper.null2String(subject.getReallevel(), levelid);
        if (realLevel.equals(levelid)) {
            subject.setKekaodu("2c90d0af93bf117f0193cd68bc3e0004");//可靠
        } else {
            subject.setKekaodu("2c90d0af93bf117f0193cd68e68a0005");//不可靠
        }
    }

    public List<Subject> getSubjectByCourseIdList(List<String> courseIdList) {
        LambdaQueryWrapper<Subject> wrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isNotEmpty(courseIdList)) {
            wrapper.in(Subject::getComponentid, courseIdList);
            return this.list(wrapper);
        } else {
            return null;
        }
    }

    public void parseContent(Subject subject) {
        if ("9e47efc0ce894454856a80171e1e6efe".equals(subject.getType())// 单选题
                || "3ef580ec56ae436eb79b91b25d1a078e".equals(subject.getType())) {// 多选题
            String content = "<single>";
            content += "<item>" + subject.getItemA() + "</item>";
            content += "<item>" + subject.getItemB() + "</item>";
            content += "<item>" + subject.getItemC() + "</item>";
            content += "<item>" + subject.getItemD() + "</item>";
            content += "</single>";
            subject.setContent(content);
        }
    }

    @Override
    public void parseItems(Subject subject) {
        String content = StringHelper.null2String(subject.getContent());
        try {
            if (("9e47efc0ce894454856a80171e1e6efe".equals(subject.getType()) || "3ef580ec56ae436eb79b91b25d1a078e".equals(subject.getType())) && !content.isEmpty()) {// 多选题
                Document doc = DocumentHelper.parseText(content);
                Element root = doc.getRootElement();
                Element child;
                for (Iterator<Element> it = root.elementIterator("item"); it.hasNext();) {
                    child = it.next();
                    if(StringHelper.null2String(subject.getItemA()).isEmpty()){
                        subject.setItemA(child.getStringValue());
                    }else if(StringHelper.null2String(subject.getItemB()).isEmpty()){
                        subject.setItemB(child.getStringValue());
                    }else if(StringHelper.null2String(subject.getItemC()).isEmpty()){
                        subject.setItemC(child.getStringValue());
                    }else if(StringHelper.null2String(subject.getItemD()).isEmpty()){
                        subject.setItemD(child.getStringValue());
                    }
                }
            }
        } catch (Exception ex) {
        }
    }
}
