package com.lms.common.redis.client;

import javax.annotation.Resource;

import com.lms.common.redis.base.BaseMap;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

@Configuration
public class JeecgRedisClient {
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    public JeecgRedisClient() {
    }

    public void sendMessage(String handlerName, BaseMap params) {
        params.put("handlerName", handlerName);
        this.redisTemplate.convertAndSend("jeecg_redis_topic", params);
    }
}
