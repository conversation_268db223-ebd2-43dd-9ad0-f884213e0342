package com.lms.base.subject.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.base.feign.model.Subject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Mapper
public interface SubjectMapper extends BaseMapper<Subject> {

    @Select(value = "select id from u_subject where componentid = #{id}")
    List<String> exsitUseSubject(String id);


    @Select(value = "select #{analysetype} , count(id) as amount from u_subject  group by #{analysetype} order by #{analysetype}")
    List<Map<String, Object>> getSubjectAnalysisByType(String analysetype);

    default List<Subject> getPlacticeSubjectList(String ordertype, List<String> courseidList) {

        LambdaQueryWrapper<Subject> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Subject::getType, Arrays.asList("23e162b9f27b4ebc9a5c93db09913693", "9e47efc0ce894454856a80171e1e6efe", "3ef580ec56ae436eb79b91b25d1a078e"));
        if (courseidList.size() > 0) {
            wrapper.in(Subject::getComponentid, courseidList);
        }
        wrapper.orderByDesc(Subject::getType);
        List<Subject> results = this.selectList(wrapper);
        if (ordertype.equals("random")) {// 随机打乱顺序
            Collections.shuffle(results);
        }
        return results;
    }


    @Select("${sql}")
    List<String> getRandomIds(@Param("sql") String sql);
}
