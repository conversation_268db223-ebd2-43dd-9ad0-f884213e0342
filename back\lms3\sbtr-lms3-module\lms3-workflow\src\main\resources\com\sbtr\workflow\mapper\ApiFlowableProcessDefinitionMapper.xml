<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ApiFlowableProcessDefinitionMapper">

    <select id="getPageSetMySql" resultType="com.sbtr.workflow.vo.ProcessDefinitionVo">


		SELECT
		t2.ID_ as id,
		t1.NAME_ AS NAME,
		t1.KEY_ AS modelKey,
		t1.VERSION_ AS version,
		amc.category_name AS category,
		t1.ID_ as procdefId,
		t1.SUSPENSION_STATE_ as suspensionState,
		adm.description as description
		FROM
		act_re_procdef t1
		LEFT JOIN act_re_deployment t2 ON t1.DEPLOYMENT_ID_ = t2.ID_
		left JOIN act_de_model adm on adm.model_key=t1.KEY_
		left JOIN act_my_category amc on amc.category_code=t2.CATEGORY_
		 where
		<if test="category!='' and category!=null">
			t2.CATEGORY_ =#{category}  and
		</if>
		<if test="processModelType!='' and processModelType!=null">
			adm.process_model_type =#{processModelType}  and
		</if>
		<if test="name!='' and name!=null">
			adm.name = #{name} and
		</if>
		t1.major_version =#{majorVersion}
		order by adm.created desc
	<!--	SELECT
		a.`NAME`,
		a.modelKey,
		max( a.version ) as version,
		a.category
		FROM
		(
		SELECT
		t1.NAME_ AS NAME,
		t1.KEY_ AS modelKey,
		max( t1.VERSION_ ) AS version,
		t2.CATEGORY_ AS category
		FROM
		act_re_procdef t1
		LEFT JOIN act_re_deployment t2 ON t1.DEPLOYMENT_ID_ = t2.ID_
		WHERE
		<if test="category!='' and category!=null">
			t2.CATEGORY_ =#{category}  and
		</if>
		1 = 1
		GROUP BY
		t1.NAME_,
		t1.KEY_,
		t1.RESOURCE_NAME_,
		t1.DGRM_RESOURCE_NAME_,
		t1.SUSPENSION_STATE_,
		t1.DEPLOYMENT_ID_,
		t1.TENANT_ID_,
		t2.CATEGORY_,
		t1.VERSION_
		ORDER BY
		t2.DEPLOY_TIME_ DESC
		LIMIT 20
		) a
		where
		<if test="category!='' and category!=null">
			a.category =#{category}  and
		</if>
		1 = 1
		GROUP BY
		a.`NAME`,
		a.modelKey,
		a.category-->
    </select>

	<select id="getPageSetOracle" resultType="com.sbtr.workflow.vo.ProcessDefinitionVo">
		SELECT
		t2.ID_ as id,
		t1.NAME_ AS NAME,
		t1.KEY_ AS modelKey,
		t1.VERSION_ AS version,
		amc.category_name AS category,
		t1.ID_ as procdefId,
		t1.SUSPENSION_STATE_ as suspensionState,
		adm.description as description
		FROM
		act_re_procdef t1
		LEFT JOIN act_re_deployment t2 ON t1.DEPLOYMENT_ID_ = t2.ID_
		left JOIN act_de_model adm on adm.model_key=t1.KEY_
		left JOIN act_my_category amc on amc.category_code=t2.CATEGORY_
		where
		<if test="category!='' and category!=null">
			t2.CATEGORY_ =#{category}  and
		</if>
		<if test="processModelType!='' and processModelType!=null">
			adm.process_model_type =#{processModelType}  and
		</if>
		<if test="name!='' and name!=null">
			adm.name like '%'||#{name}||'%' and
		</if>
		t1.major_version ='是'
		order by adm.created desc
	</select>

	<select id="getListByIdAndModelKey" resultType="com.sbtr.workflow.vo.ProcessDefinitionVo">
		select
		ID_ as id,
		KEY_ as modelKey,
		DEPLOYMENT_ID_ as deploymentId
		 from act_re_procdef where KEY_ = #{modelKey} and major_version = #{majorVersion}
	</select>
	<select id="getCountSumByModelKey" resultType="java.lang.Integer">
		select count(1) from act_re_procdef where KEY_ = #{modelKey}
	</select>
	<select id="getProdefIdByDeployId" resultType="java.lang.String">
			select ID_ from act_re_procdef where DEPLOYMENT_ID_ = #{id}
	</select>
	<select id="getProdefIdById" resultType="com.sbtr.workflow.vo.ProcessDefinitionVo">
		select KEY_ as modelKey,NAME_ as name from act_re_procdef where ID_ = #{processDefinitionId}
	</select>
	<select id="getCustomInterface" resultType="com.sbtr.workflow.vo.ProcessDefinitionVo">
		SELECT
			t2.ID_ as id,
			t1.NAME_ AS NAME,
			t1.KEY_ AS modelKey,
			t1.VERSION_ AS version,
			t2.CATEGORY_ AS category,
			t1.ID_ as procdefId,
			t1.SUSPENSION_STATE_ as suspensionState,
			adm.description as description
		FROM
			act_re_procdef t1
				LEFT JOIN act_re_deployment t2 ON t1.DEPLOYMENT_ID_ = t2.ID_
				left JOIN act_de_model adm on adm.model_key=t1.KEY_
		where
			adm.process_model_type = 1 and
			t1.major_version ='是'
		order by adm.created desc
	</select>

	<update id="updateMajorVersion">
		update act_re_procdef set major_version = #{majorVersion} where ID_ = #{procdefId} and KEY_ = #{modelKey}
	</update>
	<update id="updateMajorVersionByModelKey">
update act_re_procdef set major_version =#{majorVersion} where  KEY_ = #{modelKey}
	</update>
</mapper>
