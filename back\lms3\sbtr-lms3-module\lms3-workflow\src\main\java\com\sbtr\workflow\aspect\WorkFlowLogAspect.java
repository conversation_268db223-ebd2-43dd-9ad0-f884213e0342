package com.sbtr.workflow.aspect;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.filter.SimplePropertyPreFilter;
import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessStatus;
import com.sbtr.workflow.service.BusinessSystemDataService;
import com.sbtr.workflow.vo.SecurityLogInfoVo;
import com.sbtr.workflow.vo.SysOperLogVo;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.MessageFormat;
import java.util.*;

/**
 * 操作日志记录处理
 *
 * <AUTHOR>
 * @date 20240627
 */
@Aspect
@Component
public class WorkFlowLogAspect {

    private static final Logger logger = LoggerFactory.getLogger(WorkFlowLogAspect.class);

    @Autowired
    private BusinessSystemDataService businessSystemDataService;

    /**
     * 排除敏感属性字段
     */
    public static final String[] EXCLUDE_PROPERTIES = {"password", "oldPassword", "newpassword", "confirmPassword"};

    /**
     * 配置织入点
     */
    @Pointcut("@annotation(com.sbtr.workflow.annotation.Log)")
    public void logPointCut() {
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "logPointCut()", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, Object jsonResult) {
//        handleLog(joinPoint, null, jsonResult);
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(value = "logPointCut()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Exception e) {
//        handleLog(joinPoint, e, null);
    }

    protected void handleLog(final JoinPoint joinPoint, final Exception e, Object jsonResult) {
        try {
            // 获得Log注解
            Log controllerLog = getAnnotationLog(joinPoint);
            if (controllerLog == null) {
                return;
            }
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();

            // 获取目标方法的所有参数
            Object[] args = joinPoint.getArgs();
            // *========数据库日志=========*//
            SysOperLogVo operLog = new SysOperLogVo();
            //TODO 程序成功但业务上的失败如何处理？
            operLog.setStatus(BusinessStatus.SUCCESS.ordinal());
            //请求的IP
            String ip = ServletUtil.getClientIP(request);
            operLog.setOperIp(ip);
            // 项目ID
            operLog.setProjectId(businessSystemDataService.getProjectId());
            //请求的路径
            operLog.setOperUrl(request.getRequestURI());
            //获取当前的用户
            String userName = businessSystemDataService.getUserName();
            if (StringUtils.isNotEmpty(userName)) {
                operLog.setOperName(userName);
            }
            Long userId = Long.parseLong(businessSystemDataService.getUserId());
            if (userId!=null) {
                operLog.setUserId(userId);
            }
            String userSecurity = businessSystemDataService.getUserSecurity();
            if (StringUtils.isNotEmpty(userSecurity)) {
                operLog.setUserSecurity(userSecurity);
            }
            //异常信息
            if (e != null) {
                operLog.setStatus(BusinessStatus.FAIL.ordinal());
                operLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 2000));
            }
            if (jsonResult != null) {
                // 返回参数
                operLog.setJsonResult(StringUtils.substring(JSON.toJSONString(jsonResult), 0, 2000));
            }
            // 设置方法名称
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = joinPoint.getSignature().getName();
            operLog.setMethod(className + "." + methodName + "()");
            // 设置请求方式
            operLog.setRequestMethod(request.getMethod());
            // 处理设置注解上的参数
            getControllerMethodDescription(controllerLog, operLog);
            //处理日志描述及日志主体
            if(StringUtils.isEmpty(controllerLog.method())) {
                getLogObject(controllerLog, operLog, args);
            }else{
                setLogObject(controllerLog,operLog,joinPoint);
            }
//            operLog.setAppId(appId);
            // 保存数据库
            businessSystemDataService.saveSysOperLog(JSON.toJSONString(operLog));
        } catch (Exception exp) {
            // 记录本地异常日志
            logger.error("==前置通知异常==");
            logger.error("异常信息:{}", exp);
        }
    }

    /**
     * 执行指定的代理对象的方法来获取日志主体
     * @param operLog 日志对象
     * @param joinPoint 切入点
     */
    private void setLogObject(Log controllerLog,SysOperLogVo operLog,JoinPoint joinPoint){
        Method method;
        try {
            method = joinPoint.getTarget().getClass().getMethod(controllerLog.method());
        } catch (NoSuchMethodException e) {
            method = null;
        }

        if(method != null){
            try {
                operLog.setTitle(controllerLog.title());
                Object object = method.invoke(joinPoint.getTarget());
                if(object instanceof SecurityLogInfoVo){
                    SecurityLogInfoVo li=(SecurityLogInfoVo)object;
                    operLog.setData(li.getData());
                    operLog.setDataSecurity(li.getDataSecurity());
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            } catch (InvocationTargetException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 处理日志主体
     */
    private void getLogObject(Log log, SysOperLogVo operLog, Object[] args) throws Exception {
        int[] position = log.order();
        if (position.length == 0) {
            operLog.setTitle(log.title());
        } else {
            List<Object> replaceArgs = new ArrayList<>(position.length);
            for (int index : position) {
                replaceArgs.add(args[index]);
            }
            operLog.setTitle(MessageFormat.format(log.title(), replaceArgs.toArray()));
        }
        // 是否需要保存request，参数和值
        if (log.isSaveRequestData()) {
            // 获取参数的信息，传入到数据库中。
            setRequestValue(operLog, args);
        }
    }

    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param log     日志
     * @param operLog 操作日志
     * @throws Exception
     */
    public void getControllerMethodDescription(Log log, SysOperLogVo operLog) {
        // 设置action动作
        operLog.setBusinessType(log.businessType().ordinal());
        // 设置模块
        operLog.setModule(log.module());
    }

    /**
     * 获取请求的参数，放到log中
     *
     * @param operLog 操作日志
     * @throws Exception 异常
     */
    private void setRequestValue(SysOperLogVo operLog, Object[] args) throws Exception {
        if (args != null && args.length > 0) {
            Map<String, Object> param = new HashMap<>();
            for (int i = 1; i <= args.length; i++) {
                param.put("参数" + i, args[i - 1]);
            }
            SimplePropertyPreFilter excludefilter = new SimplePropertyPreFilter();
            String params = JSONObject.toJSONString(param/*, excludefilter*/);//TODO 重构为fastjson2时未处理
            operLog.setOperParam(StringUtils.substring(params, 0, 2000));
        }
//        else {
//            Map<String, String[]> map = ServletUtils.getRequest().getParameterMap();
//            if (StringUtils.isNotEmpty(map)) {
//                PropertyPreFilters.MySimplePropertyPreFilter excludefilter = new PropertyPreFilters().addFilter();
//                excludefilter.addExcludes(EXCLUDE_PROPERTIES);
//                String params = JSONObject.toJSONString(map, excludefilter);
//                operLog.setOperParam(StringUtils.substring(params, 0, 2000));
//            }
//        }
    }

    /**
     * 是否存在注解，如果存在就获取
     */
    private Log getAnnotationLog(JoinPoint joinPoint) throws Exception {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        if (method != null) {
            return method.getAnnotation(Log.class);
        }
        return null;
    }
}
