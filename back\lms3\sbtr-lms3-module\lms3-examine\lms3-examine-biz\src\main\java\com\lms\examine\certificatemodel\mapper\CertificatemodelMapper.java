
package com.lms.examine.certificatemodel.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.examine.feign.model.Certificatemodel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface CertificatemodelMapper extends BaseMapper<Certificatemodel> {
	@Select(value = "select * from u_certificatemodel e where e.name = #{name} and if(#{id}!='',e.id<>#{id},1=1)")
	List getByName(@Param("name") String name, @Param("id") String id) ;


}
