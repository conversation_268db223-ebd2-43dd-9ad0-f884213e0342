package com.lms.system.storage.strategy;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.*;
import com.lms.common.enums.StorageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;

/**
 * 阿里云OSS存储策略实现
 */
@Slf4j
@Component("ossStorageStrategy")
public class OSSStorageStrategy extends AbstractFileStorageStrategy {

    @Autowired
    private OSS ossClient;

    @Value("${OSS.endpoint}")
    private String endpoint;

    @Value("${OSS.bucketName}")
    private String defaultBucket;

    @Override
    protected StorageTypeEnum getStorageTypeEnum() {
        return StorageTypeEnum.OSS;
    }

    @Override
    public String upload(MultipartFile file) throws Exception {
        return upload(defaultBucket, file);
    }

    @Override
    public String upload(String bucketName, MultipartFile file) throws Exception {
        validateFile(file);
        bucketName = safeBucketName(bucketName, defaultBucket);
        if (!bucketExists(bucketName)) {
            makeBucket(bucketName);
        }
        String objectName = generateUniqueFileName(file.getOriginalFilename());
        try (InputStream inputStream = file.getInputStream()) {
            ObjectMetadata meta = new ObjectMetadata();
            meta.setContentLength(file.getSize());
            meta.setContentType(file.getContentType());
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, inputStream, meta);
            ossClient.putObject(putObjectRequest);
            logOperation("文件上传", objectName, true, "上传到存储桶: " + bucketName);
            return objectName;
        } catch (Exception e) {
            logOperation("文件上传", objectName, false, e.getMessage());
            throw new RuntimeException("OSS文件上传失败", e);
        }
    }

    @Override
    /**
     * 生成唯一文件名
     * 格式：yyyy/MM/dd/HH/mm/ss/UUID.扩展名
     *
     * @param originalFilename 原始文件名
     * @return 唯一文件名
     */
    protected String generateUniqueFileName(String originalFilename) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(originalFilename)) {
            throw new IllegalArgumentException("原始文件名不能为空");
        }
        String extension = getFileExtension(originalFilename);
        String timestamp = java.time.LocalDateTime.now()
                .format(java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd/HH/mm/ss"));
        String uuid = java.util.UUID.randomUUID().toString();
        return "lms/" + timestamp + "/" + uuid + extension;
    }

    @Override
    public Map<String, String> uploadFile(MultipartFile file, String objectName) throws Exception {
        validateFile(file);
        if (StringUtils.isEmpty(objectName)) {
            objectName = generateUniqueFileName(file.getOriginalFilename());
        }
        try (InputStream inputStream = file.getInputStream()) {
            ObjectMetadata meta = new ObjectMetadata();
            meta.setContentLength(file.getSize());
            meta.setContentType(file.getContentType());
            PutObjectRequest putObjectRequest = new PutObjectRequest(defaultBucket, objectName, inputStream, meta);
            PutObjectResult putObjectResult = ossClient.putObject(putObjectRequest);
            String fileUrl = generateFileUrl(defaultBucket, objectName);
            Map<String, String> result = new HashMap<>();
            result.put("fileName", objectName);
            result.put("fileUrl", fileUrl);
            result.put("fileSize", String.valueOf(file.getSize()));
            result.put("contentType", file.getContentType());
            result.put("etag", putObjectResult.getETag());
            logOperation("文件上传", objectName, true, "返回详细信息");
            return result;
        } catch (Exception e) {
            logOperation("文件上传", objectName, false, e.getMessage());
            throw new RuntimeException("OSS文件上传失败", e);
        }
    }

    @Override
    public void uploadLocalFile(String fileLocation, String bucketName, String objectName) throws Exception {
        if (StringUtils.isEmpty(fileLocation) || StringUtils.isEmpty(objectName)) {
            throw new IllegalArgumentException("文件路径和对象名称不能为空");
        }
        bucketName = safeBucketName(bucketName, defaultBucket);
        try {
            ObjectMetadata meta = new ObjectMetadata();
            UploadFileRequest uploadFileRequest = new UploadFileRequest(bucketName, objectName);
            uploadFileRequest.setUploadFile(fileLocation);
            uploadFileRequest.setTaskNum(5);
            uploadFileRequest.setPartSize(1024 * 1024);
            uploadFileRequest.setEnableCheckpoint(true);
            uploadFileRequest.setCheckpointFile(fileLocation);
            uploadFileRequest.setObjectMetadata(meta);
            ossClient.uploadFile(uploadFileRequest);
            logOperation("本地文件上传", objectName, true, "从路径: " + fileLocation);
        } catch (OSSException oe) {
            logOperation("本地文件上传", objectName, false, "OSS异常: " + oe.getErrorMessage());
            throw new RuntimeException("OSS上传异常: " + oe.getErrorMessage(), oe);
        } catch (Throwable ce) {
            logOperation("本地文件上传", objectName, false, "客户端异常: " + ce.getMessage());
            throw new RuntimeException("客户端异常: " + ce.getMessage(), ce);
        }
    }

    @Override
    public void download(String fileName, HttpServletResponse response) throws Exception {
        download(defaultBucket, fileName, response);
    }

    @Override
    public void download(String bucketName, String fileName, HttpServletResponse response) throws Exception {
        if (StringUtils.isEmpty(fileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        bucketName = safeBucketName(bucketName, defaultBucket);
        OSSObject ossObject = null;
        try {
            ossObject = ossClient.getObject(bucketName, fileName);
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            try (InputStream inputStream = ossObject.getObjectContent();
                 OutputStream outputStream = response.getOutputStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }
            logOperation("文件下载", fileName, true, "从存储桶: " + bucketName);
        } catch (Exception e) {
            logOperation("文件下载", fileName, false, e.getMessage());
            throw new RuntimeException("OSS文件下载失败", e);
        } finally {
            if (ossObject != null) {
                try {
                    ossObject.close();
                } catch (IOException e) {
                    log.error("关闭OSSObject失败", e);
                }
            }
        }
    }

    @Override
    public InputStream download(String fileName) throws Exception {
        return download(defaultBucket, fileName);
    }

    @Override
    public InputStream download(String bucketName, String fileName) throws Exception {
        if (StringUtils.isEmpty(fileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        bucketName = safeBucketName(bucketName, defaultBucket);

        try {
            OSSObject ossObject = ossClient.getObject(bucketName, fileName);
            logOperation("文件流下载", fileName, true, "从存储桶: " + bucketName);
            return ossObject.getObjectContent();
        } catch (Exception e) {
            logOperation("文件流下载", fileName, false, e.getMessage());
            throw new RuntimeException("OSS文件下载失败", e);
        }
    }

    @Override
    public boolean checkFileIsExist(String fileName) {
        return checkFileIsExist(defaultBucket, fileName);
    }

    @Override
    public boolean checkFileIsExist(String bucketName, String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return false;
        }

        bucketName = safeBucketName(bucketName, defaultBucket);

        try {
            return ossClient.doesObjectExist(bucketName, fileName);
        } catch (Exception e) {
            logOperation("文件存在检查", fileName, false, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean deleteFile(String fileName) {
        return deleteFile(defaultBucket, fileName);
    }

    @Override
    public boolean deleteFile(String bucketName, String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return false;
        }

        bucketName = safeBucketName(bucketName, defaultBucket);

        try {
            ossClient.deleteObject(bucketName, fileName);
            logOperation("文件删除", fileName, true, "从存储桶: " + bucketName);
            return true;
        } catch (Exception e) {
            logOperation("文件删除", fileName, false, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean bucketExists(String bucketName) {
        if (StringUtils.isEmpty(bucketName)) {
            return false;
        }

        try {
            return ossClient.doesBucketExist(bucketName);
        } catch (Exception e) {
            log.error("检查存储桶是否存在失败: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public boolean makeBucket(String bucketName) {
        if (StringUtils.isEmpty(bucketName)) {
            return false;
        }

        try {
            if (!bucketExists(bucketName)) {
                ossClient.createBucket(bucketName);
                logOperation("存储桶创建", bucketName, true, "新建存储桶");
                return true;
            }
            return true;
        } catch (Exception e) {
            logOperation("存储桶创建", bucketName, false, e.getMessage());
            return false;
        }
    }

    @Override
    public String getPreviewUrl(String fileName) {
        return getPreviewUrl(defaultBucket, fileName);
    }

    @Override
    public String getPreviewUrl(String bucketName, String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return "";
        }

        bucketName = safeBucketName(bucketName, defaultBucket);
        return generateFileUrl(bucketName, fileName);
    }

    @Override
    public String generateFileUrl(String bucketName, String objectName) {
        if (StringUtils.isEmpty(bucketName) || StringUtils.isEmpty(objectName)) {
            return "";
        }

        try {
            return String.format("https://%s.%s/%s",
                    bucketName, endpoint.replace("https://", ""), objectName);
        } catch (Exception e) {
            log.error("生成文件URL出错: {}", e.getMessage());
            return String.format("https://%s.%s/%s",
                    bucketName, endpoint.replace("https://", ""), objectName);
        }
    }
}
