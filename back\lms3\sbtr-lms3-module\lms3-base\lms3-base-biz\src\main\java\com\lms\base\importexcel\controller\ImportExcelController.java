package com.lms.base.importexcel.controller;

import com.lms.base.feign.model.Subject;
import com.lms.base.importexcel.service.impl.ImportExcelServiceImpl;
import com.lms.common.controller.BaseController;
import com.lms.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping("/importExcel")
@Api(value = "excel导入管理", tags = "excel导入管理")
public class ImportExcelController{

    @Resource
    private ImportExcelServiceImpl importExcelService;


    /*以下是远程调用暴露接口*/
    @GetMapping("/readExcelFile")
    @ApiOperation(value = "读取excel", httpMethod = "GET")
    public Result<Map> readExcelFile(@ApiParam(value = "文件路径") @RequestParam String path){
        return Result.OK(importExcelService.readExcelFile(path));
    }

    @GetMapping("/readExcelFileWithIndex")
    @ApiOperation(value = "读取excel", httpMethod = "GET")
    public Result<Map> readExcelFile(@ApiParam(value = "文件路径") @RequestParam String path,
                                     @ApiParam(value = "从指定行开始读取") @RequestParam int index){
        return Result.OK(importExcelService.readExcelFile(path, index));
    }



}
