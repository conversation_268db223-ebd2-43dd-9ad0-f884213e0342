package com.lms.base.modelAttribute.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
@ApiModel(value = "模型属性VO")
public class ModelAttributeVo {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "属性名称")
    private String name;

    @ApiModelProperty(value = "属性值")
    private String value;

    @ApiModelProperty(value = "属性关系")
    private String relationship;

    @ApiModelProperty(value = "模型id")
    private String modelId;

    // 从TuGraph节点数据构造ModelAttributeVo对象
    public static ModelAttributeVo fromTuGraphNode(Map<String, Object> nodeData) {
        ModelAttributeVo model = new ModelAttributeVo();
        @SuppressWarnings("unchecked")
        Map<String, Object> properties = (Map<String, Object>) nodeData.get("properties");

        model.setId((String)properties.get("id"));
        model.setName((String) properties.get("name"));
        model.setValue((String) properties.get("value"));

        return model;
    }

}
