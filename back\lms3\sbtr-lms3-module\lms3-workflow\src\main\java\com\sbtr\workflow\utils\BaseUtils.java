package com.sbtr.workflow.utils;

import cn.ewsd.common.utils.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sbtr.workflow.filter.loginInfo.LoginInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

/**
 * 重写BaseUtils支持多租户
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
@Component
public class BaseUtils {

    @Autowired
    private HttpServletRequest request;

    private static Boolean saasEnabled;

    /**
     * 是否开启SaaS配置文件 true开启 false不开启，默认为false
     */
    @Value("${saas.enabled:false}")
    public void setSaasEnabled(Boolean saasEnabled) {
        BaseUtils.saasEnabled = saasEnabled;
    }


    public static String filterSort(HttpServletRequest request, String sqlStr) {
        return "1 = 1" + sassSqlStr() + filter(request) + sqlStr + sort(request);
    }

    public static String filter(HttpServletRequest request) {
        String var1 = "";
        String var2 = request.getParameter("filterRules");
        String var3 = request.getParameter("advanceFilter");
        String var4 = var2 + var3;
        if (var4.equals("nullnull")) {
            var4 = null;
        } else {
            var4 = var4.replace("null[", "[").replace("]null", "]").replace("][", "");
        }

        String var5 = null;
        String var6 = null;
        String var7 = null;
        String var8 = null;
        String var9 = null;
        String var10 = null;
        if (var4 != null) {
            JSONArray var11 = JSONArray.parseArray(var4);

            for (int var12 = 0; var12 < var11.size(); ++var12) {
                JSONObject var13 = (JSONObject) var11.get(var12);
                var5 = StringUtils.humpToUnderline(var13.getString("field"));
                if (!var5.equals("")) {
                    var6 = var13.getString("op").toString();
                    var7 = var13.getString("value");
                    if (var6.equals("contains")) {
                        var9 = " LIKE '%" + var7 + "%'";
                    } else if (var6.equals("equal")) {
                        var9 = " = '" + var7 + "'";
                    } else if (var6.equals("notequal")) {
                        var9 = " <> '" + var7 + "'";
                    } else if (var6.equals("beginwith")) {
                        var9 = " LIKE '" + var7 + "%'";
                    } else if (var6.equals("endwith")) {
                        var9 = " LIKE '%" + var7 + "'";
                    } else if (var6.equals("less")) {
                        var9 = " < '" + var7 + "'";
                    } else if (var6.equals("lessorequal")) {
                        var9 = " <= '" + var7 + "'";
                    } else if (var6.equals("greater")) {
                        var9 = " > '" + var7 + "'";
                    } else if (var6.equals("greaterorequal")) {
                        var9 = " >= '" + var7 + "'";
                    }

                    if (var12 == 0) {
                        var10 = " AND ";
                    } else {
                        JSONObject var14 = (JSONObject) var11.get(var12);
                        var8 = var14.getString("join");
                        if (var8 == null) {
                            var8 = "AND";
                        }

                        if (var8.equals("or")) {
                            var10 = " OR ";
                        } else {
                            var10 = " AND ";
                        }
                    }

                    String var16 = var13.getString("lb") == null ? "" : var13.getString("lb");
                    String var15 = var13.getString("rb") == null ? "" : var13.getString("rb");
                    var1 = var1 + var10 + var16 + var5 + var9 + var15;
                }
            }
        }

        return var1;
    }


    public static String sort(HttpServletRequest request) {
        String var1 = "";
        String var2 = request.getParameter("sort");
        String var3 = request.getParameter("order");
        String var4 = "";
        if (var2 != null) {
            String[] var5 = var2.split(",");
            String[] var6 = var3.split(",");

            for (int var7 = 0; var7 < var5.length; ++var7) {
                var4 = var4 + "," + StringUtils.humpToUnderline(var5[var7]) + " " + var6[var7];
            }

            var1 = var1 + " ORDER BY " + var4.substring(1);
        } else {
            var1 = " ORDER BY create_time DESC";
        }

        return var1;
    }

    public static String filterSort(HttpServletRequest request) {
        return "1 = 1" + sassSqlStr() + filter(request) + sort(request);
    }

    public static String UUIDGenerator() {
        UUID var0 = UUID.randomUUID();
        return var0.toString().replace("-", "").toUpperCase();
    }

    /**
     * 开启SaaS则返回拼接tenantUuid,否则返回""
     */
    public static String sassSqlStr() {
        //租户UUID
        String tenantUuid = LoginInfoVo.getTenantUuid();
        if (null != saasEnabled && saasEnabled) {
            return " and tenant_uuid= " + tenantUuid + " ";
        }
        return "";
    }


}
