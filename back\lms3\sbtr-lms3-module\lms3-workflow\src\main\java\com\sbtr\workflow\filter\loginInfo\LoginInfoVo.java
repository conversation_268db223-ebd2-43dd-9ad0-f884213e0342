package com.sbtr.workflow.filter.loginInfo;

/**
 * LoginInfo 支持多租户
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
public class LoginInfoVo {
    public static final ThreadLocal<UserInfoVo> userInfoTL = new ThreadLocal();

    public LoginInfoVo() {
    }

    public static void add(UserInfoVo userInfo) {
        userInfoTL.set(userInfo);
    }

    public static String getTenantUuid() {
        return ((UserInfoVo) userInfoTL.get()).getTenantUuid();
    }

}