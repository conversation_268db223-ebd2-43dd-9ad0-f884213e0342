package com.lms.base.coursewarelearnrecord.service;

import com.lms.base.feign.model.CoursewareLearnRecord;
import com.lms.common.service.BaseService;

import java.util.Map;

public interface CoursewareLearnRecordService extends BaseService<CoursewareLearnRecord> {

    CoursewareLearnRecord getCoursewareLearnRecord(Map<String, Object> map);

    int getMinCoursewareByCourse(Map<String, Object> map);
}
