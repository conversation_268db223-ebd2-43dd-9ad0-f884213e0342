package com.sbtr.workflow.client;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * @MethodName
 * @Description 调用sbtr-office相关接口
 * @Description 本地开发时可以指定 ip+网关端口进行访问  在configuration中配置 url = "http://*************:9774/office"
 * @Param null
 * @Return
 * <AUTHOR> @ sbtr.com>
 * @Date 2020-11-23 14:37
 */
//@FeignClient(name = FeignClientName.OFFICE_SERVER_NAME, configuration = FeignConfig.class)
public interface OfficeClient {


    /**
     * 借款申请业务状态修改
     *
     * @param uuid
     * @param state
     * @return Object
     */
    @PostMapping("/officeFinance/updateState")
    Object officeLoanApplicationuUpdateState(@RequestParam("uuid") String uuid, @RequestParam("state") String state);

    /**
     * 会议申请通过后插入相关数据
     *
     * @param uuid
     * @return Object
     */
    @PostMapping("/oaMeetingApplication/meetingInsertData")
    Object meetingInsertData(@RequestParam("uuid") String uuid);
}
