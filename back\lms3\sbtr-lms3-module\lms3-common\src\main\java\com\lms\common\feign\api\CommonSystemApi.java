package com.lms.common.feign.api;

import com.lms.common.feign.dto.Setting;
import com.lms.common.feign.fallback.CommonSystemApiFallbackFactory;
import com.lms.common.config.FeignConfig;
import com.lms.common.feign.dto.Log;
import com.lms.common.model.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: quanjinghua
 * @Date: 2024/7/9 19:10
 * @Description: 系统公共接口集合, 对比较常用的，全局性使用的接口，写入公共类，其他服务集成common类即可使用，
 * 无需再引用对应的服务接口，一般超过半数服务都会使用的接口，考虑纳入公共接口
 */
@FeignClient(contextId = "CommonSystemApi", value = "lms-system", url = "${feignClient.baseUrl.baseApi:}", configuration = FeignConfig.class, fallbackFactory = CommonSystemApiFallbackFactory.class)
public interface CommonSystemApi {

    /**
     * 操作日志
     *
     * @param objname
     * @return
     */
    @PostMapping("/log/saveByParam")
    Result saveLog(@RequestParam("objname") String objname, @RequestParam("desc") String desc, @RequestParam("logtype") String logtype, @RequestParam("result") String result);

    @PostMapping("/log/saveByLog")
    Result saveLog(@RequestBody Log log);

    /**
     * 多语言翻译
     *
     * @param keyword
     * @return
     */
    @GetMapping("/languagelabel/translatedContent")
    String translateContent(@RequestParam("keyword") String keyword, @RequestParam("module") String module, @RequestParam("variables") String variables);

    @GetMapping("/languagelabel/translatedContent2")
    String translateContent(@RequestParam("keyword") String keyword, @RequestParam("module") String module, @RequestParam("variables") String[] variables);

    @GetMapping("/languagelabel/translatedByKeyword")
    String translateContent(@RequestParam("keyword") String keyword);

    @GetMapping("/languagelabel/translateContentNotCreated")
    String translateContentNotCreated(@RequestParam("keyword") String keyword, @RequestParam("module") String module);

    /**
     * 系统设置
     *
     * @param
     * @return
     */
    @GetMapping(value = {"/setting/getSettingValueById"})
    Result<String> getSettingValueById(@RequestParam("id") String id);

    @GetMapping(value = {"/setting/getById"})
    Result<Setting> getById(@RequestParam("id") String id);

    /**
     * 业务流水编号
     *
     * @param
     * @return
     */
    @GetMapping(value = {"/seriescode/getNewCode"})
    String getNewCode(@RequestParam("key") String key);

}
