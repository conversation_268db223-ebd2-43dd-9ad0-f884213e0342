/**
 * FileName:	ClientExaminesubject.java
 * Author:		ji<PERSON><PERSON><PERSON>
 * Time:		2013-3-4上午11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.examine.examinesubject.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.DBSqlUtil;
import com.lms.examine.examinesubject.mapper.ExaminesubjectMapper;
import com.lms.examine.examinesubject.service.ExaminesubjectService;
import com.lms.examine.feign.model.Examinesubject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.List;


/**
 * 保存项目的相关信息
 */
@Service("ExaminesubjectService")
public class ExaminesubjectServiceImpl extends BaseServiceImpl<ExaminesubjectMapper, Examinesubject> implements ExaminesubjectService {

	@Resource
	private DBSqlUtil dbSqlUtil;
	@Resource
	private ExaminesubjectMapper examinesubjectMapper;

	public List<Examinesubject> getExaminesubjectById(String id, int titledisturb) throws SQLException {
		String sql = "select * from u_examinesubject es where es.examineid = '"+id+"' order by es.type,es.seqno";
		if (titledisturb == 1) {
			sql = "select * from u_examinesubject es where es.examineid = '"+id+"' order by es.type,"
					+ dbSqlUtil.getRandomFlag();
		}
		return examinesubjectMapper.getExaminesubjectById(sql);
	}


	public List<Examinesubject> getSubjectByExamineId(String examineid) {
		LambdaQueryWrapper<Examinesubject> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(Examinesubject::getExamineid, examineid);
		wrapper.orderByDesc(Examinesubject::getSeqno);
		return this.list(wrapper);
	}


	public List<Examinesubject> getExaminesubjectsByExamType(String examid,String subjecttype) {
		LambdaQueryWrapper<Examinesubject> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(Examinesubject::getExamineid, examid);
		wrapper.eq(Examinesubject::getType, subjecttype);
		wrapper.orderByDesc(Examinesubject::getType);
		return this.list(wrapper);
	}


	public List<Examinesubject> getExaminesubjectsBySubjectId(String examid,String subjectid) {
		LambdaQueryWrapper<Examinesubject> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(Examinesubject::getExamineid, examid);
		wrapper.eq(Examinesubject::getSubjectid, subjectid);
		wrapper.orderByDesc(Examinesubject::getType);
		return this.list(wrapper);
	}


	public List<Examinesubject> isExistBySubjectId(String subjectId) {
		LambdaQueryWrapper<Examinesubject> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(Examinesubject::getSubjectid, subjectId);
		return this.list(wrapper);
	}


	public List<Examinesubject> getValidExaminesubjects() {
		return this.listAll();
	}



	public int getCurrentSeqno(String Examinesubjectid) {
		return 0;
	}

}
