package com.lms.common.feign.dto;

import cn.hutool.core.util.ObjectUtil;
import com.lms.common.util.StringHelper;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

@Data
public class TreeNode {
    private String id;          // 节点ID
    private String pid;         // 父节点ID
    private String name;         // 节点名称
    private String courseFlag;
    private String courseType;
    private BigDecimal status;
    private List<TreeNode> children = new ArrayList<>(); // 子节点列表


    // 添加子节点
    public void addChild(TreeNode child) {
        this.children.add(child);
    }

    @Override
    public String toString() {
        return "TreeNode{" +
                "id='" + StringHelper.null2String(id) + '\'' +
                ", pid='" + StringHelper.null2String(pid) + '\'' +
                ", name='" + StringHelper.null2String(name) + '\'' +
                ", courseFlag='" + StringHelper.null2String(courseFlag) + '\'' +
                ", courseType='" + StringHelper.null2String(courseType) + '\'' +
                ", status=" + status +
                ", children=" + children +
                '}';
    }

    /**
     * 将平铺的数据组装成树形结构
     *
     * @param nodes 平铺的节点列表
     * @return 树形结构的根节点列表
     */
    public static List<TreeNode> buildTree(List<TreeNode> nodes, String rootid) {
        // 用于存储所有节点的Map，key为节点ID，value为节点对象
        Map<String, TreeNode> nodeMap = new HashMap<>();
        // 根节点列表
        List<TreeNode> rootNodes = new ArrayList<>();
        // 将所有节点存入Map
        for (TreeNode node : nodes) {
            nodeMap.put(node.getId(), node);
        }

        // 遍历所有节点，构建树形结构
        for (TreeNode node : nodes) {
            String pid = StringHelper.null2String(node.getPid());
            String id = StringHelper.null2String(node.getId());
            if (StringHelper.null2String(rootid).equals(id) || pid.isEmpty()) {
                // 如果pid为空值或者id等于rootid，说明是根节点
                rootNodes.add(node);
            } else {
                // 否则，找到父节点，并将当前节点添加到父节点的子节点列表中
                TreeNode parent = nodeMap.get(pid);
                if (ObjectUtil.isNotEmpty(parent)) {
                    parent.addChild(node);
                }
            }
        }

        return rootNodes;
    }
}
