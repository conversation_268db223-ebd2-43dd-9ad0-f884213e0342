package com.lms.examine.coursewareexamine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.examine.feign.model.Coursewareexamine;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface CoursewareexamineMapper extends BaseMapper<Coursewareexamine> {

	@Select(value = "select * from u_coursewareexamine where coursewareid = ?1 order by seqno")
    List<Coursewareexamine> getByCourseware(String coursewareid);

	@Select(value = "select ifnull(max(examsecond),0) from u_coursewareexamine es where es.coursewareid = #{#coursewareexamine.coursewareid}" +
			" and es.seqno < #{#coursewareexamine.seqno} " +
			"if(#{#coursewareexamine.id} != '' or #{#coursewareexamine.id} != null, " +
			" and es.id!= #{#coursewareexamine.id}, and 1=1)")
    int getMaxExamSecondByCourseware(Coursewareexamine coursewareexamine);

	@Select(value = "select seqno from u_coursewareexamine es where es.coursewareid = #{#coursewareexamine.coursewareid}" +
			" and es.seqno= #{#coursewareexamine.seqno} " +
			"if(#{#coursewareexamine.id} != '' or #{#coursewareexamine.id} != null, " +
			"and es.id!= #{#coursewareexamine.id}, and 1=1)")
    int existSeqno(Coursewareexamine coursewareexamine);

	@Select(value = "select ifnull(min(examsecond),99999999) from u_coursewareexamine es where es.coursewareid = #{#coursewareexamine.coursewareid}" +
			" and es.seqno > #{#coursewareexamine.seqno} " +
			"if(#{#coursewareexamine.id} != '' or #{#coursewareexamine.id} != null, " +
			" and es.id!= #{#coursewareexamine.id}, and 1=1)")
    int getMinExamSecondByCourseware(Coursewareexamine coursewareexamine);
}
