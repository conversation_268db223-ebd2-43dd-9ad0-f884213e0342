package com.sbtr.workflow.listener;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.common.engine.api.delegate.event.FlowableEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEventListener;
import org.flowable.common.engine.impl.event.FlowableEntityEventImpl;
import org.flowable.engine.TaskService;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Component
public class TaskCreateListener implements FlowableEventListener {

    private static final Logger log = LoggerFactory.getLogger(TaskCreateListener.class);

    @Autowired
    private  TaskService taskService;


    @Override
    public void onEvent(FlowableEvent flowableEvent) {
        TaskEntity taskEntity = (TaskEntity) ((FlowableEntityEventImpl) flowableEvent).getEntity();
        String taskId = taskEntity.getId();

        log.error(taskId);
        log.error(taskEntity.getName());

        List<IdentityLink> idList = taskService.getIdentityLinksForTask(taskId);
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<String> userNameList = new ArrayList<>();
        // 获取接收人，此处从Identity获取，实际情况会更复杂
        idList.forEach(identityLink -> {
            if (StringUtils.isNotBlank(identityLink.getUserId())) {
                userNameList.add(identityLink.getUserId());
            }
        });
        if (CollectionUtils.isNotEmpty(userNameList)) {
            // TODO: <AUTHOR> 发送提醒消息
        }
    }



    @Override
    public boolean isFailOnException() {
        return false;
    }

    @Override
    public boolean isFireOnTransactionLifecycleEvent() {
        return false;
    }

    @Override
    public String getOnTransaction() {
        return null;
    }
}
