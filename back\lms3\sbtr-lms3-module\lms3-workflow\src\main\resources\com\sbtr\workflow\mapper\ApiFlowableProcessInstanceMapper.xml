<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ApiFlowableProcessInstanceMapper">

    <select id="getProcessInstanceMySql" resultType="com.sbtr.workflow.vo.ProcessInstanceVo">
        SELECT
        t1.ID_ AS processInstanceId,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.NAME_ AS formName,
        t1.TENANT_ID_ AS systemSn,
        t1.BUSINESS_KEY_ AS businessKey,
        t1.START_TIME_ AS startTime,
        t1.END_TIME_ AS endTime,
        t1.DURATION_ AS duration,
        t1.START_USER_ID_ AS startUserId,
        t1.DELETE_REASON_ AS isdelete,
        arp.KEY_ as modelKey
        FROM
        act_hi_procinst t1
        LEFT JOIN act_re_procdef arp ON t1.PROC_DEF_ID_ = arp.ID_
        <where>
            <if test="modelKey != null and modelKey!=''">
                arp.KEY_ = #{modelKey}
            </if>
            <if test="businessKey != null and businessKey!=''">
                and t1.BUSINESS_KEY_ = #{businessKey}
            </if>
        </where>
        order by t1.START_TIME_ desc
    </select>

    <select id="getProcessInstanceMySqlByBusinessKeys" resultType="com.sbtr.workflow.vo.ProcessInstanceVo">
        SELECT
        t1.ID_ AS processInstanceId,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.NAME_ AS formName,
        t1.TENANT_ID_ AS systemSn,
        t1.BUSINESS_KEY_ AS businessKey,
        t1.START_TIME_ AS startTime,
        t1.END_TIME_ AS endTime,
        t1.DURATION_ AS duration,
        t1.START_USER_ID_ AS startUserId,
        t1.DELETE_REASON_ AS isdelete,
        arp.KEY_ as modelKey
        FROM
        act_hi_procinst t1
        LEFT JOIN act_re_procdef arp ON t1.PROC_DEF_ID_ = arp.ID_
        <where>
            <if test="modelKey != null and modelKey!=''">
                arp.KEY_ = #{modelKey}
            </if>
            AND t1.BUSINESS_KEY_ IN
            <foreach collection="businessKeys" index="index" item="businessKey" open="(" close=")" separator=",">
                #{businessKey}
            </foreach>
        </where>
        order by t1.START_TIME_ desc
    </select>


    <select id="getProcessInstanceOracle" resultType="com.sbtr.workflow.vo.ProcessInstanceVo">
        SELECT
        t1.ID_ AS processInstanceId,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.NAME_ AS formName,
        t1.TENANT_ID_ AS systemSn,
        t1.BUSINESS_KEY_ AS businessKey,
        t1.START_TIME_ AS startTime,
        t1.END_TIME_ AS endTime,
        t1.DURATION_ AS duration,
        f_get_username(t1.START_USER_ID_) AS startUserId,
        arp.KEY_ as modelKey
        FROM
        act_hi_procinst t1
        LEFT JOIN act_re_procdef arp ON t1.PROC_DEF_ID_ = arp.ID_
        <where>
            <if test="modelKey != null and modelKey!=''">
                arp.KEY_ = #{name}
            </if>
            <if test="businessKey != null and businessKey!=''">
                and t1.BUSINESS_KEY_ = #{businessKey}
            </if>
        </where>
        order by t1.START_TIME_ desc
    </select>


    <select id="getPageSetMySql" resultType="com.sbtr.workflow.vo.ProcessInstanceVo">
        SELECT
        t1.ID_ AS processInstanceId,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.NAME_ AS formName,
        t1.TENANT_ID_ AS systemSn,
        t1.BUSINESS_KEY_ AS businessKey,
        t1.START_TIME_ AS startTime,
        t1.END_TIME_ AS endTime,
        t1.DURATION_ AS duration,
        t1.START_USER_ID_ AS startUserId,
        arp.KEY_ as modelKey
        FROM
        act_hi_procinst t1
        LEFT JOIN act_re_procdef arp ON t1.PROC_DEF_ID_ = arp.ID_
        <where>
            <if test="name != null and name!=''">
                t1.NAME_ = #{name}
            </if>
        </where>
        order by t1.START_TIME_ desc
    </select>

    <select id="getPageSetOracle" resultType="com.sbtr.workflow.vo.ProcessInstanceVo">
        SELECT
        t1.ID_ AS processInstanceId,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.NAME_ AS formName,
        t1.TENANT_ID_ AS systemSn,
        t1.BUSINESS_KEY_ AS businessKey,
        t1.START_TIME_ AS startTime,
        t1.END_TIME_ AS endTime,
        t1.DURATION_ AS duration,
        f_get_username(t1.START_USER_ID_) AS startUserId,
        arp.KEY_ as modelKey
        FROM
        act_hi_procinst t1
        LEFT JOIN act_re_procdef arp ON t1.PROC_DEF_ID_ = arp.ID_
        <where>
            <if test="name != null and name!=''">
                t1.NAME_ like '%'||#{name}||'%'
            </if>
        </where>
        order by t1.START_TIME_ desc
    </select>

    <insert id="executeInsertSql">
        ${sql}
    </insert>


    <select id="getProcessFreeJumpData" resultType="com.sbtr.workflow.vo.TaskVo">
		SELECT
	 NAME_ AS taskName,
	TASK_DEF_KEY_  as nodeId
FROM
	act_ru_task art
WHERE
	PROC_INST_ID_ = #{processInstanceId}
	AND SUSPENSION_STATE_ = 1

	</select>

    <select id="getPageSetSqlServer" resultType="com.sbtr.workflow.vo.ProcessInstanceVo">
        SELECT
        t1.ID_ AS processInstanceId,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.NAME_ AS formName,
        t1.TENANT_ID_ AS systemSn,
        t1.BUSINESS_KEY_ AS businessKey,
        t1.START_TIME_ AS startTime,
        t1.END_TIME_ AS endTime,
        t1.DURATION_ AS duration,
        dbo.f_get_username(t1.START_USER_ID_) AS startUserId,
        arp.KEY_ as modelKey
        FROM
        act_hi_procinst t1
        LEFT JOIN act_re_procdef arp ON t1.PROC_DEF_ID_ = arp.ID_
        <where>
            <if test="name != null and name!=''">
                t1.NAME_ like concat('%',#{name},'%')
            </if>
        </where>
        order by t1.START_TIME_ desc
    </select>

    <select id="getRunProcessInstByParam" resultType="com.sbtr.workflow.vo.ProcessInstanceVo">
		SELECT
	 id_ AS processInstanceId,
	PROC_DEF_ID_  as processDefinitionId,
	NAME_ as formName,
	BUSINESS_KEY_  as businessKey,
	start_time_  as startTime
	FROM
	ACT_RU_EXECUTION exe
    WHERE 1=1
		<if test="businessKey != null and businessKey!=''">
			and exe.BUSINESS_KEY_ = #{businessKey}
		</if>
	order by start_time_ desc
	</select>
</mapper>
