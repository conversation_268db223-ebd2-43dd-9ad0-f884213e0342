<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sbtr.workflow.mapper.MyVariableMapper" >

  <sql id="Base_Column_List" >
    *
  </sql>
    <update id="updateAssigneeAndTaskname">
      update ${tableName}
      set assignee=#{assignee},
      taskname=#{name}
       where uuid=#{businessId}
    </update>
  <update id="updateAssigneeAndTasknameAndExecutionid">
      update ${tableName}
      set assignee=#{assignee},
      taskname=#{name},
      executionid=#{executionId}
       where uuid=#{businessId}
    </update>
  <update id="updateBytes">
    update act_ge_bytearray_temporaryNode set BYTES_=#{bytes} where ID_='aa44d22a-2dc7-11ea-a6a8-0036761c88c4'
  </update>
  <update id="updateAssigneeById">
    update act_hi_taskinst set ASSIGNEE_=#{userNameId} where id_=#{id}
  </update>
  <update id="updateAssignee">
        update act_ru_task set ASSIGNEE_=#{assignee} where id_=#{id}
  </update>
  <select id="getPageSet" resultType="com.sbtr.workflow.model.MyVariable">
    select
    <include refid="Base_Column_List"></include>
    from act_my_variable
    <where>
      <if test="filterSort != null">
        ${filterSort}
      </if>
    </where>
  </select>

  <select id="getVariable" resultType="com.sbtr.workflow.model.MyVariable">
    select * from act_my_variable WHERE process_key = #{param1} AND variable_name = #{param2}
  </select>

    <select id="getListByProcessKey" resultType="com.sbtr.workflow.model.MyVariable">
        select
        <include refid="Base_Column_List"></include>
        from act_my_variable
        WHERE
        process_key =#{processkey}
    </select>


    <select id="getVariables" resultType="com.sbtr.workflow.model.MyVariable">
    select * from act_my_variable WHERE process_key = #{param1}
  </select>
    <select id="getAllActGeBytearray" resultType="java.util.HashMap">
      SELECT * FROM act_ge_bytearray_temporaryNode
    </select>



    <select id="getListByUserNamdIdOrUserName" resultType="com.sbtr.workflow.model.User">
      SELECT PERSONNAME user_name,USERNAME user_name_id  FROM users where PERSONNAME like '%'||#{filter}||'%' or USERNAME like '%'||#{filter}||'%'
    </select>
  <select id="getListByText" resultType="com.sbtr.workflow.model.Organization">
          SELECT text FROM emis_system.sys_organization where text like concat('%',#{filter},'%')
  </select>
    <select id="getModelsByModelType" resultType="org.flowable.ui.modeler.domain.AbstractModel">
       select id,NAME,DESCRIPTION,MODEL_KEY as 'key',version from ACT_DE_MODEL
      <where>
        model_type =#{modelType} and
        <if test="filterSort != null">
          ${filterSort}
        </if>
      </where>
       order by name asc
    </select>
  <select id="getListByExecutionId" resultType="com.sbtr.workflow.model.TaskCommon">
    SELECT ASSIGNEE_ as assignee,ID_ as id from act_hi_taskinst where PROC_INST_ID_=#{executionId} and START_TIME_=(select max(START_TIME_) as START_TIME_ from act_hi_taskinst where PROC_INST_ID_=#{executionId} )
  </select>
  <select id="getAssings" resultType="java.util.HashMap">
    SELECT
	id_ AS id,
	ASSIGNEE_ AS assignee,
	PROC_INST_ID_ as procinstid
FROM
	act_hi_taskinst
WHERE
	EXECUTION_ID_ =#{executionId}
	AND START_TIME_ =(
	SELECT
		MIN( START_TIME_ )
	FROM
		act_hi_taskinst
	WHERE
	EXECUTION_ID_ = #{executionId}
	)
  </select>
  <select id="getActRuTaskByProcInstId" resultType="java.util.Map">
        SELECT
            *
        FROM
            act_ru_task
        WHERE
            PROC_INST_ID_ = #{procInstId}
            AND opening_time IS NULL;
    </select>
  <select id="getDetailsByKey" resultType="com.sbtr.workflow.model.TaskCommon">
       SELECT
	PROC_DEF_ID_ AS processDefinitionId,
	ID_ AS taskId,
	EXECUTION_ID_ AS executionId,
	NAME_ AS NAME,
	CREATE_TIME_ AS createTime,
	opening_time AS openingTime
FROM
	act_ru_task where ID_ =#{id}
    </select>

  <update id="updateTaskCommonOpeningTimeByKey">
         update act_ru_task set opening_time=#{date} where id_=#{id}
    </update>

  <update id="updateActHiActinst">
         update act_hi_actinst set opening_time=#{date} where TASK_ID_=#{taskId}
    </update>

    <delete id="delectActRuTaskByKey">
     delete from act_ru_task where ID_ = #{id}
    </delete>

    <select id="getActHiActinstByProcInstId" resultType="java.util.Map">
    SELECT
            *
        FROM
            ACT_HI_ACTINST
        WHERE
            PROC_INST_ID_ = #{procInstId}
            AND ACT_TYPE_ =#{actType}
            and opening_time IS not NULL

        ORDER BY
	END_TIME_ desc limit 2
    </select>
    <resultMap id="TaskCommonResultMap" type="com.sbtr.workflow.model.TaskCommon">
        <result property="name" column="name"></result>
        <result property="text" column="text"></result>
        <result property="processInstanceId" column="processInstanceId"></result>
        <collection property="actRuVariables"
                    ofType="com.sbtr.workflow.model.ActRuVariable"
                    column="{processInstanceId=processInstanceId}"
                    select="getActRuVariables"></collection>
    </resultMap>

    <select id="getActRuVariables" resultType="com.sbtr.workflow.model.ActRuVariable">
    select NAME_ AS  `name`,TEXT_ as text from ACT_RU_VARIABLE where EXECUTION_ID_ = #{processInstanceId} and TASK_ID_ is null
</select>

    <select id="taskQuery" resultMap="TaskCommonResultMap">


        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ as name,
        t1.NAME_ as taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t1.CREATE_TIME_ as createTime,
        t2.START_TIME_ as flowStartTime,
        t2.START_TIME_ AS processStartTime,
        t1.opening_time as openingTime,
        t2.ID_ as instanceId,
        t4.KEY_ AS modelKey,
        t2.START_USER_ID_ as startUserId,
        t4.NAME_ AS modelName,
        t4.NAME_ AS `processname`,
        t1.TASK_DEF_KEY_ as nodeId,
        t1.PRIORITY_ as priority,t1.PROC_DEF_ID_ as processDefinitionId,t1.EXECUTION_ID_ as executionId
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        left join act_re_procdef t4 on t4.ID_= t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        left JOIN act_de_model adm on adm.model_key=t4.KEY_
        WHERE
        <if test="processDefinitionName!='' and processDefinitionName!=null">
            t4.NAME_ like concat('%',#{processDefinitionName},'%') and
        </if>
        <if test="processModelType!='' and processModelType!=null">
            adm.process_model_type like concat('%',#{processModelType},'%') and
        </if>
        t2.BUSINESS_KEY_ IS NOT NULL
        AND (
        t1.ASSIGNEE_ = #{assignee}
        OR (
        t1.ASSIGNEE_ IN (
        SELECT g.user_name_id FROM sys_user g WHERE g.user_name_id = #{assignee}
        )
        )
        OR (
        (
        coalesce(t1.ASSIGNEE_ ,N'') = ''
        )
        AND (
        t3.USER_ID_ = #{assignee}
        OR find_in_set (
        (SELECT g.user_group FROM sys_user g WHERE g.user_name_id = #{assignee} ),t3.GROUP_ID_
        )
        )
        )
        ) ORDER BY CREATE_TIME_ DESC

   <!--     SELECT DISTINCT
        p.NAME_ AS `processname`,
        t2.START_TIME_ AS processStartTime,
        t2.BUSINESS_KEY_ as businessKey,
        t2.ID_ as instanceId,
        t1.PROC_DEF_ID_ as processDefinitionId,
        t1.ID_ as taskId,
        t1.EXECUTION_ID_ as executionId,
        t1.NAME_ as name,
        t1.NAME_ as taskName,
        t1.CREATE_TIME_ as createTime,
        t1.opening_time as openingTime,
        t1.TASK_DEF_KEY_ as nodeId,
        p.KEY_ AS modelKey,
        t2.PROC_INST_ID_ AS processInstanceId
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        INNER JOIN ACT_RE_PROCDEF P ON t2.PROC_DEF_ID_ = P.ID_
        WHERE
        t2.BUSINESS_KEY_ IS NOT NULL
        <if test="processDefinitionName !=null and processDefinitionName !='' ">
            and P.NAME_=#{processDefinitionName}
        </if>
        AND (
        t1.ASSIGNEE_ = #{assignee}
        OR (
        t1.ASSIGNEE_ IN (
        SELECT
        G.group_id_
        FROM
        act_id_membership G
        WHERE
        G.user_id_ = #{assignee}
        )
        )
        OR (
        (
        t1.ASSIGNEE_ IS NULL
        OR t1.ASSIGNEE_ = ''
        )
        AND (
        t3.USER_ID_ = #{assignee}
        OR t3.GROUP_ID_ IN (
        SELECT
        g.group_id_
        FROM
        act_id_membership g
        WHERE
        g.user_id_ =#{assignee}
        )
        )
        )
        )
        order by t1.CREATE_TIME_ desc-->
    </select>

    <insert id="addActRuTask" parameterType="java.util.HashMap">
        insert into ACT_RU_TASK
                (
                    `ID_`,
                    `REV_`,
                    `EXECUTION_ID_`,
                    `PROC_INST_ID_`,
                    `PROC_DEF_ID_`,
                    `NAME_`,
                    `PARENT_TASK_ID_`,
                    `ASSIGNEE_`,
                    `CREATE_TIME_`,
                    `SUSPENSION_STATE_`,
                    `IS_COUNT_ENABLED_`,
                    `VAR_COUNT_`,
                    `ID_LINK_COUNT_`,
                    `SUB_TASK_COUNT_`

                )
                values
                (
                 #{map.ID_},
                 #{map.REV_},
                 #{map.EXECUTION_ID_},
                 #{map.PROC_INST_ID_},
                 #{map.PROC_DEF_ID_},
                 #{map.NAME_},
                 #{map.PARENT_TASK_ID_},
                 #{map.ASSIGNEE_},
                 #{map.CREATE_TIME_},
                 #{map.SUSPENSION_STATE_},
                 #{map.IS_COUNT_ENABLED_},
                 #{map.VAR_COUNT_},
                 #{map.ID_LINK_COUNT_},
                 #{map.SUB_TASK_COUNT_}
                 )
    </insert>

    <select id="gettoDoWithPageSetData" resultType="java.util.Map">
      <!--  SELECT
        t.*
        FROM
        act_hi_taskinst t
        WHERE
        <if test="processDefinitionName!=null and ''!=processDefinitionName">
            t.NAME_ =#{processDefinitionName}     and
        </if>
        t.ASSIGNEE_ = #{userNameId} and END_TIME_ IS not NULL-->
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t3.NAME_ AS formName,
        t3.BUSINESS_KEY_ AS businessKey,
        t3.PROC_INST_ID_ AS processInstanceId,
        t3.TENANT_ID_ AS systemSn,
        t1.START_TIME_ AS startTime,
        t1.END_TIME_ AS endTime,
        t5.KEY_ AS modelKey,t3.PROC_DEF_ID_ as processDefinitionId
        FROM
        act_hi_taskinst t1
        JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        LEFT JOIN act_re_procdef t5 ON t3.PROC_DEF_ID_ = t5.ID_
        WHERE
        <if test="processDefinitionName!=null and ''!=processDefinitionName">
            t.NAME_ =#{processDefinitionName}     and
        </if>
        t1.END_TIME_ IS NOT NULL
        AND t1.ASSIGNEE_ = #{userNameId}

    </select>
    <select id="getTitle" resultType="java.util.Map">
select * from act_hi_varinst where proc_inst_id_ =#{userNameId}
    </select>

    <select id="getAllByProcInstIdByActType" resultType="com.sbtr.workflow.model.ActHiActinst">
SELECT
	ID_ as id,
	REV_ as rev,
	PROC_DEF_ID_ as procDefId,
	PROC_INST_ID_ as procInstId,
	EXECUTION_ID_ as executionId,
	ACT_ID_ as actId,
	TASK_ID_ as taskId,
	CALL_PROC_INST_ID_ as callProcInstId,
	ACT_NAME_ as actName,
	ACT_TYPE_ as actType,
	ASSIGNEE_ as assignee,
	START_TIME_ as seartTime,
	END_TIME_ as endTime,
	DURATION_ as duration,
	DELETE_REASON_ as deleteReason,
	TENANT_ID_ as tenantId,
	opening_time
FROM
	ACT_HI_ACTINST
WHERE
	PROC_INST_ID_ = #{procInstId}
	AND ACT_TYPE_ =#{actType}
ORDER BY
	END_TIME_ ASC
    </select>

    <select id="getListBydisActivityId" resultType="java.util.Map">
SELECT
	*
FROM
	act_hi_actinst t
WHERE
	t.PROC_INST_ID_ = #{processInstanceId}
	AND t.ACT_ID_ = #{disActivityId}
ORDER BY
	t.END_TIME_ ASC
    </select>
    <select id="getListBydisEndTime" resultType="java.util.Map">
        SELECT
        t.*
        FROM
        act_hi_taskinst t
        WHERE
        t.PROC_INST_ID_ = #{processInstanceId} and (t.END_TIME_ >= #{endTime} or t.END_TIME_ is null)
    </select>

    <delete id="deleteRunActinstsByIds">
        delete from act_ru_task where ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <delete id="deleteHisActinstsByIds">
        delete from act_hi_actinst where TASK_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>



    <select id="getMyNoEndProcessPageSetData" resultMap="TaskCommonResultMasp">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        t2.START_USER_ID_ AS startUserId,
        t4.NAME_ AS modelName,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
        t1.ASSIGNEE_ as assignee,
        t4.KEY_ AS modelKey,
        t1.TASK_DEF_KEY_ as nodeId
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        WHERE
        <if test="formName!='' and formName!=null">
            t2.NAME_ like concat('%',#{formName},'%') and
        </if>
        <if test="modelName!='' and modelName!=null">
            t4.NAME_ like concat('%',#{modelName},'%') and
        </if>
        t2.START_USER_ID_ =#{userNameId}
        AND t2.BUSINESS_KEY_ IS NOT NULL
        order by t1.CREATE_TIME_ desc
    </select>

    <resultMap id="TaskCommonResultMasp" type="com.sbtr.workflow.dto.ProcessInstanceDto">
        <result property="name" column="name"></result>
        <result property="text" column="text"></result>
        <result property="processInstanceId" column="processInstanceId"></result>
        <collection property="actRuVariables"
                    ofType="com.sbtr.workflow.model.ActRuVariable"
                    column="{processInstanceId=processInstanceId}"
                    select="getActRuVariabless"></collection>
    </resultMap>

    <select id="getActRuVariabless" resultType="com.sbtr.workflow.model.ActRuVariable">
    select NAME_ AS  `name`,TEXT_ as text from ACT_RU_VARIABLE where EXECUTION_ID_ = #{processInstanceId} and TASK_ID_ is null
</select>


    <resultMap id="TaskCommonResultMasps" type="com.sbtr.workflow.vo.TaskVo">
        <result property="name" column="name"></result>
        <result property="text" column="text"></result>
        <result property="processInstanceId" column="processInstanceId"></result>
        <collection property="actRuVariables"
                    ofType="com.sbtr.workflow.model.ActRuVariable"
                    column="{processInstanceId=processInstanceId}"
                    select="getActRuVariablessa"></collection>
    </resultMap>

    <select id="getActRuVariablessa" resultType="com.sbtr.workflow.model.ActRuVariable">
    select NAME_ AS  `name`,TEXT_ as text from act_hi_varinst where EXECUTION_ID_ = #{processInstanceId} and TASK_ID_ is null
</select>


    <select id="getApplyedTasks" resultMap="TaskCommonResultMasps">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t3.NAME_ AS formName,
        t3.BUSINESS_KEY_ AS businessKey,
        t3.PROC_INST_ID_ AS processInstanceId,
        t3.TENANT_ID_ AS systemSn,
        t1.START_TIME_ AS startTime,
        t1.END_TIME_ AS endTime,
        adm.name as modelName,
        t5.KEY_ AS modelKey,t3.PROC_DEF_ID_ as processDefinitionId
        FROM
        act_hi_taskinst t1
        JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        LEFT JOIN act_re_procdef t5 ON t3.PROC_DEF_ID_ = t5.ID_
        left JOIN act_de_model adm on adm.model_key=t5.KEY_

        WHERE
        <if test="processModelType!=null and processModelType!=''">
            adm.process_model_type like concat('%',#{processModelType},'%') and
        </if>
        <if test="processDefinitionName!=null and processDefinitionName!=''">
            adm.name like concat('%',#{processDefinitionName},'%') and
        </if>
        t1.END_TIME_ IS NOT NULL
        AND t1.ASSIGNEE_ = #{userNameId}
        order by  t1.START_TIME_ desc
    </select>
    <select id="getUserNameByUserNameId" resultType="java.lang.String">
        SELECT
            user_name
        FROM sys_user
        WHERE user_name_id = #{userNameId}
    </select>

</mapper>
