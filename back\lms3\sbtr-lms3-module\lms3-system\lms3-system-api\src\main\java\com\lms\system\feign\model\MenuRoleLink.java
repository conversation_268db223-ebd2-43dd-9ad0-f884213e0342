package com.lms.system.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import lombok.Data;

/**
 * PMenurolelinkId entity. <AUTHOR> Persistence Tools
 */

@TableName("p_menurolelink")
@Data
public class MenuRoleLink extends BaseModel {

	@TableId(type= IdType.ASSIGN_ID)
	private String id;
	private String menuid;
	private String roleid;
	@TableField(exist = false)
	private String url;
}
