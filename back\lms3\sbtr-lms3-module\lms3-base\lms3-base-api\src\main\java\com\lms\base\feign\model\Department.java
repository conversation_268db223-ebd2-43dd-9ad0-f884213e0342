package com.lms.base.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import com.lms.common.model.SearchContent;
import com.lms.common.model.SearchName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Department entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)

@TableName("p_department")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "department")
public class Department extends BaseModel {

	@TableId(type= IdType.ASSIGN_ID)
	@ApiModelProperty("主键id")
	private String id;

	@ApiModelProperty("编码")
	@SearchName
	private String code;

	@ApiModelProperty("部门名称")
	@SearchContent
	private String name;

	@ApiModelProperty("简短名称")
	@SearchContent
	private String shortname;

	@ApiModelProperty("序号")
	private Integer seqno;

	@ApiModelProperty("状态")
	private Integer status;

	@ApiModelProperty("父节点id")
	private String pid;

	@TableField(exist = false)
	@ApiModelProperty("父节点名称")
	private String parentname;

	@ApiModelProperty("组织全路径")
	private String fullpath;

	@ApiModelProperty("组织名称全路径")
	@SearchContent
	private String fullpathname;

	@TableField("ORGFLAG")
	@ApiModelProperty("组训单位标识")
	private Integer orgFlag; //组训单位标识
	// Constructors

}
