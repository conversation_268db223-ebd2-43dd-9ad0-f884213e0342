package com.lms.base.course.service;

import com.lms.base.feign.model.Course;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.feign.dto.TreeNode;
import com.lms.common.service.BaseService;

import java.util.List;

public interface CourseService extends BaseService<Course> {
    Long getTotalCourse();

    Boolean existCourse(Course course);

    Boolean existCourseNo(Course course);

    List<TreeNode> getCourseStructureTree(PageInfo pageInfo);

    void batchReleaseCourse(List<String> idList);

    List<Course> getByCourseIdList(List<String> courseIdList);

    List<Course> getCourseListByComponentIdList(List<String> componentIdList);

}
