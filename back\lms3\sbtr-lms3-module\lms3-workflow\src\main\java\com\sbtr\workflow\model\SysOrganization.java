package com.sbtr.workflow.model;


import cn.ewsd.common.model.MCoreBase;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Table;
import java.util.Date;

/**
 * 组织机构表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2019-08-16 17:35:23
 */
@Table(name = "sys_organization")
public class SysOrganization {

    /**
     * 机构ID
     */
    private String id;
    /**
     * 父级ID
     */
    private String pid;
    /**
     * 组织机构名称
     */
    private String orgName;
    /**
     * 组织机构负责人
     */
    private String orgPerson;
    /**
     * 排序
     */
    private Integer orgSort;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgPerson() {
        return orgPerson;
    }

    public void setOrgPerson(String orgPerson) {
        this.orgPerson = orgPerson;
    }

    public Integer getOrgSort() {
        return orgSort;
    }

    public void setOrgSort(Integer orgSort) {
        this.orgSort = orgSort;
    }
    @Override
    public String toString() {
        return "SysOrganization{" +
                "id=" + id +
                ", pid=" + pid +
                ", orgName='" + orgName + '\'' +
                ", orgPerson='" + orgPerson + '\'' +
                ", orgSort=" + orgSort +
                '}';
    }
}
