package com.sbtr.workflow.controller;

import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.service.ApiFlowableProcessDefinitionService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.Result;
import com.sbtr.workflow.vo.ProcessDefinitionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;


/**
 * 流程定义
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
@Api(tags = {"流程定义" })
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/apiFlowableProcessDefinition")
public class ActFlowableProcessDefinitionController extends WorkflowBaseController {

    @Autowired
    private ApiFlowableProcessDefinitionService apiFlowableProcessDefinitionService;



    @ApiOperation(value="流程定义列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "pageSize", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "category", value = "分类标识", defaultValue = "category", required = true, dataType = "String"),
            @ApiImplicitParam(name = "processModelType", value = "流程模型类型  1 自定义流程界面  2 托拉拽界面", defaultValue = "1", required = true, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "流程名称", defaultValue = "name", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/getPageSet", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam, String category,String processModelType,String name) {
        // 获取最新版本发布的流程
        PageSet<ProcessDefinitionVo> pageSet = apiFlowableProcessDefinitionService.getPageSet(pageParam,category,processModelType,name);
        return pageSet;
    }

    @ResponseBody
    @ApiOperation(value="针对app 流程定义列表查询")
    @RequestMapping(value = "/getAllData", method = RequestMethod.POST)
    public Result getAllData(PageParam pageParam, String category, String processModelType, String name) {
        List<ProcessDefinitionVo> pageSet = apiFlowableProcessDefinitionService.getAllData(pageParam,category,processModelType,name);
        return Result.ofSuccess(pageSet);
    }




    @Log(title = "流程定义-删除",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ApiOperation(value="删除流程定义")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deploymentId", value = "部署id", defaultValue = "deploymentId", required = true, dataType = "String")
    })
    @ResponseBody
    @PostMapping(value = "/deleteDeployment")
    public Object deleteDeployment(String deploymentId) {
        ProcessDefinitionVo prodef = apiFlowableProcessDefinitionService.getProdefIdById(apiFlowableProcessDefinitionService.getProdefIdByDeployId(deploymentId));
        if(prodef!=null)
            setLogMessage(prodef.getName(),"");
        Object object = apiFlowableProcessDefinitionService.deleteDeployment(deploymentId);
        return object;
    }



    @Log(title = "流程定义-激活挂起",module = LogModule.WORKFLOW,businessType = BusinessType.ACTIVATE, method = "getLogMessage")
    @ApiOperation(value="流程定义激活挂起")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processDefinitionId", value = "流动定义Id", defaultValue = "processDefinitionId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "suspensionState", value = "1挂起  2激活", defaultValue = "deploymentId", required = true, dataType = "Int")
    })
    @ResponseBody
    @PostMapping(value = "/saDefinitionById")
    public Object saDefinitionById(String processDefinitionId, int suspensionState) {
        ProcessDefinitionVo prodef = apiFlowableProcessDefinitionService.getProdefIdById(processDefinitionId);
        if(prodef!=null)
            setLogMessage(prodef.getName(),"");
        Object object = apiFlowableProcessDefinitionService.suspendOrActivateProcessDefinitionById(processDefinitionId, suspensionState);
        return object;
    }


}
