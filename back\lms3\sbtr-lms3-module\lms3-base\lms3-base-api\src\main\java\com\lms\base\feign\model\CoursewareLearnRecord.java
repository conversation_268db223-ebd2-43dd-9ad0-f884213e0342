package com.lms.base.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)

@TableName("u_coursewarelearnrecord")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "课件学习记录对象")
public class CoursewareLearnRecord extends BaseModel {

    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty(value = "课件学习记录唯一标识")
    private String id;

    @ApiModelProperty(value = "课程id")
    @TableField("COURSEID")
    private String courseId; //课程id

    @ApiModelProperty(value = "课件id")
    @TableField("COURSEWAREID")
    private String coursewareid; //课件id

    @ApiModelProperty(value = "学员证件号")
    @TableField("CARDNUM")
    private String cardNum;

    @ApiModelProperty(value = "状态")
    @TableField("STATUS")
    private Integer status; //状态

    @ApiModelProperty(value = "学习进度")
    @TableField("SPEED")
    private Integer speed; //进度

    @TableField("SUBMITEXAMID")
    @ApiModelProperty(value = "试卷id")
    private String submitexamid; //考试通过的试卷id

    @ApiModelProperty(value = "学习日期")
    @TableField("LEARNDATE")
    private String learnDate; //学习日期

    @ApiModelProperty(value = "课件对象")
    @TableField(exist = false)
    private Courseware courseware;

    @ApiModelProperty(value = "学习次数")
    @TableField("STUDYNUM")
    private Integer studynum; //学习次数

}
