package com.lms.base.person.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 定期考核结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssessmentResult {

    @Excel(name = "教员")
    private String teacherName;

    @Excel(name = "账号")
    private String teacherNumber;

    @Excel(name = "学历与专业B1")
    private int educationAndMajor;

    @Excel(name = "专业知识B2")
    private int specializedKnowledge;

    @Excel(name = "通用知识B3")
    private int universalKnowledge;

    @Excel(name = "动手操作技能B4/理论讲授技能B5")
    private int teachingSkill;

    @Excel(name = "沟通技能B6")
    private int communicationSkill;

    @Excel(name = "管理技能B7")
    private int managementSkill;

    @Excel(name = "政治素养B8")
    private int politicalAccomplishment;

    @Excel(name = "职业素养B9")
    private int professionalAccomplishment;

    @Excel(name = "品德素养B10")
    private int moralAccomplishment;

    @Excel(name = "敬业精神B11")
    private int dedicationOfJob;

    @Excel(name = "合作意识B12")
    private int cooperation;

    @Excel(name = "工作态度B13")
    private int workingAttitude;

    @Excel(name = "心理品质B14")
    private int psychologicalTraits;

    @Excel(name = "学习热情B15")
    private int passionForLearning;


}
