package com.sbtr.workflow.service;


import com.sbtr.workflow.dto.CommonTaskDto;
import com.sbtr.workflow.dto.ProcessDefinitionDto;
import com.sbtr.workflow.dto.StartedDto;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.Result;
import com.sbtr.workflow.vo.CommentVo;
import com.sbtr.workflow.vo.FlowNodeVo;
import com.sbtr.workflow.vo.TaskVo;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 流程任务
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:35:23
 */
public interface ApiFlowableTaskService {

    /**
     * 同意
     *
     * @return
     * @Param processInstanceId 分页参数
     * @Param taskId 任务标题
     * @Param message 当前登录用户Id
     * @Param processDefinitionId 任务名称
     * @Param map 任务名称
     */
    Object agree(String processInstanceId, String taskId, String message, String processDefinitionId, Map<String, Object> map);

    /**
     * 驳回
     *
     * @return
     * @Param processInstanceId 实例Id
     * @Param taskId 任务Id
     * @Param message 驳回原因
     * @Param distFlowElementId 需要驳回的节点id
     */
    Object reject(String processInstanceId, String taskId, String message, String distFlowElementId, Map<String, Object> params);

    /**
     * 待办任务列表(自己)
     *
     * @return
     * @Param pageParam 分页参数
     * @Param formName 任务标题
     * @Param userNameId 当前登录用户Id
     * @Param modelName 任务名称
     * @Param processModelType 任务名称
     */
    PageSet<TaskVo> getToDoTasks(PageParam pageParam, String userNameId, String formName, String modelName, String processModelType, String startTime);

    /**
     * 待办任务列表(自己)，不分页
     *
     * @return
     * @Param pageParam 分页参数
     * @Param formName 任务标题
     * @Param userNameId 当前登录用户Id
     * @Param modelName 任务名称
     * @Param processModelType 任务名称
     */
    List<TaskVo> getAllToDoTasks(PageParam pageParam, String businessKey, String userNameId, String formName, String modelName, String processModelType, String startTime);

    /**
     * 历史任务列表(自己)
     *
     * @return
     * @Param pageParam 分页参数
     * @Param userNameId 当前登录用户Id
     * @Param name 当前登录用户Id
     * @Param procDefName 当前登录用户Id
     */
    PageSet<CommonTaskDto> getMyHistoryPageSet(PageParam pageParam, String userNameId, String name, String procDefName,
                                               String processModelType, String startTime);

    /**
     * 查询在办任务列表(自己)
     *
     * @return
     * @Param pageParam 分页参数
     * @Param userNameId 当前登录用户Id
     * @Param formName 当前登录用户Id
     * @Param modelName 当前登录用户Id
     * @Param processModelType
     */
    PageSet<TaskVo> getMyNoEndProcessPageSetData(PageParam pageParam, String userNameId, String formName, String modelName, String processModelType, String startTime);

    /**
     * 获得进行中流程分页集数据(自己)
     *
     * @return
     */
    PageSet<CommonTaskDto> getTaskPageSet(PageParam pageParam, String formName, String modelName);

    /**
     * 已启动流程分页集数据()
     *
     * @return
     */
    PageSet<StartedDto> getStartedPageSet(PageParam pageParam, String modelName, String modelKey);

    /**
     * 已部署流程分页列表
     *
     * @return
     */
    PageSet<ProcessDefinitionDto> getDeployedPageSet(PageParam pageParam, String modelName, String modelKey);

    /**
     * 已部署流程删除
     * deploymentId 部署Id
     * id 定义Id
     * key 模型key
     *
     * @return
     */
    Object deployedDelete(String deploymentId, String id, String key);

    /**
     * 历史任务点击删除
     *
     * @return
     */
    Object deleteHistoricProcessInstance(String instanceId);

    /**
     * 已办任务分页列表
     *
     * @return
     */
    PageSet<TaskVo> getApplyedTasks(PageParam pageParam, String userNameId, String formName, String modelName, String processModelType);

    /**
     * 已办任务不分页列表
     *
     * @return
     */
    List<TaskVo> getApplyedTasks(PageParam pageParam, String userNameId);

    /**
     * 历史任务点击详情(自己)
     *
     * @return
     * @Param processInstanceId 实例Id
     * @Param modelKey 模型key
     */
    Object historyClickDetails(String processInstanceId, String modelKey, String processDefinitionId, String businessKey, String nodeId) throws Exception;

    /**
     * 已办任务点击详情(自己)
     *
     * @return
     * @Param processInstanceId 实例Id
     * @Param modelKey 模型key
     */
    Object applyedTasksClickDetails(String processInstanceId, String modelKey);


    /**
     * 获取可驳回节点列表
     *
     * @param taskId            任务id
     * @param processInstanceId 流程实例id
     * @return
     */
    List<FlowNodeVo> getBackNodesByProcessInstanceId(String processInstanceId, String taskId, String nodelId);


    Object getTaskPageSetClickDetails(String processInstanceId, String modelKey);

    /**
     * 转办
     *
     * @return
     * @Param taskId 任务Id
     * @Param comment 转办原因
     * @Param instanceId 实例Id
     * @Param userNameId 转办人userNameId
     */
    //直接将办理人assignee 换成别人，这时任务的拥有着不再是转办人，而是为空，相当与将任务转出
    Object transfer(String taskId, String comment, String instanceId, String owner, String userNameId);

    /**
     * 委派
     *
     * @return
     * @Param taskId 任务Id
     * @Param comment 转办原因
     * @Param instanceId 实例Id
     * @Param userNameId 转办人userNameId
     */
    //解释：委派
    //A由于某些原因不能处理该任务，可以把任务委派给用户B代理，当B处理完成之后再次回到用户A这里，在这个过程中A是任务的所有者，B是该任务的办理人
    //A->B->A
    Object delegate(String taskId, String comment, String instanceId, String userNameId);

    /**
     * 撤回
     *
     * @return
     * @Param taskId 任务Id
     * @Param comment 撤回原因
     * @Param instanceId 实例Id
     */
    Object revokeProcess(String taskId, String comment, String instanceId);

    //
    //加签：同时让多人处理任务
    //1、向前加签
    //任务在A这里，A这个时候需要BCD核对一下，等BCD核对之后又回到A这里
    //2、向后加签
    //任务在A这里，A这个时候需要BCD处理这个事情，处理完毕之后就不用管了，继续后面的审批环节
    //实际情况中，我们应该只有一种加签比较合适，因为用户的使用的时候，如果是一排按钮的话，他自己都搞不清楚到底点击哪个了。我在实际的情况下是使用向后加签的。

    /**
     * 加签
     *
     * @return
     * @Param taskId 任务Id
     * @Param comment 原因
     * @Param instanceId 实例Id
     * @Param userNameId 工号
     * @Param signature 前 after  后 !after 加签
     */
    Object signature(String taskId, String comment, String instanceId, String userNameId, String signature);


    /**
     * 获取审批意见
     *
     * @return
     * @Param taskId 任务Id
     */
    List<CommentVo> getTaskCommentsByTaskId(String taskId);

    /**
     * 获取审批意见
     *
     * @return
     * @Param taskId 任务Id
     */
    Object clickStartedViewFlowchart(String processInstanceId, String modelKey, String processDefinitionId);

    /**
     * 催办
     *
     * @return
     * @Param type
     * @Param message
     */
    Object processUrging(String type, String message, String formName, String processDefinitionId, String taskId, String assignee, String startUserId);

    /**
     * 流程撤销
     *
     * @return
     * @Param type
     * @Param message
     */
    Object processRevocation(String processInstanceId, String message, String taskId);


    /**
     * 通用更新业务数据
     *
     * @return
     * @Param sql
     */
    Integer updateBusinessData(String sql);


    /**
     * 驳回上一步
     *
     * @return
     * @Param processInstanceId 实例Id
     * @Param currentActivityId 当前节点Id
     * @Param processDefinitionId 定义Id
     */
    Object goBackToThePreviousStep(String processInstanceId, String currentActivityId, String processDefinitionId, String taskId);


    /**
     * 获取下一步任务 id name
     *
     * @return
     * @Param node 实例Id    now当前   next 下一个审批节点
     * @Param taskId 当前节点Id
     */
    Object nextFlowNode(String node, String taskId);


    /**
     * 待办任务列表(财务管理财务审核)
     *
     * @return
     * @Param pageParam 分页参数
     * @Param formName 任务标题
     * @Param userNameId 当前登录用户Id
     * @Param modelName 任务名称
     * @Param processModelType 任务名称
     */
    List<String> getFinanceUuidByTankName(String userNameId, String formName, String modelName, String processModelType, String startTime);


    /**
     * 通过业务uuid查询待办数据
     *
     * @param uuid 业务uuid
     * @return
     */
    TaskVo getToDoTaskByUuid(String userNameId, String uuid);

    PageSet<CommonTaskDto> getCompletedTaskPageSet(PageParam pageParam, String taskId, String procDefName);


    /**
     * 设置任务节点处理人 不支持会签
     *
     * @return
     * @Param assignUser  处理人工号 多个逗号隔开
     * @Param processInstanceId  实例ID
     * @Param modelKey 模型key
     */
    Result assignedTo(String assignUser, String processInstanceId, String modelKey);

    /**
     * @param processInstanceId
     * @return {@link List}<{@link CommentVo}>
     */
    List<CommentVo> getTaskCommentsByProcessInstanceId(String processInstanceId);

    CommentVo getActHiTaskinstById(String taskId);

    List<TaskVo> getTaskRefByParam(String processInstanceId, String businessKey, String taskId);

    List<TaskVo> getActHiTaskVoById(String processInstanceId, String businessKey, String taskId);

    TaskVo getToDoTasks(String taskId);

    /**
     * 根据任务ID获取当前处理人名称
     */
    List<String> getCurrentAssigneeByTask(String taskId);


    /**
     * 根据任务ID获取当前处理人ID
     */
    List<String> getCurrentAssigneeUserIdByTask(String taskId);

    /**
     * 根据任务ID获取当前实例当前任务节点的所有任务：处理人账号键值
     */
    List<TaskVo> getTaskAssigneeInfo(String taskId);
    /**
     * 获取代理任务列表
     */
    PageSet<TaskVo> getAgentTasks(PageParam pageParam, String userNameId, String formName, String modelName, String processModelType, String startTime);

    TaskVo getAgentTasks(String taskId);

    Object agent(String taskId,String processInstanceId,String userNameId);

    HashMap<String, HashMap<String, TaskVo>> getTaskInfoByBusinessKey(String[] businessKeys);

    HashMap<String, HashMap<String, TaskVo>> getTaskInfoByModelKey(String modelKey);

    void updateTaskOpeningTimeByKey(String taskId, Date date);

    void updateMlevelByBusinessKey(String businessUuid, Integer slevel);

    Map<String, Object> getActNameByProcessInstanceIdList(List<String> processInstanceIdList);

    Map<String, Object> geCurrentAssigneeByTaskIdList(List<String> taskIdList);

    HashMap<String, HashMap<String, TaskVo>> getTaskInfoByBusinessKeyAndModelKey(String[] businessKeys, String modelKey);
}
