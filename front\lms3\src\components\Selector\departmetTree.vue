<template>
  <a-tree-select
    v-model:value="value"
    allowClear
    show-search
    style="width: 100%"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    placeholder="请选择单位"
    :multiple="multiple"
    :fieldNames="
      props.fieldNames
        ? props.fieldNames
        : { children: 'children', label: 'name', value: 'id' }
    "
    treeNodeFilterProp="name"
    tree-default-expand-all
    :tree-data="departTreeOptions"
    @change="treeChange"
  />
</template>
<script lang="ts" setup>
import { ref, watch } from "vue";
import type { TreeSelectProps } from "ant-design-vue";
import { departTreeOptions } from "@/data/system.ts";

interface Emits {
  (e: "change", data: TreeSelectProps): void;
}

interface Props {
  treeValue?: string;
  value?: any;
  multiple?: boolean;
  fieldNames?: object;
  "onUpdate:value"?: (value: any) => void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const value = ref<string>(props.value || props.treeValue || "");
const multiple = ref<boolean>(props.multiple || false);

const treeChange = (v, l) => {
  emit("change", { value: v ? v : "", label: l });
};

watch(
  () => props.value,
  newValue => {
    if (newValue !== undefined) {
      value.value = newValue;
    }
  }
);

defineExpose({ value });
</script>
