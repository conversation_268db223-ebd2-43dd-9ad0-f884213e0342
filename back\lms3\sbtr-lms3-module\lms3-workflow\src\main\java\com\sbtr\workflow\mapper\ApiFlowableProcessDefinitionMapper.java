package com.sbtr.workflow.mapper;

import com.sbtr.workflow.vo.ProcessDefinitionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * api可流动流程定义映射器
 *
 * <AUTHOR>
 * @Version V5.4.21
 * @Email <EMAIL>
 * @Date 2023/03/20
 */
public interface ApiFlowableProcessDefinitionMapper {

    /**
     * 获取页面设置我sql
     *
     * @param category         类别
     * @param processModelType 流程模型类型
     * @param name             名字
     * @return {@link List}<{@link ProcessDefinitionVo}>
     */
    List<ProcessDefinitionVo> getPageSetMySql(@Param("category") String category,
                                              @Param("processModelType") String processModelType,
                                              @Param("name") String name,
                                              @Param("majorVersion") String majorVersion);

    List<ProcessDefinitionVo> getPageSetOracle(@Param("category") String category,@Param("processModelType") String processModelType,@Param("name") String name);

    List<ProcessDefinitionVo> getListByIdAndModelKey(@Param("modelKey")String modelKey,@Param("majorVersion") String majorVersion);

    Integer updateMajorVersion(@Param("procdefId")String procdefId, @Param("modelKey")String modelKey,@Param("majorVersion") String majorVersion);

    Integer getCountSumByModelKey(@Param("modelKey")String modelKey);

    Integer updateMajorVersionByModelKey(@Param("modelKey")String modelKey, @Param("majorVersion")String majorVersion);

    String getProdefIdByDeployId(@Param("id")String id);

    ProcessDefinitionVo getProdefIdById(@Param("processDefinitionId")String processDefinitionId);

    List<ProcessDefinitionVo> getCustomInterface();

}
