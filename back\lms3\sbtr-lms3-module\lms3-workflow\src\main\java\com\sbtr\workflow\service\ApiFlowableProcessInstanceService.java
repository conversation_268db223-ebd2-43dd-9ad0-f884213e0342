package com.sbtr.workflow.service;


import com.alibaba.fastjson.JSONObject;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.Result;
import com.sbtr.workflow.vo.ProcessInstanceVo;
import com.sbtr.workflow.vo.TaskVo;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 流程实例
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:35:23
 */
public interface ApiFlowableProcessInstanceService extends BaseService{

    /**
     *  通用启动流程
     *
     * @param modelKey      模型Key 必填
     * @param businessUuid  业务Id 必填
     * @param businessTitle 标题  必填
     * @param assignUser    下一步处理人工号 非必填
     * @param duplicateUser 抄送人工号,多个逗号隔开 非必填
     * @param skipNode      是否默认处理第一步 非必填 true false 非必填
     * @param params      表单数据 非必填 主要处理第一个任务节点后马上接分支
     * @Return java.lang.Object
     */
    Object startProcessInstanceByKey(String modelKey,
                                     String businessUuid,
                                     String businessTitle,
                                     String assignUser,
                                     String duplicateUser,
                                     String skipNode,
    Map<String, Object> params);

    /**
     *   流程是否挂起
     *
     * @param processInstanceId 流程实例Id
     * @Return boolean
     */
    boolean isSuspended(String processInstanceId);


    /**
     *   终止该流程
     *
     * @Param processInstanceId 流程实例Id
     * @Param message 终止原因
     * @Return Object
     */
    Object stopProcessInstanceById(String taskId, String processInstanceId, String message);

    /**
     *   撤回流程
     *
     * @Param processInstanceId 流程实例Id
     * @Param message 撤回原因
     * @Return Object
     */
    Object revokeProcess(String processInstanceId, String message);

    /**
     *   流程实例查询
     *
     * @Param processInstanceId 流程实例Id
     * @Param message 撤回原因
     * @Return Object
     */
    PageSet<ProcessInstanceVo> getPageSet(PageParam pageParam, String name);

    /**
     * 流程实例查询
     * @param modelKey 模型key
     * @param businessKey 业务id
     * @return
     */
    List<ProcessInstanceVo> getProcessInstance(String modelKey, String businessKey);


    List<ProcessInstanceVo> getProcessInstanceByBusinessKeys(String modelKey, List<String> businessKeys);


    /**
     * 激活流程定义
     *
     * @param processInstanceId 流程实例id
     * @param suspensionState   2激活 1挂起
     */
    Object suspendOrActivateProcessInstanceById(String processInstanceId, Integer suspensionState);


    /**
     * 点击办理
     *
     * @param modelKey            模型key
     * @param taskId              任务id
     * @param processInstanceId   流程实例id
     * @param nodeId              节点id
     * @param processDefinitionId 定义id
     */
    Object clickStartProcess(String modelKey, String taskId, String processInstanceId, String nodeId, String processDefinitionId) throws Exception;

    /**
     * 根据业务id获取审批意见
     *
     * @param businessKey 业务ID
     */
    Object getListCommentsByBusinessKey(String businessKey);


    /**
     * 自由跳转
     *
     * @param processInstanceId 业务id
     * @param nodeId            节点id
     * @param toNodeId          要跳转的节点id
     */
    Object processFreeJump(String processInstanceId, String nodeId, String toNodeId);

    /**
     * 自由跳转
     *
     * @param processInstanceId 业务id
     * @param nodeId            节点id
     * @param toNodeId          要跳转的节点id
     * @param message           审批意见
     */
    Object processFreeJumpForApi(String processInstanceId, String nodeId, String toNodeId, String message);

    /**
     * 获取自由跳转的节点
     *
     * @param processInstanceId 业务id
     */
    List<TaskVo> getProcessFreeJumpData(String processInstanceId);


    /**
     * 获取自由跳转的节点
     *
     * @param processInstanceId 业务id
     */
    Object getAllUserTaskListByProcessInstanceId(String processInstanceId);

    String getProcessInstanceNameByProcessInstanceId(String processInstanceId);

    /**
     * 获取自由跳转的节点
     *
     * @param processInstanceId 业务id
     * @param message 业务id
     */
    Object stopProcessInstanceByProcessInstanceId(String processInstanceId, String message);

    /**
     * 流程实例删除
     *
     * @param processInstanceId 实例id
     */
    Object deleteProcessInstanceById(String processInstanceId);

    Object customizeFormStartProcessInstance(String modelKey, String businessTitle,String skipNode, @RequestParam Map<String, Object> map);

    /**
     * 流程作废
     *
     * @param processInstanceId 实例id
     * @param reason            作废原因
     */
    Result deleteProcessInstance(String processInstanceId, String reason);
//    JSONObject getBusinessData(String tablename, String primarykey, String keyValue);
    List<ProcessInstanceVo> getRunProcessInstByParam(String businessKey);

}
