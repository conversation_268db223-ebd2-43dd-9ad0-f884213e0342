package com.lms.common.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ExcelUtils {

    private ExcelUtils() {
        super();
    }

    /**
     * 文件上传保存到服务器
     *
     * @param uploadDir
     * @param file
     * @param fileName
     * @return
     * @throws Exception
     */
    public static String excelUpload(String uploadDir, MultipartFile file, String fileName) throws Exception {
        String fileOriName = file.getOriginalFilename();
        // 文件后缀名
        String suffix = fileOriName.substring(fileOriName.lastIndexOf("."));
        // 上传文件名
        String filename = fileName + suffix;
        //服务器端保存的文件对象
        File saveFile = new File(uploadDir + filename);
        // 将上传的文件写入到服务器端文件内
        file.transferTo(saveFile);
        return filename;
    }

    /**
     * 本地文件读取Excel，进行导出数据
     *
     * @param file
     * @param sheetIndex
     * @param pojoClass
     * @return
     */
    public static <T> List<T> importExcel(File file, Integer sheetIndex, Class<T> pojoClass) {
        if (null == file) return getTList(null);
        ImportParams params = getImportParams(0, 1, sheetIndex);
        List<T> list = null;
        try {
            params.setStartSheetIndex(sheetIndex);
            list = ExcelImportUtil.importExcel(file, pojoClass, params);
        } catch (Exception e) {
            throw new RuntimeException("easypoi导入数据出错！" + e.getMessage());
        }
        return getTList(list);
    }


    /**
     * 导入Excel方法默认设置类型1
     * 1.默认表名称行为1
     * 2.默认表头字段为1
     * 3.默认sheet页为0
     * 4.默认服务器不保存导入文件
     *
     * @param file：导入文件
     * @param pojoClass：导入对应类
     * @return
     */
    public static <T> List<T> importExcel(MultipartFile file, Class<T> pojoClass) {
        if (null == file) return getTList(null);
        ImportParams params = getImportParams(0, 1, 0);
        List<T> list = null;
        try (InputStream inputStream = file.getInputStream()){
            list = ExcelImportUtil.importExcel(inputStream, pojoClass, params);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("easypoi导入数据出错！" + e.getMessage());
        }
        return getTList(list);
    }

    /**
     * 导入Excel方法默认设置类型2
     * 1.默认表名称行为1
     * 2.默认表头字段为1
     * 3.默认服务器不保存导入文件
     *
     * @param file：导入文件
     * @param pojoClass：导入对应类
     * @param sheetIndex：sheet页
     * @return
     */
    public static <T> List<T> importExcel(MultipartFile file, Integer sheetIndex, Class<T> pojoClass) {
        if (null == file) return getTList(null);
        ImportParams params = getImportParams(0, 1, sheetIndex);
        List<T> list = null;
        try (InputStream inputStream = file.getInputStream()){
            params.setStartSheetIndex(sheetIndex);
            list = ExcelImportUtil.importExcel(inputStream, pojoClass, params);
        } catch (Exception e) {
            e.printStackTrace(); // 生产环境可以不抛出
            throw new RuntimeException("easypoi导入数据出错！" + e.getMessage());
        }
        return getTList(list);
    }

    /**
     * 自定义导入Excel方法
     *
     * @param file：文件
     * @param titleRows：标题行
     * @param headerRows：表头行
     * @param sheetIndex：sheet页
     * @param pojoClass：导入对应类
     * @return
     */
    public static <T> List<T> importExcel(MultipartFile file, Integer titleRows, Integer headerRows,
                                          Integer sheetIndex, Class<T> pojoClass) {
        if (null == file) return getTList(null);
        ImportParams params = getImportParams(titleRows, headerRows, sheetIndex);
        List<T> list = null;
        try (InputStream inputStream = file.getInputStream()){
            params.setStartSheetIndex(sheetIndex);
            list = ExcelImportUtil.importExcel(inputStream, pojoClass, params);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("easypoi导入数据出错！" + e.getMessage());
        }
        return getTList(list);
    }

    /**
     * 根据输入流导入Excel
     *
     * @param titleRows
     * @param headerRows
     * @param sheetIndex
     * @param pojoClass
     * @return
     */
    public static <T> List<T> importExcel(InputStream ins, Integer titleRows, Integer headerRows,
                                          Integer sheetIndex, Class<T> pojoClass) {
        if (null == ins)
            return getTList(null);
        ImportParams params = getImportParams(titleRows, headerRows, sheetIndex);
        List<T> list = null;
        try {
            params.setStartSheetIndex(sheetIndex);
            list = ExcelImportUtil.importExcel(ins, pojoClass, params);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("easypoi导入数据出错！" + e.getMessage());
        }
        return getTList(list);
    }

    /**
     * 导出Excel
     *
     * @param list
     * @param title
     * @param sheetName
     * @param pojoClass
     * @param fileName
     * @param response
     */
    public static void exportExcel(List<?> list, String title, String sheetName, Class<?> pojoClass,
                                   String fileName, HttpServletResponse response) {
        ExportParams exportParams = new ExportParams(title, sheetName);
        exportParams.setCreateHeadRows(true);
        defaultExport(list, pojoClass, fileName, response, exportParams);
    }

    /**
     * 导出Excel
     *
     * @param list
     * @param title
     * @param sheetName
     * @param pojoClass
     * @param fileName
     * @param isCreateHeader
     * @param response
     */
    public static void exportExcel(List<?> list, String title, String sheetName, Class<?> pojoClass, String fileName,
                                   boolean isCreateHeader, HttpServletResponse response) {
        ExportParams exportParams = new ExportParams(title, sheetName);
        exportParams.setCreateHeadRows(isCreateHeader);
        defaultExport(list, pojoClass, fileName, response, exportParams);
    }


    /**
     * 导出Excel，又Map对象组成的List集合
     *
     * @param list
     * @param fileName
     * @param response
     */
    public static void exportExcel(List<Map<String, Object>> list,
                                   String fileName, HttpServletResponse response) {
        defaultExport(list, fileName, response);
    }


    /**
     * 导出无固定类的Map集合的Excel
     *
     * @param fileName
     * @param response
     */
    public static void customExport(String title, String sheelName, String fileName,
                                    List<ExcelExportEntity> entityList, Collection<Map<String, String>> dataSet, HttpServletResponse response) {
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(title, sheelName), entityList, dataSet);
        if (null != workbook) {
            downLoadExcelToBroswer(fileName, response, workbook);
        }
    }

    /**
     * 导出无固定类的Map集合的大数据量Excel
     *
     * @param fileName
     * @param response
     */
    public static void customBigExport(String title, String sheelName, String fileName,
                                       List<ExcelExportEntity> entityList, Collection<Map<String, String>> dataSet, HttpServletResponse response) {

        Workbook workbook = ExcelExportUtil.exportBigExcel(new ExportParams(title, sheelName), entityList, dataSet);
        if (null != workbook) {
            downLoadExcelToBroswer(fileName, response, workbook);
        }
    }

    /**
     * 构造sheet页数据
     * @param sheetName
     * @param pojoClass
     * @param list
     * @param sheelList
     */
    public static void getExportSheetMap(String sheetName, Class<?> pojoClass, List<?> list, List<Map<String, Object>> sheelList) {
        ExportParams multipleSheetParams = new ExportParams();
        multipleSheetParams.setSheetName(sheetName);
        Map<String, Object> map = new HashMap<>();
        map.put("title", multipleSheetParams);
        map.put("entity", pojoClass);
        map.put("data", list);
        sheelList.add(map);
    }

    public static String multipleSheetExport(List<Map<String, Object>> list, String fileName, HttpServletResponse response) {
        String error = "";
        Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.HSSF);
        if (null != workbook) {
            try {
                downLoadExcelToBroswer(fileName, response, workbook);
            } catch (Exception e) {
                e.printStackTrace();
                error = "Excel导出失败";
            }
        }
        return error;
    }

    /**
     * 动态字段列表，下载Excel文件到服务器本地
     * @param title
     * @param sheetName
     * @param fileName
     * @param entity
     * @param list
     */
    public static void downExcelToLocal(String title, String sheetName,
                                        String fileName, List<ExcelExportEntity> entity, List<?> list) {
        ExportParams exportParams = new ExportParams(title, sheetName);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, entity, list);
        try {
            FileOutputStream fos = new FileOutputStream("E:\\log\\" + fileName); // TODO 更改
            workbook.write(fos);
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("easypoi导出数据到本地出错！" + e.getMessage());
        }
    }

    /**
     * 保存Excel文件到服务器本地
     * @param list
     * @param fileName
     * @param path
     */
    public static void downExcelToLocal(List<Map<String, Object>> list, String fileName, String path) {
        Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.HSSF);
        try {
            FileOutputStream fos = new FileOutputStream(path + fileName);
            workbook.write(fos);
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("easypoi导出数据到本地出错！" + e.getMessage());
        }
    }


    /**
     * 导入参数信息设置，默认导入文件服务器不保存
     *
     * @param titleRows：标题行
     * @param headerRows：表字段行
     * @param sheetIndex：sheet页序号
     * @return
     */
    private static ImportParams getImportParams(Integer titleRows, Integer headerRows, Integer sheetIndex) {

        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        params.setNeedSave(false);// 设置不保存记录
        return params;
    }

    /**
     * // 防止空指针
     *
     * @param list
     * @return
     */
    private static <T> List<T> getTList(List<T> list) {

        if (null == list) {
            list = new ArrayList<T>();
        }
        return list;
    }


    /**
     * 导出无固定类的Map集合的Excel
     *
     * @param list
     * @param fileName
     * @param response
     */
    private static void defaultExport(List<Map<String, Object>> list, String fileName, HttpServletResponse response) {

        Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.HSSF);
        if (null != workbook) {
            downLoadExcelToBroswer(fileName, response, workbook);
        }
    }

    /**
     * 默认导出Excel的list集合基础方法
     *
     * @param list
     * @param pojoClass
     * @param fileName
     * @param response
     * @param exportParams
     */
    private static void defaultExport(List<?> list, Class<?> pojoClass, String fileName,
                                      HttpServletResponse response, ExportParams exportParams) {
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);
        if (null != workbook) {
            downLoadExcelToBroswer(fileName, response, workbook);
        }
    }

    /**
     * 导出Excel到浏览器
     *
     * @param fileName
     * @param response
     * @param workbook
     */
    private static void downLoadExcelToBroswer(String fileName,
                                               HttpServletResponse response, Workbook workbook) {

        try {
            response.setHeader("Content-Type", "application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment");
            response.setHeader("filename", URLEncoder.encode(fileName, "UTF-8"));
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Excel输出导出异常！" + e.getMessage());
        }
    }





}
