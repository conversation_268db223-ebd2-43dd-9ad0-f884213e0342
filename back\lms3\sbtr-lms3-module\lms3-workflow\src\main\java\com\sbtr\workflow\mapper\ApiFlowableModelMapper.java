package com.sbtr.workflow.mapper;

import com.sbtr.workflow.vo.ActDeModelVo;
import com.sbtr.workflow.vo.ActReDeploymentVo;
import org.apache.ibatis.annotations.Param;
import org.flowable.ui.modeler.domain.Model;

import java.util.List;

public interface ApiFlowableModelMapper{

    //分页
    List<ActDeModelVo> getPageSetOracle(@Param("modelName") String modelName, @Param("modelKey") String modelKey,@Param("modelType") String modelType);

    List<ActDeModelVo> getPageSetMySql(@Param("modelName") String modelName, @Param("modelKey") String modelKey,@Param("modelType") String modelType);

    List<ActDeModelVo> getListByModelKey(@Param("modelKey")String modelKey);

    List<ActDeModelVo> getDataByModelKey(@Param("modelKey")String modelKey);

    void deleteDateByModelId(@Param("modelId")String modelId);

    Integer updateProcessModelTypeById(@Param("id")String id,@Param("processModelType") String processModelType);

    List<ActReDeploymentVo> getActReDeploymenListByModelKey(@Param("key")String key);

    //根据模型key去更改name
    Integer updateNameByModelKey(@Param("name")String name, @Param("modelKey")String modelKey);

    /**
     * 得到模型
     *
     * @param id 模型ID
     * @return {@link Model}
     */
    Model getModel(@Param("id")String id);
    List<ActDeModelVo> getPageSet(String modelName, String modelKey, String modelType);
    /**
     * 找到关键和类型
     *
     * @param processId     进程id
     * @param modelTypeBpmn bpmn模型类型
     * @return {@link List}<{@link Model}>
     */
    List<Model> findByKeyAndType(@Param("processId")String processId,@Param("modelTypeBpmn") int modelTypeBpmn);
}
