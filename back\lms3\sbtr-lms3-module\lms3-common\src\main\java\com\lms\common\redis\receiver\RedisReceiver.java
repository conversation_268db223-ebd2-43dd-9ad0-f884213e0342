package com.lms.common.redis.receiver;

import cn.hutool.core.util.ObjectUtil;
import com.lms.common.redis.base.BaseMap;
import com.lms.common.redis.listener.JeecgRedisListerer;
import com.lms.common.redis.util.SpringContextHolder;
import org.springframework.stereotype.Component;

@Component
public class RedisReceiver {
    public void onMessage(BaseMap params) {
        Object handlerName = params.get("handlerName");
        JeecgRedisListerer messageListener = (JeecgRedisListerer) SpringContextHolder.getHandler(handlerName.toString(), JeecgRedisListerer.class);
        if (ObjectUtil.isNotEmpty(messageListener)) {
            messageListener.onMessage(params);
        }

    }

    public RedisReceiver() {
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof RedisReceiver)) {
            return false;
        } else {
            RedisReceiver other = (RedisReceiver)o;
            return other.canEqual(this);
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof RedisReceiver;
    }

    public int hashCode() {
//        int result = true;
        return 1;
    }

    public String toString() {
        return "RedisReceiver()";
    }
}
