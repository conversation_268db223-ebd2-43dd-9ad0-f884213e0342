package com.lms.examine.examinesubjecttype.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.examine.feign.model.Examinesubjecttype;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface ExaminesubjecttypeMapper extends BaseMapper<Examinesubjecttype> {

    @Select("${sql}")
    List<Map<String, Object>> listSQLQuery(@Param("sql") String sql);
}
