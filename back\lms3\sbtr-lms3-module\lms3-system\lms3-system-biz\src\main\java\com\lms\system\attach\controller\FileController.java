package com.lms.system.attach.controller;

import com.lms.common.feign.dto.FileDTO;
import com.lms.common.model.Result;
import com.lms.common.service.FileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 文件es操作
 *
 * <AUTHOR>
 * @since 2024-11-09
 */
@Slf4j
@RestController
@RequestMapping("/esfile")
@Api(value="全文检索", tags = "全文检索")
public class FileController {

    @Resource
    private FileService fileService;


//    /**
//     * 保存文件
//     *
//     * @return
//     */
//    @ApiOperation("保存文件")
//    @PostMapping(value = "/saveFile")
//    public Result<?> saveFile(@RequestBody Attach attach) throws Exception {
//        fileService.saveFile(attach);
//        return Result.OK();
//    }

    /**
     * 保存数据
     *
     * @return
     */
//    @ApiOperation("保存数据")
//    @PostMapping(value = "/saveData")
//    public Result<?> saveData(@RequestBody BaseModel baseModel)  {
//        fileService.saveData(baseModel);
//        return Result.OK();
//    }


    /**
     * 关键字查询-repository
     *
     * @throws Exception
     */
    @PostMapping(value = "/search")
    @ApiOperation(value = "关键字查询", httpMethod = "POST")
    public Result<?> search(@RequestBody FileDTO dto) {
        return Result.OK(fileService.search(dto));
    }

    /**
     * 该接口用于查询
     * 关键字查询-原生方法
     *
     * @throws Exception
     */
    @PostMapping(value = "/searchPage")
    @ApiOperation(value = "查询", httpMethod = "POST")
    public Result<?> searchPage(@RequestBody FileDTO dto) {
        return Result.OK(fileService.searchPage(dto));
    }

    /**
     * 清除所有数据
     *
     * @return
     */
    @ApiOperation("清除所有数据")
    @DeleteMapping(value = "/deleteAll")
    public Result<?> deleteAll()  {
        fileService.deleteAll();
        return Result.OK("已清除所有全文检索数据");
    }

}
