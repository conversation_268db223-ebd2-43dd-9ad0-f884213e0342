package com.sbtr.workflow.vo;

import cn.ewsd.common.model.MCoreBase;

import javax.persistence.Table;


/**
 * 表格布局
 *
 * <AUTHOR>
 * @Version 5.5.0
 * @Email <EMAIL>
 * @Date 2023-07-24 15:23:44
 */
@Table(name = "form_layout")
public class FormLayoutVo extends MCoreBase {
    private static final long serialVersionUID = 1L;

    /**
     * 基础配置uuid
     */
    private String basicConfigureUuid;

    /**
     * 表单设计json数据
     */
    private String designJson;


    /**
     * 租户UUID
     */
    private String tenantUuid;

    /**
     * 布局名字
     */
    private String layoutName;
    /**
     * 布局代码
     */
    private String layoutCode;
    /**
     * 布局形式
     */
    private Integer layoutSort;


    public String getBasicConfigureUuid() {
        return basicConfigureUuid;
    }

    public void setBasicConfigureUuid(String basicConfigureUuid) {
        this.basicConfigureUuid = basicConfigureUuid;
    }

    public String getDesignJson() {
        return designJson;
    }

    public void setDesignJson(String designJson) {
        this.designJson = designJson;
    }

    public String getTenantUuid() {
        return tenantUuid;
    }

    public void setTenantUuid(String tenantUuid) {
        this.tenantUuid = tenantUuid;
    }

    public String getLayoutName() {
        return layoutName;
    }

    public void setLayoutName(String layoutName) {
        this.layoutName = layoutName;
    }

    public String getLayoutCode() {
        return layoutCode;
    }

    public void setLayoutCode(String layoutCode) {
        this.layoutCode = layoutCode;
    }

    public Integer getLayoutSort() {
        return layoutSort;
    }

    public void setLayoutSort(Integer layoutSort) {
        this.layoutSort = layoutSort;
    }
}
