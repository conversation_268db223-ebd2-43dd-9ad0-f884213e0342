<script setup lang="ts">
import { ref, reactive, nextTick } from "vue";
import { message } from "ant-design-vue";
import type { FormInstance, UploadFile } from "ant-design-vue";
import {
  FileTextOutlined,
  CloudUploadOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
  PlusOutlined,
  AppstoreOutlined,
  FileOutlined,
  SelectOutlined
} from "@ant-design/icons-vue";
import { addModel, getModelAttributeList } from "@/api/model";
import ModelattrSelector from "@/components/Selector/modelattrSelector.vue";
import {
  isImage,
  isVideo,
  is3DModel,
  beforeUpload,
  uploadFile
} from "@/utils/uploadUtils";
interface Property {
  id: number | null; // 属性ID
  modelId: number | null; // 模型ID
  name: string; // 属性名
  relationship: string; // 属性关系
  value: string; // 属性值
}

interface ModelAddForm {
  id: number | null;
  name: string;
  number: string | null;
  desc: string;
  attributeList: Property[] | [];
  fileUrl: string;
  previewUrl: string;
  uploadPerson: string | null;
  uploadTime: string | null;
}

interface Props {
  visible: boolean;
}

interface Emits {
  (e: "update:visible", visible: boolean): void;
  (e: "submit", data: ModelAddForm): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const loading = ref(false);
const fileList = ref<UploadFile[]>([]);
const uploadedFile = ref<File | null>(null);
const uploading = ref(false);
const uploadProgress = ref(0);
const uploadSuccess = ref(false);
const previewUrl = ref<string>("");

// 表单数据
const formData = reactive<ModelAddForm>({
  id: null,
  name: "",
  number: "",
  desc: "",
  attributeList: [],
  fileUrl: "",
  fileType: "",
  previewUrl: "",
  uploadPerson: "",
  uploadTime: ""
});

// 属性数据 - 独立管理
const properties = ref<Property[]>([
  // {
  //   name: "",
  //   value: "",
  //   relationship: "拥有",
  //   id: null,
  //   modelId: null
  // },
  // {
  //   name: "",
  //   value: "",
  //   relationship: "拥有",
  //   id: null,
  //   modelId: null
  // },
  // {
  //   name: "",
  //   value: "",
  //   relationship: "拥有",
  //   id: null,
  //   modelId: null
  // }
]);

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入模型名称" }]
  // number: [{ required: true, message: "请输入编号" }]
  // desc: [{ required: true, message: "请输入描述" }],
  // uploadPerson: [{ required: true, message: "请输入上传人" }]
};

// 属性表格列定义 - 新增属性关系列
const propertyColumns = [
  {
    title: "属性名",
    dataIndex: "name",
    key: "name",
    width: "30%"
  },
  {
    title: "属性值",
    dataIndex: "value",
    key: "value",
    width: "30%"
  },
  {
    title: "属性关系",
    dataIndex: "relationship",
    key: "relationship",
    width: "30%"
  },
  {
    title: "",
    key: "action",
    width: "10%",
    align: "center"
  }
];

// 文件上传前的处理
const beforeUploadHandler = (file: File) => {
  return beforeUpload(file);
};

// 文件变化处理
const handleFileChange = (info: any) => {
  const { file } = info;
  if (file.status !== "removed") {
    uploadedFile.value = file.originFileObj || file;
    uploadFileHandler();
  }
};



// 真实文件上传
const uploadFileHandler = async () => {
  if (!uploadedFile.value) return;

  try {
    uploading.value = true;
    uploadProgress.value = 0;
    uploadSuccess.value = false;
    previewUrl.value = "";

    // 使用统一的上传函数
    const uploadResult = await uploadFile(uploadedFile.value, (progress) => {
      uploadProgress.value = progress;
    });

    // 设置上传结果
    if (uploadResult && uploadResult.result && uploadResult.result.fileUrl) {
      formData.fileUrl = uploadResult.result.fileUrl;
      formData.fileType = uploadResult.result.fileType || "";
      formData.previewUrl = uploadResult.result.fileUrl;
      previewUrl.value = uploadResult.result.fileUrl;
    } else {
      // 兜底：为所有文件类型创建本地预览URL，确保formData.fileUrl和previewUrl有值
      const localUrl = URL.createObjectURL(uploadedFile.value);
      previewUrl.value = localUrl;
      formData.previewUrl = localUrl;
      
      // 如果接口没有返回fileUrl，使用本地URL作为fileUrl的兜底
      if (!formData.fileUrl) {
        formData.fileUrl = localUrl;
      }
      
      // 为3D模型文件（包括VRP/VRPC）设置文件类型
      if (is3DModel(uploadedFile.value)) {
        formData.fileType = uploadedFile.value.name.split('.').pop()?.toLowerCase() || '';
      }
    }

    uploading.value = false;
    uploadSuccess.value = true;
    uploadProgress.value = 100;
    message.success("文件上传成功");
  } catch (error) {
    console.error("文件上传失败:", error);
    uploading.value = false;
    uploadSuccess.value = false;
    uploadProgress.value = 0;
    
    // 上传失败时，为所有文件类型创建本地预览URL作为兜底
    if (uploadedFile.value) {
      const localUrl = URL.createObjectURL(uploadedFile.value);
      previewUrl.value = localUrl;
      formData.previewUrl = localUrl;
      
      // 如果接口没有返回fileUrl，使用本地URL作为fileUrl的兜底
      if (!formData.fileUrl) {
        formData.fileUrl = localUrl;
      }
      
      // 为3D模型文件（包括VRP/VRPC）设置文件类型
      if (is3DModel(uploadedFile.value)) {
        formData.fileType = uploadedFile.value.name.split('.').pop()?.toLowerCase() || '';
      }
      
      uploadSuccess.value = true;
      message.warning("文件上传失败，但已生成本地预览");
    } else {
      message.error("文件上传失败");
    }
  }
};

// 预览错误处理
const handlePreviewError = (event: Event) => {
  console.error("预览加载失败:", event);
  message.error("预览加载失败");
};

// 移除文件
const removeFile = () => {
  // 清理本地预览URL
  if (previewUrl.value && previewUrl.value.startsWith("blob:")) {
    URL.revokeObjectURL(previewUrl.value);
  }
  
  uploadedFile.value = null;
  uploading.value = false;
  uploadProgress.value = 0;
  uploadSuccess.value = false;
  fileList.value = [];
  formData.fileUrl = "";
  formData.previewUrl = "";
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 添加属性
const handleAddProperty = () => {
  const newProperty: Property = {
    name: "",
    value: "",
    relationship: "",
    id: null,
    modelId: null
  };
  properties.value.push(newProperty);
};

// 删除属性
const handleDeleteProperty = (index: number) => {
  // if (properties.value.length <= 1) {
  //   message.warning("至少需要保留一个属性");
  //   return;
  // }
  properties.value.splice(index, 1);
};

// 构建field对象
const buildFieldObject = (modelId: number | null): Property[] => {
  const validProperties: Property[] = [];
  console.log(properties.value);
  properties.value.forEach(prop => {
    if (prop.name.trim() && prop.value.trim() && prop.relationship) {
      validProperties.push({
        id: null, // 新增时设为null
        modelId: null, // 新增时设为null
        name: prop.name.trim(),
        relationship: prop.relationship,
        value: prop.value.trim()
      });
    }
  });

  return validProperties;
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();

    // 校验属性表格每一行
    const hasEmptyProperty = properties.value.some(
      prop =>
        !prop.name?.trim() || !prop.value?.trim() || !prop.relationship?.trim()
    );
    if (hasEmptyProperty) {
      message.error("请完善所有属性信息");
      return;
    }

    if (!formData.fileUrl || !formData.previewUrl) {
      message.error("请先上传模型文件");
      return;
    }
    loading.value = true;

    // 构建提交数据
    const submitData: ModelAddForm = {
      ...formData,
      attributeList: buildFieldObject(null)
      // uploadTime: new Date().toISOString()
    };

    // 调用新增模型接口
    await addModel(submitData);

    message.success("模型新增成功");
    emit("submit", submitData);
    handleCancel();
  } catch (error) {
    console.error("新增模型失败:", error);
    message.error("新增模型失败");
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit("update:visible", false);
  // 重置表单
  nextTick(() => {
    formRef.value?.resetFields();
    Object.assign(formData, {
      id: null,
      name: "",
      number: "",
      desc: "",
      attributeList: [],
      fileUrl: "",
      previewUrl: "",
      uploadPerson: "",
      uploadTime: ""
    });
    // 重置属性
    properties.value = [
      // {
      //   id: null,
      //   modelId: null,
      //   name: "",
      //   relationship: "拥有",
      //   value: ""
      // },
      // {
      //   id: null,
      //   modelId: null,
      //   name: "",
      //   relationship: "位于",
      //   value: ""
      // },
      // {
      //   id: null,
      //   modelId: null,
      //   name: "",
      //   relationship: "附有",
      //   value: ""
      // }
    ];
    removeFile();
  });
};

const propertyNameOptions = [
  { label: "ATA章节", value: "ATA章节" },
  { label: "SNS代码", value: "SNS代码" },
  { label: "件号", value: "件号" }
];

// 添加属性关系选项
const propertyRelationshipOptions = [
  { label: "拥有", value: "拥有" },
  { label: "位于", value: "位于" },
  { label: "附有", value: "附有" },
  { label: "包含", value: "包含" },
  { label: "属于", value: "属于" }
];

const selectorVisible = ref<boolean>(false);
const handleConfirm = dataArray => {
  dataArray.forEach(item => {
    const newProperty: Property = {
      id: item.id,
      modelId: formData.id,
      name: item.name,
      relationship: "拥有",
      value: item.value
    };
    properties.value.push(newProperty);
  });
  selectorVisible.value = false;
};

const handleSelectProperty = () => {
  selectorVisible.value = true;
};

// 添加属性名选择处理
const handlePropertyNameSelect = (value: string, record: Property) => {
  // 选择下拉选项时，直接设置值
  record.name = value;
  console.log("选择属性名:", value);
};

// 添加属性名搜索处理
const handlePropertyNameSearch = (value: string, record: Property) => {
  // 搜索时直接更新记录的值，确保手动输入被保存
  record.name = value;
  console.log("搜索属性名:", value);
};

// 添加属性名变化处理
const handlePropertyNameChange = (value: string, record: Property) => {
  // 值变化时，直接设置到记录中，支持手动输入
  record.name = value;
  console.log("属性名变化:", value);
};

// 添加属性名模糊处理
const handlePropertyNameBlur = (event: Event, record: Property) => {
  // 获取输入框的值
  const target = event.target as HTMLInputElement;
  if (target && target.value) {
    // 确保手动输入的值被保存
    record.name = target.value;
    console.log("属性名模糊处理，保存值:", target.value);
  }
};

// 添加属性关系选择处理
const handlePropertyRelationshipSelect = (value: string, record: Property) => {
  // 选择下拉选项时，直接设置值
  record.relationship = value;
};

// 添加属性关系变化处理
const handlePropertyRelationshipChange = (value: string, record: Property) => {
  // 值变化时，直接设置到记录中，支持手动输入
  record.relationship = value;
};

// 添加属性关系模糊处理
const handlePropertyRelationshipBlur = (event: Event, record: Property) => {
  // 获取输入框的值
  const target = event.target as HTMLInputElement;
  if (target && target.value) {
    // 确保手动输入的值被保存
    record.relationship = target.value;
  }
};
</script>
<template>
  <a-modal
    :open="visible"
    :confirm-loading="loading"
    width="1200px"
    :destroy-on-close="true"
    class="model-add-modal"
    @cancel="handleCancel"
  >
    <!-- 自定义标题 -->
    <template #title>
      <div class="modal-title">
        <h3>新增模型</h3>
      </div>
    </template>

    <div class="modal-content">
      <!-- 左侧文件上传区域 -->
      <div class="upload-area">
        <a-upload-dragger
          v-model:fileList="fileList"
          name="file"
          :multiple="false"
          :before-upload="beforeUploadHandler"
          :show-upload-list="false"
          class="upload-dragger"
          @change="handleFileChange"
        >
          <div class="upload-content">
            <div v-if="!uploadedFile" class="upload-placeholder">
              <CloudUploadOutlined class="upload-icon" />
              <p class="upload-text">点击或拖拽文件到此区域上传</p>
              <p class="upload-hint">支持单个文件上传</p>
            </div>

            <div v-else class="upload-preview">
              <div class="file-info">
                <FileTextOutlined class="file-icon" />
                <div class="file-details">
                  <p class="file-name">{{ uploadedFile.name }}</p>
                  <p class="file-size">
                    {{ formatFileSize(uploadedFile.size) }}
                  </p>
                </div>
                <a-button
                  type="text"
                  danger
                  size="small"
                  class="remove-btn"
                  @click.stop="removeFile"
                >
                  <DeleteOutlined />
                </a-button>
              </div>

              <!-- 上传进度 -->
              <div v-if="uploading" class="upload-progress">
                <a-progress :percent="uploadProgress" size="small" />
                <p class="progress-text">上传中...</p>
              </div>

              <!-- 上传成功 -->
              <!-- 预览区域 -->
              <div
                v-else-if="uploadSuccess && previewUrl"
                class="preview-container"
              >
                <!-- 图片预览 -->
                <div v-if="isImage(uploadedFile)" class="image-preview">
                  <img
                    :src="previewUrl"
                    :alt="uploadedFile.name"
                    class="preview-image"
                    @error="handlePreviewError"
                  />
                </div>

                <!-- 视频预览 -->
                <div v-else-if="isVideo(uploadedFile)" class="video-preview">
                  <video
                    :src="previewUrl"
                    class="preview-video"
                    controls
                    preload="metadata"
                    @error="handlePreviewError"
                  >
                    您的浏览器不支持视频播放
                  </video>
                </div>

                <!-- 3D模型预览 -->
                <div v-else-if="is3DModel(uploadedFile)" class="model-preview">
                  <div class="model-preview-placeholder">
                    <AppstoreOutlined class="model-icon" />
                    <p>3D模型预览</p>
                    <p class="model-url">{{ previewUrl }}</p>
                  </div>
                </div>

                <!-- 其他文件类型 -->
                <div v-else class="file-preview">
                  <FileOutlined class="file-preview-icon" />
                  <p>文件已上传</p>
                  <p class="file-url">{{ previewUrl }}</p>
                </div>
              </div>

              <!-- 上传成功但无预览 -->
              <div v-else-if="uploadSuccess" class="upload-success">
                <CheckCircleOutlined class="success-icon" />
                <p class="success-text">上传成功</p>
              </div>
            </div>
          </div>
        </a-upload-dragger>
      </div>

      <!-- 右侧信息表单区域 -->
      <div class="form-area">
        <!-- 基本信息表单 -->
        <div class="form-section">
          <a-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            layout="horizontal"
            :label-col="{ span: 6 }"
          >
            <a-row style="padding: 5px">
              <a-col :span="22">
                <a-form-item label="模型名称" name="name">
                  <a-input
                    v-model:value="formData.name"
                    placeholder="请输入模型名称"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="22">
                <a-form-item label="描述" name="desc">
                  <a-textarea
                    v-model:value="formData.desc"
                    placeholder="请输入描述"
                    :rows="3"
                    :autoSize="{ minRows: 2, maxRows: 6 }"
                    style="resize: none"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <!-- 属性表格 -->
        <div class="properties-section">
          <div class="properties-header">
            <h4>模型属性</h4>
            <a-button-group>
              <a-button
                type="primary"
                size="small"
                style="margin-right: 5px"
                class="add-property-btn"
                @click="handleAddProperty"
              >
                <template #icon>
                  <PlusOutlined />
                </template>
                新增
              </a-button>
              <a-button
                type="primary"
                size="small"
                class="add-property-btn"
                @click="handleSelectProperty"
              >
                <template #icon>
                  <SelectOutlined />
                </template>
                选择属性
              </a-button>
            </a-button-group>
          </div>
          <a-table
            :columns="propertyColumns"
            :data-source="properties"
            :pagination="false"
            :scroll="{ y: 360 }"
            size="small"
            class="property-table"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'name'">
                <a-select
                  v-model:value="record.name"
                  :options="propertyNameOptions"
                  placeholder="请输入属性名"
                  size="small"
                  show-search
                  allow-clear
                  :filter-option="true"
                  :not-found-content="null"
                  mode="combobox"
                  style="width: 100%"
                  @select="value => handlePropertyNameSelect(value, record)"
                  @change="value => handlePropertyNameChange(value, record)"
                  @blur="e => handlePropertyNameBlur(e, record)"
                />
              </template>
              <template v-else-if="column.key === 'value'">
                <a-input
                  v-model:value="record.value"
                  placeholder="请输入属性值"
                  size="small"
                />
              </template>
              <template v-else-if="column.key === 'relationship'">
                <a-select
                  v-model:value="record.relationship"
                  :options="propertyRelationshipOptions"
                  placeholder="请选择或输入关系"
                  size="small"

                  allow-clear
                  :filter-option="true"
                  :not-found-content="null"
                  mode="combobox"
                  style="width: 100%"
                  @select="value => handlePropertyRelationshipSelect(value, record)"
                  @change="value => handlePropertyRelationshipChange(value, record)"
                  @blur="e => handlePropertyRelationshipBlur(e, record)"
                />
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button
                  type="text"
                  danger
                  size="small"
                  @click="handleDeleteProperty(index)"
                >
                  <template #icon> <DeleteOutlined /></template>
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" :loading="loading" @click="handleSubmit">
          保存
        </a-button>
      </div>
    </template>
    <modelattr-selector
      multiple
      :visible="selectorVisible"
      @close="selectorVisible = $event"
      @confirm="handleConfirm"
    />
  </a-modal>
</template>

<style lang="scss" scoped>
.modal-title {
  background-color: var(--gray-100);
  margin: -24px -24px 24px -24px;
  padding: var(--spacing-6);

  h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
    color: var(--gray-900);
    margin: 0;
  }
}

.modal-content {
  display: flex;
  height: 600px;
  gap: var(--spacing-6);
}

.upload-area {
  width: 65%;
  display: flex;
  flex-direction: column;
}

.upload-dragger {
  flex: 1;

  :deep(.ant-upload-drag) {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius-md);
    background-color: var(--gray-50);
    transition: all var(--transition-normal);

    &:hover {
      border-color: var(--primary-color);
      background-color: rgba(24, 144, 255, 0.05);
    }
  }
}

.upload-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
}

.upload-placeholder {
  text-align: center;

  .upload-icon {
    font-size: 48px;
    color: var(--gray-400);
    margin-bottom: var(--spacing-4);
  }

  .upload-text {
    font-size: var(--text-base);
    color: var(--gray-600);
    margin-bottom: var(--spacing-2);
  }

  .upload-hint {
    font-size: var(--text-sm);
    color: var(--gray-500);
    margin: 0;
  }
}

.upload-preview {
  width: 100%;
  text-align: center;
}

.file-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background-color: white;
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-200);
  margin-bottom: var(--spacing-4);

  .file-icon {
    font-size: 24px;
    color: var(--primary-color);
    flex-shrink: 0;
  }

  .file-details {
    flex: 1;
    text-align: left;

    .file-name {
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--gray-900);
      margin: 0 0 4px 0;
      word-break: break-all;
    }

    .file-size {
      font-size: var(--text-xs);
      color: var(--gray-500);
      margin: 0;
    }
  }

  .remove-btn {
    flex-shrink: 0;
    color: var(--error-color);

    &:hover {
      background-color: rgba(255, 77, 79, 0.1);
    }
  }
}

.upload-progress {
  .progress-text {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin-top: var(--spacing-2);
    margin-bottom: 0;
  }
}

.upload-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);

  .success-icon {
    font-size: 32px;
    color: var(--success-color);
  }

  .success-text {
    font-size: var(--text-sm);
    color: var(--success-color);
    margin: 0;
  }
}

.form-area {
  width: 35%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.form-section {
  margin-bottom: var(--spacing-6);
}

.form-vertical {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.properties-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: var(--spacing-4);
}

.properties-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);

  h4 {
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    color: var(--gray-900);
    margin: 0;
  }
}

.add-property-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-1);
}

.property-table {
  flex: 1;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-3);
}
:deep(.form-section .ant-row) {
  padding: 5px;
}
// 属性表格样式
:deep(.property-table .ant-table-thead > tr > th) {
  background-color: #eff7fd !important;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
  padding: 8px 12px;
}

:deep(.property-table .ant-table-tbody > tr > td) {
  font-size: var(--text-sm);
  border-bottom: 1px solid var(--gray-200);
  padding: 8px 12px;
}

:deep(.property-table .ant-table-tbody > tr:hover > td) {
  background-color: var(--gray-50);
}

:deep(.property-table .ant-table) {
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

:deep(.property-table .ant-table-container) {
  border: 0;
}

// 表格内输入框样式
:deep(.property-table .ant-input) {
  border: 0;
  box-shadow: none;
  background-color: transparent;

  &:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    background-color: white;
  }
}

// 表格内选择器样式
:deep(.property-table .ant-select) {
  .ant-select-selector {
    border: 0;
    box-shadow: none;
    background-color: transparent;
  }

  &.ant-select-focused .ant-select-selector {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    background-color: white;
  }
}

// 属性名输入区域样式
.property-name-input {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.property-name-input .ant-input {
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);

  &:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }
}

.property-name-input .ant-select {
  .ant-select-selector {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    background-color: var(--gray-50);

    &:hover {
      border-color: var(--primary-color);
    }
  }

  &.ant-select-focused .ant-select-selector {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }
}

// 表单样式
:deep(.ant-form-item) {
  margin-bottom: 0;
}

:deep(.ant-form-item-label) {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
}

:deep(.ant-input),
:deep(.ant-textarea) {
  width: 100%;
}

// 删除按钮样式
:deep(.ant-btn-text.ant-btn-dangerous) {
  color: var(--error-color);

  &:hover {
    color: #d32f2f;
    background-color: rgba(255, 77, 79, 0.1);
  }

  &:disabled {
    color: var(--gray-300);

    &:hover {
      color: var(--gray-300);
      background-color: transparent;
    }
  }
}

// 自定义标题样式
:deep(.ant-modal-header) {
  padding: 0;
  border: 0;
}

:deep(.ant-modal-title) {
  padding: 0;
}

// 弹窗样式覆盖
:deep(.model-add-modal .ant-modal-content) {
  overflow: hidden;
}

:deep(.model-add-modal .ant-modal-body) {
  padding: var(--spacing-6);
  max-height: 70vh;
  overflow: auto;
}
</style>
