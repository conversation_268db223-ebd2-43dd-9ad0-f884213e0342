package com.sbtr.workflow.service;

import com.sbtr.workflow.model.ActMyCategory;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;

/**
 * 流程分类表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-12 06:00:42
 */
public interface ActMyCategoryService extends WorkflowBaseService<ActMyCategory, String> {

    /**
     * 分页
     *
     * @return
     * @Param pageParam 分页参数
     * @Param categoryName 分类名
     */
    PageSet<ActMyCategory> getPageSet(PageParam pageParam, String categoryName);

    /**
     * 批量删除
     *
     * @return
     * @Param uuid 主键
     */
	int executeDeleteBatch(String[] uuids);

    Boolean getListByCodeAndUuid(String categoryCode, String uuid);
}
