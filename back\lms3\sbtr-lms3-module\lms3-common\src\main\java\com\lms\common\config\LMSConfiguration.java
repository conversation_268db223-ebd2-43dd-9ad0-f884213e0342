package com.lms.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "lms")
@Data
public class LMSConfiguration {

    private String storagepath;

    private String temppath;

    private String uploadpath;

    private String scormStoragePath;

    private String viewurl;

    private String localhost;

    private Boolean initlabel;

    /**
     * 是否检查客户端ip与浏览器类型
     */
    private Boolean clientCheck;

    private String minioDataPath;

    private String bucketName;

}
