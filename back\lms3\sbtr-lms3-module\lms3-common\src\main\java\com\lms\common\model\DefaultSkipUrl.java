package com.lms.common.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 配置默认跳过请求地址
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
public class DefaultSkipUrl {
    private static final List<String> DEFAULT_SKIP_URL = new ArrayList<>();

    static {
        //document
        DEFAULT_SKIP_URL.add("/document/oss/wpsOssUpload");
        DEFAULT_SKIP_URL.add("/swagger-resources");
        DEFAULT_SKIP_URL.add("/favicon.ico");
        DEFAULT_SKIP_URL.add("/document/v2/api-docs");
        //education
        DEFAULT_SKIP_URL.add("/education/v2/api-docs");
        //erp
        DEFAULT_SKIP_URL.add("/erp/v2/api-docs");
        //form
        DEFAULT_SKIP_URL.add("/form/v2/api-docs");
        //hr
        DEFAULT_SKIP_URL.add("/hr/v2/api-docs");
        //mdata
        DEFAULT_SKIP_URL.add("/mdata/user/getUserByUserNameId");
        DEFAULT_SKIP_URL.add("/mdata/nail/login");
        DEFAULT_SKIP_URL.add("/mdata/weixin/getUserInfo");
        DEFAULT_SKIP_URL.add("/mdata/weiXinMiniProgram/login");
        DEFAULT_SKIP_URL.add("/mdata/v2/api-docs");
        //message
        DEFAULT_SKIP_URL.add("/message/socket/chart");
        DEFAULT_SKIP_URL.add("/message/mongodb/chart");
        DEFAULT_SKIP_URL.add("/message/v2/api-docs");
        //office
        DEFAULT_SKIP_URL.add("/office/po/poserver.zz");
        DEFAULT_SKIP_URL.add("/office/pageoffice.js");
        DEFAULT_SKIP_URL.add("/office/pageOfficeDemo/word");
        DEFAULT_SKIP_URL.add("/office/pageOfficeDemo/excel");
        DEFAULT_SKIP_URL.add("/office/pageOfficeDemo/pdf");
        DEFAULT_SKIP_URL.add("/office/pageOfficeDemo/save");
        DEFAULT_SKIP_URL.add("/office/pageOfficeDemo/pdf");
        DEFAULT_SKIP_URL.add("/office/poserver.zz");
        DEFAULT_SKIP_URL.add("/office/posetup.exe");
        DEFAULT_SKIP_URL.add("/office/pobstyle.css");
        DEFAULT_SKIP_URL.add("/pageoffice.js");
        DEFAULT_SKIP_URL.add("/office/v1/3rd/file/info");
        DEFAULT_SKIP_URL.add("/office/v1/3rd/file/online");
        DEFAULT_SKIP_URL.add("/office/v2/api-docs");
        //report
        DEFAULT_SKIP_URL.add("/ureport/v2/api-docs");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/venderjs/jquery.contextMenu.min.js");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/venderjs/jquery.min.js");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/css/bootstrap.min.css");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/venderjs/completer.min.js");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/venderjs/bootstrap-colorpicker.min.js");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/js/designer.bundle.js");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/css/bootstrap.min.css.map");
        DEFAULT_SKIP_URL.add("/report/ureport/designer/loadReport");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/expand-down.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/fonts/glyphicons-halflings-regular.woff2");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/fonts/glyphicons-halflings-regular.woff");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/fonts/glyphicons-halflings-regular.ttf");
        DEFAULT_SKIP_URL.add("/report/ureport/datasource/loadBuildinDatasources");
        DEFAULT_SKIP_URL.add("/report/ureport/datasource/buildDatabaseTables");
        DEFAULT_SKIP_URL.add("/report/ureport/datasource/previewData");
        DEFAULT_SKIP_URL.add("/report/ureport/datasource/buildFields");
        DEFAULT_SKIP_URL.add("/report/ureport/designer/savePreviewData");
        DEFAULT_SKIP_URL.add("/report/ureport/designer/loadReportProviders");
        DEFAULT_SKIP_URL.add("/report/ureport/preview");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/venderjs/chartjs-plugin-datalabels.min.js");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/js/preview.bundle.js");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/venderjs/Chart.bundle.min.js");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/venderjs/bootstrap-datetimepicker.js");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/venderjs/bootstrap.min.js");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/print.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/word.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/excel-with-paging-sheet.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/pdf-direct-print.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/pdf-print.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/pdf.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/excel.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/excel-paging.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/pdf/show");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/loading.gif");
        DEFAULT_SKIP_URL.add("/report/ureport/preview/loadPagePaper");
        DEFAULT_SKIP_URL.add("/report/ureport/word");
        DEFAULT_SKIP_URL.add("/report/ureport/preview/loadPrintPages");
        DEFAULT_SKIP_URL.add("/report/ureport/pdf");
        DEFAULT_SKIP_URL.add("/report/ureport/excel");
        DEFAULT_SKIP_URL.add("/report/ureport/excel/paging");
        DEFAULT_SKIP_URL.add("/report/ureport/excel/sheet");
        DEFAULT_SKIP_URL.add("/report/ureport/designer/loadReportProviders");
        DEFAULT_SKIP_URL.add("/report/ureport/designer/deleteReportFile");
        DEFAULT_SKIP_URL.add("/report/ureport/searchFormDesigner");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/venderjs/jquery-ui.min.js");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/js/searchform.bundle.js");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/expr-expand-down.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/qrcode.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/barcode.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/designer");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/expression.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/datasource/testConnection");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/css/handsontable.min.css");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/js/common.bundle.js");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/misboot-print.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/misboot-pdf-direct-print.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/misboot-pdf-print.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/misboot-pdf.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/misboot-word.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/misboot-excel.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/misboot-excel-paging.svg");
        DEFAULT_SKIP_URL.add("/report/ureport/res/ureport-asserts/icons/misboot-excel-paging-sheet.svg");

        //gateway
        DEFAULT_SKIP_URL.add("/gateway/login");
        DEFAULT_SKIP_URL.add("/gateway/dingding/ddLogin");
        DEFAULT_SKIP_URL.add("/gateway/genCodes");
        DEFAULT_SKIP_URL.add("/gateway/login/login");
        DEFAULT_SKIP_URL.add("/gateway/jwtLogin");
        DEFAULT_SKIP_URL.add("/system/jwtLogin");
        DEFAULT_SKIP_URL.add("/gateway/codeItem/getListByCodeSetIdAndLevelId");
        DEFAULT_SKIP_URL.add("/gateway/codeItem/getListByPid");
        DEFAULT_SKIP_URL.add("/gateway/codeItem/getFatherIds");
        DEFAULT_SKIP_URL.add("/gateway/codeItem/getZoneFillbackData");
        DEFAULT_SKIP_URL.add("/gateway/attachment/showPic");
        DEFAULT_SKIP_URL.add("/gateway/weixin/weixinUrl");
        DEFAULT_SKIP_URL.add("/gateway/weixin/weixinLogin");
        DEFAULT_SKIP_URL.add("/gateway/config/getAllConfigData");
        DEFAULT_SKIP_URL.add("/gateway/config/getAllConfigData");
        DEFAULT_SKIP_URL.add("/gateway/v2/api-docs");
        DEFAULT_SKIP_URL.add("/gateway/sysEnterpriseRegister/save");
        DEFAULT_SKIP_URL.add("/gateway/sysEnterpriseRegister/search");
        DEFAULT_SKIP_URL.add("/gateway/updateApp");
        DEFAULT_SKIP_URL.add("/gateway/bimface/callback");
        //workflow
        DEFAULT_SKIP_URL.add("/workflow/v2/api-docs");
        DEFAULT_SKIP_URL.add("/workflow/apiFlowableModel/downLoadXmlByModelId");
        DEFAULT_SKIP_URL.add("/workflow/apiFlowableTask/getFlowImgByExecutionId");
        //shop
        DEFAULT_SKIP_URL.add("/shop/shopCategory/getCategorysByTypeAndLevelId");
        DEFAULT_SKIP_URL.add("/shop/shopCategory/getListByPid");
        DEFAULT_SKIP_URL.add("/shop/shopGoods/getPageSet");
        DEFAULT_SKIP_URL.add("/shop/shopGoods/getPageSet");
        DEFAULT_SKIP_URL.add("/shop/v2/api-docs");
        //document
        DEFAULT_SKIP_URL.add("/document/minio/showPic");
        DEFAULT_SKIP_URL.add("/document/v1/3rd/file/info");
        DEFAULT_SKIP_URL.add("/document/v1/3rd/file/online");
        DEFAULT_SKIP_URL.add("/document/v2/api-docs");

        //未知 不知道谁写的
        DEFAULT_SKIP_URL.add("/fastdfs/fdfs/download_filebyte");
        DEFAULT_SKIP_URL.add("/third/nail/login");
        DEFAULT_SKIP_URL.add("/third/weixin/getUserInfo");
        DEFAULT_SKIP_URL.add("/third/weiXinMiniProgram/login");

    }


    public static List<String> getDefaultSkipUrl() {
        return DEFAULT_SKIP_URL;
    }


}
