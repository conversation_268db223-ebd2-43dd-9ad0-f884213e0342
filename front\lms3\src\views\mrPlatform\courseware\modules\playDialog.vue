<template>
  <div class="play-dialog">
    <div v-if="courseware" class="courseware-preview">
      <!-- 课件信息 -->
      <div class="courseware-info">
        <h3>{{ courseware.name }}</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">课件编号：</span>
            <span class="value">{{ courseware.number }}</span>
          </div>
          <div class="info-item">
            <span class="label">课件类型：</span>
            <span class="value">{{ getCoursewareTypeText(courseware.coursewaretype) }}</span>
          </div>
          <div class="info-item">
            <span class="label">版本：</span>
            <span class="value">{{ courseware.version }}</span>
          </div>
          <div class="info-item">
            <span class="label">责任人：</span>
            <span class="value">{{ courseware.principal }}</span>
          </div>
          <div class="info-item">
            <span class="label">责任单位：</span>
            <span class="value">{{ courseware.dutyunit }}</span>
          </div>
          <div class="info-item">
            <span class="label">修改时间：</span>
            <span class="value">{{ courseware.modifydate }}</span>
          </div>
        </div>
      </div>

      <!-- 课件预览区域 -->
      <div class="preview-area">
        <div v-if="courseware.location" class="preview-content">
          <!-- 图片预览 -->
          <div v-if="isImageType" class="image-preview">
            <img :src="courseware.location" :alt="courseware.name" />
          </div>

          <!-- 视频预览 -->
          <div v-else-if="isVideoType" class="video-preview">
            <video controls :src="courseware.location" style="width: 100%; max-height: 400px;">
              您的浏览器不支持视频播放
            </video>
          </div>

          <!-- 音频预览 -->
          <div v-else-if="isAudioType" class="audio-preview">
            <audio controls :src="courseware.location" style="width: 100%;">
              您的浏览器不支持音频播放
            </audio>
          </div>

          <!-- 文档预览 -->
          <div v-else-if="isDocumentType" class="document-preview">
            <div class="document-placeholder">
              <FileTextOutlined class="document-icon" />
              <p>文档预览</p>
              <a-button type="primary" @click="openDocument">
                打开文档
              </a-button>
            </div>
          </div>

          <!-- 其他类型 -->
          <div v-else class="other-preview">
            <div class="other-placeholder">
              <FileOutlined class="other-icon" />
              <p>文件预览</p>
              <a-button type="primary" @click="downloadFile">
                下载文件
              </a-button>
            </div>
          </div>
        </div>

        <!-- 无文件地址时的提示 -->
        <div v-else class="no-file">
          <ExclamationCircleOutlined class="no-file-icon" />
          <p>暂无预览文件</p>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-space>
          <a-button @click="handleClose">关闭</a-button>
          <a-button type="primary" @click="downloadFile" v-if="courseware.location">
            下载
          </a-button>
          <a-button type="primary" @click="openInNewTab" v-if="courseware.location">
            新窗口打开
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 无课件数据时的提示 -->
    <div v-else class="no-courseware">
      <ExclamationCircleOutlined class="no-courseware-icon" />
      <p>未找到课件信息</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from "vue";
import {
  FileTextOutlined,
  FileOutlined,
  ExclamationCircleOutlined
} from "@ant-design/icons-vue";

interface Props {
  courseware: any;
}

interface Emits {
  (e: "close"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 计算属性：判断课件类型
const isImageType = computed(() => {
  if (!props.courseware?.coursewaretype) return false;
  return ['image', 'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'].includes(props.courseware.coursewaretype.toLowerCase());
});

const isVideoType = computed(() => {
  if (!props.courseware?.coursewaretype) return false;
  return ['video', 'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(props.courseware.coursewaretype.toLowerCase());
});

const isAudioType = computed(() => {
  if (!props.courseware?.coursewaretype) return false;
  return ['audio', 'mp3', 'wav', 'ogg', 'aac', 'flac'].includes(props.courseware.coursewaretype.toLowerCase());
});

const isDocumentType = computed(() => {
  if (!props.courseware?.coursewaretype) return false;
  return ['document', 'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'txt'].includes(props.courseware.coursewaretype.toLowerCase());
});

// 获取课件类型文本
function getCoursewareTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    'document': '文档',
    'video': '视频',
    'audio': '音频',
    'image': '图片',
    'other': '其他'
  };
  return typeMap[type] || type;
}

// 打开文档
function openDocument() {
  if (props.courseware?.location) {
    window.open(props.courseware.location, '_blank');
  }
}

// 下载文件
function downloadFile() {
  if (props.courseware?.location) {
    const link = document.createElement('a');
    link.href = props.courseware.location;
    link.download = props.courseware.name || 'courseware';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

// 新窗口打开
function openInNewTab() {
  if (props.courseware?.location) {
    window.open(props.courseware.location, '_blank');
  }
}

// 关闭对话框
function handleClose() {
  emit("close");
}
</script>

<style lang="scss" scoped>
.play-dialog {
  .courseware-preview {
    .courseware-info {
      margin-bottom: 24px;

      h3 {
        margin: 0 0 16px 0;
        color: #262626;
        font-size: 18px;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;

        .info-item {
          display: flex;
          align-items: center;

          .label {
            color: #8c8c8c;
            font-size: 14px;
            min-width: 80px;
          }

          .value {
            color: #262626;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }

    .preview-area {
      margin-bottom: 24px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      overflow: hidden;

      .preview-content {
        min-height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fafafa;

        .image-preview {
          width: 100%;
          text-align: center;

          img {
            max-width: 100%;
            max-height: 400px;
            object-fit: contain;
          }
        }

        .video-preview {
          width: 100%;
          text-align: center;
        }

        .audio-preview {
          width: 100%;
          text-align: center;
          padding: 20px;
        }

        .document-preview,
        .other-preview {
          text-align: center;
          padding: 40px;

          .document-icon,
          .other-icon {
            font-size: 48px;
            color: #d9d9d9;
            margin-bottom: 16px;
          }

          p {
            color: #8c8c8c;
            margin-bottom: 16px;
          }
        }
      }

      .no-file {
        min-height: 200px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #fafafa;

        .no-file-icon {
          font-size: 48px;
          color: #d9d9d9;
          margin-bottom: 16px;
        }

        p {
          color: #8c8c8c;
          margin: 0;
        }
      }
    }

    .action-buttons {
      text-align: center;
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;
    }
  }

  .no-courseware {
    text-align: center;
    padding: 60px 20px;

    .no-courseware-icon {
      font-size: 48px;
      color: #d9d9d9;
      margin-bottom: 16px;
    }

    p {
      color: #8c8c8c;
      margin: 0;
    }
  }
}
</style>
