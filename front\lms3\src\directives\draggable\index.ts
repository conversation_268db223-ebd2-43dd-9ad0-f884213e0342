import { nextTick } from "vue";
import type { Directive, DirectiveBinding } from "vue";

//  TrDialog暂时弃用
interface DragModalOptions {
  /** 是否启用拖拽 */
  enabled?: boolean;
  /** 是否销毁时关闭 */
  destroyOnClose?: boolean;
}

interface DragModalElement extends HTMLElement {
  _dragModal?: {
    header?: HTMLElement;
    modal?: HTMLElement;
    left?: number;
    top?: number;
    isDragging?: boolean;
    startX?: number;
    startY?: number;
    headerLeft?: number;
    headerTop?: number;
  };
}

/** 拖拽模态框指令，弥补 modal 组件不能拖动的缺陷 */
export const dragModal: Directive = {
  mounted(el: DragModalElement, binding: DirectiveBinding<DragModalOptions>) {
    const options = binding.value || {};
    const enabled = options.enabled !== false; // 默认启用

    if (!enabled) return;

    // 检查元素是否为有效的DOM元素
    if (!(el instanceof HTMLElement)) {
      console.warn(
        "[Directive: drag-modal]: Element is not a valid HTMLElement"
      );
      return;
    }

    el._dragModal = {};

    const initDrag = () => {
      nextTick(() => {
        // 查找ant-design-vue的modal和header元素
        // 首先在当前元素中查找
        let modal = el.querySelector(".ant-modal") as HTMLElement;
        let header = el.querySelector(".ant-modal-header") as HTMLElement;

        // 如果没找到，尝试在document中查找最近的modal
        if (!modal || !header) {
          // 查找当前元素或其父元素中的modal
          let currentEl = el;
          while (currentEl && currentEl !== document.body) {
            modal = currentEl.querySelector(".ant-modal") as HTMLElement;
            if (modal) {
              header = modal.querySelector(".ant-modal-header") as HTMLElement;
              if (header) break;
            }
            currentEl = currentEl.parentElement as HTMLElement;
          }
        }

        // 如果还是没找到，尝试在document中查找
        if (!modal || !header) {
          const modals = document.querySelectorAll(".ant-modal");
          for (const modalEl of modals) {
            const headerEl = modalEl.querySelector(".ant-modal-header");
            if ((headerEl && modalEl.contains(el)) || el.contains(modalEl)) {
              modal = modalEl as HTMLElement;
              header = headerEl as HTMLElement;
              break;
            }
          }
        }

        if (!modal || !header) {
          console.warn(
            "[Directive: drag-modal]: Modal or header element not found"
          );
          return;
        }

        el._dragModal!.modal = modal;
        el._dragModal!.header = header;

        // 设置遮罩层可滚动
        setTimeout(() => {
          document.body.style.width = "100%";
          document.body.style.overflowY = "inherit";
        }, 0);

        // 鼠标变成可移动的指示
        header.style.cursor = "move";

        // 初始化位置
        let left = 0;
        let top = 0;

        // 未定义 destroyOnClose 时，dom未被销毁，关闭弹窗再次打开，弹窗会停留在上一次拖动的位置
        if (!options.destroyOnClose) {
          left = (modal as any).left || 0;
          top = (modal as any).top || 0;
        }
        // top 初始值为 offsetTop
        top = top || modal.offsetTop;

        el._dragModal!.left = left;
        el._dragModal!.top = top;

        // 绑定拖拽事件
        header.addEventListener("mousedown", handleMouseDown);
      });
    };

    const handleMouseDown = (e: MouseEvent) => {
      e.preventDefault();
      const { modal, header } = el._dragModal!;
      if (!modal || !header) return;

      el._dragModal!.isDragging = true;
      el._dragModal!.startX = e.clientX;
      el._dragModal!.startY = e.clientY;
      el._dragModal!.headerLeft = header.offsetLeft;
      el._dragModal!.headerTop = header.offsetTop;

      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (!el._dragModal!.isDragging) return;

      const { modal, startX, startY, headerLeft, headerTop, left, top } =
        el._dragModal!;
      if (
        !modal ||
        startX === undefined ||
        startY === undefined ||
        headerLeft === undefined ||
        headerTop === undefined ||
        left === undefined ||
        top === undefined
      )
        return;

      const endX = e.clientX;
      const endY = e.clientY;

      const newLeft = headerLeft + (endX - startX) + left;
      const newTop = headerTop + (endY - startY) + top;

      modal.style.left = newLeft + "px";
      modal.style.top = newTop + "px";

      (modal as any).left = newLeft;
      (modal as any).top = newTop;
    };

    const handleMouseUp = () => {
      if (!el._dragModal!.isDragging) return;

      const { modal } = el._dragModal!;
      if (modal) {
        el._dragModal!.left = (modal as any).left;
        el._dragModal!.top = (modal as any).top;
      }

      el._dragModal!.isDragging = false;
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };

    // 清理函数
    const cleanup = () => {
      const { header } = el._dragModal!;
      if (header) {
        header.removeEventListener("mousedown", handleMouseDown);
      }
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };

    // 存储清理函数以便后续使用
    (el as any)._dragModalCleanup = cleanup;

    // 初始化拖拽
    initDrag();
  },

  updated(el: DragModalElement, binding: DirectiveBinding<DragModalOptions>) {
    const options = binding.value || {};
    const enabled = options.enabled !== false;

    // 检查元素是否为有效的DOM元素
    if (!(el instanceof HTMLElement)) {
      return;
    }

    if (!enabled && el._dragModal) {
      // 如果禁用拖拽，清理相关事件
      const cleanup = (el as any)._dragModalCleanup;
      if (cleanup) {
        cleanup();
        el._dragModal = undefined;
      }
    } else if (enabled && !el._dragModal) {
      // 如果启用拖拽但未初始化，重新初始化
      this.mounted(el, binding);
    }
  },

  unmounted(el: DragModalElement) {
    // 检查元素是否为有效的DOM元素
    if (!(el instanceof HTMLElement)) {
      return;
    }

    // 清理事件监听器
    const cleanup = (el as any)._dragModalCleanup;
    if (cleanup) {
      cleanup();
    }
    el._dragModal = undefined;
  }
};
