/**
 * FileName:	ClientDepartment.java
 * Author:		ji<PERSON><PERSON><PERSON>
 * Time:		2013-3-4上午11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.base.department.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.base.department.mapper.DepartmentMapper;
import com.lms.base.department.service.DepartmentService;
import com.lms.base.feign.model.Department;
import com.lms.common.feign.dto.DepartmentTree;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.StringHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 保存项目的相关信息
 */
@Service("DepartmentService")
public class DepartmentServiceImpl extends BaseServiceImpl<DepartmentMapper, Department> implements DepartmentService {

    @Resource
    private DepartmentMapper departmentMapper;

    public int existDepartType(String departmentname, String id, String pid) {
        int result = 0;
        LambdaQueryWrapper<Department> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Department::getName, departmentname);
        wrapper.eq(Department::getStatus, 1);
        List<Department> list = this.list(wrapper);
        for (Department dept : list) {
            if (dept.getId().equals(id))
                continue;
            result = 1;
            if (dept.getPid().equals(pid)) {
                result = 2;
                break;
            }
        }
        return result;
    }

    public void batchDeleteDepartment(List<String> ids) {
        this.removeBatchByIds(ids);
    }

    public String getDepartmentNamesByIds(String ids) {
        String[] idArray = StringHelper.string2Array(ids, ",");
        String str = "";
        if (idArray != null && idArray.length > 0) {
            for (String id : idArray) {
                String name = getDepartmentNameById(id);
                if (!StringHelper.isEmpty(str)) {
                    str += ",";
                }
                str += name;
            }
        }
        return str;
    }

    private String getDepartmentNameById(String id) {
        Department department = this.getById(id);
        if (department != null) {
            return department.getName();
        } else {
            return "";
        }
    }

    public List<Department> getValidDepartments() {
        LambdaQueryWrapper<Department> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Department::getStatus, 1);
        wrapper.orderByDesc(Department::getSeqno);
        return this.list(wrapper);
    }

    public List<Department> getValidDepartmentsByPid(String pid) {
        String sql = "select * from p_department  where status = 1 ";
        sql += ObjectUtil.isEmpty(pid) || pid.equals("1") ? " and (pid='' or pid is null)  order by seqno" : " and pid='" + pid + "'  order by seqno";
        return departmentMapper.getValidDepartmentsByPid(sql);
    }

    public List<Department> getAllDepartmentsByPid(String pid) {
        String parentId = StringHelper.isEmpty(pid) ? "1" : pid;
        String sql = "select * from p_department where status=1 and FIND_IN_SET(id,getDepartChildList('" + parentId + "')) order by seqno";
        return departmentMapper.getAllDepartmentsByPid(sql);
    }

    public List<DepartmentTree> setObjectTree(String pid) {
        List<Department> mapList = this.getAllDepartmentsByPid(pid);
        List<DepartmentTree> departList = new ArrayList<>();
        mapList.forEach(d -> {
            DepartmentTree treeNode = new DepartmentTree();
            BeanUtils.copyProperties(d, treeNode);
            departList.add(treeNode);
        });
        return DepartmentTree.buildTree(departList, pid);
    }

    public JSONObject setObjetDetails(Department dept) {
        JSONObject jb = new JSONObject();
        jb.put("id", StringHelper.null2String(dept.getId()));
        jb.put("name", StringHelper.null2String(dept.getName()));
        jb.put("code", StringHelper.null2String(dept.getCode()));
        jb.put("shortname", StringHelper.null2String(dept.getShortname()));
        jb.put("seqno", dept.getSeqno());
        jb.put("status", StringHelper.null2String(dept.getStatus()));
        jb.put("orgFlag", dept.getOrgFlag());
        jb.put("mlevel", dept.getMlevel());
        jb.put("mlimit", dept.getMlimit());
        String pid = StringHelper.null2String(dept.getPid());
        jb.put("pid", pid);
        if (!pid.isEmpty()) {
            Department parent = this.getById(dept.getPid());
            if (parent != null) {
                jb.put("parentname", StringHelper.null2String(parent.getName()));
            } else {
                jb.put("parentname", "");
            }
        } else {
            jb.put("parentname", "");
        }
        String code = StringHelper.null2String(dept.getId());
        List<Department> departments = this.getValidDepartmentsByPid(code);
        if (departments.size() > 0) {
            JSONArray jbcArray = new JSONArray();
            for (Department department : departments) {
                JSONObject jbc = this.setObjetDetails(department);
                jbcArray.add(jbc);
            }
            jb.put("children", jbcArray);
        }
        return jb;
    }

}
