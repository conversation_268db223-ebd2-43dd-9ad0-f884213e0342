package com.lms.base.operationanalysis.service;

import com.lms.base.operationanalysis.model.OperationAnalysis;

import java.util.List;

public interface OperationAnalysisService {

    void save(OperationAnalysis entity);

    void saveBatch(List<OperationAnalysis> entities);

    void deleteById(String id);

    void deleteBatch(List<String> ids);

    OperationAnalysis findById(String id);

    List<OperationAnalysis> findByIds(List<String> ids);

    List<OperationAnalysis> queryList(java.util.Map<String, Object> conditions, int page, int size);
}
