package com.sbtr.workflow.bean;

import org.flowable.bpmn.model.BpmnModel;
import org.flowable.image.impl.DefaultProcessDiagramGenerator;

import java.awt.image.BufferedImage;
import java.util.List;

/**
 * 字体配置
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-08-16 17:35:23
 */
public class CustomProcessDiagramGenerator extends DefaultProcessDiagramGenerator {

    protected String activityFontName = "宋体";
    protected String labelFontName = "微软雅黑";
    protected String annotationFontName = "黑体";

    public CustomProcessDiagramGenerator(final double scaleFactor) {
        super(scaleFactor);
    }

    public CustomProcessDiagramGenerator() {
        super();
    }

    @Override
    public BufferedImage generateImage(BpmnModel bpmnModel, String imageType,
                                       List<String> highLightedActivities, List<String> highLightedFlows,
                                       String activityFontName, String labelFontName, String annotationFontName, ClassLoader customClassLoader,
                                       double scaleFactor, boolean drawSequenceFlowNameWithNoLabelDI) {
        return generateImage(bpmnModel, imageType,
                highLightedActivities, highLightedFlows,
                this.activityFontName, this.labelFontName, this.annotationFontName, customClassLoader,
                scaleFactor, drawSequenceFlowNameWithNoLabelDI);
    }

}
