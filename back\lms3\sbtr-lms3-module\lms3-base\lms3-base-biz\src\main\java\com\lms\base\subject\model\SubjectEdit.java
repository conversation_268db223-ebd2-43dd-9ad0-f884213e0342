package com.lms.base.subject.model;

import java.util.ArrayList;
import java.util.List;

import com.lms.base.feign.model.Subject;
import com.lms.common.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SubjectEdit extends BaseModel {
    /**
     *
     */
    private static final long serialVersionUID = 4457560925053984411L;
    private String id;
    private String title;
    private String content;
    private Integer onlyexam;
    private String type;
    private String principal;
    private String dutyunit;
    private String levelid;
    private String specialid;
    private String equipmentid;
    private String correctresponse;
    private ArrayList<String> multicorrect;
    private String componentid;
    private String contenttext;
    private String remark;
    private String personlevelid;// ?

    private String item1;
    private String item2;
    private String item3;
    private String item4;
    private String attach;
    private String imgUrl;
    private String softcode;

    public Subject toSubject(Subject subject) {
        Subject s = null;
        if (subject == null) {
            s = new Subject();
            s.setId(id);
            s.setTitle(title);
            s.setLevelid(levelid);
            s.setEquipmentid(equipmentid);
            s.setSpecialid(specialid);
            s.setPrincipal(principal);
            s.setDutyunit(dutyunit);
            s.setOnlyexam(onlyexam);
            s.setType(type);
            s.setContent(parseContent());
            if ("3ef580ec56ae436eb79b91b25d1a078e".equals(type)) {
                s.setCorrectresponse(correctresponse);
            } else {
                s.setCorrectresponse(correctresponse);
            }
            s.setComponentid(componentid);
            s.setPersonlevelid(personlevelid);
            s.setAttach(attach);
            s.setSoftcode(softcode);
        } else {
            s = subject;
            s.setTitle(title);
            s.setLevelid(levelid);
            s.setEquipmentid(equipmentid);
            s.setSpecialid(specialid);
            s.setPrincipal(principal);
            s.setDutyunit(dutyunit);
            s.setOnlyexam(onlyexam);
            s.setContent(parseContent());
            if ("3ef580ec56ae436eb79b91b25d1a078e".equals(type)) {
                s.setCorrectresponse(correctresponse);
            } else {
                s.setCorrectresponse(correctresponse);
            }
//			s.setComponentid(componentid);
            s.setPersonlevelid(personlevelid);
            s.setAttach(attach);
            s.setSoftcode(softcode);
        }
        return s;
    }

    private String parseCorrectReponse(List<String> correct) {
        if (correct == null || correct.size() == 0)
            return "";
        StringBuilder sb = new StringBuilder();
        for (String s : correct) {
            if (sb.length() > 0)
                sb.append("," + s);
            else
                sb.append(s);
        }
        return sb.toString();
    }

    private String parseContent() {
        if ("9e47efc0ce894454856a80171e1e6efe".equals(this.getType())// 单选题
                || "3ef580ec56ae436eb79b91b25d1a078e".equals(this.getType())) {// 多选题
            this.content = "<single>";
            this.content += "<item>" + item1 + "</item>";
            this.content += "<item>" + item2 + "</item>";
            this.content += "<item>" + item3 + "</item>";
            this.content += "<item>" + item4 + "</item>";
            this.content += "</single>";
        }
        return content;
    }

}
