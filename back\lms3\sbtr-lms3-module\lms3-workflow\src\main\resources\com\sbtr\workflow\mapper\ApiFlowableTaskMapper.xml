<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ApiFlowableTaskMapper">


    <select id="getToDoTasksSqlServer" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        t4.KEY_ AS modelKey,
        dbo.f_get_username(t2.START_USER_ID_) AS startUserId,
        t4.NAME_ AS modelName,
        t1.TASK_DEF_KEY_ AS nodeId,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        WHERE
        <if test="formName!='' and formName!=null">
            t2.NAME_ like concat('%',#{formName},'%') and
        </if>
        <if test="startTime!='' and startTime!=null">
            ( DATE_FORMAT(t1.CREATE_TIME_,'%Y-%m-%d') BETWEEN #{startDate} and #{endDate} ) and
        </if>
        <if test="modelName!='' and modelName!=null">
            t4.NAME_ like concat('%',#{modelName},'%') and
        </if>
        <if test="processModelType!='' and processModelType!=null">
            adm.process_model_type =#{processModelType}  and
        </if>
        t2.BUSINESS_KEY_ IS NOT NULL
        AND (
        t1.ASSIGNEE_ =#{userNameId}
        OR ( t1.ASSIGNEE_ IN ( SELECT g.user_name_id FROM sys_user g WHERE g.user_name_id = #{userNameId}) )
        )
        ORDER BY
        CREATE_TIME_ DESC
    </select>

    <select id="getToDoTasksOracle" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        t4.KEY_ AS modelKey,
        f_get_username(t2.START_USER_ID_) as startUserId,
        t4.NAME_ AS modelName,
        t1.TASK_DEF_KEY_ AS nodeId,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
	        t1.opening_time,
        nc.node_code
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_my_node_code nc on t1.PROC_DEF_ID_=nc.PROCDEF_ID and t1.TASK_DEF_KEY_=nc.node_id
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        WHERE
        <if test="formName!='' and formName!=null">
            t2.NAME_ like '%'||#{formName}||'%' and
        </if>
        <if test="mlevel!='' and mlevel!=null">
            t2.MLEVEL <![CDATA[<=]]> #{mlevel} and
        </if>
        <if test="startTime!='' and startTime!=null">
            ( t1.CREATE_TIME_ BETWEEN to_date(#{startDate},'yyyy-mm-dd') and to_date(#{endDate},'yyyy-mm-dd') ) and
        </if>
        <if test="modelName!='' and modelName!=null">
            t4.NAME_ like '%'||#{modelName}||'%' and
        </if>
        <if test="processModelType!='' and processModelType!=null">
            adm.process_model_type =#{processModelType}  and
        </if>
        <if test="taskId!='' and taskId!=null">
            t1.ID_ =#{taskId}  and
        </if>
        <if test="businessKey!='' and businessKey!=null">
            t2.BUSINESS_KEY_ =#{businessKey}  and
        </if>
        t2.BUSINESS_KEY_ IS NOT NULL
        AND (
            t1.ASSIGNEE_ =#{ userNameId }
            OR ( t1.OWNER_=#{ userNameId } and (coalesce(t1.ASSIGNEE_ ,N'') = ''))
            OR (
                ( coalesce(t1.ASSIGNEE_ ,N'') = '') and ( coalesce(t1.OWNER_ ,N'') = '')
                AND (
                    t3.USER_ID_ = #{ userNameId }
                    <if test="list != null and list.size() > 0">
            OR 	 t3.GROUP_ID_ IN
            <foreach collection="list" index="index" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
              </if>
                )
            )
        )
       ORDER BY
       CREATE_TIME_ DESC
   </select>


    <select id="getToDoTasksMySql" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        t4.KEY_ AS modelKey,
        f_get_username(t2.START_USER_ID_) as startUserId,
        t4.NAME_ AS modelName,
        t1.TASK_DEF_KEY_ AS nodeId,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
        t1.opening_time,
        nc.node_code
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_my_node_code nc on t1.PROC_DEF_ID_=nc.PROCDEF_ID and t1.TASK_DEF_KEY_=nc.node_id
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        WHERE
        <if test="formName!='' and formName!=null">
            t2.NAME_ like concat('%',#{formName},'%') and
        </if>
        <if test="mlevel!='' and mlevel!=null">
            t2.MLEVEL <![CDATA[<=]]> #{mlevel} and
        </if>
        <if test="startTime!='' and startTime!=null">
            ( DATE_FORMAT(t1.CREATE_TIME_,'%Y-%m-%d') BETWEEN #{startDate} and #{endDate} ) and
        </if>
        <if test="modelName!='' and modelName!=null">
            t4.NAME_ like concat('%',#{modelName},'%') and
        </if>
        <if test="processModelType!='' and processModelType!=null">
            adm.process_model_type =#{processModelType}  and
        </if>
        <if test="taskId!='' and taskId!=null">
            t1.ID_ =#{taskId}  and
        </if>
        t2.BUSINESS_KEY_ IS NOT NULL
        AND (
        t1.ASSIGNEE_ =#{userNameId}
        OR ( t1.OWNER_=#{ userNameId } and (coalesce(t1.ASSIGNEE_ ,N'') = ''))
        OR (( coalesce(t1.ASSIGNEE_ ,N'') = '' ) AND (t3.USER_ID_ = #{userNameId}
        <if test="list != null and list.size() > 0">
            OR 	 t3.GROUP_ID_ IN
            <foreach collection="list" index="index" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        )
        )
        )
        ORDER BY
        t1.CREATE_TIME_ DESC
    </select>

    <select id="getToDoTaskBybusinessKeysOracle" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        t4.KEY_ AS modelKey,
        f_get_username(t2.START_USER_ID_) as startUserId,
        t4.NAME_ AS modelName,
        t1.TASK_DEF_KEY_ AS nodeId,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
        t1.opening_time,
        nc.node_code
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_my_node_code nc on t1.PROC_DEF_ID_=nc.PROCDEF_ID and t1.TASK_DEF_KEY_=nc.node_id
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        WHERE
        <if test="businessKeys!=null and businessKeys.size() > 0">
            t2.BUSINESS_KEY_ in
            <foreach collection="businessKeys" index="index" item="businessKey" open="(" close=")" separator=",">
                #{businessKey}
            </foreach>
        </if>
        AND (
        t1.ASSIGNEE_ =#{ userNameId }
        OR ( t1.OWNER_=#{ userNameId } and (coalesce(t1.ASSIGNEE_ ,N'') = ''))
        OR (
        ( coalesce(t1.ASSIGNEE_ ,N'') = '') and ( coalesce(t1.OWNER_ ,N'') = '')
        AND (
        t3.USER_ID_ = #{ userNameId }
        <if test="groupIds != null and groupIds.size() > 0">
            OR 	 t3.GROUP_ID_ IN
            <foreach collection="groupIds" index="index" item="groupId" open="(" close=")" separator=",">
                #{groupId}
            </foreach>
        </if>
        )
        )
        )
        ORDER BY
        CREATE_TIME_ DESC
    </select>

    <select id="getBusinessInfoByTaskIdFromApplyedOracle" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT t1.ID_ AS taskId,
	    t3.NAME_ AS formName,
	    t3.BUSINESS_KEY_ AS businessKey
        FROM
        act_hi_taskinst t1
        JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        WHERE t1.ID_ = #{taskId}
        ORDER BY t3.START_TIME_ ASC
    </select>

    <select id="getBusinessInfoByTaskIdFromApplyedMySql" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT t1.ID_ AS taskId,
	    t3.NAME_ AS formName,
	    t3.BUSINESS_KEY_ AS businessKey
        FROM
        act_hi_taskinst t1
        JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        WHERE t1.ID_ = #{taskId}
        ORDER BY t3.START_TIME_ ASC
    </select>

    <select id="getBusinessInfoByTaskIdMySql" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        f_get_username(t2.START_USER_ID_) as startUserId,
        t1.TASK_DEF_KEY_ AS nodeId,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
        t1.opening_time
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        WHERE t1.ID_ = #{taskId}
        ORDER BY
        t2.START_TIME_ ASC
    </select>

    <select id="getBusinessInfoByTaskIdOracle" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        f_get_username(t2.START_USER_ID_) as startUserId,
        t1.TASK_DEF_KEY_ AS nodeId,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
        t1.opening_time
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        WHERE t1.ID_ = #{taskId}
        ORDER BY
        t2.START_TIME_ ASC
    </select>


    <select id="getToDoTaskBybusinessKeysMySql" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        t4.KEY_ AS modelKey,
        f_get_username(t2.START_USER_ID_) as startUserId,
        t4.NAME_ AS modelName,
        t1.TASK_DEF_KEY_ AS nodeId,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
        t1.opening_time,
        nc.node_code
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_my_node_code nc on t1.PROC_DEF_ID_=nc.PROCDEF_ID and t1.TASK_DEF_KEY_=nc.node_id
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        WHERE
        <if test="businessKeys!=null and businessKeys.size() > 0">
            t2.BUSINESS_KEY_ in
            <foreach collection="businessKeys" index="index" item="businessKey" open="(" close=")" separator=",">
                #{businessKey}
            </foreach>
        </if>
        AND (
        t1.ASSIGNEE_ =#{userNameId}
        OR ( t1.OWNER_=#{ userNameId } and (coalesce(t1.ASSIGNEE_ ,N'') = ''))
        OR (( coalesce(t1.ASSIGNEE_ ,N'') = '' ) AND (t3.USER_ID_ = #{userNameId}
        <if test="groupIds != null and groupIds.size() > 0">
            OR 	 t3.GROUP_ID_ IN
            <foreach collection="groupIds" index="index" item="groupId" open="(" close=")" separator=",">
                #{groupId}
            </foreach>
        </if>
        )
        )
        )
        ORDER BY
        t1.CREATE_TIME_ DESC
    </select>

    <select id="getToDoTaskBybusinessKeysAndModelKeyOracle" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        t4.KEY_ AS modelKey,
        f_get_username(t2.START_USER_ID_) as startUserId,
        t4.NAME_ AS modelName,
        t1.TASK_DEF_KEY_ AS nodeId,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
        t1.opening_time,
        nc.node_code
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_my_node_code nc on t1.PROC_DEF_ID_=nc.PROCDEF_ID and t1.TASK_DEF_KEY_=nc.node_id
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        WHERE
        <if test="businessKeys!=null and businessKeys.size() > 0">
            t2.BUSINESS_KEY_ in
            <foreach collection="businessKeys" index="index" item="businessKey" open="(" close=")" separator=",">
                #{businessKey}
            </foreach>
        </if>
        <if test="modelKey!=null and modelKey != ''">
            AND t4.KEY_ = #{modelKey}
        </if>
        AND (
        t1.ASSIGNEE_ =#{ userNameId }
        OR ( t1.OWNER_=#{ userNameId } and (coalesce(t1.ASSIGNEE_ ,N'') = ''))
        OR (
        ( coalesce(t1.ASSIGNEE_ ,N'') = '') and ( coalesce(t1.OWNER_ ,N'') = '')
        AND (
        t3.USER_ID_ = #{ userNameId }
        <if test="groupIds != null and groupIds.size() > 0">
            OR 	 t3.GROUP_ID_ IN
            <foreach collection="groupIds" index="index" item="groupId" open="(" close=")" separator=",">
                #{groupId}
            </foreach>
        </if>
        )
        )
        )
        ORDER BY
        CREATE_TIME_ DESC
    </select>



    <select id="getToDoTaskBybusinessKeysAndModelKeyMySql" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        t4.KEY_ AS modelKey,
        f_get_username(t2.START_USER_ID_) as startUserId,
        t4.NAME_ AS modelName,
        t1.TASK_DEF_KEY_ AS nodeId,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
        t1.opening_time,
        nc.node_code
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_my_node_code nc on t1.PROC_DEF_ID_=nc.PROCDEF_ID and t1.TASK_DEF_KEY_=nc.node_id
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        WHERE
        <if test="businessKeys!=null and businessKeys.size() > 0">
            t2.BUSINESS_KEY_ in
            <foreach collection="businessKeys" index="index" item="businessKey" open="(" close=")" separator=",">
                #{businessKey}
            </foreach>
        </if>
        <if test="modelKey!=null and modelKey != ''">
            AND t4.KEY_ = #{modelKey}
        </if>
        AND (
        t1.ASSIGNEE_ =#{userNameId}
        OR ( t1.OWNER_=#{ userNameId } and (coalesce(t1.ASSIGNEE_ ,N'') = ''))
        OR (( coalesce(t1.ASSIGNEE_ ,N'') = '' ) AND (t3.USER_ID_ = #{userNameId}
        <if test="groupIds != null and groupIds.size() > 0">
            OR 	 t3.GROUP_ID_ IN
            <foreach collection="groupIds" index="index" item="groupId" open="(" close=")" separator=",">
                #{groupId}
            </foreach>
        </if>
        )
        )
        )
        ORDER BY
        t1.CREATE_TIME_ DESC
    </select>


    <select id="getToDoTaskByModelKeyOracle" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        t4.KEY_ AS modelKey,
        f_get_username(t2.START_USER_ID_) as startUserId,
        t4.NAME_ AS modelName,
        t1.TASK_DEF_KEY_ AS nodeId,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
        t1.opening_time,
        nc.node_code
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_my_node_code nc on t1.PROC_DEF_ID_=nc.PROCDEF_ID and t1.TASK_DEF_KEY_=nc.node_id
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        WHERE
        t4.KEY_ =#{modelKey} AND
        <if test="mlevel!='' and mlevel!=null">
            t2.MLEVEL <![CDATA[<=]]> #{mlevel}
        </if>
        AND (
        t1.ASSIGNEE_ =#{ userNameId }
        OR ( t1.OWNER_=#{ userNameId } and (coalesce(t1.ASSIGNEE_ ,N'') = ''))
        OR (
        ( coalesce(t1.ASSIGNEE_ ,N'') = '') and ( coalesce(t1.OWNER_ ,N'') = '')
        AND (
        t3.USER_ID_ = #{ userNameId }
        <if test="groupIds != null and groupIds.size() > 0">
            OR 	 t3.GROUP_ID_ IN
            <foreach collection="groupIds" index="index" item="groupId" open="(" close=")" separator=",">
                #{groupId}
            </foreach>
        </if>
        )
        )
        )
        ORDER BY
        CREATE_TIME_ DESC
    </select>


    <select id="getToDoTaskByModelKeyMySql" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        t4.KEY_ AS modelKey,
        f_get_username(t2.START_USER_ID_) as startUserId,
        t4.NAME_ AS modelName,
        t1.TASK_DEF_KEY_ AS nodeId,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
        t1.opening_time,
        nc.node_code
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_my_node_code nc on t1.PROC_DEF_ID_=nc.PROCDEF_ID and t1.TASK_DEF_KEY_=nc.node_id
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        WHERE
        t4.KEY_ =#{modelKey}
        <if test="mlevel!='' and mlevel!=null"> AND
            t2.MLEVEL <![CDATA[<=]]> #{mlevel}
        </if>
        AND (
        t1.ASSIGNEE_ =#{userNameId}
        OR ( t1.OWNER_=#{ userNameId } and (coalesce(t1.ASSIGNEE_ ,N'') = ''))
        OR (( coalesce(t1.ASSIGNEE_ ,N'') = '' ) AND (t3.USER_ID_ = #{userNameId}
        <if test="groupIds != null and groupIds.size() > 0">
            OR 	 t3.GROUP_ID_ IN
            <foreach collection="groupIds" index="index" item="groupId" open="(" close=")" separator=",">
                #{groupId}
            </foreach>
        </if>
        )
        )
        )
        ORDER BY
        t1.CREATE_TIME_ DESC
    </select>



    <select id="getApplyedTaskBybusinessKeysOracle" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT wm_concat(distinct t1.ID_) AS taskId,
        wm_concat(distinct t1.NAME_) AS taskName,
        t3.NAME_ AS formName,
        t3.BUSINESS_KEY_ AS businessKey,
        t3.PROC_INST_ID_ AS processInstanceId,
        t3.TENANT_ID_ AS systemSn,
        f_get_username (t3.START_USER_ID_)  AS userNameId,
        t3.START_TIME_ AS startTime,
        max(t1.END_TIME_) AS endTime,
        wm_concat(distinct t1.TASK_DEF_KEY_) AS nodeId,
        adm.name AS modelName,
        t5.KEY_ AS modelKey,
        t3.PROC_DEF_ID_ AS processDefinitionId
        FROM
        act_hi_taskinst t1
        JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        LEFT JOIN act_re_procdef t5 ON t3.PROC_DEF_ID_ = t5.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t5.KEY_
        WHERE
        <if test="businessKeys!=null and businessKeys.size() > 0">
            t3.BUSINESS_KEY_ in
            <foreach collection="businessKeys" index="index" item="businessKey" open="(" close=")" separator=",">
                #{businessKey}
            </foreach>
        </if>
        AND
        t1.END_TIME_ IS NOT NULL
        AND t1.ASSIGNEE_ = #{userNameId}
        group by t3.PROC_INST_ID_,t3.NAME_,t3.BUSINESS_KEY_,t3.TENANT_ID_,t3.START_USER_ID_,adm.name,t3.START_TIME_,t3.PROC_DEF_ID_,t5.KEY_
        order by max(t1.END_TIME_) desc
    </select>

    <select id="getApplyedTaskBybusinessKeysMySql" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT group_concat(distinct t1.ID_) AS taskId,
        group_concat(distinct t1.NAME_) AS taskName,
        t3.NAME_ AS formName,
        t3.BUSINESS_KEY_ AS businessKey,
        t3.PROC_INST_ID_ AS processInstanceId,
        t3.TENANT_ID_ AS systemSn,
        f_get_username (t3.START_USER_ID_)  AS userNameId,
        t3.START_TIME_ AS startTime,
        max(t1.END_TIME_) AS endTime,
        group_concat(distinct t1.TASK_DEF_KEY_) AS nodeId,
        adm.name AS modelName,
        t5.KEY_ AS modelKey,
        t3.PROC_DEF_ID_ AS processDefinitionId
        FROM
        act_hi_taskinst t1
        JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        LEFT JOIN act_re_procdef t5 ON t3.PROC_DEF_ID_ = t5.ID_
        left JOIN act_de_model adm on adm.model_key=t5.KEY_
        WHERE
        <if test="businessKeys!=null and businessKeys.size() > 0">
            t3.BUSINESS_KEY_ in
            <foreach collection="businessKeys" index="index" item="businessKey" open="(" close=")" separator=",">
                #{businessKey}
            </foreach>
        </if>
        AND
        t1.END_TIME_ IS NOT NULL
        AND t1.ASSIGNEE_ = #{userNameId}
        group by t3.PROC_INST_ID_,t3.NAME_,t3.BUSINESS_KEY_,t3.TENANT_ID_,t3.START_USER_ID_,adm.name,t3.START_TIME_,t3.PROC_DEF_ID_,t5.KEY_
        order by max(t1.END_TIME_) desc
    </select>

    <select id="getApplyedTaskBybusinessKeysAndModelKeyOracle" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT wm_concat(distinct t1.ID_) AS taskId,
        wm_concat(distinct t1.NAME_) AS taskName,
        t3.NAME_ AS formName,
        t3.BUSINESS_KEY_ AS businessKey,
        t3.PROC_INST_ID_ AS processInstanceId,
        t3.TENANT_ID_ AS systemSn,
        f_get_username (t3.START_USER_ID_)  AS userNameId,
        t3.START_TIME_ AS startTime,
        max(t1.END_TIME_) AS endTime,
        wm_concat(distinct t1.TASK_DEF_KEY_) AS nodeId,
        adm.name AS modelName,
        t5.KEY_ AS modelKey,
        t3.PROC_DEF_ID_ AS processDefinitionId
        FROM
        act_hi_taskinst t1
        JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        LEFT JOIN act_re_procdef t5 ON t3.PROC_DEF_ID_ = t5.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t5.KEY_
        WHERE
        <if test="businessKeys!=null and businessKeys.size() > 0">
            t3.BUSINESS_KEY_ in
            <foreach collection="businessKeys" index="index" item="businessKey" open="(" close=")" separator=",">
                #{businessKey}
            </foreach>
        </if>
        <if test="modelKey!=null and modelKey != ''">
            AND t5.KEY_ = #{modelKey}
        </if>
        AND
        t1.END_TIME_ IS NOT NULL
        AND t1.ASSIGNEE_ = #{userNameId}
        group by t3.PROC_INST_ID_,t3.NAME_,t3.BUSINESS_KEY_,t3.TENANT_ID_,t3.START_USER_ID_,adm.name,t3.START_TIME_,t3.PROC_DEF_ID_,t5.KEY_
        order by max(t1.END_TIME_) desc
    </select>

    <select id="getApplyedTaskBybusinessKeysAndModelKeyMySql" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT group_concat(distinct t1.ID_) AS taskId,
        group_concat(distinct t1.NAME_) AS taskName,
        t3.NAME_ AS formName,
        t3.BUSINESS_KEY_ AS businessKey,
        t3.PROC_INST_ID_ AS processInstanceId,
        t3.TENANT_ID_ AS systemSn,
        f_get_username (t3.START_USER_ID_)  AS userNameId,
        t3.START_TIME_ AS startTime,
        max(t1.END_TIME_) AS endTime,
        group_concat(distinct t1.TASK_DEF_KEY_) AS nodeId,
        adm.name AS modelName,
        t5.KEY_ AS modelKey,
        t3.PROC_DEF_ID_ AS processDefinitionId
        FROM
        act_hi_taskinst t1
        JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        LEFT JOIN act_re_procdef t5 ON t3.PROC_DEF_ID_ = t5.ID_
        left JOIN act_de_model adm on adm.model_key=t5.KEY_
        WHERE
        <if test="businessKeys!=null and businessKeys.size() > 0">
            t3.BUSINESS_KEY_ in
            <foreach collection="businessKeys" index="index" item="businessKey" open="(" close=")" separator=",">
                #{businessKey}
            </foreach>
        </if>
        <if test="modelKey!=null and modelKey != ''">
            AND t5.KEY_ = #{modelKey}
        </if>
        AND
        t1.END_TIME_ IS NOT NULL
        AND t1.ASSIGNEE_ = #{userNameId}
        group by t3.PROC_INST_ID_,t3.NAME_,t3.BUSINESS_KEY_,t3.TENANT_ID_,t3.START_USER_ID_,adm.name,t3.START_TIME_,t3.PROC_DEF_ID_,t5.KEY_
        order by max(t1.END_TIME_) desc
    </select>

    <select id="getApplyedTaskByModelKeyOracle" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t3.NAME_ AS formName,
        t3.BUSINESS_KEY_ AS businessKey,
        t3.PROC_INST_ID_ AS processInstanceId,
        t3.TENANT_ID_ AS systemSn,
        f_get_username (t3.START_USER_ID_)  AS userNameId,
        t3.START_TIME_ AS startTime,
        t1.END_TIME_ AS endTime,
        t1.TASK_DEF_KEY_ AS nodeId,
        adm.name AS modelName,
        t5.KEY_ AS modelKey,
        t3.PROC_DEF_ID_ AS processDefinitionId
        FROM
        act_hi_taskinst t1
        JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        LEFT JOIN act_re_procdef t5 ON t3.PROC_DEF_ID_ = t5.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t5.KEY_
        WHERE
        t5.KEY_ =#{modelKey}
        AND
        t1.END_TIME_ IS NOT NULL
        AND t1.ASSIGNEE_ = #{userNameId}
        order by t1.END_TIME_ desc
    </select>

    <select id="getApplyedTaskByModelKeyMySql" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT group_concat(distinct t1.ID_) AS taskId,
        group_concat(distinct t1.NAME_) AS taskName,
        t3.NAME_ AS formName,
        t3.BUSINESS_KEY_ AS businessKey,
        t3.PROC_INST_ID_ AS processInstanceId,
        t3.TENANT_ID_ AS systemSn,
        f_get_username (t3.START_USER_ID_)  AS userNameId,
        t3.START_TIME_ AS startTime,
        max(t1.END_TIME_) AS endTime,
        group_concat(distinct t1.TASK_DEF_KEY_) AS nodeId,
        adm.name AS modelName,
        t5.KEY_ AS modelKey,
        t3.PROC_DEF_ID_ AS processDefinitionId
        FROM
        act_hi_taskinst t1
        JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        LEFT JOIN act_re_procdef t5 ON t3.PROC_DEF_ID_ = t5.ID_
        left JOIN act_de_model adm on adm.model_key=t5.KEY_
        WHERE
        t5.KEY_ =#{modelKey}
        AND
        t1.END_TIME_ IS NOT NULL
        AND t1.ASSIGNEE_ = #{userNameId}
        group by t3.PROC_INST_ID_,t3.NAME_,t3.BUSINESS_KEY_,t3.TENANT_ID_,t3.START_USER_ID_,adm.name,t3.START_TIME_,t3.PROC_DEF_ID_,t5.KEY_
        order by max(t1.END_TIME_) desc
    </select>

    <select id="getAgentTasksOracle" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        t4.KEY_ AS modelKey,
        f_get_username(t2.START_USER_ID_) as startUserId,
        f_get_username(case when t1.ASSIGNEE_ is not null then t1.ASSIGNEE_  when t1.owner_ is not null then t1.owner_  when t3.user_id_ is not null then t3.user_id_ else g.user_name_id end) as userName,
        case when t1.ASSIGNEE_ is not null then t1.ASSIGNEE_  when t1.owner_ is not null then t1.owner_  when t3.user_id_ is not null then t3.user_id_ else g.user_name_id end as userNameId,
        t4.NAME_ AS modelName,
        t1.TASK_DEF_KEY_ AS nodeId,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
        t1.opening_time,
        nc.node_code
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_my_node_code nc on t1.PROC_DEF_ID_=nc.PROCDEF_ID and t1.TASK_DEF_KEY_=nc.node_id
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN sys_group_user g ON g.id = t3.GROUP_ID_
        WHERE
        <if test="formName!='' and formName!=null">
            t2.NAME_ like '%'||#{formName}||'%' and
        </if>
        <if test="startTime!='' and startTime!=null">
            ( t1.CREATE_TIME_ BETWEEN to_date(#{startDate},'yyyy-mm-dd') and to_date(#{endDate},'yyyy-mm-dd') ) and
        </if>
        <if test="modelName!='' and modelName!=null">
            t4.NAME_ like '%'||#{modelName}||'%' and
        </if>
        <if test="processModelType!='' and processModelType!=null">
            adm.process_model_type =#{processModelType}  and
        </if>
        <if test="taskId!='' and taskId!=null">
            t1.ID_ =#{taskId}  and
        </if>
        <if test="businessKey!='' and businessKey!=null">
            t2.BUSINESS_KEY_ =#{businessKey}  and
        </if>
        t2.BUSINESS_KEY_ IS NOT NULL
        AND (
        t1.ASSIGNEE_ IN
         <foreach collection="userNameIds" index="index" item="userNameId" open="(" close=")" separator=",">
        #{userNameId}
         </foreach>
        OR (
          t1.OWNER_ IN
          <foreach collection="userNameIds" index="index" item="userNameId" open="(" close=")" separator=",">
           #{userNameId}
          </foreach>
          and (coalesce(t1.ASSIGNEE_ ,N'') = '')
        ) OR (
          ( coalesce(t1.ASSIGNEE_ ,N'') = '' ) and ( coalesce(t1.OWNER_ ,N'') = '' )
          AND (
          t3.USER_ID_ IN
           <foreach collection="userNameIds" index="index" item="userNameId" open="(" close=")" separator=",">
            #{userNameId}
           </foreach>
          <if test="groupIds != null and groupIds.size() > 0">
              OR (
              t3.GROUP_ID_ IN
              <foreach collection="groupIds" index="index" item="id" open="(" close=")" separator=",">#{id}
              </foreach>
              and g.user_name_id in
              <foreach
                      collection="userNameIds" index="index" item="userNameId" open="(" close=")" separator=",">
              #{userNameId}
             </foreach>
             )
          </if>
          )
        )
        )
        ORDER BY
        CREATE_TIME_ DESC
    </select>

    <select id="getAgentTasksMysql" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        t4.KEY_ AS modelKey,
        f_get_username(t2.START_USER_ID_) as startUserId,
        t4.NAME_ AS modelName,
        t1.TASK_DEF_KEY_ AS nodeId,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
        t1.opening_time,
        nc.node_code
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_my_node_code nc on t1.PROC_DEF_ID_=nc.PROCDEF_ID and t1.TASK_DEF_KEY_=nc.node_id
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        WHERE
        <if test="formName!='' and formName!=null">
            t2.NAME_ like concat('%',#{formName},'%') and
        </if>
        <if test="startTime!='' and startTime!=null">
            ( DATE_FORMAT(t1.CREATE_TIME_,'%Y-%m-%d') BETWEEN #{startDate} and #{endDate} ) and
        </if>
        <if test="modelName!='' and modelName!=null">
            t4.NAME_ like concat('%',#{modelName},'%') and
        </if>
        <if test="processModelType!='' and processModelType!=null">
            adm.process_model_type =#{processModelType}  and
        </if>
        <if test="taskId!='' and taskId!=null">
            t1.ID_ =#{taskId}  and
        </if>
        t2.BUSINESS_KEY_ IS NOT NULL
        AND (
        t1.ASSIGNEE_ IN
        <foreach collection="userNameIds" index="index" item="userNameId" open="(" close=")" separator=",">
            #{userNameId}
        </foreach>
        OR (
        t1.OWNER_ IN
        <foreach collection="userNameIds" index="index" item="userNameId" open="(" close=")" separator=",">
            #{userNameId}
        </foreach>
        and (coalesce(t1.ASSIGNEE_ ,N'') = '')
        ) OR (
        ( coalesce(t1.ASSIGNEE_ ,N'') = '' ) and ( coalesce(t1.OWNER_ ,N'') = '' )
        AND (
        t3.USER_ID_  IN
        <foreach collection="userNameIds" index="index" item="userNameId" open="(" close=")" separator=",">
            #{userNameId}
        </foreach>
        <if test="groupIds != null and groupIds.size() > 0">
            OR 	 t3.GROUP_ID_ IN
            <foreach collection="groupIds" index="index" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        )
        )
        )
        ORDER BY
        CREATE_TIME_ DESC
    </select>
    <select id="getFinanceMySql" resultType="java.lang.String">
        SELECT DISTINCT
        t2.BUSINESS_KEY_ AS uuid
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        WHERE
        <if test="formName!='' and formName!=null">
            t2.NAME_ like concat('%',#{formName},'%') and
        </if>
        <if test="startTime!='' and startTime!=null">
            ( DATE_FORMAT(t1.CREATE_TIME_,'%Y-%m-%d') BETWEEN #{startDate} and #{endDate} ) and
        </if>
        <if test="modelName!='' and modelName!=null">
            t4.NAME_ like concat('%',#{modelName},'%') and
        </if>
        <if test="processModelType!='' and processModelType!=null">
            adm.process_model_type =#{processModelType}  and
        </if>
        (t4.KEY_='xtbg-bxsq' or t4.KEY_='xtbg-fksq' or t4.KEY_ = 'xtbg-jksq') and
        t1.NAME_ = '出纳' and
        t2.BUSINESS_KEY_ IS NOT NULL
        AND (
        t1.ASSIGNEE_ =#{userNameId}
        OR ( t1.ASSIGNEE_ IN ( SELECT g.user_name_id FROM sys_user g WHERE g.user_name_id = #{userNameId}) )

        OR (( coalesce(t1.ASSIGNEE_ ,N'') = '' ) AND (t3.USER_ID_ = #{userNameId}
        OR
        find_in_set (  t3.GROUP_ID_ ,(  select  GROUP_CONCAT(CAST(b.uuid AS CHAR))  AS  courseIds from (SELECT uuid FROM sys_auth_group WHERE FIND_IN_SET(id,(SELECT g.user_group FROM sys_user g WHERE g.user_name_id = #{userNameId}))) as b))
        OR
        find_in_set ( ( select uuid from sys_organization where FIND_IN_SET(id,(select org_id from sys_user g where g.user_name_id= #{userNameId} ) )), t3.GROUP_ID_ )
        OR
        find_in_set ( (  select  GROUP_CONCAT(CAST(a.uuid AS CHAR))  AS  courseId from (SELECT DISTINCT sdi.`uuid`  FROM sys_user_post sup LEFT JOIN sys_dic_item sdi ON sup.post = sdi.VALUE AND sdi.puuid = '4028804863ede3960163ee2cc35301fe' WHERE sup.user_name_id =#{userNameId}) as a  ), t3.GROUP_ID_ )
        )
        )
        )
    </select>

    <select id="getFinanceOracle" resultType="java.lang.String">
        SELECT DISTINCT
        t2.BUSINESS_KEY_ AS uuid
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        WHERE
        (t4.KEY_='xtbg-bxsq' or t4.KEY_='xtbg-fksq' or t4.KEY_ = 'xtbg-jksq') and
        t1.NAME_ = '出纳' and
        <if test="formName!='' and formName!=null">
            t2.NAME_ like '%'||#{formName}||'%' and
        </if>
        <if test="startTime!='' and startTime!=null">
            ( t1.CREATE_TIME_ BETWEEN to_date(#{startDate},'yyyy-mm-dd') and to_date(#{endDate},'yyyy-mm-dd') ) and
        </if>
        <if test="modelName!='' and modelName!=null">
            t4.NAME_ like '%'||#{modelName}||'%' and
        </if>
        <if test="processModelType!='' and processModelType!=null">
            adm.process_model_type =#{processModelType}  and
        </if>
        t2.BUSINESS_KEY_ IS NOT NULL
        AND (
        t1.ASSIGNEE_ =#{ userNameId }
        OR ( t1.ASSIGNEE_ IN ( SELECT g.user_name_id FROM sys_user g WHERE g.user_name_id = #{ userNameId } ) )
        OR (
        ( coalesce(t1.ASSIGNEE_ ,N'') = '' )
        AND (
        t3.USER_ID_ = #{ userNameId }
        OR t3.GROUP_ID_ IN (
        SELECT
        uuid
        FROM
        sys_auth_group
        WHERE
        TO_CHAR( id ) IN (
        SELECT
        REGEXP_SUBSTR( ( SELECT g.user_group FROM sys_user g WHERE g.user_name_id =#{ userNameId } ), '[^,]+', 1, LEVEL )
        FROM
        DUAL CONNECT BY REGEXP_SUBSTR( ( SELECT g.user_group FROM sys_user g WHERE g.user_name_id = #{ userNameId } ), '[^,]+', 1, LEVEL ) IS NOT NULL
        )
        )
        )
        )
        )
        ORDER BY
        CREATE_TIME_ DESC
    </select>

    <select id="getFinanceSqlServer" resultType="java.lang.String">
        SELECT DISTINCT
        t2.BUSINESS_KEY_ AS uuid,
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        WHERE
        (t4.KEY_='xtbg-bxsq' or t4.KEY_='xtbg-fksq' or t4.KEY_ = 'xtbg-jksq') and
        t1.NAME_ = '出纳' and
        <if test="formName!='' and formName!=null">
            t2.NAME_ like concat('%',#{formName},'%') and
        </if>
        <if test="startTime!='' and startTime!=null">
            ( DATE_FORMAT(t1.CREATE_TIME_,'%Y-%m-%d') BETWEEN #{startDate} and #{endDate} ) and
        </if>
        <if test="modelName!='' and modelName!=null">
            t4.NAME_ like concat('%',#{modelName},'%') and
        </if>
        <if test="processModelType!='' and processModelType!=null">
            adm.process_model_type =#{processModelType}  and
        </if>
        t2.BUSINESS_KEY_ IS NOT NULL
        AND (
        t1.ASSIGNEE_ =#{userNameId}
        OR ( t1.ASSIGNEE_ IN ( SELECT g.user_name_id FROM sys_user g WHERE g.user_name_id = #{userNameId}) )


        )
        ORDER BY
        CREATE_TIME_ DESC
    </select>


    <select id="getToDoTaskByUuid" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        t4.KEY_ AS modelKey,
        f_get_username(t2.START_USER_ID_) AS startUserId,
        t4.NAME_ AS modelName,
        t1.TASK_DEF_KEY_ AS nodeId,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
        t1.opening_time
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_de_model adm ON adm.model_key = t4.KEY_
        WHERE
        t2.BUSINESS_KEY_ IS NOT NULL
        and t2.BUSINESS_KEY_ = #{uuid}
        AND (
        t1.ASSIGNEE_ =#{userNameId}
        OR ( t1.ASSIGNEE_ IN ( SELECT g.user_name_id FROM sys_user g WHERE g.user_name_id = #{userNameId}) )

        OR (( coalesce(t1.ASSIGNEE_ ,N'') = '' ) AND (t3.USER_ID_ = #{userNameId}
        OR
        find_in_set (  t3.GROUP_ID_ ,(  select  GROUP_CONCAT(CAST(b.uuid AS CHAR))  AS  courseIds from (SELECT uuid FROM sys_auth_group WHERE FIND_IN_SET(id,(SELECT g.user_group FROM sys_user g WHERE g.user_name_id = #{userNameId}))) as b))
        OR
        find_in_set ( ( select uuid from sys_organization where FIND_IN_SET(id,(select org_id from sys_user g where g.user_name_id= #{userNameId} ) )), t3.GROUP_ID_ )
        OR
        find_in_set ( (  select  GROUP_CONCAT(CAST(a.uuid AS CHAR))  AS  courseId from (SELECT DISTINCT sdi.`uuid`  FROM sys_user_post sup LEFT JOIN sys_dic_item sdi ON sup.post = sdi.VALUE AND sdi.puuid = '4028804863ede3960163ee2cc35301fe' WHERE sup.user_name_id =#{userNameId}) as a  ), t3.GROUP_ID_ )
        )
        )
        )
        ORDER BY
        CREATE_TIME_ DESC
    </select>

    <select id="getMyHistoryPageSetOracle" resultType="com.sbtr.workflow.dto.CommonTaskDto">
        SELECT DISTINCT
        RES.ID_ AS id,
        RES.NAME_ as name,
        DEF.KEY_ AS procDefKey,
        DEF.NAME_ AS procDefName,
        DEF.VERSION_ AS version,
        DEF.DEPLOYMENT_ID_ AS deployment,
        f_get_username(RES.START_USER_ID_) as startUserId,
        RES.START_TIME_ as startTime,
        RES.END_TIME_ as endTime,
        RES.DURATION_ as duration,
        RES.PROC_INST_ID_ as processInstanceId,
        RES.BUSINESS_KEY_ as businessKey,
        DEF.ID_ as processDefinitionId
        FROM
        ACT_HI_PROCINST RES
        LEFT OUTER JOIN ACT_RE_PROCDEF DEF ON RES.PROC_DEF_ID_ = DEF.ID_
        left JOIN act_de_model adm on adm.model_key=DEF.KEY_
        WHERE
        <if test="name!='' and name !=null">
            RES.NAME_ LIKE '%'||#{name}||'%' and
        </if>
        <if test="startTime!='' and startTime!=null">
            ( RES.START_TIME_ BETWEEN to_date(#{startDate},'yyyy-mm-dd') and to_date(#{startDate},'yyyy-mm-dd') ) and
        </if>
        <if test="procDefName!='' and procDefName !=null">
            DEF.NAME_ LIKE '%'||#{procDefName}||'%' and
        </if>
        <if test="processModelType!='' and processModelType!=null">
            adm.process_model_type =#{processModelType}  and
        </if>
        RES.END_TIME_ IS NOT NULL
        AND RES.START_USER_ID_ =#{userNameId}
        ORDER BY
        RES.END_TIME_ DESC
    </select>

   <select id="getMyHistoryPageSetMySql" resultType="com.sbtr.workflow.dto.CommonTaskDto">
       SELECT DISTINCT
       RES.ID_ AS id,
       RES.NAME_ as name,
       DEF.KEY_ AS procDefKey,
       DEF.NAME_ AS procDefName,
       DEF.VERSION_ AS version,
       DEF.DEPLOYMENT_ID_ AS deployment,
       f_get_username(RES.START_USER_ID_) as startUserId,
       RES.START_TIME_ as startTime,
       RES.END_TIME_ as endTime,
       RES.DURATION_ as duration,
       RES.PROC_INST_ID_ as processInstanceId,
       RES.BUSINESS_KEY_ as businessKey,
       DEF.ID_ as processDefinitionId
       FROM
       ACT_HI_PROCINST RES
       LEFT OUTER JOIN ACT_RE_PROCDEF DEF ON RES.PROC_DEF_ID_ = DEF.ID_
       left JOIN act_de_model adm on adm.model_key=DEF.KEY_
       WHERE
       <if test="name!='' and name !=null">
           RES.NAME_ like '%'||#{name}||'%' and
       </if>
       <if test="startTime!='' and startTime!=null">
           ( (RES.START_TIME_) BETWEEN #{startDate} and #{endDate} ) and
       </if>
       <if test="procDefName!='' and procDefName !=null">
           DEF.NAME_ like '%'||#{procDefName}||'%' and
       </if>
       <if test="processModelType!='' and processModelType!=null">
           adm.process_model_type =#{processModelType}  and
       </if>
       RES.END_TIME_ IS NOT NULL
       AND RES.START_USER_ID_ =#{userNameId}
       ORDER BY
       RES.END_TIME_ DESC
   </select>

   <select id="getMyNoEndProcessPageSetDataOracle" resultType="com.sbtr.workflow.vo.TaskVo">
       SELECT wm_concat(distinct t1.ID_) AS taskId,
       wm_concat(distinct t1.NAME_) AS taskName,
       wm_concat(distinct t1.TASK_DEF_KEY_) as nodeId,
       t2.NAME_ AS formName,
       t2.TENANT_ID_ AS systemSn,
       t2.BUSINESS_KEY_ AS businessKey,
       t2.PROC_INST_ID_ AS processInstanceId,
       t2.START_TIME_ AS flowStartTime,
       f_get_username(t2.START_USER_ID_) as startUserId,
       t2.PROC_DEF_ID_ AS processDefinitionId,
       t2.ID_ AS executionId,
       t4.KEY_ AS modelKey,
       t4.NAME_ AS modelName
       FROM
       act_ru_task t1
       INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
       LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
       WHERE
       <if test="formName!='' and formName!=null">
           t2.NAME_ like '%'||#{formName}||'%' and
       </if>
       <if test="startTime!='' and startTime!=null">
           ( t2.START_TIME_ BETWEEN to_date(#{startDate},'yyyy-mm-dd') and to_date(#{endDate},'yyyy-mm-dd') ) and
       </if>
       <if test="modelName!='' and modelName!=null">
           t4.NAME_ like '%'||#{modelName}||'%' and
       </if>
       <if test="processModelType!='' and processModelType!=null">
           adm.process_model_type =#{processModelType}  and
       </if>
       t2.START_USER_ID_ =#{userNameId}
       AND t2.BUSINESS_KEY_ IS NOT NULL
       group by t4.KEY_,t4.NAME_,t2.NAME_,t2.ID_,
       t2.TENANT_ID_,
       t2.BUSINESS_KEY_,
       t2.PROC_INST_ID_,
       t2.START_TIME_,
       t2.START_USER_ID_,t2.PROC_DEF_ID_
       order by t2.START_TIME_ desc
   </select>

    <select id="getMyNoEndProcessPageSetDataSqlServer" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT group_concat(distinct t1.ID_) AS taskId,
        group_concat(distinct t1.NAME_) AS taskName,
        group_concat(distinct t1.TASK_DEF_KEY_) as nodeId,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t2.START_TIME_ AS flowStartTime,
        dbo.f_get_username(t2.START_USER_ID_) as startUserId,
        t2.PROC_DEF_ID_ AS processDefinitionId,
        t2.ID_ AS executionId,
        t4.KEY_ AS modelKey,
        t4.NAME_ AS modelName
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        WHERE
        <if test="formName!='' and formName!=null">
            t2.NAME_ like concat('%',#{formName},'%') and
        </if>
        <if test="startTime!='' and startTime!=null">
            ( convert(varchar,START_TIME_,23) BETWEEN #{startDate} and #{endDate} ) and
        </if>
        <if test="modelName!='' and modelName!=null">
            t4.NAME_ like concat('%',#{modelName},'%') and
        </if>
        <if test="processModelType!='' and processModelType!=null">
            adm.process_model_type =#{processModelType}  and
        </if>
        t2.START_USER_ID_ =#{userNameId}
        AND t2.BUSINESS_KEY_ IS NOT NULL
        group by t4.KEY_,t4.NAME_,t2.NAME_,t2.ID_,
        t2.TENANT_ID_,
        t2.BUSINESS_KEY_,
        t2.PROC_INST_ID_,
        t2.START_TIME_,
        t2.START_USER_ID_,t2.PROC_DEF_ID_
        order by t2.START_TIME_ desc
    </select>

    <select id="getMyNoEndProcessPageSetDataMySql" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_TIME_ AS flowStartTime,
        f_get_username(t2.START_USER_ID_) as startUserId,
        t4.NAME_ AS modelName,
        t1.PRIORITY_ AS priority,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        t1.EXECUTION_ID_ AS executionId,
        t1.ASSIGNEE_ as assignee,
        t4.KEY_ AS modelKey,
        t1.TASK_DEF_KEY_ as nodeId
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        left JOIN act_de_model adm on adm.model_key=t4.KEY_
        WHERE
        <if test="formName!='' and formName!=null">
            t2.NAME_ like concat('%',#{formName},'%') and
        </if>
        <if test="startTime!='' and startTime!=null">
            ( t2.START_TIME_ BETWEEN #{startDate} and #{endDate} ) and
        </if>
        <if test="modelName!='' and modelName!=null">
            t4.NAME_ like concat('%',#{modelName},'%') and
        </if>
        <if test="processModelType!='' and processModelType!=null">
            adm.process_model_type =#{processModelType}  and
        </if>
        t2.START_USER_ID_ =#{userNameId}
        AND t2.BUSINESS_KEY_ IS NOT NULL
        order by t1.CREATE_TIME_ desc
    </select>

    <select id="getDeployedPageSetOracle" resultType="com.sbtr.workflow.dto.ProcessDefinitionDto">
       select RES.*,RES.ID_ as procdefId from ACT_RE_PROCDEF RES where 1=1
       <if test="modelKey!=null and modelKey!=''">
           and RES.KEY_ like '%'||#{modelKey}||'%'
       </if>
       <if test="modelName!=null and modelName!=''">
           and RES.NAME_ like '%'||#{modelName}||'%'
       </if>
       order by RES.DEPLOYMENT_ID_
    </select>
    <select id="getDeployedPageSetMySql" resultType="com.sbtr.workflow.dto.ProcessDefinitionDto">
        select RES.*,RES.ID_ as procdefId from ACT_RE_PROCDEF RES where 1=1
        <if test="modelKey!=null and modelKey!=''">
            and RES.KEY_ like concat('%',#{modelKey},'%')
        </if>
        <if test="modelName!=null and modelName!=''">
            and RES.NAME_ like concat('%',#{modelName},'%')
        </if>
        order by RES.DEPLOYMENT_ID_
    </select>

    <select id="getTaskPageSetOracle" resultType="com.sbtr.workflow.dto.CommonTaskDto">
        SELECT wm_concat(distinct t1.ID_) AS taskId,
        wm_concat(distinct t1.NAME_) AS taskName,
        wm_concat(distinct t1.TASK_DEF_KEY_) as nodeId,
        t2.NAME_ AS formName,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t2.START_TIME_ AS startTime,
        f_get_username(t2.START_USER_ID_) as startUserId,
        t2.PROC_DEF_ID_ AS processDefinitionId,
        t2.ID_ AS executionId,
        t4.KEY_ AS modelKey,
        t4.NAME_ AS modelName
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        WHERE
        <if test="formName!='' and formName!=null">
            t2.NAME_ like '%'||#{formName}||'%' and
        </if>
        <if test="modelName!='' and modelName!=null">
            t4.NAME_ like '%'||#{modelName}||'%' and
        </if>
        t2.BUSINESS_KEY_ IS NOT NULL
        group by t4.KEY_,t4.NAME_,t2.NAME_,t2.ID_,
        t2.BUSINESS_KEY_,
        t2.PROC_INST_ID_,
        t2.START_TIME_,
        t2.START_USER_ID_,t2.PROC_DEF_ID_
        order by t2.START_TIME_ desc
    </select>

    <select id="getTaskPageSetMySql" resultType="com.sbtr.workflow.dto.CommonTaskDto">
        SELECT group_concat(distinct t1.ID_) AS taskId,
        group_concat(distinct t1.NAME_) AS taskName,
        group_concat(distinct t1.TASK_DEF_KEY_) as nodeId,
        t2.NAME_ AS formName,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t2.START_TIME_ AS startTime,
        f_get_username(t2.START_USER_ID_) as startUserId,
        t2.PROC_DEF_ID_ AS processDefinitionId,
        t2.ID_ AS executionId,
        t4.KEY_ AS modelKey,
        t4.NAME_ AS modelName
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_re_procdef t4 ON t4.ID_ = t1.PROC_DEF_ID_
        WHERE
        <if test="formName!=null and formName!=''">
            t2.NAME_ like concat('%',#{formName},'%') and
        </if>
        <if test="modelName!=null and modelName!=''">
            t4.NAME_ like concat('%',#{modelName},'%') and
        </if>
        1=1 and t2.BUSINESS_KEY_ IS NOT NULL
        group by t4.KEY_,t4.NAME_,t2.NAME_,t2.ID_,
        t2.BUSINESS_KEY_,
        t2.PROC_INST_ID_,
        t2.START_TIME_,
        t2.START_USER_ID_,t2.PROC_DEF_ID_
        order by t2.START_TIME_ desc
    </select>

    <select id="getTaskPageSetSqlServer" resultType="com.sbtr.workflow.dto.CommonTaskDto">
        select distinct RES.*,
        RES.PROC_INST_ID_ AS processInstanceId,
        dbo.f_get_username(RES.ASSIGNEE_) AS assignee,
        RES.NAME_ AS name ,
        RES.EXECUTION_ID_ AS executionId ,
        RES.CREATE_TIME_ AS createTime,
        RES.PROC_DEF_ID_ AS processDefinitionId,
        e.NAME_ as procDefName,arp.KEY_ AS procDefKey
        from ACT_RU_TASK RES
        inner JOIN act_ru_execution e ON RES.PROC_INST_ID_ = e.ID_
        LEFT JOIN act_re_procdef arp ON RES.PROC_DEF_ID_ = arp.ID_
        WHERE
        <if test="procDefName!=null and procDefName!=''">
            arp.NAME_ like concat('%',#{procDefName},'%') and
        </if>
        <if test="taskId!=null and taskId!=''">
            RES.ID_ like concat('%',#{taskId},'%') and
        </if>
        1=1 order by RES.CREATE_TIME_ desc
    </select>

   <select id="getStartedPageSet" resultType="com.sbtr.workflow.dto.StartedDto">
       select distinct RES.* ,
       P.KEY_ as `key`,
       P.ID_ as processDefinitionId, P.NAME_ as processDefinitionName,
       P.VERSION_ as processDefinitionVersion,
       RES.PROC_INST_ID_ AS processInstanceId,
       P.DEPLOYMENT_ID_ as deploymentId from
       ACT_RU_EXECUTION RES inner join ACT_RE_PROCDEF
       P on RES.PROC_DEF_ID_ = P.ID_ WHERE
       <if test="modelName!=null and modelName!=''">
           P.NAME_ like concat('%',#{modelName},'%') and
       </if>
       <if test="modelKey!=null and modelKey!=''">
           P.KEY_ like concat('%',#{modelKey},'%') and
       </if>
       RES.PARENT_ID_ is null order by RES.ID_ desc
   </select>

   <select id="getApplyedTasksOracle" resultType="com.sbtr.workflow.vo.TaskVo">
       SELECT wm_concat(distinct t1.ID_) AS taskId,
       wm_concat(distinct t1.NAME_) AS taskName,
       t3.NAME_ AS formName,
       t3.BUSINESS_KEY_ AS businessKey,
       t3.PROC_INST_ID_ AS processInstanceId,
       t3.TENANT_ID_ AS systemSn,
       f_get_username (t3.START_USER_ID_)  AS userNameId,
       t3.START_TIME_ AS startTime,
       max(t1.END_TIME_) AS endTime,
       wm_concat(distinct t1.TASK_DEF_KEY_) AS nodeId,
       adm.name AS modelName,
       t5.KEY_ AS modelKey,
       t3.PROC_DEF_ID_ AS processDefinitionId
       FROM
           act_hi_taskinst t1
           JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
           LEFT JOIN act_re_procdef t5 ON t3.PROC_DEF_ID_ = t5.ID_
           LEFT JOIN act_de_model adm ON adm.model_key = t5.KEY_
       WHERE
       <if test="processModelType!=null and processModelType!=''">
           adm.process_model_type like '%'||#{processModelType}||'%' and
       </if>
       <if test="modelName!=null and modelName!=''">
           adm.name like '%'||#{modelName}||'%' and
       </if>
       <if test="formName!=null and formName!=''">
           t3.NAME_ like '%'||#{formName}||'%' and
       </if>
       t1.END_TIME_ IS NOT NULL
       AND t1.ASSIGNEE_ = #{userNameId}
       group by t3.PROC_INST_ID_,t3.NAME_,t3.BUSINESS_KEY_,t3.TENANT_ID_,t3.START_USER_ID_,adm.name,t3.START_TIME_,t3.PROC_DEF_ID_,t5.KEY_
       order by max(t1.END_TIME_) desc
   </select>

    <select id="getApplyedTasksMySql" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT group_concat(distinct t1.ID_) AS taskId,
        group_concat(distinct t1.NAME_) AS taskName,
        t3.NAME_ AS formName,
        t3.BUSINESS_KEY_ AS businessKey,
        t3.PROC_INST_ID_ AS processInstanceId,
        t3.TENANT_ID_ AS systemSn,
        f_get_username (t3.START_USER_ID_)  AS userNameId,
        t3.START_TIME_ AS startTime,
        max(t1.END_TIME_) AS endTime,
        group_concat(distinct t1.TASK_DEF_KEY_) AS nodeId,
        adm.name AS modelName,
        t5.KEY_ AS modelKey,
        t3.PROC_DEF_ID_ AS processDefinitionId
        FROM
        act_hi_taskinst t1
        JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        LEFT JOIN act_re_procdef t5 ON t3.PROC_DEF_ID_ = t5.ID_
        left JOIN act_de_model adm on adm.model_key=t5.KEY_
        WHERE
        <if test="processModelType!=null and processModelType!=''">
            adm.process_model_type =#{processModelType} and
        </if>
        <if test="modelName!=null and modelName!=''">
            adm.name like concat('%',#{modelName},'%') and
        </if>
        <if test="formName!=null and formName!=''">
            t3.NAME_ like concat('%',#{formName},'%') and
        </if>
        t1.END_TIME_ IS NOT NULL
        AND t1.ASSIGNEE_ = #{userNameId}
        group by t3.PROC_INST_ID_,t3.NAME_,t3.BUSINESS_KEY_,t3.TENANT_ID_,t3.START_USER_ID_,adm.name,t3.START_TIME_,t3.PROC_DEF_ID_,t5.KEY_
        order by max(t1.END_TIME_) desc
    </select>

   <select id="getTaskCommentsByTaskId" resultType="com.sbtr.workflow.vo.CommentVo">
       SELECT DISTINCT
           t1.ID_ AS id,
           t1.TIME_ AS time,
           t1.TASK_ID_ AS taskId,
           t1.TYPE_ AS TYPE,
           t1.USER_ID_ AS userId,
           t1.MESSAGE_ AS message
       FROM
           act_hi_comment t1
       WHERE
           TASK_ID_ =#{ taskId }
         AND TYPE_ != 'event'
   </select>

   <select id="getListByTaskId" resultType="com.sbtr.workflow.vo.ActHiIdentitylinkVo">
       SELECT DISTINCT
   ID_ AS id,
   GROUP_ID_ AS groupId,
   TYPE_ AS type,
   USER_ID_ AS userId,
   TASK_ID_ AS taskId
   from
   act_hi_identitylink  where TASK_ID_=#{taskId}
   </select>

   <select id="getListByUserNmaeId" resultType="java.lang.String">
SELECT user_name_id FROM sys_user WHERE FIND_IN_SET(( SELECT id FROM sys_auth_group WHERE uuid = #{groupId} ),
user_group
)
   </select>


   <select id="getListPostByUserNmaeId" resultType="java.lang.String">
SELECT
   user_name_id
FROM
   sys_user_post
WHERE
   post = (
   SELECT
       post
   FROM
       sys_user_post
   WHERE
       uuid = #{groupId}
   )
   </select>


   <update id="updateBusinessData">
       ${sql}
   </update>


    <select id="getApplyedTasksSqlserver" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t3.NAME_ AS formName,
        t3.BUSINESS_KEY_ AS businessKey,
        t3.PROC_INST_ID_ AS processInstanceId,
        t3.TENANT_ID_ AS systemSn,
        dbo.f_get_username(t3.START_USER_ID_) as userNameId,
        t1.START_TIME_ AS startTime,
        t1.END_TIME_ AS endTime,
        t1.TASK_DEF_KEY_ AS nodeId,
        adm.name AS modelName,
        t5.KEY_ AS modelKey,t3.PROC_DEF_ID_ as processDefinitionId
        FROM
        act_hi_taskinst t1
        JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        LEFT JOIN act_re_procdef t5 ON t3.PROC_DEF_ID_ = t5.ID_
        left JOIN act_de_model adm on adm.model_key=t5.KEY_

        WHERE
        <if test="processModelType!=null and processModelType!=''">
            adm.process_model_type like concat('%',#{processModelType},'%') and
        </if>
        <if test="modelName!=null and modelName!=''">
            adm.name like concat('%',#{modelName},'%') and
        </if>
        <if test="formName!=null and formName!=''">
            t3.NAME_ like concat('%',#{formName},'%') and
        </if>
        t1.END_TIME_ IS NOT NULL
        AND t1.ASSIGNEE_ = #{userNameId}
        order by  t1.START_TIME_ desc
    </select>

    <select id="getMyHistoryPageSetSqlServer" resultType="com.sbtr.workflow.dto.CommonTaskDto">
        SELECT DISTINCT
        RES.ID_ AS id,
        RES.NAME_ as name,
        DEF.KEY_ AS procDefKey,
        DEF.NAME_ AS procDefName,
        DEF.VERSION_ AS version,
        DEF.DEPLOYMENT_ID_ AS deployment,
        f_get_username(RES.START_USER_ID_) as startUserId,
        RES.START_TIME_ as startTime,
        RES.END_TIME_ as endTime,
        RES.DURATION_ as duration,
        RES.PROC_INST_ID_ as processInstanceId,
        RES.BUSINESS_KEY_ as businessKey,
        DEF.ID_ as processDefinitionId
        FROM
        ACT_HI_PROCINST RES
        LEFT OUTER JOIN ACT_RE_PROCDEF DEF ON RES.PROC_DEF_ID_ = DEF.ID_
        left JOIN act_de_model adm on adm.model_key=DEF.KEY_
        WHERE
        <if test="name!='' and name !=null">
            RES.NAME_ LIKE CONCAT('%',#{name},'%') and
        </if>
        <if test="startTime!='' and startTime!=null">
            ( convert( varchar,RES.START_TIME_,23) BETWEEN #{startDate} and #{endDate} ) and
        </if>
        <if test="procDefName!='' and procDefName !=null">
            DEF.NAME_ LIKE CONCAT('%',#{procDefName},'%') and
        </if>
        <if test="processModelType!='' and processModelType!=null">
            adm.process_model_type =#{processModelType}  and
        </if>
        RES.END_TIME_ IS NOT NULL
        AND RES.START_USER_ID_ =#{userNameId}
        ORDER BY
        RES.END_TIME_ DESC
    </select>

    <select id="getTaskRefByParam" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t1.PROC_INST_ID_ AS processInstanceId,
        t1.task_def_key_ AS nodeId,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        m.act_de_model_key AS modelKey,
        n.node_Code AS nodeCode
        FROM
        act_ru_task t1
        inner JOIN act_my_model m ON t1.PROC_DEF_ID_ = m.procdef_id
        inner JOIN act_ru_execution e ON t1.PROC_INST_ID_ = e.id_
        inner JOIN act_my_node_code n on n.PROCDEF_ID = t1.PROC_DEF_ID_ AND n.NODE_ID = t1.TASK_DEF_KEY_
        where 1 = 1
        <if test="taskId!='' and taskId!=null">
            and t1.ID_ = #{taskId}
        </if>
        <if test="processInstanceId!='' and processInstanceId!=null">
            and t1.PROC_INST_ID_ = #{processInstanceId}
        </if>
        <if test="businessKey!='' and businessKey!=null">
            and e.BUSINESS_KEY_ = #{businessKey}
        </if>
    </select>
    <select id="getCompletedTaskPageSet" resultType="com.sbtr.workflow.dto.CommonTaskDto">
        SELECT
            t1.ID_ AS processInstanceId,
            t1.NAME_ AS formName,
            arp.NAME_ as modelName,
            t1.START_TIME_ AS startTime,
            t1.END_TIME_ AS endTime,
            t1.DURATION_ AS duration,
            f_get_username(t1.START_USER_ID_) as startUserId,
            arp.KEY_ AS modelKey,
            arp.ID_ AS processDefinitionId,
        t1.BUSINESS_KEY_ AS businessKey
        FROM
            act_hi_procinst t1
                LEFT JOIN act_re_procdef arp ON t1.PROC_DEF_ID_ = arp.ID_
        WHERE
        <if test="formName!='' and formName !=null">
            t1.NAME_ = #{formName} and
        </if>
        <if test="modelName!='' and modelName !=null">
            arp.NAME_ = #{modelName}  and
        </if>
        t1.END_TIME_!=''  ORDER BY  t1.START_TIME_ desc
    </select>
    <select id="getTaskCommentsByProcessInstanceId" resultType="com.sbtr.workflow.vo.CommentVo">
        SELECT DISTINCT
            t1.ID_ AS id,
            t1.TIME_ AS time,
           t1.TASK_ID_ AS taskId,
           t1.TYPE_ AS TYPE,
           t1.USER_ID_ AS userId,
           t1.MESSAGE_ AS message
        FROM
            act_hi_comment t1
        WHERE
            PROC_INST_ID_ =#{processInstanceId}
          AND TYPE_ != 'event' and  TYPE_ != 'TJ'
          ORDER by t1.TIME_
    </select>

    <select id="getActHiTaskinstById" resultType="com.sbtr.workflow.vo.CommentVo">
        SELECT DISTINCT
            t1.TASK_DEF_KEY_ AS activityId,
            t1.NAME_ AS activityName
        FROM
            act_hi_taskinst t1
        WHERE
            t1.ID_ =#{id}
    </select>

    <select id="getActHiTaskVoById" resultType="com.sbtr.workflow.vo.TaskVo">
        SELECT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t1.PROC_INST_ID_ AS processInstanceId,
        t1.task_def_key_ AS nodeId,
        t1.PROC_DEF_ID_ AS processDefinitionId,
        m.act_de_model_key AS modelKey,
        e.BUSINESS_KEY_ AS businessKey
        FROM
        ACT_HI_TASKINST t1
        inner JOIN act_my_model m ON t1.PROC_DEF_ID_ = m.procdef_id
        inner JOIN ACT_HI_PROCINST e ON t1.PROC_INST_ID_ = e.id_
        where 1 = 1
        <if test="taskId!='' and taskId!=null">
            and t1.ID_ = #{taskId}
        </if>
        <if test="processInstanceId!='' and processInstanceId!=null">
            and t1.PROC_INST_ID_ = #{processInstanceId}
        </if>
        <if test="businessKey!='' and businessKey!=null">
            and e.BUSINESS_KEY_ = #{businessKey}
        </if>
    </select>

    <select id="getCurrentAssigneeByTask" resultType="java.util.HashMap">
        SELECT
            t.assignee_ AS userNameId,
            t.owner_ AS owner,
            t.PROC_INST_ID_ as procInstId,
            t.TASK_DEF_KEY_ as taskDefKey,
            i.user_id_ userId,
            i.group_id_ AS groupId
        FROM
            act_ru_task t
            INNER JOIN act_ru_execution e ON t.PROC_INST_ID_ = e.PROC_INST_ID_
            LEFT JOIN act_ru_identitylink i ON i.TASK_ID_ = t.ID_
        WHERE
            t.ID_ =#{taskId}
    </select>
    <select id="getOtherAssigneeByTask" resultType="java.util.HashMap">
        SELECT
            t.assignee_ AS userNameId,
            t.owner_ AS owner,
            t.id_ AS taskId
        FROM
            act_ru_task t
        WHERE
            t.PROC_INST_ID_ =#{pid} and t.TASK_DEF_KEY_ =#{kid}
    </select>

    <update id="updateTaskOpeningTimeByKey">
        update act_ru_task set opening_time=#{date} where id_=#{taskId}
    </update>

    <update id="updateMlevelByBusinessKey">
        update act_ru_execution set MLEVEL=#{slevel} where BUSINESS_KEY_=#{businessUuid}
    </update>


    <select id="getActNameByProcessInstanceIdListOracle" resultType="java.util.Map">
        SELECT
        proc_inst_id_,
        act_name_,
        task_id_
        FROM
        act_hi_actinst
        WHERE
        proc_inst_id_ IN
        <foreach collection="processInstanceIdList" index="index" item="processInstanceId" open="(" close=")" separator=",">
            #{processInstanceId}
        </foreach>
        AND act_type_ != 'sequenceFlow'
        ORDER BY
        end_time_ DESC
    </select>

    <select id="getActNameByProcessInstanceIdListMySql" resultType="java.util.Map">
        SELECT
        proc_inst_id_,
        act_name_
        FROM
        act_hi_actinst
        WHERE
        proc_inst_id_ IN
        <foreach collection="processInstanceIdList" index="index" item="processInstanceId" open="(" close=")" separator=",">
            #{processInstanceId}
        </foreach>
        AND act_type_ != 'sequenceFlow'
        ORDER BY
        end_time_ DESC
    </select>

</mapper>
