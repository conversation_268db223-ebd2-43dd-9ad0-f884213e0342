package com.lms.common.model;

import cn.hutool.core.util.ObjectUtil;
import com.lms.common.util.ConstParamUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否成功")
    private boolean success = true;
    @ApiModelProperty(value = "响应信息")
    private String message = "";
    @ApiModelProperty(value = "响应状态码")
    private Integer statusCode = 0;
    @ApiModelProperty(value = "响应数据")
    private T result;
    @ApiModelProperty(value = "当前时间", dataType = "long")
    private long timestamp = System.currentTimeMillis();


    public Result() {
    }

    public Result(Integer statusCode, String message) {
        this.statusCode = statusCode;
        this.message = message;
    }

    public static Result OK() {
        return OK("", null);
    }

    public static <T> Result<T> OK(T data) {
        return OK("", data);
    }

    public static <T> Result<T> OK(String msg, T data) {
        Result<T> r = new Result();
        r.setSuccess(true);
        r.setStatusCode(ConstParamUtil.SUCESSCODE);
        r.setMessage(ObjectUtil.isEmpty(msg) ? "操作成功！" : msg);
        r.setResult(data);
        return r;
    }

    public static Result error(String msg) {
        return error(msg, "");
    }

    public static <T> Result<T> error(String msg, T data) {
        Result<T> r = new Result();
        r.setSuccess(false);
        r.setStatusCode(ConstParamUtil.ERRORCODE);
        r.setMessage(ObjectUtil.isEmpty(msg) ? "操作失败，请联系管理员处理！" : msg);
        r.setResult(data);
        return r;
    }


    public String toString() {
        return "Result(success=" + this.isSuccess() + ", message=" + this.getMessage() + ", code=" + this.getStatusCode() + ", result=" + this.getResult() + ", timestamp=" + this.getTimestamp() + ")";
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }


    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
}

