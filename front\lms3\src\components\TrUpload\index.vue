<template>
  <div class="tr-upload">
    <!-- 拖拽上传区域 -->
    <a-upload-dragger
      v-if="drag"
      v-model:fileList="internalFileList"
      :multiple="multiple"
      :accept="acceptString"
      :disabled="disabled || loading"
      :before-upload="beforeUpload"
      :show-upload-list="showUploadList"
      :custom-request="customRequest"
      class="upload-dragger"
      @change="handleFileChange"
    >
      <div class="upload-content">
        <div v-if="!hasFiles" class="upload-placeholder">
          <CloudUploadOutlined class="upload-icon" />
          <p class="upload-text">{{ dragText || "点击或拖拽文件到此区域上传" }}</p>
          <p class="upload-hint">{{ hint || "支持单个文件上传" }}</p>
        </div>

        <div v-else class="upload-preview">
          <div
            v-for="file in internalFileList"
            :key="file.uid"
            class="file-item"
          >
            <div class="file-info">
              <component :is="getFileIcon(file)" class="file-icon" />
              <div class="file-details">
                <p class="file-name">{{ file.name }}</p>
                <p class="file-size">
                  {{ FileFormatUtils.formatFileSize(file.size || 0) }}
                </p>
              </div>
              <a-button
                v-if="!disabled"
                type="text"
                danger
                size="small"
                class="remove-btn"
                @click.stop="removeFile(file)"
              >
                <DeleteOutlined />
              </a-button>
            </div>

            <!-- 上传进度 -->
            <div v-if="file.status === 'uploading'" class="upload-progress">
              <a-progress :percent="file.percent || 0" size="small" />
              <p class="progress-text">上传中...</p>
            </div>

            <!-- 上传成功 -->
            <div v-else-if="file.status === 'done'" class="upload-success">
              <CheckCircleOutlined class="success-icon" />
              <p class="success-text">上传成功</p>
              
              <!-- 预览区域 -->
              <div
                v-if="showPreview && file.url"
                class="preview-container"
              >
                <!-- 图片预览 -->
                <div v-if="isImage(file)" class="image-preview">
                  <img
                    :src="file.url"
                    :alt="file.name"
                    class="preview-image"
                    @error="handlePreviewError"
                    @click="handlePreview(file)"
                  />
                </div>

                <!-- 视频预览 -->
                <div v-else-if="isVideo(file)" class="video-preview">
                  <video
                    :src="file.url"
                    class="preview-video"
                    controls
                    preload="metadata"
                    @error="handlePreviewError"
                  >
                    您的浏览器不支持视频播放
                  </video>
                </div>

                <!-- 3D模型预览 -->
                <div v-else-if="is3DModel(file)" class="model-preview">
                  <div class="model-preview-placeholder">
                    <AppstoreOutlined class="model-icon" />
                    <p>3D模型预览</p>
                    <p class="model-url">{{ file.url }}</p>
                  </div>
                </div>

                <!-- 其他文件类型 -->
                <div v-else class="file-preview">
                  <FileOutlined class="file-preview-icon" />
                  <p>文件已上传</p>
                  <p class="file-url">{{ file.url }}</p>
                </div>
              </div>
            </div>

            <!-- 上传失败 -->
            <div v-else-if="file.status === 'error'" class="upload-error">
              <CloseCircleOutlined class="error-icon" />
              <p class="error-text">上传失败</p>
            </div>
          </div>
        </div>
      </div>
    </a-upload-dragger>

    <!-- 普通上传按钮 -->
    <a-upload
      v-else
      v-model:fileList="internalFileList"
      :multiple="multiple"
      :accept="acceptString"
      :disabled="disabled || loading"
      :before-upload="beforeUpload"
      :show-upload-list="showUploadList"
      :custom-request="customRequest"
      class="upload-button"
      @change="handleFileChange"
    >
      <a-button :loading="loading" :disabled="disabled">
        <UploadOutlined />
        {{ buttonText || "选择文件" }}
      </a-button>
    </a-upload>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from "vue";
import { message } from "ant-design-vue";
import type { UploadFile, UploadProps } from "ant-design-vue";
import {
  CloudUploadOutlined,
  UploadOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  FileTextOutlined,
  FileImageOutlined,
  VideoCameraOutlined,
  AppstoreOutlined,
  FileOutlined
} from "@ant-design/icons-vue";
import type { TrUploadProps, TrUploadEmits } from "./types";
import { FileTypeUtils, FileValidationUtils, FileFormatUtils, UploadUtils } from "./utils";

// Props定义
const props = withDefaults(defineProps<TrUploadProps>(), {
  multiple: false,
  maxSize: 100,
  showPreview: true,
  autoUpload: true,
  showUploadList: false,
  drag: true,
  disabled: false,
  loading: false,
  buttonText: "选择文件",
  dragText: "点击或拖拽文件到此区域上传",
  hint: "支持单个文件上传"
});

// Emits定义
const emit = defineEmits<TrUploadEmits>();

// 响应式数据
const internalFileList = ref<UploadFile[]>([]);

// 计算属性
const acceptString = computed(() => {
  if (!props.accept || props.accept.length === 0) return undefined;
  return props.accept.join(",");
});

const hasFiles = computed(() => {
  return internalFileList.value.length > 0;
});

// 标记是否正在同步，避免循环更新
let isSyncing = false;

// 监听外部fileList变化
watch(
  () => props.fileList,
  (newFileList) => {
    if (!isSyncing && newFileList) {
      isSyncing = true;
      internalFileList.value = [...newFileList];
      nextTick(() => {
        isSyncing = false;
      });
    }
  },
  { immediate: true, deep: true }
);

// 监听内部fileList变化，同步到外部
watch(
  internalFileList,
  (newFileList) => {
    if (!isSyncing) {
      isSyncing = true;
      emit("update:fileList", newFileList);
      nextTick(() => {
        isSyncing = false;
      });
    }
  },
  { deep: true }
);

/**
 * 文件上传前验证
 */
const beforeUpload = (file: File): boolean | Promise<boolean> => {
  // 文件验证
  const validation = FileValidationUtils.validateFile(file, {
    maxSize: props.maxSize,
    accept: props.accept,
    fileTypes: props.fileTypes
  });

  if (!validation.valid) {
    message.error(validation.message || "文件验证失败");
    return false;
  }

  // 如果设置了自定义上传函数，阻止默认上传
  if (props.customUpload) {
    return false;
  }

  // 如果设置了自动上传，阻止默认上传，使用自定义上传
  if (props.autoUpload) {
    return false;
  }

  return true;
};

/**
 * 自定义上传请求
 */
const customRequest = async (options: any) => {
  const { file, onProgress, onSuccess, onError } = options;

  try {
    // 使用自定义上传函数
    if (props.customUpload) {
      const result = await props.customUpload(file);
      onSuccess(result);
      return;
    }

    // 使用默认上传逻辑
    if (props.autoUpload) {
      const result = await UploadUtils.uploadFile(file, onProgress);
      const processedResult = UploadUtils.processUploadResult(result, file);
      
      // 更新文件信息
      const fileIndex = internalFileList.value.findIndex(f => f.uid === file.uid);
      if (fileIndex !== -1) {
        internalFileList.value[fileIndex] = {
          ...internalFileList.value[fileIndex],
          status: "done",
          url: processedResult.fileUrl,
          response: result
        };
      }

      onSuccess(processedResult);
    }
  } catch (error) {
    console.error("上传失败:", error);
    
    // 更新文件状态为失败
    const fileIndex = internalFileList.value.findIndex(f => f.uid === file.uid);
    if (fileIndex !== -1) {
      internalFileList.value[fileIndex] = {
        ...internalFileList.value[fileIndex],
        status: "error"
      };
    }

    onError(error);
    message.error("文件上传失败");
  }
};

/**
 * 文件变化处理
 */
const handleFileChange = (info: any) => {
  const { file, fileList } = info;

  // 更新内部文件列表
  internalFileList.value = fileList;

  // 触发change事件
  emit("change", info);

  // 处理文件状态变化
  if (file.status === "done") {
    emit("success", file, file.response);
  } else if (file.status === "error") {
    emit("error", file, file.error);
  }
};

/**
 * 移除文件
 */
const removeFile = (file: UploadFile) => {
  const fileIndex = internalFileList.value.findIndex(f => f.uid === file.uid);
  if (fileIndex !== -1) {
    internalFileList.value.splice(fileIndex, 1);
    emit("remove", file);
  }
};

/**
 * 获取文件图标组件
 */
const getFileIcon = (file: UploadFile) => {
  if (!file.originFileObj) return FileOutlined;
  
  const iconName = FileFormatUtils.getFileIcon(file.originFileObj);
  const iconMap: Record<string, any> = {
    FileImageOutlined,
    VideoCameraOutlined,
    AppstoreOutlined,
    FileTextOutlined,
    FileOutlined
  };
  
  return iconMap[iconName] || FileOutlined;
};

/**
 * 文件类型判断
 */
const isImage = (file: UploadFile) => {
  return file.originFileObj ? FileTypeUtils.isImage(file.originFileObj) : false;
};

const isVideo = (file: UploadFile) => {
  return file.originFileObj ? FileTypeUtils.isVideo(file.originFileObj) : false;
};

const is3DModel = (file: UploadFile) => {
  return file.originFileObj ? FileTypeUtils.is3DModel(file.originFileObj) : false;
};

/**
 * 预览处理
 */
const handlePreview = (file: UploadFile) => {
  emit("preview", file);
};

/**
 * 预览错误处理
 */
const handlePreviewError = () => {
  message.error("预览失败");
};

// 暴露方法给父组件
defineExpose({
  fileList: internalFileList,
  upload: customRequest,
  removeFile
});
</script>

<style lang="scss" scoped>
.tr-upload {
  width: 100%;

  .upload-dragger {
    width: 100%;
    // border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    text-align: center;
    cursor: pointer;
    transition: border-color 0.3s;
    box-sizing: border-box;
    overflow: hidden;

    &:hover {
      border-color: #1890ff;
    }

    &.ant-upload-drag-hover {
      border-color: #1890ff;
    }
  }

  .upload-content {
    padding: 20px;
  }

  .upload-placeholder {
    .upload-icon {
      font-size: 48px;
      color: #999;
      margin-bottom: 16px;
    }

    .upload-text {
      font-size: 16px;
      color: #666;
      margin-bottom: 8px;
    }

    .upload-hint {
      font-size: 14px;
      color: #999;
    }
  }

  .upload-preview {
    .file-item {
      margin-bottom: 16px;
      padding: 12px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      background: #fff;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .file-info {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .file-icon {
        font-size: 20px;
        color: #1890ff;
        margin-right: 8px;
      }

      .file-details {
        flex: 1;

        .file-name {
          font-size: 14px;
          color: #333;
          margin: 0 0 4px 0;
          word-break: break-all;
        }

        .file-size {
          font-size: 12px;
          color: #999;
          margin: 0;
        }
      }

      .remove-btn {
        margin-left: 8px;
      }
    }

    .upload-progress {
      margin-top: 8px;

      .progress-text {
        font-size: 12px;
        color: #666;
        margin: 4px 0 0 0;
        text-align: center;
      }
    }

    .upload-success {
      display: flex;
      align-items: center;
      margin-top: 8px;

      .success-icon {
        font-size: 16px;
        color: #52c41a;
        margin-right: 8px;
      }

      .success-text {
        font-size: 14px;
        color: #52c41a;
        margin: 0;
      }
    }

    .upload-error {
      display: flex;
      align-items: center;
      margin-top: 8px;

      .error-icon {
        font-size: 16px;
        color: #ff4d4f;
        margin-right: 8px;
      }

      .error-text {
        font-size: 14px;
        color: #ff4d4f;
        margin: 0;
      }
    }

    .preview-container {
      margin-top: 12px;

      .image-preview {
        .preview-image {
          max-width: 100%;
          max-height: 200px;
          border-radius: 4px;
          cursor: pointer;
          transition: opacity 0.3s;

          &:hover {
            opacity: 0.8;
          }
        }
      }

      .video-preview {
        .preview-video {
          max-width: 100%;
          max-height: 200px;
          border-radius: 4px;
        }
      }

      .model-preview {
        .model-preview-placeholder {
          text-align: center;
          padding: 20px;
          background: #f5f5f5;
          border-radius: 4px;

          .model-icon {
            font-size: 32px;
            color: #999;
            margin-bottom: 8px;
          }

          p {
            margin: 4px 0;
            color: #666;
            font-size: 14px;

            &.model-url {
              font-size: 12px;
              color: #999;
              word-break: break-all;
            }
          }
        }
      }

      .file-preview {
        text-align: center;
        padding: 20px;
        background: #f5f5f5;
        border-radius: 4px;

        .file-preview-icon {
          font-size: 32px;
          color: #999;
          margin-bottom: 8px;
        }

        p {
          margin: 4px 0;
          color: #666;
          font-size: 14px;

          &.file-url {
            font-size: 12px;
            color: #999;
            word-break: break-all;
          }
        }
      }
    }
  }

  .upload-button {
    width: 100%;
  }
}
</style>
