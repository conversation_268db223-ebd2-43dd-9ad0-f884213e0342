<template>
  <div class="custom-tree-component">
    <a-tree
      :tree-data="treeData"
      :selectedKeys="selectedKeys"
      :show-line="true"
      :blockNode="true"
      :placeholder="placeholder"
      @select="handleSelect"
    />
    <div class="event-buttons">
      <a-button
        size="small"
        type="link"
        @click="handleClear"
        v-if="allowClear && selectedKeys.length"
      >
        清空
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";

interface TreeNode {
  title: string;
  key: string;
  children?: TreeNode[];
}

interface Props {
  modelValue?: string[]; // 支持多选可改为string[]
  treeData?: TreeNode[];
  placeholder?: string;
  allowClear?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  treeData: () => [
    {
      title: "节点1",
      key: "0-0",
      children: [
        { title: "子节点1-1", key: "0-0-1" },
        { title: "子节点1-2", key: "0-0-2" }
      ]
    },
    { title: "节点2", key: "0-1" }
  ],
  placeholder: "请选择节点",
  allowClear: true
});

const emit = defineEmits<{
  "update:modelValue": [value: string[]];
  select: [value: string[]];
  clear: [];
}>();

const selectedKeys = ref<string[]>(props.modelValue);

watch(
  () => props.modelValue,
  val => {
    selectedKeys.value = val || [];
  }
);

const handleSelect = (selected: string[], info: any) => {
  selectedKeys.value = selected;
  emit("update:modelValue", selected);
  emit("select", selected);
};

const handleClear = () => {
  selectedKeys.value = [];
  emit("update:modelValue", []);
  emit("clear");
};
</script>

<style scoped>
.custom-tree-component {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
}

.event-buttons {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
  margin-top: 4px;
}

.event-buttons .ant-btn {
  padding: 0 4px;
  height: 20px;
  font-size: 12px;
}
</style>
