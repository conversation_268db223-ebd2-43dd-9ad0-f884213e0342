package com.sbtr.workflow.utils;


import java.util.List;

/**
 * ListUtil
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-05-18 13:40:36
 */
public class ListUtil {

    // 判断List<String> 是否为空  true不为空   false为空
    public static boolean ListStringIsItEmpty(List<String> list) {
        //list存在且里面有元素
        return list != null && !list.isEmpty();
    }

    //List<String> 转string
    public static String ListConvertString(List<String> list) {
        if (!ListStringIsItEmpty(list)) {
            return "";
        }
        return String.join(",", list);
    }

    //List<Object> 是否为空  true不为空   false为空
    public static boolean ListObjectIsItEmpty(List<String> list) {
        //list存在且里面有元素
        return list != null && !list.isEmpty();
    }

}
