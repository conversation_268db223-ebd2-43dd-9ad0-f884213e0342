package com.sbtr.workflow.utils;

import java.util.ArrayList;
import java.util.List;


/**
 * 配置默认跳过请求地址
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:33:52
 */
public class DefaultSkipUrl {
    private static final List<String> DEFAULT_SKIP_URL = new ArrayList<>();

    static {

        //token放开的请求地址
        DEFAULT_SKIP_URL.add("/actuator/info");
        DEFAULT_SKIP_URL.add("/actuator/health");
        DEFAULT_SKIP_URL.add("/login");
        DEFAULT_SKIP_URL.add("/");
        DEFAULT_SKIP_URL.add("/login/login");
        DEFAULT_SKIP_URL.add("/jwtLogin");
        DEFAULT_SKIP_URL.add("/codeItem/getListByCodeSetIdAndLevelId");
        DEFAULT_SKIP_URL.add("/codeItem/getListByPid");
        DEFAULT_SKIP_URL.add("/codeItem/getFatherIds");
        DEFAULT_SKIP_URL.add("/codeItem/getZoneFillbackData");
        DEFAULT_SKIP_URL.add("/organization/getListByLevelId");
        DEFAULT_SKIP_URL.add("/organization/getListByPid");
        DEFAULT_SKIP_URL.add("/user/getPageSet");
        DEFAULT_SKIP_URL.add("/po/poserver.zz");
        DEFAULT_SKIP_URL.add("/apiFlowableTask/getFlowImgByExecutionId");
        DEFAULT_SKIP_URL.add("/apiFlowableTask/downLoadXmlByModelId");
        //swagger
        DEFAULT_SKIP_URL.add("/v2/api-docs");
    }

    public static List<String> getDefaultSkipUrl() {
        return DEFAULT_SKIP_URL;
    }

}
