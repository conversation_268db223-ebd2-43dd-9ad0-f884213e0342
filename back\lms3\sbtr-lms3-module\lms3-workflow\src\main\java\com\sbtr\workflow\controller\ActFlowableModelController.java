package com.sbtr.workflow.controller;


import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.service.ApiFlowableBpmnModelService;
import com.sbtr.workflow.service.ApiFlowableModelService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.vo.ActDeModelVo;
import com.sbtr.workflow.vo.ModelVo;
import com.sbtr.workflow.vo.TaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.flowable.engine.RepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 流程模型
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
@Api(tags = {"流程模型" })
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/apiFlowableModel")
public class ActFlowableModelController extends WorkflowBaseController {

    @Autowired
    private ApiFlowableModelService apiFlowableModelService;

    @Autowired
    private ApiFlowableBpmnModelService apiFlowableBpmnModelService;

    @Autowired
    protected RepositoryService repositoryService;

    @ApiOperation(value="流程模型分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "pageSize", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "modelName", value = "模型名称", defaultValue = "modelName", required = true, dataType = "String"),
            @ApiImplicitParam(name = "modelKey", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/getModleList", method = RequestMethod.POST)
    public Object getModleList(PageParam pageParam, String modelName, String modelKey,String modelType) {
        PageSet<ActDeModelVo> pageSet = apiFlowableModelService.getModleList(pageParam,modelName,modelKey,modelType);
        return pageSet;
    }

    @ApiOperation(value="流程模型版本记录分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "pageSize", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "modelName", value = "模型名称", defaultValue = "modelName", required = true, dataType = "String"),
            @ApiImplicitParam(name = "modelKey", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/getPageSet", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam, String modelName, String modelKey,String modelType) {
        PageSet<ActDeModelVo> pageSet = apiFlowableModelService.getPageSet(pageParam,modelName,modelKey,modelType);
        return pageSet;
    }


    @Log(title = "流程模型-流程部署",module = LogModule.WORKFLOW,businessType = BusinessType.DEPLOY, method = "getLogMessage")
    @ApiOperation(value="流程部署")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "modelId", value = "模型Id", defaultValue = "modelId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "categoryCode", value = "分类标识", defaultValue = "categoryCode", required = true, dataType = "String"),
    })
    @ResponseBody
    @RequestMapping(value = "/deployModelId", method = RequestMethod.POST)
    public Object deployModelId(String modelId, String categoryCode,String procdefId) {
        setLogMessage(apiFlowableModelService.getModelNameById(modelId),"");
        return apiFlowableModelService.processDeployment(modelId, categoryCode,procdefId);
    }

    @Log(title = "流程模型-流程复制",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @ApiOperation(value="流程复制")
    @ResponseBody
    @RequestMapping(value = "/copyProcess", method = RequestMethod.POST)
    public Object copyProcess(String modelId, String categoryCode,String procdefId) {
        setLogMessage(apiFlowableModelService.getModelNameById(modelId),"");
        return apiFlowableModelService.copyProcess(modelId, categoryCode,procdefId);
    }

    @ApiOperation(value="导出bpmn.xml")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "modelId", value = "模型Id", defaultValue = "1", required = true, dataType = "String")
    })
    @RequestMapping(value = "/downLoadXmlByModelId", method = RequestMethod.GET)
    public void downLoadXmlByModelId(String modelId, HttpServletResponse response) {
         apiFlowableModelService.downLoadXmlByModelId(modelId, response);
    }



    @Log(title = "流程模型-模型保存",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @ApiOperation(value="模型保存")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "modelId", value = "模型Id", defaultValue = "1", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/addModel", method = RequestMethod.POST)
    public Object addModel(ModelVo modelId) {
        setLogMessage(modelId.getProcessName(),"");
        return apiFlowableBpmnModelService.addModel(modelId,null,null);
    }


    /**
     * 获取模型编辑的数据
     *
     * @param modelId
     * @Return void
     */
    @ResponseBody
    @RequestMapping(value = "/editorModelData", method = RequestMethod.POST)
    public Object editorModel(String modelId) {
        return apiFlowableBpmnModelService.editorModelData(modelId);
    }


    /**
     * 模型导入
     *
     * @param file
     * @Return void
     */
    @ResponseBody
    @RequestMapping(value = "/importProcessModel", method = RequestMethod.POST)
    public Object importProcessModel(@RequestParam("file") MultipartFile file) throws IOException {
        return apiFlowableBpmnModelService.importProcessModel(file);
    }


    @Log(title = "流程模型-模型删除",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ApiOperation(value="模型删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "modelId", value = "模型Id", defaultValue = "1", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/deleteModel", method = RequestMethod.POST)
    @Transactional
    public Object deleteModel(String modelId,String procdefId,int delType)   {
        setLogMessage(apiFlowableModelService.getModelNameById(modelId),"");
        Object o = apiFlowableModelService.deleteModelByModelId(modelId, procdefId, delType);
        apiFlowableModelService.deleteDateByModelId(modelId); // 删除act_de_model表数据
        return o;
    }



    @ApiOperation(value="根据模型key查询所有对应的版本")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "pageSize", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "modelKey", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/getListByModelKey", method = RequestMethod.POST)
    public Object getListByModelKey(PageParam pageParam, String modelKey)   {
        PageSet<ActDeModelVo> pageSet = apiFlowableModelService.getListByModelKey(pageParam,modelKey);
        return pageSet;
    }


    @Log(title = "流程模型-设置主版本",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE)
    @ApiOperation(value="设置主版本")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "procdefId", value = "流程定义Id", defaultValue = "procdefId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "modelKey", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/setMajorVersion", method = RequestMethod.POST)
    public Object setMajorVersion(String procdefId,String modelKey,String name)   {
        Object object = apiFlowableModelService.setMajorVersion(procdefId,modelKey,name);
        return object;
    }

    @ResponseBody
    @RequestMapping(value = "/moveUserTaskUpper", method = RequestMethod.POST)
    public List<TaskVo> moveUserTaskUpper(String procdefId, String nodeId, String processInstanceId)   {
        return   apiFlowableModelService.moveUserTaskUpper(procdefId,nodeId,processInstanceId);
    }



    @Log(title = "流程模型-模型验证",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE)
    @ApiOperation(value="模型验证")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "modelXml", value = "模型xml", defaultValue = "modelXml", required = true, dataType = "String"),
    })
    @ResponseBody
    @RequestMapping(value = "/validatorModel", method = RequestMethod.POST)
    public Object validatorModel(String modelXml)   {
        Object object = apiFlowableModelService.validatorModel(modelXml);
        return object;
    }

}
