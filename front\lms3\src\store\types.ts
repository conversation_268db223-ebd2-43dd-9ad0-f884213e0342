import type { RouteRecordName } from "vue-router";

export type cacheType = {
  mode: string;
  name?: RouteRecordName;
};

export type positionType = {
  startIndex?: number;
  length?: number;
};

export type appType = {
  sidebar: {
    opened: boolean;
    withoutAnimation: boolean;
    // 判断是否手动点击Collapse
    isClickCollapse: boolean;
  };
  layout: string;
  device: string;
  viewportSize: { width: number; height: number };
};

export type multiType = {
  path: string;
  name: string;
  meta: any;
  query?: object;
  params?: object;
};

export type setType = {
  title: string;
  fixedHeader: boolean;
  hiddenSideBar: boolean;
};

export type userType = {
  avatar?: string;
  username?: string;
  nickname?: string;
  roles?: Array<string>;
  permissions?: Array<string>;
  isRemembered?: boolean;
  loginDay?: number;
  userid?: string;
  personid?: string;
  cardNum?: string;
  personname?: string;
  deptid?: string;
  deptname?: string;
  specialityid?: string;
  dutyid?: string;
  equipmentid?: string;
  persontype?: string;
  usertype?: any;
  token?: string;
  logininfo?: string;
  loginmsg?: string;
  curdate?: string;
  personimage?: string;
  slevel?: any;
  language?: string;
  roleid?: string;
  rolename?: string;
  showDialog?: boolean;
};
