package com.sbtr.workflow.service;

import com.sbtr.workflow.vo.ModelVo;
import org.flowable.bpmn.model.Activity;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.EndEvent;
import org.flowable.bpmn.model.FlowNode;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 流程bpmnmodel
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:35:23
 */
public interface ApiFlowableBpmnModelService {

    /**
     * 通过流程定义id获取BpmnModel
     *
     * @param processDefId 流程定义id
     * @return
     */
    BpmnModel getBpmnModelByProcessDefId(String processDefId);


    /**
     * 判断节点是不是子流程的节点
     * @param processDefId 流程定义id
     * @param activityId 节点id
     * @return
     */
    boolean checkActivitySubprocessByActivityId(String processDefId, String activityId);
    /**


    /**
     * 获取end节点
     *
     * @param processDefId 流程定义id
     * @return FlowElement
     */
    List<EndEvent> findEndFlowElement(String processDefId);

    /**
     * 通过名称获取节点
     *
     * @param processDefId 流程定义id
     * @param name         节点名称
     * @return
     */
    Activity findActivityByName(String processDefId, String name);

    /**
     * 查找节点
     *
     * @param processDefId 流程定义id
     * @param activityId   节点id
     * @return
     */
    FlowNode findFlowNodeByActivityId(String processDefId, String activityId);


    /**
     * 添加模型
     * @param modelVo
     * @return
     */
    Object addModel(ModelVo modelVo, String formFieldList,String formBtnList);


    /**
     * 获取更新数据
     * @param modelId
     * @return
     */
    Object editorModelData(String modelId);

    /**
     * 导入模型
     * @param file
     * @return
     */
    Object importProcessModel(MultipartFile file) throws IOException;




}
