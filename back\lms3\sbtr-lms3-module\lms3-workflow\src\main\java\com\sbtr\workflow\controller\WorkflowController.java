package com.sbtr.workflow.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.enums.FlowEnum;
import com.sbtr.workflow.utils.BaseUtils;
import cn.ewsd.common.utils.CookieUtil;
import cn.ewsd.common.utils.HttpRequestUtils;
import cn.ewsd.common.utils.JsonUtils;
import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import cn.ewsd.common.utils.easyui.PageUtils;
import cn.hutool.http.HttpRequest;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.dto.*;
import com.sbtr.workflow.model.*;
import com.sbtr.workflow.properties.MyProperties;
import com.sbtr.workflow.service.*;
import com.sbtr.workflow.utils.AddHisCommentCmd;
import com.sbtr.workflow.utils.Result;
import com.sbtr.workflow.utils.tree.TreeUtils;
import com.sbtr.workflow.vo.CommentVo;
import com.sbtr.workflow.vo.ProcessDefinitionVo;
import com.sbtr.workflow.vo.TaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.cmmn.engine.impl.process.ProcessInstanceService;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.RepositoryServiceImpl;
import org.flowable.engine.impl.cmd.GetDeploymentProcessDiagramCmd;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.Model;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.image.ProcessDiagramGenerator;
import org.flowable.image.impl.DefaultProcessDiagramGenerator;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.flowable.ui.modeler.domain.AbstractModel;
import org.flowable.ui.modeler.serviceapi.ModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.annotation.Transient;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 原始流程界面控制器
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/workflow")
@Api(tags = {"流程管理"})
public class WorkflowController extends WorkflowBaseController {

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private ProcessEngineConfiguration processEngineConfiguration;

    @Autowired
    private ModelService modelService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private IdentityService identityService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    public MyProperties myProperties;

    @Resource
    private MyVariableService myVariableService;

    @Autowired
    private ManagementService managementService;

    @Resource
    private ImageService imageService;

    @Resource
    private BusinessSystemDataService businessSystemDataService;

    private static final String PROCESS_DEFINE_KEY = "vacation";

    /**
     * 所有可申请的流程
     */
    @RequestMapping("/all")
    public String all(PageParam pageParam) {
        String filterSort = "";
        filterSort = BaseUtils.filterSort(request, filterSort);
        //PageSet<ProcessDefinitionDto> processDefinitions = workflowService.getDeployedPageSet(pageParam, "all", "", "");
        PageSet<ProcessDefinitionVo> processDefinitions = workflowService.getDeployedPageSet(pageParam, null, "1", null);
        request.setAttribute("processDefinitions", processDefinitions);
        return "workflow/workflow/all";
    }

    ///**
    // * 获得流程定义分页集
    // *
    // * @return
    // */
    //@ResponseBody
    //@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
    //@RequestMapping(value = "/getProcessDefinitionPageSetData", method = RequestMethod.POST)
    //@ApiOperation(value = "获得流程定义分页集")
    //@ApiImplicitParams({
    //        @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
    //        @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
    //        @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
    //        @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String")
    //})
    //public Object getProcessDefinitionPageSetData(PageParam pageParam, String processDefinitionName) {
    //    String filterSort = "";
    //    filterSort = BaseUtils.filterSort(request, filterSort);
    //    PageSet<ProcessDefinitionDto> processDefinitionDtoPageSet = workflowService.getDeployedPageSet(pageParam, "latest", processDefinitionName, "");
    //    return processDefinitionDtoPageSet;
    //}
    //

    @ApiOperation(value = "流程定义列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "pageSize", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "category", value = "分类标识", defaultValue = "category", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/getProcessDefinitionPageSetData", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam, String category, String name) {
        PageSet<ProcessDefinitionVo> pageSet = workflowService.getDeployedPageSet(pageParam, category, "1", name);
        return pageSet;
    }


    /**
     * 启动流程页面
     */
    @Log(title = "启动流程页面",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @RequestMapping(value = "add", method = RequestMethod.GET)
    @ApiOperation(value = "启动流程页面")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "procDefId", value = "流程定义ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "processName", value = "流程名称", required = true, dataType = "String")
    })
    public String add(String procDefId, String processName) {
        String businessId = BaseUtils.UUIDGenerator();
        request.setAttribute("businessId", businessId);
        request.setAttribute("procDefId", procDefId);
        request.setAttribute("processName", processName);
        setLogMessage(processName,"");
        return "workflow/workflow/add";
    }

    /**
     * 打开流程处理界面
     */
    @RequestMapping("/processDeal")
    public String processDeal(String taskId, String executionId, String businessTitle, String processDefinitionId, String completeTaskUrl) {
        String currentUserNameId = getCurrentUserNameId();
        CommonTaskDto commonTaskDto = workflowService.getTaskBeanById(taskId, currentUserNameId);

        //leave.setTitle(taskService.getVariable(taskId, "businessTitle").toString());

        String processInstanceId = taskService.createTaskQuery().taskId(taskId).singleResult().getProcessInstanceId();
        String businessKey = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId).singleResult().getBusinessKey();

        request.setAttribute("commonTaskDto", commonTaskDto);

        List<CommentBean> commentBeanList = workflowService.getCommentBeanByTaskId(taskId);
        request.setAttribute("commentBeanList", commentBeanList);

        request.setAttribute("executionId", executionId);
        request.setAttribute("businessTitle", businessTitle);
        request.setAttribute("processDefinitionId", processDefinitionId);
        request.setAttribute("completeTaskUrl", completeTaskUrl);
        return "workflow/workflow/processDeal";
    }

    /**
     * 根据任务Id获取到审批意见 待办
     */
    @ResponseBody
    @RequestMapping("/getListByTaskId")
    public List<CommentBean> getListByTaskId(String taskId) {
        List<CommentBean> commentBeanList = workflowService.getCommentBeanByTaskId(taskId);
        return commentBeanList;
    }

    /**
     * 历史
     */
    @ResponseBody
    @RequestMapping("/getListByProcessInstanceId")
    public List<CommentBean> getListByProcessInstanceId(@RequestParam("taskId") String taskId) {
        List<CommentBean> commentBeanList = workflowService.getCommentListByProcessInstanceId(request.getParameter("taskId"));
        return commentBeanList;
    }

    @ResponseBody
    @RequestMapping("/getcommentByTaskId")
    public List<CommentBean> getcommentByTaskId(String taskId) {
        List<CommentBean> commentBeanList = workflowService.getCommentBeanByTaskId(request.getParameter("taskId"));
        return commentBeanList;
    }

    @RequestMapping("/comment")
    public String comment(String taskId) {
        List<CommentBean> commentBeanList = workflowService.getCommentBeanByTaskId(taskId);
        request.setAttribute("commentBeanList", commentBeanList);

        return "workflow/workflow/comment";
    }

    @RequestMapping("/historyProcessComment")
    public String historyProcessComment(String processInstanceId) {
        List<CommentBean> commentBeanList = workflowService.getCommentListByProcessInstanceId(processInstanceId);
        request.setAttribute("commentBeanList", commentBeanList);

        return "workflow/workflow/comment";
    }

    @ResponseBody
    @RequestMapping("/getCommonTaskDto")
    public CommonTaskDto getCommonTaskDto(String taskId, String userNameId) {
        return workflowService.getTaskBeanById(taskId, userNameId);
    }

    @ResponseBody
    @RequestMapping("/getCommentBeanByTaskId")
    public List<CommentBean> getCommentBeanByTaskId(String taskId) {
        return workflowService.getCommentBeanByTaskId(taskId);
    }

    @Log(title = "流程处理",module = LogModule.WORKFLOW,businessType = BusinessType.APPROVE, method = "getLogMessage")
    @ResponseBody
    @RequestMapping("/completeTask")
    public Object completeTask(@RequestParam Map<String, Object> map) {
        try {
            String taskId = map.get("taskId").toString();
            String comment = map.get("comment").toString();
            String userNameId = getCurrentUserNameId();
            map.remove("taskId");
            map.remove("comment");

            //设置流程连线中需要的变量
            List varNames = workflowService.getVarsNameByProcessDefinitionId(map.get("processDefinitionId").toString());
            Map<String, Object> procVars = new HashMap<>();
            for (int i = 0; i < varNames.size(); i++) {
                String varName = varNames.get(i).toString();
                procVars.put(varName, map.get(varName));
            }
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            setLogMessage(task.getName(),"");
            //单独处理委派
            if (DelegationState.PENDING.equals(task.getDelegationState())) {
                TaskEntity tasks = workflowService.createSubTask((TaskEntityImpl) task, userNameId);
                taskService.complete(tasks.getId());
                taskService.resolveTask(taskId, procVars);
                managementService.executeCommand(new AddHisCommentCmd(tasks.getId(), userNameId, task.getProcessInstanceId(),
                        "comment", comment));
            } else {
                //单独处理加签
                String parentTaskId = task.getParentTaskId();
                if (!StringUtils.isEmpty(parentTaskId)) {
                    String tableName = managementService.getTableName(TaskEntity.class);
                    String sql = "select count(1) from " + tableName + " where PARENT_TASK_ID_=#{parentTaskId}";
                    long subTaskCount = taskService.createNativeTaskQuery().sql(sql).parameter("parentTaskId", parentTaskId).count();
                    if (subTaskCount == 0) {
                        Task tasks = taskService.createTaskQuery().taskId(parentTaskId).singleResult();
                        //处理前后加签的任务
                        taskService.resolveTask(parentTaskId);
                        if ("after".equals(tasks.getScopeType())) {
                            taskService.complete(parentTaskId);
                        }
                    }
                    managementService.executeCommand(new AddHisCommentCmd(task.getId(), userNameId, task.getProcessInstanceId(),
                            "comment", comment));
                } else {
                    workflowService.completeTask(taskId, comment, userNameId, procVars);
                }
            }
            return JsonUtils.messageJson(200, "操作成功", "审批成功");
        } catch (
                Exception e) {
            e.printStackTrace();
            return JsonUtils.messageJson(300, "操作失败", "审批失败");
        }

    }


    @ResponseBody
    @RequestMapping("/completeTaskWithVars")
    public void completeTaskWithVars(String taskId, String comment, String userNameId, @RequestBody Map<String, Object> map) {
        workflowService.completeTask(taskId, comment, userNameId, map);
    }

    /**
     * 获得当前任务所有出口
     */
    @ResponseBody
    @RequestMapping("/getOutComeList")
    public Object getOutComeList(String taskId, String modelKey, String nodeId, String processDefinitionId) {
        //以前版本 根据线上文字渲染数据出来
        //List<String> strings = new ArrayList<>();
        //List<String> outcomes = workflowService.getTaskOutcomes(taskId);
        //for (int i = 0; i < outcomes.size(); i++) {
        //    int size = outcomes.get(i).split(",").length;
        //    for (int j = 0; j < size; j++) {
        //        strings.add(outcomes.get(i).split(",")[j]);
        //    }
        //}
        //return strings;
        Example examples = new Example(ActMyNodeButton.class);
        Example.Criteria criterias = examples.createCriteria();
        criterias.andEqualTo("actDeModelKey", modelKey);
        criterias.andEqualTo("nodeId", nodeId);
        if (org.apache.commons.lang.StringUtils.isNotBlank(processDefinitionId)) {
            criterias.andEqualTo("procdefId", processDefinitionId);
        }
        List<ActMyNodeButton> flowNodeButtons = flowNodeButtonService.selectByExample(examples);
        List<String> strings = new ArrayList<>();
        for (int i = 0; i < flowNodeButtons.size(); i++) {
            strings.add(flowNodeButtons.get(i).getNodeButtonName());
        }
        return strings;
    }

    /**
     * 流程处理
     *
     * @param taskId
     * @param comment
     * @param outcome
     * @return
     * @throws Exception
     */
    @Log(title = "流程处理",module = LogModule.WORKFLOW,businessType = BusinessType.APPROVE, method = "getLogMessage")
    @ResponseBody
    @RequestMapping(value = "/doTask", method = RequestMethod.POST)
    @ApiOperation(value = "流程处理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId", value = "任务ID", defaultValue = "0", required = true, dataType = "String"),
            @ApiImplicitParam(name = "comment", value = "内容", defaultValue = "0", required = true, dataType = "String"),
            @ApiImplicitParam(name = "outcome", value = "同意/驳回", defaultValue = "同意", required = true, dataType = "String")
    })
    public Object doTask(String taskId, String comment, String outcome) throws Exception {
        try {
            String currentUserNameId = getCurrentUserNameId();

            Map<String, Object> vars = new HashMap<>(4);
            vars.put("outcome", outcome);
            vars.put("days", 3);
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if(task!=null)
                setLogMessage(task.getName(),"");

            workflowService.completeTask(taskId, comment, currentUserNameId, vars);
            return JsonUtils.messageJson(200, "操作成功", "审批成功");
        } catch (Exception e) {
            e.printStackTrace();
            return JsonUtils.messageJson(300, "操作失败", "审批失败");
        }
        //return "redirect:toDoList";
    }

    /**
     * 待办流程
     */
    @RequestMapping("/toDoList")
    public String toDoList(PageParam pageParam) {
        /*String currentUserNameId = getCurrentUserNameId();
        PageSet<CommonTaskDto> commonTaskDtos = workflowService.getTasksByAssignee(pageParam, currentUserNameId);
        request.setAttribute("commonTaskDtos", commonTaskDtos);*/
        return "workflow/workflow/toDoList";
    }

    /**
     * 获得待办列表分页集
     *
     * @param pageParam
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getToDoListPageSetData", method = RequestMethod.POST)
    @ApiOperation(value = "获得待办列表分页集")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String")
    })
    public Object getToDoListPageSetData(PageParam pageParam) {
        String currentUserNameId = getCurrentUserNameId();
        PageSet<TaskCommon> pageSet = workflowService.getTasksByAssignee(pageParam, currentUserNameId, request, "1");
        for (int i = 0; i < pageSet.getRows().size(); i++) {
            List<ActRuVariable> list = removeDupliById(pageSet.getRows().get(i).getActRuVariables());
            if (null != list && list.size() != 0) {
                //2.iterator遍历，查出对应值，做对应操作
                Iterator<ActRuVariable> it = list.iterator();
                while (it.hasNext()) {
                    ActRuVariable x = it.next();
                    if (StringUtils.isEmpty(x.getText())) {
                        it.remove();
                    }
                }
                Map<String, Object> result = list.stream().collect(Collectors.toMap(student -> {
                    return student.getName();
                }, ActRuVariable::getText));
                pageSet.getRows().get(i).setVariables(result);
            }

        }
        return pageSet;
    }

    /**
     * 根据对象属性去重  属性：userId
     *
     * @param persons
     * @return
     */
    public static List<ActRuVariable> removeDupliById(List<ActRuVariable> persons) {
        Set<ActRuVariable> personSet = new TreeSet<>((o1, o2) -> o1.getName().compareTo(o2.getName()));
        personSet.addAll(persons);
        return new ArrayList<>(personSet);
    }


    @ResponseBody
    @RequestMapping(value = "kaifengTreatment", method = RequestMethod.POST)
    public Object kaifengTreatment(String taskId, String instanceId)   {
        //查询是否已处理开封时间
        int result = 0;
        TaskCommon taskCommon = workflowService.getDetailsByKey(taskId);
        Date date = DateUtil.date();
        if (null == taskCommon.getOpeningTime()) {
            result = workflowService.updateTaskCommonOpeningTimeByKey(taskId, date);
//            workflowService.updateActHiActinst(taskId, date);
        } else {
            result = 1;
        }
        return result > 0 ? success("开封成功！") : failure("开封成功！");
    }


    @ResponseBody
    @RequestMapping(value = "withdrawSteps", method = RequestMethod.POST)
    public Object withdrawSteps(String taskId, String procInstId) throws Exception {
        //查询是否已处理开封时间
        int result = 0;
        //查询到运行表未开封流程
        Map<String, Object> actRuTask = workflowService.getActRuTaskByProcInstId(procInstId);
        //查询未开封流程上两步流程 下标0是上两步，下标1是上一步
        List<Map<String, Object>> actHiActinst = workflowService.getActHiActinstByProcInstId(procInstId, "userTask");
        //删除运行表未开封流程
        result = workflowService.delectActRuTaskByKey(actRuTask.get("ID_").toString());
        //添加上一步骤到运行流程表
        Map<String, Object> map = new HashMap<>();
        map.put("ID_", BaseUtils.UUIDGenerator());
        map.put("REV_", actHiActinst.get(0).get("REV_"));
        map.put("EXECUTION_ID_", actHiActinst.get(0).get("EXECUTION_ID_"));
        map.put("PROC_INST_ID_", actHiActinst.get(0).get("PROC_INST_ID_"));
        map.put("PROC_DEF_ID_", actHiActinst.get(0).get("PROC_DEF_ID_"));
        map.put("NAME_", actHiActinst.get(0).get("ACT_NAME_"));
        map.put("PARENT_TASK_ID_", actHiActinst.get(0).get("ACT_ID_"));
        map.put("ASSIGNEE_", actHiActinst.get(0).get("ASSIGNEE_"));
        map.put("CREATE_TIME_", actHiActinst.get(0).get("CREATE_TIME_"));
        map.put("SUSPENSION_STATE_", actHiActinst.get(0).get("SUSPENSION_STATE_"));
        map.put("IS_COUNT_ENABLED_", 1);
        map.put("VAR_COUNT_", 0);
        map.put("ID_LINK_COUNT_", 0);
        map.put("SUB_TASK_COUNT_", 0);
        int a = workflowService.addActRuTask(map);
        //将上一步流程重新加入流程节点运行表
        Map<String, Object> procVars = new HashMap<>();
        procVars.put("outcome", "驳回");
        taskService.complete(taskId, procVars);
//        workflowService.completeTask(actHiActinst.get(0).get("TASK_ID_").toString(), "同意", getCurrentUserNameId(), procVars);
        return result > 0 ? success("开封成功！") : failure("开封成功！");
    }

    /**
     * 我的流程
     */
    @RequestMapping("/index")
    public String index(PageParam pageParam) {
        String currentUserNameId = getCurrentUserNameId();
        PageSet<CommonTaskDto> commonTaskDtos = workflowService.getTasksByUserNameId(pageParam, currentUserNameId);
        //List<TaskBean> leaveTasks = workflowService.getTasksByAssigneeAndUserNameId(getCurrentUserNameId(), getCurrentUserNameId());
        request.setAttribute("commonTaskDtos", commonTaskDtos);
        return "workflow/workflow/index";
    }

    @ResponseBody
    @RequestMapping(value = "/getMyNoEndProcessPageSetData", method = RequestMethod.POST)
    @ApiOperation(value = "获得在办列表分页集")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String")
    })
    public Object getMyNoEndProcessPageSetData(PageParam pageParam, String processDefinitionName) {
        String currentUserNameId = getCurrentUserNameId();
        PageSet<ProcessInstanceDto> pageSet = workflowService.getMyNoEndProcessPageSetData(pageParam, currentUserNameId, processDefinitionName, "1");
        for (int i = 0; i < pageSet.getRows().size(); i++) {
            List<ActRuVariable> list = removeDupliById(pageSet.getRows().get(i).getActRuVariables());
            if (null != list && list.size() != 0) {
                //2.iterator遍历，查出对应值，做对应操作
                Iterator<ActRuVariable> it = list.iterator();
                while (it.hasNext()) {
                    ActRuVariable x = it.next();
                    if (StringUtils.isEmpty(x.getText())) {
                        it.remove();
                    }
                }
                Map<String, Object> result = list.stream().collect(Collectors.toMap(student -> {
                    return student.getName();
                }, ActRuVariable::getText));
                pageSet.getRows().get(i).setVariables(result);
            }

        }
        return pageSet;
    }

    /**
     * 流程文件模型页面
     */
    @RequestMapping(value = "/template", method = RequestMethod.GET)
    public String template(ModelAndView mav) {
        //mav.addObject("list", workflowService.getFlows());
        //mav.setViewName("{theme}/workflow/template");
        //return mav;
        //request.setAttribute("list", workflowService.getFlows());
        return display("workflow/workflow/template");
    }

    /**
     * 流程文件模型分页数据
     */
    @ResponseBody
    @RequestMapping(value = "/getTemplatePageSet")
    public Object getTemplatePageSet(PageParam pageParam) {
        PageSet<TemplateDto> pageSet = workflowService.getFlowsPageSet();
        return pageSet;
    }

    //流程数据模型页面
    @RequestMapping(value = "/dataModel", method = RequestMethod.GET)
    @ApiOperation(value = "打开流程部署页面")
    public String dataModel() {
        return display("workflow/workflow/data_model");
    }

    //流程数据模型分页数据
    @ResponseBody
    @RequestMapping(value = "/getDataModelPageSet", method = RequestMethod.POST)
    @ApiOperation(value = "获得流程定义分页集数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String")
    })
    public Object getDataModelPageSet(PageParam pageParam) {
        String filterSort = "";
        filterSort = BaseUtils.filterSort(request, filterSort).replace(" ORDER BY create_time DESC", "").replace("key LIKE", "MODEL_KEY LIKE");
        PageHelper.startPage(pageParam.getPage(), pageParam.getRows());
        //原生
        //List<AbstractModel> abstractModelss = modelService.getModelsByModelType(0);
        List<AbstractModel> abstractModels = myVariableService.getModelsByModelType(filterSort, 0);
        PageInfo<AbstractModel> pageInfo = new PageInfo<>(abstractModels);
        return PageUtils.getPageSet(pageInfo);
    }

    /**
     * 部署数据模型流程
     */
    @Log(title = "数据模型流程部署",module = LogModule.WORKFLOW,businessType = BusinessType.DEPLOY, method = "getLogMessage")
    @ResponseBody
    @RequestMapping("deployByModelId")
    public Object deployByModelId(String modelId) {
        try {
            org.flowable.ui.modeler.domain.Model model = modelService.getModel(modelId);
            BpmnModel bpmnModel = modelService.getBpmnModel(model);
            setLogMessage(model.getName(),"");
            //Process process;
            //   不能设置 key(model.getKey()).tenantId(BaseUtils.UUIDGenerator()
            //            Deployment deployment = repositoryService.createDeployment()
            //        .name(model.getName()).key(model.getKey()).tenantId(BaseUtils.UUIDGenerator())
            //           .addBpmnModel(model.getKey() + ".bpmn", bpmnModel).deploy();
            Deployment deployment = repositoryService.createDeployment()
                    .name(model.getName())
                    .addBpmnModel(model.getKey() + ".bpmn", bpmnModel).deploy();
            return success("流程部署成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return failure("流程部署失败！");
        }
    }

    /**
     * 已部署流程
     */
    @RequestMapping("/deployed")
    public String deployed(String processName, ModelAndView mav, PageParam pageParam) {
        String filterSort = "";
        filterSort = BaseUtils.filterSort(request, filterSort);
        workflowService.deployFlow(processName);
        PageSet<ProcessDefinitionVo> processDefinitions = workflowService.getDeployedPageSet(pageParam, "", "1", null);
        request.setAttribute("processDefinitions", processDefinitions);
        return display("workflow/workflow/deployed");
    }

    /**
     * 部署流程
     */
    @Log(title = "流程部署",module = LogModule.WORKFLOW,businessType = BusinessType.DEPLOY, method = "getLogMessage")
    @ResponseBody
    @RequestMapping("deploy")
    public Object deploy(String processName, ModelAndView mav, PageParam pageParam) {
        try {
            setLogMessage(processName,"");
            workflowService.deployFlow(processName);
            return success("流程部署成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return failure("流程部署失败！");
        }
    }

    //https://blog.csdn.net/liuwenjun05101/article/details/86668700
    //https://blog.csdn.net/bigtree_3721/article/details/82808007

    /**
     * 发布模型为流程定义
     *
     * @return
     * @throws Exception
     */
//    @RequestMapping("/deployByModelId/{modelId}")
//    public String deployByModelId(@PathVariable("modelId") String modelId) throws Exception {
//        try {
//            Model modelData = repositoryService.getModel(modelId);
//            ObjectNode modelNode = (ObjectNode) new ObjectMapper().readTree(repositoryService.getModelEditorSource(modelData.getId()));
//            byte[] bpmnBytes = null;
//
//            BpmnModel model = new BpmnJsonConverter().convertToBpmnModel(modelNode);
//            bpmnBytes = new BpmnXMLConverter().convertToXML(model);
//
//            String processName = modelData.getName() + ".bpmn20.xml";
//            Deployment deployment = repositoryService.createDeployment().name(modelData.getName()).addString(processName, new String(bpmnBytes)).deploy();
//            //redirectAttributes.addFlashAttribute("message", "部署成功，部署ID=" + deployment.getId());
//        } catch (Exception e) {
//            logger.error("根据模型部署流程失败：modelId={}", modelId, e);
//        }
//        return "redirect:/workflow/model/list";
//    }
//    @RequestMapping("/deployByModelId/{modelId}")
//    public Object deployByModelId(@PathVariable("modelId") String id) throws Exception {
//        //获取模型
//        Model modelData = repositoryService.getModel(id);
//        byte[] bytes = repositoryService.getModelEditorSource(modelData.getId());
//        if (bytes == null) {
//            return ToWeb.buildResult().status(Status.FAIL)
//                    .msg("模型数据为空，请先设计流程并成功保存，再进行发布。");
//        }
//        JsonNode modelNode = new ObjectMapper().readTree(bytes);
//        BpmnModel model = new BpmnJsonConverter().convertToBpmnModel(modelNode);
//        if (model.getProcesses().size() == 0) {
//            return ToWeb.buildResult().status(Status.FAIL)
//                    .msg("数据模型不符要求，请至少设计一条主线流程。");
//        }
//        byte[] bpmnBytes = new BpmnXMLConverter().convertToXML(model);
//        //发布流程
//        String processName = modelData.getName() + ".bpmn20.xml";
//        Deployment deployment = repositoryService.createDeployment()
//                .name(modelData.getName())
//                .addString(processName, new String(bpmnBytes, "UTF-8"))
//                .deploy();
//        modelData.setDeploymentId(deployment.getId());
//        repositoryService.saveModel(modelData);
//        return ToWeb.buildResult().refresh();
//    }

    ///**
    // * @return void
    // * @functionName deployByModelId
    // * @description 根据modelId部署模型
    // * @Date 2019-01-03 13:17
    // */
    @ResponseBody
    @RequestMapping(value = "/getDeployedPageSet", method = RequestMethod.POST)
    @ApiOperation(value = "获取已部署流程分页数据集分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "类型", required = false, dataType = "String")
    })
    public Object getDeployedPageSet(PageParam pageParam, String type, String processDefinitionName, String key) {
        PageSet<ProcessDefinitionVo> processDefinitionDtos = workflowService.getDeployedPageSet(pageParam, null, "1", null);
        return processDefinitionDtos;
    }

    @ResponseBody
    @RequestMapping("/getDeploymentPageSet")
    public Object getDeploymentPageSet(PageParam pageParam) {
        PageSet<Deployment> pageSet = workflowService.getDeploymentPageSet(pageParam);

        List<DeploymentDto> deploymentDtos = new ArrayList<>();
        for (Deployment deployment : pageSet.getRows()) {
            DeploymentDto deploymentDto = new DeploymentDto();
            deploymentDto.setId(deployment.getId());
            deploymentDto.setName(deployment.getName());
            deploymentDto.setKey(deployment.getKey());
            deploymentDto.setDeploymentTime(deployment.getDeploymentTime());
            deploymentDtos.add(deploymentDto);
        }

        PageInfo<DeploymentDto> pageInfo = new PageInfo<>(deploymentDtos);
        pageInfo.setTotal(pageSet.getTotal());
        return PageUtils.getPageSet(pageInfo);
    }


    /**
     * 启动一个流程实例
     * userNameId存入流程变量，业务ID存入业务KEY
     */
    @Log(title = "流程启动",module = LogModule.WORKFLOW,businessType = BusinessType.START, method = "getLogMessage")
    @ResponseBody
    @RequestMapping(value = "start", method = RequestMethod.POST)
    @ApiOperation(value = "启动一个流程实例")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "procDefId", value = "流程定义ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "businessTitle", value = "业务标题", required = true, dataType = "String"),
            @ApiImplicitParam(name = "businessId", value = "业务ID", required = true, dataType = "String")
    })
    public Object start(String procDefId, String businessTitle, String businessId) {
        String currentUserNameId = getCurrentUserNameId();
        RuntimeService runtimeService = processEngine.getRuntimeService();
        ProcessDefinition processDefinitionQuery = repositoryService.createProcessDefinitionQuery().processDefinitionId(procDefId).singleResult();
        String procDefKey = processDefinitionQuery.getKey();

        //String businessId = BaseUtils.UUIDGenerator();
        String gateway = request.getHeader("Gateway");
        String businessSaveUrl = myVariableService.getVariable(procDefKey, "businessSaveUrl");
        String businessEditUrl = myVariableService.getVariable(procDefKey, "businessEditUrl");
        String businessUpdateUrl = myVariableService.getVariable(procDefKey, "businessUpdateUrl");
        String businessDetailUrl = myVariableService.getVariable(procDefKey, "businessDetailUrl");
        String completeTaskUrl = myVariableService.getVariable(procDefKey, "completeTaskUrl");
        //做页面判断的currency(通用界面)
        String businessJudge = myVariableService.getVariable(procDefKey, "businessJudge");

        Map<String, Object> map = new HashMap<>();
        map.put("userNameId", currentUserNameId);
        map.put("businessTitle", businessTitle);
        map.put("businessEditUrl", businessEditUrl);
        map.put("businessUpdateUrl", businessUpdateUrl);
        map.put("businessDetailUrl", businessDetailUrl);
        map.put("completeTaskUrl", completeTaskUrl);
        map.put("businessJudge", businessJudge);
        map.put("assignee", currentUserNameId);

        map.put("startUserNameId", businessSystemDataService.getUserNameId());
        //设置流程发起人ID，可通过startedBy查询的值
        identityService.setAuthenticatedUserId(getCurrentUserNameId());
        //启动流程实例
        ProcessInstance processInstance = runtimeService.createProcessInstanceBuilder()
                .processDefinitionKey(procDefKey.trim())
                .name(businessTitle)
                .businessKey(businessId)
                .variables(map)
                .tenantId(businessSystemDataService.getTenantId())
                .start();
        Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
        setLogMessage(task.getName(),"");
        //默认处理第一步
        Authentication.setAuthenticatedUserId(businessSystemDataService.getUserNameId());
        String taskId = task.getId();
        List varNames = workflowService.getVarsNameByProcessDefinitionId(procDefId);
        Map<String, Object> procVars = new HashMap<>();
        for (int i = 0; i < varNames.size(); i++) {
            String varName = varNames.get(i).toString();
            procVars.put(varName, null == map.get(varName) ? "同意" : map.get(varName));
        }
        taskService.addComment(taskId, task.getProcessInstanceId(), FlowEnum.SP.toString(), "同意");
        taskService.complete(taskId, procVars);

        ////1.历史任务表中没有处理人导致无法驳回到申请人 强行更新act_hi_taskinst表中处理人为登录人
        //Integer integer = myVariableService.updateAssigneeById(businessSystemDataService.getUserNameId(), taskId);


        //链式构建请求，带cookie请求
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("uuid", businessId);
        paramMap.put("title", businessTitle);
        paramMap.put("creatorId", currentUserNameId);

        String cookies = CookieUtil.getCookies(request);
        HttpRequest.post(gateway + businessSaveUrl)
                .cookie(cookies)
                .form(paramMap)
                .timeout(20000)
                .execute().body();

        //获得已启动流程实例
        //List<ProcessInstance> list = runtimeService.createProcessInstanceQuery().list();
        //request.setAttribute("list", list);

        //return display("workflow/workflow/started");
        Map<String, Object> hashMap = JsonUtils.messageJson(200, "操作提示", "流程启动成功！！！");
        hashMap.put("taskId", taskId);
        hashMap.put("businessTitle", businessTitle);
        //hashMap.put("businessDetailUrl", businessDetailUrl);
        hashMap.put("procDefKey", procDefKey);
        hashMap.put("businessId", businessId);


        return hashMap;
    }

    /**
     * 启动一个流程实例
     * userNameId存入流程变量，业务ID存入业务KEY
     */
    @Log(title = "流程启动2",module = LogModule.WORKFLOW,businessType = BusinessType.START, method = "getLogMessage")
    @ResponseBody
    @RequestMapping("start2")
    public Object start2(String procDefId, String businessTitle) {
        String currentUserNameId = getCurrentUserNameId();
        RuntimeService runtimeService = processEngine.getRuntimeService();
        String procDefKey = repositoryService.createProcessDefinitionQuery().processDefinitionId(procDefId).singleResult().getKey();

        String businessId = BaseUtils.UUIDGenerator();

        String businessSaveUrl = request.getScheme() + "://" + request.getParameter("gatewayDomain") + ":" + request.getParameter("gatewayPort")
                + myVariableService.getVariable(procDefKey, "businessSaveUrl");
        String businessDetailUrl = myVariableService.getVariable(procDefKey, "businessDetailUrl");

        Map<String, Object> map = new HashMap<>();
        map.put("userNameId", currentUserNameId);
        map.put("businessTitle", businessTitle);
        map.put("businessDetailUrl", businessDetailUrl);

        //启动流程实例
        //ProcessInstance processInstance = runtimeService.startProcessInstanceById(id);
        ProcessInstance processInstance = runtimeService.startProcessInstanceById(procDefId, businessId, map);

        Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
        String taskId = task.getId();
        setLogMessage(task.getName(),"");

        String param = "uuid=" + businessId + "&title=" + businessTitle + "&creatorId=" + currentUserNameId;
        String result = HttpRequestUtils.sendPost(businessSaveUrl, param, false);

        //获得已启动流程实例
        //List<ProcessInstance> list = runtimeService.createProcessInstanceQuery().list();
        //request.setAttribute("list", list);

        //return display("workflow/workflow/started");
        Map<String, Object> hashMap = JsonUtils.messageJson(200, "操作提示", "流程启动成功！！！");
        hashMap.put("taskId", taskId);
        hashMap.put("businessTitle", businessTitle);
        hashMap.put("businessDetailUrl", businessDetailUrl);
        return hashMap;
    }

    /**
     * 所有已启动流程实例
     */
    @RequestMapping("started")
    public String started() {
        //List<ProcessInstance> processInstances = workflowService.getStartedProcess();
        //request.setAttribute("processInstances", processInstances);
        return "workflow/workflow/started";
    }

    /**
     * 所有已启动流程实例分页数据
     */
    @ResponseBody
    @RequestMapping(value = "getStartedPageSet", method = RequestMethod.POST)
    @ApiOperation(value = "获取已启动流程分页数据集")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String")
    })
    public Object getStartedPageSet(PageParam pageParam, String key) {
        PageSet<ProcessInstance> pageSet = workflowService.getStartedProcess(pageParam, key);
        List<StartedDto> startedList = new ArrayList<>();
        for (ProcessInstance processInstance : pageSet.getRows()) {
            StartedDto started = new StartedDto();
            started.setId(processInstance.getId());
            started.setProcessDefinitionId(processInstance.getProcessDefinitionId());
            started.setProcessDefinitionName(processInstance.getProcessDefinitionName());
            started.setStartTime(processInstance.getStartTime());
            started.setKey(processInstance.getProcessDefinitionKey());
            startedList.add(started);
        }
        PageInfo<StartedDto> pageInfo = new PageInfo<>(startedList);
        pageInfo.setTotal(pageSet.getTotal());
        return PageUtils.getPageSet(pageInfo);
    }

    @RequestMapping("/task")
    public String task() {
        //List<CommonTaskDto> tasks = workflowService.getTasks();
        //request.setAttribute("tasks", tasks);
        return "workflow/workflow/task";
    }

    /**
     * 获取任务分页数据
     *
     * @param pageParam
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getTaskPageSet", method = RequestMethod.POST)
    @ApiOperation(value = "获得进行中流程分页集数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String")
    })
    public Object getTaskPageSet(PageParam pageParam, String taskName) {
        PageSet<CommonTaskDto> pageSet = workflowService.getTasks(pageParam, taskName);
        return pageSet;
    }

    @RequestMapping("/myHistory")
    public Object myHistory() {
        return "workflow/workflow/myHistory";
    }

    @ResponseBody
    @RequestMapping(value = "/getMyHistoryPageSet", method = RequestMethod.POST)
    @ApiOperation(value = "获得历史流程分页集数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String")
    })
    public Object getMyHistoryPageSet(PageParam pageParam, String processDefinitionName) {
        PageSet<CommonTaskDto> hisProInstance = workflowService.getHistoryTasksByStartUserId(pageParam, getCurrentUserNameId(), processDefinitionName);
        return hisProInstance;
    }

    @RequestMapping("complete_layui")
    public ModelAndView complete_layui(String id) {
        Map<String, Object> vars = new HashMap<>(4);
        //vars.put("applyUser", userName);
        vars.put("days", 3);
        //vars.put("reason", vac.getReason());
        // 完成任务
        taskService.complete(id, vars);
        //workflowService.completeTask(id);
        return new ModelAndView("redirect:task");
    }

    /**
     * 完成流程
     *
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "complete", method = RequestMethod.POST)
    @ApiOperation(value = "完成流程")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "流程ID", required = true, dataType = "String")
    })
    public Object complete(String id) {
        try {
            Map<String, Object> vars = new HashMap<>(4);
            //vars.put("applyUser", userName);
            vars.put("days", 3);
            //vars.put("reason", vac.getReason());
            // 完成任务
            taskService.complete(id, vars);
            return success("完成成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return failure("完成失败！");
        }


    }

    /**
     * 生成流程图
     *
     * @throws IOException
     */
    @RequestMapping(value = "/graphics", method = RequestMethod.GET)
    @ApiOperation(value = "生成流程图方法")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "definitionId", value = "流程定义ID", required = true, dataType = "String")
    })
    public void graphics(String definitionId, String instanceId, String taskId, ModelAndView mav, HttpServletResponse response) throws IOException {

        /*ProcessInstance pi = this.processEngine.getRuntimeService().createProcessInstanceQuery()
                .processDefinitionId(definitionId).singleResult();*/

        /*ProcessInstance pi = this.processEngine.getRuntimeService().createProcessInstanceQuery()
                .processInstanceId(instanceId).singleResult();*/
        //BpmnModel bpmnModel = this.processEngine.getRepositoryService().getBpmnModel(pi.getProcessDefinitionId());

        //List<String> activeIds = this.processEngine.getRuntimeService().getActiveActivityIds(pi.getId());

        BpmnModel bpmnModel = this.processEngine.getRepositoryService().getBpmnModel(definitionId);

        ProcessDiagramGenerator diagramGenerator = processEngine.getProcessEngineConfiguration().getProcessDiagramGenerator();
        //ProcessDiagramGenerator diagramGenerator = processEngineConfiguration.getProcessDiagramGenerator();

        //设置字体否则图片中文会出现乱码
        InputStream imageStream = diagramGenerator.generateDiagram(bpmnModel, "png", "宋体", "微软雅黑", "黑体", null, true);

        response.setContentType("image/png");
        Command<InputStream> cmd = null;

        if (definitionId != null) {
            cmd = new GetDeploymentProcessDiagramCmd(definitionId);
        }

        /*if (instanceId != null) {
            cmd = new ProcessInstanceDiagramCmd(instanceId);
        }

        if (taskId != null) {
            Task task = processEngine.getTaskService().createTaskQuery().taskId(taskId).singleResult();
            cmd = new ProcessInstanceDiagramCmd(task.getProcessInstanceId());
        }*/

        if (cmd != null) {
            //InputStream is = processEngine.getManagementService().executeCommand(cmd);
            //InputStream is = processEngine.getRepositoryService().getProcessDiagram(definitionId);
            //InputStream is = processEngine.getRepositoryService().getResourceAsStream(deploymentId, resourceName);
            int len = 0;
            byte[] b = new byte[1024];
            while ((len = imageStream.read(b, 0, 1024)) != -1) {
                response.getOutputStream().write(b, 0, len);
            }
        }
    }

    public void genPic(String processInstanceId) throws Exception {
        ProcessInstance pi = this.processEngine.getRuntimeService().createProcessInstanceQuery()
                .processInstanceId(processInstanceId).singleResult();
        BpmnModel bpmnModel = this.processEngine.getRepositoryService().getBpmnModel(pi.getProcessDefinitionId());

        List<String> activeIds = this.processEngine.getRuntimeService().getActiveActivityIds(pi.getId());

        ProcessDiagramGenerator diagramGenerator = new DefaultProcessDiagramGenerator();
        InputStream is = diagramGenerator.generateDiagram(bpmnModel, "png", activeIds, true);

        File file = new
                File("d:\\Download\\process.png");
        OutputStream os = new
                FileOutputStream(file);

        byte[] buffer = new
                byte[1024];
        int len = 0;
        while ((len = is.read(buffer)) !=
                -1) {
            os.write(buffer, 0, len);
        }

        os.close();
        is.close();
    }


    /**
     * 获取已经流转的线
     *
     * @param bpmnModel
     * @param historicActivityInstances
     * @return
     */
    private static List<String> getHighLightedFlows(BpmnModel bpmnModel, List<HistoricActivityInstance> historicActivityInstances) {
        // 高亮流程已发生流转的线id集合
        List<String> highLightedFlowIds = new ArrayList<>();
        // 全部活动节点
        List<FlowNode> historicActivityNodes = new ArrayList<>();
        // 已完成的历史活动节点
        List<HistoricActivityInstance> finishedActivityInstances = new ArrayList<>();

        for (HistoricActivityInstance historicActivityInstance : historicActivityInstances) {
            FlowNode flowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(historicActivityInstance.getActivityId(), true);
            historicActivityNodes.add(flowNode);
            if (historicActivityInstance.getEndTime() != null) {
                finishedActivityInstances.add(historicActivityInstance);
            }
        }

        FlowNode currentFlowNode = null;
        FlowNode targetFlowNode = null;
        // 遍历已完成的活动实例，从每个实例的outgoingFlows中找到已执行的
        for (HistoricActivityInstance currentActivityInstance : finishedActivityInstances) {
            // 获得当前活动对应的节点信息及outgoingFlows信息
            currentFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(currentActivityInstance.getActivityId(), true);
            List<SequenceFlow> sequenceFlows = currentFlowNode.getOutgoingFlows();

            /**
             * 遍历outgoingFlows并找到已流转的 满足如下条件认为已流转：
             * 1.当前节点是并行网关或兼容网关，则通过outgoingFlows能够在历史活动中找到的全部节点均为已流转
             * 2.当前节点是以上两种类型之外的，通过outgoingFlows查找到的时间最早的流转节点视为有效流转
             */
            if ("parallelGateway".equals(currentActivityInstance.getActivityType()) || "inclusiveGateway".equals(currentActivityInstance.getActivityType())) {
                // 遍历历史活动节点，找到匹配流程目标节点的
                for (SequenceFlow sequenceFlow : sequenceFlows) {
                    targetFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(sequenceFlow.getTargetRef(), true);
                    if (historicActivityNodes.contains(targetFlowNode)) {
                        highLightedFlowIds.add(targetFlowNode.getId());
                    }

                }
            } else {
                List<Map<String, Object>> tempMapList = new ArrayList<>();
                for (SequenceFlow sequenceFlow : sequenceFlows) {
                    for (HistoricActivityInstance historicActivityInstance : historicActivityInstances) {
                        if (historicActivityInstance.getActivityId().equals(sequenceFlow.getTargetRef())) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("highLightedFlowId", sequenceFlow.getId());
                            map.put("highLightedFlowStartTime", historicActivityInstance.getStartTime().getTime());
                            tempMapList.add(map);
                        }
                    }
                }

                if (!CollectionUtils.isEmpty(tempMapList)) {
                    // 遍历匹配的集合，取得开始时间最早的一个
                    long earliestStamp = 0L;
                    String highLightedFlowId = null;
                    for (Map<String, Object> map : tempMapList) {
                        long highLightedFlowStartTime = Long.valueOf(map.get("highLightedFlowStartTime").toString());
                        if (earliestStamp == 0 || earliestStamp <= highLightedFlowStartTime) {
                            highLightedFlowId = map.get("highLightedFlowId").toString();
                            earliestStamp = highLightedFlowStartTime;
                        }
                    }

                    highLightedFlowIds.add(highLightedFlowId);
                }

            }

        }
        return highLightedFlowIds;
    }


    @RequestMapping("/getFlowImgByTaskId")
    public void getFlowImgByTaskId(String taskId, OutputStream outputStream) throws Exception {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        InputStream inputStream = imageService.generateImageByProcInstId(task.getProcessInstanceId());
        byte[] b = new byte[1024];
        int len;
        while ((len = inputStream.read(b, 0, 1024)) != -1) {
            outputStream.write(b, 0, len);
        }
        //  getFlowImgByInstanceId(task.getProcessInstanceId(), outputStream);
    }

    @RequestMapping("/getFlowImgByExecutionId")
    public void getFlowImgByExecutionId(String executionId, OutputStream outputStream) throws Exception {
        Task task = taskService.createTaskQuery().executionId(executionId).singleResult();

        InputStream inputStream = imageService.generateImageByProcInstId(task.getProcessInstanceId());
        byte[] b = new byte[1024];
        int len;
        while ((len = inputStream.read(b, 0, 1024)) != -1) {
            outputStream.write(b, 0, len);
        }
        // getFlowImgByInstanceId(task.getProcessInstanceId(), outputStream);
    }

    /**
     * 根据流程实例Id,获取实时流程图片
     *
     * @param processInstanceId
     * @param outputStream
     * @return
     */
    @RequestMapping("/getFlowImgByInstanceId")
    public void getFlowImgByInstanceId(String processInstanceId, OutputStream outputStream) throws Exception {

        InputStream inputStream = imageService.generateImageByProcInstId(processInstanceId);
        byte[] b = new byte[1024];
        int len;
        while ((len = inputStream.read(b, 0, 1024)) != -1) {
            outputStream.write(b, 0, len);
        }
//        try {
//            if (StringUtils.isEmpty(processInstanceId)) {
//                System.out.println("processInstanceId is null");
//                return;
//            }
//            // 获取历史流程实例
//            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
//            // 获取流程中已经执行的节点，按照执行先后顺序排序
//            List<HistoricActivityInstance> historicActivityInstances = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId)
//                    .orderByHistoricActivityInstanceId().asc().list();
//            // 高亮已经执行流程节点ID集合
//            List<String> highLightedActivitiIds = new ArrayList<>();
//            for (HistoricActivityInstance historicActivityInstance : historicActivityInstances) {
//                highLightedActivitiIds.add(historicActivityInstance.getActivityId());
//            }
//
//            List<HistoricProcessInstance> historicFinishedProcessInstances = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).finished()
//                    .list();
//            ProcessDiagramGenerator processDiagramGenerator = null;
//            // 如果还没完成，流程图高亮颜色为绿色，如果已经完成为红色
//            if (!CollectionUtils.isEmpty(historicFinishedProcessInstances)) {
//                // 如果不为空，说明已经完成
//                processDiagramGenerator = processEngineConfiguration.getProcessDiagramGenerator();
//            } else {
//                processDiagramGenerator = new CustomProcessDiagramGenerator();
//            }
//
//            BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());
//            // 高亮流程已发生流转的线id集合
//            List<String> highLightedFlowIds = getHighLightedFlows(bpmnModel, historicActivityInstances);
//
//            // 使用默认配置获得流程图表生成器，并生成追踪图片字符流
//            InputStream imageStream = processDiagramGenerator.generateDiagram(bpmnModel, "png", highLightedActivitiIds, highLightedFlowIds,
//                    "宋体", "微软雅黑", "黑体", null, 1.0, true);
//
//            // 输出图片内容
//            byte[] b = new byte[1024];
//            int len;
//            while ((len = imageStream.read(b, 0, 1024)) != -1) {
//                outputStream.write(b, 0, len);
//            }
//        } catch (Exception e) {
//            System.out.println("processInstanceId" + processInstanceId + "生成流程图失败，原因：" + e.getMessage());
//        }

    }

    /**
     * 根据taskId生成流程图
     *
     * @throws IOException
     */
    @RequestMapping("/taskGraphics")
    public void taskGraphics(String taskId, HttpServletResponse response) throws IOException {
//        Task task = processEngine.getTaskService().createTaskQuery().taskId(taskId).singleResult();
//        /**
//         * 流程实例
//         **/
//        BpmnModel bpmnModel = processEngine.getRepositoryService().getBpmnModel(task.getProcessDefinitionId());
//        List<String> activeActivityIds = processEngine.getRuntimeService().getActiveActivityIds(task.getProcessInstanceId());
//        ProcessEngineImpl defaultProcessEngine = (ProcessEngineImpl) ProcessEngines.getDefaultProcessEngine();
//        Context.setProcessEngineConfiguration(defaultProcessEngine.getProcessEngineConfiguration());
//        ProcessEngineConfiguration processEngineConfiguration = Context.getProcessEngineConfiguration();
//        /**
//         * 得到图片输出流
//         **/
//        InputStream imageStream = processEngine.getProcessEngineConfiguration()
//                .getProcessDiagramGenerator()
//                .generateDiagram(bpmnModel, "png", activeActivityIds,
//                        new ArrayList<String>(),
//                        processEngineConfiguration.getActivityFontName(),
//                        processEngineConfiguration.getLabelFontName(),
//                        null,
//                        ClassLoader.getSystemClassLoader(),
//                        1.0);
//        int len = 0;
//        byte[] b = new byte[1024];
//        while ((len = imageStream.read(b, 0, 1024)) != -1) {
//            response.getOutputStream().write(b, 0, len);
//        }
    }

    /**
     * 删除流程定义
     *
     * @param deploymentId
     * @return
     */
    @RequestMapping("/delete_layui")
    public String deleteProcessDefinition_layui(String deploymentId) {
        workflowService.deleteProcessDefinition(deploymentId);
        return "redirect:http://" + myProperties.getHost() + ":" + myProperties.getGatewayPort() + "/workflow/workflow/deployed";
    }

    @Autowired
    private ApiFlowableProcessDefinitionService apiFlowableProcessDefinitionService;


    @Autowired
    private ActMyModelService flowModelService;

    @Autowired
    private ActMyNodeButtonService flowNodeButtonService;

    @Autowired
    private ActMyNodeFieldService flowNodeFieldService;

    /**
     * 删除流程定义
     *
     * @param deploymentId
     * @return
     */
    @ResponseBody
    @RequestMapping("/delete")
    public Object deleteProcessDefinition(String deploymentId) {
        try {
            String procdefId = apiFlowableProcessDefinitionService.getProdefIdByDeployId(deploymentId);
            workflowService.deleteProcessDefinition(deploymentId);
            //以下特殊
            //1.删除流程与表单表 act_my_model
            Example example = new Example(ActMyModel.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("procdefId", procdefId);
            flowModelService.deleteByExample(example);
            //2.删除流程节点按钮 act_my_node_button
            Example example1 = new Example(ActMyNodeButton.class);
            Example.Criteria criteria1 = example1.createCriteria();
            criteria1.andEqualTo("procdefId", procdefId);
            flowNodeButtonService.deleteByExample(example1);
            //3.删除流程字段 act_my_node_field
            Example example11 = new Example(ActMyNodeField.class);
            Example.Criteria criteria11 = example11.createCriteria();
            criteria11.andEqualTo("procdefId", procdefId);
            flowNodeFieldService.deleteByExample(example11);
            return success("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return failure("删除失败！");
        }


    }

    /**
     * 删除已启动流程
     *
     * @param instanceId
     * @return
     */
    @RequestMapping("/deleteStartedProcess_layui")
    public String deleteProcessStarted_layui(String instanceId) {
        runtimeService.deleteProcessInstance(instanceId, "no reason");
        return "redirect:/workflow/workflow/index";
    }

    @ResponseBody
    @RequestMapping("/deleteStartedProcess")
    public Object deleteProcessStarted(String instanceId) {
        try {
            runtimeService.deleteProcessInstance(instanceId, "no reason");
            return success("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return failure("删除失败！");
        }
    }

    @Log(title = "流程删除",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ResponseBody
    @RequestMapping(value = "/deleteHistoryProcess", method = RequestMethod.POST)
    @ApiOperation(value = "删除流程")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "instanceId", value = "流程ID", required = true, dataType = "String")
    })
    public Object deleteHistoryProcess(String instanceId) {
        try {
            Task task = taskService.createTaskQuery().processInstanceId(instanceId).singleResult();
            if(task!=null)
                setLogMessage(task.getName(),"");
            historyService.deleteHistoricProcessInstance(instanceId);
            return success("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return failure("删除失败！");
        }

    }

    @Log(title = "流程删除",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ResponseBody
    @RequestMapping(value = "/deleteProcessForApi", method = RequestMethod.POST)
    @ApiOperation(value = "删除流程")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "instanceId", value = "流程ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "businessKey", value = "业务表id", required = true, dataType = "String")
    })
    public com.lms.common.model.Result<Object> deleteProcessForApi(@RequestParam String instanceId, @RequestParam String businessKey) {
        try {
            runtimeService.deleteProcessInstance(instanceId, "删除");
            //流程删除,同时删除门户待办
            businessSystemDataService.pushCancelTask(businessKey);
            return com.lms.common.model.Result.OK("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return com.lms.common.model.Result.error("删除失败！", null);
        }

    }


    /**
     * 获取所有模型
     *
     * @return
     */
    //@ApiOperation(value = "获取所有模型")
    @GetMapping("/modelList")
    @ResponseBody
    public List<Model> modelList() {
        //RepositoryService repositoryService = processEngine.getRepositoryService();
        return repositoryService.createModelQuery().orderByCreateTime().desc().list();
    }

    /**
     * 部署流程
     *
     * @param procFile processes/vacation.bpmn20.xml
     * @return
     */
    @GetMapping("/deployProc")
    public Deployment deployProc(String procFile) {
        //根据bpmn文件部署流程
        Deployment deploy = repositoryService.createDeployment()
                .addClasspathResource("processes/vacation.bpmn20.xml")
                .deploy();
        return deploy;
    }

    /**
     * 流程定义
     *
     * @return
     */
    @GetMapping("/procDef")
    public List<ProcessDefinition> procDef() {
        //RepositoryService repositoryService = processEngine.getRepositoryService();
        List<ProcessDefinition> processDefinitions = repositoryService.createProcessDefinitionQuery().orderByDeploymentId().desc().list();
        return processDefinitions;
    }

    /**
     * 已部署流程列表
     */
    /*@RequestMapping("/deployed")
    public Object deployed() {
        List<ProcessDefinition> processDefinitions = workflowService.getDeployedFlows();

        *//*List<Map<String, Object>> customProcDefList = new ArrayList<>();
        for (ProcessDefinition processDefinition : processDefinitions) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("procDefId", processDefinition.getId());
            map.put("description", processDefinition.getDescription());
            map.put("procName", processDefinition.getName());

            customProcDefList.add(map);
        }*//*
        //return customProcDefList;

        request.setAttribute("processDefinitions", processDefinitions);
        return "workflow/workflow/deployed";
    }*/

    /*@RequestMapping(value = "/startProcess2", method = RequestMethod.GET)
    public void startProcess2(Vacation vacation) {
        vacation.setUserNameId(getCurrentUserNameId());
        vacation.setDays(3);
        vacation.setFormId("form1");
        //启动流程实例，字符串"vacation"是BPMN模型文件里process元素的id
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey("vacation");
        //流程实例启动后，流程会跳转到请假申请节点
        Task tast = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
        //设置请假申请任务的执行人
        taskService.setAssignee(tast.getId(), vacation.getUserNameId().toString());

        //设置流程参数：请假天数和表单ID
        //流程引擎会根据请假天数days>3判断流程走向
        //formId是用来将流程数据和表单数据关联起来
        Map<String, Object> args = new HashMap<>();
        args.put("days", vacation.getDays());
        args.put("formId", vacation.getFormId());

        //完成请假申请任务
        taskService.complete(tast.getId(), args);
    }*/

    /**
     * 开始流程并“申请请假”（员工）
     */
    /*@ResponseBody
    @RequestMapping("/startProcess")
    public Object startProcess(String userName, Vacation vac) {

        identityService.setAuthenticatedUserId(userName);
        // 开始流程
        ProcessInstance vacationInstance = runtimeService.startProcessInstanceByKey(PROCESS_DEFINE_KEY);
        // 查询当前任务
        Task currentTask = taskService.createTaskQuery().processInstanceId(vacationInstance.getId()).singleResult();
        // 申明任务
        taskService.claim(currentTask.getId(), userName);

        Map<String, Object> vars = new HashMap<>(4);
        vars.put("applyUser", userName);
        vars.put("days", vac.getDays());
        vars.put("reason", vac.getReason());
        // 完成任务
        taskService.complete(currentTask.getId(), vars);

        return true;
    }*/
    @RequestMapping("/daiban_index")
    public String daiban_index() {
        return "workflow/daiban_index";
    }

    /**
     * 查询需要自己审批的请假
     *
     * @param userName
     * @return
     */
    /*@ResponseBody
    @RequestMapping("/myAudit")
    public Object myAudit(String userName) {
        //List<Task> taskList = taskService.createTaskQuery().taskCandidateUser(userName).orderByTaskCreateTime().desc().list();
        List<Task> taskList = taskService.createTaskQuery().taskAssignee(userName).orderByTaskCreateTime().desc().list();
//        / 多此一举 taskList中包含了以下内容(用户的任务中包含了所在用户组的任务)
//        Group group = identityService.createGroupQuery().groupMember(userName).singleResult();
//        List<Task> list = taskService.createTaskQuery().taskCandidateGroup(group.getId()).list();
//        taskList.addAll(list);
        List<VacTask> vacTaskList = new ArrayList<>();
        for (Task task : taskList) {
            VacTask vacTask = new VacTask();
            vacTask.setId(task.getId());
            vacTask.setName(task.getName());
            vacTask.setCreateTime(task.getCreateTime());
            String instanceId = task.getProcessInstanceId();
            ProcessInstance instance = runtimeService.createProcessInstanceQuery().processInstanceId(instanceId).singleResult();
            Vacation vac = getVacation(instance);
            vacTask.setVac(vac);
            vacTaskList.add(vacTask);
        }
        return vacTaskList;
    }*/

    /*private Vacation getVacation(ProcessInstance instance) {
        Integer days = runtimeService.getVariable(instance.getId(), "days", Integer.class);
        String reason = runtimeService.getVariable(instance.getId(), "reason", String.class);
        Vacation vacation = new Vacation();
        vacation.setApplyUser(instance.getStartUserId());
        vacation.setDays(days);
        vacation.setReason(reason);
        Date startTime = instance.getStartTime(); // activiti 6 才有
        vacation.setApplyTime(startTime);
        vacation.setApplyStatus(instance.isEnded() ? "申请结束" : "等待审批");
        return vacation;
    }*/

    /**
     * 审批请假
     *
     * @param userName
     * @param vacTask
     * @return
     */
    /*@RequestMapping("/passAudit")
    public Object passAudit(String userName, VacTask vacTask) {
        String taskId = vacTask.getId();
        String result = vacTask.getVac().getResult();
        Map<String, Object> vars = new HashMap<>();
        vars.put("result", result);
        vars.put("auditor", userName);
        vars.put("auditTime", new Date());
        //taskService.claim(taskId, userName);
        taskService.setAssignee(taskId, userName);
        taskService.complete(taskId, vars);
        return true;
    }*/

    /**
     * 查询请假记录
     *
     * @param userName
     * @return
     */
    /*@RequestMapping("/myVacRecord")
    public Object myVacRecord(String userName) {
        List<HistoricProcessInstance> hisProInstance = historyService.createHistoricProcessInstanceQuery()
                .processDefinitionKey(PROCESS_DEFINE_KEY).startedBy(userName).finished()
                .orderByProcessInstanceEndTime().desc().list();

        List<Vacation> vacList = new ArrayList<>();
        for (HistoricProcessInstance hisInstance : hisProInstance) {
            Vacation vacation = new Vacation();
            vacation.setApplyUser(hisInstance.getStartUserId());
            vacation.setApplyTime(hisInstance.getStartTime());
            vacation.setApplyStatus("申请结束");
            List<HistoricVariableInstance> varInstanceList = historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(hisInstance.getId()).list();
            ActivitiUtil.setVars(vacation, varInstanceList);
            vacList.add(vacation);
        }
        return vacList;
    }*/





    /*作者：编程序的艺术家
    来源：CSDN
    原文：https://blog.csdn.net/m0_37222746/article/details/73321680?utm_source=copy
    版权声明：本文为博主原创文章，转载请附上博文链接！

    升级到Activiti6.0.0  之后，发现pvm 包整个被删掉了。。。。这样一来就导致之前的跟踪流失效了。代码连编译都通过不了。

    因为pvm包没了，所以就不能再使用ActivityImpl 等相关类了。只能改成用org.activiti.bpmn.model包下的FlowNode类来替代。好在他们差不多，
    所以代码改动也不大。下面是完整代码：*/

    /**
     * @param processDefinitionId 流程定义ID
     * @param resourceName        资源名称
     * <AUTHOR>
     * @Note 读取流程资源
     * @Date 2017-1-3 15:11
     */
    @RequestMapping(value = "/readResource")
    public void readResource(String processDefinitionId, String resourceName, String pProcessInstanceId, HttpServletResponse response)
            throws Exception {
        // 设置页面不缓存
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        ProcessDefinitionQuery pdq = repositoryService.createProcessDefinitionQuery();
        ProcessDefinition pd = pdq.processDefinitionId(processDefinitionId).singleResult();

        if (resourceName.endsWith(".png") && StringUtils.isEmpty(pProcessInstanceId) == false) {
            getActivitiProccessImage(pProcessInstanceId, response);
            //ProcessDiagramGenerator.generateDiagram(pde, "png", getRuntimeService().getActiveActivityIds(processInstanceId));
        } else {
            // 通过接口读取
            InputStream resourceAsStream = repositoryService.getResourceAsStream(pd.getDeploymentId(), resourceName);

            // 输出资源内容到相应对象
            byte[] b = new byte[1024];
            int len = -1;
            while ((len = resourceAsStream.read(b, 0, 1024)) != -1) {
                response.getOutputStream().write(b, 0, len);
            }
        }
    }


    /**
     * 获取流程图像，已执行节点和流程线高亮显示
     */
    public void getActivitiProccessImage(String pProcessInstanceId, HttpServletResponse response) {
        //logger.info("[开始]-获取流程图图像");
        try {
            //  获取历史流程实例
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(pProcessInstanceId).singleResult();

            if (historicProcessInstance == null) {
                //throw new BusinessException("获取流程实例ID[" + pProcessInstanceId + "]对应的历史流程实例失败！");
            } else {
                // 获取流程定义
                ProcessDefinitionEntity processDefinition = (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService)
                        .getDeployedProcessDefinition(historicProcessInstance.getProcessDefinitionId());

                // 获取流程历史中已执行节点，并按照节点在流程中执行先后顺序排序
                List<HistoricActivityInstance> historicActivityInstanceList = historyService.createHistoricActivityInstanceQuery()
                        .processInstanceId(pProcessInstanceId).orderByHistoricActivityInstanceId().asc().list();
                // 已执行的节点ID集合
                List<String> executedActivityIdList = new ArrayList<String>();
                int index = 1;
                //logger.info("获取已经执行的节点ID");
                for (HistoricActivityInstance activityInstance : historicActivityInstanceList) {
                    executedActivityIdList.add(activityInstance.getActivityId());

                    //logger.info("第[" + index + "]个已执行节点=" + activityInstance.getActivityId() + " : " +activityInstance.getActivityName());
                    index++;
                }

                BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());

                // 已执行的线集合
                List<String> flowIds = new ArrayList<String>();
                // 获取流程走过的线 (getHighLightedFlows是下面的方法)
                flowIds = getHighLightedFlows(bpmnModel, processDefinition, historicActivityInstanceList);


                // 获取流程图图像字符流
                ProcessDiagramGenerator pec = processEngine.getProcessEngineConfiguration().getProcessDiagramGenerator();
                //配置字体
                InputStream imageStream = pec.generateDiagram(bpmnModel, "png", executedActivityIdList, flowIds, true);

                response.setContentType("image/png");
                OutputStream os = response.getOutputStream();
                int bytesRead = 0;
                byte[] buffer = new byte[8192];
                while ((bytesRead = imageStream.read(buffer, 0, 8192)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.close();
                imageStream.close();
            }
            //logger.info("[完成]-获取流程图图像");
        } catch (Exception e) {
            System.out.println(e.getMessage());
            //logger.error("【异常】-获取流程图失败！" + e.getMessage());
            //throw new BusinessException("获取流程图失败！" + e.getMessage());
        }
    }

    public List<String> getHighLightedFlows(BpmnModel bpmnModel, ProcessDefinitionEntity processDefinitionEntity, List<HistoricActivityInstance> historicActivityInstances) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); //24小时制
        List<String> highFlows = new ArrayList<String>();// 用以保存高亮的线flowId

        for (int i = 0; i < historicActivityInstances.size() - 1; i++) {
            // 对历史流程节点进行遍历
            // 得到节点定义的详细信息
            FlowNode activityImpl = (FlowNode) bpmnModel.getMainProcess().getFlowElement(historicActivityInstances.get(i).getActivityId());


            List<FlowNode> sameStartTimeNodes = new ArrayList<FlowNode>();// 用以保存后续开始时间相同的节点
            FlowNode sameActivityImpl1 = null;

            HistoricActivityInstance activityImpl_ = historicActivityInstances.get(i);// 第一个节点
            HistoricActivityInstance activityImp2_;

            for (int k = i + 1; k <= historicActivityInstances.size() - 1; k++) {
                activityImp2_ = historicActivityInstances.get(k);// 后续第1个节点

                if (activityImpl_.getActivityType().equals("userTask") && activityImp2_.getActivityType().equals("userTask") &&
                        df.format(activityImpl_.getStartTime()).equals(df.format(activityImp2_.getStartTime()))) //都是usertask，且主节点与后续节点的开始时间相同，说明不是真实的后继节点
                {

                } else {
                    sameActivityImpl1 = (FlowNode) bpmnModel.getMainProcess().getFlowElement(historicActivityInstances.get(k).getActivityId());//找到紧跟在后面的一个节点
                    break;
                }

            }
            sameStartTimeNodes.add(sameActivityImpl1); // 将后面第一个节点放在时间相同节点的集合里
            for (int j = i + 1; j < historicActivityInstances.size() - 1; j++) {
                HistoricActivityInstance activityImpl1 = historicActivityInstances.get(j);// 后续第一个节点
                HistoricActivityInstance activityImpl2 = historicActivityInstances.get(j + 1);// 后续第二个节点

                if (df.format(activityImpl1.getStartTime()).equals(df.format(activityImpl2.getStartTime()))) {// 如果第一个节点和第二个节点开始时间相同保存
                    FlowNode sameActivityImpl2 = (FlowNode) bpmnModel.getMainProcess().getFlowElement(activityImpl2.getActivityId());
                    sameStartTimeNodes.add(sameActivityImpl2);
                } else {// 有不相同跳出循环
                    break;
                }
            }
            List<SequenceFlow> pvmTransitions = activityImpl.getOutgoingFlows(); // 取出节点的所有出去的线

            for (SequenceFlow pvmTransition : pvmTransitions) {// 对所有的线进行遍历
                FlowNode pvmActivityImpl = (FlowNode) bpmnModel.getMainProcess().getFlowElement(pvmTransition.getTargetRef());// 如果取出的线的目标节点存在时间相同的节点里，保存该线的id，进行高亮显示
                if (sameStartTimeNodes.contains(pvmActivityImpl)) {
                    highFlows.add(pvmTransition.getId());
                }
            }

        }
        return highFlows;

    }


    @Transient
    @ResponseBody
    @RequestMapping("/coome")
    public Object coome(String taskId, String comment, String name, String instanceId) {
        return workflowService.coome(taskId, request.getParameterValues("userNameId[]"), comment, name, instanceId);
    }


    /**
     * 流程中断 流程实例
     *
     * @param proInstId 实例id
     * @return
     */
    @Log(title = "流程中断",module = LogModule.WORKFLOW,businessType = BusinessType.INTERRUPT, method = "getLogMessage")
    @Transient
    @ResponseBody
    @RequestMapping("/processInterrupt")
    public Object processInterrupt(String proInstId, String executionId) {
        Task task = taskService.createTaskQuery().processInstanceId(proInstId).singleResult();
        if(task!=null)
            setLogMessage(task.getName(),"");
        runtimeService.suspendProcessInstanceById(proInstId);
        Execution execution = runtimeService.createExecutionQuery().executionId(executionId).singleResult();
        return execution.isSuspended();
    }


    /**
     * 流程激活
     *
     * @param proInstId 实例id
     * @return
     */
    @Log(title = "流程激活",module = LogModule.WORKFLOW,businessType = BusinessType.ACTIVATE, method = "getLogMessage")
    @Transient
    @ResponseBody
    @RequestMapping("/processActivation")
    public Object processActivation(String proInstId, String executionId) {
        Task task = taskService.createTaskQuery().processInstanceId(proInstId).singleResult();
        if(task!=null)
            setLogMessage(task.getName(),"");
        runtimeService.activateProcessInstanceById(proInstId);
        Execution execution = runtimeService.createExecutionQuery().executionId(executionId).singleResult();
        return execution.isSuspended();
    }


    /**
     * @param key,  businessId,  title 手动启动流程
     * @return
     * @Description 携带保存信息以及token
     */
    @Log(title = "手动启动流程",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @Transient
    @ResponseBody
    @RequestMapping("/manualOpenFlow")
    public Object manualOpenFlow(String key, String businessId, String title) {
        try {
            //获取最新的流程定义id
            String procDefId = workflowService.getProcDefIdBykey(key);
            //设置流程发起人ID，可通过startedBy查询的值
            identityService.setAuthenticatedUserId(getCurrentUserNameId());

            Map<String, Object> map = new HashMap<>();
            map.put("userNameId", getCurrentUserNameId());
            map.put("businessTitle", title);
            map.put("assignee", getCurrentUserNameId());
            //获取流程变量
            List<MyVariable> list = myVariableService.getListByProcessKey(key);
            for (int i = 0; i < list.size(); i++) {
                if ("businessEditUrl".equals(list.get(i).getVariableName())) {
                    map.put("businessEditUrl", list.get(i).getVariableValue());
                } else if ("businessUpdateUrl".equals(list.get(i).getVariableName())) {
                    map.put("businessUpdateUrl", list.get(i).getVariableValue());
                } else if ("businessDetailUrl".equals(list.get(i).getVariableName())) {
                    map.put("businessDetailUrl", list.get(i).getVariableValue());
                } else if ("completeTaskUrl".equals(list.get(i).getVariableName())) {
                    map.put("completeTaskUrl", list.get(i).getVariableValue());
                } else if ("businessJudge".equals(list.get(i).getVariableName())) {
                    map.put("businessJudge", list.get(i).getVariableValue());
                }
            }

            //设置流程发起人ID，可通过startedBy查询的值
            identityService.setAuthenticatedUserId(getCurrentUserNameId());
            //启动流程实例
            ProcessInstance processInstance = runtimeService.startProcessInstanceById(procDefId, businessId, map);
            Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
            if(task!=null)
                setLogMessage(task.getName(),"");
            //默认处理第一步
            Authentication.setAuthenticatedUserId(businessSystemDataService.getUserNameId());
            String taskId = task.getId();
            List varNames = workflowService.getVarsNameByProcessDefinitionId(procDefId);
            Map<String, Object> procVars = new HashMap<>();
            for (int i = 0; i < varNames.size(); i++) {
                String varName = varNames.get(i).toString();
                procVars.put(varName, "同意");
            }
            taskService.addComment(taskId, task.getProcessInstanceId(), "同意");
            taskService.complete(taskId, procVars);
            //1.历史任务表中没有处理人导致无法驳回到申请人 强行更新act_hi_taskinst表中处理人为登录人
            Integer integers = myVariableService.updateAssigneeById(businessSystemDataService.getUserNameId(), taskId);
            return integers;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }


    /*****************************************以下针对手机上的流程处理************************************************/

    @Autowired
    private OaLeaveService oaLeaveService;

    /**
     * @param oaLeave 业务保存
     * @return
     * @Description 携带保存信息以及token
     */
    @Log(title = "流程业务保存",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @Transient
    @ResponseBody
    @RequestMapping("/businessSave")
    public Object businessSave(OaLeave oaLeave) {
        try {
            //流程可以
            String key = "qjlxys";
            String uuid = BaseUtils.UUIDGenerator();
            oaLeave.setUuid(uuid);
            Integer integer = oaLeaveService.insertSelective(getSaveData(oaLeave));
            //获取最新的流程定义id
            String procDefId = workflowService.getProcDefIdBykey(key);
            //设置流程发起人ID，可通过startedBy查询的值
            identityService.setAuthenticatedUserId(getCurrentUserNameId());
            //启动流程实例
            ProcessInstance processInstance = runtimeService.startProcessInstanceById(procDefId, uuid);
            Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
            if(task!=null)
                setLogMessage(task.getName(),"");
            //默认处理第一步
            Authentication.setAuthenticatedUserId(businessSystemDataService.getUserNameId());
            String taskId = task.getId();
            List varNames = workflowService.getVarsNameByProcessDefinitionId(procDefId);
            Map<String, Object> procVars = new HashMap<>();
            for (int i = 0; i < varNames.size(); i++) {
                String varName = varNames.get(i).toString();
                procVars.put(varName, "同意");
            }
            taskService.addComment(taskId, task.getProcessInstanceId(), oaLeave.getReason());
            taskService.complete(taskId, procVars);
            //1.历史任务表中没有处理人导致无法驳回到申请人 强行更新act_hi_taskinst表中处理人为登录人
            Integer integers = myVariableService.updateAssigneeById(businessSystemDataService.getUserNameId(), taskId);
            return integers;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    //
    ///**
    // * @MethodName getToDoTasks
    // * @Description  获取待办流程
    // * 携带参数  分页参数    以及token
    // * @Return java.lang.Object
    // */
    //@ResponseBody
    //@RequestMapping("/getToDoTasks")
    //public Object getToDoTasks(PageParam pageParam) {
    //    String filterSort = "";
    //    filterSort = BaseUtils.filterSort(request, filterSort).replace(" ORDER BY create_time DESC", " ORDER BY t1.CREATE_TIME_ DESC");
    //    String userNmeId = businessSystemDataService.getUserNameId();
    //    PageSet<OaLeaveCommon> pageSet = oaLeaveService.getToDoTasks(pageParam, filterSort, userNmeId);
    //    return pageSet;
    //
    //
    //}


    ///**
    // * @MethodName getTasksInProgress
    // * @Description  获取在办数据
    // * @Param pageParam 携带分页数据以及token
    // * @Return java.lang.Object
    // */
    //@ResponseBody
    //@RequestMapping("/getTasksInProgress")
    //public Object getTasksInProgress(PageParam pageParam) {
    //    String filterSort = "";
    //    filterSort = BaseUtils.filterSort(request, filterSort).replace(" ORDER BY create_time DESC", " ORDER BY RES.ID_ DESC");
    //    String userNmeId = businessSystemDataService.getUserNameId();
    //    PageSet<OaLeaveCommon> pageSet = oaLeaveService.getTasksInProgress(pageParam, filterSort, userNmeId);
    //    return pageSet;
    //}

    ///**
    // * @MethodName getHistoryTasks
    // * @Description  获取历史数据
    // * @Param 携带分页数据以及token
    // * @Return java.lang.Object
    // */
    //@ResponseBody
    //@RequestMapping("/getHistoryTasks")
    //public Object getHistoryTasks(PageParam pageParam) {
    //    String filterSort = "";
    //    filterSort = BaseUtils.filterSort(request, filterSort).replace(" ORDER BY create_time DESC", " ORDER BY RES.END_TIME_ DESC");
    //    String userNmeId = businessSystemDataService.getUserNameId();
    //    PageSet<OaLeaveCommon> pageSet = oaLeaveService.getHistoryTasks(pageParam, filterSort, userNmeId);
    //    return pageSet;
    //}


    /**
     * @MethodName processApproval
     * @Description 流程审批
     * @Param taskId 任务Id
     * @Param comment 意见
     * @Param processDefinitionId  流程定义id
     * @Param type  同意(1)  驳回(0)
     */
    @Log(title = "流程审批",module = LogModule.WORKFLOW,businessType = BusinessType.APPROVE, method = "getLogMessage")
    @ResponseBody
    @RequestMapping(value = "/processApproval", method = RequestMethod.POST)
    public Object processApproval(String taskId, String comment, String processDefinitionId, String type) {
        String opinion = "同意";
        if ("0".equals(type)) {
            opinion = "驳回";
        }
        try {
            String userNameId = getCurrentUserNameId();
            //设置流程连线中需要的变量
            List varNames = workflowService.getVarsNameByProcessDefinitionId(processDefinitionId);
            Map<String, Object> procVars = new HashMap<>();
            for (int i = 0; i < varNames.size(); i++) {
                String varName = varNames.get(i).toString();
                procVars.put(varName, opinion);
            }
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if(task!=null){
                setLogMessage(task.getName(),"");
            }
            workflowService.completeTask(taskId, comment, userNameId, procVars);
            return JsonUtils.messageJson(200, "操作成功", "审批成功");
        } catch (Exception e) {
            e.printStackTrace();
            return JsonUtils.messageJson(300, "操作失败", "审批失败");
        }
    }


    @Autowired
    private ApiFlowableTaskService apiFlowableTaskService;

    /**
     * @MethodName getStepApprovalComments
     * @Description 查询待办/在办的审批步骤以及审批意见以及业务信息
     * @Param processInstanceId  流程实例Id
     * @Param taskId  任务Id  (在办任务Id为空)
     * @Param uuid  业务Id
     * @Return java.lang.Object
     */
    @ResponseBody
    @RequestMapping(value = "/getStepApprovalComments", method = RequestMethod.POST)
    public Object getStepApprovalComments(String processInstanceId, String taskId, String uuid) {
        //在办详细根据实例id去查询taskId
        String taskIds = "";
        if (StringUtils.isEmpty(taskId) || taskId.contains("null")) {
            List<Task> tasks = taskService.createTaskQuery().processInstanceIdIn(Collections.singletonList(processInstanceId)).list();
            taskIds = tasks.get(0).getId();
        } else {
            taskIds = taskId;
        }


        Task task = taskService.createTaskQuery().taskId(taskIds).singleResult();
        List<HistoricActivityInstance> haiList = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(task.getProcessInstanceId())
                .activityType("userTask")
                .orderByHistoricActivityInstanceStartTime()
                .asc()
                .list();
        List<CommentBean> commentBeans = new ArrayList<>();
        for (HistoricActivityInstance hai : haiList) {
            List<CommentVo> commList = apiFlowableTaskService.getTaskCommentsByTaskId(hai.getTaskId());
            for (int i = 0; i < commList.size(); i++) {
                SysUser user = businessSystemDataService.getSysUserByUserNameId(commList.get(i).getUserId());
                CommentBean commentBean = new CommentBean();
                commentBean.setActivityName(hai.getActivityName());
                commentBean.setComment(commList.get(i));
                commentBean.setUserName(user.getUserName());
                commentBeans.add(commentBean);
            }
            if (commList.size() == 0) {
                CommentBean commentBean = new CommentBean();
                commentBean.setActivityName(hai.getActivityName());
                commentBean.setUserName(hai.getActivityId());
                commentBean.setActivityName(hai.getActivityName());
                commentBeans.add(commentBean);
            }
        }
        List<OaLeaveCommon> leaveCommons = new ArrayList<>();
        //获取流程发布Id信息
        String definitionId = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult().getProcessDefinitionId();
        //获取所有节点信息
        List<org.flowable.bpmn.model.Process> processes = repositoryService.getBpmnModel(definitionId).getProcesses();
        for (org.flowable.bpmn.model.Process process : processes) {
            Collection<FlowElement> flowElements = process.getFlowElements();
            for (FlowElement flowElement : flowElements) {
                OaLeaveCommon oaLeaveCommon = null;
                if (flowElement instanceof UserTask) {
                    oaLeaveCommon = new OaLeaveCommon();
                    oaLeaveCommon.setTitle(flowElement.getName());
                    oaLeaveCommon.setTaskId(flowElement.getId());
                    leaveCommons.add(oaLeaveCommon);
                }
            }
        }
        String id = commentBeans.get(commentBeans.size() - 1).getUserName();

        Integer integer = 0;
        for (int i = 0; i < leaveCommons.size(); i++) {
            if (leaveCommons.get(i).getTaskId().equals(id)) {
                integer = i;
                break;
            }
        }
        for (int j = 0; j <= integer; j++) {
            leaveCommons.remove(0);
        }

        CommentBean commentBean = null;
        for (int i = 0; i < leaveCommons.size(); i++) {
            commentBean = new CommentBean();
            commentBean.setActivityName(leaveCommons.get(i).getTitle());
            commentBeans.add(commentBean);
        }


        OaLeave oaLeave = oaLeaveService.selectByPrimaryKey(uuid);
        Map<Object, Object> map = new HashMap<>();
        map.put("oaLeave", oaLeave);
        map.put("commentBeans", commentBeans);
        return map;
    }


    /**
     * @MethodName gethistoryProcessComment
     * @Description 获取历史审批流程的审批步骤以及审批意见以及业务信息
     * @Param processInstanceId  实例Id
     * @Param uuid 业务Id
     * @Return java.lang.Object
     */
    @ResponseBody
    @PostMapping("/gethistoryProcessComment")
    public Object gethistoryProcessComment(String processInstanceId, String uuid) {
        List<CommentBean> commentBeanList = workflowService.getCommentListByProcessInstanceId(processInstanceId);
        OaLeave oaLeave = oaLeaveService.selectByPrimaryKey(uuid);
        Map<Object, Object> map = new HashMap<>();
        map.put("oaLeave", oaLeave);
        map.put("commentBeanList", commentBeanList);
        return map;
    }


    /**
     * @MethodName stopProcessInstanceById  流程终止
     * @Description
     * @Param processInstanceId
     * @Return java.lang.Object
     * <AUTHOR> @ sbtr.com>
     * @Date 2020-09-07 11:13
     */
    @Log(title = "流程终止",module = LogModule.WORKFLOW,businessType = BusinessType.FINISH, method = "getLogMessage")
    @ResponseBody
    @PostMapping("/stopProcessInstanceById")
    public Object stopProcessInstanceById(String processInstanceId, String taskId) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (ObjectUtil.isNotEmpty(processInstance)) {
            setLogMessage(processInstance.getName(),"");
            //1、添加审批记录
            managementService.executeCommand(new AddHisCommentCmd(taskId, businessSystemDataService.getUserNameId(), processInstanceId,
                    "流程终止", "什么原因终止"));
            List<EndEvent> endNodes = findEndFlowElement(processInstance.getProcessDefinitionId());
            String endId = endNodes.get(0).getId();
            //2、执行终止
            List<Execution> executions = runtimeService.createExecutionQuery().parentId(processInstanceId).list();
            List<String> executionIds = new ArrayList<>();
            executions.forEach(execution -> executionIds.add(execution.getId()));
            moveExecutionsToSingleActivityId(executionIds, endId);
            return JsonUtils.messageJson(200, "操作成功", "终止成功");
        } else {
            return JsonUtils.messageJson(200, "操作成功", "不存在运行的流程实例,请确认!");
        }
    }

    protected void moveExecutionsToSingleActivityId(List<String> executionIds, String activityId) {
        runtimeService.createChangeActivityStateBuilder()
                .moveExecutionsToSingleActivityId(executionIds, activityId)
                .changeState();
    }


    public List<EndEvent> findEndFlowElement(String processDefId) {
        BpmnModel bpmnModel = getBpmnModelByProcessDefId(processDefId);
        if (bpmnModel != null) {
            Process process = bpmnModel.getMainProcess();
            return process.findFlowElementsOfType(EndEvent.class);
        } else {
            return null;
        }
    }

    public BpmnModel getBpmnModelByProcessDefId(String processDefId) {
        return repositoryService.getBpmnModel(processDefId);
    }

    //打开自由跳转页面
    @RequestMapping("/freeJumpPage")
    public String freeJumpPage(String processInstanceId, String taskName, String taskId) {
        List<String> activeActivityIds = runtimeService.getActiveActivityIds(processInstanceId);
        request.setAttribute("currentNodeName", taskName);
        request.setAttribute("processInstanceId", processInstanceId);
        request.setAttribute("activeActivityIds", activeActivityIds.get(0));
        return "workflow/workflow/freeJumpPage";
    }

    //自由跳转保存
    @Transactional
    @ResponseBody
    @RequestMapping(value = "/freeJumpSave", method = RequestMethod.POST)
    public Object freeJumpSave(String processInstanceId, String nodeId, String toNodeId) {
        try {
            runtimeService.createChangeActivityStateBuilder()
                    .processInstanceId(processInstanceId)
                    .moveActivityIdTo(nodeId, toNodeId)
                    .changeState();
            return success("跳转成功");
        } catch (Exception e) {
            e.printStackTrace();
            return failure("跳转失败");
        }
    }

    //根据流程实例id获取所有任务节点
    @ResponseBody
    @RequestMapping("/getAllUserTaskListByProcessInstanceId")
    public Object getAllUserTaskListByProcessInstanceId(String processInstanceId) {
        //流程定义id
        String processDefinitionId = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult().getProcessDefinitionId();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        Process process = bpmnModel.getProcesses().get(0);
        //获取所有节点
        Collection flowElements = process.getFlowElements();
        List UserTaskList = process.findFlowElementsOfType(UserTask.class);
        return UserTaskList;
    }


    /**
     * 已办
     */
    @RequestMapping("/toDoWith")
    public String toDoWith() {
        return "workflow/workflow/toDoWith";
    }

    /**
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/toDoWithPageSetData", method = RequestMethod.POST)
    public Object toDoWithPageSetData(PageParam pageParam, String processDefinitionName) {
        String filterSort = "";
        filterSort = BaseUtils.filterSort(request, filterSort);
        PageSet<TaskVo> pageSet = myVariableService.gettoDoWithPageSetData(pageParam, filterSort, businessSystemDataService.getUserNameId(), processDefinitionName);
        for (int i = 0; i < pageSet.getRows().size(); i++) {
            List<ActRuVariable> list = removeDupliById(pageSet.getRows().get(i).getActRuVariables());
            if (null != list && list.size() != 0) {
                //2.iterator遍历，查出对应值，做对应操作
                Iterator<ActRuVariable> it = list.iterator();
                while (it.hasNext()) {
                    ActRuVariable x = it.next();
                    if (StringUtils.isEmpty(x.getText())) {
                        it.remove();
                    }
                }
                Map<String, Object> result = list.stream().collect(Collectors.toMap(student -> {
                    return student.getName();
                }, ActRuVariable::getText));
                pageSet.getRows().get(i).setVariables(result);
            }

        }
        return pageSet;
    }

    @RequestMapping("/viewSteps")
    public String viewSteps(String taskId, String procInstId) {
        List<CommentBeanDto> commentBeanList = workflowService.getCommentListViewStepsByTaskId(taskId);
        request.setAttribute("commentBeanList", commentBeanList);
        request.setAttribute("taskId", taskId);
        request.setAttribute("procInstId", procInstId);

        return "workflow/workflow/viewSteps";
    }

    @Log(title = "流程撤回",module = LogModule.WORKFLOW,businessType = BusinessType.REVOCATION, method = "getLogMessage")
    @ResponseBody
    @PostMapping("/revokeProcess")
    public Object revokeProcess(String processInstanceId) {
        if (!StringUtils.isEmpty(processInstanceId)) {
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId).singleResult();
            if (processInstance != null) {
                setLogMessage(processInstance.getName(),"");
                //1.添加撤回意见
                managementService.executeCommand(new AddHisCommentCmd(null, businessSystemDataService.getUserNameId(), processInstanceId,
                        "comment", "同意"));
                //taskService.addComment(businessSystemDataService.getUserNameId(), processInstanceId, "同意","同意");
                //2.设置提交人
                runtimeService.setVariable(processInstanceId, businessSystemDataService.getUserNameId(), processInstance.getStartUserId());
                //3.执行撤回
                Activity disActivity = findActivityByName(processInstance.getProcessDefinitionId(), "填写申请");
                //4.删除运行和历史的节点信息
                deleteActivity(disActivity.getId(), processInstanceId);
                //5.执行跳转
                List<Execution> executions = runtimeService.createExecutionQuery().parentId(processInstanceId).list();
                List<String> executionIds = new ArrayList<>();
                executions.forEach(execution -> executionIds.add(execution.getId()));
                runtimeService.createChangeActivityStateBuilder()
                        .moveExecutionsToSingleActivityId(executionIds, disActivity.getId())
                        .changeState();
                return success("撤回成功!");
            }
        } else {
            return failure("流程实例id不能为空!");
        }
        return success("撤回成功!");
    }

    public Activity findActivityByName(String processDefId, String name) {
        Activity activity = null;
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefId);
        Process process = bpmnModel.getMainProcess();
        Collection<FlowElement> list = process.getFlowElements();
        for (FlowElement f : list) {
            if (org.apache.commons.lang.StringUtils.isNotBlank(name)) {
                if (name.equals(f.getName())) {
                    activity = (Activity) f;
                    break;
                }
            }
        }
        return activity;
    }

    //删除运行和历史的节点信息
    protected void deleteActivity(String disActivityId, String processInstanceId) {
        List<Map<String, Object>> disActivities = myVariableService.getListBydisActivityId(disActivityId, processInstanceId);
        if (org.flowable.editor.language.json.converter.util.CollectionUtils.isNotEmpty(disActivities)) {
            List<Map<String, Object>> datas = myVariableService.getListBydisEndTime(disActivities.get(0).get("END_TIME_").toString(), processInstanceId);
            List<String> runActivityIds = new ArrayList<>();
            if (org.flowable.editor.language.json.converter.util.CollectionUtils.isNotEmpty(datas)) {
                datas.forEach(ai -> runActivityIds.add(ai.get("ID_").toString()));
                Integer integer = myVariableService.deleteRunActinstsByIds(runActivityIds);
                myVariableService.deleteHisActinstsByIds(runActivityIds);
            }
        }
    }

    @ResponseBody
    @PostMapping(value = "/getListSysRole")
    public Result getListSysRole(PageParam pageParam, String roleName) {
        PageInfo<SysRole> list = businessSystemDataService.getListSysRole(pageParam,roleName);
        return Result.ofSuccess(list);
    }
    @ResponseBody
    @PostMapping(value = "/getListPosition")
    public Result getListPosition(PageParam pageParam, String positionName) {
        PageInfo<SysPosition> list = businessSystemDataService.getListPosition(pageParam,positionName);
        return Result.ofSuccess(list);
    }

    @ApiOperation(value = "获取组织机构和用户数据组装成组织机构下存在用户")
    @ResponseBody
    @PostMapping(value = "getTreeOrganizationAndUserData")
    public Result getTreeOrganizationAndUserData() {
        //获取组织机构数据 按照sort asc排序
        List<SysOrganization> listOrgs = businessSystemDataService.queryOrganizations();
        //获取所有用户信息
        List<SysUser> list11 = businessSystemDataService.queryUsers();
        //拷贝组织机构和用户信息
        List<SysOrganizationDto> list = BeanUtil.copyToList(listOrgs, SysOrganizationDto.class);
        List<SysUserDto> list2 = BeanUtil.copyToList(list11, SysUserDto.class);
        List<SysUserDto> list3 = new ArrayList<>();
        //循环组织机构
//        for (int i = 0; i < list.size(); i++) {
        for (int i = list.size() - 1; i >= 0; i--) {
            //循环用户信息
            for (int j = list2.size() - 1; j >= 0; j--) {
                //判断组织机构ID和用户表存储的组织机构ID是否相等
                if (list.get(i).getId().equals(list2.get(j).getUserOrgId())) {
                    //相等 add 到一个新的集合
                    list3.add(list2.get(j));
                    //相等 并且移除这条用户数据 不在做循环了
                    list2.remove(j);
                }
                //把用户集合添加到组织机构里面去
                list.get(i).setList(list3);
            }
            list3 = new ArrayList<>();
        }
        //获取到树形的组织结构
        List<SysOrganizationDto> list23 = TreeUtils.build(list);
        return Result.ofSuccess(list23);
    }

    @ApiOperation(value = "根据多个用户标识获取多个用户信息")
    @ResponseBody
    @RequestMapping("/getUserDetailById")
    public Result getUserDetailByIds(String ids, String fieldName) {
        if (StrUtil.isBlank(ids)) {
            return Result.ofFailMsg("用户标识不能为空！");
        }
        if (StrUtil.isBlank(fieldName)) {
            return Result.ofFailMsg("查询字段不能为空！");
        }
        List<SysUser> user = businessSystemDataService.getUserDetailByIds(ids, fieldName);
        return Result.ofSuccess(user);
    }
}
