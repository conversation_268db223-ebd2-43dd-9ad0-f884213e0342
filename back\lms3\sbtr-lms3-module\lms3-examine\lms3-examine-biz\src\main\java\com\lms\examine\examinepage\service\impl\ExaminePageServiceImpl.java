/**
 * FileName:	ClientExaminepage.java
 * Author:		<PERSON><PERSON><PERSON><PERSON>
 * Time:		2013-3-4上午11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.examine.examinepage.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.base.feign.api.LmsBaseApi;
import com.lms.base.feign.model.Subject;
import com.lms.common.config.LMSConfiguration;
import com.lms.common.feign.dto.Selectitem;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.StringHelper;
import com.lms.examine.examinepage.mapper.ExaminePageMapper;
import com.lms.examine.examinepage.service.ExaminePageService;
import com.lms.examine.examinestudent.service.ExaminestudentService;
import com.lms.examine.examinesubject.service.ExaminesubjectService;
import com.lms.examine.examinesubjecttype.service.ExaminesubjecttypeService;
import com.lms.examine.feign.model.Examinecourse;
import com.lms.examine.feign.model.Examinepage;
import com.lms.examine.feign.model.Examinesubjecttype;
import com.lms.examine.feign.model.Examinesubject;
import com.lms.examine.feign.model.ExaminesubjectViewModel;
import com.lms.examine.feign.model.ExportDataViewModel;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 保存项目的相关信息
 */
@Service("ExaminePageService")
public class ExaminePageServiceImpl extends BaseServiceImpl<ExaminePageMapper, Examinepage> implements ExaminePageService {

	@Resource
	private ExaminePageMapper examinePageMapper;

	@Resource
	private ExaminesubjecttypeService examinesubjecttypeService;

	@Resource
	private ExaminesubjectService examinesubjectService;

	@Resource
	private ExaminestudentService examinestudentService;

	@Resource
	private LmsBaseApi lmsBaseApi;

	private Configuration configuration;

	@Resource
	private LMSConfiguration lmsConfiguration;

	public ExaminePageServiceImpl() {
		configuration = new Configuration(Configuration.VERSION_2_3_32);
		configuration.setDefaultEncoding("UTF-8");
	}


	public String getScoreResult(String examineid, Double score) {
		Examinepage ep = getById(examineid);
		Double pass = ep.getPass();
		Double choiceness = ep.getChoiceness();
		Double excellence = ep.getExcellence();
		String result = "";
		if(score != null && pass!=null){
			if (score < pass) {
				result = "17ebb78046e44967a5c3bc9338342062";// 不及格
			}else if(choiceness==null || excellence==null){
				result = "f6d1d2fbb4274362bf83991c5d07cca7";// 及格,没有设置优秀和良好分数，结果就是及格
			}else if (score >= pass && score < choiceness) {
				result = "f6d1d2fbb4274362bf83991c5d07cca7";// 及格
			} else if (score >= choiceness && score < excellence) {
				result = "78681997db8e43979ed600f1ef341325";// 良好
			} else {
				result = "4939f022ca4d4d30a0321f1e3b36236f";// 优秀
			}
		}
		return result;
	}

	private void getExamineSubjectInfo(Map<String, Object> dataMap,
			Examinepage examinepage) {
		List<Examinesubjecttype> typelist = examinesubjecttypeService.getExaminesubjecttypeWithoutEmpty(examinepage.getId());
		List<ExportDataViewModel> list = new ArrayList<ExportDataViewModel>();
		List<String> indexList = new ArrayList();
		List<ExaminesubjectViewModel> modellist = new ArrayList<ExaminesubjectViewModel>();
		Map<String, List> subjectMap = new HashMap();
		String checkMsg = examinestudentService.chekcSubjectAmountByExamine(examinepage, 1, subjectMap);
		for (int i = 0; i < typelist.size(); i++) {
			Examinesubjecttype examinesubjecttype = typelist.get(i);
			Selectitem selectitem = commonBaseApi.getSelectitemById(StringHelper.null2String(examinesubjecttype.getSubjecttype())).getResult();
			String objname = StringHelper.null2String(selectitem.getObjname());
			String qty = StringHelper.null2String(examinesubjecttype.getQty());
			String point = StringHelper.null2String(examinesubjecttype.getPerpoint());
			indexList.add(objname);
			point = point.replaceAll("0+?$", "");
			point = point.replaceAll("[.]$", "");
			String[] parameters = {qty, point};
			String desc = commonSystemApi.translateContent("（共[?]道题，每题[?]分）", "", parameters);
			examinesubjecttype.setTypename(setIndex(objname+desc,i));
			ExportDataViewModel exportDataViewModel = new ExportDataViewModel();
			exportDataViewModel.setExaminesubjecttype(examinesubjecttype);
			List<Examinesubject> examinesubjects = new ArrayList<>();
			if(examinepage.getModal().equals("12058eacd08a454fb12ebbf19a5df651")){
				if(checkMsg.indexOf("createSuccess") > -1){
					for (Map.Entry<String, List> entry : subjectMap.entrySet()) {
						String st = entry.getKey();
						List<String> subjectids = entry.getValue();
						if (st.equals(examinesubjecttype.getSubjecttype()) && subjectids.size() > 0) {
							for (int j = 0; j < subjectids.size(); j++) {
								Examinesubject examinesubject = new Examinesubject();
								examinesubject.setExamineid(examinepage.getId());
								String sid = subjectids.get(j);
								Subject sub = lmsBaseApi.getSubjectById(sid).getResult();
								examinesubject.setSeqno(j+1);
								examinesubject.setContent(sub.getContent());
								examinesubject.setExamineid(examinepage.getId());
								examinesubject.setCorrectresponse(sub.getContent());
								examinesubject.setTitle(sub.getTitle());
								examinesubject.setAttach(sub.getAttach());
								examinesubject.setType(sub.getType());
								examinesubject.setImgUrl(sub.getImgUrl());
								examinesubjects.add(examinesubject);
							}
						}
					}
				}
			}else{
				examinesubjects = examinesubjectService.getExaminesubjectsByExamType(
						examinesubjecttype.getExamineid(),
						examinesubjecttype.getSubjecttype());
			}
			for (int j = 0; j < examinesubjects.size(); j++) {
				Examinesubject es = examinesubjects.get(j);
				ExaminesubjectViewModel viewmodel = new ExaminesubjectViewModel(
						es, j);
				modellist.add(viewmodel);
			}
			exportDataViewModel.setItems(modellist);
			list.add(exportDataViewModel);
		}
		dataMap.put("subjectinfo", list);
		dataMap.put("subjectindex", indexList);
	}

	private String setIndex(String objname, int count) {
		String language = commonSystemApi.getSettingValueById("90f77810bad911ec85425c4f7a74d8de").getResult();//获取当前系统语言设置
		String index = "";
		switch (count) {
		case 0:
			index = language.equals("Chinese")?"一":"I";
			break;
		case 1:
			index = language.equals("Chinese")?"二":"II";
			break;
		case 2:
			index = language.equals("Chinese")?"三":"III";
			break;
		case 3:
			index = language.equals("Chinese")?"四":"IV";
			break;
		case 4:
			index = language.equals("Chinese")?"五":"V";
			break;
		case 5:
			index = language.equals("Chinese")?"六":"VI";
			break;
		case 6:
			index = language.equals("Chinese")?"七":"VII";
			break;
		default:
			break;
		}
		if (objname.isEmpty()) {
			return index;
		}
		return index + "、" + objname;
	}


	public String isRelease(String id) {
		String errorName = "";
		Examinepage examinepage = getById(id);
		if(examinepage!=null){
			String type = StringHelper.null2String(examinepage.getType());
			String name = StringHelper.null2String(examinepage.getName());
			int status = examinepage.getStatus();
			if(type.equals("4028828a6005533d0160055343e90000") && status>1){
				errorName = name;
			}
		}
		return errorName;
	}


	public String findExamineIdByCourse(String courseId,String planId) {
		String examid = "";
		LambdaQueryWrapper<Examinepage> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(Examinepage::getCourseid, courseId);
		wrapper.eq(Examinepage::getPlanid, planId);
		wrapper.ne(Examinepage::getStatus, 0);
		List<Examinepage> examinepageList = list(wrapper);
		if(examinepageList.size()>0){
			return examinepageList.get(0).getId();
		}
		return examid;
	}

	public Examinepage getEagerData(String id){
		Examinepage examinepage = getById(id);
		if(ObjectUtil.isNotEmpty(examinepage)){
			String hql = "from Examinesubjecttype where examineid='"+id+"'";
			List<Examinesubjecttype> examinesubjecttypes = JSON.parseArray(JSON.toJSONString(examinePageMapper.listQuery(hql)), Examinesubjecttype.class);
			hql = "from Examinecourse where examineid='"+id+"'";
			List<Examinecourse> examinecourses = JSON.parseArray(JSON.toJSONString(examinePageMapper.listQuery(hql)), Examinecourse.class);
			examinepage.setExaminesubjecttypes(examinesubjecttypes);
			examinepage.setExaminecourses(examinecourses);
		} else {
			examinepage = new Examinepage();
		}
		return examinepage;
	}

	public List<Examinepage> listExaminepage(String planId){
		return examinePageMapper.listExaminepage(planId);
	}

	public void batchReleaseExaminepage(List<String> idList) {
		examinePageMapper.batchReleaseExaminepage(idList);
	}

	public String createWord(Examinepage examine) throws IOException {// 项目任务书
		Map<String, Object> dataMap = new HashMap<String, Object>();
		getExamineSubjectInfo(dataMap, examine);
		dataMap.put("examinename", examine.getName());
		configuration.setClassForTemplateLoading(this.getClass(),
				"/com/lms/examine/examinepage/service/ftl"); // FTL文件所存在的位置
		Template t = null;
		try {
			t = configuration.getTemplate("examinepage.ftl"); // 文件名
		} catch (IOException e) {
			e.printStackTrace();
		}

//		String fileRootPath = "D:\\oa2\\ceprei";
//		String filePath = fileRootPath + "\\keyan";
		String filePath = lmsConfiguration.getTemppath();
		String fileid = commonSystemApi.getNewCode("ex");
		File file = new File(filePath);
		if (!file.exists()) {
			file.mkdir();
		}
		String filedir = filePath + "\\" + fileid;
		File outFile = new File(filedir);

		Writer out = null;
		try {
			out = new BufferedWriter(new OutputStreamWriter(
					new FileOutputStream(outFile), StandardCharsets.UTF_8));
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		try {
			t.process(dataMap, out);
		} catch (Exception e) {
			e.printStackTrace();
		}
		try {
			out.close();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return filedir;

	}

	public void calcSubjectKKD(String examid){
		String sql = "SELECT r.EXAMINESUBJECTID subjectid,sum(ifnull(r.SCORE,0)) score,sum(t.PERPOINT) totalpoint from U_EXAMINESTUDENTRECORD r" +
				" inner join u_examinepage p on p.id=r.EXAMINEID and p.STATUS=3" +
				" inner join u_examinesubjecttype t on t.examineid=r.EXAMINEID and t.subjecttype=r.type" +
				" where r.EXAMINESUBJECTID in(" +
				" SELECT SUBJECTID FROM U_EXAMINESUBJECT WHERE EXAMINEID='"+examid+"')" +
				" group by r.EXAMINESUBJECTID having count(r.EXAMINESUBJECTID)>=5";
		List list = listSQLQuery(sql);
		lmsBaseApi.calcSubjectKKD(list).getResult();
	}

	public JSONObject calcSubjectScoreRate(String subjectid){
		String sql = "SELECT p.name,round(sum(ifnull(r.SCORE,0)) * 100 / sum(t.PERPOINT),2) scorerate from U_EXAMINESTUDENTRECORD r" +
				" inner join u_examinepage p on p.id=r.EXAMINEID and p.STATUS=3" +
				" inner join u_examinesubjecttype t on t.examineid=r.EXAMINEID and t.subjecttype=r.type" +
				" where r.EXAMINESUBJECTID ='"+subjectid+"'" +
				" group by p.id,p.name";
		List list = listSQLQuery(sql);
		JSONObject resultObject = new JSONObject();
		JSONArray XArray = new JSONArray();
		JSONArray YArray = new JSONArray();
		list.forEach(c -> {
			Map map = (Map)c;
			String name = StringHelper.null2String(map.get("name"));
			String scorerate = StringHelper.null2String(map.get("scorerate"));
			XArray.add(name);
			YArray.add(scorerate);
		});
		resultObject.put("XArray",XArray);
		resultObject.put("YArray",YArray);
		return resultObject;
	}

	private List listSQLQuery(String sql) {
		return examinePageMapper.listQuery(sql);
	}

}
