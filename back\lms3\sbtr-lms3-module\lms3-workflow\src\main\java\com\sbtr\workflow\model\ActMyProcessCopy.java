package com.sbtr.workflow.model;

import cn.ewsd.common.model.MCoreBase;

import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * 流程抄送表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-12-09 11:58:35
 */
@Table(name = "act_my_process_copy")
public class ActMyProcessCopy extends MCoreBase {
    private static final long serialVersionUID = 1L;
    /**
     * 被抄送人的工号
     */
    private String userNameId;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 流程定义id
     */
    private String processDefinitionId;

    /**
     * 流程标题
     */
    private String formName;
    /**
     * 节点名称
     */
    private String modelKey;
    /**
     * 节点名称
     */
    private String taskId;
    /**
     * 节点名称
     */
    private String nodeId;
    /**
     * 节点名称
     */
    private String taskName;
    /**
     * 节点名称
     */
    private String businessKey;
    /**
     * 流程启动人
     */
    private String startUserId;
    /**
     * 状态
     */
    private String  reviewStatus;

    @Transient
    private String  userName;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getModelKey() {
        return modelKey;
    }

    public void setModelKey(String modelKey) {
        this.modelKey = modelKey;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getStartUserId() {
        return startUserId;
    }

    public void setStartUserId(String startUserId) {
        this.startUserId = startUserId;
    }

    /**
     * 设置：被抄送人的工号
     */
    public void setUserNameId(String userNameId) {
        this.userNameId = userNameId;
    }

    /**
     * 获取：被抄送人的工号
     */
    public String getUserNameId() {
        return userNameId;
    }

    /**
     * 设置：流程实例id
     */
    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    /**
     * 获取：流程实例id
     */
    public String getProcessInstanceId() {
        return processInstanceId;
    }

    /**
     * 设置：流程定义id
     */
    public void setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    /**
     * 获取：流程定义id
     */
    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    /**
     * 设置：流程标题
     */
    public void setFormName(String formName) {
        this.formName = formName;
    }

    /**
     * 获取：流程标题
     */
    public String getFormName() {
        return formName;
    }


}
