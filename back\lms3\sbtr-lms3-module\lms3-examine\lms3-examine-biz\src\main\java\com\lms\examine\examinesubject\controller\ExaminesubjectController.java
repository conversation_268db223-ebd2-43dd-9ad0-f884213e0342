/**
 * FileName:ExaminesubjectController.java
 * Author:ji<PERSON><PERSON><PERSON>
 * Time  :2013-3-7下午4:18:10
 * CopyRight: SBTR LTD.
 * decription:to-do
 */
package com.lms.examine.examinesubject.controller;

import com.alibaba.fastjson.JSONObject;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.feign.dto.Selectitem;
import com.lms.common.model.Result;
import com.lms.common.util.StringHelper;
import com.lms.examine.examinepage.service.ExaminePageService;
import com.lms.examine.examinesubject.service.ExaminesubjectService;
import com.lms.system.feign.api.AttachApi;
import com.lms.examine.feign.model.Examinepage;
import com.lms.examine.feign.model.Examinesubject;
import com.lms.examine.feign.model.ExaminesubjectViewModel;
import com.lms.examine.feign.model.Examinesubjecttype;
import com.lms.system.feign.model.Attach;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR> 试题信息操作控制层
 */
@RestController
@RequestMapping("/examinesubject")
@Api(value = "试卷题目信息管理",tags = {"试卷题目信息管理"})
public class ExaminesubjectController extends BaseController<Examinesubject> {
    @Resource
    private ExaminesubjectService examinesubjectService;
    @Resource
    private ExaminePageService examinepageService;

    @Resource
    private AttachApi attachService;

    @PostMapping(value = {"/listpage"})
    @ApiOperation(value = "试卷题目查询", httpMethod = "POST")
    public Result getExaminesubjectList(@RequestBody JSONObject request) {
        // 设置分页参数,查询参数
        // super.setParameter(request);
        PageInfo pageInfo = super.getPageInfo(request);
        Page<Examinesubject> examinesubjects = examinesubjectService.listByCondition(pageInfo);

        List<ExaminesubjectViewModel> sModelList = new ArrayList<ExaminesubjectViewModel>();
        List<Selectitem> slist = commonBaseApi.getSelectitemsByType("fc657149c8d84e79a5f3043c40d7baaa").getResult();
        List<Selectitem> returnlist = new ArrayList<Selectitem>();
        String extids = "";
        for (Examinesubject sub : examinesubjects.getRecords()) {
            String subjecttype = sub.getType();
            extids = extids.indexOf(subjecttype) > -1 ? extids : extids + ","
                    + subjecttype;
            if (!StringHelper.isEmpty(sub.getAttach())) {
                Attach attach = attachService.get(sub.getAttach()).getResult();
                if (attach != null) {
                    sub.setImgUrl(attach.getFiledir());
                }
            }
            ExaminesubjectViewModel s = new ExaminesubjectViewModel(sub);
            sModelList.add(s);
        }
        for (int i = 0; i < slist.size(); i++) {
            Selectitem s = slist.get(i);
            String sid = s.getId();
            if (extids.indexOf(sid) >= 0) {
                returnlist.add(s);
            }
        }
        if (sModelList.size() > 0) {
            ExaminesubjectViewModel first = sModelList.get(0);
            first.setSelectitems(returnlist);
        }
        return Result.OK(createPage(sModelList, examinesubjects));
    }

    /*
     * 获取所有试题信息
     */
    @RequestMapping(value = {"/list"}, method = RequestMethod.GET)
    @ApiOperation(value = "获取所有试题信息", httpMethod = "GET")
    public Result getValidExaminesubject() {
        List<Examinesubject> examinesubjects = this.examinesubjectService
                .getValidExaminesubjects();
        return Result.OK(examinesubjects);
    }

    /*
     * 根据ID获取试题信息
     */
    @RequestMapping(value = {"/getInfo/{id}"}, method = RequestMethod.GET)
    @ApiOperation(value = "根据ID获取试题信息", httpMethod = "GET")
    public Result getExaminesubjectInfo(@PathVariable("id") String pid) {
        Examinesubject examinesubject = this.examinesubjectService.getById(pid);
        return Result.OK(examinesubject);
    }


    @RequestMapping(value = {"/modify/{id}"}, method = RequestMethod.POST)
    @ApiOperation(value = "编辑试题信息", httpMethod = "POST")
    public Result updateExaminesubject(@RequestBody Examinesubject examinesubject,
                                       @PathVariable("id") String id) {
        // Examinesubject p = this.examinesubjectService.get(id);
        examinesubject.setId(id);
        examinesubjectService.saveOrUpdate(examinesubject);
        return Result.OK(examinesubject);
    }


    @RequestMapping(value = {"/del/{id}"}, method = RequestMethod.DELETE)
    @ApiOperation(value = "删除试题信息", httpMethod = "DELETE")
    public Result deleteExaminesubject(@PathVariable("id") String id) {
        Examinesubject examinesubject = examinesubjectService.getById(id);
        int delSeqno = examinesubject.getSeqno();
        String examineid = StringHelper.null2String(examinesubject
                .getExamineid());
        String type = StringHelper.null2String(examinesubject.getType());
        this.examinesubjectService.removeById(examinesubject);
        List<Examinesubject> ess = examinesubjectService
                .getExaminesubjectsByExamType(examineid, type);
        for (int i = 0; i < ess.size(); i++) {
            Examinesubject es = ess.get(i);
            int seqno = es.getSeqno();
            if (seqno > delSeqno) {
                seqno--;
                es.setSeqno(seqno);
                this.examinesubjectService.saveOrUpdate(es);
            }
        }
        return Result.OK();
    }

    // 批量删除
    @RequestMapping(value = {"/batchremove"}, method = RequestMethod.DELETE)
    @ApiOperation(value = "批量删除试题信息", httpMethod = "DELETE")
    public void batchDeleteExaminesubject(@RequestParam("ids") String ids) {
        String[] idarray = ids.split(",");
        List<String> idList = new ArrayList<String>();
        for (String id : idarray) {
            idList.add(id);
        }
        //获取试题类型，试题id；
        Examinesubject examinesubject = examinesubjectService.getById(idarray[0]);
        String examineid = StringHelper.null2String(examinesubject
                .getExamineid());
        String type = StringHelper.null2String(examinesubject.getType());

        // 删除试题;
        this.examinesubjectService.removeBatchByIds(idList);

        //更新试题序号；
        if (idarray.length > 0) {
            List<Examinesubject> examinesubjectList = examinesubjectService
                    .getExaminesubjectsByExamType(examineid, type);
            for (int i = 0; i < examinesubjectList.size(); i++) {
                Examinesubject es = examinesubjectList.get(i);
                es.setSeqno(i + 1);
                this.examinesubjectService.saveOrUpdate(es);
            }
        }
    }

    @RequestMapping(value = {"/add"}, method = RequestMethod.POST)
    @ApiOperation(value = "添加试题信息", httpMethod = "POST")
    public Result saveExaminesubject(
            @RequestBody Examinesubject[] examinesubjects) {
        // 所有新增的用户都是普通用户
        // int existamount = 0;
        String examineid = StringHelper.null2String(examinesubjects[0]
                .getExamineid());
        String type = StringHelper.null2String(examinesubjects[0].getType());
        String subjectid = StringHelper.null2String(examinesubjects[0].getSubjectid());
        int examqty = 0;
        if (examinesubjects.length > 0) {
            Examinepage ep = examinepageService.getEagerData(examinesubjects[0]
                    .getExamineid());
            List<Examinesubjecttype> es = ep.getExaminesubjecttypes();
            for (Examinesubjecttype s : es) {
                if (s.getSubjecttype().equals(type)) {
                    examqty = s.getQty();
                    break;
                }
            }
            List<Examinesubject> eps = examinesubjectService.getExaminesubjectsBySubjectId(examineid, subjectid);
            int existamount = eps.size();
            if (existamount > 0) {
                return Result.error(commonSystemApi.translateContent("试卷中不能维护重复的题目，请确认！"));
            } else {
                eps = examinesubjectService
                        .getExaminesubjectsByExamType(examineid, type);
                existamount = eps.size();
                if (examqty < existamount + examinesubjects.length) {
                    return Result.error(commonSystemApi.translateContent("该题型只能添加[?]道题目，如需增加，请修改试卷题型配置！","",String.valueOf(examqty)));
                } else {
                    for (int i = 0; i < examinesubjects.length; i++) {
                        Examinesubject examinesubject = examinesubjects[i];
                        int seqno = examinesubject.getSeqno();
                        seqno = seqno + existamount;
                        examinesubject.setSeqno(seqno);
                        this.examinesubjectService.saveOrUpdate(examinesubject);
                    }
                }
            }
        }
        return Result.OK(examinesubjects);
    }

    @RequestMapping(value = {"/addcwsubject"}, method = RequestMethod.POST)
    @ApiOperation(value = "添加课件试题信息", httpMethod = "POST")
    public Result saveCursewareExaminesubject(
            @RequestBody Examinesubject[] examinesubjects) throws SQLException {
        // 所有新增的用户都是普通用户
        // int existamount = 0;
        String examineid = StringHelper.null2String(examinesubjects[0]
                .getExamineid());
        String type = StringHelper.null2String(examinesubjects[0].getType());
        String subjectid = StringHelper.null2String(examinesubjects[0].getSubjectid());
//        int examqty = 0;
        if (examinesubjects.length > 0) {
//            Examinepage ep = examinepageService.get(examinesubjects[0]
//                    .getExamineid());
//            Set<Examinesubjecttype> es = ep.getExaminesubjecttype();
//            for (Examinesubjecttype s : es) {
//                if (s.getSubjecttype().equals(type)) {
//                    examqty = s.getQty();
//                    break;
//                }
//            }
            List<Examinesubject> eps = examinesubjectService.getExaminesubjectsBySubjectId(examineid, subjectid);
            int existamount = eps.size();
            if (existamount > 0) {
                return Result.error(commonSystemApi.translateContent("试卷中不能维护重复的题目，请确认！"));
            } else {
                eps = examinesubjectService.getExaminesubjectById(examineid, 0);
                existamount = eps.size();
                for (int i = 0; i < examinesubjects.length; i++) {
                    Examinesubject examinesubject = examinesubjects[i];
                    int seqno = examinesubject.getSeqno();
                    seqno = seqno + existamount;
                    examinesubject.setSeqno(seqno);
                    this.examinesubjectService.saveOrUpdate(examinesubject);
                }
            }
        }
        return Result.OK(examinesubjects);
    }

    /**
     * 以下是远程调用暴露接口
     */
    @GetMapping("/getSubjectByExamineId")
    @ApiOperation(value = "根据ID获取试题信息", httpMethod = "POST")
    Result<List<Examinesubject>> getSubjectByExamineId(@RequestParam String id){
        return Result.OK(examinesubjectService.getSubjectByExamineId(id));
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存试题信息", httpMethod = "POST")
    public Result save(@RequestBody Examinesubject examinesubject){
        return Result.OK(examinesubjectService.saveOrUpdate(examinesubject));
    }

    @GetMapping("/getExaminesubjectsBySubjectId")
    @ApiOperation(value = "根据试题id获取考试试题信息", httpMethod = "GET")
    public Result<List<Examinesubject>> getExaminesubjectsBySubjectId(@RequestParam String examid, @RequestParam String subjectid) {
        return Result.OK(examinesubjectService.getExaminesubjectsBySubjectId(examid, subjectid));
    }


    @GetMapping("/isExistBySubjectId")
    @ApiOperation(value = "根据试题id判断考试试题信息是否存在", httpMethod = "GET")
    public Result<List<Examinesubject>> isExistBySubjectId(@RequestParam String subjectId) {
        return Result.OK(examinesubjectService.isExistBySubjectId(subjectId));
    }


}
