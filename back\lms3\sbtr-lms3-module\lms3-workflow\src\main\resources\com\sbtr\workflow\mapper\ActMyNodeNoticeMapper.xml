<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ActMyNodeNoticeMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sbtr.workflow.model.ActMyNodeNotice" id="flowNodeNoticeMap">
        <result property="uuid" column="uuid"/>
        <result property="createTime" column="create_time"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorOrgId" column="creator_org_id"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="actDeModelId" column="act_de_model_id"/>
        <result property="actDeModelKey" column="act_de_model_key"/>
        <result property="noticeName" column="notice_name"/>
        <result property="noticeCode" column="notice_code"/>
        <result property="nodeName" column="node_name"/>
        <result property="nodeId" column="node_id"/>
        <result property="flowModelUuid" column="flow_model_uuid"/>
        <result property="procdefId" column="procdef_id"/>
    </resultMap>

    <sql id="Base_Column_List">
  	  	     			 uuid
		  ,  	  	     			 create_time
		  ,  	  	     			 creator
		  ,  	  	     			 creator_id
		  ,  	  	     			 creator_org_id
		  ,  	  	     			 modifier_id
		  ,  	  	     			 modifier
		  ,  	  	     			 modify_time
		  ,  	  	     			 act_de_model_id
		  ,  	  	     			 act_de_model_key
		  ,  	  	     			 notice_name
		  ,  	  	     			 notice_code
		  ,  	  	     			 node_name
		  ,  	  	     			 node_id
		  ,  	  	     			 flow_model_uuid
		  ,  	  	     			 procdef_id
		    	  </sql>
    <insert id="insertList">
        insert into act_my_node_notice (
        uuid
        , create_time
        , creator
        , creator_id
        , creator_org_id
        , act_de_model_id
        , act_de_model_key
        , notice_name
        , node_id
        , flow_model_uuid
        , procdef_id
        )
        values
        <foreach collection="list" item="list" index="index" separator=",">
            (#{list.uuid,jdbcType=VARCHAR},
            #{list.createTime,jdbcType=DATE},
            #{list.creator,jdbcType=VARCHAR},
            #{list.creatorId,jdbcType=VARCHAR},
            #{list.creatorOrgId,jdbcType=VARCHAR},
            #{list.actDeModelId,jdbcType=VARCHAR},
            #{list.actDeModelKey,jdbcType=VARCHAR},
            #{list.noticeName,jdbcType=VARCHAR},
            #{list.nodeId,jdbcType=VARCHAR},
            #{list.flowModelUuid,jdbcType=VARCHAR},
            #{list.procdefId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <update id="updateProcdefIdByModelKeyAndProcdef">
        update act_my_node_notice set procdef_id=#{procdefId}  where act_de_model_key = #{key} and procdef_id =#{id}
    </update>
    <update id="updateProcdefIdByModelKey">
        update act_my_node_notice set procdef_id=#{procdefId}  where act_de_model_key = #{key} and (coalesce(procdef_id ,N'') = '')
    </update>
    <update id="updateProcdefIdIsNull">
                update act_my_node_notice set procdef_id=''  where act_de_model_key = #{key} and procdef_id =#{id}

    </update>

    <select id="getPageSet" resultType="com.sbtr.workflow.model.ActMyNodeNotice">
        select
        <include refid="Base_Column_List"></include>
        from act_my_node_notice
        <where>
            <if test="filterSort != null">
                ${filterSort}
            </if>
        </where>
    </select>
    <select id="getListByNodeIdAndModelKeyAndProcdefId" resultType="com.sbtr.workflow.model.ActMyNodeNotice">
        select
        <include refid="Base_Column_List"></include>
        from act_my_node_notice where act_de_model_key = #{modelKey} and procdef_id=#{procdefId} and node_id =#{nodelId}
    </select>

    <select id="getListByActDeModelKeyAndProcdefId" resultType="com.sbtr.workflow.model.ActMyNodeNotice">
        select
        <include refid="Base_Column_List"></include>
        from act_my_node_notice where act_de_model_key = #{key} and procdef_id=#{procdefIds}
    </select>


    <delete id="executeDeleteBatch">
        delete from act_my_node_notice where uuid in
        <foreach item="uuid" collection="array" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </delete>

    <delete id="deleteByModelKeyAnProcdefId">
        delete from act_my_node_notice where act_de_model_key = #{key} and procdef_id=#{id}
    </delete>

</mapper>
