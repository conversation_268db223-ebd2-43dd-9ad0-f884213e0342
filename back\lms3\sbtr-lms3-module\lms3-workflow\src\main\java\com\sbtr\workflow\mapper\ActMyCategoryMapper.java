package com.sbtr.workflow.mapper;
import com.sbtr.workflow.model.ActMyCategory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程分类表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-12 06:00:42
 */
public interface ActMyCategoryMapper extends tk.mybatis.mapper.common.Mapper<ActMyCategory> {

   int executeDeleteBatch(Object[] var1);

    int getListByCodeAndUuid(@Param("categoryCode")String categoryCode,@Param("uuid") String uuid);
}
