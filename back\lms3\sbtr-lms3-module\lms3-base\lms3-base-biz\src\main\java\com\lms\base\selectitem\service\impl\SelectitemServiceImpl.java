/**
 * FileName:	SelectitemService.java
 * Author:		z<PERSON><PERSON>rong
 * Time:		2017-11-22上午11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.base.selectitem.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.base.feign.model.Selectitem;
import com.lms.base.selectitem.service.SelectitemService;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.dao.query.Parameter;
import com.lms.common.service.impl.BaseServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import com.lms.base.selectitem.mapper.SelectitemMapper;

import javax.annotation.Resource;

/**
 * 保存项目的相关信息
 */
@Service("SelectitemService")
public class SelectitemServiceImpl extends BaseServiceImpl<SelectitemMapper, Selectitem> implements SelectitemService {

    @Resource
    private SelectitemMapper selectitemDao;

    public List<Selectitem> getSelectitemsByType(String typeid) {
        return selectitemDao.getSelectitemListByTypeid(typeid);
    }

    public boolean existName(String id, String objname, String typeid) {
        LambdaQueryWrapper<Selectitem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Selectitem::getStatus, 1);
        wrapper.ne(Selectitem::getId, id);
        wrapper.eq(Selectitem::getObjname, objname);
        wrapper.eq(Selectitem::getTypeid, typeid);
        wrapper.ne(id != null, Selectitem::getId, id);
        List<Selectitem> list = this.list(wrapper);
        return list.size() > 0;
    }

    public List<Selectitem> getAllSelectitemsByType(String typeid) {
        return selectitemDao.getAllSelectitemsByType(typeid);
    }

    public Selectitem getSelectitemByTypeName(String name, String typeid) {
        return this.selectitemDao.getSelectitemByTypeName(name, typeid);
    }

    public List<Selectitem> getByNameList(ArrayList<String> nameList) {
        LambdaQueryWrapper<Selectitem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Selectitem::getObjname, nameList);
        return this.list(wrapper);
    }

    public Selectitem getSelectitemByFileName(String objname) {
        PageInfo pageInfo = new PageInfo(1000, 1);
        Parameter parameter = Parameter.getParameter(
                "S_Like_objname", objname); //教员过滤
        pageInfo.getParameters().add(parameter);
        Page<Selectitem> page = listByCondition(pageInfo);
        if (null != page && CollectionUtils.isNotEmpty(page.getRecords())) {
            return page.getRecords().get(0);
        } else {
            return null;
        }

    }
}
