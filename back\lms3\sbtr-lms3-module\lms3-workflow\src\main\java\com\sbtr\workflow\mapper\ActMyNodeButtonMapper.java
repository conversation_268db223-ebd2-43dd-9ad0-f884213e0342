package com.sbtr.workflow.mapper;

import com.sbtr.workflow.model.ActMyNodeButton;
import com.sbtr.workflow.vo.ActMyNodeButtonVo;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 流程每个节点所对应按钮
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-23 07:12:25
 */
public interface ActMyNodeButtonMapper extends tk.mybatis.mapper.common.Mapper<ActMyNodeButton> {

    //获取分页集
    List<ActMyNodeButton> getPageSet();

   int executeDeleteBatch(Object[] var1);


    Integer insertListMySql(@Param("list")  List<ActMyNodeButton> list);

    Integer insertListOracle(@Param("list")  List<ActMyNodeButton> list);

    List<ActMyNodeButton> getListByActDeModelKeyAndProcdefId(@Param("key") String key, @Param("procdefIds")  String procdefIds);

    Integer updateProcdefIdByModelKey(@Param("key") String key,@Param("procdefId")  String procdefId);

    Integer updateProcdefIdIsNull(@Param("key") String key, @Param("id") String id);

    Integer deleteByModelKeyAnProcdefId(@Param("key") String key, @Param("id") String id);

    List<ActMyNodeButtonVo> getListByActDeModelKeyAndProcdefIdAndNodeId(@Param("modelKey")String modelKey, @Param("processDefinitionId")String processDefinitionId, @Param("nodeId") String nodeId);

    HashMap<String, Object> selectByBusinessKey(@Param("sql")String sql);
    Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id);

    List<HashMap<String, Object>> getDetailByModelKey(@Param("modelKey") String modelKey);
}
