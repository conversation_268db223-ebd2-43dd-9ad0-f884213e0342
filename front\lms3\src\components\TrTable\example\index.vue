<script setup lang="ts">
import { ref, h } from "vue";
import TrTable from "@/components/TrTable/index.vue";
import type {
  PaginationState,
  SortState,
  TableState
} from "@/components/TrTable/types";
import { message, notification } from "ant-design-vue";
import {
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  HeartOutlined,
  SettingOutlined
} from "@ant-design/icons-vue";
import GenderSelect from "./GenderSelect.vue";
import CustomEventComponent from "./CustomEventComponent.vue";

// 查询项配置 - 展示各种组件类型
const queryItems = [
  {
    key: "name",
    label: "姓名",
    component: "a-input",
    props: {
      placeholder: "请输入姓名",
      allowClear: true,
      prefix: h(UserOutlined)
    }
  },
  {
    key: "gender",
    label: "性别",
    componentInstance: GenderSelect,
    props: {
      placeholder: "请选择性别",
      allowClear: true
    }
  },
  {
    key: "age",
    label: "年龄",
    component: "a-input-number",
    props: {
      placeholder: "请输入年龄",
      min: 1,
      max: 120,
      style: { width: "100%" }
    }
  },
  {
    key: "status",
    label: "状态",
    component: "a-select",
    props: {
      options: [
        { label: "正常", value: 1 },
        { label: "禁用", value: 0 },
        { label: "待审核", value: 2 }
      ],
      allowClear: true,
      placeholder: "请选择状态"
    }
  },
  {
    key: "registerTime",
    label: "注册时间",
    component: "a-range-picker",
    props: {
      placeholder: ["开始时间", "结束时间"],
      format: "YYYY-MM-DD",
      allowClear: true,
      style: { maxWidth: "250px" }
    }
  },
  {
    key: "email",
    label: "邮箱",
    component: "a-input",
    props: {
      placeholder: "请输入邮箱",
      allowClear: true,
      prefix: h(MailOutlined)
    }
  },
  {
    key: "phone",
    label: "手机号",
    component: "a-input",
    props: {
      placeholder: "请输入手机号",
      allowClear: true,
      prefix: h(PhoneOutlined)
    }
  },
  {
    key: "department",
    label: "部门",
    component: "a-select",
    props: {
      options: [
        { label: "技术部", value: "技术部" },
        { label: "产品部", value: "产品部" },
        { label: "设计部", value: "设计部" },
        { label: "运营部", value: "运营部" },
        { label: "市场部", value: "市场部" }
      ],
      allowClear: true,
      placeholder: "请选择部门"
    }
  },
  {
    key: "position",
    label: "职位",
    component: "a-select",
    props: {
      options: [
        { label: "工程师", value: "工程师" },
        { label: "产品经理", value: "产品经理" },
        { label: "设计师", value: "设计师" },
        { label: "运营专员", value: "运营专员" },
        { label: "市场专员", value: "市场专员" }
      ],
      allowClear: true,
      placeholder: "请选择职位"
    }
  },
  {
    key: "rating",
    label: "评分",
    component: "a-select",
    props: {
      options: [
        { label: "1星", value: 1 },
        { label: "2星", value: 2 },
        { label: "3星", value: 3 },
        { label: "4星", value: 4 },
        { label: "5星", value: 5 }
      ],
      allowClear: true,
      placeholder: "请选择评分"
    }
  },
  {
    key: "address",
    label: "地址",
    component: "a-input",
    props: {
      placeholder: "请输入地址",
      allowClear: true
      // style: { width: "200px" }
    }
  },
  {
    key: "tags",
    label: "标签",
    component: "a-select",
    props: {
      mode: "multiple",
      options: [
        { label: "VIP", value: "VIP" },
        { label: "活跃用户", value: "活跃用户" },
        { label: "新用户", value: "新用户" }
      ],
      allowClear: true,
      placeholder: "请选择标签"
      // style: { width: "200px" }
    }
  },
  {
    key: "customEvent",
    label: "自定义事件组件",
    componentInstance: CustomEventComponent,
    props: {
      placeholder: "支持事件的输入框",
      allowClear: true
    },
    events: {
      clear: () => {
        message.info("清空事件触发");
      },
      select: value => {
        message.success(`选择事件触发，值：${value}`);
      }
    }
  }
];

// 自定义按钮配置
const customButtons = [
  // {
  //   key: "settings",
  //   label: "设置",
  //   props: {
  //     type: "default",
  //     icon: h(SettingOutlined)
  //   }
  // },
  // {
  //   key: "favorite",
  //   label: "收藏",
  //   props: {
  //     type: "default",
  //     icon: h(HeartOutlined)
  //   }
  // }
];

// 操作列扩展按钮
const operationButtons = [
  // {
  //   key: "view",
  //   label: "查看",
  //   props: {
  //     type: "link",
  //     icon: h(EyeOutlined)
  //   }
  // },
  // {
  //   key: "star",
  //   label: "标星",
  //   props: {
  //     type: "link",
  //     icon: h(StarOutlined)
  //   }
  // },
  // {
  //   key: "info",
  //   label: "详情",
  //   props: {
  //     type: "link",
  //     icon: h(InfoCircleOutlined)
  //   }
  // },
  // {
  //   key: "download",
  //   label: "下载",
  //   props: {
  //     type: "link",
  //     icon: h(DownloadOutlined)
  //   }
  // },
  // {
  //   key: "share",
  //   label: "分享",
  //   props: {
  //     type: "link",
  //     icon: h(ShareAltOutlined)
  //   }
  // },
  // {
  //   key: "copy",
  //   label: "复制",
  //   props: {
  //     type: "link",
  //     icon: h(CopyOutlined)
  //   }
  // },
  // {
  //   key: "more",
  //   label: "更多",
  //   props: {
  //     type: "link",
  //     icon: h(MoreOutlined)
  //   }
  // }
];

// 状态映射
const statusMap = {
  0: { status: "error", text: "禁用" },
  1: { status: "success", text: "正常" },
  2: { status: "warning", text: "待审核" }
};

// 表格列配置 - 展示所有渲染类型和高级配置
const columns = [
  {
    title: "头像",
    dataIndex: "avatar",
    key: "avatar",
    renderType: "img",
    width: 80,
    align: "center",
    resizable: true
  },
  {
    title: "姓名",
    dataIndex: "name",
    key: "name",
    width: 120,
    sorter: true,
    resizable: true
  },
  {
    title: "年龄",
    dataIndex: "age",
    key: "age",
    width: 80,
    align: "center",
    sorter: true,
    resizable: true,
    render: (age: number) => {
      return age + "岁";
    }
  },
  {
    title: "性别",
    dataIndex: "gender",
    key: "gender",
    renderType: "tag",
    tagColor: "blue",
    width: 80,
    align: "center",
    resizable: true,
    filters: [
      { text: "男", value: "男" },
      { text: "女", value: "女" }
    ]
  },
  {
    title: "注册时间",
    dataIndex: "registerTime",
    key: "registerTime",
    renderType: "time",
    width: 160,
    sorter: true,
    resizable: true
  },
  {
    title: "简介",
    dataIndex: "desc",
    key: "desc",
    renderType: "html",
    width: 200,
    ellipsis: true,
    resizable: true
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
    renderType: "status",
    statusMap,
    width: 100,
    align: "center",
    resizable: true,
    filters: [
      { text: "禁用", value: 0 },
      { text: "正常", value: 1 },
      { text: "待审核", value: 2 }
    ]
  },
  {
    title: "标签",
    dataIndex: "tags",
    key: "tags",
    width: 180,
    resizable: true,
    render: (tags: string[]) => {
      if (!tags || !Array.isArray(tags)) return null;
      return h(
        "div",
        { style: "width: 42px;display: flex; gap: 4px; flex-wrap: wrap;" },
        tags.map(tag => h("a-tag", { color: "blue", size: "small" }, tag))
      );
    }
  },
  {
    title: "评分",
    dataIndex: "rating",
    key: "rating",
    width: 120,
    align: "center",
    resizable: true,
    render: (rating: number) => {
      return h(
        "div",
        { style: "display: flex; align-items: center; gap: 4px;" },
        [
          h("span", { style: "color: #faad14;" }, "★".repeat(rating)),
          h("span", { style: "color: #d9d9d9;" }, "★".repeat(5 - rating)),
          h(
            "span",
            { style: "margin-left: 4px; font-size: 12px; color: #666;" },
            `${rating}/5`
          )
        ]
      );
    }
  }
];

// mock 数据
const allData = Array.from({ length: 50 }).map((_, i) => ({
  id: i + 1,
  avatar: `https://randomuser.me/api/portraits/${i % 2 === 0 ? "men" : "women"}/${(i % 20) + 1}.jpg`,
  name: `用户${i + 1}`,
  age: 18 + (i % 50),
  gender: i % 2 === 0 ? "男" : "女",
  registerTime: Date.now() - i * 86400000 - Math.random() * 86400000,
  desc: `<span style='color:#1890ff'>这是用户${i + 1}的详细简介，包含了一些<span style='color:#52c41a'>重要信息</span>和<span style='color:#faad14'>特殊标记</span>。</span>`,
  status: i % 3,
  tags: i % 2 === 0 ? ["VIP", "活跃用户"] : i % 3 === 0 ? ["新用户"] : ["VIP"],
  rating: (i % 5) + 1,
  email: `user${i + 1}@example.com`,
  phone: `138${String(i + 1).padStart(8, "0")}`,
  address: `北京市朝阳区第${i + 1}街道`,
  department: i % 2 === 0 ? "技术部" : "产品部",
  position: i % 2 === 0 ? "工程师" : "产品经理"
}));

const dataSource = ref([...allData]);
const loading = ref(false);
const selectedRowKeys = ref<any[]>([]);
const selectedRows = ref<any[]>([]);

// 表格组件实例
const tableRef = ref<InstanceType<typeof TrTable>>();

// 分页和排序状态（用于显示）
const pagination = ref<PaginationState>({
  pageIndex: 1,
  pageSize: 10,
  total: allData.length
});
const sort = ref<SortState>({
  field: undefined,
  order: null
});

// 事件处理函数
function handleQuery(form: any) {
  loading.value = true;

  // 模拟异步查询
  setTimeout(() => {
    let filtered = allData;

    if (form.name) {
      filtered = filtered.filter(item => item.name.includes(form.name));
    }
    if (form.gender) {
      filtered = filtered.filter(item => item.gender === form.gender);
    }
    if (form.age) {
      filtered = filtered.filter(item => item.age === form.age);
    }
    if (form.status !== undefined && form.status !== null) {
      filtered = filtered.filter(item => item.status === form.status);
    }
    if (form.registerTime && form.registerTime.length === 2) {
      const [start, end] = form.registerTime;
      filtered = filtered.filter(item => {
        const time = new Date(item.registerTime);
        return time >= start && time <= end;
      });
    }
    if (form.tags && form.tags.length > 0) {
      filtered = filtered.filter(item =>
        form.tags.some((tag: string) => item.tags.includes(tag))
      );
    }
    if (form.email) {
      filtered = filtered.filter(item => item.email.includes(form.email));
    }
    if (form.phone) {
      filtered = filtered.filter(item => item.phone.includes(form.phone));
    }
    if (form.department) {
      filtered = filtered.filter(item => item.department === form.department);
    }
    if (form.position) {
      filtered = filtered.filter(item => item.position === form.position);
    }
    if (form.rating !== undefined && form.rating !== null) {
      filtered = filtered.filter(item => item.rating === form.rating);
    }
    if (form.address) {
      filtered = filtered.filter(item => item.address.includes(form.address));
    }

    dataSource.value = filtered;
    // 查询后，模拟后端返回total
    pagination.value.total = filtered.length;
    loading.value = false;

    message.success(`查询完成，共找到 ${filtered.length} 条记录`);
  }, 500);
}

function handleEdit(record: any) {
  notification.info({
    message: "编辑操作",
    description: `正在编辑用户：${record.name}`,
    placement: "topRight"
  });
}

function handleDelete(record: any) {
  notification.warning({
    message: "删除操作",
    description: `确认删除用户：${record.name}？`,
    placement: "topRight"
  });
}

function handleRowSelectionChange(keys: any[], rows: any[]) {
  selectedRowKeys.value = keys;
  selectedRows.value = rows;
  console.log("选中行变化:", { keys, rows });
}

// 处理查询项组件事件
function handleQueryItemEvent(eventData: {
  key: string;
  eventName: string;
  value: any;
  args: any[];
}) {
  console.log("查询项事件:", eventData);
  // 可以根据需要处理不同的事件
  switch (eventData.eventName) {
    case "clear":
      console.log(`清空了 ${eventData.key} 字段`);
      break;
    case "suggest":
      console.log(`建议事件，字段：${eventData.key}，值：${eventData.value}`);
      break;
    case "press-enter":
      console.log(`回车事件，字段：${eventData.key}，值：${eventData.value}`);
      break;
    default:
      console.log(`未知事件：${eventData.eventName}`);
  }
}

// 获取表格状态
function getTableState() {
  const state = tableRef.value?.getTableState();
  if (state) {
    // 展示完整分页状态（含total）
    pagination.value = { ...pagination.value, ...state.pagination };
    sort.value = { ...sort.value, ...state.sort };
    console.log("当前表格状态", state);
  } else {
    message.warning("无法获取表格状态");
  }
}

// 设置表格状态
function setTableState() {
  // 示例：设置到第2页，每页20条，模拟后端返回total=50
  const newState: TableState = {
    pagination: { pageIndex: 2, pageSize: 20, total: 50 },
    sort: { field: "name", order: "descend" }
  };
  tableRef.value?.setTableState(newState);
  tableRef.value?.setPaginationState({ pageIndex: 2, pageSize: 20, total: 50 });
  pagination.value = { ...pagination.value, ...newState.pagination };
  sort.value = { ...sort.value, ...newState.sort };
}

// 处理列宽变化
function handleResizeColumn(width: number, column: any) {
  console.log("列宽变化:", column.key, width);
  column.width = width;
  // 可以根据需要更新列的宽度或存储
}
</script>

<template>
  <div class="tr-table-demo-page">
    <!-- <div class="demo-header">
      <h2>TrTable 组件完整功能演示</h2>
      <p>展示 TrTable 组件的所有配置项和功能特性</p>
    </div> -->
    <TrTable
      ref="tableRef"
      :columns="columns"
      :dataSource="dataSource"
      rowKey="id"
      :queryItems="queryItems"
      :customButtons="customButtons"
      :operationButtons="operationButtons"
      :loading="loading"
      :pagination="pagination"
      :scroll="{ x: 1000, y: 350 }"
      :resizable="true"
      :showOperation="false"
      paginationMode="client"
      @query="handleQuery"
      @edit="handleEdit"
      @delete="handleDelete"
      @row-selection-change="handleRowSelectionChange"
      @resize-column="handleResizeColumn"
      @query-item-event="handleQueryItemEvent"
    >
      <!-- 查询区扩展插槽 -->
      <!-- <template #queryArea="{ queryForm, pagination, sort }">
        <div class="custom-query-area">
          <a-tag color="green">共 {{ dataSource.length }} 条记录</a-tag>
          <a-tag color="blue">内部状态管理</a-tag>
          <a-tag color="orange">当前页: {{ pagination.pageIndex }}</a-tag>
          <a-tag color="purple"
            >排序: {{ sort.field || "无" }} {{ sort.order || "" }}</a-tag
          >
          <a-tag color="cyan"
            >查询表单字段数: {{ Object.keys(queryForm).length }}</a-tag
          >
        </div>
      </template> -->

      <!-- 按钮区扩展插槽 -->
      <template #buttonArea>
        <a-button type="dashed" size="small" @click="getTableState">
          获取状态
        </a-button>
        <a-button type="dashed" size="small" @click="setTableState">
          设置状态
        </a-button>
        <!-- <a-button
          type="dashed"
          size="small"
          @click="() => tableRef?.resetTableState()"
        >
          重置状态
        </a-button> -->
      </template>

      <!-- 操作列扩展插槽 -->
      <!-- <template #operation="{ record }">
        <a-button
          type="link"
          size="small"
          @click="() => message.info(`自定义操作：${record.name}`)"
        >
          自定义操作
        </a-button>
        <a-button
          type="link"
          size="small"
          @click="() => message.info(`查看详情：${record.name}`)"
        >
          详情
        </a-button>
      </template> -->
    </TrTable>
  </div>
</template>

<style scoped>
.tr-table-demo-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  padding: 16px;
}

.demo-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-header h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 24px;
}

.demo-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.tr-table-demo-page :deep(.tr-table-wrapper) {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tr-table-demo-page :deep(.tr-table-table-section) {
  overflow: auto;
}

/* 优化表格滚动体验 */
.tr-table-demo-page :deep(.tr-table-table) {
  width: 100%;
}

.tr-table-demo-page :deep(.ant-table-thead > tr > th) {
  position: sticky;
  top: 0;
  z-index: 2;
  background: #eff7fd;
}

.tr-table-demo-page :deep(.ant-table-tbody > tr > td) {
  word-break: break-word;
}

/* 确保表格容器正确处理滚动 */
.tr-table-demo-page :deep(.ant-table-container) {
  overflow: auto;
}

/* 修复表头和数据列对齐问题 */
.tr-table-demo-page :deep(.ant-table-thead > tr > th),
.tr-table-demo-page :deep(.ant-table-tbody > tr > td) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 对于需要换行的列，单独处理 */
.tr-table-demo-page :deep(.ant-table-tbody > tr > td[data-col-key*="desc"]) {
  white-space: normal;
  word-break: break-word;
}

/* 确保表格滚动时列对齐 */
.tr-table-demo-page :deep(.ant-table) {
  table-layout: fixed;
}

/* 优化固定列的显示 */
.tr-table-demo-page :deep(.ant-table-cell-fix-left),
.tr-table-demo-page :deep(.ant-table-cell-fix-right) {
  z-index: 1;
}

/* 确保固定列在滚动时正确显示 */
.tr-table-demo-page :deep(.ant-table-cell-fix-left-first) {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.12);
}

.tr-table-demo-page :deep(.ant-table-cell-fix-right-last) {
  box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.12);
}

/* 确保操作列按钮正确显示 */
.tr-table-demo-page :deep(.tr-table-op-btns) {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: flex-start;
  width: 100%;
  max-width: 180px;
}

/* 优化操作列按钮样式 */
.tr-table-demo-page :deep(.tr-table-op-btns .ant-btn) {
  padding: 1px 2px;
  font-size: 10px;
  height: 20px;
  line-height: 1.2;
  width: 45px;
  margin: 0;
  border-radius: 2px;
  flex-shrink: 0;
  text-align: center;
}

/* 确保操作列有足够空间 */
.tr-table-demo-page :deep(.ant-table-cell[data-col-key*="__operation"]) {
  padding: 6px 2px;
  width: 140px;
  min-width: 140px;
}

.custom-query-area {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
}

.demo-footer {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-top: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feature-list h3 {
  margin: 0 0 16px 0;
  color: #1890ff;
  font-size: 18px;
}

.feature-list ul {
  margin: 0;
  padding-left: 20px;
}

.feature-list li {
  margin: 8px 0;
  color: #333;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tr-table-demo-page {
    padding: 8px;
  }

  .demo-header {
    padding: 16px;
  }

  .demo-footer {
    padding: 16px;
  }
}
</style>
