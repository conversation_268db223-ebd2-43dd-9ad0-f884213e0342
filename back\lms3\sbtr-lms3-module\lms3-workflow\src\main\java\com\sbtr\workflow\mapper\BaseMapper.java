package com.sbtr.workflow.mapper;

import com.sbtr.workflow.model.ActForm;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 常用外置表单数据主表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:33:52
 */
public interface BaseMapper {

    HashMap<String, Object> selectByKey(@Param("sql")String sql);
    @MapKey("id")
    List<Map> selectBysql(@Param("sql")String sql);

    String selectStr(String sql);
}
