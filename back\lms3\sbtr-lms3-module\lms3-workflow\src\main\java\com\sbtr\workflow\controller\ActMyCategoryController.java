package com.sbtr.workflow.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.model.ActMyCategory;
import com.sbtr.workflow.service.ActMyCategoryService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.StringUtil.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Valid;
import java.util.List;

/**
 * 流程分类表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-12 06:00:42
 */
@Api(tags = {"流程分类" })
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/flowCategory")
public class ActMyCategoryController extends WorkflowBaseController {

    @Autowired
    private ActMyCategoryService workflowCategoryService;


    //系统默认分类标识不允许删除修改
    public static final String CATEGORY_CODE = "";
//    public static final String CATEGORY_CODE = "formWok,hr,cw,wzbd,form,project";



    @ResponseBody
    @RequestMapping(value = "/getPageSet", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam, String categoryName) {
        PageSet<ActMyCategory> pageSet = workflowCategoryService.getPageSet(pageParam, categoryName);
        return pageSet;
    }

    @ApiOperation(value = "获得WorkflowCategory模块详细数据")
    @ApiImplicitParam(name = "uuid", value = "获得WorkflowCategory模块详细数据", required = true, dataType = "String")
    @ResponseBody
    @RequestMapping(value = "/getDetailByUuid")
    public Object getDetailByUuid(String uuid) {
        ActMyCategory workflowCategory = workflowCategoryService.selectByPrimaryKey(uuid);
        return workflowCategory;
    }


    @Log(title = "流程分类-保存WorkflowCategory模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Object save(@Valid @ModelAttribute ActMyCategory workflowCategory){
        if (StrUtil.isBlank(workflowCategory.getCategoryCode())) {
            return failure("必要参数不能为空！！！");
        }
        Boolean bol = StringUtils.isAlphanumeric(workflowCategory.getCategoryCode());
        if (!bol) {
            return failure("标识只能包含数字或字母，且长度要在1-50位之间");
        }
        setLogMessage(workflowCategory.getCategoryName(),"");
        Example example = new Example(ActMyCategory.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("categoryCode", workflowCategory.getCategoryCode());
        List<ActMyCategory> lists = workflowCategoryService.selectByExample(example);
        if (lists.size() > 0) {
            return failure("标识已存在！！！");
        }
        int result = workflowCategoryService.insertSelective(getSaveData(workflowCategory));
        return result > 0 ? success("保存成功！") : failure("保存失败！");
    }


    @Log(title = "流程分类-更新WorkflowCategory模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.UPDATE, method = "getLogMessage")
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Object update(@ModelAttribute ActMyCategory workflowCategory) {
        if (StrUtil.isAllBlank(workflowCategory.getCategoryCode(), workflowCategory.getUuid())) {
            return failure("必要参数不能为空！！！");
        }
        //系统默认标识不允许修改
        ActMyCategory actMyCategory = workflowCategoryService.selectByPrimaryKey(workflowCategory.getUuid());
        if (!workflowCategory.getCategoryCode().equals(actMyCategory.getCategoryCode())   && CATEGORY_CODE.contains(workflowCategory.getCategoryCode()) ) {
            return failure("该条数据标识不予许修改！！！");
        }

        Boolean bol = StringUtils.isAlphanumeric(workflowCategory.getCategoryCode());
        if (!bol) {
            return failure("标识只能包含数字或字母，且长度要在1-50位之间");
        }

        Boolean isCodeExist = workflowCategoryService.getListByCodeAndUuid(workflowCategory.getCategoryCode(), workflowCategory.getUuid());
        if (isCodeExist) {
            return failure("更新失败！标识已存在");
        }
        setLogMessage(workflowCategory.getCategoryName(),"");
        int result = workflowCategoryService.updateByPrimaryKeySelective(getUpdateData(workflowCategory));
        return result > 0 ? success("更新成功！") : failure("更新失败！");
    }

    @Log(title = "流程分类-删除WorkflowCategory模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ResponseBody
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.POST)
    public Object deleteBatch(@RequestParam String[] uuid) {
        if (ArrayUtil.isEmpty(uuid)) {
            return failure("必要参数uuid不能为空！！！");
        }
        //自带按钮标识不能删除
        for (int i = 0; i < uuid.length; i++) {
            ActMyCategory actMyCategory = workflowCategoryService.selectByPrimaryKey(uuid[i]);
            if (ObjectUtil.isNotEmpty(actMyCategory) && CATEGORY_CODE.contains(actMyCategory.getCategoryCode())) {
                return failure("该条数据标识不予许删除！！！");
            }
        }
        if(uuid!=null&&uuid.length>0){
            StringBuilder logStr=new StringBuilder();
            for (String s : uuid) {
                ActMyCategory actMyNodeButton = workflowCategoryService.selectByPrimaryKey(s);
                if(actMyNodeButton!=null)
                    logStr.append(actMyNodeButton.getCategoryName()+",");
            }
            setLogMessage(logStr.toString(),"");
        }
        int result = workflowCategoryService.executeDeleteBatch(uuid);
        return result > 0 ? success("删除成功！") : failure("删除失败！");
    }

    @ResponseBody
    @RequestMapping(value = "/getAllData", method = RequestMethod.POST)
    public Object getAllData() {
        List<ActMyCategory> list = workflowCategoryService.selectAll();
        return list;
    }
}
