package com.sbtr.workflow.core;

//import com.alibaba.cloud.commons.lang.StringUtils;
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.ExtensionAttribute;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.editor.language.json.converter.UserTaskJsonConverter;

import java.util.Map;


/**
 * 自定义usertask
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
public class CustomUserTaskJsonConverter extends UserTaskJsonConverter {

    @Override
    protected FlowElement convertJsonToElement(JsonNode elementNode, JsonNode modelNode,
                                               Map<String, JsonNode> shapeMap) {
        UserTask flowElement = (UserTask) super.convertJsonToElement(elementNode, modelNode, shapeMap);

        LOGGER.info("进入自定义属性解析");
        if(flowElement instanceof UserTask){

            String customSetting = getPropertyValueAsString("customSetting",elementNode);
            if(StringUtils.isNotBlank(customSetting)){
                LOGGER.info("新增自定义属性[customSetting]="+customSetting);
                ExtensionAttribute ea1 = generateExtensionAttribute("customSetting",customSetting);
                flowElement.addAttribute(ea1);
            }
//            addExtensionElement("customSetting", customSetting, flowElement);
        }
        return flowElement;
    }

    public static ExtensionAttribute generateExtensionAttribute(String key, String val){
        ExtensionAttribute ea = new ExtensionAttribute();
        ea.setNamespace("http://flowable.org/bpmn");
        ea.setName(key);
        ea.setNamespacePrefix("custom");
        ea.setValue(val);
        return ea;
    }
}
