/**
 * TrPreview组件类型定义
 */

/**
 * 文件类型枚举
 */
export enum FileType {
  IMAGE = "image",
  VIDEO = "video",
  MODEL_3D = "model3d",
  OTHER = "other"
}

/**
 * 文件信息接口
 */
export interface FileInfo {
  /** 文件名称 */
  name: string;
  /** 文件URL */
  url: string;
  /** 预览URL（可选） */
  previewUrl?: string;
  /** 文件类型（可选，不提供则自动识别） */
  type?: FileType;
  /** 文件大小（可选） */
  size?: number;
  /** 文件扩展名（可选） */
  extension?: string;
}

/**
 * 组件Props接口
 */
export interface TrPreviewProps {
  /** 文件信息 */
  file: FileInfo;
  /** 容器宽度 */
  width?: string | number;
  /** 容器高度 */
  height?: string | number;
  /** 是否显示文件类型徽章 */
  showTypeBadge?: boolean;
  /** 是否显示下载按钮（仅对非3D模型文件有效） */
  showDownloadButton?: boolean;
  /** 是否显示编辑按钮（仅对3D模型文件有效） */
  showEditButton?: boolean;
  /** 是否显示全屏预览按钮（仅对3D模型文件有效） */
  showFullscreenButton?: boolean;
  /** 自定义样式类名 */
  class?: string;
  /** 是否禁用交互 */
  disabled?: boolean;
  /** 3D模型iframe源地址 */
  modelIframeSrc?: string;
  /** 3D模型编辑器地址 */
  modelEditorUrl?: string;
  /** 默认图片地址 */
  defaultImageUrl?: string;
}

/**
 * 组件事件类型
 */
export interface TrPreviewEmits {
  /** 文件加载完成 */
  (e: "load", file: FileInfo): void;
  /** 文件加载错误 */
  (e: "error", error: Error, file: FileInfo): void;
  /** 下载文件 */
  (e: "download", file: FileInfo): void;
  /** 编辑3D模型 */
  (e: "edit", file: FileInfo): void;
  /** 全屏预览3D模型 */
  (e: "fullscreen", file: FileInfo): void;
  /** 重新加载 */
  (e: "retry", file: FileInfo): void;
}

/**
 * 文件类型配置
 */
export interface FileTypeConfig {
  /** 文件扩展名 */
  extensions: string[];
  /** MIME类型前缀 */
  mimeTypes: string[];
  /** 显示名称 */
  displayName: string;
  /** 图标组件名称 */
  icon: string;
  /** 徽章颜色 */
  badgeColor: string;
}
