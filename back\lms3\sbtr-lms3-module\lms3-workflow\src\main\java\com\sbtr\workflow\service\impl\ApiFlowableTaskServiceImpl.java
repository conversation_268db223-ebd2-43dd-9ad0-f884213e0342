package com.sbtr.workflow.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.lms.common.util.ContextUtil;
import com.sbtr.workflow.constants.DriverClassName;
import com.sbtr.workflow.exception.SystemException;
import com.sbtr.workflow.model.*;
import com.sbtr.workflow.properties.Druid;
import com.sbtr.workflow.utils.BaseUtils;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.client.MessageClient;
import com.sbtr.workflow.dto.CommentBean;
import com.sbtr.workflow.dto.CommonTaskDto;
import com.sbtr.workflow.dto.ProcessDefinitionDto;
import com.sbtr.workflow.dto.StartedDto;
import com.sbtr.workflow.enums.FlowEnum;
import com.sbtr.workflow.enums.FlowConstant;
import com.sbtr.workflow.mapper.ApiFlowableModelMapper;
import com.sbtr.workflow.mapper.ApiFlowableTaskMapper;
import com.sbtr.workflow.service.*;
import com.sbtr.workflow.utils.*;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.PageUtil.PageUtils;
import com.sbtr.workflow.utils.StringUtil.StringUtils;
import com.sbtr.workflow.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.model.*;
import org.flowable.bpmn.model.Process;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.impl.persistence.entity.ActivityInstanceEntity;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.idm.api.User;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


@Service("apiFlowableTaskServiceImpl")
public class ApiFlowableTaskServiceImpl extends BaseServiceImpl implements ApiFlowableTaskService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiFlowableTaskServiceImpl.class);

    @Autowired
    protected ManagementService managementService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    protected org.flowable.engine.TaskService taskService;

    @Autowired
    private ApiFlowableBpmnModelService apiFlowableBpmnModelService;

    @Autowired
    private ApiFlowableTaskMapper apiFlowableTaskMapper;

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private HistoryService historyService;

    @Autowired
    protected IdentityService identityService;

    @Autowired
    private ActMyNodeButtonService flowNodeButtonService;

    @Autowired
    private ActMyNodeFieldService flowNodeFieldService;

    @Autowired
    private ActMyNodeNoticeService flowNodeNoticeService;
    @Autowired
    private ActMyNodeCodeService flowNodeCodeService;

    @Autowired
    private ApiRunFlowActinstService apiRunFlowActinstService;

    @Autowired
    private ApiHisFlowableActinstService apiHisFlowableActinstService;

    @Autowired
    private ActMyModelService flowModelService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private ApiFlowableProcessInstanceService apiFlowableProcessInstanceService;

    @Autowired
    private ActMyFlowAgentService actMyFlowAgentService;


    @Autowired
    private Druid druid;

    /**
     * 创建子任务
     *
     * @param ptask    创建子任务
     * @param assignee 子任务的执行人
     * @return
     */
    protected TaskEntity createSubTask(TaskEntity ptask, String ptaskId, String assignee) {
        TaskEntity task = null;
        if (ptask != null) {
            //1.生成子任务
            task = (TaskEntity) taskService.newTask(BaseUtils.UUIDGenerator());
            task.setCategory(ptask.getCategory());
            task.setDescription(ptask.getDescription());
            task.setTenantId(ptask.getTenantId());
            task.setAssignee(assignee);
            task.setName(ptask.getName());
            task.setParentTaskId(ptaskId);
            task.setProcessDefinitionId(ptask.getProcessDefinitionId());
            task.setProcessInstanceId(ptask.getProcessInstanceId());
            task.setTaskDefinitionKey(ptask.getTaskDefinitionKey());
            task.setTaskDefinitionId(ptask.getTaskDefinitionId());
            task.setPriority(ptask.getPriority());
            task.setCreateTime(new Date());
            taskService.saveTask(task);
        }
        return task;
    }

    @Autowired
    private ActMyProcessCopyService actMyProcessCopyService;

    @Transactional
    @Override
    public Object agree(String processInstanceId, String taskId, String message, String processDefinitionId, Map<String, Object> map) {
        try {
            if (!StrUtil.hasBlank(processInstanceId, taskId, processDefinitionId)) {
                //查看当前任务是存在
                TaskEntity taskEntity = (TaskEntity) taskService.createTaskQuery().taskId(taskId).singleResult();
                if (ObjectUtil.isNotNull(taskEntity)) {
                    String type = Convert.toStr(FlowEnum.SP);
                    if (2 == taskEntity.getSuspensionState()) {
                        return failure("此流程已被挂起,请联系管理员去激活！！！");
                    }
                    // 流程processInstance
                    ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();

                    //设置流程连线中需要的变量
                    List varNames = workflowService.getVarsNameByProcessDefinitionId(processDefinitionId);
                    Map<String, Object> params = new HashMap<>();
                    for (int i = 0; i < varNames.size(); i++) {
                        String varName = Convert.toStr(varNames.get(i));
                        params.put(varName, map.get(varName));
                    }

                    if ("驳回".equals(map.get("outcome"))) {
                        Authentication.setAuthenticatedUserId(businessSystemDataService.getUserNameId());
                        taskService.addComment(taskId, taskEntity.getProcessInstanceId(), Convert.toStr(FlowEnum.BH), message);
                        taskService.setAssignee(taskId, businessSystemDataService.getUserNameId());
                        taskService.complete(taskId, params);
                        //下一步处理人
                        if (!map.isEmpty() && null != map.get("assignUser") && StrUtil.isNotBlank(Convert.toStr(map.get("assignUser")))) {
                            Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
                            LOGGER.info("下一步任务节点数据=" + task);
                            if (ObjectUtil.isNotNull(task)) {
                                String assignUserStr[] = Convert.toStr(map.get("assignUser")).split(",");
                                if (1 == assignUserStr.length) {
                                    taskService.setAssignee(task.getId(), assignUserStr[0]);
                                }
                                if (assignUserStr.length > 1) {
                                    //删除之前的候选人
                                    List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
                                    for (IdentityLink identityLink : identityLinks) {
                                        if (StrUtil.isNotBlank(identityLink.getGroupId())) {
                                            taskService.deleteCandidateGroup(task.getId(), identityLink.getGroupId());
                                        }
                                    }
                                    //添加新的候选人
                                    for (int i = 0; i < assignUserStr.length; i++) {
                                        taskService.addCandidateUser(task.getId(), assignUserStr[i]);
                                    }
                                }
                            }
                        }
                    } else {
                        //2.委派处理
                        if (DelegationState.PENDING.equals(taskEntity.getDelegationState())) {
                            //2.1生成历史记录
                            TaskEntity task = createSubTask(taskEntity, taskEntity.getId(), "");
                            taskService.complete(task.getId());
                            //2.2执行委派
                            taskService.resolveTask(taskId, params);
//                            type = Convert.toStr(FlowEnum.WP);
                        } else {
                            //3.1修改执行人 其实我这里就相当于签收了
                            taskService.setAssignee(taskId, businessSystemDataService.getUserNameId());
                            params.put("departmentHeadId", businessSystemDataService.getleaderIdByUserNameId(processInstance.getStartUserId()));
                            params.put("reportsToId", businessSystemDataService.getReportsToByUserNameId(processInstance.getStartUserId()));
                            //7.抄送
                            if (!map.isEmpty() && null != map.get("duplicateUser") && StrUtil.isNotBlank(Convert.toStr(map.get("duplicateUser")))) {
                                if (ObjectUtil.isNotNull(processInstance)) {
                                    Task tasks = taskService.createTaskQuery().processInstanceId(processInstance.getProcessInstanceId()).singleResult();

                                    List<String> list = Arrays.asList(Convert.toStr(map.get("duplicateUser")).split(","));
                                    for (int i = 0; i < list.size(); i++) {
                                        ActMyProcessCopy actMyProcessCopy = new ActMyProcessCopy();
                                        actMyProcessCopy.setUserNameId(list.get(i));
                                        actMyProcessCopy.setProcessDefinitionId(processInstance.getProcessInstanceId());
                                        actMyProcessCopy.setProcessInstanceId(processInstance.getProcessInstanceId());
                                        actMyProcessCopy.setFormName(processInstance.getName());
                                        actMyProcessCopy.setTaskName(tasks.getName());
                                        actMyProcessCopy.setTaskId(tasks.getId());
                                        actMyProcessCopy.setNodeId(tasks.getTaskDefinitionKey());
                                        actMyProcessCopy.setBusinessKey(processInstance.getBusinessKey());
                                        actMyProcessCopy.setModelKey(processInstance.getProcessDefinitionKey());
                                        actMyProcessCopy.setStartUserId(processInstance.getStartUserId());
                                        actMyProcessCopy.setReviewStatus("review_status.01");
                                        actMyProcessCopyService.insertSelective(getSaveData(actMyProcessCopy));
                                    }
                                }
                            }
                            //写入表单变量
                            params.putAll(map);
                            taskService.claim(taskId, businessSystemDataService.getUserNameId());
                            //执行任务
                            taskService.complete(taskId, params);
                            //4.处理加签父任务
                            String parentTaskId = taskEntity.getParentTaskId();
                            if (StringUtils.isNotBlank(parentTaskId)) {
                                String tableName = managementService.getTableName(TaskEntity.class);
                                String sql = "select count(1) from " + tableName + " where PARENT_TASK_ID_=#{parentTaskId}";
                                long subTaskCount = taskService.createNativeTaskQuery().sql(sql).parameter("parentTaskId", parentTaskId).count();
                                if (subTaskCount == 0) {
                                    Task task = taskService.createTaskQuery().taskId(parentTaskId).singleResult();
                                    //处理前后加签的任务
                                    taskService.resolveTask(parentTaskId);
                                    if ("after".equals(task.getScopeType())) {
                                        taskService.complete(parentTaskId);
                                    }
                                }
                            }
                        }
                        //5.生成审批意见
                        managementService.executeCommand(new AddHisCommentCmd(taskId, businessSystemDataService.getUserNameId(), processInstanceId, type
                                , message));

                        //6.下一步处理人
                        if (!map.isEmpty() && null != map.get("assignUser") && StringUtils.isNotBlank(Convert.toStr(map.get("assignUser")))) {
                            List<String> activeActivityIds = runtimeService.getActiveActivityIds(processInstanceId);
                            if (activeActivityIds.size() > 1) {
                                throw new RuntimeException("流程下一环节为多实例任务节点，不支持指定任务处理人！！！");
                            }
                            Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
                            if (ObjectUtil.isNotEmpty(task)) {
                                String[] assignUserStr = Convert.toStr(map.get("assignUser")).split(",");
                                if (ArrayUtil.isNotEmpty(assignUserStr)) {
                                    if (1 == assignUserStr.length) {
                                        taskService.setAssignee(task.getId(), assignUserStr[0]);
                                    }
                                    if (assignUserStr.length > 1) {
                                        //删除之前的候选人
                                        List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
                                        for (IdentityLink identityLink : identityLinks) {
                                            if (StrUtil.isNotBlank(identityLink.getGroupId())) {
                                                taskService.deleteCandidateGroup(task.getId(), identityLink.getGroupId());
                                            }
                                        }
                                        //添加新的候选人
                                        for (int i = 0; i < assignUserStr.length; i++) {
                                            taskService.addCandidateUser(task.getId(), assignUserStr[i]);
                                        }
                                    }
                                }
                            }

                        }
                    }
                } else {
                    return failure("没有此任务,请确认或刷新页面！！！");
                }
            } else {
                return failure("请输入正确的参数或刷新下页面！！！");
            }
            return success("流程处理成功！！！");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            String msg = StringUtils.hasChineseCharacter(e.getMessage())?"流程办理失败!原因是" + e.getMessage():"流程办理失败，请联系管理员！！！";
            LOGGER.error(msg);
            return failure(msg);
        }
    }


    @Transactional
    @Override
    public Object reject(String processInstanceId, String taskId, String message, String distFlowElementId, Map<String, Object> map) {
        try {
            if (StringUtils.isBlank(processInstanceId) || StringUtils.isBlank(taskId) || StringUtils.isBlank(message) || StringUtils.isBlank(distFlowElementId)) {
                return failure("processInstanceId/taskId/message/distFlowElementId不能为空");
            }
            TaskEntity taskEntity = (TaskEntity) taskService.createTaskQuery().taskId(taskId).singleResult();
            //1.把当前的节点设置为空
            if (taskEntity != null) {
                //2.设置审批人
                taskEntity.setAssignee(businessSystemDataService.getUserNameId());
                taskEntity.setPriority(Integer.valueOf(Convert.toStr(map.get("priority"))));
                taskService.saveTask(taskEntity);
                //3.添加驳回意见
                managementService.executeCommand(new AddHisCommentCmd(taskId, businessSystemDataService.getUserNameId(), processInstanceId,
                        Convert.toStr(FlowEnum.BH), message));
                //4.处理提交人节点
                FlowNode distActivity = apiFlowableBpmnModelService.findFlowNodeByActivityId(taskEntity.getProcessDefinitionId(), distFlowElementId);
                if (distActivity != null) {
                    if ("发起人".equals(distActivity.getName())) {
                        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(taskEntity.getProcessInstanceId()).singleResult();
                        runtimeService.setVariable(processInstanceId, "发起人", processInstance.getStartUserId());
                    }
                }
                //5.删除节点
                //  deleteActivity(distFlowElementId, taskEntity.getProcessInstanceId());
                //6.判断节点是不是子流程内部的节点
                if (apiFlowableBpmnModelService.checkActivitySubprocessByActivityId(taskEntity.getProcessDefinitionId(),
                        distFlowElementId)
                        && apiFlowableBpmnModelService.checkActivitySubprocessByActivityId(taskEntity.getProcessDefinitionId(),
                        taskEntity.getTaskDefinitionKey())) {
                    //6.1 子流程内部驳回
                    Execution executionTask = runtimeService.createExecutionQuery().executionId(taskEntity.getExecutionId()).singleResult();
                    String parentId = executionTask.getParentId();
//                    List<Execution> executions = runtimeService.createExecutionQuery().parentId(parentId).list();
//                    executions.forEach(execution -> executionIds.add(execution.getId()));
                    this.moveExecutionsToSingleActivityId(parentId, distFlowElementId);
                } else {
                    //6.2 普通驳回
                    Map<String, Object> params = new HashMap<>();
                    params.put("outcome", "驳回");
                    params.put("priority", Integer.valueOf(Convert.toStr(map.get("priority"))));
//                    taskService.complete(taskId, params);
//                    List<Execution> executions = runtimeService.createExecutionQuery().parentId(taskEntity.getProcessInstanceId()).list();
//                    executions.forEach(execution -> executionIds.add(execution.getId()));
                    this.moveExecutionsToSingleActivityId(taskEntity.getProcessInstanceId(), distFlowElementId);
                }
                return success("驳回成功!");
            } else {
                return failure("不存在任务实例,请确认");
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure("驳回失败,原因为===" + e.getMessage());
        }

    }

    /**
     * runFlowableActinstDao
     * 删除跳转的历史节点信息
     *
     * @param disActivityId     跳转的节点id
     * @param processInstanceId 流程实例id
     */
    protected void deleteActivity(String disActivityId, String processInstanceId) {
        String tableName = managementService.getTableName(ActivityInstanceEntity.class);
        String sql = "select t.* from " + tableName + " t where t.PROC_INST_ID_=#{processInstanceId} and t.ACT_ID_ = #{disActivityId} " +
                " order by t.END_TIME_ ASC";
        List<ActivityInstance> disActivities = runtimeService.createNativeActivityInstanceQuery().sql(sql)
                .parameter("processInstanceId", processInstanceId)
                .parameter("disActivityId", disActivityId).list();
        //删除运行时和历史节点信息
        if (org.flowable.editor.language.json.converter.util.CollectionUtils.isNotEmpty(disActivities)) {
            ActivityInstance activityInstance = disActivities.get(0);
            sql = "select t.* from " + tableName + " t where t.PROC_INST_ID_=#{processInstanceId} and (t.END_TIME_ >= #{endTime} or t.END_TIME_ is null)";
            List<ActivityInstance> datas = runtimeService.createNativeActivityInstanceQuery().sql(sql).parameter("processInstanceId", processInstanceId)
                    .parameter("endTime", activityInstance.getEndTime()).list();
            List<String> runActivityIds = new ArrayList<>();
            if (org.flowable.editor.language.json.converter.util.CollectionUtils.isNotEmpty(datas)) {
                datas.forEach(ai -> runActivityIds.add(ai.getId()));
                apiRunFlowActinstService.deleteRunActinstsByIds(runActivityIds);
                apiHisFlowableActinstService.deleteHisActinstsByIds(runActivityIds);
            }
        }
    }

    /**
     * 实例跳转至任意节点
     */
    protected void moveExecutionsToSingleActivityId(String processInstanceId,String activityId) {
        List<Execution> executions = runtimeService.createExecutionQuery().parentId(processInstanceId).list();
        List<String> executionIds = new ArrayList<>();
        executions.forEach(execution -> executionIds.add(execution.getId()));
//        this.moveExecutionsToSingleActivityId(executionIds, distFlowElementId);
        runtimeService.createChangeActivityStateBuilder()
                .moveExecutionsToSingleActivityId(executionIds, activityId)
                .changeState();
    }
    @Override
    public PageSet<TaskVo> getAgentTasks(PageParam pageParam, String userNameId, String formName, String modelName, String processModelType, String startTime) {
        String startDate = "";
        String endDate = "";
        if (StrUtil.isNotBlank(startTime)) {
            startDate = startTime.split(",")[0];
            endDate = startTime.split(",")[1];
        }
        List<TaskVo> list = new ArrayList<>();
        String currentDateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // 获取当前日期用户userNameId可以代理的委托人信息
        List<String> mandators = actMyFlowAgentService.getMandatorsByAgent(userNameId,currentDateTime);
        if(mandators.size()>0){
            //获取所有可代理委托人的角色和岗位9Id
            List<String> groupIds = businessSystemDataService.getUserPositions(String.join(",",mandators));
            groupIds.addAll(businessSystemDataService.getUserRoles(String.join(",",mandators)));
            PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
            //获取所有可代理委托人的待办列表
            if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
                list = apiFlowableTaskMapper.getAgentTasksOracle("","",mandators, formName, modelName, processModelType, startTime, startDate, endDate,
                        groupIds
                );
            } else if (DriverClassName.MYSQL_NAME.equals(driverClassName)) {
                list = apiFlowableTaskMapper.getAgentTasksMysql("","",mandators, formName, modelName, processModelType, startTime, startDate, endDate,
                        groupIds
                );
            }
        }
        PageInfo<TaskVo> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public TaskVo getAgentTasks(String taskId) {
        String userNameId = businessSystemDataService.getUserNameId();
        String currentDateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        List<String> mandators = actMyFlowAgentService.getMandatorsByAgent(userNameId,currentDateTime);
        List<String> groupIds = businessSystemDataService.getUserPositions(String.join(",",mandators));
        groupIds.addAll(businessSystemDataService.getUserRoles(String.join(",",mandators)));
        List<TaskVo> list = new ArrayList<>();
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getAgentTasksOracle(taskId,"",mandators, "", "", "", "", "","",
                    groupIds);
        } else if (DriverClassName.MYSQL_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getAgentTasksMysql(taskId,"",mandators, "", "","", "", "","",
                    groupIds);
        }
        return list.size()>0?list.get(0):null;
    }

    @Override
    public Object agent(String taskId,String processInstanceId,String userNameId) {
        String comment = "转交"+businessSystemDataService.getUserName()+"处理";
        return transfer(taskId, comment, processInstanceId, userNameId, businessSystemDataService.getUserNameId());
    }

    @Override
    public HashMap<String, HashMap<String, TaskVo>> getTaskInfoByBusinessKeyAndModelKey(String[] businessKeys, String modelKey) {
        HashMap<String, HashMap<String, TaskVo>> result = new HashMap<>();

        // 1. 获取待办任务数据
        String userNameId = businessSystemDataService.getUserNameId();
        List<String> groupIds = businessSystemDataService.getUserPositions();
        if (CollectionUtils.isEmpty(groupIds)){
            groupIds = new ArrayList<>();
        }
        groupIds.addAll(businessSystemDataService.getUserRoles());
        List<TaskVo> todoTaskList = new ArrayList<>();
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            todoTaskList = apiFlowableTaskMapper.getToDoTaskBybusinessKeysAndModelKeyOracle(Arrays.asList(businessKeys), userNameId, groupIds, modelKey);
        } else if (DriverClassName.MYSQL_NAME.equals(driverClassName)) {
            todoTaskList = apiFlowableTaskMapper.getToDoTaskBybusinessKeysAndModelKeyMySql(Arrays.asList(businessKeys), userNameId, groupIds, modelKey);
        }

        // 2. 获取已办任务数据
        List<TaskVo> applyedTaskList = new ArrayList<>();
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            applyedTaskList = apiFlowableTaskMapper.getApplyedTaskBybusinessKeysAndModelKeyOracle(Arrays.asList(businessKeys), userNameId, modelKey);
        } else {
            applyedTaskList = apiFlowableTaskMapper.getApplyedTaskBybusinessKeysAndModelKeyMySql(Arrays.asList(businessKeys), userNameId, modelKey);
        }

        // 3. 组装返回数据
        Map<String, TaskVo> todoTaskMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(todoTaskList)){
            todoTaskMap = todoTaskList.stream().collect(Collectors.toMap(TaskVo::getBusinessKey, t -> t, (t1, t2) -> t1));
        }
        Map<String, TaskVo> applyedTaskMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(applyedTaskList)){
            applyedTaskMap = applyedTaskList.stream().collect(Collectors.toMap(TaskVo::getBusinessKey, t -> t, (t1, t2) -> t1));
        }
        for (String businessKey : businessKeys) {
            TaskVo todoTaskVo = todoTaskMap.get(businessKey);
            TaskVo applyedTaskVo = applyedTaskMap.get(businessKey);
            HashMap<String, TaskVo> map = new HashMap<>();
            map.put("todotaskinfo", todoTaskVo);
            map.put("applyedtaskinfo", applyedTaskVo);
            result.put(businessKey, map);
        }
        return result;
    }

    @Override
    public HashMap<String, HashMap<String, TaskVo>> getTaskInfoByBusinessKey(String[] businessKeys) {
        HashMap<String, HashMap<String, TaskVo>> result = new HashMap<>();

        // 1. 获取待办任务数据
        String userNameId = businessSystemDataService.getUserNameId();
        List<String> groupIds = businessSystemDataService.getUserPositions();
        if (CollectionUtils.isEmpty(groupIds)){
            groupIds = new ArrayList<>();
        }
        groupIds.addAll(businessSystemDataService.getUserRoles());
        List<TaskVo> todoTaskList = new ArrayList<>();
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            todoTaskList = apiFlowableTaskMapper.getToDoTaskBybusinessKeysOracle(Arrays.asList(businessKeys), userNameId, groupIds);
        } else if (DriverClassName.MYSQL_NAME.equals(driverClassName)) {
            todoTaskList = apiFlowableTaskMapper.getToDoTaskBybusinessKeysMySql(Arrays.asList(businessKeys), userNameId, groupIds);
        }

        // 2. 获取已办任务数据
        List<TaskVo> applyedTaskList = new ArrayList<>();
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            applyedTaskList = apiFlowableTaskMapper.getApplyedTaskBybusinessKeysOracle(Arrays.asList(businessKeys), userNameId);
        } else {
            applyedTaskList = apiFlowableTaskMapper.getApplyedTaskBybusinessKeysMySql(Arrays.asList(businessKeys), userNameId);
        }

        // 3. 组装返回数据
        Map<String, TaskVo> todoTaskMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(todoTaskList)){
            todoTaskMap = todoTaskList.stream().collect(Collectors.toMap(TaskVo::getBusinessKey, t -> t, (t1, t2) -> t1));
        }
        Map<String, TaskVo> applyedTaskMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(applyedTaskList)){
            applyedTaskMap = applyedTaskList.stream().collect(Collectors.toMap(TaskVo::getBusinessKey, t -> t, (t1, t2) -> t1));
        }
        for (String businessKey : businessKeys) {
            TaskVo todoTaskVo = todoTaskMap.get(businessKey);
            TaskVo applyedTaskVo = applyedTaskMap.get(businessKey);
            HashMap<String, TaskVo> map = new HashMap<>();
            map.put("todotaskinfo", todoTaskVo);
            map.put("applyedtaskinfo", applyedTaskVo);
            result.put(businessKey, map);
        }
        return result;
    }

    @Override
    public HashMap<String, HashMap<String, TaskVo>> getTaskInfoByModelKey(String modelKey) {
        HashMap<String, HashMap<String, TaskVo>> result = new HashMap<>();

        // 1. 获取待办任务数据
        String userNameId = businessSystemDataService.getUserNameId();
        List<String> groupIds = businessSystemDataService.getUserPositions();
        if (CollectionUtils.isEmpty(groupIds)){
            groupIds = new ArrayList<>();
        }
        groupIds.addAll(businessSystemDataService.getUserRoles());
        List<TaskVo> todoTaskList = new ArrayList<>();
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            todoTaskList = apiFlowableTaskMapper.getToDoTaskByModelKeyOracle(modelKey, userNameId, groupIds, ContextUtil.getSlevel());
        } else if (DriverClassName.MYSQL_NAME.equals(driverClassName)) {
            todoTaskList = apiFlowableTaskMapper.getToDoTaskByModelKeyMySql(modelKey, userNameId, groupIds, ContextUtil.getSlevel());
        }

        // 2. 获取已办任务数据
        List<TaskVo> applyedTaskList = new ArrayList<>();
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            applyedTaskList = apiFlowableTaskMapper.getApplyedTaskByModelKeyOracle(modelKey, userNameId);
        } else {
            applyedTaskList = apiFlowableTaskMapper.getApplyedTaskByModelKeyMySql(modelKey, userNameId);
        }

        // 3. 组装返回数据
        Map<String, TaskVo> todoTaskMap = new HashMap<>();
        List<TaskVo> taskVoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(todoTaskList)){
            taskVoList.addAll(todoTaskList);
            todoTaskMap = todoTaskList.stream().collect(Collectors.toMap(TaskVo::getBusinessKey, t -> t, (t1, t2) -> t1));
        }
        Map<String, TaskVo> applyedTaskMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(applyedTaskList)){
            taskVoList.addAll(applyedTaskList);
            applyedTaskMap = applyedTaskList.stream().collect(Collectors.toMap(TaskVo::getBusinessKey, t -> t, (t1, t2) -> t1));
        }
        Set<String> businessKeys = new HashSet<>();
        taskVoList.forEach(t -> businessKeys.add(t.getBusinessKey()));
        for (String businessKey : businessKeys) {
            if(!StringUtils.isNullOrEmpty(businessKey)){
                TaskVo todoTaskVo = todoTaskMap.get(businessKey);
                TaskVo applyedTaskVo = applyedTaskMap.get(businessKey);
                HashMap<String, TaskVo> map = new HashMap<>();
                map.put("todotaskinfo", todoTaskVo);
                map.put("applyedtaskinfo", applyedTaskVo);
                result.put(businessKey, map);
            }
        }
        return result;

    }

    @Override
    public void updateTaskOpeningTimeByKey(String taskId, Date date) {
        apiFlowableTaskMapper.updateTaskOpeningTimeByKey(taskId,date);
    }

    @Override
    public void updateMlevelByBusinessKey(String businessUuid, Integer slevel) {
        apiFlowableTaskMapper.updateMlevelByBusinessKey(businessUuid, slevel);
    }


    @Override
    public PageSet<TaskVo> getToDoTasks(PageParam pageParam, String userNameId, String formName, String modelName, String processModelType, String startTime) {
        String startDate = "";
        String endDate = "";
        if (StrUtil.isNotBlank(startTime)) {
            startDate = startTime.split(",")[0];
            endDate = startTime.split(",")[1];
        }
        //Map<String, Object> map = userClient.getUserByUserNameId(userNameId);
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<String> groupIds = businessSystemDataService.getUserPositions();
        if (CollectionUtils.isEmpty(groupIds)){
            groupIds = new ArrayList<>();
        }
        groupIds.addAll(businessSystemDataService.getUserRoles());
        List<TaskVo> list = new ArrayList<>();
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getToDoTasksOracle("","",userNameId, formName, modelName, processModelType, startTime, startDate, endDate,
                    groupIds, ContextUtil.getSlevel()
            );
        } else if (DriverClassName.MYSQL_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getToDoTasksMySql("","",userNameId, formName, modelName, processModelType, startTime, startDate, endDate,
                    groupIds, ContextUtil.getSlevel()
            );
        }
        PageInfo<TaskVo> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public List<TaskVo> getAllToDoTasks(PageParam pageParam,String businessKey, String userNameId, String formName, String modelName, String processModelType, String startTime) {
        String startDate = "";
        String endDate = "";
        if (StrUtil.isNotBlank(startTime)) {
            startDate = startTime.split(",")[0];
            endDate = startTime.split(",")[1];
        }
        List<String> groupIds = businessSystemDataService.getUserPositions();
        groupIds.addAll(businessSystemDataService.getUserRoles());
        List<TaskVo> list = new ArrayList<>();
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getToDoTasksOracle("",businessKey,userNameId, formName, modelName, processModelType, startTime, startDate, endDate,
                    groupIds, ContextUtil.getSlevel()
            );
        } else if (DriverClassName.MYSQL_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getToDoTasksMySql("",businessKey,userNameId, formName, modelName, processModelType, startTime, startDate, endDate,
                    groupIds, ContextUtil.getSlevel()
            );
        }
        return list;
    }

    @Override
    public TaskVo getToDoTasks(String taskId) {
        List<String> groupIds = businessSystemDataService.getUserPositions();
        if (CollectionUtils.isEmpty(groupIds)){
            groupIds = new ArrayList<>();
        }
        groupIds.addAll(businessSystemDataService.getUserRoles());
        String userNameId = businessSystemDataService.getUserNameId();
        List<TaskVo> list = new ArrayList<>();
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getToDoTasksOracle(taskId,"",userNameId, "", "", "", "", "","",
                    groupIds, ContextUtil.getSlevel());
        } else if (DriverClassName.MYSQL_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getToDoTasksMySql(taskId,"",userNameId, "", "","", "", "","",
                    groupIds, ContextUtil.getSlevel());
        }
        return list.size()>0?list.get(0):null;
    }

    @Override
    public PageSet<CommonTaskDto> getMyHistoryPageSet(PageParam pageParam, String userNameId, String name, String procDefName, String processModelType, String startTime) {
        String startDate = "";
        String endDate = "";
        if (StrUtil.isNotBlank(startTime)) {
            startDate = startTime.split(",")[0];
            endDate = startTime.split(",")[1];
        }
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());

        List<CommonTaskDto> list = apiFlowableTaskMapper.getMyHistoryPageSetMySql(userNameId, name, procDefName, processModelType, startTime, startDate, endDate);
        PageInfo<CommonTaskDto> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public PageSet<TaskVo> getMyNoEndProcessPageSetData(PageParam pageParam, String userNameId, String formName, String modelName,
                                                        String processModelType, String startTime) {
        String startDate = "";
        String endDate = "";
        if (StrUtil.isNotBlank(startTime)) {
            startDate = startTime.split(",")[0];
            endDate = startTime.split(",")[1];
        }

        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<TaskVo> list = new ArrayList<>();
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getMyNoEndProcessPageSetDataOracle(userNameId, formName, modelName, processModelType, startTime, startDate, endDate);
        } else if (DriverClassName.MYSQL_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getMyNoEndProcessPageSetDataMySql(userNameId, formName, modelName, processModelType, startTime, startDate, endDate);
        }
        PageInfo<TaskVo> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public PageSet<CommonTaskDto> getTaskPageSet(PageParam pageParam, String formName, String modelName) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<CommonTaskDto> list;
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getTaskPageSetOracle(formName, modelName);
        } else if (DriverClassName.SQLSERVER_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getTaskPageSetSqlServer(formName, modelName);
        } else {
            list = apiFlowableTaskMapper.getTaskPageSetMySql(formName, modelName);
        }
        PageInfo<CommonTaskDto> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public PageSet<StartedDto> getStartedPageSet(PageParam pageParam, String modelName, String modelKey) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<StartedDto> list = apiFlowableTaskMapper.getStartedPageSet(modelName, modelKey);
        PageInfo<StartedDto> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Value("${spring.datasource.dynamic.datasource.master.driver-class-name:null}")
    public String driverClassName;

    @Override
    public PageSet<ProcessDefinitionDto> getDeployedPageSet(PageParam pageParam, String modelName, String modelKey) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<ProcessDefinitionDto> list;
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getDeployedPageSetOracle(modelName, modelKey);
        } else {
            list = apiFlowableTaskMapper.getDeployedPageSetMySql(modelName, modelKey);
        }
        PageInfo<ProcessDefinitionDto> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Autowired
    private ApiFlowableModelMapper apiFlowableModelMapper;


    @Transactional
    @Override
    public Object deployedDelete(String deploymentId, String id, String key) {
        if (StrUtil.isBlank(deploymentId)) {
            return failure("deploymentId不能为空,请确认！！！");
        }
        try {
            RepositoryService repositoryService = processEngine.getRepositoryService();
//            List<ActDeModelVo> list = apiFlowableModelMapper.getListByModelKey(key);
            //如果查询到多个版本数据
//            if (CollUtil.isNotEmpty(list) && list.size() >= 2) {
//                //如果选择删除的数据刚好是主版数据 则提示
//                for (ActDeModelVo actDeModelVo : list) {
//                    if (actDeModelVo.getProcdefId().equals(id) && "是".equals(actDeModelVo.getMajorVersion())) {
//                        return failure("当前流程存在多个版本数据，请取消该数据主版本,再进行删除！！！");
//                    }
//                }
//            }
            repositoryService.deleteDeployment(deploymentId, true);
            // 以下为特殊处理  把流程定义Id置为空
            //如果部署了多个版本得直接删除 不然会有问题
            //根据modelKey去act_my_model表查询到多条的话  直接根据流程定义Id加模型key删除 act_my_model,act_my_node_button,act_my_node_field,act_my_node_notice
            Integer integers = flowModelService.selectCountInteger(key);
            if (integers >= 2) {
                flowModelService.deleteByModelKeyAnProcdefId(key, id);
                flowNodeButtonService.deleteByModelKeyAnProcdefId(key, id);
                flowNodeFieldService.deleteByModelKeyAnProcdefId(key, id);
                flowNodeNoticeService.deleteByModelKeyAnProcdefId(key, id);
                flowNodeCodeService.deleteByModelKeyAnProcdefId(key, id);
            } else {
                String procdefId = BaseUtils.UUIDGenerator();
                //1.流程表单管理数据
                Integer integer = flowModelService.updateProcdefIdByModelKeyAndProcdef(procdefId,key, id);
                //2.流程按钮数据 根据key以及流程定义Id为空更新流程定义Id
                Integer integer1 = flowNodeButtonService.updateProcdefIdByModelKeyAndProcdef(procdefId,key, id);
                //3.节点字段可编辑不可编辑数据 根据key以及流程定义Id为空更新流程定义Id
                Integer integer2 = flowNodeFieldService.updateProcdefIdByModelKeyAndProcdef(procdefId,key, id);
                //4.节点通知 根据key以及流程定义Id为空更新流程定义Id
                Integer integer3 = flowNodeNoticeService.updateProcdefIdByModelKeyAndProcdef(procdefId,key, id);
                //5.节点权限码 根据key以及流程定义Id为空更新流程定义Id
                Integer integer4 = flowNodeCodeService.updateProcdefIdByModelKeyAndProcdef(procdefId,key, id);
            }
            //删除抄送的流程
            Integer integer = actMyProcessCopyService.deleteByProcessDefinitionId(id);
            return success("删除成功！！！");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure("删除失败" + e.getMessage());
        }
    }


    @Transactional
    @Override
    public Object deleteHistoricProcessInstance(String instanceId) {
        if (StrUtil.isBlank(instanceId)) {
            return failure("instanceId不能为空,请确认！！！");
        }
        try {
            historyService.deleteHistoricProcessInstance(instanceId);
            return success("删除成功！！！");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure("删除失败,失败原因为===" + e.getMessage());
        }
    }

    @Override
    public PageSet<TaskVo> getApplyedTasks(PageParam pageParam, String userNameId, String formName, String modelName, String processModelType) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<TaskVo> list = new ArrayList<>();
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getApplyedTasksOracle(userNameId, formName, modelName, processModelType);
        } else {
            list = apiFlowableTaskMapper.getApplyedTasksMySql(userNameId, formName, modelName, processModelType);
        }
        PageInfo<TaskVo> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public List<TaskVo> getApplyedTasks(PageParam pageParam, String userNameId) {
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            return apiFlowableTaskMapper.getApplyedTasksOracle(userNameId, null, null, null);
        } else {
            return apiFlowableTaskMapper.getApplyedTasksMySql(userNameId, null, null, null);
        }
    }

    @Autowired
    public HttpServletRequest request;

    @Autowired
    public ActMyFormService actMyFormService;



    @Override
    public Object historyClickDetails(String processInstanceId, String modelKey, String processDefinitionId, String businessKey, String nodeId) throws Exception {
        if (StringUtils.isBlank(processInstanceId) || StringUtils.isBlank(modelKey)) {
            return failure("processInstanceId/modelKey不能为空");
        }
        Example example = new Example(ActMyModel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modelKey);
        if (StringUtils.isNotBlank(processDefinitionId)) {
            criteria.andEqualTo("procdefId", processDefinitionId);
        }
        List<ActMyModel> lists = flowModelService.selectByExample(example);
        if (CollectionUtils.isEmpty(lists)) {
            return failure("流程与表单关联表查找不到数据,请检查");
        }

        HashMap<String, Object> map = new HashMap<>();
        if (StringUtils.isNotBlank(processInstanceId)) {
            List<CommentBean> commentBeanList = workflowService.getCommentListByProcessInstanceId(processInstanceId);
            map.put("commentBeanList", commentBeanList);
        }

        //根据实例id、模型key、procdefId
        ActMyForm actMyForm = actMyFormService.selectByProcessInstanceId(processInstanceId, modelKey, processDefinitionId);
        if (null != actMyForm) {
            lists.get(0).setFormDesign(actMyForm.getFormDesign());
        }
        map.put("lists", lists.get(0));

        // 查询历史节点表 ACT_HI_ACTINST
        List<HistoricActivityInstance> historicActivityInstanceList = getHistoricActivityInstAsc(processInstanceId);
        //1 正在执行的节点
        List<String> runningActivitiIdList = new ArrayList<String>();
        //2 获取已流经的流程线
        List<String> highLightedFlowIds = new ArrayList<>();
        //3.已执行历史节点
        List<String> executedActivityIdList = new ArrayList<String>();
        historicActivityInstanceList.forEach(historicActivityInstance -> {
            //1
            if (null == historicActivityInstance.getEndTime()) {
                runningActivitiIdList.add(historicActivityInstance.getActivityId());
            }
            //2
            if (historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                highLightedFlowIds.add(historicActivityInstance.getActivityId());
            }
            //3
            if (null != historicActivityInstance.getEndTime() && !historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                executedActivityIdList.add(historicActivityInstance.getActivityId());
            }

        });

        map.put("inProgress", runningActivitiIdList);
        highLightedFlowIds.addAll(executedActivityIdList);
        map.put("notInProgress", highLightedFlowIds);

        //4.通过流程定义id与modelKey查询查询所有流程节点按钮 并去重,再去遍历去查找详情数据
        List<ActMyNodeButtonVo> flowNodeButtons = flowNodeButtonService.getListByActDeModelKeyAndProcdefIdAndNodeId(modelKey, processDefinitionId, nodeId);
        if (flowNodeButtons.size()>0) {
//            String str = actMyNodeButtonVo.getNodeFormEditPath();
            ActMyNodeButtonVo actMyNodeButtonVo = flowNodeButtons.get(0);
            String tablename = actMyNodeButtonVo.getTablename();
            String primarykey = actMyNodeButtonVo.getPrimarykey();
            JSONObject jsonObject = businessSystemDataService.getBusinessData(tablename,primarykey,businessKey,modelKey);
            Map<String, Object> businessDataMap = new HashMap<>();
            map.put("businessData", jsonObject);
//            if (org.apache.commons.lang.StringUtils.isNotBlank(str)) {
//                String gateway = request.getHeader("Gateway");
//                String token = request.getHeader(ConstParamUtil.X_ACCESS_TOKEN);
//                //链式构建请求，带cookie请求
//                Map<String, Object> paramMap = new HashMap<>();
//                paramMap.put("uuid", businessKey);
//                String result2 = HttpRequest.post(gateway + str)
//                        .cookie("token=" + token)
//                        .form(paramMap)
//                        .timeout(20000)
//                        .execute().body();
//                Map<String, Object> map1 = new HashMap<>();
//                map1.put("businessData", jsonObject);
//                actMyNodeButtonVo.setMap(map1);
//            }
        }
        List<FlowNodeFieldVo> flowNodeField = flowNodeFieldService.selectByModelKeyAndId(modelKey, nodeId, processDefinitionId);
        map.put("flowNodeField", flowNodeField);
        map.put("flowNodeButtons", flowNodeButtons);
//        map.put("taskId",taskId);
        map.put("modelKey",modelKey);
        map.put("processDefinitionId",processDefinitionId);
        map.put("processInstanceId",processInstanceId);
        map.put("businessKey",businessKey);
        return map;
    }

    @Override
    public Object applyedTasksClickDetails(String processInstanceId, String modelKey) {
        if (StringUtils.isBlank(processInstanceId) || StringUtils.isBlank(modelKey)) {
            return failure("processInstanceId/modelKey不能为空");
        }
        Example example = new Example(ActMyModel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modelKey);
        List<ActMyModel> lists = flowModelService.selectByExample(example);
        if (CollectionUtils.isEmpty(lists)) {
            return failure("查找不到数据");
        }
        HashMap<String, Object> map = new HashMap<>();
        if (StringUtils.isNotBlank(processInstanceId)) {
            List<CommentBean> commentBeanList = workflowService.getCommentListByProcessInstanceId(processInstanceId);
            map.put("commentBeanList", commentBeanList);
        }
        map.put("lists", lists.get(0));

        // 查询历史节点表 ACT_HI_ACTINST
        List<HistoricActivityInstance> historicActivityInstanceList = getHistoricActivityInstAsc(processInstanceId);
        //1 正在执行的节点
        List<String> runningActivitiIdList = new ArrayList<String>();
        //2 获取已流经的流程线
        List<String> highLightedFlowIds = new ArrayList<>();
        //3.已执行历史节点
        List<String> executedActivityIdList = new ArrayList<String>();
        historicActivityInstanceList.forEach(historicActivityInstance -> {
            //1
            if (null == historicActivityInstance.getEndTime()) {
                runningActivitiIdList.add(historicActivityInstance.getActivityId());
            }
            //2
            if (historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                highLightedFlowIds.add(historicActivityInstance.getActivityId());
            }
            //3
            if (null != historicActivityInstance.getEndTime() && !historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                executedActivityIdList.add(historicActivityInstance.getActivityId());
            }

        });


        //4查询当前节点的按钮
        map.put("flowNodeButtons", "");
        return map;
    }

    @Override
    public List<FlowNodeVo> getBackNodesByProcessInstanceId(String processInstanceId, String taskId, String nodelId) {
        List<FlowNodeVo> backNods = new ArrayList<>();
        TaskEntity taskEntity = (TaskEntity) taskService.createTaskQuery().taskId(taskId).singleResult();
        String currActId = taskEntity.getTaskDefinitionKey();
        //获取运行节点表中usertask
        String sql = "select t.* from act_ru_actinst t where t.ACT_TYPE_ = 'userTask' " +
                " and t.PROC_INST_ID_=#{processInstanceId} and t.END_TIME_ is not null ";
        List<ActivityInstance> activityInstances = runtimeService.createNativeActivityInstanceQuery().sql(sql)
                .parameter("processInstanceId", processInstanceId)
                .list();
        //获取运行节点表的parallelGateway节点并出重
        sql = "SELECT t.ID_, t.REV_,t.PROC_DEF_ID_,t.PROC_INST_ID_,t.EXECUTION_ID_,t.ACT_ID_, t.TASK_ID_, t.CALL_PROC_INST_ID_, t.ACT_NAME_, t.ACT_TYPE_, " +
                " t.ASSIGNEE_, t.START_TIME_, max(t.END_TIME_) as END_TIME_, t.DURATION_, t.DELETE_REASON_, t.TENANT_ID_" +
                " FROM  act_ru_actinst t WHERE t.ACT_TYPE_ = 'parallelGateway' AND t.PROC_INST_ID_ = #{processInstanceId,jdbcType=VARCHAR} and t.END_TIME_ is not null" +
                " and t.ACT_ID_ <> #{actId,jdbcType=VARCHAR} GROUP BY t.ID_, t.REV_,t.PROC_DEF_ID_,t.PROC_INST_ID_,t.EXECUTION_ID_,t.ACT_ID_, t.TASK_ID_, t.CALL_PROC_INST_ID_, " +
                " t.ACT_NAME_, t.ACT_TYPE_, t.ASSIGNEE_, t.START_TIME_, t.END_TIME_, t.DURATION_, t.DELETE_REASON_, t.TENANT_ID_";
        List<ActivityInstance> parallelGatewaies = runtimeService.createNativeActivityInstanceQuery().sql(sql)
                .parameter("processInstanceId", processInstanceId)
                .parameter("Id", currActId)
                .list();
        //排序
        if (org.flowable.editor.language.json.converter.util.CollectionUtils.isNotEmpty(parallelGatewaies)) {
            activityInstances.addAll(parallelGatewaies);
            activityInstances.sort(Comparator.comparing(ActivityInstance::getEndTime));
        }
        //分组节点
        int count = 0;
        Map<ActivityInstance, List<ActivityInstance>> parallelGatewayUserTasks = new HashMap<>();
        List<ActivityInstance> userTasks = new ArrayList<>();
        ActivityInstance currActivityInstance = null;
        for (ActivityInstance activityInstance : activityInstances) {
            if (BpmnXMLConstants.ELEMENT_GATEWAY_PARALLEL.equals(activityInstance.getActivityType())) {
                count++;
                if (count % 2 != 0) {
                    List<ActivityInstance> datas = new ArrayList<>();
                    currActivityInstance = activityInstance;
                    parallelGatewayUserTasks.put(currActivityInstance, datas);
                }
            }
            if (BpmnXMLConstants.ELEMENT_TASK_USER.equals(activityInstance.getActivityType())) {
                if (count % 2 == 0) {
                    userTasks.add(activityInstance);
                } else {
                    if (parallelGatewayUserTasks.containsKey(currActivityInstance)) {
                        parallelGatewayUserTasks.get(currActivityInstance).add(activityInstance);
                    }
                }
            }
        }
        //组装人员名称
        List<HistoricTaskInstance> historicTaskInstances = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId).finished().list();
        Map<String, List<HistoricTaskInstance>> taskInstanceMap = new HashMap<>();
        List<String> userCodes = new ArrayList<>();
        historicTaskInstances.forEach(historicTaskInstance -> {
            userCodes.add(historicTaskInstance.getAssignee());
            String taskDefinitionKey = historicTaskInstance.getTaskDefinitionKey();
            if (taskInstanceMap.containsKey(historicTaskInstance.getTaskDefinitionKey())) {
                taskInstanceMap.get(taskDefinitionKey).add(historicTaskInstance);
            } else {
                List<HistoricTaskInstance> tasks = new ArrayList<>();
                tasks.add(historicTaskInstance);
                taskInstanceMap.put(taskDefinitionKey, tasks);
            }
        });
        //组装usertask的数据
        List<User> userList = identityService.createUserQuery().userIds(userCodes).list();
        Map<String, String> activityIdUserNames = getApplyers(processInstanceId, userList, taskInstanceMap);
        if (org.flowable.editor.language.json.converter.util.CollectionUtils.isNotEmpty(userTasks)) {
            userTasks.forEach(activityInstance -> {
                FlowNodeVo node = new FlowNodeVo();
                node.setNodeId(activityInstance.getActivityId());
                node.setNodeName(activityInstance.getActivityName());
                node.setEndTime(activityInstance.getEndTime());
                node.setUserName(activityInstance.getAssignee());
                //node.setUserName(activityIdUserNames.get(activityInstance.getActivityId()));
                backNods.add(node);
            });
        }
        //组装会签节点数据
        if (MapUtils.isNotEmpty(taskInstanceMap)) {
            parallelGatewayUserTasks.forEach((activity, activities) -> {
                FlowNodeVo node = new FlowNodeVo();
                node.setNodeId(activity.getActivityId());
                node.setEndTime(activity.getEndTime());
                StringBuffer nodeNames = new StringBuffer("会签:");
                StringBuffer userNames = new StringBuffer("审批人员:");
                if (org.flowable.editor.language.json.converter.util.CollectionUtils.isNotEmpty(activities)) {
                    activities.forEach(activityInstance -> {
                        nodeNames.append(activityInstance.getActivityName()).append(",");
                        userNames.append(activityIdUserNames.get(activityInstance.getActivityId())).append(",");
                    });
                    node.setNodeName(Convert.toStr(nodeNames));
                    node.setUserName(Convert.toStr(userNames));
                    backNods.add(node);
                }
            });
        }
        //去重合并
        List<FlowNodeVo> datas = backNods.stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(() ->
                        new TreeSet<>(Comparator.comparing(nodeVo -> nodeVo.getNodeId()))), ArrayList::new));

        //移除当前节点
        if (StringUtils.isNotBlank(nodelId)) {
            int size = datas.size();
            for (int i = size - 1; i >= 0; i--) {
                String item = datas.get(i).getNodeId();
                if (nodelId.equals(item)) {
                    datas.remove(item);
                }
            }
        }


        datas.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(FlowNodeVo::getNodeId))), ArrayList::new));

        //排序
        datas.sort(Comparator.comparing(FlowNodeVo::getEndTime));
        return datas;
    }


    @Override
    public Object getTaskPageSetClickDetails(String processInstanceId, String modelKey) {
        if (StringUtils.isBlank(processInstanceId) || StringUtils.isBlank(modelKey)) {
            return failure("processInstanceId/modelKey不能为空");
        }
        Example example = new Example(ActMyModel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modelKey);
        List<ActMyModel> lists = flowModelService.selectByExample(example);
        if (CollectionUtils.isEmpty(lists)) {
            return failure("查找不到数据");
        }
        HashMap<String, Object> map = new HashMap<>();
        if (StringUtils.isNotBlank(processInstanceId)) {
            List<CommentBean> commentBeanList = workflowService.getCommentListByProcessInstanceId(processInstanceId);
            map.put("commentBeanList", commentBeanList);
        }
        map.put("lists", lists.get(0));

        // 查询历史节点表 ACT_HI_ACTINST
        List<HistoricActivityInstance> historicActivityInstanceList = getHistoricActivityInstAsc(processInstanceId);
        //1 正在执行的节点
        List<String> runningActivitiIdList = new ArrayList<String>();
        //2 获取已流经的流程线
        List<String> highLightedFlowIds = new ArrayList<>();
        //3.已执行历史节点
        List<String> executedActivityIdList = new ArrayList<String>();
        historicActivityInstanceList.forEach(historicActivityInstance -> {
            //1
            if (null == historicActivityInstance.getEndTime()) {
                runningActivitiIdList.add(historicActivityInstance.getActivityId());
            }
            //2
            if (historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                highLightedFlowIds.add(historicActivityInstance.getActivityId());
            }
            //3
            if (null != historicActivityInstance.getEndTime() && !historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                executedActivityIdList.add(historicActivityInstance.getActivityId());
            }

        });


        //4查询当前节点的按钮
        map.put("flowNodeButtons", "");

        //5 查询当前节点的字段属性
        map.put("flowNodeField", "");
        return map;
    }


    private Map<String, String> getApplyers(String processInstanceId, List<User> userList, Map<String, List<HistoricTaskInstance>> taskInstanceMap) {
        Map<String, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, user -> user));
        Map<String, String> applyMap = new HashMap<>();
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        taskInstanceMap.forEach((activityId, taskInstances) -> {
            StringBuffer applyers = new StringBuffer();
            StringBuffer finalApplyers = applyers;
            taskInstances.forEach(taskInstance -> {
                if (!taskInstance.getName().equals("发起人")) {
                    User user = userMap.get(taskInstance.getAssignee());
                    if (user != null) {
                        if (StringUtils.indexOf(Convert.toStr(finalApplyers), Convert.toChar(user.getDisplayName())) == -1) {
                            finalApplyers.append(user.getDisplayName()).append(",");
                        }
                    }
                } else {
                    String startUserId = processInstance.getStartUserId();
                    User user = identityService.createUserQuery().userId(startUserId).singleResult();
                    if (user != null) {
                        finalApplyers.append(user.getDisplayName()).append(",");
                    }
                }
            });
            if (applyers.length() > 0) {
                applyers = applyers.deleteCharAt(applyers.length() - 1);
            }
            applyMap.put(activityId, Convert.toStr(applyers));
        });
        return applyMap;
    }


    /**
     * 通过流程实例ID获取流程中已经执行的节点，按照执行先后顺序排序
     *
     * @param procInstId
     * @return
     */
    public List<HistoricActivityInstance> getHistoricActivityInstAsc(String procInstId) {
        return historyService.createHistoricActivityInstanceQuery().processInstanceId(procInstId)
                .orderByHistoricActivityInstanceStartTime().asc().list();
    }


    /**
     * 转办
     *
     * @param taskId
     * @param comment
     * @param instanceId
     * @Param userNameId 转办人userNameId
     */
    @Transactional
    @Override
    public Object transfer(String taskId, String comment, String instanceId, String owner,String userNameId) {
        String transferType = StringUtils.isNullOrEmpty(owner)?"转办":"任务接收";
        try {
            if (StrUtil.hasBlank(taskId, comment, instanceId, userNameId)) {
                return failure("taskId/comment/instanceId/userNameId不能为空");
            }
            owner = StringUtils.emptyToDefault(owner,businessSystemDataService.getUserNameId());
            TaskEntityImpl currTask = (TaskEntityImpl) taskService.createTaskQuery().taskId(taskId).singleResult();
            if (ObjectUtil.isNotEmpty(currTask)) {
                //1.生成历史记录
                TaskEntity task = createSubTask(currTask, owner);
                managementService.executeCommand(new AddHisCommentCmd(taskId, owner, instanceId,
                        Convert.toStr(FlowEnum.ZB), comment));
                taskService.complete(task.getId());
                //3.转办
                taskService.setOwner(taskId, userNameId);
                taskService.setAssignee(taskId, userNameId);
                updateTaskOpeningTimeByKey(taskId, null);
                //3.转办
                return success(transferType+"成功!!!");
            } else {
                return failure("根据任务Id查询不到该任务,请刷新该页面!!!");
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure(transferType+"失败,失败原因为---" + e.getMessage());
        }
    }


    public TaskEntity createSubTask(TaskEntityImpl ptask, String userNameId) {
        TaskEntity task = null;
        if (ptask != null) {
            //1.生成子任务
            task = (TaskEntity) taskService.newTask(RandomUtil.randomString(32));
            task.setCategory(ptask.getCategory());
            task.setDescription(ptask.getDescription());
            task.setTenantId(ptask.getTenantId());
            task.setAssignee(userNameId);
            task.setName(ptask.getName());
            task.setParentTaskId(ptask.getId());
            task.setProcessDefinitionId(ptask.getProcessDefinitionId());
            task.setProcessInstanceId(ptask.getProcessInstanceId());
            task.setTaskDefinitionKey(ptask.getTaskDefinitionKey());
            task.setTaskDefinitionId(ptask.getTaskDefinitionId());
            task.setPriority(ptask.getPriority());
            task.setCreateTime(new Date());
            taskService.saveTask(task);
        }
        return task;
    }

    @Transactional
    @Override
    public Object delegate(String taskId, String comment, String instanceId, String userNameId) {
        try {

            if (StrUtil.hasBlank(taskId, comment, instanceId, userNameId)) {
                return failure("taskId/comment/instanceId/userNameId不能为空");
            }
            TaskEntityImpl currTask = (TaskEntityImpl) taskService.createTaskQuery().taskId(taskId).singleResult();
            if (ObjectUtil.isNotNull(currTask)) {
                //1.添加审批意见
                managementService.executeCommand(new AddHisCommentCmd(taskId, businessSystemDataService.getUserNameId(), instanceId,
                        Convert.toStr(FlowEnum.WP), comment));
                //2.设置审批人就是当前登录人
                taskService.setAssignee(taskId, businessSystemDataService.getUserNameId());
                //3.执行委派
                taskService.delegateTask(taskId, userNameId);
                updateTaskOpeningTimeByKey(taskId, null);
                //4. 记录委派数据 暂时先不搞
                return success("委派成功!!!");
            } else {
                return failure("没有运行时的任务实例,请确认!");
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure("委派失败,原因为!" + e.getMessage());
        }

    }

    @Transactional
    @Override
    public Object revokeProcess(String taskId, String comment, String instanceId) {

        try {
            if (StrUtil.hasBlank(taskId, comment, instanceId)) {
                return failure("taskId/comment/instanceId不能为空！！！");
            }
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(instanceId).singleResult();
            if (ObjectUtil.isEmpty(processInstance)) {
                return failure("查询不到流程实例数据，请刷新当前页面");
            }
            if (ObjectUtil.isNotEmpty(processInstance)) {
                //添加撤回意见
                managementService.executeCommand(new AddHisCommentCmd(taskId, businessSystemDataService.getUserNameId(), instanceId,
                        Convert.toStr(FlowEnum.CH), comment));
                //设置提交人
                runtimeService.setVariable(instanceId, FlowConstant.FLOW_SUBMITTER_VAR, processInstance.getStartUserId());
                //获取第一个任务节点id
                String disActivity = getFirstActivityId(processInstance.getProcessDefinitionId());
                if (StrUtil.isBlank(disActivity)) {
                    return failure("查询不到流程第一步,不允许进行撤回");
                }
                //删除运行和历史的节点信息
                //deleteActivity(disActivity.getId(), instanceId);
                //执行跳转
                this.moveExecutionsToSingleActivityId(instanceId, disActivity);
                return success("撤回成功");
            }

            return success("撤回成功!!!");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("撤回失败的原因为" + e.getMessage());
            return failure("撤回失败");
        }

    }

    /**
     * @param processDefinitionId
     * @return {@link String}
     */
    public String getFirstActivityId(String processDefinitionId) {
        RepositoryService repositoryService = processEngine.getRepositoryService();
        if (ObjectUtil.isEmpty(repositoryService)) {
            return null;
        }
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(processDefinitionId)
                .singleResult();
        if (ObjectUtil.isEmpty(repositoryService)) {
            return null;
        }
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
        List<Process> processes = bpmnModel.getProcesses();
        List<FlowElement> flowElements = new ArrayList<>();

        String targetID = "";
        if (!processes.isEmpty()) {
            Collection<FlowElement> processFlowElements = processes.get(0).getFlowElements();
            for (FlowElement flowElement : processFlowElements) {
                if (flowElement instanceof StartEvent) {
                    targetID = ((StartEvent) flowElement).getOutgoingFlows().get(0).getTargetRef();
                    break;
                }
            }
        }
        return targetID;
    }


    @Override
    public Object signature(String taskId, String comment, String instanceId, String userNameId, String signature) {
        if (StrUtil.hasBlank(taskId, comment, instanceId, userNameId, signature)) {
            return failure("taskId/comment/instanceId/userNameId/signature必要参数不能为空，请确认!");
        }
        TaskEntityImpl taskEntity = (TaskEntityImpl) taskService.createTaskQuery().taskId(taskId).singleResult();
        //把当前的节点设置为空
        if (ObjectUtil.isNotEmpty(taskEntity)) {
            //如果是加签再加签
            String parentTaskId = taskEntity.getParentTaskId();
            if (StringUtils.isBlank(parentTaskId)) {
                taskEntity.setOwner(businessSystemDataService.getUserNameId());
                taskEntity.setAssignee(null);
                taskEntity.setCountEnabled(true);
                if ("after".equals(signature)) {
                    taskEntity.setScopeType(FlowConstant.AFTER_ADDSIGN);
                } else {
                    taskEntity.setScopeType(FlowConstant.BEFORE_ADDSIGN);
                }
                //1.2 设置任务为空执行者
                taskService.saveTask(taskEntity);
            }
            //2.添加加签数据
            createSignSubTasks(userNameId, taskEntity);
            //3.添加审批意见
            String type = "after".equals(signature) ? Convert.toStr(FlowEnum.HJQ) : FlowEnum.QJQ.toString();
            //1.添加审批意见
            managementService.executeCommand(new AddHisCommentCmd(taskId, businessSystemDataService.getUserNameId(), instanceId,
                    type, comment));
            String message = "after".equals(signature) ? "后加签成功" : "前加签成功";
            return success(message);
        } else {
            return failure("不存在任务实例，请确认!");
        }
    }

    @Override
    public List<CommentVo> getTaskCommentsByTaskId(String taskId) {
        return apiFlowableTaskMapper.getTaskCommentsByTaskId(taskId);
    }

    @Override
    public Object clickStartedViewFlowchart(String processInstanceId, String modelKey, String processDefinitionId) {

        Example example = new Example(ActMyModel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modelKey);
        if (StringUtils.isNotBlank(processDefinitionId)) {
            criteria.andEqualTo("procdefId", processDefinitionId);
        }
        List<ActMyModel> lists = flowModelService.selectByExample(example);
        if (CollectionUtils.isEmpty(lists)) {
            return failure("查找不到数据");
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("commentBeanList", null);
        map.put("lists", lists.get(0));

        // 查询历史节点表 ACT_HI_ACTINST
        List<HistoricActivityInstance> historicActivityInstanceList = getHistoricActivityInstAsc(processInstanceId);
        //1 正在执行的节点
        List<String> runningActivitiIdList = new ArrayList<String>();
        //2 获取已流经的流程线
        List<String> highLightedFlowIds = new ArrayList<>();
        //3.已执行历史节点
        List<String> executedActivityIdList = new ArrayList<String>();
        historicActivityInstanceList.forEach(historicActivityInstance -> {
            //1
            if (null == historicActivityInstance.getEndTime()) {
                runningActivitiIdList.add(historicActivityInstance.getActivityId());
            }
            //2
            if (historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                highLightedFlowIds.add(historicActivityInstance.getActivityId());
            }
            //3
            if (null != historicActivityInstance.getEndTime() && !historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                executedActivityIdList.add(historicActivityInstance.getActivityId());
            }

        });

        map.put("inProgress", runningActivitiIdList);
        highLightedFlowIds.addAll(executedActivityIdList);
        map.put("notInProgress", highLightedFlowIds);
        map.put("flowNodeButtons", "");
        map.put("flowNodeButtons", "");

        return map;
    }


    /**
     * 创建加签子任务
     *
     * @param userNameId 被加钱人 ewsd0001,ewsd0002
     * @param taskEntity 父任务
     */
    private void createSignSubTasks(String userNameId, TaskEntity taskEntity) {
        if (org.flowable.editor.language.json.converter.util.CollectionUtils.isNotEmpty(Arrays.asList(userNameId.split(",")))) {
            String parentTaskId = taskEntity.getParentTaskId();
            if (StringUtils.isBlank(parentTaskId)) {
                parentTaskId = taskEntity.getId();
            }
            String finalParentTaskId = parentTaskId;
            //1.创建被加签人的任务列表
            Arrays.asList(userNameId.split(",")).forEach(userCode -> {
                if (StringUtils.isNotBlank(userCode)) {
                    this.createSubTask(taskEntity, finalParentTaskId, userCode);
                }
            });
            String taskId = taskEntity.getId();
            if (StringUtils.isBlank(taskEntity.getParentTaskId())) {
                //2.创建加签人的任务并执行完毕
                Task task = this.createSubTask(taskEntity, finalParentTaskId, businessSystemDataService.getUserNameId());
                taskId = task.getId();
            }
            Task taskInfo = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (null != taskInfo) {
                taskService.complete(taskId);
            }
            //如果是候选人，需要删除运行时候选表种的数据。
            long candidateCount = taskService.createTaskQuery().taskId(parentTaskId).taskCandidateUser(businessSystemDataService.getUserNameId()).count();
            if (candidateCount > 0) {
                taskService.deleteCandidateUser(parentTaskId, businessSystemDataService.getUserNameId());
            }
        }
    }

    //    @Resource
//    private SysUserService sysUserService;
//    @Resource
//    private SysOrganizationService sysOrganizationService;
    @Override
    public Object processUrging(String type, String message, String formName, String processDefinitionId, String taskId, String assignee, String startUserId) {
        if (StrUtil.hasBlank(type, message, taskId)) {
            return failure("请检查type/message/taskId不能为空!!!");
        }
        //查询任务是否存在
        TaskEntity taskEntity = (TaskEntity) taskService.createTaskQuery().taskId(taskId).singleResult();
        if (ObjectUtil.isNull(taskEntity)) {
            return failure("没有此任务,请确认或刷新页面！！！");
        }
        //# 当act_ru_task的assignee为空时，考虑可能是角色，岗位在act_hi_identitylink表中
        if (StringUtils.isBlank(assignee)) {
            //根据任务id去act_hi_identitylink查询出用户组GROUP_ID_
            List<ActHiIdentitylinkVo> list = apiFlowableTaskMapper.getListByTaskId(taskId);
            String groupId = list.get(0).getGroupId();
            //判断是角色 直接根据groupId角色id去用户表查询是否存在数据
            List<String> list1 = apiFlowableTaskMapper.getListByUserNmaeId(groupId);
            //判断是岗位 直接根据
            List<String> list2 = apiFlowableTaskMapper.getListPostByUserNmaeId(groupId);
            if (ListUtil.ListStringIsItEmpty(list1)) {
                assignee = ListUtil.ListConvertString(list1);
            } else if (ListUtil.ListStringIsItEmpty(list2)) {
                assignee = ListUtil.ListConvertString(list2);
            }
            if (StrUtil.isBlank(assignee)) {
                assignee = list.stream()
                        .map(obj -> obj.getUserId())
                        .collect(Collectors.joining(","));
            } else {
                assignee = "";
            }
        }
        if (StringUtils.isBlank(assignee)) {
            return failure("找不到要催办的人");
        }
        List<String> userNameIdStr = Arrays.asList(assignee.split(","));
//        for (int i = 0; i < userNameIdStr.size(); i++) {
//            SysUser user = sysUserService.getSysUserByUserNameId(userNameIdStr.get(i));
//            if (null != user) {
//                String[] list = type.split(",");
//                for (String str : list) {
//                    switch (str) {
//                        case "note":
//                            sendNote(formName, userNameIdStr.get(i), messageClient, message);
//                            break;
//                        case "mail":
//                            sendSimpleMail(user, formName, userNameIdStr.get(i), messageClient, message);
//                            break;
//                        case "message":
//                            sendSms(user, formName, userNameIdStr.get(i), messageClient, message);
//                            break;
//                    }
//                }
//            }
//
//        }
        return success("发送成功!");
    }


    //发送站内信
    private Object sendNote(String formName, String assignee, MessageClient messageClient, String message) {
        Map<String, Object> map = new HashMap<>();
        map.put("title", formName);
        map.put("type", "流程审批");
        map.put("description", "流程审批");
        map.put("receiverId", assignee);
        map.put("url", 1);
        map.put("content", message);
        Object object = messageClient.save(map);
        LOGGER.warn("流程催办发送站内信{}", object);
        LOGGER.warn("流程催办发送站内信给{}", assignee);
        return object;
    }

/*
    //发送邮件
    private Object sendSimpleMail(SysUser user, String formName, String assignee, MessageClient messageClient, String message) {
        //获取用户详情数据
        Map<String, Object> map = new HashMap<>();
        map.put("toMail", Convert.toStr(user.getUserEmail()));
        map.put("subject", formName);
        map.put("content", message);
        Object object = messageClient.sendSimpleMail(map);
        LOGGER.info("发送邮件==" + object);
        return object;
    }

    //发送短信
    private Object sendSms(SysUser user, String formName, String assignee, MessageClient messageClient, String message) {
        String messages = "{businessStatus:'" + message + "'}";
        Object object = messageClient.sendSms(Convert.toStr(user.getUserCellphone()), "广州赛宝腾睿信息科技有限公司", "SMS_162220712",
                messages, assignee);
        LOGGER.info("发送短信==" + object);
        return object;
    }*/


    @Override
    public Object processRevocation(String processInstanceId, String message, String taskId) {
        if (StrUtil.hasBlank(processInstanceId, message, taskId)) {
            return failure("请检查processInstanceId/message/taskId不能为空！！！");
        }

        boolean flag = apiFlowableProcessInstanceService.isSuspended(processInstanceId);
        if (!flag) {
            return failure("流程已挂起,请联系管理员激活！！！");
        }
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (ObjectUtil.isNotEmpty(processInstance)) {
            //添加审批记录
            managementService.executeCommand(new AddHisCommentCmd(taskId, businessSystemDataService.getUserNameId(), processInstanceId,
                    Convert.toStr(FlowEnum.LCCX), message));
            //获取end节点
            List<EndEvent> endNodes = apiFlowableBpmnModelService.findEndFlowElement(processInstance.getProcessDefinitionId());
            String endId = endNodes.get(0).getId();
            //2、执行终止
            List<Execution> executions = runtimeService.createExecutionQuery().parentId(processInstanceId).list();
            List<String> executionIds = new ArrayList<>();
            executions.forEach(execution -> executionIds.add(execution.getId()));
            runtimeService.createChangeActivityStateBuilder()
                    .moveExecutionsToSingleActivityId(executionIds, endId)
                    .changeState();

            return success("流程撤销成功！！！");
        } else {
            return failure("该流程已经审核结束,请刷新下该页面去【我的历史】界面查看数据！！！");
        }
    }

    @Override
    public Integer updateBusinessData(String sql) {
        return apiFlowableTaskMapper.updateBusinessData(sql);
    }

    @Autowired
    private RepositoryService repositoryService;


    /**
     * 根据任务id获取上一个节点的信息
     *
     * @param taskId
     * @return
     */
    @Override
    public Object goBackToThePreviousStep(String processInstanceId, String currentActivityId, String processDefinitionId, String taskId) {
        if (StrUtil.hasBlank(processInstanceId, currentActivityId, processDefinitionId, taskId)) {
            return failure("processInstanceId/currentActivityId/processDefinitionId不能为空！！！");
        }
        try {
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
            FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(currentActivityId);
            SequenceFlow sequenceFlow = flowNode.getIncomingFlows().get(0);
            // 获取上一个节点的activityId
            String sourceRef = sequenceFlow.getSourceRef();
            if (sequenceFlow.getSourceFlowElement() instanceof UserTask) {
                //添加审批记录
                managementService.executeCommand(new AddHisCommentCmd(taskId, businessSystemDataService.getUserNameId(), processInstanceId,
                        Convert.toStr(FlowEnum.BHSYB), "驳回上一步"));
                // 流程回退到上一个节点，审批人继续审批
                runtimeService.createChangeActivityStateBuilder().processInstanceId(processInstanceId)
                        .moveActivityIdTo(currentActivityId, sourceRef).changeState();
                return success("驳回上一步成功！！！");
            } else {
                return failure("上一步不是用户任务节点请使用驳回按钮+划线方式进行驳回");
            }
        } catch (Exception e) {
            LOGGER.error("驳回上一步失败" + e.getMessage());
            return failure("驳回失败" + e.getMessage());
        }
    }


    @Override
    public Object nextFlowNode(String node, String taskId) {
        HashMap var3 = new HashMap();
        String id = "";
        String name = "";
        Task task = processEngine.getTaskService().createTaskQuery().taskId(taskId).singleResult();
        ExecutionEntity ee = (ExecutionEntity) processEngine.getRuntimeService().createExecutionQuery()
                .executionId(task.getExecutionId()).singleResult();
        // 当前审批节点
        String crruentActivityId = ee.getActivityId();
        BpmnModel bpmnModel = processEngine.getRepositoryService().getBpmnModel(task.getProcessDefinitionId());
        FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(crruentActivityId);
        // 输出连线
        List<SequenceFlow> outFlows = flowNode.getOutgoingFlows();
        for (SequenceFlow sequenceFlow : outFlows) {
            //当前审批节点
            if ("now".equals(node)) {
                FlowElement sourceFlowElement = sequenceFlow.getSourceFlowElement();
                System.out.println("当前节点: id=" + sourceFlowElement.getId() + ",name=" + sourceFlowElement.getName());
                id = sourceFlowElement.getId();
                name = sourceFlowElement.getName();
            } else if ("next".equals(node)) {
                //# 广州赛宝腾睿信息科技有限公司  后续考虑传入类型 任务节点/网关/结束节点。。。。
                // 下一个审批节点
                FlowElement targetFlow = sequenceFlow.getTargetFlowElement();
                if (targetFlow instanceof UserTask) {
                    LOGGER.info("下一个审批节点UserTask", targetFlow);
                    id = targetFlow.getId();
                    name = targetFlow.getName();
                }
                // 如果下个审批节点为结束节点
                if (targetFlow instanceof EndEvent) {
                    System.out.println("下一节点为结束节点：id=" + targetFlow.getId() + ",name=" + targetFlow.getName());
                    id = targetFlow.getId();
                    name = targetFlow.getName();
                }
            }
        }
        var3.put("id", id);
        var3.put("name", name);
        return var3;
    }

    @Override
    public List<String> getFinanceUuidByTankName(String userNameId, String formName, String modelName, String processModelType, String startTime) {
        String startDate = "";
        String endDate = "";
        if (StrUtil.isNotBlank(startTime)) {
            startDate = startTime.split(",")[0];
            endDate = startTime.split(",")[1];
        }
        List<String> list = new ArrayList<>();
        if (druid.getDriverClassName().equals(DriverClassName.SQLSERVER_NAME)) {
            list = apiFlowableTaskMapper.getFinanceSqlServer(userNameId, formName, modelName, processModelType, startTime, startDate, endDate);
        } else if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            list = apiFlowableTaskMapper.getFinanceOracle(userNameId, formName, modelName, processModelType, startTime, startDate, endDate);
        } else {
            list = apiFlowableTaskMapper.getFinanceMySql(userNameId, formName, modelName, processModelType, startTime, startDate, endDate);
        }
        return list;
    }

    @Override
    public TaskVo getToDoTaskByUuid(String userNameId, String uuid) {
        return apiFlowableTaskMapper.getToDoTaskByUuid(userNameId, uuid);
    }

    @Override
    public PageSet<CommonTaskDto> getCompletedTaskPageSet(PageParam pageParam, String formName, String modelName) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<CommonTaskDto> list = apiFlowableTaskMapper.getCompletedTaskPageSet(formName, modelName);
        PageInfo<CommonTaskDto> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public Result assignedTo(String assignUser, String processInstanceId, String modelKey) {
        if (StrUtil.hasBlank(assignUser, processInstanceId)) {
            throw new SystemException("必要参数不能为空");
        }
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(modelKey)
                .latestVersion().singleResult();
        if (ObjectUtil.isNotEmpty(processDefinition) && processDefinition.isSuspended()) {
            throw new SystemException("必要此流程已经挂起,请联系系统管理员去激活该流程！！！");
        }
        String assignUserStr[] = assignUser.split(",");
        Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
        if (ObjectUtil.isEmpty(task)) {
            throw new SystemException("查询不到该任务,请刷新改页面！！！");
        }
        if (assignUserStr.length == 1) {
            taskService.setAssignee(task.getId(), assignUserStr[0]);
            return Result.ofSuccessMsg("设置处理人成功！！！");
        }
        if (assignUserStr.length > 1) {
            for (int i = 0; i < assignUserStr.length; i++) {
                taskService.addCandidateUser(task.getId(), assignUserStr[i]);
            }
            return Result.ofSuccessMsg("设置处理人成功！！！");
        }
        return Result.ofSuccessMsg("设置处理人成功！！！");
    }
    public List<TaskVo> getTaskRefByParam(String processInstanceId, String businessKey, String taskId) {
        return apiFlowableTaskMapper.getTaskRefByParam( processInstanceId, businessKey ,taskId);
    }

    @Override
    public List<TaskVo> getActHiTaskVoById(String processInstanceId, String businessKey, String taskId) {
        return apiFlowableTaskMapper.getActHiTaskVoById( processInstanceId, businessKey ,taskId);
    }

    @Override
    public List<CommentVo> getTaskCommentsByProcessInstanceId(String processInstanceId) {
        return apiFlowableTaskMapper.getTaskCommentsByProcessInstanceId(processInstanceId);
    }
    @Override
    public CommentVo getActHiTaskinstById(String id) {
        return apiFlowableTaskMapper.getActHiTaskinstById(id);
    }

    //获取流程实例当前任务（节点所有任务，包含多实例任务或加签的子任务）处理人的方法
    @Override
    public List<String> getCurrentAssigneeByTask(String taskId) {
        List<String> currentUserNames = new ArrayList<>();
        List<String> currentUserNameIds = new ArrayList<>();
        List<Map> currenthandlers = apiFlowableTaskMapper.getCurrentAssigneeByTask(taskId);// 获取当前任务分配的处理人、候选人、候选组
        String pid = "";// 当前任务实例id
        String kid = "";// 当前任务节点key id
        for(Map map : currenthandlers){
            if(map==null || map.isEmpty()) continue;
            String userNameId = StringUtils.convertNullToEmpty(map.get("userNameId"));
            String userId = StringUtils.convertNullToEmpty(map.get("userId"));
            String owner = StringUtils.convertNullToEmpty(map.get("owner"));
            String groupId = StringUtils.convertNullToEmpty(map.get("groupId"));
            String procInstId = StringUtils.convertNullToEmpty(map.get("procInstId"));
            String taskDefKey = StringUtils.convertNullToEmpty(map.get("taskDefKey"));
            if(!procInstId.isEmpty() && !taskDefKey.isEmpty() && pid.isEmpty() && kid.isEmpty()){
                pid = procInstId;
                kid = taskDefKey;
            }
            // 任务处理人列表加入当前任务的候选人员或处理人
            if(!StringUtils.isNullOrEmpty(userNameId)){
                currentUserNameIds.add(userNameId);
            }else{
                //只有任务被分配人或拥有者均为空时，去关系表获取候选处理人信息
                // 解析关系表当前任务的候选角色或岗位，获取符合条件的角色或岗位人员id加入任务处理人列表
                if(!StringUtils.isNullOrEmpty(owner)){
                    currentUserNameIds.add(owner);
                }else if(!StringUtils.isNullOrEmpty(userId)){
                    currentUserNameIds.add(userId);
                }else if(!StringUtils.isNullOrEmpty(groupId)){
                    String roleUserNameIds = businessSystemDataService.getUserNameIdByRoleId(groupId);//获取角色人员
                    if(!StringUtils.isNullOrEmpty(roleUserNameIds)){
                        currentUserNameIds.addAll(Arrays.asList(roleUserNameIds.split(",")));
                    }
                    String postUserNameIds = businessSystemDataService.getUserNameIdByPost(groupId);//获取岗位人员
                    if(!StringUtils.isNullOrEmpty(postUserNameIds)){
                        currentUserNameIds.addAll(Arrays.asList(postUserNameIds.split(",")));
                    }
                }
            }
        }
        List<Map> otherhandlers = apiFlowableTaskMapper.getOtherAssigneeByTask(pid,kid);// 获取当前任务以外的流程处理人（包含委派、转办、加签、多实例任务的处理人）,此种情况不考虑关系表数据
        for(Map map : otherhandlers){
            if(map==null || map.isEmpty()) continue;
            String tid = StringUtils.convertNullToEmpty(map.get("taskId"));
            if(tid.equals(taskId)) continue;// 当前任务的处理人数据不再解析
            String userNameId = StringUtils.convertNullToEmpty(map.get("userNameId"));
            String owner = StringUtils.convertNullToEmpty(map.get("owner"));
            // 存在委派关系，要把任务拥有者从当前处理人中移除
//            if(!StringUtils.isNullOrEmpty(userNameId) && !StringUtils.isNullOrEmpty(owner)){
//                currentUserNameIds.removeIf(nameId -> nameId.equals(owner));
//                currentUserNameIds.add(userNameId);
//                continue;
//            }

            if(!StringUtils.isNullOrEmpty(userNameId)){
                // 任务处理人列表加入当前任务的候选人员或处理人
                currentUserNameIds.add(userNameId);
            }else if(!StringUtils.isNullOrEmpty(owner)){
                // userNameId为空的时候，判断owner是否有值，若有说明是任务拥有者，也是当前处理人
                currentUserNameIds.add(owner);
            }
        }
        // 当前处理人员id去重
        currentUserNameIds = currentUserNameIds.stream().distinct().collect(Collectors.toList());
        // 将人员id转为人员名称
        for(String userNameId : currentUserNameIds){
            String currentUserName = businessSystemDataService.getSysUserByUserNameId(userNameId).getUserName();
            if(!StringUtils.isNullOrEmpty(currentUserName)){
                currentUserNames.add(currentUserName);
            }
        }
        return currentUserNames;
    }

    //获取流程实例当前任务（节点所有任务，包含多实例任务或加签的子任务）处理人的方法
    @Override
    public List<String> getCurrentAssigneeUserIdByTask(String taskId) {
        List<String> personIdList = new ArrayList<>();
        List<String> currentUserNameIds = new ArrayList<>();
        List<Map> currenthandlers = apiFlowableTaskMapper.getCurrentAssigneeByTask(taskId);// 获取当前任务分配的处理人、候选人、候选组
        String pid = "";// 当前任务实例id
        String kid = "";// 当前任务节点key id
        for(Map map : currenthandlers){
            if(map==null || map.isEmpty()) continue;
            String userNameId = StringUtils.convertNullToEmpty(map.get("userNameId"));
            String userId = StringUtils.convertNullToEmpty(map.get("userId"));
            String owner = StringUtils.convertNullToEmpty(map.get("owner"));
            String groupId = StringUtils.convertNullToEmpty(map.get("groupId"));
            String procInstId = StringUtils.convertNullToEmpty(map.get("procInstId"));
            String taskDefKey = StringUtils.convertNullToEmpty(map.get("taskDefKey"));
            if(!procInstId.isEmpty() && !taskDefKey.isEmpty() && pid.isEmpty() && kid.isEmpty()){
                pid = procInstId;
                kid = taskDefKey;
            }
            // 任务处理人列表加入当前任务的候选人员或处理人
            if(!StringUtils.isNullOrEmpty(userNameId)){
                currentUserNameIds.add(userNameId);
            }else{
                //只有任务被分配人或拥有者均为空时，去关系表获取候选处理人信息
                // 解析关系表当前任务的候选角色或岗位，获取符合条件的角色或岗位人员id加入任务处理人列表
                if(!StringUtils.isNullOrEmpty(owner)){
                    currentUserNameIds.add(owner);
                }else if(!StringUtils.isNullOrEmpty(userId)){
                    currentUserNameIds.add(userId);
                }else if(!StringUtils.isNullOrEmpty(groupId)){
                    String roleUserNameIds = businessSystemDataService.getUserNameIdByRoleId(groupId);//获取角色人员
                    if(!StringUtils.isNullOrEmpty(roleUserNameIds)){
                        currentUserNameIds.addAll(Arrays.asList(roleUserNameIds.split(",")));
                    }
                    String postUserNameIds = businessSystemDataService.getUserNameIdByPost(groupId);//获取岗位人员
                    if(!StringUtils.isNullOrEmpty(postUserNameIds)){
                        currentUserNameIds.addAll(Arrays.asList(postUserNameIds.split(",")));
                    }
                }
            }
        }
        List<Map> otherhandlers = apiFlowableTaskMapper.getOtherAssigneeByTask(pid,kid);// 获取当前任务以外的流程处理人（包含委派、转办、加签、多实例任务的处理人）,此种情况不考虑关系表数据
        for(Map map : otherhandlers){
            if(map==null || map.isEmpty()) continue;
            String tid = StringUtils.convertNullToEmpty(map.get("taskId"));
            if(tid.equals(taskId)) continue;// 当前任务的处理人数据不再解析
            String userNameId = StringUtils.convertNullToEmpty(map.get("userNameId"));
            String owner = StringUtils.convertNullToEmpty(map.get("owner"));
            // 存在委派关系，要把任务拥有者从当前处理人中移除
//            if(!StringUtils.isNullOrEmpty(userNameId) && !StringUtils.isNullOrEmpty(owner)){
//                currentUserNameIds.removeIf(nameId -> nameId.equals(owner));
//                currentUserNameIds.add(userNameId);
//                continue;
//            }

            if(!StringUtils.isNullOrEmpty(userNameId)){
                // 任务处理人列表加入当前任务的候选人员或处理人
                currentUserNameIds.add(userNameId);
            }else if(!StringUtils.isNullOrEmpty(owner)){
                // userNameId为空的时候，判断owner是否有值，若有说明是任务拥有者，也是当前处理人
                currentUserNameIds.add(owner);
            }
        }
        // 当前处理人员id去重
        currentUserNameIds = currentUserNameIds.stream().distinct().collect(Collectors.toList());
        // 将人员id转为人员名称
        for(String userNameId : currentUserNameIds){
            String personId = businessSystemDataService.getSysUserByUserNameId(userNameId).getPersonId();
            if(!StringUtils.isNullOrEmpty(personId)){
                personIdList.add(personId);
            }
        }
        return personIdList;
    }



    //获取流程实例任务d与处理人的方法
    @Override
    public List<TaskVo> getTaskAssigneeInfo(String taskId) {
        List<TaskVo> currentTaskAssignee = new ArrayList<>();
        List<String> currentUserNameIds = new ArrayList<>();
        List<Map> currenthandlers = apiFlowableTaskMapper.getCurrentAssigneeByTask(taskId);// 获取当前任务分配的处理人、候选人、候选组
        String pid = "";// 当前任务实例id
        String kid = "";// 当前任务节点key id
        for(Map map : currenthandlers){
            if(map==null || map.isEmpty()) continue;
            String userNameId = StringUtils.convertNullToEmpty(map.get("userNameId"));
            String userId = StringUtils.convertNullToEmpty(map.get("userId"));
            String owner = StringUtils.convertNullToEmpty(map.get("owner"));
            String groupId = StringUtils.convertNullToEmpty(map.get("groupId"));
            String procInstId = StringUtils.convertNullToEmpty(map.get("procInstId"));
            String taskDefKey = StringUtils.convertNullToEmpty(map.get("taskDefKey"));
            if(!procInstId.isEmpty() && !taskDefKey.isEmpty() && pid.isEmpty() && kid.isEmpty()){
                pid = procInstId;
                kid = taskDefKey;
            }

            // 任务处理人列表加入当前任务的候选人员或处理人
            if(!StringUtils.isNullOrEmpty(userNameId)){
                currentUserNameIds.add(userNameId);
            }else{
                //只有任务被分配人或拥有者均为空时，去关系表获取候选处理人信息
                // 解析关系表当前任务的候选角色或岗位，获取符合条件的角色或岗位人员id加入任务处理人列表
                if(!StringUtils.isNullOrEmpty(owner)){
                    currentUserNameIds.add(owner);
                }else if(!StringUtils.isNullOrEmpty(userId)){
                    currentUserNameIds.add(userId);
                }else if(!StringUtils.isNullOrEmpty(groupId)){
                    String roleUserNameIds = businessSystemDataService.getUserNameIdByRoleId(groupId);//获取角色人员
                    if(!StringUtils.isNullOrEmpty(roleUserNameIds)){
                        currentUserNameIds.addAll(Arrays.asList(roleUserNameIds.split(",")));
                    }
                    String postUserNameIds = businessSystemDataService.getUserNameIdByPost(groupId);//获取岗位人员
                    if(!StringUtils.isNullOrEmpty(postUserNameIds)){
                        currentUserNameIds.addAll(Arrays.asList(postUserNameIds.split(",")));
                    }
                }
            }
        }
        currentUserNameIds = currentUserNameIds.stream().distinct().collect(Collectors.toList());
        for(String userNameId : currentUserNameIds){
            addTaskAssigneeVo(currentTaskAssignee,taskId,userNameId);
        }
        List<Map> otherhandlers = apiFlowableTaskMapper.getOtherAssigneeByTask(pid,kid);// 获取当前任务以外的流程处理人（包含委派、转办、加签、多实例任务的处理人）,此种情况不考虑关系表数据
        for(Map map : otherhandlers){
            if(map==null || map.isEmpty()) continue;
            String tid = StringUtils.convertNullToEmpty(map.get("taskId"));
            if(tid.equals(taskId)) continue;// 当前任务的处理人数据不再解析
            String userNameId = StringUtils.convertNullToEmpty(map.get("userNameId"));
            String owner = StringUtils.convertNullToEmpty(map.get("owner"));
            if(!StringUtils.isNullOrEmpty(userNameId)){
                // 任务处理人列表加入当前任务的候选人员或处理人
                addTaskAssigneeVo(currentTaskAssignee,tid,userNameId);
            }else if(!StringUtils.isNullOrEmpty(owner)){
                // userNameId为空的时候，判断owner是否有值，若有说明是任务拥有者，也是当前处理人
                addTaskAssigneeVo(currentTaskAssignee,tid,owner);
            }
        }
        return currentTaskAssignee;
    }

    private void addTaskAssigneeVo(List currentTaskAssignee,String taskId,String userNameId){
        TaskVo taskVo = new TaskVo();
        taskVo.setTaskId(taskId);
        taskVo.setUserNameId(userNameId);
        currentTaskAssignee.add(taskVo);
    }

    @Override
    public Map<String, Object> getActNameByProcessInstanceIdList(List<String> processInstanceIdList) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        if (DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)) {
            resultList = apiFlowableTaskMapper.getActNameByProcessInstanceIdListOracle(processInstanceIdList);
        } else if (DriverClassName.MYSQL_NAME.equals(driverClassName)) {
            resultList = apiFlowableTaskMapper.getActNameByProcessInstanceIdListMySql(processInstanceIdList);
        }
        Map<String, Object> resultMapHasTaskId = resultList.stream().filter(r -> StringUtils.isNotEmpty((String) r.get("task_id_"))).collect(Collectors.toMap(r -> Objects.toString(r.get("proc_inst_id_")), r -> r, (r1, r2) -> r1));
        Map<String, Object> resultMap = resultList.stream().collect(Collectors.toMap(r -> Objects.toString(r.get("proc_inst_id_")), r -> r, (r1, r2) -> r1));
        resultMap.forEach((k,v) -> {
            HashMap map = (HashMap)v;
            if (map.get("task_id_") == null){
                HashMap hasTaskIdMap = (HashMap) resultMapHasTaskId.get(k);
                if (hasTaskIdMap != null && hasTaskIdMap.get("task_id_") != null){
                    map.put("task_id_", hasTaskIdMap.get("task_id_"));
                }
            }
        });
        return resultMap;
    }

    @Override
    public Map<String, Object> geCurrentAssigneeByTaskIdList(List<String> taskIdList) {
        Map<String, Object> resultMap = new HashMap<>();
        for (String taskId : taskIdList) {
            List<String> currentAssignee = getCurrentAssigneeByTask(taskId);
            resultMap.put(taskId, currentAssignee);
        }
        return resultMap;
    }
}
