package com.sbtr.workflow.service;


import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.vo.ActDeModelVo;
import com.sbtr.workflow.vo.TaskVo;
import org.flowable.ui.modeler.domain.Model;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 流程模型
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:35:23
 */
public interface ApiFlowableModelService {

    /**
     *  流程部署
     *
     * @param modelId      模型Id
     * @param categoryCode 流程分类
     * @Return java.lang.Object
     */
    Object deployModelId(String modelId, String categoryCode);


    /**
     *  流程模型删除
     *
     * @param modelId 模型Id
     * @Return java.lang.Object
     */
    Object deleteModel(String modelId);


    /**
     *  显示xml
     *
     * @param modelId  modelId
     * @param response
     * @Return void
     */
    void loadXmlByModelId(String modelId, HttpServletResponse response);


    /**
     *  导出bpmn.xml
     *
     * @param modelId  modelId
     * @param response
     * @Return void
     */
    void downLoadXmlByModelId(String modelId, HttpServletResponse response);


    /**
     *  流程模型查询
     *
     * @param pageParam 分页参数
     * @param modelName 模型名称
     * @param modelKey  模型key
     * @param processModelType  流程模型类型  1 自定义流程界面  2 托拉拽界面
     * @Return void
     */
    PageSet<ActDeModelVo> getPageSet(PageParam pageParam, String modelName, String modelKey,String modelType);

    /**
     *  根据模型key查询所有版本的流程模型数据
     *
     * @param pageParam 分页参数
     * @param modelKey  模型key
     * @Return void
     */
    PageSet<ActDeModelVo> getListByModelKey(PageParam pageParam, String modelKey);

    /**
     *  设置主版本
     *
     * @param procdefId 流程定义Id
     * @param modelKey  模型key
     * @param name  模型name
     * @Return void
     */
    Object setMajorVersion(String procdefId, String modelKey,String name);


    Model getModelById(String modelId);

    String getModelNameById(String modelId);

    /**
     *  流程结合表单进行流程部署 特殊
     *
     * @param modelId      流程定义Id
     * @param categoryCode 模型key
     * @param procdefId    模型key
     * @Return void
     */
    Object processDeployment(String modelId, String categoryCode, String procdefId);


    /**
     *  流程模型删除 特殊
     *
     * @param modelId 模型Id
     * @Return java.lang.Object
     */
    Object deleteModelByModelId(String modelId, String procdefId,int delType);


    /**
     *  根据模型key去act_de_model查询数据
     *
     * @param modelKey 模型Id
     * @Return java.lang.Object
     */
    List<ActDeModelVo> getDataByModelKey(String modelKey);

    /**
     * 根据模型id删除act_de_model表数据
     * @param modelId
     */
    void deleteDateByModelId(String modelId);

    /**
     *  审批意见悬浮
     *
     * @param procdefId 模型Id
     * @param nodeId 模型Id
     * @param processInstanceId 模型Id
     * @Return java.lang.Object
     */
    List<TaskVo> moveUserTaskUpper(String procdefId, String nodeId,String processInstanceId);



    Object submitProcess();

    /**
     *  模型验证
     *
     * @param modelXml 模型xml数据
     * @Return java.lang.Object
     */
    Object validatorModel(String modelXml);

    /**
     *  根据act_de_model表id字段更新processModelType字段
     *
     * @param id 模型xml数据
     * @param processModelType 模型xml数据
     * @Return java.lang.Object
     */
    Integer updateProcessModelTypeById(String id, String processModelType);

    PageSet<ActDeModelVo> getModleList(PageParam pageParam, String modelName, String modelKey, String modelType);

    Object copyProcess(String modelId, String categoryCode, String procdefIds);
}
