package com.lms.examine.examinesubjecttype.service;

import com.lms.common.service.BaseService;
import com.lms.examine.feign.model.Examinesubjecttype;

import java.util.List;
import java.util.Map;

public interface ExaminesubjecttypeService extends BaseService<Examinesubjecttype> {

    List<Examinesubjecttype> getExaminesubjecttypeByIdAndType(String examineid, String subjecttype);

    List<Examinesubjecttype> getExaminesubjecttypeWithoutEmpty(String examineid);

    List<Examinesubjecttype> getExaminesubjecttypeById(String Id);

    List<Examinesubjecttype> getByExamineIdList(List<String> examineIdList);

    List<Map<String, Object>> listSQLQuery(String sql);
}
