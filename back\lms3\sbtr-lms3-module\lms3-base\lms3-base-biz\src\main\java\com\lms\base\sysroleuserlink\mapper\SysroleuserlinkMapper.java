package com.lms.base.sysroleuserlink.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.base.feign.model.Sysroleuserlink;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SysroleuserlinkMapper extends BaseMapper<Sysroleuserlink> {


    @Select(value = "delete from p_sysroleuserlink where userid = #{userid}")
    void deleteRoleLinkByUser(@Param("userid") String userid);

    @Select(value = "select m.name from p_sysroleuserlink s inner join p_user m on m.id=s.userid where s.roleid=#{roleId}")
    List<String> getUserNamesBySysRoleLink(@Param("roleId") String roleId);
}
