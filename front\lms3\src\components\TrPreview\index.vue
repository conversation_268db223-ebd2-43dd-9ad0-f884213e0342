<template>
  <div
    class="tr-preview"
    :class="[customClass, { 'tr-preview--disabled': disabled }]"
    :style="containerStyle"
  >
    <!-- 图片预览 -->
    <div v-if="isImageFile" class="tr-preview__image">
      <img
        :src="currentFileUrl"
        :alt="file.name"
        class="tr-preview__image-content"
        @load="handleImageLoad"
        @error="handleImageError"
      />
      <FileTypeBadge
        v-if="showTypeBadge"
        :type="FileType.IMAGE"
        :config="fileTypeConfigs[FileType.IMAGE]"
      />
    </div>

    <!-- 视频预览 -->
    <div v-else-if="isVideoFile" class="tr-preview__video">
      <video
        :src="currentFileUrl"
        class="tr-preview__video-content"
        controls
        preload="metadata"
        @loadstart="handleVideoLoad"
        @error="handleVideoError"
      >
        您的浏览器不支持视频播放
      </video>
      <FileTypeBadge
        v-if="showTypeBadge"
        :type="FileType.VIDEO"
        :config="fileTypeConfigs[FileType.VIDEO]"
      />
    </div>

    <!-- 3D模型预览 -->
    <div v-else-if="is3DModelFile" class="tr-preview__model">
      <div class="tr-preview__model-container">
        <div class="tr-preview__model-thumbnail">
          <iframe
            ref="modelIframe"
            :src="modelIframeSrc"
            class="tr-preview__model-iframe"
            frameborder="0"
            allowfullscreen
            allow="fullscreen"
            scrolling="no"
            @load="handleModelLoad"
            @error="handleModelError"
          />

          <!-- 加载遮罩 -->
          <div v-if="modelLoading" class="tr-preview__model-loading">
            <a-spin size="large" />
            <p>正在加载3D模型...</p>
          </div>

          <!-- 错误遮罩 -->
          <div v-if="modelError" class="tr-preview__model-error">
            <WarningOutlined class="tr-preview__model-error-icon" />
            <p>模型加载失败</p>
            <a-button type="primary" size="small" @click="retryLoadModel">
              重新加载
            </a-button>
          </div>
        </div>
      </div>

      <FileTypeBadge
        v-if="showTypeBadge"
        :type="FileType.MODEL_3D"
        :config="fileTypeConfigs[FileType.MODEL_3D]"
      />

      <!-- 模型控制按钮 -->
      <div class="tr-preview__model-controls">
        <a-button
          v-if="showFullscreenButton"
          type="primary"
          ghost
          class="tr-preview__model-control-btn"
          @click="handleFullscreen"
        >
          <template #icon>
            <FullscreenOutlined />
          </template>
          全屏预览
        </a-button>
        <a-button
          v-if="showEditButton"
          type="primary"
          ghost
          class="tr-preview__model-control-btn"
          @click="handleEdit"
        >
          <template #icon>
            <EditOutlined />
          </template>
          编辑模型
        </a-button>
      </div>
    </div>

    <!-- 其他文件类型 -->
    <div v-else class="tr-preview__file">
      <div class="tr-preview__file-container">
        <FileOutlined class="tr-preview__file-icon" />
        <p class="tr-preview__file-name">{{ file.name }}</p>
        <p class="tr-preview__file-url">{{ currentFileUrl }}</p>
        <a-button
          v-if="showDownloadButton"
          type="primary"
          class="tr-preview__file-download"
          @click="handleDownload"
        >
          <template #icon>
            <DownloadOutlined />
          </template>
          下载文件
        </a-button>
      </div>

      <FileTypeBadge
        v-if="showTypeBadge"
        :type="FileType.OTHER"
        :config="fileTypeConfigs[FileType.OTHER]"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  FileOutlined,
  DownloadOutlined,
  EditOutlined,
  FullscreenOutlined,
  WarningOutlined,
  PictureOutlined,
  PlayCircleOutlined,
  AppstoreOutlined
} from '@ant-design/icons-vue';
import type { TrPreviewProps, TrPreviewEmits, FileInfo, FileTypeConfig } from './types';
import { FileType } from './types';
import FileTypeBadge from './components/FileTypeBadge.vue';

// 定义Props
const props = withDefaults(defineProps<TrPreviewProps>(), {
  width: '100%',
  height: '100%',
  showTypeBadge: true,
  showDownloadButton: true,
  showEditButton: true,
  showFullscreenButton: false,
  disabled: false,
  modelIframeSrc: '/player/data/GUI/web.html',
  modelEditorUrl: '/editor/data/GUI/STUDIO.html',
  defaultImageUrl: 'https://lms3.oss-cn-guangzhou.aliyuncs.com/model.png'
});

// 定义Emits
const emit = defineEmits<TrPreviewEmits>();

// 响应式数据
const modelIframe = ref<HTMLIFrameElement>();
const modelLoading = ref(false);
const modelError = ref(false);

// 文件类型配置
const fileTypeConfigs: Record<FileType, FileTypeConfig> = {
  [FileType.IMAGE]: {
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'],
    mimeTypes: ['image/'],
    displayName: '图片',
    icon: 'PictureOutlined',
    badgeColor: '#52c41a'
  },
  [FileType.VIDEO]: {
    extensions: ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v'],
    mimeTypes: ['video/'],
    displayName: '视频',
    icon: 'PlayCircleOutlined',
    badgeColor: '#1890ff'
  },
  [FileType.MODEL_3D]: {
    extensions: ['.obj', '.fbx', '.gltf', '.glb', '.dae', '.3ds', '.ply', '.stl', '.blend', '.max', '.ma', '.mb', '.vrp', '.vrpc'],
    mimeTypes: ['model/', 'application/octet-stream'],
    displayName: '3D模型',
    icon: 'AppstoreOutlined',
    badgeColor: '#722ed1'
  },
  [FileType.OTHER]: {
    extensions: [],
    mimeTypes: [],
    displayName: '文件',
    icon: 'FileOutlined',
    badgeColor: '#8c8c8c'
  }
};

// 计算属性
const customClass = computed(() => props.class || '');

const containerStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height
}));

const currentFileUrl = computed(() => {
  return props.file.previewUrl || props.file.url || '';
});

const isImageFile = computed(() => {
  if (props.file.type === FileType.IMAGE) return true;
  return checkFileType(FileType.IMAGE);
});

const isVideoFile = computed(() => {
  if (props.file.type === FileType.VIDEO) return true;
  return checkFileType(FileType.VIDEO);
});

const is3DModelFile = computed(() => {
  if (props.file.type === FileType.MODEL_3D) return true;
  return checkFileType(FileType.MODEL_3D);
});

/**
 * 检查文件类型
 */
function checkFileType(targetType: FileType): boolean {
  if (!currentFileUrl.value) return false;

  const config = fileTypeConfigs[targetType];
  const url = currentFileUrl.value.toLowerCase();

  // 检查扩展名
  const hasExtension = config.extensions.some(ext => url.includes(ext));
  if (hasExtension) return true;

  // 检查MIME类型
  const hasMimeType = config.mimeTypes.some(type => url.includes(type));
  if (hasMimeType) return true;

  // 检查文件URL
  if (props.file.url) {
    const fileUrl = props.file.url.toLowerCase();
    const hasFileExtension = config.extensions.some(ext => fileUrl.includes(ext));
    if (hasFileExtension) return true;

    const hasFileMimeType = config.mimeTypes.some(type => fileUrl.includes(type));
    if (hasFileMimeType) return true;
  }

  return false;
}

// 事件处理函数
const handleImageLoad = () => {
  emit('load', props.file);
};

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = props.defaultImageUrl;
  const error = new Error('图片加载失败');
  emit('error', error, props.file);
};

const handleVideoLoad = () => {
  emit('load', props.file);
};

const handleVideoError = (event: Event) => {
  const error = new Error('视频加载失败');
  emit('error', error, props.file);
};

const handleModelLoad = () => {
  console.log('🎯 3D模型iframe加载完成');
  modelLoading.value = false;
  modelError.value = false;

  // 发送模型数据到iframe
  sendModelDataToIframe();
  emit('load', props.file);
};

const handleModelError = () => {
  console.error('❌ 3D模型iframe加载失败');
  modelLoading.value = false;
  modelError.value = true;
  const error = new Error('3D模型加载失败');
  emit('error', error, props.file);
  message.error('3D模型加载失败');
};

const retryLoadModel = () => {
  if (modelIframe.value) {
    modelLoading.value = true;
    modelError.value = false;
    modelIframe.value.src = modelIframe.value.src;
    emit('retry', props.file);
  }
};

const handleDownload = () => {
  if (!currentFileUrl.value) {
    message.error('文件地址不存在');
    return;
  }

  try {
    const link = document.createElement('a');
    link.href = currentFileUrl.value;
    link.download = props.file.name || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    message.success('开始下载文件');
    emit('download', props.file);
  } catch (error) {
    console.error('下载失败:', error);
    message.error('下载失败');
  }
};

const handleEdit = () => {
  if (!props.file.url) {
    message.error('模型信息不存在');
    return;
  }

  // 构建编辑器URL
  const editorUrl = props.modelEditorUrl;

  // 打开新标签页
  const newWindow = window.open(editorUrl, '_blank');

  if (newWindow) {
    // 等待新窗口加载完成后发送模型数据
    const checkWindow = setInterval(() => {
      try {
        if (newWindow.document && newWindow.document.readyState === 'complete') {
          clearInterval(checkWindow);

          // 发送模型数据到新窗口
          const modelData = {
            type: 'MODEL_EDIT_DATA',
            data: {
              id: props.file.name,
              name: props.file.name,
              fileUrl: props.file.url,
              previewUrl: props.file.previewUrl,
              mode: 'edit'
            }
          };
          newWindow.postMessage(modelData, '*');
          message.success('模型编辑器已在新标签页中打开');
          emit('edit', props.file);
        }
      } catch (error) {
        // 跨域限制，使用延时发送
        if (Date.now() - startTime > 3000) {
          clearInterval(checkWindow);
          setTimeout(() => {
            const modelData = {
              type: 'MODEL_EDIT_DATA',
              data: {
                id: props.file.name,
                name: props.file.name,
                fileUrl: props.file.url,
                previewUrl: props.file.previewUrl,
                mode: 'edit'
              }
            };
            newWindow.postMessage(modelData, '*');
          }, 1000);
          message.success('模型编辑器已在新标签页中打开');
          emit('edit', props.file);
        }
      }
    }, 100);

    const startTime = Date.now();
  } else {
    message.error('无法打开新标签页，请检查浏览器设置');
  }
};

const handleFullscreen = () => {
  emit('fullscreen', props.file);
};

/**
 * 发送模型数据到iframe
 */
const sendModelDataToIframe = () => {
  if (!modelIframe.value || !modelIframe.value.contentWindow || !props.file.url) return;

  // 等待一段时间确保iframe完全加载
  setTimeout(() => {
    const modelDataSrc = props.file.url;
    try {
      modelIframe.value!.contentWindow!.postMessage(modelDataSrc, '*');
      message.success('模型预览已打开');
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  }, 1000);
};

// 生命周期
onMounted(() => {
  if (is3DModelFile.value) {
    modelLoading.value = true;
  }
});

onUnmounted(() => {
  // 清理工作
});
</script>

<style lang="scss" scoped>
.tr-preview {
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius-md);
  background-color: var(--gray-100);

  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  // 图片预览样式
  &__image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &-content {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: var(--border-radius-md);
    }
  }

  // 视频预览样式
  &__video {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &-content {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: var(--border-radius-md);
    }
  }

  // 3D模型预览样式
  &__model {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &-container {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
    }

    &-thumbnail {
      width: 100%;
      height: 100%;
      border-radius: var(--border-radius-md);
      overflow: hidden;
      position: relative;
      background-color: #f5f5f5;
    }

    &-iframe {
      width: 100%;
      height: 100%;
      border: none;
      display: block;
      background: white;
      transform-origin: center center;
      transition: transform 0.3s ease;
    }

    &-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.9);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-4);
      z-index: 20;

      p {
        color: var(--gray-600);
        margin: 0;
        font-size: var(--text-sm);
      }
    }

    &-error {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.95);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-3);
      z-index: 20;

      &-icon {
        font-size: 48px;
        color: var(--error-color);
      }

      p {
        color: var(--error-color);
        margin: 0;
        font-size: var(--text-base);
        font-weight: var(--font-medium);
      }
    }

    &-controls {
      position: absolute;
      bottom: var(--spacing-4);
      right: var(--spacing-4);
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
      z-index: 15;
    }

    &-control-btn {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      background-color: rgba(255, 255, 255, 0.9);
      border-color: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(8px);
      box-shadow: var(--shadow-sm);

      &:hover {
        background-color: rgba(255, 255, 255, 1);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
      }
    }
  }

  // 文件预览样式
  &__file {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &-container {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-4);
      text-align: center;
      padding: var(--spacing-6);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    &-icon {
      font-size: 80px;
      color: white;
    }

    &-name {
      font-size: var(--text-xl);
      font-weight: var(--font-medium);
      color: white;
      margin: 0;
      word-break: break-all;
    }

    &-url {
      font-size: var(--text-sm);
      color: rgba(255, 255, 255, 0.8);
      margin: 0;
      word-break: break-all;
      max-width: 80%;
    }

    &-download {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      background-color: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.3);
      color: white;

      &:hover {
        background-color: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}

// 文件类型徽章组件
.file-type-badge {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
  z-index: 10;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: white;
  background-color: var(--badge-color, #8c8c8c);
}
</style>
