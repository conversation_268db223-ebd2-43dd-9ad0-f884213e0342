<template>
  <div class="tr-upload-example">
    <h2>TrUpload 组件示例</h2>
    
    <!-- 基础用法 -->
    <div class="example-section">
      <h3>基础用法</h3>
      <TrUpload v-model:fileList="basicFileList" @change="handleBasicChange" />
    </div>

    <!-- 多文件上传 -->
    <div class="example-section">
      <h3>多文件上传</h3>
      <TrUpload
        v-model:fileList="multipleFileList"
        :multiple="true"
        :max-size="50"
        @change="handleMultipleChange"
      />
    </div>

    <!-- 图片视频上传 -->
    <div class="example-section">
      <h3>图片视频上传</h3>
      <TrUpload
        v-model:fileList="imageVideoFileList"
        :accept="['image/*', 'video/*']"
        :show-preview="true"
        drag-text="点击或拖拽图片、视频文件到此区域上传"
        hint="支持 jpg、png、gif、mp4、avi 等格式"
        @change="handleImageVideoChange"
        @success="handleSuccess"
        @error="handleError"
      />
    </div>

    <!-- 3D模型上传 -->
    <div class="example-section">
      <h3>3D模型上传</h3>
      <TrUpload
        v-model:fileList="modelFileList"
        :accept="['.obj', '.fbx', '.gltf', '.glb', '.vrp', '.vrpc']"
        :max-size="200"
        drag-text="点击或拖拽3D模型文件到此区域上传"
        hint="支持 obj、fbx、gltf、glb、vrp、vrpc 等格式，最大200MB"
        @change="handleModelChange"
      />
    </div>

    <!-- 分片上传示例 -->
    <div class="example-section">
      <h3>分片上传示例</h3>
      <p class="example-desc">大文件或特定类型文件（如.vrp/.vrpc）会自动使用分片上传，支持断点续传</p>
      <TrUpload
        v-model:fileList="chunkUploadFileList"
        :accept="['.vrp', '.vrpc', '.zip', '.rar', '.7z']"
        :max-size="500"
        :auto-upload="true"
        drag-text="点击或拖拽大文件到此区域，将自动使用分片上传"
        hint="支持 vrp、vrpc、zip、rar、7z 等格式，最大500MB，自动分片上传"
        @change="handleChunkUploadChange"
        @success="handleChunkUploadSuccess"
        @error="handleChunkUploadError"
      />
      
      <!-- 分片上传进度展示 -->
      <div v-if="chunkUploadProgress > 0 && chunkUploadProgress < 100" class="chunk-progress">
        <h4>分片上传进度</h4>
        <a-progress 
          :percent="chunkUploadProgress" 
          :status="chunkUploadStatus"
          :format="(percent) => `${percent}% (${chunkUploadedChunks}/${chunkTotalChunks} 分片)`"
        />
        <p class="progress-desc">{{ chunkProgressDesc }}</p>
      </div>
    </div>

    <!-- 图片视频预览示例 -->
    <div class="example-section">
      <h3>图片视频预览示例</h3>
      <p class="example-desc">支持图片和视频的预览功能，上传完成后直接显示预览</p>
      <TrUpload
        v-model:fileList="previewFileList"
        :accept="['image/*', 'video/*']"
        :show-preview="true"
        :multiple="true"
        drag-text="点击或拖拽图片、视频文件到此区域，上传后直接预览"
        hint="支持 jpg、png、gif、mp4、avi 等格式，上传完成后自动显示预览"
        @change="handlePreviewChange"
        @success="handlePreviewSuccess"
        @error="handlePreviewError"
      />
    </div>

    <!-- 自定义上传 -->
    <div class="example-section">
      <h3>自定义上传</h3>
      <TrUpload
        v-model:fileList="customFileList"
        :custom-upload="customUpload"
        :auto-upload="false"
        drag-text="点击或拖拽文件到此区域，将使用自定义上传逻辑"
        hint="自定义上传函数示例"
        @change="handleCustomChange"
      />
    </div>

    <!-- 禁用状态 -->
    <div class="example-section">
      <h3>禁用状态</h3>
      <TrUpload
        v-model:fileList="disabledFileList"
        :disabled="true"
        drag-text="此上传区域已禁用"
        hint="无法上传文件"
      />
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { message } from "ant-design-vue";
import type { UploadFile } from "ant-design-vue";
import { TrUpload } from "./index";

// 基础用法
const basicFileList = ref<UploadFile[]>([]);
const handleBasicChange = (info: any) => {
  console.log("基础用法 - 文件变化:", info);
};

// 多文件上传
const multipleFileList = ref<UploadFile[]>([]);
const handleMultipleChange = (info: any) => {
  console.log("多文件上传 - 文件变化:", info);
};

// 图片视频上传
const imageVideoFileList = ref<UploadFile[]>([]);
const handleImageVideoChange = (info: any) => {
  console.log("图片视频上传 - 文件变化:", info);
};

// 3D模型上传
const modelFileList = ref<UploadFile[]>([]);
const handleModelChange = (info: any) => {
  console.log("3D模型上传 - 文件变化:", info);
};

// 分片上传
const chunkUploadFileList = ref<UploadFile[]>([]);
const chunkUploadProgress = ref(0);
const chunkUploadStatus = ref<'active' | 'success' | 'exception'>('active');
const chunkUploadedChunks = ref(0);
const chunkTotalChunks = ref(0);
const chunkProgressDesc = ref('');

const handleChunkUploadChange = (info: any) => {
  console.log("分片上传 - 文件变化:", info);
  const { file } = info;
  
  // 更新分片上传进度
  if (file.status === 'uploading' && file.percent !== undefined) {
    chunkUploadProgress.value = file.percent;
    chunkUploadStatus.value = 'active';
    
    // 模拟分片信息（实际项目中应该从上传响应中获取）
    if (file.size > 10 * 1024 * 1024) { // 大于10MB的文件
      const chunkSize = 2 * 1024 * 1024; // 2MB per chunk
      chunkTotalChunks.value = Math.ceil(file.size / chunkSize);
      chunkUploadedChunks.value = Math.ceil((file.percent / 100) * chunkTotalChunks.value);
      chunkProgressDesc.value = `正在上传第 ${chunkUploadedChunks.value} 个分片，共 ${chunkTotalChunks.value} 个分片`;
    }
  } else if (file.status === 'done') {
    chunkUploadProgress.value = 100;
    chunkUploadStatus.value = 'success';
    chunkProgressDesc.value = '分片上传完成，文件合并成功';
  } else if (file.status === 'error') {
    chunkUploadStatus.value = 'exception';
    chunkProgressDesc.value = '分片上传失败';
  }
};

const handleChunkUploadSuccess = (file: UploadFile, response: any) => {
  message.success(`文件 ${file.name} 分片上传成功`);
  console.log("分片上传成功:", file, response);
};

const handleChunkUploadError = (file: UploadFile, error: any) => {
  message.error(`文件 ${file.name} 分片上传失败`);
  console.log("分片上传失败:", file, error);
};

// 图片视频预览
const previewFileList = ref<UploadFile[]>([]);

const handlePreviewChange = (info: any) => {
  console.log("预览上传 - 文件变化:", info);
};

const handlePreviewSuccess = (file: UploadFile, response: any) => {
  message.success(`文件 ${file.name} 上传成功，已显示预览`);
  console.log("预览上传成功:", file, response);
};

const handlePreviewError = (file: UploadFile, error: any) => {
  message.error(`文件 ${file.name} 上传失败`);
  console.log("预览上传失败:", file, error);
};

// 自定义上传
const customFileList = ref<UploadFile[]>([]);
const customUpload = async (file: File) => {
  // 模拟自定义上传逻辑
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (Math.random() > 0.5) {
        resolve({
          fileUrl: `https://example.com/uploads/${file.name}`,
          fileType: file.type,
          fileName: file.name,
          fileSize: file.size
        });
      } else {
        reject(new Error("自定义上传失败"));
      }
    }, 2000);
  });
};
const handleCustomChange = (info: any) => {
  console.log("自定义上传 - 文件变化:", info);
};

// 禁用状态
const disabledFileList = ref<UploadFile[]>([]);

// 事件处理
const handleSuccess = (file: UploadFile, response: any) => {
  message.success(`文件 ${file.name} 上传成功`);
  console.log("上传成功:", file, response);
};

const handleError = (file: UploadFile, error: any) => {
  message.error(`文件 ${file.name} 上传失败`);
  console.log("上传失败:", file, error);
};
</script>

<style lang="scss" scoped>
.tr-upload-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
  }

  .example-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    background: #fff;

    h3 {
      margin-bottom: 16px;
      color: #666;
      font-size: 16px;
    }

    .example-desc {
      margin-bottom: 16px;
      color: #999;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .chunk-progress {
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    h4 {
      margin-bottom: 12px;
      color: #333;
      font-size: 14px;
    }

    .progress-desc {
      margin-top: 8px;
      font-size: 12px;
      color: #666;
    }
  }


}
</style> 