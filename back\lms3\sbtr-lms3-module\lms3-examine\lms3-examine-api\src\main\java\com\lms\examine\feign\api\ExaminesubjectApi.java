package com.lms.examine.feign.api;

import com.lms.common.config.FeignConfig;
import com.lms.common.model.Result;
import com.lms.examine.feign.fallback.ExaminesubjectApiFallbackFactory;
import com.lms.examine.feign.model.Examinesubject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "examinesubjectApi",value = "lms-examine", path = "/examinesubject",configuration = FeignConfig.class,fallbackFactory = ExaminesubjectApiFallbackFactory.class)
public interface ExaminesubjectApi {

    @GetMapping("/getSubjectByExamineId")
    Result<List<Examinesubject>> getSubjectByExamineId(@RequestParam String id);

    @PostMapping("/save")
    Result save(@RequestBody Examinesubject examinesubject);

    @GetMapping("/getExaminesubjectsBySubjectId")
    Result<List<Examinesubject>> getExaminesubjectsBySubjectId(@RequestParam String examid, @RequestParam String subjectid);

    @GetMapping("/isExistBySubjectId")
    Result<List<Examinesubject>> isExistBySubjectId(@RequestParam String subjectId);


}
