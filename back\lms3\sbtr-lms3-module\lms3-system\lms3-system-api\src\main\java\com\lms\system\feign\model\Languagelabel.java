package com.lms.system.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)

@TableName("b_languagelabel")
@Data
@ApiModel(value = "多语言标签实体类")
public class Languagelabel extends BaseModel {

    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private String id;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("中文内容")
    private String chinese;

    @ApiModelProperty("语言")
    private String language;

    @ApiModelProperty("翻译内容")
    private String content;

    @ApiModelProperty("创建日期")
    private String createdate;

    @ApiModelProperty("所属模块描述")
    private String moduledesc;

    @ApiModelProperty("状态")
    private int status;


}
