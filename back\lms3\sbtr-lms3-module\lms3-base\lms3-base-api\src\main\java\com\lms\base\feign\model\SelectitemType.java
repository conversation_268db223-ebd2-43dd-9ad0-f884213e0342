package com.lms.base.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * BSelectitemtypeId entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)

@TableName("b_selectitemtype")
@Data
@ApiModel(value = "数据字典类型实体类")
public class SelectitemType extends BaseModel {

    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private String id;

    @ApiModelProperty("名称")
    private String objname;

    @ApiModelProperty("序号")
    private int seqno;

    @ApiModelProperty("描述")
    private String objdesc;

    @ApiModelProperty("状态")
    private int status;

    @ApiModelProperty("是否是系统级")
    private int issystemlevel;

}
