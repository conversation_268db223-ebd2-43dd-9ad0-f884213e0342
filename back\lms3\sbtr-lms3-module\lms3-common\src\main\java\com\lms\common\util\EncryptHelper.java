package com.lms.common.util;

import org.apache.commons.codec.binary.Base64;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 加密常用工具方法类。
 *
 * <AUTHOR>
 */
public final class EncryptHelper {

	public static String md5Password(String pw) {
		// 构造字符串：pssword{salt}格式
		String newString = pw + "{" + ConstParamUtil.MD5SALT + "}";
		String md5Password = encodeMd5(newString);
		return md5Password;
	}

	/**
	 * 将指定的字符串用MD5加密（128位）
	 *
	 * @param strIn
	 * @return
	 * @throws NoSuchAlgorithmException
	 */
	public static String encodeMd5(String strIn) {
		String result = null;
		// 用来将字节转换成16进制表示的字符
		char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };

		try {
			// 返回实现指定摘要算法的MessageDigest对象
			MessageDigest mDigest = MessageDigest.getInstance("MD5");
			// 使用UTF-8编码将strIn字符编码并保存到source字节数组
			byte[] source = strIn.getBytes("utf-8");
			// 使用指定的byte数组更新摘要
			mDigest.update(source);
			// 通过执行诸如填充之类的最终操作完成哈希计算，结果是一个128位的长整数
			byte[] tmp = mDigest.digest();
			// 用16进制表示需要32位
			char[] str = new char[32];

			for (int i = 0, j = 0; i < 16; i++) {
				// j表示转换结果中对应的字符位置
				// 从第一个字节开始，对MD5的每一个字节转换成16进制
				byte b = tmp[i];
				// 取字节中高四位的数字转换
				// 无符号右移运算符>>>,它总是在左边补0
				str[j++] = hexDigits[b >>> 4 & 0xf];
				// 取字节中低4位的数字转换
				str[j++] = hexDigits[b & 0xf];
			}

			result = new String(str);
		} catch (Exception e) {
			e.printStackTrace(); // To change body of catch statement use File |
		}
		return result;
	}

	public static String decodeMd5(String encodeStr){
		byte[] b = encodeStr.getBytes();
		Base64 base64 = new Base64();
		b = base64.decodeBase64(b);
		String s = "";
		try {
			s = new String (b,"UTF-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return s;
	}

	public static void main(String[] args) throws NoSuchAlgorithmException{

	String test = encodeMd5("Sbtr.123456"+"{"+ConstParamUtil.MD5SALT+"}");
	System.out.println(test);
	}

}
