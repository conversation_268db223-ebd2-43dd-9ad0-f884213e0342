package com.sbtr.workflow.service;

import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import com.sbtr.workflow.model.ActMyNodeNotice;

import java.util.List;

/**
 * 流程每个节点所对应通知
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-12-09 11:58:35
 */
public interface ActMyNodeNoticeService extends WorkflowBaseService<ActMyNodeNotice, String> {

    /**
     * 分页
     *
     * @return
     * @Param pageParam 分页参数
     * @Param filterSort 过滤排序字段
     */
    PageSet<ActMyNodeNotice> getPageSet(PageParam pageParam, String filterSort);

    /**
     * 批量删除
     *
     * @return
     * @Param uuid 主键
     */
	int executeDeleteBatch(String[] uuids);

    /**
     * 批量插入
     *
     * @return
     * @Param uuid 主键
     */
    Integer insertList( List<ActMyNodeNotice> list);

    List<ActMyNodeNotice> getListByNodeIdAndModelKeyAndProcdefId(String nodelId, String modelKey, String procdefId);

    /**
     * 根据模型key以及流程定义 查询详细数据
     *
     * @return
     */
    List<ActMyNodeNotice> getListByActDeModelKeyAndProcdefId(String key, String procdefIds);

    /**
     * 根据模型key以及流程定义更新数据
     *
     * @return
     */
    Integer updateProcdefIdByModelKey(String key, String procdefId);

    /**
     * 根据模型key以及流程定义 吧流程定义Id置为空
     *
     * @return
     */
    Integer updateProcdefIdIsNull(String key, String id);

    /**
     * 据模型key以及流程定义 删除数据
     *
     * @return
     */
    int deleteByModelKeyAnProcdefId(String key, String id);

    Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id);
}
