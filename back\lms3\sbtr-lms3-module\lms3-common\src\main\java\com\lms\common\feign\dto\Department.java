package com.lms.common.feign.dto;

import com.lms.common.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class Department extends BaseModel {

    private String id;

    private String code;

    private String name;

    private String shortname;

    private Integer seqno;

    private Integer status;

    private String pid;

    private String parentname;

    private String fullpath;

    private String fullpathname;

    private Integer orgFlag; //组训单位标识

}
