package com.lms.system.users.service.impl;

import com.lms.system.users.mapper.VUsersMapper;
import com.lms.system.feign.model.VUsers;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.system.users.service.VUsersService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("vusersService")
public class VUsersServiceImpl extends BaseServiceImpl<VUsersMapper, VUsers> implements VUsersService {

	@Resource
	private VUsersMapper vUsersMapper;

}
