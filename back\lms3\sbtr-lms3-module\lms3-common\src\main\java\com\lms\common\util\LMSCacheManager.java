package com.lms.common.util;

import org.apache.poi.hwpf.usermodel.DateAndTime;
import reactor.core.publisher.Timed;

import java.util.Calendar;
import java.util.HashMap;

public class LMSCacheManager {
	// 定义一个内存存储区域
	private static HashMap<String, Object> cacheMap = new HashMap<String, Object>();
	private static HashMap<String, Object> dataMap = new HashMap<String, Object>();
	public LMSCacheManager() {
	}

	public static void addDataToCach(String key, Object value,
                                     DateAndTime datetime) {
		dataMap.put(key, value);
		cacheMap.put(key, datetime);
	}

	public static void addDataToCach(String key, Object value, Timed timerdiff) {
		dataMap.put(key, value);
		cacheMap.put(key, timerdiff);
	}

	public static Object GetDataFromCach(String key) {
		if (dataMap.containsKey(key)) {
			return dataMap.get(key);
		} else {
			return null;
		}
	}

	public static void RefreshCache(String key, Object value, int min) {
		if (dataMap.containsKey(key) && cacheMap.containsKey(key)) {
			DateAndTime dataTime = (DateAndTime) cacheMap.get(key);
			dataTime.getDate().add(Calendar.MINUTE, min);
			dataMap.remove(key);
			cacheMap.remove(key);
			addDataToCach(key, value, dataTime);
		}
	}

	public static void removeCache(String key) {
		dataMap.remove(key);
		cacheMap.remove(key);
	}
}
