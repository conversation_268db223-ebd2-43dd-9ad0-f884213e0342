package com.lms.base.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import com.lms.common.model.SearchContent;
import com.lms.common.model.SearchName;
import com.lms.common.model.SearchSelectItemContent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@TableName("u_course")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "课件对象")
public class Course extends BaseModel {

    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private String id;

    @TableField("COURSENAME")
    @ApiModelProperty("课程名称")
    @SearchContent
    private String courseName;  //科目名称

    @TableField("COURSETYPE")
    @ApiModelProperty("课程类型")
    @SearchSelectItemContent
    private String courseType;  //科目类型（基础理论学习、岗位演示学习）

    @TableField("VERSION")
    @ApiModelProperty("版本")
    private String version;     //版本

    @TableField("COURSECONTENT")
    @ApiModelProperty("内容")
    @SearchContent
    private String courseContent;   //内容

    @TableField("CLASSHOUR")
    @ApiModelProperty("课时")
    private BigDecimal classHour;  //课时

    @TableField("TEACHERID")
    @ApiModelProperty("教员ID")
    private String teacherId;       //教员ID

    @TableField("OPENFLAG")
    @ApiModelProperty("是否公开")
    private int openFlag;    //是否公开

    @TableField("EDITDATE")
    @ApiModelProperty("编辑时间")
    private String editDate;    //编辑时间

    @TableField("CREATORID")
    @ApiModelProperty("创建时间")
    private String creatorid;

    @TableField("STATUS")
    @ApiModelProperty("状态")
    private Integer status;     //状态

    @TableField("COMPONENTID")
    @ApiModelProperty("构型树节点")
    private String componentId;       //构型树节点

    @TableField(exist = false)
    @ApiModelProperty("拷贝ID")
    private String copyid;

    @TableField("TEACHERNAME")
    @ApiModelProperty("教员名称")
    @SearchContent
    private String teacherName;

    @TableField("NUMBER")
    @ApiModelProperty("编号")
    @SearchName
    private String number; // 编号

    @ApiModelProperty("型号ID全路径")
    private String equipmentid;

    @TableField(exist = false)
    @ApiModelProperty("型号名称")
    private String equipmentname;

    @TableField(exist = false)
    @ApiModelProperty("型号ID")
    private String lastequipmentid;

}
