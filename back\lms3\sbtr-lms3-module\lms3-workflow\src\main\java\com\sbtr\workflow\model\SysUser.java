package com.sbtr.workflow.model;

import cn.ewsd.common.model.MCoreBase;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户信息表
 *
 * <AUTHOR>
 * @Version V5.4.21
 * @Email <EMAIL>
 * @Date 2023-02-08 11:40:27
 */
@Table(name = "sys_user")
public class SysUser{

    /**
     * 用户账号
     */
    private String userNameId;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 组织机构ID
     */
    private String userOrgId;
    /**
     * 用户岗位
     */
    private String userPosition;
    /**
     * 用户角色
     */
    private String userGroup;
    /**
     * 用户直属主管
     */
    private String userReportsTo;

    /**
     * person表id
     */
    private String personId;

    public String getUserNameId() {
        return userNameId;
    }

    public void setUserNameId(String userNameId) {
        this.userNameId = userNameId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserOrgId() {
        return userOrgId;
    }

    public void setUserOrgId(String userOrgId) {
        this.userOrgId = userOrgId;
    }

    public String getUserPosition() {
        return userPosition;
    }

    public void setUserPosition(String userPosition) {
        this.userPosition = userPosition;
    }

    public String getUserGroup() {
        return userGroup;
    }

    public void setUserGroup(String userGroup) {
        this.userGroup = userGroup;
    }

    public String getUserReportsTo() {
        return userReportsTo;
    }

    public void setUserReportsTo(String userReportsTo) {
        this.userReportsTo = userReportsTo;
    }

    public String getPersonId() {
        return personId;
    }

    public void setPersonId(String personId) {
        this.personId = personId;
    }

    @Override
    public String toString() {
        return "SysUser{" +
                "userNameId='" + userNameId + '\'' +
                ", userName='" + userName + '\'' +
                ", userOrgId='" + userOrgId + '\'' +
                ", userPosition='" + userPosition + '\'' +
                ", userGroup='" + userGroup + '\'' +
                ", userReportsTo='" + userReportsTo + '\'' +
                ", personId='" + personId + '\'' +
                '}';
    }
}
