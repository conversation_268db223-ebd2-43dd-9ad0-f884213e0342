package com.lms.common.enums;

import org.apache.commons.collections.CollectionUtils;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum StatusEnum {
    DEL(0, "删除"),
    NEW(1, "新建"),
    AUDIT(10, "审核中"),
    APPROVE(20, "发布"),
    IMPLEMENTTING(30,"执行中"),
    FREEZE(40,"冻结"),
    STOP(45,"终止"),
    END(50,"结束");

    private final int statusCode;

    private final String statusMsg;

    StatusEnum(int statusCode, String statusMsg) {
        this.statusCode = statusCode;
        this.statusMsg = statusMsg;
    }

    public static String getMsgByCode(int statusCode) {
        List<StatusEnum> list = Arrays.stream(StatusEnum.values()).filter(t -> t.getStatusCode() == statusCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)){
            return list.get(0).getStatusMsg();
        }
        return "";
    }

    public int getStatusCode() {
        return statusCode;
    }

    public String getStatusMsg() {
        return statusMsg;
    }
}
