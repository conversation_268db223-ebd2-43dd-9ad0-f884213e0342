package com.lms.common.feign.fallback;

import com.lms.common.dao.query.PageInfo;
import com.lms.common.feign.api.CommonBaseApi;
import com.lms.common.feign.dto.Department;
import com.lms.common.feign.dto.Person;
import com.lms.common.feign.dto.Selectitem;
import com.lms.common.model.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * misboot-mdata相关接口 模块降级处理
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
@Component
public class CommonBaseApiFallbackFactory implements FallbackFactory<CommonBaseApi> {

    private static final Logger log = LoggerFactory.getLogger(CommonBaseApiFallbackFactory.class);


    @Override
    public CommonBaseApi create(Throwable throwable) {
        log.error("CommonBaseApi模块服务调用失败:{}", throwable.getMessage());
        return new CommonBaseApi() {

            @Override
            public Result<Department> getDepartmentById(String id) {
                return null;
            }

            @Override
            public Result<Person> getPersonById(String id) {
                return null;
            }

            @Override
            public Result<Selectitem> getSelectitemById(String id) {
                return null;
            }

            @Override
            public Result<List<Selectitem>> getSelectitemsByType(String id) {
                return null;
            }

            @Override
            public Result<Page<Person>> listByConditionPersons(PageInfo pageInfo) {
                return null;
            }

            @Override
            public Result<List<com.lms.common.model.Component>> getComponentByIdList(List<String> idList) {
                return null;
            }

            @Override
            public Result<List<com.lms.common.model.Component>> getAllChildComponentsByPidFullPath(String pidFullPath) {
                return null;
            }

            @Override
            public Result<List<Selectitem>> getSelectitemByIdWidthEmpty(String typeid) {
                return null;
            }
        };
    }
}
