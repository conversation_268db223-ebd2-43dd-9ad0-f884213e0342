package com.lms.common.util;

import org.apache.commons.codec.binary.Base64;


import org.apache.commons.codec.binary.Base64;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * <AUTHOR> 广州赛宝腾睿信息科技有限公司
 * @version V1.0
 * @date Date : 2021年09月23日 21:36
 * @Description: RSA加解密工具类，实现公钥加密私钥解密和私钥解密公钥解密
 */

public class RsaUtils {

    private final static String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC5bxAa8KASxYwrRe7kP8XjF8LOpbAqDVj3HZEu+j/8zp75TRYXnOeNFV4QjPktsvTI0ErtH2Bzvkedkhm5JQYxQ/yvnGMtbvV3VNkaKtT+fVYtBN5bF1IXJP5YLR/IvevMx5h3WqTiwZG4Df/QJ0Abenl6GUfXZi0aR85+ysc6UwIDAQAB";
    private final static String privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALlvEBrwoBLFjCtF7uQ/xeMXws6lsCoNWPcdkS76P/zOnvlNFhec540VXhCM+S2y9MjQSu0fYHO+R52SGbklBjFD/K+cYy1u9XdU2Roq1P59Vi0E3lsXUhck/lgtH8i968zHmHdapOLBkbgN/9AnQBt6eXoZR9dmLRpHzn7KxzpTAgMBAAECgYEApmfgkAx32eCllP/BDduc/doI2+EcYeBwgfJqozm3ymqxhw+CT9ZihN0z+K5JvpTlzn/9fSW3LKcJJYJCyz9KecsYmm4ua/E5e6zHkbilDvfBEp73FXK+VPRn9fOnQsfu/KkWqAv+JHnhdRnyLT6UGkEaLLu7KU6yR/7533GqHkECQQDs79y9dEzsPhAQgd4+esR9AFU91ojUXS94uIxy6T4XHGPriSH+sOzdGRdNxUerxfbvccY9/NkFVj9xeHK2DRuLAkEAyFpm/rj/MOlRZ+F4RTDXYSj2nOepILoOQxqNkgE8E9C69D9QePNrMWvVwPsAniegXii05JvHMYRQIekyqqrVWQJBAJ1LkAvmTbr4aMnMrbJk5lTmB7EJ38S8KGvIeNVAqwvEuUEAxSTEaDtQEPx8X9xVjF3h9m3oklrMq0m1YNIjWYkCQD+i9zPQ7r1KXmDwbJMNgFuL90i/DCko737A4FaPrdVdZwRNV/IS3ulDpxE0qG2TiIxfwZUbJZnEvO8Ict9ig4ECQH89PK/mmNfc9WGGbqLLpS6dVV15wjrEBGD0ou/rqoAVQD9Preh0wZ85mUbIAN9vdEb07jFxLRH/AB5aCZOvK8Y=";

    /**
     * 公钥加密私钥解密
     */
    private static void test1(String source) throws Exception {
        System.out.println("***************** 公钥加密私钥解密开始 *****************");
        String text1 = encryptByPublicKey(source);
        String text2 = decryptByPrivateKey(text1);
        System.out.println("加密前：" + source);
        System.out.println("加密后：" + text1);
        System.out.println("解密后：" + text2);
        if (source.equals(text2)) {
            System.out.println("解密字符串和原始字符串一致，解密成功");
        } else {
            System.out.println("解密字符串和原始字符串不一致，解密失败");
        }
        System.out.println("***************** 公钥加密私钥解密结束 *****************");
    }

    /**
     * 私钥加密公钥解密
     *
     * @throws Exception
     */
    private static void test2(String source) throws Exception {

        System.out.println("***************** 私钥加密公钥解密开始 *****************");
        String text1 = encryptByPrivateKey(source);
        String text2 = decryptByPublicKey(text1);
        System.out.println("加密前：" + source);
        System.out.println("加密后：" + text1);
        System.out.println("解密后：" + text2);
        if (source.equals(text2)) {
            System.out.println("解密字符串和原始字符串一致，解密成功");
        } else {
            System.out.println("解密字符串和原始字符串不一致，解密失败");
        }
        System.out.println("***************** 私钥加密公钥解密结束 *****************");
    }

    /**
     * 公钥解密
     *
     * @param text
     * @return
     * @throws Exception
     */
    public static String decryptByPublicKey(String text) throws Exception {
        try {
            X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKey));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, publicKey);
            byte[] result = cipher.doFinal(Base64.decodeBase64(text));
            return new String(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 私钥加密
     *
     * @param text
     * @return
     * @throws Exception
     */
    public static String encryptByPrivateKey(String text) throws Exception {
        try {
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, privateKey);
            byte[] result = cipher.doFinal(text.getBytes());
            return Base64.encodeBase64String(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 私钥解密
     *
     * @param text
     * @return
     * @throws Exception 公钥 前端用
     *                   -----BEGIN PUBLIC KEY-----
     *                   MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC5bxAa8KASxYwrRe7kP8XjF8LO
     *                   pbAqDVj3HZEu+j/8zp75TRYXnOeNFV4QjPktsvTI0ErtH2Bzvkedkhm5JQYxQ/yv
     *                   nGMtbvV3VNkaKtT+fVYtBN5bF1IXJP5YLR/IvevMx5h3WqTiwZG4Df/QJ0Abenl6
     *                   GUfXZi0aR85+ysc6UwIDAQAB
     *                   -----END PUBLIC KEY-----
     */
    public static String decryptByPrivateKey(String text) {
        //私钥
        try {
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec5 = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec5);
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] result = cipher.doFinal(Base64.decodeBase64(text));
            return new String(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 公钥加密
     *
     * @param text
     * @return
     */
    public static String encryptByPublicKey(String text) {
        X509EncodedKeySpec x509EncodedKeySpec2 = new X509EncodedKeySpec(Base64.decodeBase64(publicKey));
        KeyFactory keyFactory = null;
        try {
            keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec2);
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] result = cipher.doFinal(text.getBytes());
            return Base64.encodeBase64String(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 构建RSA密钥对
     *
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static RSAKeyPair generateKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(1024);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyPair.getPrivate();
        String publicKeyString = Base64.encodeBase64String(rsaPublicKey.getEncoded());
        String privateKeyString = Base64.encodeBase64String(rsaPrivateKey.getEncoded());
        RSAKeyPair rsaKeyPair = new RSAKeyPair(publicKeyString, privateKeyString);
        return rsaKeyPair;
    }


    /**
     * RSA密钥对对象
     */
    public static class RSAKeyPair {

        private String publicKey;
        private String privateKey;

        public RSAKeyPair(String publicKey, String privateKey) {
            this.publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC5bxAa8KASxYwrRe7kP8XjF8LOpbAqDVj3HZEu+j/8zp75TRYXnOeNFV4QjPktsvTI0ErtH2Bzvkedkhm5JQYxQ/yvnGMtbvV3VNkaKtT+fVYtBN5bF1IXJP5YLR/IvevMx5h3WqTiwZG4Df/QJ0Abenl6GUfXZi0aR85+ysc6UwIDAQAB";
            this.privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALlvEBrwoBLFjCtF7uQ/xeMXws6lsCoNWPcdkS76P/zOnvlNFhec540VXhCM+S2y9MjQSu0fYHO+R52SGbklBjFD/K+cYy1u9XdU2Roq1P59Vi0E3lsXUhck/lgtH8i968zHmHdapOLBkbgN/9AnQBt6eXoZR9dmLRpHzn7KxzpTAgMBAAECgYEApmfgkAx32eCllP/BDduc/doI2+EcYeBwgfJqozm3ymqxhw+CT9ZihN0z+K5JvpTlzn/9fSW3LKcJJYJCyz9KecsYmm4ua/E5e6zHkbilDvfBEp73FXK+VPRn9fOnQsfu/KkWqAv+JHnhdRnyLT6UGkEaLLu7KU6yR/7533GqHkECQQDs79y9dEzsPhAQgd4+esR9AFU91ojUXS94uIxy6T4XHGPriSH+sOzdGRdNxUerxfbvccY9/NkFVj9xeHK2DRuLAkEAyFpm/rj/MOlRZ+F4RTDXYSj2nOepILoOQxqNkgE8E9C69D9QePNrMWvVwPsAniegXii05JvHMYRQIekyqqrVWQJBAJ1LkAvmTbr4aMnMrbJk5lTmB7EJ38S8KGvIeNVAqwvEuUEAxSTEaDtQEPx8X9xVjF3h9m3oklrMq0m1YNIjWYkCQD+i9zPQ7r1KXmDwbJMNgFuL90i/DCko737A4FaPrdVdZwRNV/IS3ulDpxE0qG2TiIxfwZUbJZnEvO8Ict9ig4ECQH89PK/mmNfc9WGGbqLLpS6dVV15wjrEBGD0ou/rqoAVQD9Preh0wZ85mUbIAN9vdEb07jFxLRH/AB5aCZOvK8Y=";
        }

        public String getPublicKey() {
            return publicKey;
        }

        public String getPrivateKey() {
            return privateKey;
        }

    }

}
