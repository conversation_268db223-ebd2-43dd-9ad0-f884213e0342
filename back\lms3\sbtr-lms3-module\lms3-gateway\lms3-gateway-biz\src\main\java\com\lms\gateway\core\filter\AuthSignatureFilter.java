package com.lms.gateway.core.filter;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.lms.gateway.core.utils.DefaultProhibitUrl;
import com.lms.gateway.core.utils.DefaultSkipUrl;
import com.lms.gateway.core.utils.RedisUtil;
import com.lms.gateway.core.utils.StringUtils;
import io.jsonwebtoken.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpCookie;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import javax.xml.bind.DatatypeConverter;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * @MethodName
 * @Description 网关模块过滤器
 * @Param null
 * @Return
 * <AUTHOR> @ sbtr.com>
 * @Date 2019-8-25 16:39
 */
@Component
public class AuthSignatureFilter implements GlobalFilter, Ordered {

    static Logger logger = LoggerFactory.getLogger(AuthSignatureFilter.class);

    // URLs字符串匹配
    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Resource
    private RedisUtil redisUtil;

    private static final String JWT_SECRET = "MDk4ZjZiY2Q0NjIxZDM3M2NhZGU0ZTgzMjYyN2I0ZjY=";

    private static final String USER_TOKEN_KEY = "REDIS-USERNAME-";

    //private static final Integer TOKEN_EXPIRATION_TIME = 120;
    private static final Integer TOKEN_EXPIRATION_TIME = 120;

    // filter
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        URI uri = request.getURI();
        logger.info("[网关过滤器]请求路径:{}", exchange.getRequest().getPath());
        String uriPath = request.getURI().getPath();
        //  不需要认证的请求地址
        String path = exchange.getRequest().getURI().getPath();
        if (isSkip(path)) {
            logger.info("请求地址：" + uriPath + "被放开拦截");
            ServerHttpRequest host = exchange.getRequest().mutate().header("a", "888").build();
            ServerWebExchange build = exchange.mutate().request(host).build();
            return chain.filter(build);
        }

        // 接口包含 .api  v1  office/v1 /shop/ 直接放开拦截 /doc.html
        if (uriPath.contains(".api") ||
                uriPath.contains("/doc.html") ||
                uriPath.contains("/api-docs") ||
                uriPath.contains("/savepassword") ||
                uriPath.contains("/document/scorm") ||
                uriPath.contains("/checkpassword") ||
                uriPath.contains("/LmsStoragePath") ||
                uriPath.contains("isRightPassword")) {
            logger.info("请求地址：" + uriPath + " 放开拦截");
            //向headers中放文件，记得build
            ServerHttpRequest host = exchange.getRequest().mutate().header("a", "888").build();
            //将现在的request 变成 change对象
            ServerWebExchange build = exchange.mutate().request(host).build();
            return chain.filter(build);
        }

        // 获取token
        String token = "";
        if (!uriPath.equals("/system/jwtLogin")){
            token = getToken(request, uri, uriPath);
        }

        //  token失效退出到登录界面
        if (!StringUtils.isNullOrEmpty(token) & !"undefined".equals(token)) {
            if (isTokenExpired(token)) {
                byte[] bytes = "登录授权已过期,请重新登录系统!".getBytes(StandardCharsets.UTF_8);
                DataBuffer buffer = exchange.getResponse().bufferFactory().wrap(bytes);
                exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
                return exchange.getResponse().writeWith(Flux.just(buffer));
            }
        }

        //   token非空，跳转到其他系统模块接口
        if (((StringUtils.isNullOrEmpty(token) | "undefined".equals(token)) &&
                !uriPath.equals("/gateway/login") &&
                !uriPath.equals("/system/jwtLogin")) ||
                uriPath.equals("/gateway/login/logout")) {
            if (!isSkip(path)) {
                String requestType = request.getHeaders().getFirst("X-Requested-With");
                if ("XMLHttpRequest".equals(requestType)) {
                    byte[] bytes = "登录超时，请重新登录系统!".getBytes(StandardCharsets.UTF_8);
                    DataBuffer buffer = exchange.getResponse().bufferFactory().wrap(bytes);
                    exchange.getResponse().setStatusCode(HttpStatus.BAD_REQUEST);
                    return exchange.getResponse().writeWith(Flux.just(buffer));
                } else {
                    byte[] bytes = "当前请求未授权，请重新登录系统!".getBytes(StandardCharsets.UTF_8);
                    DataBuffer buffer = exchange.getResponse().bufferFactory().wrap(bytes);
                    exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
                    return exchange.getResponse().writeWith(Flux.just(buffer));
                }
            } else {
                //向headers中放文件，记得build
                ServerHttpRequest host = exchange.getRequest().mutate().header("a", "888").build();
                //将现在的request 变成 change对象
                ServerWebExchange build = exchange.mutate().request(host).build();
                return chain.filter(build);
            }
        } else {
            //向headers中放文件，记得build
            String ipAddr = ObjectUtil.defaultIfNull(exchange.getRequest().getHeaders().get("x-forwarded-for"),"").toString();
            ipAddr = StringUtils.isNullOrEmpty(ipAddr)? String.valueOf(exchange.getRequest().getRemoteAddress()) : ipAddr;
            ServerHttpRequest host = exchange.getRequest().mutate().header("Gateway", uri.getScheme() + "://" + uri.getHost() + ":" + uri.getPort())
                    .header("ipaddr", ipAddr).build();
            //将现在的request 变成 change对象
            ServerWebExchange build = exchange.mutate().request(host).build();
            return chain.filter(build);
        }
    }

    //  getOrder
    @Override
    public int getOrder() {
        return -200;
    }

    //  clearCookie
    public void clearCookie(ServerWebExchange exchange) {
        ServerHttpRequest request = exchange.getRequest();
        MultiValueMap<String, HttpCookie> cookies = request.getCookies();

        ServerHttpResponse response = exchange.getResponse();
        //response.addCookie();
    }


    // 匹配默认跳过请求地址
    private boolean isSkip(String path) {
        return DefaultSkipUrl.getDefaultSkipUrl().stream().anyMatch(pattern -> antPathMatcher.match(pattern, path));
    }

    // 匹配默认禁止请求接口地址
    private boolean isProhibitUrl(String path) {
        return DefaultProhibitUrl.getDefaultProhibitUrl().stream().anyMatch(pattern -> antPathMatcher.match(pattern, path));
    }

    // 获取Token
    private String getToken(ServerHttpRequest request, URI uri, String uriPath) {
        String token = request.getHeaders().getFirst("token");
        //处理头部token
        if (StringUtils.isNullOrEmpty(token) | "undefined".equals(token)) {
            HttpCookie tokenCookie = request.getCookies().getFirst("token");
            if (tokenCookie != null) {
                token = tokenCookie.getValue();
            }else{
                token = "";
            }
        }
        //  特殊处理 report swagger 模块token问题 request.getHeaders().get("Referer")
        /*List<String> referer = request.getHeaders().get("Referer");
        if (referer != null && !referer.isEmpty()) {
            if ((StringUtils.isNullOrEmpty(token) && uriPath.contains("report"))
                    || (StringUtils.isNullOrEmpty(token)
                    && request.getHeaders().get("Referer").toString().contains("swagger"))
            ) {
                try {
                    token = StringUtils.getParamByUrl(request.getHeaders().get("Referer").get(0).toString());
                } catch (Exception e) {
                    token = null;
                }
            }
        }*/
        return token;
    }

    /**
     * 检查令牌是否过期（只基于Redis判断，忽略JWT本身的过期时间）
     * @param token JWT令牌
     * @return 是否过期
     */
    public Boolean isTokenExpired(String token) {
        Claims claims;
        try {
            // 尝试正常解析JWT令牌
            claims = (Claims) Jwts.parser().
                    setSigningKey(DatatypeConverter.parseBase64Binary(JWT_SECRET)).parseClaimsJws(token).getBody();
            logger.debug("JWT令牌解析成功，继续检查Redis状态");
        } catch (ExpiredJwtException e) {
            // JWT过期，但仍可以从异常中获取claims来检查Redis
            claims = e.getClaims();
            if (claims == null) {
                logger.error("无法从过期的JWT令牌中获取用户信息: {}", e.getMessage());
                return true;
            }
            logger.debug("JWT令牌已过期但仍可解析用户信息，继续检查Redis状态");
        } catch (Exception e) {
            // 令牌解析失败（格式错误、签名错误等）
            logger.error("JWT令牌解析失败: {}", e.getMessage());
            return true;
        }
        String userId = Convert.toStr(claims.get("id"));
        if (StringUtils.isNullOrEmpty(userId)) {
            logger.error("无法从JWT令牌中获取用户ID");
            return true;
        }
        // 只检查Redis中的用户信息是否存在和过期
        String redisKey = USER_TOKEN_KEY + userId;
        if (!redisUtil.hasKey(redisKey)) {
            logger.info("Redis中用户信息已过期或不存在，用户ID: {}", userId);
            return true;
        }
        // 检查Redis中的剩余过期时间（单位：分钟）
        long remainingMinutes = redisUtil.getExpire(redisKey);
        if (remainingMinutes <= 0) {
            logger.info("Redis中用户信息已过期，用户ID: {}", userId);
            return true;
        }
        // 如果剩余时间少于30分钟，自动延长到2小时
        if (remainingMinutes <= 30) {
            logger.info("用户会话即将过期，剩余时间: {} 分钟，自动延长到2小时，用户ID: {}", remainingMinutes, userId);
            boolean success = redisUtil.expire(redisKey, TOKEN_EXPIRATION_TIME);
            if (success) {
                logger.info("成功延长用户会话时间，用户ID: {}, 新过期时间: {} 分钟", userId, TOKEN_EXPIRATION_TIME);
            } else {
                logger.error("延长用户会话时间失败，用户ID: {}", userId);
            }
        }
        return false;
    }
}
