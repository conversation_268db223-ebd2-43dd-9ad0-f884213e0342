package com.lms.system.users.service;

import com.lms.common.model.JwtUser;
import com.lms.common.model.Result;
import com.lms.common.service.BaseService;
import com.lms.system.feign.model.Users;

import java.util.List;

public interface UsersService extends BaseService<Users> {
    Result<JwtUser> login(String userName, String password, String ipaddr, String clientagent);

    Users getUsersByUserName(String userName);

    Boolean existUser(String name, String id);

    void clearUserCache(Users users);

    List<Users> getUsersByPersonIds(List<String> idList);

    Result checkpassword(String password);

    Users getUsersByPersonId(String personId);
}
