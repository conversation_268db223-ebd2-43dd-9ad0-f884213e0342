package com.lms.base.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import com.lms.common.model.SearchContent;
import com.lms.common.model.SearchName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * BSelectitemId entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)

@TableName("b_selectitem")
@Data
@ApiModel(value = "数据字典实体类")
public class Selectitem extends BaseModel {
    @TableId(type= IdType.ASSIGN_ID)
    @SearchName
    @ApiModelProperty("主键id")
    private String id;

    @SearchContent
    @ApiModelProperty("名称")
    private String objname;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("描述")
    @SearchContent
    private String objdesc;

    @ApiModelProperty("父选项id")
    private String pid;

    @ApiModelProperty("类型id")
    private String typeid;

    @ApiModelProperty("序号")
    private int seqno;

    @ApiModelProperty("状态")
    private int status;

    @TableField(exist = false)
    @ApiModelProperty("父选项名称")
    @SearchContent
    private String parentname;

    @ApiModelProperty("是否是系统级")
    private int issystemlevel;

    public static Selectitem getEmptyItem() {
        Selectitem item = new Selectitem();
        item.setId("");
        item.setObjname("");
        return item;
    }

}
