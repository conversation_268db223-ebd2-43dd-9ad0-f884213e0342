package com.sbtr.workflow.mapper;

import com.sbtr.workflow.model.ActMyProcessCopy;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程抄送表
 *
 * <AUTHOR>
 * @Email sbtr@.com
 * @Date 2021-03-02 17:03:24
 */
public interface ActMyProcessCopyMapper extends tk.mybatis.mapper.common.Mapper<ActMyProcessCopy> {

    //获取分页集
    List<ActMyProcessCopy> getPageSetOracle(@Param("formName") String formName, @Param("userNameId") String userNameId);

    List<ActMyProcessCopy> getPageSetSqlServer(@Param("formName") String formName, @Param("userNameId") String userNameId);

    List<ActMyProcessCopy> getPageSetMySql(@Param("formName") String formName, @Param("userNameId") String userNameId);

    //批量删除
    int executeDeleteBatch(Object[] var1);

    //根据流程定义ID删除数据
    Integer deleteByProcessDefinitionId(@Param("processDefinitionId") String processDefinitionId);

    List<ActMyProcessCopy> getMyPageSet(@Param("userNameId") String userNameId, @Param("formName") String formName);

    List<ActMyProcessCopy> getPageSet(@Param("userNameId") String userNameId, @Param("formName") String formName);

    Integer updateReviewStatusByUuid(@Param("uuid")String uuid, @Param("reviewStatus")String reviewStatus);
}
