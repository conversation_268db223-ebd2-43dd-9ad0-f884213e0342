package com.sbtr.workflow.dto;


/**
 * 解析前端传过来的每个节点所对应的字段是否可编辑是否可以查看
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
public class FormNoticeListDto {

    //字段code
    private String filed;
    //字段名
    private String filedName;
    //是否可以编辑
    private String isEdit;
    //节点Id
    private String id;
    //模型key
    private String modalKey;
    //是否可以编辑
    private String isLook;

    public String getFiled() {
        return filed;
    }

    public void setFiled(String filed) {
        this.filed = filed;
    }

    public String getFiledName() {
        return filedName;
    }

    public void setFiledName(String filedName) {
        this.filedName = filedName;
    }

    public String getIsEdit() {
        return isEdit;
    }

    public void setIsEdit(String isEdit) {
        this.isEdit = isEdit;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getModalKey() {
        return modalKey;
    }

    public void setModalKey(String modalKey) {
        this.modalKey = modalKey;
    }

    public String getIsLook() {
        return isLook;
    }

    public void setIsLook(String isLook) {
        this.isLook = isLook;
    }
}
