package com.sbtr.workflow.mapper;

import com.sbtr.workflow.model.SysOrganization;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 组织机构 Mapper
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-08-01 14:55:46
 */
public interface SysOrganizationMapper extends tk.mybatis.mapper.common.Mapper<SysOrganization> {

    /**
     * fetch data by rule id
     *
     * @param ids
     * @param groupLeader
     * @return Result<XxxxDO>
     */
    List<SysOrganization> getListByOrgType(@Param("ids") String ids, @Param("groupLeader") String groupLeader);

    /**
     * fetch data by rule id
     *
     * @return Result<XxxxDO>
     */
    List<Map<String, Object>> getOrgList();


    /**
     * fetch data by rule id
     *
     * @param userNameId
     * @return Result<XxxxDO>
     */
    String getleaderIdByUserNameId(@Param("userNameId") String userNameId);

    List<SysOrganization> getListOrganizationByOrgId(@Param("orgId") String orgId);
}
