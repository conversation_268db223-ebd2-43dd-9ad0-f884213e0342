package com.sbtr.workflow.model;

import cn.ewsd.common.model.MCoreBase;

import javax.persistence.Table;
import java.util.HashMap;
import java.util.Map;

/**
 * 岗位表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-08-01 14:55:46
 */
@Table(name = "sys_position")
public class SysPosition{
    private static final long serialVersionUID = 1L;

    /**
     * 岗位id
     */
    private String id;
    /**
     * 岗位名称
     */
    private String positionName;
    /**
     * 岗位代码
     */
    private String positionCode;
    /**
     * 岗位排序
     */
    private Integer positionSort;
    /**
     * 岗位状态
     */
    private String positionStatus;
    /**
     * 岗位类型
     */
    private String positionType;

    /**
     * 岗位备注
     */
    private String positionRemark;

    private String organizationId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public Integer getPositionSort() {
        return positionSort;
    }

    public void setPositionSort(Integer positionSort) {
        this.positionSort = positionSort;
    }

    public String getPositionStatus() {
        return positionStatus;
    }

    public void setPositionStatus(String positionStatus) {
        this.positionStatus = positionStatus;
    }

    public String getPositionType() {
        return positionType;
    }

    public void setPositionType(String positionType) {
        this.positionType = positionType;
    }

    public String getPositionRemark() {
        return positionRemark;
    }

    public void setPositionRemark(String positionRemark) {
        this.positionRemark = positionRemark;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

}
