<template>
  <div class="trdialog-example">
    <h2>TrDialog 组件示例</h2>
    <p class="description">基于 Ant Design Vue 的二次封装对话框组件</p>

    <!-- 基础用法 -->
    <div class="demo-section">
      <h3>基础用法</h3>
      <a-button type="primary" @click="showBasicDialog = true">
        基础对话框
      </a-button>

      <TrDialog v-model:open="showBasicDialog" title="基础对话框" width="500px">
        <p>这是一个基础的对话框示例</p>
      </TrDialog>
    </div>

    <!-- 拖拽功能 -->
    <div class="demo-section">
      <h3>拖拽功能</h3>
      <a-button type="primary" @click="showDragDialog = true">
        可拖拽对话框
      </a-button>

      <TrDialog
        v-model:open="showDragDialog"
        title="可拖拽对话框"
        :draggable="true"
        width="600px"
      >
        <p>拖拽标题栏可以移动对话框位置</p>
        <p>
          注意：默认不显示全屏按钮，如需显示请设置
          :show-fullscreen-button="true"
        </p>
      </TrDialog>
    </div>

    <!-- 全屏功能 -->
    <div class="demo-section">
      <h3>全屏功能</h3>
      <a-button type="primary" @click="showFullscreenDialog = true">
        全屏对话框
      </a-button>

      <TrDialog
        v-model:open="showFullscreenDialog"
        title="全屏对话框"
        :fullscreen="false"
        :draggable="true"
        :show-fullscreen-button="true"
        width="700px"
      >
        <div class="fullscreen-content">
          <p>点击右上角全屏按钮切换全屏模式</p>
          <!-- <p>全屏模式下拖拽功能会被禁用</p> -->
        </div>
      </TrDialog>
    </div>

    <!-- 自定义按钮 -->
    <div class="demo-section">
      <h3>自定义按钮</h3>
      <a-button type="primary" @click="showCustomButtonsDialog = true">
        自定义按钮
      </a-button>

      <TrDialog
        v-model:open="showCustomButtonsDialog"
        title="自定义按钮"
        :footer-buttons="customButtons"
        width="500px"
      >
        <p>底部按钮完全自定义配置</p>
      </TrDialog>
    </div>

    <!-- 表单对话框 -->
    <div class="demo-section">
      <h3>表单对话框</h3>
      <a-button type="primary" @click="showFormDialog = true">
        表单对话框
      </a-button>

      <TrDialog
        v-model:open="showFormDialog"
        title="用户信息"
        :draggable="true"
        width="600px"
        @ok="handleFormSubmit"
      >
        <a-form :model="formData" layout="vertical">
          <a-form-item label="姓名">
            <a-input v-model:value="formData.name" placeholder="请输入姓名" />
          </a-form-item>
          <a-form-item label="邮箱">
            <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
          </a-form-item>
          <a-form-item label="备注">
            <a-textarea
              v-model:value="formData.remark"
              placeholder="请输入备注"
            />
          </a-form-item>
        </a-form>
      </TrDialog>
    </div>

    <!-- TrForm 表单对话框  等TrForm封装-->
    <!-- <div class="demo-section">
      <h3>TrForm 表单对话框</h3>
      <a-button type="primary" @click="showTrFormDialog = true">
        TrForm 表单对话框
      </a-button>

      <TrForm
        :model="trFormData"
        :fields="trFormFields"
        :rules="trFormRules"
        mode="modal"
        modal-mode="add"
        :modal-visible="showTrFormDialog"
        modal-title="用户信息 (TrForm)"
        :modal-width="600"
        @update:modal-visible="showTrFormDialog = $event"
        @submit="handleTrFormSubmit"
        @cancel="handleTrFormCancel"
      />
    </div> -->

    <!-- 确认对话框 -->
    <div class="demo-section">
      <h3>确认对话框</h3>
      <a-button type="primary" danger @click="showConfirmDialog = true">
        删除操作
      </a-button>

      <TrDialog
        v-model:open="showConfirmDialog"
        title="确认删除"
        :draggable="true"
        width="500px"
        :footer-buttons="confirmButtons"
      >
        <p>确定要删除这条记录吗？此操作不可恢复。</p>
      </TrDialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import TrDialog from "./index.vue";
import type { ButtonConfig } from "./types";

// 响应式数据
const showBasicDialog = ref(false);
const showDragDialog = ref(false);
const showFullscreenDialog = ref(false);
const showCustomButtonsDialog = ref(false);
const showFormDialog = ref(false);
const showConfirmDialog = ref(false);
const draggable = ref(true);

// 表单数据
const formData = reactive({
  name: "",
  email: "",
  remark: ""
});

// 自定义按钮配置
const customButtons: ButtonConfig[] = [
  {
    text: "保存",
    type: "primary",
    onClick: () => {
      console.log("保存操作");
      showCustomButtonsDialog.value = false;
    }
  },
  {
    text: "删除",
    type: "default",
    danger: true,
    onClick: () => {
      console.log("删除操作");
    }
  },
  {
    text: "取消",
    type: "default",
    onClick: () => {
      showCustomButtonsDialog.value = false;
    }
  }
];

// 确认按钮配置
const confirmButtons: ButtonConfig[] = [
  {
    text: "确定删除",
    type: "primary",
    danger: true,
    onClick: () => {
      console.log("执行删除操作");
      showConfirmDialog.value = false;
    }
  },
  {
    text: "取消",
    type: "default",
    onClick: () => {
      showConfirmDialog.value = false;
    }
  }
];

// 事件处理函数
const handleFormSubmit = (e: MouseEvent) => {
  console.log("表单提交", formData);
  showFormDialog.value = false;
};
</script>

<style lang="scss" scoped>
.trdialog-example {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    color: #1890ff;
    margin-bottom: 8px;
  }

  .description {
    color: #666;
    margin-bottom: 32px;
  }

  .demo-section {
    margin-bottom: 32px;
    padding: 20px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fafafa;

    h3 {
      color: #333;
      margin-bottom: 16px;
      border-bottom: 2px solid #1890ff;
      padding-bottom: 8px;
    }
  }

  .button-group {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
  }

  .feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }

  .feature-item {
    padding: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    background: #fff;
    text-align: center;

    h4 {
      color: #1890ff;
      margin-bottom: 8px;
    }
  }

  .code-example {
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    padding: 16px;

    h4 {
      margin-bottom: 12px;
      color: #333;
    }

    pre {
      margin: 0;
      font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
      font-size: 14px;
      line-height: 1.5;
    }

    code {
      color: #24292e;
    }
  }

  .fullscreen-content {
    min-height: 200px;
  }

  .complex-content {
    padding: 16px;
  }
}
</style>
