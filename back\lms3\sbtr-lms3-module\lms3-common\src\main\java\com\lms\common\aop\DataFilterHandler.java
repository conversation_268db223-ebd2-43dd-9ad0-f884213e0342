package com.lms.common.aop;

import cn.hutool.core.util.ObjectUtil;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.dao.query.Parameter;
import com.lms.common.enums.RoleEum;
import com.lms.common.feign.api.CommonBaseApi;
import com.lms.common.model.*;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.BeanUtils;
import com.lms.common.util.ContextUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Aspect
@Component
public class DataFilterHandler {

    @Resource
    private CommonBaseApi commonBaseApi;

    @Around(value = "execution(* com..service.*.*(..)) && @annotation(dataCheck)")
    public Object doDataCheck(ProceedingJoinPoint joinPoint, DataCheck dataCheck) throws Throwable {
        Object result = null;
        // 拦截的实体类，就是当前正在执行的controller
        Object target = joinPoint.getTarget();
        // 拦截的方法参数
        Object[] args = joinPoint.getArgs();
        // 拦截的放参数类型
        Signature sig = joinPoint.getSignature();
        MethodSignature msig = null;
        if (!(sig instanceof MethodSignature)) {
            throw new IllegalArgumentException("该注解只能用于方法");
        }
        msig = (MethodSignature) sig;
        // 获得被拦截的方法
//      Method method = target.getClass().getMethod(methodName, parameterTypes);
        Method method = msig.getMethod();
        // 判断是否包含自定义的注解，说明一下这里的SystemLog就是我自己自定义的注解
        if (method.isAnnotationPresent(DataCheck.class)) {
            DataCheck checkInfo = method.getAnnotation(DataCheck.class);
            String[] objnames = checkInfo.checkNames();
            String[] descs = checkInfo.checkDescs();
            if (ObjectUtil.isNotEmpty(objnames)) {
                Object objects = args[0];
                if (objects instanceof BaseModel) {
                    for (int i = 0; i < objnames.length; i++) {
                        String objname = objnames[i];
                        String desc = descs[i];
                        Object o = BeanUtils.getValueByName(objects, objname);
                        if (ObjectUtil.isEmpty(o)) {
                            if (objname.equals("mlevel")) {
                                BeanUtils.setValueByName(objects, objname, 20);
                            } else {
                                throw new DataException(desc);
                            }
                        }
                    }
                } else if (objects instanceof List) {
                    for (Object object : (List) objects) {
                        for (int i = 0; i < objnames.length; i++) {
                            String objname = objnames[i];
                            String desc = descs[i];
                            Object o = BeanUtils.getValueByName(object, objname);
                            if (ObjectUtil.isEmpty(o)) {
                                if (objname.equals("mlevel")) {
                                    BeanUtils.setValueByName(object, objname, 20);
                                } else {
                                    throw new DataException(desc);
                                }
                            }
                        }
                    }
                }
            }
        }
        result = joinPoint.proceed();
        return result;
    }

    @Around(value = "execution(* com..service.*.*(..)) && @annotation(dataFilter)")
    public Object doDataFilter(ProceedingJoinPoint joinPoint, DataFilter dataFilter) throws Throwable {
        Object result = null;
        // 拦截的方法参数
        Object[] args = joinPoint.getArgs();
        // 拦截的放参数类型
        Signature sig = joinPoint.getSignature();
        MethodSignature msig = null;
        if (!(sig instanceof MethodSignature)) {
            throw new IllegalArgumentException("该注解只能用于方法");
        }
        msig = (MethodSignature) sig;
        // 获得被拦截的方法
//      Method method = target.getClass().getMethod(methodName, parameterTypes);
        Method method = msig.getMethod();
        // 判断是否包含自定义的注解，说明一下这里的SystemLog就是我自己自定义的注解
        if (method.isAnnotationPresent(DataFilter.class)) {
            DataFilter filterInfo = method.getAnnotation(DataFilter.class);
//            String[] objnames = filterInfo.filterNames();
            if (args.length > 0) {
                Object object = args[0];
                Object service = joinPoint.getTarget();
                if (service instanceof BaseServiceImpl) {
                    if (object instanceof PageInfo) {
                        //对所有调取Listcon
                        PageInfo pageInfo = (PageInfo) object;
                        Class<?> Tclass = ((BaseServiceImpl<?,?>) service).getTClass();
                        List objectFieldNames = BeanUtils.getClassFieldNames(Tclass);
                        if (objectFieldNames.contains("mlevel") && ObjectUtil.isNotEmpty(ContextUtil.getSlevel())) {
                            Parameter parameter = Parameter.getParameter("I_LE_mlevel", ContextUtil.getSlevel());
                            pageInfo.getParameters().add(parameter);
                        }
                        /*
                        // 型号数据过滤
                        String componentid = objectFieldNames.contains("componentid") ? "componentid" : "";
                        boolean isCourseQuery = false;
                        if (StringUtils.isEmpty(componentid)) {
                            componentid = objectFieldNames.contains("componentId") ? "componentId" : "";
                            if (StringUtils.isNotEmpty(componentid)) {
                                isCourseQuery = true;
                            }
                        }
                        if (!componentid.isEmpty()) {
                            adaptEquipmentQueryParam(pageInfo, isCourseQuery);
                        }
                        boolean isStudent = RoleEum.student.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
                        if (isStudent){
                            // 专业数据过滤
                            String specialityid = objectFieldNames.contains("specialityid") ? "specialityid" : "";
                            if (!specialityid.isEmpty() && !Optional.ofNullable(ContextUtil.getCurrentUser().getSpecialityid()).orElse("").isEmpty()) {
                                Parameter parameter1 = Parameter.getParameter("S_EQ_specialityid_OR_S_ISNULL_specialityid", ContextUtil.getCurrentUser().getSpecialityid());
                                pageInfo.getParameters().add(parameter1);
                            }
                            // 人员层级数据过滤
                            String personlevelid = objectFieldNames.contains("personlevelid") ? "personlevelid" : "";
                            String levelid = objectFieldNames.contains("levelid") ? "levelid" : "";
                            if (!personlevelid.isEmpty() && !Optional.ofNullable(ContextUtil.getCurrentUser().getPersonlevelid()).orElse("").isEmpty()) {
                                Parameter parameter1 = Parameter.getParameter("S_EQ_personlevelid_OR_S_ISNULL_personlevelid", ContextUtil.getCurrentUser().getPersonlevelid());
                                pageInfo.getParameters().add(parameter1);
                            }else if (!levelid.isEmpty() && !Optional.ofNullable(ContextUtil.getCurrentUser().getPersonlevelid()).orElse("").isEmpty()){
                                Parameter parameter1 = Parameter.getParameter("S_EQ_levelid_OR_S_ISNULL_levelid", ContextUtil.getCurrentUser().getPersonlevelid());
                                pageInfo.getParameters().add(parameter1);
                            }
                        }*/
                    } else if (args.length > 1) {
//                    Ob = args[1];
//                    BeanUtils.existedField(objects,"mlevel");
                    }
                }

            }
        }
        result = joinPoint.proceed();
        return result;
    }


    /**
     * 适配按型号分配资源数据权限
     *
     */
    /*public void adaptEquipmentQueryParam(PageInfo pageInfo, boolean isCourseQuery) {
        List<String> currentUserComponentId = getCurrentUserComponentId(false);
        if (currentUserComponentId == null) return;
        // 获取当前用户所处型号下的所有课程id
        List<Course> courseList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        String idstr = "";
        Result<List<Course>> res = commonBaseApi.getCourseListByComponentIdList(currentUserComponentId);
        if (res != null && res.getResult() != null) {
            courseList = res.getResult();
        }
        courseList.forEach(course -> {
            sb.append(course.getId());
            sb.append(",");
        });
        currentUserComponentId.forEach(id -> {
            sb.append(id);
            sb.append(",");
        });
        idstr = sb.toString();
        if (StringUtils.isNotEmpty(idstr)) {
            idstr = idstr.substring(0, idstr.length() - 1);
        }
        // 适配查询条件
        Parameter componentIdParam;
        if (isCourseQuery) {
            componentIdParam = Parameter.getParameter(
                    "S_IN_componentId", idstr);
        } else {
            componentIdParam = Parameter.getParameter(
                    "S_IN_componentid", idstr);
        }

        pageInfo.getParameters().add(componentIdParam);
    }*/

    private List<String> getCurrentUserComponentId(boolean containsFullPathId) {
        // 获取当前用户型号id,包含子型号
        Set<String> currentUserComponentId = new HashSet<>();
        boolean isStudent = RoleEum.student.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
        boolean isTeacher = RoleEum.teacher.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
        boolean isAdmin = RoleEum.admin.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
        boolean isLeader = RoleEum.leader.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
        String equipmentid = "";
        if (isStudent) { //学员
            equipmentid = ContextUtil.getCurrentUser().getEquipmentid();
        } else if (isTeacher) { //教员
            equipmentid = ContextUtil.getCurrentUser().getTeachingmodel();
            //教员未分配管理或教学型号,默认为可查看所有型号数据,不进行数据过滤
            if (StringUtils.isEmpty(equipmentid)) {
                return null;
            }
        } else if (isAdmin || isLeader) {
            equipmentid = ContextUtil.getCurrentUser().getManagemodel();
            // 管理员未分配管理或教学型号,默认为可查看所有型号数据,不进行数据过滤
            if (StringUtils.isEmpty(equipmentid)) {
                return null;
            }
        } else {
            // 其他管理角色不做过滤
            return null;
        }
        if (StringUtils.isNotEmpty(equipmentid)) {
            String[] componentArray = equipmentid.split(",");
            if (componentArray.length > 0) {
                for (String componentId : componentArray) {
                    if (StringUtils.isNotEmpty(componentId)) {
                        String[] split = componentId.split("/");
                        String[] fullpathsplit = new String[0];
                        String fullpath = "";
                        String s = split[split.length - 1];
                        Result<List<com.lms.common.model.Component>> componentByIdList = commonBaseApi.getComponentByIdList(Collections.singletonList(s));
                        if (componentByIdList != null && componentByIdList.getResult() != null){
                            List<com.lms.common.model.Component> list = componentByIdList.getResult();
                            if (CollectionUtils.isNotEmpty(list)){
                                com.lms.common.model.Component component = list.get(0);
                                fullpath = component.getFullpath();
                                fullpathsplit = fullpath.split("/");
                                if (containsFullPathId) {
                                    currentUserComponentId.addAll(Arrays.asList(fullpathsplit));
                                }
                            }
                        }
                        String lastComponentId = fullpathsplit[fullpathsplit.length - 1];
                        Result<List<com.lms.common.model.Component>> result = commonBaseApi.getAllChildComponentsByPidFullPath(fullpath);
                        List<com.lms.common.model.Component> allComponentsByPid = new ArrayList<>();
                        if (result != null && result.getResult() != null) {
                            allComponentsByPid = result.getResult();
                        }
                        if (CollectionUtils.isNotEmpty(allComponentsByPid)) {
                            List<String> childComponentIdList = allComponentsByPid.stream().map(com.lms.common.model.Component::getId).collect(Collectors.toList());
                            currentUserComponentId.addAll(childComponentIdList);
                        }
                        currentUserComponentId.add(lastComponentId);
                    }
                }
            }
        }
        return new ArrayList<>(currentUserComponentId);
    }


}
