
package com.lms.examine.certificate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.DBSqlUtil;
import com.lms.common.util.NumberHelper;
import com.lms.examine.certificate.mapper.CertificateMapper;
import com.lms.examine.certificate.service.CertificateService;
import com.lms.examine.feign.model.Certificate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

@Service("CertificateService")
public class CertificateServiceImpl extends BaseServiceImpl<CertificateMapper, Certificate> implements CertificateService {

	@Resource
	private CertificateMapper certificateMapper;

	@Resource
	private DBSqlUtil dbSqlUtil;

	public List<Certificate> getByModelId(String id) {
		return certificateMapper.getByModelId(id);
	}

	public int getMaxSeqno(String planid) throws SQLException {
		String hql = "select "
				+ dbSqlUtil.getIfNullFlag()
				+ "(max(k.seqno),0) max from u_certificate k where k.planid='" + planid + "'";
		Map map = certificateMapper.getMaxSeqno(hql);
		int seqno = NumberHelper.getIntegerValue(map.get("max"));
		return seqno;
	}

	public List<Certificate> getByPlanId(String planId) {
		LambdaQueryWrapper<Certificate> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(Certificate::getPlanid, planId);
		return this.list(wrapper);
	}
}
