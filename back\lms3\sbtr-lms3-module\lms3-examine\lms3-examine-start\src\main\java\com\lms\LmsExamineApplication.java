package com.lms;

import com.lms.common.util.ConstParamUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;


/**
 *  系统模块启动类
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2024-07-16 13:31:39
 */
@SpringBootApplication
@ComponentScan(basePackages = {ConstParamUtil.commonScanPackage,ConstParamUtil.examineScanPackage,ConstParamUtil.commonFeignPackage})
@EnableFeignClients(basePackages = {ConstParamUtil.commonFeignPackage})
public class LmsExamineApplication {
    public static void main(String[] args) {
        SpringApplication.run(LmsExamineApplication.class, args);
    }
}
