package com.sbtr.workflow.controller;

import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.model.ActMyFlowAgent;
import com.sbtr.workflow.service.ActMyFlowAgentService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 流程每个节点所对应按钮
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2024-06-25 14:32:25
 */
@Api(tags = {"流程代理" })
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/flowAgent")
public class ActMyFlowAgentController extends WorkflowBaseController {

    @Autowired
    private ActMyFlowAgentService flowAgentService;

    @ApiOperation(value = "获得ActFlowAgent分页集数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String"),
    })
    @ResponseBody
    @RequestMapping(value = "/getPageSet", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam, String agentName, String mandatorName,String agent,String mandator) {
        PageSet<ActMyFlowAgent> pageSet = flowAgentService.getPageSet(pageParam,agentName,mandatorName,agent,mandator);
        return pageSet;
    }

    @ApiOperation(value = "获得FlowAgent模块详细数据")
    @ApiImplicitParam(name = "uuid", value = "获得FlowAgent模块详细数据", required = true, dataType = "String")
    @ResponseBody
    @RequestMapping(value = "/getDetailByUuid")
    public Object getDetailByUuid(String uuid) {
        ActMyFlowAgent flowAgent =flowAgentService.selectByPrimaryKey(uuid);
        return flowAgent;
    }



    @ApiOperation(value = "保存FlowAgent模块数据")
    @ApiImplicitParam(name = "flowAgent", value = "保存FlowAgent模块数据", required = true, dataType = "FlowAgent")
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Object save(@ModelAttribute ActMyFlowAgent flowAgent) {
        Integer result = flowAgentService.save(flowAgent);
        return result > 0 ? success("保存成功！") : failure("保存失败！");
    }


    @ApiOperation(value = "更新FlowAgent模块数据")
    @ApiImplicitParam(name = "flowAgent", value = "更新FlowAgent模块数据", required = true, dataType = "FlowAgent")
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Object update(@ModelAttribute ActMyFlowAgent flowAgent) {
        int result = flowAgentService.updateByPrimaryKeySelective(getUpdateData(flowAgent));
        return result > 0 ? success("更新成功！") : failure("更新失败！");
    }

    @ApiOperation(value = "删除FlowAgent模块数据")
    @ApiImplicitParam(name = "flowAgent", value = "删除FlowAgent模块数据", required = true, dataType = "FlowAgent")
    @ResponseBody
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.POST)
    public Object deleteBatch(@RequestParam String[] uuid) {
        int result = flowAgentService.executeDeleteBatch(uuid);
        return result > 0 ? success("删除成功！") : failure("删除失败！");
    }




}
