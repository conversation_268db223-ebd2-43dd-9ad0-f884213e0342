package com.lms.examine.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.feign.dto.Person;
import com.lms.common.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * UExaminestudent entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)

@Data
@TableName("u_examinestudent")
@ApiModel("试卷学员对象")
public class Examinestudent extends BaseModel {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("试卷学员唯一标识")
    private String id;

    @ApiModelProperty("试卷id")
    private String examineid;

    @ApiModelProperty("人员id")
    private String personid;

    @ApiModelProperty("接收时间")
    private String receivetime;

    @ApiModelProperty("开始考试时间")
    private String begintime;

    @ApiModelProperty("结束考试时间")
    private String endtime;

    @ApiModelProperty("考试截止时间")
    private String examinedtime;

    @ApiModelProperty("提交时间")
    private String submittime;

    @ApiModelProperty("得分")
    private Double score;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("考试结果")
    private String result;

    @ApiModelProperty("试卷状态")
    private String examinestatus;

    @TableField(exist = false)
    @ApiModelProperty("试卷名称")
    private String examinename;

    @TableField(exist = false)
    @ApiModelProperty("发布日期")
    private String publishdate;

    @TableField(exist = false)
    @ApiModelProperty("人员对象")
    private Person person;

    @ApiModelProperty("考试附件")
    private String attachid;
}
