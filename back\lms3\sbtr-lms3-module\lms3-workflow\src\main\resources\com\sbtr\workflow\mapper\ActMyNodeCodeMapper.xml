<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ActMyNodeCodeMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sbtr.workflow.model.ActMyNodeCode" id="flowNodeCodeMap">
        <result property="uuid" column="uuid"/>
        <result property="nodeId" column="node_id"/>
        <result property="nodeCode" column="node_code"/>
        <result property="nodeName" column="node_name"/>
        <result property="procdefId" column="procdef_id"/>
        <result property="actDeModelKey" column="act_de_model_key"/>

    </resultMap>

    <sql id="Base_Column_List">
  	  	     			 uuid
		  ,  	  	     			 node_id
		  ,  	  	     			 node_code
		  ,  	  	     			 node_name
		  ,procdef_id,act_de_model_key
		    	  </sql>
    <insert id="insertListOracle">
        insert into act_my_node_code (
            uuid,
        node_id,
        node_code,
        node_name,
        procdef_id,
        ACT_DE_MODEL_KEY
        )
        <foreach collection="list" item="list" index="index" separator="union all">
            (
                select
                    #{list.uuid,jdbcType=VARCHAR},
                    #{list.nodeId,jdbcType=VARCHAR},
                    #{list.nodeCode,jdbcType=VARCHAR},
                    #{list.nodeName,jdbcType=VARCHAR},
                    #{list.procdefId,jdbcType=VARCHAR},
            #{list.actDeModelKey,jdbcType=VARCHAR}
                from dual
            )
        </foreach>
    </insert>
    <insert id="insertList">
        insert into act_my_node_code (
        uuid,
        node_id,
        node_code,
        node_name,
        procdef_id,
        ACT_DE_MODEL_KEY
        )
        values
        <foreach collection="list" item="list" index="index" separator=",">
            (
            select
            #{list.uuid},
            #{list.nodeId,jdbcType=VARCHAR},
            #{list.nodeCode,jdbcType=VARCHAR},
            #{list.nodeName,jdbcType=VARCHAR},
            #{list.procdefId,jdbcType=VARCHAR},
            #{list.actDeModelKey,jdbcType=VARCHAR}
            from dual
            )
        </foreach>
    </insert>

    <select id="getPageSet" resultType="com.sbtr.workflow.model.ActMyNodeCode">
        select
        <include refid="Base_Column_List"></include>
        from act_my_node_code
    </select>
    <select id="selectByParam" resultType="com.sbtr.workflow.model.ActMyNodeCode">
        select <include refid="Base_Column_List" />  from act_my_node_code
        where act_de_model_key = #{actDeModelKey} and node_id=#{nodeId}
        <if test="procdefId!=null and procdefId!=''">
            and procdef_id=#{procdefId}
        </if>
    </select>
    <select id="getListByActDeModelKeyAndProcdefId" resultType="com.sbtr.workflow.model.ActMyNodeCode">
        select
        <include refid="Base_Column_List"></include>
        from act_my_node_code where act_de_model_key = #{key} and procdef_id=#{procdefId}
    </select>
    <update id="updateNodeCodeByParam">
        update act_my_node_code set node_code=#{nodeCode,jdbcType=VARCHAR} where uuid =#{uuid}
    </update>
    <update id="updateProDefByParam">
        update act_my_node_code set procdef_id=#{procdefId,jdbcType=VARCHAR} where uuid =#{uuid}
    </update>
    <update id="updateProcdefIdByModelKeyAndProcdef">
        update act_my_node_code set procdef_id=#{procdefId,jdbcType=VARCHAR}  where model_key = #{key} and procdef_id =#{id}
    </update>
    <update id="updateProcdefIdByModelKey">
        update act_my_node_code set procdef_id=#{procdefId,jdbcType=VARCHAR} where act_de_model_key = #{key} and (coalesce(procdef_id ,N'') = '')
    </update>
    <delete id="deleteByModelKeyAnProcdefId">
        delete from act_my_node_code where act_de_model_key = #{key} and procdef_id=#{id}
    </delete>
</mapper>
