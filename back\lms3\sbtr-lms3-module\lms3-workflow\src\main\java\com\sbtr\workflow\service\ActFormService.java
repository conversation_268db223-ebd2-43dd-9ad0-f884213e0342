package com.sbtr.workflow.service;

import com.sbtr.workflow.model.ActForm;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;

/**
 * 常用外置表单数据主表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:33:52
 */
public interface ActFormService extends WorkflowBaseService<ActForm, String> {

    /**
     * 分页
     *
     * @return
     * @Param pageParam 分页参数
     * @Param filterSort 过滤排序字段
     */
    PageSet<ActForm> getPageSet(PageParam pageParam, String filterSort);

    /**
     * 批量删除
     *
     * @return
     * @Param uuid 主键
     */
    int executeDeleteBatch(String[] uuid);

}
