package com.lms.common.controller;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.feign.api.CommonBaseApi;
import com.lms.common.feign.api.CommonSystemApi;
import com.lms.common.util.*;
import com.lms.common.dao.query.BuildQueryParameter;
import com.lms.common.dao.query.Parameter;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @version V1.0
 * @fileoverview: <br>
 * Copyright @ 2017-2020 SBTR LTD. <br>
 * This program is protected by copyright laws. <br>
 * Project Name:SBTR-TSS <br>
 * 模块名称:com.cepreitr.common.commondomain.base.web.BaseController <br>
 * 功能:基础控制器，实现前端request参数解析
 * @create 2017-11-23 8:47
 */
@Slf4j
public abstract class BaseController<T> {
    /**
     * @param request 前端查询参数存放在request中，并进行解析
     * 仅对“PARAMETER_”开头的参数转为查询条件
     */
    @Resource
    public RedisUtil redisUtil;
    @Resource
    public CommonSystemApi commonSystemApi;
    @Resource
    public CommonBaseApi commonBaseApi;
    public PageInfo getPageInfo(JSONObject jsonObject) {
        PageInfo pageInfo = new PageInfo();
        int pageSizeInt = NumberHelper.string2Int(jsonObject.get("pageSize"), 20);
        int pageIndexInt = NumberHelper.string2Int(jsonObject.get("pageIndex"), 1);

        pageInfo.setPageIndex(pageIndexInt);
        pageInfo.setPageSize(pageSizeInt);
        pageInfo.setParameters(this.buildParameterList(jsonObject));
        // 获取排序字段
        String orderName = StringHelper.null2String(jsonObject.getString("orderName"));
        String sortType = StringHelper.null2String(jsonObject.getString("sortType"));

        if (ObjectUtil.isNotEmpty(orderName) && ObjectUtil.isNotEmpty(orderName)) {
            pageInfo.setOrderName(orderName);
            if (sortType.equals("ascending") || sortType.equals("asc")) {
                pageInfo.setSort(PageInfo.ASC);
            } else {
                pageInfo.setSort(PageInfo.DESC);
            }
        }

        return pageInfo;
    }

    public Page createPage(List list, Page page){
        Page<Object> pageResult = new Page<>();
        pageResult.setRecords(list);
        pageResult.setTotal(page.getTotal());
        pageResult.setSize(page.getSize());
        pageResult.setCurrent(page.getCurrent());
        pageResult.setPages(page.getPages());
        return pageResult;
    }

    /**
     * 获取通用查询参数.
     *
     * @param jsonObject
     * @return 参数列表
     */
    public List<Parameter> buildParameterList(JSONObject jsonObject) {
        List<Parameter> list = new ArrayList<Parameter>();
        list = BuildQueryParameter.buildParameterList(jsonObject);
        return list;
    }


}
