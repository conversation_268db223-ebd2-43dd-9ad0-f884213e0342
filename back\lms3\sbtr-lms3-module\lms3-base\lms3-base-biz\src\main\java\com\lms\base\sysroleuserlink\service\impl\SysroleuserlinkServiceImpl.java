package com.lms.base.sysroleuserlink.service.impl;

import com.lms.base.feign.model.Sysroleuserlink;
import com.lms.base.sysroleuserlink.mapper.SysroleuserlinkMapper;
import com.lms.base.sysroleuserlink.service.SysroleuserlinkService;
import com.lms.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SysroleuserlinkServiceImpl extends BaseServiceImpl<SysroleuserlinkMapper, Sysroleuserlink> implements SysroleuserlinkService {

    @Resource
    private SysroleuserlinkMapper sysroleuserlinkMapper;

    @Override
    public void deleteRoleLinkByUser(String userId) {
        sysroleuserlinkMapper.deleteRoleLinkByUser(userId);
    }

    @Override
    public List<String> getUserNamesBySysRoleLink(String roleId) {
        return sysroleuserlinkMapper.getUserNamesBySysRoleLink(roleId);
    }
}
