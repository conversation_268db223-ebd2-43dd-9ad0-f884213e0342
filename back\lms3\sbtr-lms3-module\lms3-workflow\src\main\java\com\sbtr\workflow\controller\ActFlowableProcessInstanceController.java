package com.sbtr.workflow.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

import com.alibaba.fastjson.JSON;
import com.lms.common.model.Result;
import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.model.ActMyModel;
import com.sbtr.workflow.service.ActMyModelService;
import com.sbtr.workflow.service.ApiFlowableProcessInstanceService;
import com.sbtr.workflow.service.ApiFlowableTaskService;
import com.sbtr.workflow.utils.OkHttpClientUtil;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.StringUtil.StringUtils;
import com.sbtr.workflow.vo.ProcessInstanceVo;
import com.sbtr.workflow.vo.TaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import liquibase.pro.packaged.S;
import org.apache.commons.collections.CollectionUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

/**
 * 流程实例
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
@Api(tags = {"流程实例"})
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/apiFlowableProcessInstance")
public class ActFlowableProcessInstanceController extends WorkflowBaseController {

    @Autowired
    private ApiFlowableProcessInstanceService apiFlowableProcessInstanceService;
    @Autowired
    private HistoryService historyService;

    @Autowired
	private RepositoryService repositoryService;

    @Autowired
    private ActMyModelService flowModelService;
	@Autowired
    private ApiFlowableTaskService apiFlowableTaskService;
	@Autowired
    private OkHttpClientUtil okHttpClientUtil;

    //流程实例列表查询
    @ResponseBody
    @RequestMapping(value = "/getPageSet", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam, String name) {
        PageSet<ProcessInstanceVo> pageSet = apiFlowableProcessInstanceService.getPageSet(pageParam, name);
        return pageSet;
    }

    //流程实例列表查询
    @ResponseBody
    @RequestMapping(value = "/getProcessInstance", method = RequestMethod.POST)
    public Result<Object> getProcessInstance(@RequestParam(value = "modelKey") String modelKey, @RequestParam(value = "businessKey") String businessKey) {
        return Result.OK(apiFlowableProcessInstanceService.getProcessInstance(modelKey, businessKey));
    }

    @ResponseBody
    @RequestMapping(value = "/getProcessInstanceByBusinessKeys", method = RequestMethod.POST)
    Result<Object> getProcessInstanceByBusinessKeys(@RequestParam(value = "modelKey") String modelKey, @RequestParam(value = "businessKeys") List<String> businessKeys){
        return Result.OK(apiFlowableProcessInstanceService.getProcessInstanceByBusinessKeys(modelKey, businessKeys));
    }


    // 通用的提交启动流程
    @Log(title = "流程实例-通用的提交启动",module = LogModule.WORKFLOW,businessType = BusinessType.START, method = "getLogMessage")
    @ApiOperation(value = "通用的提交启动流程")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "modelKey", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String"),
            @ApiImplicitParam(name = "businessUuid", value = "业务主键", defaultValue = "业务主键", required = true, dataType = "String"),
            @ApiImplicitParam(name = "businessTitle", value = "业务标题", defaultValue = "业务标题", required = true, dataType = "String"),
            @ApiImplicitParam(name = "assignUser", value = "指定下一步处理人工号 非必填", defaultValue = "ewsd0001", required = false, dataType = "String"),
            @ApiImplicitParam(name = "duplicateUser", value = "抄送人工号非必填,多个逗号隔开 非必填", defaultValue = "ewsd0001", required = false, dataType = "String"),
            @ApiImplicitParam(name = "skipNode", value = "是否默认处理下一步 true false 非必填", defaultValue = "false", required = false, dataType = "String"),
            @ApiImplicitParam(name = "params", value = "表单数据Map形式传过来，非必填", defaultValue = "false", required = false, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/startProcessInstanceByKey", method = RequestMethod.POST)
    public Object startProcessInstanceByKey(String modelKey, String businessUuid,String businessTitle,
                                            String assignUser,String duplicateUser, String skipNode,@RequestParam Map<String, Object> params) {
        Object object = apiFlowableProcessInstanceService.startProcessInstanceByKey(modelKey, businessUuid, businessTitle,
                assignUser, duplicateUser, skipNode,params);
        setLogMessage(businessTitle,"");
        return object;
    }

    @Log(title = "流程实例-通用的提交启动",module = LogModule.WORKFLOW,businessType = BusinessType.START, method = "getLogMessage")
    @ApiOperation(value = "通用的提交启动流程")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "modelKey", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String"),
            @ApiImplicitParam(name = "businessUuid", value = "业务主键", defaultValue = "业务主键", required = true, dataType = "String"),
            @ApiImplicitParam(name = "businessTitle", value = "业务标题", defaultValue = "业务标题", required = true, dataType = "String"),
            @ApiImplicitParam(name = "assignUser", value = "指定下一步处理人工号 非必填", defaultValue = "ewsd0001", required = false, dataType = "String"),
            @ApiImplicitParam(name = "duplicateUser", value = "抄送人工号非必填,多个逗号隔开 非必填", defaultValue = "ewsd0001", required = false, dataType = "String"),
            @ApiImplicitParam(name = "skipNode", value = "是否默认处理下一步 true false 非必填", defaultValue = "false", required = false, dataType = "String"),
            @ApiImplicitParam(name = "params", value = "表单数据Map形式传过来，非必填", defaultValue = "false", required = false, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/startProcessInstanceByKeyForApi", method = RequestMethod.POST)
    public Result startProcessInstanceByKeyForApi(@RequestParam String modelKey, @RequestParam String businessUuid, @RequestParam String businessTitle,
                                                  @RequestParam String assignUser, @RequestParam String duplicateUser, @RequestParam String skipNode, @RequestParam Map<String, Object> params){
        HashMap resultMap = (HashMap) apiFlowableProcessInstanceService.startProcessInstanceByKey(modelKey, businessUuid, businessTitle,
                assignUser, duplicateUser, skipNode,params);
        setLogMessage(businessTitle,"");
        Integer statusCode = (Integer) resultMap.get("statusCode");
        String errMessage = (String) resultMap.get("message");
        if (200 == statusCode){
            return com.lms.common.model.Result.OK(resultMap);
        }else {
            return com.lms.common.model.Result.error(errMessage);
        }
    }


    @ApiOperation(value = "终止按钮方法")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "实例id", defaultValue = "processInstanceId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "message", value = "消息中心", defaultValue = "message", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/stopProcessInstanceById", method = RequestMethod.POST)
    public Object stopProcessInstanceById(String taskId, String processInstanceId, String message) {
        Object object = apiFlowableProcessInstanceService.stopProcessInstanceById(taskId, processInstanceId, message);
        return object;
    }


    @Log(title = "流程实例-通用的提交启动",module = LogModule.WORKFLOW,businessType = BusinessType.FINISH, method = "getLogMessage")
    @ApiOperation(value = "流程实例终止")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String"),
            @ApiImplicitParam(name = "message", value = "业务主键", defaultValue = "businessUuid", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/stopProcessInstanceByProcessInstanceId", method = RequestMethod.POST)
    public Object stopProcessInstanceByProcessInstanceId(String processInstanceId, String message) {
        setLogMessage(apiFlowableProcessInstanceService.getProcessInstanceNameByProcessInstanceId(processInstanceId),"");
        Object object = apiFlowableProcessInstanceService.stopProcessInstanceByProcessInstanceId(processInstanceId, message);
        return object;
    }

    @Log(title = "流程实例-挂起激活",module = LogModule.WORKFLOW,businessType = BusinessType.ACTIVATE, method = "getLogMessage")
    @ApiOperation(value = "流程实例挂起激活")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String"),
            @ApiImplicitParam(name = "suspensionState", value = "业务主键", defaultValue = "businessUuid", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/suspendOrActivateProcessInstanceById", method = RequestMethod.POST)
    public Object suspendOrActivateProcessInstanceById(String processInstanceId, Integer suspensionState) {
        setLogMessage(apiFlowableProcessInstanceService.getProcessInstanceNameByProcessInstanceId(processInstanceId),"");
        Object object = apiFlowableProcessInstanceService.suspendOrActivateProcessInstanceById(processInstanceId, suspensionState);
        return object;
    }

    @Log(title = "流程实例-点击办理",module = LogModule.WORKFLOW,businessType = BusinessType.APPROVE, method = "getLogMessage")
    @ApiOperation(value = "点击办理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "modelKey", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String"),
            @ApiImplicitParam(name = "taskId", value = "业务主键", defaultValue = "businessUuid", required = true, dataType = "String"),
            @ApiImplicitParam(name = "processInstanceId", value = "业务主键", defaultValue = "businessUuid", required = true, dataType = "String"),
            @ApiImplicitParam(name = "nodeId", value = "业务主键", defaultValue = "businessUuid", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/clickStartProcess", method = RequestMethod.POST)
    public Object clickStartProcess(String modelKey, String taskId, String processInstanceId, String nodeId, String processDefinitionId) throws Exception {
        setLogMessage(apiFlowableProcessInstanceService.getProcessInstanceNameByProcessInstanceId(processInstanceId),"");
        Object object = apiFlowableProcessInstanceService.clickStartProcess(
                modelKey,
                taskId,
                processInstanceId,
                nodeId,
                processDefinitionId);
        return object;
    }

    // 根据流程实例/业务id/taskid获取流程图及审批意见信息
    @ResponseBody
    @RequestMapping(value = "/getProcessDetailByTaskId", method = RequestMethod.POST)
    public Object getProcessDetailByTaskId(String processInstanceId,String businessKey,String taskId) throws Exception {
        List<TaskVo> taskVos = apiFlowableTaskService.getTaskRefByParam(processInstanceId,businessKey,taskId);
        Object object = null;
        if(taskVos.size()>0){
            TaskVo taskVo = taskVos.get(0);
            object = apiFlowableProcessInstanceService.clickStartProcess(
                    taskVo.getModelKey(),
                    taskVo.getTaskId(),
                    taskVo.getProcessInstanceId(),
                    taskVo.getNodeId(),
                    taskVo.getProcessDefinitionId());
        }
        return object;
    }

    // 根据taskid获取流程图及审批意见信息
    @ResponseBody
    @RequestMapping(value = "/getToDoTaskById", method = RequestMethod.POST)
    public Object getToDoTaskById(String taskId) throws Exception {
        TaskVo taskVo = apiFlowableTaskService.getToDoTasks(taskId);
        Map map = new HashMap();
        boolean isFirstClick = false;
        if(taskVo != null){
            if (taskVo.getOpeningTime() == null) {//首次点击更新开封时间
                isFirstClick = true;
                Date date = DateUtil.date();
                apiFlowableTaskService.updateTaskOpeningTimeByKey(taskVo.getTaskId(), date);
            }
            map = (Map) apiFlowableProcessInstanceService.clickStartProcess(
                    taskVo.getModelKey(),
                    taskVo.getTaskId(),
                    taskVo.getProcessInstanceId(),
                    taskVo.getNodeId(),
                    taskVo.getProcessDefinitionId());
            map.put("task",taskVo);
            if (isFirstClick){
                businessSystemDataService.pushViewTask((String) map.get("businessKey"));
            }
        }else{
            map.put("message","当前待办任务不存在或已处理!");
        }
        return map;
    }

    @ApiOperation(value = "根据业务id获取审批意见")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "businessKey", value = "业务ID", defaultValue = "businessKey", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/getListCommentsByBusinessKey", method = RequestMethod.POST)
    public Object getListCommentsByBusinessKey(String businessKey) {
        return apiFlowableProcessInstanceService.getListCommentsByBusinessKey(businessKey);
    }


    @ApiOperation(value = "点击流程图")
    @ResponseBody
    @RequestMapping(value = "/clickFlowChart", method = RequestMethod.POST)
    // 点击流程图
    public Object clickFlowChart(String businessKey) {
        if(StringUtils.isEmpty(businessKey)){
            return failure("没有传递businessKey！");
        }
        /**1:使用历史的流程实例查询，返回历史的流程实例对象，获取流程实例ID*/
        HistoricProcessInstance hpi = historyService.createHistoricProcessInstanceQuery()//对应历史的流程实例表
                .processInstanceBusinessKey(businessKey)//使用BusinessKey字段查询
                .singleResult();
        if (ObjectUtil.isNull(hpi)) {
            return failure("查询不到历史流程数据！！！,该流程数据已被删除");
        }
        //流程实例ID
        String processInstanceId = hpi.getId();
        //List<Comment> list = taskService.getProcessInstanceComments(processInstanceId);
        String processDefinitionId = hpi.getProcessDefinitionId();
        ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) repositoryService.getProcessDefinition(processDefinitionId);
        String modelKey = processDefinitionEntity.getKey();
        String taskId = "";
        Example example = new Example(ActMyModel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modelKey);
        if (org.apache.commons.lang.StringUtils.isNotBlank(processDefinitionId)) {
            criteria.andEqualTo("procdefId", processDefinitionId);
        }
        List<ActMyModel> lists = flowModelService.selectByExample(example);
        if (CollectionUtils.isEmpty(lists)) {
            return failure("流程与表单关联表查找不到数据,请检查");
        }
        HashMap<String, Object> map = new HashMap<>();
        //if (org.apache.commons.lang.StringUtils.isNotBlank(taskId)) {
        //    List<CommentBean> commentBeanList = workflowService.getCommentBeanByTaskId(taskId);
        //    map.put("commentBeanList", commentBeanList);
        //}
        map.put("lists", lists.get(0));
        // 查询历史节点表 ACT_HI_ACTINST
        List<HistoricActivityInstance> historicActivityInstanceList = getHistoricActivityInstAsc(processInstanceId);
        //1 正在执行的节点
        List<String> runningActivitiIdList = new ArrayList<String>();
        //2 获取已流经的流程线
        List<String> highLightedFlowIds = new ArrayList<>();
        //3.已执行历史节点
        List<String> executedActivityIdList = new ArrayList<String>();
        historicActivityInstanceList.forEach(historicActivityInstance -> {
            //1
            if (null == historicActivityInstance.getEndTime()) {
                runningActivitiIdList.add(historicActivityInstance.getActivityId());
            }
            //2
            if (historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                highLightedFlowIds.add(historicActivityInstance.getActivityId());
            }
            //3
            if (null != historicActivityInstance.getEndTime() && !historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                executedActivityIdList.add(historicActivityInstance.getActivityId());
            }

        });

        map.put("inProgress", runningActivitiIdList);
        highLightedFlowIds.addAll(executedActivityIdList);
        map.put("notInProgress", highLightedFlowIds);

        //4查询当前节点的按钮

        //4.通过流程定义id与modelKey查询 流程节点按钮
        //List<ActMyNodeButton> flowNodeButtons = flowNodeButtonService.getListByActDeModelKeyAndProcdefId(modelKey, processDefinitionId);
        //  处理节点表单
        //for (int i = 0; i < flowNodeButtons.size(); i++) {
        //    flowNodeButtons.get(i).setNodeFormPath(flowNodeButtons.get(0).getNodeFormPath());
        //}
        //map.put("flowNodeButtons", flowNodeButtons);
        map.put("flowNodeButtons", null);
        //  以下为特殊处理 自己写的表单页面挂载流程需要返回业务详情数据  广州赛宝腾睿信息科技有限公司
        //2  通过任务对象获取流程实例
        //ProcessInstance pi = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        ////3 通过流程实例获取“业务键”
        //String businessKey = pi.getBusinessKey();
        ////表单获取详情数据的请求地址
        //String str = flowNodeButtons.get(0).getNodeFormEditPath();
        //JSONObject json_test = null;
        //if (org.apache.commons.lang.StringUtils.isNotBlank(str)) {
        //    String gateway = request.getHeader("Gateway");
        //    String token = request.getHeader(ConstParamUtil.X_ACCESS_TOKEN);
        //    //链式构建请求，带cookie请求
        //    Map<String, Object> paramMap = new HashMap<>();
        //    paramMap.put("uuid", businessKey);
        //    String result2 = HttpRequest.post(gateway + str)
        //            .cookie("token=" + token)
        //            .form(paramMap)
        //            .timeout(20000)
        //            .execute().body();
        //    json_test = JSONObject.parseObject(result2);
        //}
        //map.put("businessData", json_test);
        map.put("businessData", null);
        //5 查询当前节点的字段属性
        map.put("flowNodeField", "");
        return map;

    }


    /**
     * 通过流程实例ID获取流程中已经执行的节点，按照执行先后顺序排序
     *
     * @param procInstId
     * @return
     */
    public List<HistoricActivityInstance> getHistoricActivityInstAsc(String procInstId) {
        return historyService.createHistoricActivityInstanceQuery().processInstanceId(procInstId)
                .orderByHistoricActivityInstanceStartTime().asc().list();
    }



    @ApiOperation(value = "获取当前任务节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "实例Id", defaultValue = "processInstanceId", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/getProcessFreeJumpData", method = RequestMethod.POST)
    public List<TaskVo> getProcessFreeJumpData(String processInstanceId) {

        List<TaskVo> task = apiFlowableProcessInstanceService.getProcessFreeJumpData(processInstanceId);
        return task;
    }


    @ApiOperation(value = "根据流程实例id获取所有任务节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "实例Id", defaultValue = "processInstanceId", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping("/getAllUserTaskListByProcessInstanceId")
    public Object getAllUserTaskListByProcessInstanceId(String processInstanceId) {
        return apiFlowableProcessInstanceService.getAllUserTaskListByProcessInstanceId(processInstanceId);
    }


    @ApiOperation(value = "流程自由跳转")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "实例Id", defaultValue = "processInstanceId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "nodeId", value = "当前节点Id", defaultValue = "nodeId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "toNodeId", value = "要跳转的节点Id", defaultValue = "toNodeId", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/processFreeJump", method = RequestMethod.POST)
    public Object processFreeJump(String processInstanceId, String nodeId, String toNodeId) {
        return apiFlowableProcessInstanceService.processFreeJump(processInstanceId, nodeId, toNodeId);
    }

    @ApiOperation(value = "流程自由跳转")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "实例Id", defaultValue = "processInstanceId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "nodeId", value = "当前节点Id", defaultValue = "nodeId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "toNodeId", value = "要跳转的节点Id", defaultValue = "toNodeId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "message", value = "审批意见", defaultValue = "message", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/processFreeJumpForApi", method = RequestMethod.POST)
    public Object processFreeJumpForApi(String processInstanceId, String nodeId, String toNodeId, String message) {
        return apiFlowableProcessInstanceService.processFreeJumpForApi(processInstanceId, nodeId, toNodeId, message);
    }


    @Log(title = "流程实例-删除",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ApiOperation(value = "流程实例删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "实例Id", defaultValue = "processInstanceId", required = true, dataType = "String")
    })
    @ResponseBody
    @PostMapping(value = "/deleteProcessInstanceById")
    public Object deleteProcessInstanceById(@RequestParam(value = "processInstanceId", required = true) String processInstanceId) {
        setLogMessage(apiFlowableProcessInstanceService.getProcessInstanceNameByProcessInstanceId(processInstanceId),"");
        return apiFlowableProcessInstanceService.deleteProcessInstanceById(processInstanceId);
    }

    @ApiOperation(value = "外置表单在流程中心发起流程")
    @ResponseBody
    @PostMapping(value = "/customizeFormStartProcessInstance")
    public Object customizeFormStartProcessInstance(String modelKey,
                                                    String businessTitle,
                                                    String skipNode,
                                                    @RequestParam Map<String, Object> map) {
        return apiFlowableProcessInstanceService.customizeFormStartProcessInstance(modelKey, businessTitle,skipNode, map);
    }


    @Log(title = "流程实例-作废",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ApiOperation(value = "流程作废")
    @ResponseBody
    @PostMapping(value = "/deleteProcessInstance")
    public Object deleteProcessInstance(String processInstanceId, String reason) {
        setLogMessage(apiFlowableProcessInstanceService.getProcessInstanceNameByProcessInstanceId(processInstanceId),"");
        return apiFlowableProcessInstanceService.deleteProcessInstance(processInstanceId, reason);
    }

}
