<template>
  <div class="course-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
      layout="horizontal"
    >
      <a-form-item label="课程编号" name="courseNumber">
        <a-input
          v-model:value="formData.courseNumber"
          placeholder="若不填则由后台自动生成"
          allow-clear
        />
      </a-form-item>

      <a-form-item label="课程名称" name="courseName">
        <a-input
          v-model:value="formData.courseName"
          placeholder="请输入课程名称"
          allow-clear
        />
      </a-form-item>

      <a-form-item label="课程类型" name="courseType">
        <a-select
          v-model:value="formData.courseType"
          placeholder="请选择课程类型"
          allow-clear
          style="width: 100%"
        >
          <a-select-option
            v-for="option in courseTypeOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="课时" name="classHour">
        <a-input-number
          v-model:value="formData.classHour"
          placeholder="请输入课时"
          :min="1"
          :max="200"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="版本" name="version">
        <a-input
          v-model:value="formData.version"
          placeholder="请输入版本号"
          allow-clear
        />
      </a-form-item>

      <a-form-item label="教员" name="teacherName">
        <a-input
          v-model:value="formData.teacherName"
          placeholder="请选择教员"
          readonly
        >
          <template #addonAfter>
            <a-button
              type="text"
              @click="handleTeacherSelect"
              style="padding: 0; height: auto;"
            >
              <SearchOutlined />
            </a-button>
          </template>
        </a-input>
      </a-form-item>

      <a-form-item label="内容" name="courseContent">
        <a-textarea
          v-model:value="formData.courseContent"
          placeholder="请输入课程内容"
          :rows="4"
          :maxlength="1500"
          show-count
        />
      </a-form-item>

      <a-form-item label="状态" name="status">
        <a-select
          v-model:value="formData.status"
          placeholder="请选择状态"
          allow-clear
        >
          <a-select-option :value="5">拟制</a-select-option>
          <a-select-option :value="1">发布</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>

    <div class="form-actions">
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </a-button>
    </div>

    <!-- 教员选择器 -->
    <PersonSelector
      :visible="teacherSelectorVisible"
      :multiple="false"
      @close="handleTeacherClose"
      @confirm="handleTeacherConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from "vue";
import { message } from "ant-design-vue";
import { SearchOutlined } from "@ant-design/icons-vue";
import type { FormInstance } from "ant-design-vue";
import { addCourse, editCourse } from "@/api/mrPlatform";
import { courseTypeOptions } from "@/data/mrPlatform";
import PersonSelector from "@/components/Selector/personSelector.vue";

// Props 定义
interface Props {
  visible: boolean;
  course?: any;
  isEdit?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  course: () => ({}),
  isEdit: false
});

// Emits 定义
const emit = defineEmits<{
  close: [value: boolean];
  submit: [];
}>();

// 响应式数据
const formRef = ref<FormInstance>();
const loading = ref(false);
const teacherSelectorVisible = ref(false);

const formData = reactive({
  courseNumber: "",
  courseName: "",
  courseType: undefined,
  classHour: undefined,
  version: "",
  teacherName: "",
  teacherId: "",
  courseContent: "",
  status: undefined
});

// 表单验证规则
const rules = {
  courseNumber: [
    { max: 50, message: "课程编号不能超过50字", trigger: "change" }
  ],
  courseName: [
    { required: true, message: "课程名称不能为空", trigger: "blur" },
    { max: 150, message: "课程名称不能超过150字", trigger: "change" }
  ],
  courseType: [
    { required: true, message: "课程类型不能为空", trigger: "change" }
  ],
  courseContent: [
    { required: true, message: "内容不能为空", trigger: "blur" },
    { max: 1500, message: "内容不能超过1500字", trigger: "change" }
  ],
  classHour: [
    { required: true, message: "课时不能为空", trigger: "blur" },
    { type: "number", min: 1, message: "课时必须大于0", trigger: "blur" }
  ],
  teacherName: [
    { required: true, message: "教员不能为空", trigger: "change" }
  ],
  status: [
    { required: true, message: "请选择状态", trigger: "change" }
  ]
};

// 教员选择处理
const handleTeacherSelect = () => {
  teacherSelectorVisible.value = true;
};

// 教员选择确认
const handleTeacherConfirm = (selectedTeachers: any[]) => {
  if (selectedTeachers && selectedTeachers.length > 0) {
    const teacher = selectedTeachers[0]; // 只取第一个，因为只能选择一个教员
    formData.teacherName = teacher.name;
    formData.teacherId = teacher.id;
  }
};

// 教员选择取消
const handleTeacherClose = (visible: boolean) => {
  teacherSelectorVisible.value = visible;
};

// 监听课程数据变化，填充表单
watch(
  () => props.course,
  (newCourse) => {
    if (newCourse && Object.keys(newCourse).length > 0) {
      Object.assign(formData, {
        ...newCourse,
        // 确保数值类型正确
        classHour: newCourse.classHour ? Number(newCourse.classHour) : undefined,
        status: newCourse.status !== undefined ? Number(newCourse.status) : undefined,
        courseType: newCourse.courseType !== undefined ? newCourse.courseType : undefined
      });
    } else {
      // 重置表单
      Object.assign(formData, {
        courseNumber: "",
        courseName: "",
        courseType: undefined,
        classHour: undefined,
        version: "",
        teacherName: "",
        teacherId: "",
        courseContent: "",
        status: undefined
      });
    }
  },
  { immediate: true, deep: true }
);

// 监听弹窗显示状态，重置表单验证
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      nextTick(() => {
        formRef.value?.clearValidate();
      });
    }
  }
);

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;

    const submitData = { ...formData };

    let response;
    if (props.isEdit) {
      // 编辑模式
      response = await editCourse(submitData);
    } else {
      // 新增模式
      response = await addCourse(submitData);
    }

    if (response && response.success) {
      message.success(props.isEdit ? "编辑成功" : "新增成功");
      emit("submit");
      emit("close", false);
    } else {
      message.error(response?.message || (props.isEdit ? "编辑失败" : "新增失败"));
    }
  } catch (error) {
    console.error("表单提交失败:", error);
    message.error("操作失败");
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit("close", false);
};
</script>

<style lang="scss" scoped>
.course-form {
  padding: 20px 0;
  min-width: 600px;
  width: 100%;
  max-width: 800px;

  /* 确保在缩放环境下正常显示 */
  transform-origin: top left;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

// 确保表单在弹窗中有合适的宽度
:deep(.ant-form) {
  width: 100%;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-input),
:deep(.ant-select),
:deep(.ant-input-number) {
  width: 100%;
}

/* 响应式调整，考虑全局缩放 */
@media (max-width: 1366px) {
  .course-form {
    padding: 16px 0;
    min-width: 500px;
    max-width: 700px;
  }

  .form-actions {
    margin-top: 20px;
    padding-top: 12px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 14px;
  }
}

@media (max-width: 1024px) {
  .course-form {
    padding: 12px 0;
    min-width: 450px;
    max-width: 600px;
  }

  .form-actions {
    margin-top: 16px;
    padding-top: 10px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 12px;
  }
}

@media (max-width: 768px) {
  .course-form {
    padding: 10px 0;
    min-width: 400px;
    max-width: 500px;
  }

  .form-actions {
    margin-top: 14px;
    padding-top: 8px;
    gap: 8px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 10px;
  }
}

/* 确保在全局缩放环境下表单元素正确显示 */
:deep(.ant-form-item-label) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.ant-form-item-control) {
  flex: 1;
  min-width: 0;
}

/* 优化按钮在缩放环境下的显示 */
.form-actions :deep(.ant-btn) {
  min-width: 60px;
  height: 32px;
  font-size: 14px;
}

/* 确保输入框在缩放后仍然可用 */
:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-input-number-input) {
  font-size: 14px;
  line-height: 1.5;
}

/* 优化文本域在缩放环境下的显示 */
:deep(.ant-input[type="textarea"]) {
  resize: vertical;
  min-height: 80px;
}
</style>
