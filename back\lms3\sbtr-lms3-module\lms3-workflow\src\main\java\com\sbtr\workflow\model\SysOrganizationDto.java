package com.sbtr.workflow.model;

import com.sbtr.workflow.utils.tree.TreeNode;

import java.util.List;

/**
 * 组织机构和用户信息
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-12-19 14:19:26
 */
public class SysOrganizationDto extends TreeNode<SysOrganizationDto> {

    /**
     * 组织机构名称
     */
    private String text;

    private String orgName;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    /**
     * 用户信息
     */
    List<SysUserDto> list;


    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public List<SysUserDto> getList() {
        return list;
    }

    public void setList(List<SysUserDto> list) {
        this.list = list;
    }
}
