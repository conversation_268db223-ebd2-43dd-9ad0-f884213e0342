package com.lms.system.role.controller;

import com.alibaba.fastjson.JSONObject;
import com.lms.system.feign.model.Role;
import com.lms.system.role.service.RoleService;
import com.lms.system.role.service.impl.RoleServiceImpl;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/role")
@Api(value = "人员角色管理", tags = "人员角色管理")
public class RoleController extends BaseController<Role> {

    @Resource
    private RoleService roleService;

    // 列表显示所有的角色
    @LMSLog(desc = "查询角色列表", otype = LogType.List, objname = "查询角色列表")
    @PostMapping(value = {"/listpage"})
    @ApiOperation(value = "查询角色列表", httpMethod = "POST")
    public Result roleList(@RequestBody JSONObject request) {
        PageInfo pageInfo = super.getPageInfo(request);
        Page<Role> roles = roleService.listByCondition(pageInfo);
        return Result.OK(roles);
    }

    // 列表显示所有的角色
    @LMSLog(desc = "查询所有角色", otype = LogType.List, objname = "查询所有角色")
    @GetMapping(value = {"/listAll"})
    @ApiOperation(value = "查询所有角色", httpMethod = "GET")
    public Result listAll() {
        List<Role> roles = roleService.list();
        return Result.OK(roles);
    }

    @LMSLog(desc = "编辑角色", otype = LogType.Update)
    @RequestMapping(value = {"/modify"}, method = RequestMethod.POST)
    @ApiOperation(value = "编辑角色", httpMethod = "POST")
    public Result updateRole(@RequestBody Role role) {
        this.roleService.saveOrUpdate(role);
        return Result.OK(role);
    }

    // 批量删除
    @LMSLog(desc = "删除角色信息", otype = LogType.Delete)
    @DeleteMapping(value = {"/batchremove"})
    @ApiOperation(value = "删除角色信息", httpMethod = "DELETE")
    public Result batchDeletePerson(@RequestParam("ids") String ids) {
        String[] idarray =  ids.split(",");
        roleService.removeBatchByIds(Arrays.asList(idarray));
        return Result.OK();
    }
//
//	// 获取角色类型
//	@RequestMapping(value = { "/roleType" }, method = RequestMethod.GET)
//	public String getRoleType(ModelMap mm) {
//		// 构造角色类型的枚举
//		List<Integer> roleType = roleService.roleType();
//		mm.put(ModelMapKey.RESULTKEY, roleType);
//		return ViewName.SuccessView;
//
//	}
//
	// 角色新增的处理
	@LMSLog(desc = "新增角色", otype = LogType.Save)
	@RequestMapping(value = { "/add" }, method = RequestMethod.POST)
    @ApiOperation(value = "新增角色", httpMethod = "POST")
    public Result saveRole(@RequestBody Role role) {
//        int code = RoleEum.;
//        role.setRolekind(code);
		this.roleService.saveOrUpdate(role);
		return Result.OK();
	}
//
    // 列出角色下相应用户
//	@RequestMapping(value = { "/listusers" }, method = RequestMethod.GET)
//	public String userList(@RequestParam("roleid") String id) {
//
//		Role role = this.roleService.getRolesDetail(id);
//		List<Users> users = roleService.getUserByRole(role);
//		mm.put(ModelMapKey.RESULTKEY, users);
//		return ViewName.SuccessView;
//	}
//
//	// 为角色添加相应用户的处理方法
//	@LMSLog(desc = "为角色添加用户", otype = LogType.Save, order = 1, method = "")
//	@RequestMapping(value = { "/{roleid}/users" }, method = RequestMethod.POST)
//	public String relateUser(ModelMap mm, @PathVariable("roleid") String id,
//			@RequestBody List<String> users) {
//		// 这里的List<Integer> 能否直接是List<Users>？
//		roleService.addRefUsers(id, users);
//		return ViewName.SuccessView;
//	}
//
//	// 删除角色下的用户
//	@LMSLog(desc = "删除角色下的用户", otype = LogType.Delete, order = 1, method = "")
//	@RequestMapping(value = { "/{roleid}/users/{userids}" }, method = RequestMethod.DELETE)
//	public String removeUser(ModelMap mm, @PathVariable("roleid") String id,
//			@PathVariable("userids") String[] ids) {
//		// 这里ids能否直接为List<Users>？
//		if (ids.length == 0) {
//			ErrorMessage em = ErrorMessageHelper.getErrorMessage("请选择需要删除的数据!");
//			mm.put(ModelMapKey.ERRORMESSAGE, em);
//			return ViewName.ErrorView;
//		}
//
//		roleService.removeUser(id, ids);
//		return ViewName.SuccessView;
//	}
//
//	// 获取角色下相应的资源
//	@RequestMapping(value = { "/{roleid}/resource" }, method = RequestMethod.GET)
//	public String resList(ModelMap mm, @PathVariable("roleid") String id) {
//
//		Role role = this.roleService.getRolesDetail(id);
//		Set<Sysresource> res = this.roleService.getResByRole(role);
//		mm.put(ModelMapKey.RESULTKEY, res);
//		return ViewName.SuccessView;
//	}
//
//	// 删除角色下的资源
//	@LMSLog(desc = "删除角色下的功能权限", otype = LogType.Delete, order = 1, method = "")
//	@RequestMapping(value = { "/{roleid}/resource/{ids}" }, method = RequestMethod.DELETE)
//	public String removeRes(ModelMap mm, @PathVariable("roleid") String id,
//			@PathVariable("ids") String[] ids) {
//
//		if (ids.length == 0) {
//			ErrorMessage em = ErrorMessageHelper.getErrorMessage("请选择需要删除的数据!");
//			mm.put(ModelMapKey.ERRORMESSAGE, em);
//			return ViewName.ErrorView;
//		}
//
//		Role role = this.roleService.getRolesDetail(id);
//
//		List<Sysresource> resSet = roleService.getResByRole(role, ids);
//		// 级联删除
//		role.getSysresource().removeAll(resSet);
//		roleService.update(role);
//
//		return ViewName.SuccessView;
//
//	}
//
//	// 为角色添加资源的处理方法
//	@LMSLog(desc = "为角色添加功能权限", otype = LogType.Save, order = 1, method = "")
//	@RequestMapping(value = { "/{roleid}/resource" }, method = RequestMethod.POST)
//	public String relateRes(ModelMap mm, @PathVariable("roleid") String id,
//			@RequestBody List<String> sysRes) {
//
//		roleService.relateRes(id, sysRes);
//		return ViewName.SuccessView;
//	}

}
