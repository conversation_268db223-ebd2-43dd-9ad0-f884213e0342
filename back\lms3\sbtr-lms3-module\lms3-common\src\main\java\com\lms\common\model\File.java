package com.lms.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Mapping;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "file")
@Mapping(mappingPath = "")
public class File {

    @TableId(type= IdType.ASSIGN_ID)
    private String id;

    /**
     * 文件名称
     */
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String fileName;

    /**
     * 文件分类
     */
    @Field(type = FieldType.Keyword)
    private String fileCategory;

    @Field(name = "attachment.content", type = FieldType.Text, analyzer = "ik_max_word")
    private String content;

    /**
     * 文件存储路径
     */
    @Field(type = FieldType.Keyword, index = false)
    private String filePath;

    /**
     * 文件大小
     */
    @Field(type = FieldType.Keyword, index = false)
    private Long fileSize;

    /**
     * 文件类型
     */
    @Field(type = FieldType.Keyword, index = false)
    private String fileType;

    /**
     * 创建人
     */
    @Field(type = FieldType.Keyword, index = false)
    private String createBy;

    /**
     * 创建日期
     */
    @Field(type = FieldType.Keyword, index = false)
    private String createTime;

    /**
     * 更新人
     */
    @Field(type = FieldType.Keyword, index = false)
    private String updateBy;

    /**
     * 更新日期
     */
    @Field(type = FieldType.Keyword, index = false)
    private Date updateTime;

    /**
     * 数据级别
     */
    @Field(type = FieldType.Keyword, index = false)
    private Integer mlevel;

}
