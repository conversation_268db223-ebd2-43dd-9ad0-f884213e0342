<template>
  <div class="system-log-management">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-item">
          <label class="filter-label">日志类型</label>
          <a-select
            v-model:value="filters.logtype"
            placeholder="请选择"
            class="filter-select"
            :options="logTypeOptions"
            allow-clear
          />
        </div>

        <div class="filter-item">
          <label class="filter-label">对象名称</label>
          <a-input
            v-model:value="filters.objname"
            placeholder="请输入"
            class="filter-input"
            allow-clear
          />
        </div>

        <div class="filter-item">
          <label class="filter-label">时间范围</label>
          <a-date-picker
            v-model:value="filters.startDate"
            placeholder="2025-06-01"
            class="filter-date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </div>
        至
        <div class="filter-item">
          <a-date-picker
            v-model:value="filters.endDate"
            placeholder="2025-06-05"
            class="filter-date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </div>

        <div class="filter-actions">
          <a-button class="reset-btn" @click="handleReset">重置</a-button>
          <a-button
            type="primary"
            class="search-btn"
            :loading="loading"
            @click="handleSearch"
          >
            查询
          </a-button>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <div class="action-buttons">
        <a-button type="primary" class="action-btn" @click="handleExport">
          <template #icon>
            <Icon icon="ant-design:download-outlined" />
          </template>
          导出
        </a-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <div class="table-container">
        <a-table
          :columns="columns"
          :data-source="systemLogs"
          :loading="loading"
          :pagination="false"
          row-key="id"
          size="middle"
          class="system-log-table"
        >
          <!-- 序号列 -->
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'index'">
              {{ (pagination.current - 1) * pagination.size + index + 1 }}
            </template>

            <!-- 日志类型 -->
            <template v-else-if="column.key === 'logtype'">
              <a-tag :color="getlogtypeColor(record.logtype)">
                {{ record.logtype }}
              </a-tag>
            </template>

            <!-- 操作结果 -->
            <template v-else-if="column.key === 'result'">
              <a-tag
                :color="
                  record.result.indexOf('操作成功') > -1 ? 'green' : 'red'
                "
              >
                {{ record.result }}
              </a-tag>
            </template>

            <!-- 密级 -->
            <template v-else-if="column.key === 'level'">
              <a-tag :color="getLevelColor(record.level)">
                {{ record.level }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 分页栏 -->
    <div v-if="!loading && systemLogs.length > 0" class="pagination-section">
      <div class="pagination-container">
        <a-pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="
            total =>
              `当前第${pagination.current}/${Math.ceil(total / pagination.size)}页`
          "
          :page-size-options="['10', '20', '50', '100']"
          :locale="paginationLocale"
          @change="handlePageChange"
          @show-size-change="handlePageSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { message } from "ant-design-vue";
import { Icon } from "@iconify/vue";
import type { SystemLog } from "@/types/system";
import { logTypeOptions } from "@/data/system";
import { getLogListPage } from "@/api/system.ts";
import { paginationLocale } from "@/plugins/i18n";

const loading = ref(false);
const systemLogs = ref<SystemLog[]>([]);

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
  pages: 0
});

// 筛选条件
let filters = {};

// 表格列定义
const columns = [
  {
    title: "对象名称",
    dataIndex: "objname",
    key: "objname",
    width: 150,
    ellipsis: true
  },
  {
    title: "操作者",
    dataIndex: "submitor",
    key: "submitor",
    width: 100
  },
  {
    title: "操作时间",
    dataIndex: "submittime",
    key: "submittime",
    width: 120,
    align: "center"
  },
  {
    title: "日志类型",
    dataIndex: "logtype",
    key: "logtype",
    width: 100,
    align: "center"
  },
  {
    title: "IP地址",
    dataIndex: "submitip",
    key: "submitip",
    width: 120
  },
  {
    title: "操作描述",
    dataIndex: "description",
    key: "description",
    width: 150,
    ellipsis: true
  },
  {
    title: "操作结果",
    dataIndex: "result",
    key: "result",
    width: 100,
    align: "center"
  }
];

// 获取日志类型颜色
const getlogtypeColor = (logtype: string) => {
  switch (logtype) {
    case "登录":
      return "blue";
    case "新增":
      return "green";
    case "修改":
      return "orange";
    case "删除":
      return "red";
    case "查询":
      return "cyan";
    case "导出":
      return "purple";
    case "导入":
      return "geekblue";
    default:
      return "default";
  }
};

// 获取密级颜色
const getLevelColor = (level: string) => {
  switch (level) {
    case "公开":
      return "green";
    case "JM":
      return "orange";
    case "SM":
      return "red";
    default:
      return "default";
  }
};

// 获取系统日志列表
const fetchSystemLogList = async () => {
  try {
    loading.value = true;

    let params = {
      pageIndex: pagination.current,
      pageSize: pagination.size,
      PARAMETER_S_Like_objname: filters.objname,
      PARAMETER_S_Like_logtype: filters.logtype,
      orderName: "submittime",
      sortType: "asc"
    };
    if (filters.startDate) {
      params["PARAMETER_S_GE_submittime"] = filters.startDate;
    }
    if (filters.endDate) {
      params["PARAMETER_S_LE_submittime"] = filters.endDate;
    }
    const response = await getLogListPage(params);
    if (response.success) {
      systemLogs.value = response.result.records;
      pagination.total = response.result.total;
      pagination.pages = response.result.pages;
    } else {
      message.error(response.message || "获取日志信息列表失败");
    }
  } catch (error) {
    console.error("获取系统日志列表失败:", error);
    message.error("获取系统日志列表失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchSystemLogList();
};

// 重置筛选条件
const handleReset = () => {
  Object.assign(filters, {
    logtype: "",
    operatorName: "",
    startDate: null,
    endDate: null
  });
  pagination.current = 1;
  fetchSystemLogList();
};

// 导出日志
const handleExport = async () => {
  try {
    loading.value = true;

    // 模拟导出延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    message.success("系统日志导出成功");
  } catch (error) {
    console.error("导出系统日志失败:", error);
    message.error("导出系统日志失败");
  } finally {
    loading.value = false;
  }
};

// 分页变化
const handlePageChange = (page: number, pageSize: number) => {
  pagination.current = page;
  pagination.size = pageSize;
  fetchSystemLogList();
};

// 页面大小变化
const handlePageSizeChange = (current: number, size: number) => {
  pagination.current = 1;
  pagination.size = size;
  fetchSystemLogList();
};

// 页面加载时获取数据
onMounted(() => {
  fetchSystemLogList();
});
</script>

<style lang="scss" scoped>
.system-log-management {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--gray-50);
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.page-header {
  padding: var(--spacing-6);
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
}

.page-title {
  font-size: var(--text-xl);
  font-weight: var(--font-medium);
  color: var(--gray-900);
  margin: 0;
}

.filter-section {
  padding: var(--spacing-6);
  background-color: white;
  // border-bottom: 1px solid var(--gray-200);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.filter-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.filter-label {
  font-size: var(--text-sm);
  color: var(--gray-600);
  white-space: nowrap;
  min-width: 80px;
}

.filter-input {
  width: 160px;
}

.filter-select {
  width: 120px;
}

.filter-date {
  width: 120px;
}

.filter-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-left: auto;
}

.search-btn,
.reset-btn {
  padding: 0 var(--spacing-6);
}

.action-section {
  padding: var(--spacing-3) var(--spacing-6);
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.table-section {
  flex: 1;
  overflow: auto;
  background-color: white;
}

.table-container {
  padding: var(--spacing-6);
}

.pagination-section {
  padding: var(--spacing-1);
  background-color: white;
  border-top: 1px solid var(--gray-200);
}

.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 表格样式
:deep(.system-log-table .ant-table-thead > tr > th) {
  background-color: #eff7fd !important;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
  padding: 12px 16px;
}

:deep(.system-log-table .ant-table-tbody > tr > td) {
  font-size: var(--text-sm);
  color: var(--gray-600);
  border-bottom: 1px solid var(--gray-200);
  padding: 12px 16px;
}

:deep(.system-log-table .ant-table-tbody > tr:hover > td) {
  background-color: var(--gray-50);
}

:deep(.system-log-table .ant-table) {
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

:deep(.system-log-table .ant-table-container) {
  border: 0;
}

// 分页样式
:deep(.ant-pagination) {
  .ant-pagination-item {
    border-color: var(--gray-300);

    &:hover {
      border-color: var(--primary-color);
    }

    &.ant-pagination-item-active {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    border-color: var(--gray-300);

    &:hover {
      border-color: var(--primary-color);
    }
  }
}

// 标签样式
:deep(.ant-tag) {
  border-radius: var(--border-radius-sm);
  font-size: var(--text-xs);
  padding: 2px 8px;
  margin: 0;
}

// 按钮样式
:deep(.ant-btn) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn .anticon) {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 日期选择器样式
:deep(.ant-picker) {
  width: 100%;
}

:deep(.ant-pagination-item-active a) {
  color: #fff !important;
}
</style>
