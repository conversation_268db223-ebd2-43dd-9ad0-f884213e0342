package com.sbtr.workflow.exception;


/**
 * 业务异常，出现此异常说明业务出现异常，但不是致命异常，还可以继续进行运行
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-05-18 13:40:36
 */
public final class BizException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private Integer statusCode;

    /**
     * 错误提示
     */
    private String message;


    public BizException() {
    }

    public BizException(String message) {
        this.message = message;
    }

    public BizException(String message, Integer statusCode) {
        this.message = message;
        this.statusCode = statusCode;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public BizException setMessage(String message) {
        this.message = message;
        return this;
    }


}