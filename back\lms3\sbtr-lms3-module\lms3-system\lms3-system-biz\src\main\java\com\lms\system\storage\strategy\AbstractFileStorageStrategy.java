package com.lms.system.storage.strategy;

import com.lms.common.enums.StorageTypeEnum;
import com.lms.system.storage.service.FileStorageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件存储策略抽象基类
 * 提供通用的文件验证和工具方法
 */
@Slf4j
public abstract class AbstractFileStorageStrategy implements FileStorageService {

    // 文件大小限制：100MB
    protected static final long MAX_FILE_SIZE = 100 * 1024 * 1024;

    // 允许的文件类型
    protected static final String[] ALLOWED_EXTENSIONS = {
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
            ".txt", ".csv", ".zip", ".rar", ".7z", ".tar", ".gz",
            ".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv",
            ".mp3", ".wav", ".flac", ".aac",
            ".vrp", ".vrpc"
    };

    /**
     * 获取存储策略类型
     *
     * @return 存储策略类型枚举
     */
    protected abstract StorageTypeEnum getStorageTypeEnum();

    /**
     * 验证文件
     *
     * @param file 要验证的文件
     * @throws IllegalArgumentException 文件验证失败
     */
    protected void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException(
                    String.format("文件大小超过限制，最大允许 %d MB", MAX_FILE_SIZE / 1024 / 1024)
            );
        }
        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        String extension = getFileExtension(originalFilename);
        if (!isAllowedExtension(extension)) {
            throw new IllegalArgumentException("不支持的文件类型: " + extension);
        }
        log.debug("文件验证通过: {}, 大小: {} bytes", originalFilename, file.getSize());
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 文件扩展名（包含点号）
     */
    protected String getFileExtension(String filename) {
        if (StringUtils.isEmpty(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex).toLowerCase() : "";
    }

    /**
     * 检查文件扩展名是否被允许
     *
     * @param extension 文件扩展名
     * @return 是否被允许
     */
    protected boolean isAllowedExtension(String extension) {
        if (StringUtils.isEmpty(extension)) {
            return false;
        }
        for (String allowedExt : ALLOWED_EXTENSIONS) {
            if (allowedExt.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 生成唯一文件名
     * 格式：yyyy-MM-dd-HH-mm-ss-UUID.扩展名
     *
     * @param originalFilename 原始文件名
     * @return 唯一文件名
     */
    protected String generateUniqueFileName(String originalFilename) {
        if (StringUtils.isEmpty(originalFilename)) {
            throw new IllegalArgumentException("原始文件名不能为空");
        }
        String extension = getFileExtension(originalFilename);
        String timestamp = java.time.LocalDateTime.now()
                .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss"));
        String uuid = java.util.UUID.randomUUID().toString();
        return timestamp + "-" + uuid + extension;
    }

    /**
     * 安全的存储桶名称处理
     *
     * @param bucketName    存储桶名称
     * @param defaultBucket 默认存储桶名称
     * @return 处理后的存储桶名称
     */
    protected String safeBucketName(String bucketName, String defaultBucket) {
        return StringUtils.isEmpty(bucketName) ? defaultBucket : bucketName;
    }

    /**
     * 记录操作日志
     *
     * @param operation 操作类型
     * @param fileName  文件名
     * @param success   是否成功
     * @param message   附加消息
     */
    protected void logOperation(String operation, String fileName, boolean success, String message) {
        String status = success ? "成功" : "失败";
        String logMessage = String.format("[%s] %s %s: %s - %s",
                getStorageType(), operation, status, fileName, message);
        if (success) {
            log.info(logMessage);
        } else {
            log.error(logMessage);
        }
    }

    @Override
    public String getStorageType() {
        return getStorageTypeEnum().getCode();
    }

    @Override
    public String getStorageDescription() {
        return getStorageTypeEnum().getDescription();
    }
}
