package com.lms.examine.coursewareexamine.controller;

import com.alibaba.fastjson.JSONObject;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.StringHelper;
import com.lms.examine.coursewareexamine.service.CoursewareexamineService;
import com.lms.examine.examinestudent.service.ExaminestudentService;
import com.lms.examine.examinesubject.service.ExaminesubjectService;
import com.lms.examine.feign.model.Coursewareexamine;
import com.lms.examine.feign.model.Examinestudent;
import com.lms.examine.feign.model.Examinesubject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/coursewareexamine")
public class CoursewareexamineController extends BaseController<Coursewareexamine> {

    @Resource
    private ExaminesubjectService examinesubjectService;

    @Resource
    private ExaminestudentService examinestudentService;

    @Resource
    private CoursewareexamineService coursewareexamineService;

    @LMSLog(desc = "查询课件试题", otype = LogType.List, order = 1, method = "setCoursewareexamineLog")
    @RequestMapping(value = {"/listpage"}, method = RequestMethod.POST)
    public Result getCoursewareexamineList(@RequestBody JSONObject jsonObject) {
        PageInfo pageInfo = super.getPageInfo(jsonObject);
        return Result.OK(coursewareexamineService.listByCondition(pageInfo));
    }

    @LMSLog(desc = "添加课件试题", otype = LogType.Save, order = 1, method = "setCoursewareexamineLog")
    @RequestMapping(value = {"/add"}, method = RequestMethod.POST)
    public Result saveCoursewareexamine(@RequestBody Coursewareexamine coursewareexamine) {
        int existno = coursewareexamineService.existSeqno(coursewareexamine);
        if (existno > 0) {
            String tips = commonSystemApi.translateContent("操作失败，试题编号已存在，不可录入编号一致的试题！");
            return Result.error(tips);
        }
        int maxExamsecond = coursewareexamineService.getMaxExamSecondByCourseware(coursewareexamine);
        int minExamsecond = coursewareexamineService.getMinExamSecondByCourseware(coursewareexamine);
        if (coursewareexamine.getExamsecond() <= maxExamsecond) {
            String tips = commonSystemApi.translateContent("操作失败，开始答题播放时间必须大于低编号的试题的答题播放时间！");
            return Result.error(tips);
        } else if (minExamsecond <= coursewareexamine.getExamsecond()) {
            String tips = commonSystemApi.translateContent("操作失败，开始答题播放时间必须小于高编号的试题的答题播放时间！");
            return Result.error(tips);
        } else {
            coursewareexamine.setCreator(ContextUtil.getCurrentUser().getPersonid());
            coursewareexamineService.saveOrUpdate(coursewareexamine);
            return Result.OK();
        }
    }

    @LMSLog(desc = "编辑课件试题", otype = LogType.Update, order = 1, method = "setCoursewareexamineLog")
    @RequestMapping(value = {"/modify"}, method = RequestMethod.POST)
    public Result updateCoursewareexamine(@RequestBody Coursewareexamine coursewareexamine) {
        int existno = coursewareexamineService.existSeqno(coursewareexamine);
        if (existno > 0) {
            String tips = commonSystemApi.translateContent("操作失败，试题编号已存在，不可录入编号一致的试题！");
            return Result.error(tips);
        }
        int maxExamsecond = coursewareexamineService.getMaxExamSecondByCourseware(coursewareexamine);
        int minExamsecond = coursewareexamineService.getMinExamSecondByCourseware(coursewareexamine);
        if (coursewareexamine.getExamsecond() <= maxExamsecond) {
            String tips = commonSystemApi.translateContent("操作失败，开始答题播放时间必须大于低编号的试题的答题播放时间！");
            return Result.error(tips);
        } else if (minExamsecond <= coursewareexamine.getExamsecond()) {
            String tips = commonSystemApi.translateContent("操作失败，开始答题播放时间必须小于高编号的试题的答题播放时间！");
            return Result.error(tips);
        } else {
            coursewareexamineService.saveOrUpdate(coursewareexamine);
            return Result.OK();
        }
    }

    @LMSLog(desc = "删除课件试题", otype = LogType.Delete, order = 1, method = "setCoursewareexamineLog")
    @RequestMapping(value = {"/delete"}, method = RequestMethod.DELETE)
    public Result batchDeleteCoursewareexamine(@RequestParam("ids") String ids) {
        String[] idList = ids.split(",");
        for (String id : idList) {
            this.coursewareexamineService.removeById(id);
        }
        return Result.OK();
    }

    @RequestMapping(value = {"/getCoursewareTestRecord/{id}"}, method = RequestMethod.GET)
    public Result getCoursewareTestRecord(@PathVariable("id") String examineid) {

        List<Examinestudent> es = examinestudentService
                .getExaminestudentsByExamId(StringHelper
                        .null2String(examineid));
        String student = ContextUtil.getCurrentUser().getPersonid();
        List<Examinestudent> examinestudents = examinestudentService.getStudentByExamine(student, examineid);
        if (examinestudents.size() > 0) {//当前用户已试题信息
            Result.OK(examinestudents.get(0));
        } else {//创建用户试题信息
            Map<String, List> subjectMap = new HashMap();
            Examinestudent examinestudent = new Examinestudent();
            examinestudent.setExamineid(examineid);
            examinestudent.setPersonid(student);
            examinestudent.setStatus(1);
            examinestudent.setExaminestatus("3");
            List<Examinesubject> checkMsgList = examinesubjectService.getSubjectByExamineId(examineid);
            if (checkMsgList.size() > 0) {
                List<String> subjectids = new ArrayList<String>();
                for (Examinesubject est : checkMsgList) {
                    subjectids.add(est.getSubjectid());
                }
                subjectMap.put("subjectids", subjectids);
                examinestudentService.saveOrUpdate(examinestudent);
                examinestudentService.createStudentRecordByExamine(examinestudent,
                        examineid, subjectMap);
                return Result.OK();
            } else {
                String tips = commonSystemApi.translateContent("试卷未维护题目！");
                return Result.error(tips);
            }
        }
		return Result.OK();
    }

    public String setCoursewareexamineLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.List)) { //查询数据日志
            objname = "课件试题列表";
        } else if (lmslog.otype().equals(LogType.Save)) { //新增数据日志
            Coursewareexamine obj = (Coursewareexamine) (args[0]);
            objname = StringHelper.null2String(obj.getName());
        } else if (lmslog.otype().equals(LogType.Update)) { //编辑数据日志
            Coursewareexamine obj = (Coursewareexamine) (args[0]);
            objname = StringHelper.null2String(obj.getName());
        } else if (lmslog.otype().equals(LogType.Delete)) { //删除数据日志
            String ids = (String) (args[0]);
            String[] idarray = ids.split(",");
            for (String id : idarray) {
                Coursewareexamine obj = coursewareexamineService.getById(id);
                objname = objname.isEmpty() ? StringHelper.null2String(obj.getName()) : objname + "," + StringHelper.null2String(obj.getName());
            }
        }
        return objname;
    }

    @RequestMapping(value = {"/getByCourseware/{id}"}, method = RequestMethod.GET)
    public Result<List<Coursewareexamine>> getByCourseware(@PathVariable("id") String coursewareid) {
        return Result.OK(coursewareexamineService.getByCourseware(coursewareid));
    }

    @PostMapping("/save")
    public Result save(@RequestBody Coursewareexamine newexamine) {
        return Result.OK(coursewareexamineService.saveOrUpdate(newexamine));
    }
}
