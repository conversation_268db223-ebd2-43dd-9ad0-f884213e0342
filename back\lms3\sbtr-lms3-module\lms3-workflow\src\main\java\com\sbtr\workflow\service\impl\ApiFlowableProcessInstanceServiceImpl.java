package com.sbtr.workflow.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.lms.common.util.DateHelper;
import com.sbtr.workflow.mapper.ApiFlowableProcessDefinitionMapper;
import com.sbtr.workflow.model.*;
import com.alibaba.fastjson.JSON;
import com.sbtr.workflow.utils.BaseUtils;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.dto.CommentBean;
import com.sbtr.workflow.enums.FlowEnum;
import com.sbtr.workflow.enums.FlowConstant;
import com.sbtr.workflow.mapper.ApiFlowableProcessInstanceMapper;
import com.sbtr.workflow.service.*;
import com.sbtr.workflow.utils.AddHisCommentCmd;
import com.sbtr.workflow.utils.OkHttpClientUtil;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.PageUtil.PageUtils;
import com.sbtr.workflow.utils.Result;
import com.sbtr.workflow.utils.StringUtil.StringUtils;
import com.sbtr.workflow.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.flowable.bpmn.model.*;
import org.flowable.bpmn.model.Process;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.task.api.Task;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * <AUTHOR>
 */
@Service("apiFlowableProcessInstanceServiceImpl")
public class ApiFlowableProcessInstanceServiceImpl extends BaseServiceImpl implements ApiFlowableProcessInstanceService {

    private static final Logger logger = LoggerFactory.getLogger(ApiFlowableProcessInstanceServiceImpl.class);
    @Resource
    public BusinessSystemDataService businessSystemDataService;
    @Resource
    protected ManagementService managementService;

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private ApiFlowableBpmnModelService apiFlowableBpmnModelService;

    @Resource
    private TaskService taskService;

    @Resource
    protected HistoryService historyService;

    @Resource
    protected IdentityService identityService;

    @Resource
    protected ApiFlowableProcessInstanceMapper apiFlowableProcessInstanceMapper;

    @Resource
    protected ApiFlowableProcessDefinitionMapper apiFlowableProcessDefinitionMapper;

    @Resource
    private ActMyModelService flowModelService;

    @Resource
    private WorkflowService workflowService;

    @Resource
    private ActMyNodeButtonService flowNodeButtonService;

    @Resource
    private ActMyNodeFieldService flowNodeFieldService;

    @Resource
    public HttpServletRequest request;

    @Resource
    private ActFormConfigureService actFormConfigureService;

    @Resource
    private OkHttpClientUtil okHttpClientUtil;


    @Value("${spring.datasource.dynamic.datasource.master.driver-class-name:null}")
    public String driverClassName;

    @PostMapping(value = "/stopProcess")
    public Object stopProcess(String processInstanceId, String message) {
        boolean flag = isSuspended(processInstanceId);
        Object returnVo = null;
        if (flag) {
            returnVo = stopProcessInstanceById(null, processInstanceId, message);
        } else {
            return failure("流程已挂起,请联系管理员激活！！！");
        }
        return returnVo;
    }


    @Transactional
    @Override
    public Object startProcessInstanceByKey(String modelKey,
                                            String businessUuid,
                                            String businessTitle,
                                            String assignUser,
                                            String duplicateUser,
                                            String skipNode, Map<String, Object> params) {
        if (StrUtil.hasBlank(modelKey, businessUuid, businessTitle)) {
            return failure("请填写模型key,业务uuid,业务标题！！！");
        }
        try {
            String processDefinitionId = "";
            List<ProcessDefinitionVo> processDefinitionVos = apiFlowableProcessDefinitionMapper.getListByIdAndModelKey(modelKey, "是");
            if(processDefinitionVos!=null&&processDefinitionVos.size()>0){
                processDefinitionId = processDefinitionVos.get(0).getId();
            }else{
                return failure("此流程没有主版本，无法启动！！！");
            }
//            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(modelKey)
//                    .latestVersion().singleResult();
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(processDefinitionId).singleResult();
            if (processDefinition != null && processDefinition.isSuspended()) {
                return failure("此流程已经挂起,请联系系统管理员去激活该流程！！！");
            }
            Boolean beStarted = flowModelService.getAuthStarted(businessSystemDataService.getUserNameId(), modelKey, processDefinitionId);
            if (!beStarted) {
                return failure("对不起，您没有权限发起该流程请联系管理员！！！");
            }
            // 可以设置全局变量  通过taskService去拿
            params = params == null? new HashMap<String, Object>() : params;
            Map<String, Object> variables = new HashMap<>();
            if (!"true".equals(skipNode)) {
                variables = params;
            }
            variables.put(FlowConstant.FLOW_SUBMITTER_VAR, businessSystemDataService.getUserNameId());
            variables.put("departmentHeadId", businessSystemDataService.getleaderIdByUserNameId(businessSystemDataService.getUserNameId()));
			variables.put("reportsToId", businessSystemDataService.getReportsToByUserNameId(businessSystemDataService.getUserNameId()));

            //1.2、设置可以自动跳过
//            variables.put(FlowConstant.FLOWABLE_SKIP_EXPRESSION_ENABLED, true);
            //  1.3、汇报线的参数设置

            //3.启动流程
            identityService.setAuthenticatedUserId(businessSystemDataService.getUserNameId());
//            ProcessInstance processInstance = runtimeService.createProcessInstanceBuilder()
//                    .processDefinitionKey(modelKey.trim())
//                    .name(businessTitle)
//                    .businessKey(businessUuid)
//                    .variables(variables)
//                    .tenantId("sbtr")
//                    .start();
            ProcessInstance processInstance = runtimeService.createProcessInstanceBuilder()
                    .processDefinitionId(processDefinitionId)
                    .name(businessTitle)
                    .businessKey(businessUuid)
                    .variables(variables)
                    .tenantId(businessSystemDataService.getTenantId())
                    .start();
            //4.  添加审批记录
            managementService.executeCommand(new AddHisCommentCmd(null, businessSystemDataService.getUserNameId(), processInstance.getProcessInstanceId(),
                    Convert.toStr(FlowEnum.TJ), "启动流程"));

            //5. 默认处理第一步
            if ("true".equals(skipNode)) {
                Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
                params.remove("modelKey");
                params.remove("businessUuid");
                params.remove("businessTitle");
                params.remove("assignUser");
                params.remove("skipNode");
                params.put("outcome", "同意");
//                params.put("departmentHeadId", userClient.getleaderIdByUserNameId(businessSystemDataService.getUserNameId()));
//                params.put("reportsToId", userClient.getReportsToByUserNameId(businessSystemDataService.getUserNameId()));
//                String[] v = {"ewsd0001", "ewsd0004"};
//                params.put("assigneeList", Arrays.asList(v));
                taskService.addComment(task.getId(), task.getProcessInstanceId(), Convert.toStr(FlowEnum.SP), "申请");
                taskService.setAssignee(task.getId(), businessSystemDataService.getUserNameId());
                taskService.complete(task.getId(), params);
            }

            //6  设置下一步处理人
            if (StrUtil.isNotBlank(assignUser)) {
                Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
                if (ObjectUtil.isNotEmpty(task)) {
                    String[] assignUserStr = assignUser.split(",");
                    if (ArrayUtil.isNotEmpty(assignUserStr)) {
                        if (1 == assignUserStr.length) {
                            taskService.setAssignee(task.getId(), assignUserStr[0]);
                        }
                        if (assignUserStr.length > 1) {
                            //删除之前的候选人
                            List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
                            for (IdentityLink identityLink : identityLinks) {
                                if (StrUtil.isNotBlank(identityLink.getGroupId())) {
                                    taskService.deleteCandidateGroup(task.getId(), identityLink.getGroupId());
                                }
                            }
                            //添加新的候选人
                            for (int i = 0; i < assignUserStr.length; i++) {
                                taskService.addCandidateUser(task.getId(), assignUserStr[i]);
                            }
                        }
                    }
                }
            }

            //7  抄送
            if (StrUtil.isNotBlank(duplicateUser)) {
                Task task = taskService.createTaskQuery().processInstanceId(processInstance.getProcessInstanceId()).singleResult();
                if (ObjectUtil.isNotEmpty(task)) {
                    List<String> list = Arrays.asList(duplicateUser.split(","));
                    for (int i = 0; i < list.size(); i++) {
                        ActMyProcessCopy actMyProcessCopy = new ActMyProcessCopy();
                        actMyProcessCopy.setUserNameId(list.get(i));
                        actMyProcessCopy.setProcessDefinitionId(processInstance.getProcessInstanceId());
                        actMyProcessCopy.setProcessInstanceId(processInstance.getProcessInstanceId());
                        actMyProcessCopy.setFormName(processInstance.getName());
                        actMyProcessCopy.setTaskName(task.getName());
                        actMyProcessCopy.setTaskId(task.getId());
                        actMyProcessCopy.setNodeId(task.getTaskDefinitionKey());
                        actMyProcessCopy.setBusinessKey(businessUuid);
                        actMyProcessCopy.setModelKey(modelKey);
                        actMyProcessCopy.setStartUserId(businessSystemDataService.getUserNameId());
                        actMyProcessCopy.setReviewStatus("review_status.01");
                        actMyProcessCopyService.insertSelective(getSaveData(actMyProcessCopy));
                    }
                }
            }

            //7. 更新任务密级
            String mlevel = (String) params.get("mlevel");
            apiFlowableTaskService.updateMlevelByBusinessKey(businessUuid, StringUtils.isNullOrEmpty(mlevel) ? 20 : Integer.parseInt(mlevel));
            //8. 推送消息数据

            HashMap var3 = new HashMap();
            var3.put("statusCode", CommonConstants.SUCCESS_STATUS_CODE);
            var3.put("title", CommonConstants.OPERATE_TIPS);
            var3.put("message", "流程启动成功！！！");
            //流程实例id
            var3.put("processInstanceId", processInstance.getId());
            //流程定义id
            var3.put("processDefinitionId", processInstance.getProcessDefinitionId());
            //标题
            var3.put("formName", businessTitle);
            //业务uuid 唯一
            var3.put("businessUuid", businessUuid);
            Task task = taskService.createTaskQuery().processInstanceId(processInstance.getProcessInstanceId()).singleResult();
            var3.put("taskId", task.getId());
            // 9. 启动实例完成后 推送门户待办
            businessSystemDataService.pushAddTaskNew(task.getId());
            return var3;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            logger.error("流程启动失败!失败原因为===" + e.getMessage());
            return failure("流程启动失败！！！");
        }


    }


    public Object commonSave(String tableName, @RequestParam Map<String, Object> map) {
        map.remove("tableName");
        Object mapValue = "" ;
        String mapKey = "" ;
        String fields = "" ;
        fields += "creator,creator_id,creator_org_id,create_time" ;
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        String currentTime = df.format(new Date());

        String values = "" ;
        values += "'" + businessSystemDataService.getUserName() + "','" + businessSystemDataService.getUserNameId() + "','" + businessSystemDataService.getOrgId() + "',  '" + currentTime + "'" ;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            mapKey = entry.getKey();
            mapValue = entry.getValue();
            if (mapKey.equals("uuid") && "" == mapValue) {
                mapValue = BaseUtils.UUIDGenerator();
            }

            if (Objects.nonNull(mapValue)) {
                fields += "," + mapKey;
                values += ",'" + mapValue + "'" ;
            }
        }
        String sql = "INSERT INTO " + tableName + "(" + fields + ") VALUES (" + values + ")" ;
        int result = apiFlowableProcessInstanceMapper.executeInsertSql(sql);
        return result > 0 ? success("保存成功") : failure("保存失败");

    }


    @Override
    public boolean isSuspended(String processInstanceId) {
        boolean flag = true;
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (processInstance != null) {
            flag = !processInstance.isSuspended();
        }
        return flag;
    }

    @Transactional
    @Override
    public Object stopProcessInstanceById(String taskId, String processInstanceId, String message) {
        if (StrUtil.hasBlank(processInstanceId, taskId, message)) {
            return failure("必要参数不能为空！！！");
        }
        boolean flag = isSuspended(processInstanceId);
        if (!flag) {
            return failure("流程已挂起,请联系管理员激活！！！");
        }
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
         if (ObjectUtil.isNotEmpty(processInstance)) {
            //1、添加审批记录 查询当前的内容
            managementService.executeCommand(new AddHisCommentCmd(taskId, businessSystemDataService.getUserNameId(), processInstanceId,
                    Convert.toStr(FlowEnum.LCZZ), message));
            //直接跳转到最后一步
            List<EndEvent> endNodes = apiFlowableBpmnModelService.findEndFlowElement(processInstance.getProcessDefinitionId());
            if (CollUtil.isEmpty(endNodes)) {
                return failure("查询不到最后一步无法终止该流程！！！");
            }
            String endId = endNodes.get(0).getId();
            //执行终止
            List<Execution> executions = runtimeService.createExecutionQuery().parentId(processInstanceId).list();
            if (CollUtil.isEmpty(executions)) {
                return failure("查询不到最后一步无法终止该流程！！！");
            }
            List<String> executionIds = new ArrayList<>();
            executions.forEach(execution -> executionIds.add(execution.getId()));
            runtimeService.createChangeActivityStateBuilder()
                    .moveExecutionsToSingleActivityId(executionIds, endId)
                    .changeState();
            return success("终止成功！！！");
        } else {
            return failure("不存在运行的流程实例,请确认！！！");
        }
    }

    @Override
    public String getProcessInstanceNameByProcessInstanceId(String processInstanceId){
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (processInstance != null) {
            return processInstance.getName();
        }
        return "";
    }

    @Override
    public Object stopProcessInstanceByProcessInstanceId(String processInstanceId, String message) {
        if (StrUtil.hasBlank(processInstanceId, message)) {
            return failure("必要参数不能为空！！！");
        }
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (processInstance != null) {
            //1、添加审批记录
            managementService.executeCommand(new AddHisCommentCmd(null, businessSystemDataService.getUserNameId(), processInstanceId,
                    Convert.toStr(FlowEnum.LCZZ), message));
            List<EndEvent> endNodes = apiFlowableBpmnModelService.findEndFlowElement(processInstance.getProcessDefinitionId());
            String endId = endNodes.get(0).getId();
            //2、执行终止
            List<Execution> executions = runtimeService.createExecutionQuery().parentId(processInstanceId).list();
            List<String> executionIds = new ArrayList<>();
            executions.forEach(execution -> executionIds.add(execution.getId()));
            runtimeService.createChangeActivityStateBuilder()
                    .moveExecutionsToSingleActivityId(executionIds, endId)
                    .changeState();
            return success("终止成功！！！");
        } else {
            return failure("不存在运行的流程实例,请确认！！！");
        }
    }

    @Transactional
    @Override
    public Object deleteProcessInstanceById(String processInstanceId) {
        if (StringUtils.isBlank(processInstanceId)) {
            return failure("流程实例id不能为空，请检查!!!");
        }
        try {
            //根据流程实例id 去ACT_RU_EXECUTION与ACT_RE_PROCDEF管理查询流程实例数据
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
            if (ObjectUtil.isNotEmpty(processInstance)) {
                runtimeService.deleteProcessInstance(processInstanceId, "流程实例删除");
            } else {
                historyService.deleteHistoricProcessInstance(processInstanceId);
            }
            return success(CommonConstants.DELETE_SUCCESS_MSG);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure(CommonConstants.DELETE_FAILURE_MSG + ":失败原因为" + e.getMessage());
        }

    }

    @Override
    public Object revokeProcess(String processInstanceId, String message) {
        return null;
        //if (StringUtils.isNotBlank(processInstanceId)) {
        //    ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
        //            .processInstanceId(processInstanceId).singleResult();
        //    if (processInstance != null) {
        //        //1.添加撤回意见
        //        managementService.executeCommand(new AddHisCommentCmd(null, businessSystemDataService.getUserNameId(), processInstanceId,
        //                CommentTypeEnum.CH.toString(), message));
        //        //2.设置提交人
        //        runtimeService.setVariable(processInstanceId, FlowConstant.FLOW_SUBMITTER_VAR, processInstance.getStartUserId());
        //        //3.执行撤回
        //        Activity disActivity = flowableBpmnModelService.findActivityByName(processInstance.getProcessDefinitionId(), FlowConstant.FLOW_SUBMITTER);
        //        //4.删除运行和历史的节点信息
        //        this.deleteActivity(disActivity.getId(), revokeVo.getProcessInstanceId());
        //        //5.执行跳转
        //        List<Execution> executions = runtimeService.createExecutionQuery().parentId(revokeVo.getProcessInstanceId()).list();
        //        List<String> executionIds = new ArrayList<>();
        //        executions.forEach(execution -> executionIds.add(execution.getId()));
        //        this.moveExecutionsToSingleActivityId(executionIds, disActivity.getId());
        //        return JsonUtils.messageJson(200, "操作提示", "撤回成功");
        //    }
        //} else {
        //    return JsonUtils.messageJson(300, "操作提示", "流程实例id不能为空,请确认");
        //}
        //return JsonUtils.messageJson(200, "操作提示", "撤回成功");
    }

    @Override
    public PageSet<ProcessInstanceVo> getPageSet(PageParam pageParam, String name) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<ProcessInstanceVo> list= apiFlowableProcessInstanceMapper.getPageSetMySql(name);
        PageInfo<ProcessInstanceVo> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public List<ProcessInstanceVo> getProcessInstance(String modelKey, String businessKey) {
        return apiFlowableProcessInstanceMapper.getProcessInstanceMySql(modelKey, businessKey);
    }

    @Override
    public List<ProcessInstanceVo> getProcessInstanceByBusinessKeys(String modelKey, List<String> businessKeys) {
        return apiFlowableProcessInstanceMapper.getProcessInstanceMySqlByBusinessKeys(modelKey, businessKeys);
    }


    @Transactional
    @Override
    public Object suspendOrActivateProcessInstanceById(String processInstanceId, Integer suspensionState) {
        if (StrUtil.hasBlank(processInstanceId, Convert.toStr(suspensionState))) {
            return failure("必要参数不能为空！！！");
        }
        try {
            if (suspensionState == 1) {
                runtimeService.suspendProcessInstanceById(processInstanceId);
                return success("挂起成功！！！");
            } else {
                runtimeService.activateProcessInstanceById(processInstanceId);
                return success("激活成功！！！");
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure("失败原因为==" + e.getMessage());
        }


    }

    @Resource
    private ActMyFormService actMyFormService;

    @Override
    public Object clickStartProcess(String modelKey, String taskId, String processInstanceId, String nodeId, String processDefinitionId) throws Exception {
        if (StrUtil.hasBlank(modelKey, taskId, processInstanceId, nodeId)) {
            return failure("modelKey/taskId/processInstanceId/nodeId不能为空,请检查");
        }
        //查询任务是否存在
        TaskEntity taskEntity = (TaskEntity) taskService.createTaskQuery().taskId(taskId).singleResult();
        if (ObjectUtil.isNull(taskEntity)) {
            return failure("没有此任务,请确认或刷新页面！！！");
        }
        Example example = new Example(ActMyModel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modelKey);
        if (StrUtil.isNotBlank(processDefinitionId)) {
            criteria.andEqualTo("procdefId", processDefinitionId);
        }
        List<ActMyModel> lists = flowModelService.selectByExample(example);
        if (CollectionUtils.isEmpty(lists)) {
            return failure("流程与表单关联表查找不到数据,请检查！！！");
        }
        HashMap<String, Object> map = new HashMap<>();
        if (StrUtil.isNotBlank(taskId)) {
            List<CommentBean> commentBeanList = workflowService.getCommentBeanByTaskId(taskId);
            map.put("commentBeanList", commentBeanList);
        }
        //根据实例id、模型key、procdefId
        ActMyForm actMyForm = actMyFormService.selectByProcessInstanceId(processInstanceId, modelKey, processDefinitionId);
        if (null != actMyForm) {
            lists.get(0).setFormDesign(actMyForm.getFormDesign());
        }
        map.put("lists", lists.get(0));
        // 查询历史节点表 ACT_HI_ACTINST
        List<HistoricActivityInstance> historicActivityInstanceList = getHistoricActivityInstAsc(processInstanceId);
        //1 正在执行的节点
        List<String> runningActivitiIdList = new ArrayList<String>();
        //2 获取已流经的流程线
        List<String> highLightedFlowIds = new ArrayList<>();
        //3.已执行历史节点
        List<String> executedActivityIdList = new ArrayList<String>();
        historicActivityInstanceList.forEach(historicActivityInstance -> {
            //1
            if (null == historicActivityInstance.getEndTime()) {
                runningActivitiIdList.add(historicActivityInstance.getActivityId());
            }
            //2
            if (historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                highLightedFlowIds.add(historicActivityInstance.getActivityId());
            }
            //3
            if (null != historicActivityInstance.getEndTime() && !historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                executedActivityIdList.add(historicActivityInstance.getActivityId());
            }

        });

        map.put("inProgress", runningActivitiIdList);
        highLightedFlowIds.addAll(executedActivityIdList);
        map.put("notInProgress", highLightedFlowIds);

        //4查询当前节点的按钮
        Example examples = new Example(ActMyNodeButton.class);
        Example.Criteria criterias = examples.createCriteria();
        criterias.andEqualTo("actDeModelKey", modelKey);
        criterias.andEqualTo("nodeId", nodeId);
        if (StrUtil.isNotBlank(processDefinitionId)) {
            criterias.andEqualTo("procdefId", processDefinitionId);
        }

        List<ActMyNodeButton> flowNodeButtons = flowNodeButtonService.selectByExample(examples);
        //  处理节点表单
        for (int i = 0; i < flowNodeButtons.size(); i++) {
            ActFormConfigure actFormConfigure = actFormConfigureService.selectByPrimaryKey(StringUtils.emptyToNull(flowNodeButtons.get(0).getFormUuid()));
            if (ObjectUtil.isNotEmpty(actFormConfigure)) {
                flowNodeButtons.get(i).setNodeFormPath(actFormConfigure.getNodeFormPath());
                flowNodeButtons.get(i).setAppPagePath(actFormConfigure.getAppPagePath());
                flowNodeButtons.get(i).setNodeFormEditPath(actFormConfigure.getNodeFormEditPath());
                flowNodeButtons.get(i).setNodeFormSavePath(actFormConfigure.getNodeFormSavePath());
                flowNodeButtons.get(i).setNodeFormUpdatePath(actFormConfigure.getNodeFormUpdatePath());
            }
        }
        map.put("flowNodeButtons", flowNodeButtons);
        //  以下为特殊处理 自己写的表单页面挂载流程需要返回业务详情数据
        //2  通过任务对象获取流程实例
        ProcessInstance pi = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        //3 通过流程实例获取“业务键”
        String businessKey = pi.getBusinessKey();
        //4 表单获取详情数据的请求地址
        if(flowNodeButtons.size()>0){
            ActMyNodeButton actMyNodeButton = flowNodeButtons.get(0);
            String tablename = actMyNodeButton.getTablename();
            String primarykey = actMyNodeButton.getPrimarykey();
            JSONObject json_test = businessSystemDataService.getBusinessData(tablename,primarykey,businessKey, modelKey);
            map.put("businessData", json_test);
        }
        //5 查询当前节点的字段属性
        if (StringUtils.isBlank(processDefinitionId)) {
            processDefinitionId = "" ;
        }
        List<FlowNodeFieldVo> flowNodeField = flowNodeFieldService.selectByModelKeyAndId(modelKey, nodeId, processDefinitionId);
        map.put("flowNodeField", flowNodeField);
        if (CollUtil.isNotEmpty(flowNodeField)) {
            lists.get(0).setModelType(flowNodeField.get(0).getModelType());
            lists.get(0).setFormUuid(flowNodeField.get(0).getFormUuid());
            //布局不为空则取布局json
            if (StrUtil.isNotBlank(flowNodeField.get(0).getFormLayout())) {
                String json = actMyFormService.getFormLayoutFormJSON(flowNodeField.get(0).getFormLayout());
                lists.get(0).setFormDesign(getLayoutFormJSON(json, lists.get(0).getFormDesign()));
            }
        }
        List<String> currentUsers = apiFlowableTaskService.getCurrentAssigneeByTask(taskId);
        map.put("taskId",taskId);
        map.put("modelKey",modelKey);
        map.put("processDefinitionId",processDefinitionId);
        map.put("processInstanceId",processInstanceId);
        map.put("businessKey",businessKey);
        map.put("currentAssignee",currentUsers);
        return map;
    }

//    @Override
//    public JSONObject getBusinessData(String tablename,String primarykey,String keyValue) {
//        HashMap<String, Object> businessDataMap = flowNodeButtonService.selectByBusinessKey("select * from "+tablename+" where "+primarykey+"='"+keyValue+"'");
//        Map newDataMap = new HashMap<String, Object>();
//        // 分支条件按小写设置
//        businessDataMap.forEach((String key,Object object) -> {
//            String newKey = key.toLowerCase();
//            newDataMap.put(newKey,object);
//        });
//        JSONObject jsonObject = JSONObject.parseObject(new Gson().toJson(newDataMap));
//        return jsonObject;
//    }

    @Override
    public List<ProcessInstanceVo> getRunProcessInstByParam(String businessKey) {
        return apiFlowableProcessInstanceMapper.getRunProcessInstByParam(businessKey);
    }

    /**
     * 把表单数据解析到布局json上
     *
     * @param jsons   json
     * @param formDes des形式
     * @return {@link String}
     */
    public String getLayoutFormJSON(String jsons, String formDes) {
        try {
            //更新表单布局
            List<FormDesignVo.ListDTO> list = JSON.parseObject(formDes, FormDesignVo.class).getList();
            //解析布局信息的设计json
            FormDesignVo formDesignVo = JSON.parseObject(jsons, FormDesignVo.class);
            for (int k = 0; k < formDesignVo.getList().size(); k++) {
                for (int i = 0; i < list.size(); i++) {
                    if (formDesignVo.getList().get(k).getModel().equals(list.get(i).getModel())) {
                        String defaultValue = list.get(i).getOptions().getDefaultValue();
                        formDesignVo.getList().get(i).getOptions().setDefaultValue(defaultValue);
                    }
                }
            }
            String json = JSON.toJSONString(formDesignVo);
            return json;
        } catch (Exception e) {
            logger.error("把表单数据解析到布局json上出现错误{}", e.getMessage());
            return null;
        }
    }

    /**
     * 通过流程实例ID获取流程中已经执行的节点，按照执行先后顺序排序
     *
     * @param procInstId
     * @return
     */
    public List<HistoricActivityInstance> getHistoricActivityInstAsc(String procInstId) {
        return historyService.createHistoricActivityInstanceQuery().processInstanceId(procInstId)
                .orderByHistoricActivityInstanceStartTime().asc().list();
    }

    @Resource
    private ApiFlowableTaskService apiFlowableTaskService;

    @Override
    public Object getListCommentsByBusinessKey(String businessKey) {
        if (StringUtils.isBlank(businessKey)) {
            return failure("业务ID不能为空，请检查!!!");
        }
        /**1:使用历史的流程实例查询，返回历史的流程实例对象，获取流程实例ID*/
        HistoricProcessInstance hpi = historyService.createHistoricProcessInstanceQuery()//对应历史的流程实例表
                .processInstanceBusinessKey(businessKey)//使用BusinessKey字段查询
                .singleResult();
        if (null == hpi) {
            return failure("根据业务ID在历史流程实例表中查询不到数据，请先提交流程或联系管理员!!!");
        }
        //流程实例ID
        String processInstanceId = hpi.getId();
        List<HistoricActivityInstance> haiList = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(processInstanceId)
                .activityType("userTask")
                .orderByHistoricActivityInstanceEndTime()
                .asc()
                .list();
        List<CommentBean> commentBeans = new ArrayList<>();
        for (HistoricActivityInstance hai : haiList) {
            List<CommentVo> commList = apiFlowableTaskService.getTaskCommentsByTaskId(hai.getTaskId());
            for (int i = 0; i < commList.size(); i++) {
                CommentBean commentBean = new CommentBean();
                commentBean.setActivityName(hai.getActivityName());
                commList.get(i).setMessage(commList.get(i).getMessage());
                commList.get(i).setTypeName(FlowEnum.getEnumMsgByType(commList.get(i).getType()));
                SysUser user = businessSystemDataService.getSysUserByUserNameId(commList.get(i).getUserId());
                commentBean.setUserName(null == user ? null : user.getUserName());
                commentBean.setComment(commList.get(i));
                commentBeans.add(commentBean);
            }

        }
        if (CollectionUtils.isEmpty(commentBeans)) {
            return failure("根据业务ID查找不到审批意见或该流程正在处理中!!!");
        }
        HashMap var3 = new HashMap();
        var3.put("statusCode", CommonConstants.SUCCESS_STATUS_CODE);
        var3.put("title", CommonConstants.OPERATE_SUCCESS_MSG);
        var3.put("message", "获取数据成功");
        var3.put("commentBeans", commentBeans);
        return var3;
    }

    @Override
    public Object processFreeJump(String processInstanceId, String nodeId, String toNodeId) {
        if (StrUtil.hasBlank(processInstanceId, nodeId, toNodeId)) {
            return failure("请检查processInstanceId/nodeId/toNodeId不能为空");
        }
        try {
            runtimeService.createChangeActivityStateBuilder()
                    .processInstanceId(processInstanceId)
                    .moveActivityIdTo(nodeId, toNodeId)
                    .changeState();
            return success("跳转成功");
        } catch (Exception e) {
            logger.error("跳转失败" + e.getMessage());
            return failure("跳转失败!!!");
        }
    }


    @Override
    public Object processFreeJumpForApi(String processInstanceId, String nodeId, String toNodeId, String message) {
        if (StrUtil.hasBlank(processInstanceId, nodeId, toNodeId)) {
            return failure("请检查processInstanceId/nodeId/toNodeId不能为空");
        }
        Task before = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
        runtimeService.createChangeActivityStateBuilder()
                .processInstanceId(processInstanceId)
                .moveActivityIdTo(nodeId, toNodeId)
                .changeState();
        Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
        // 生成审批意见
        managementService.executeCommand(new AddHisCommentCmd(task.getId(), businessSystemDataService.getUserNameId(),
                processInstanceId, Convert.toStr(FlowEnum.TZ), StringUtils.isEmpty(message) ? "同意" : message));
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("taskId", task.getId());
        // 流程流转到下一步 推送门户待办
        businessSystemDataService.pushCompleteTask("", before.getId());
        businessSystemDataService.pushAddTaskNew(task.getId());
        return hashMap;
    }

    @Override
    public List<TaskVo> getProcessFreeJumpData(String processInstanceId) {
        List<TaskVo> task = apiFlowableProcessInstanceMapper.getProcessFreeJumpData(processInstanceId);
        return task;
    }

    @Override
    public Object getAllUserTaskListByProcessInstanceId(String processInstanceId) {
        String processDefinitionId = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult().getProcessDefinitionId();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        org.flowable.bpmn.model.Process process = bpmnModel.getProcesses().get(0);
        //获取所有节点
        Collection flowElements = process.getFlowElements();
        List UserTaskList = process.findFlowElementsOfType(UserTask.class);
        return UserTaskList;
    }


    @Resource
    private ActMyProcessCopyService actMyProcessCopyService;

    @Transactional
    @Override
    public Object customizeFormStartProcessInstance(String modelKey,
                                                    String businessTitle,
                                                    String skipNode,
                                                    @RequestParam Map<String, Object> map) {
        if (StrUtil.hasBlank(modelKey, businessTitle)) {
            return failure("必要参数不能为空！！！");
        }
        try {
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(modelKey)
                    .latestVersion().singleResult();
            //判断流程是否发布
            if (ObjectUtil.isEmpty(processDefinition)) {
                return failure("此流程还未发布，请先去发布！！！");
            }
            if (processDefinition != null && processDefinition.isSuspended()) {
                return failure("此流程已经挂起,请联系系统管理员去激活该流程！！！");
            }
            Map<String, Object> variables = new HashMap<>();
            String uuid = BaseUtils.UUIDGenerator();
            //设置第一步处理人
            variables.put(FlowConstant.FLOW_SUBMITTER_VAR, businessSystemDataService.getUserNameId());
            //启动流程
            String authenticatedUserId = businessSystemDataService.getUserNameId();
            identityService.setAuthenticatedUserId(authenticatedUserId);
            ProcessInstance processInstance = runtimeService.createProcessInstanceBuilder()
                    .processDefinitionKey(modelKey.trim())
                    .name(businessTitle)
                    .businessKey(uuid)
                    .variables(variables)
                    .tenantId(businessSystemDataService.getTenantId())
                    .start();

            //4.添加审批记录
            managementService.executeCommand(new AddHisCommentCmd(null, businessSystemDataService.getUserNameId(), processInstance.getProcessInstanceId(),
                    Convert.toStr(FlowEnum.TJ), "启动流程"));

            //保存业务数据
            //链式构建请求，带cookie请求
            String gateway = request.getHeader("Gateway");
            String cookies = request.getHeader(CommonConstants.X_ACCESS_TOKEN);
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
            Collection<FlowElement> flowElements = bpmnModel.getMainProcess().getFlowElements();
            String nodeId = flowElements.stream().filter(flowElement -> flowElement instanceof UserTask).findFirst().get().getId();
            Example examples = new Example(ActMyNodeButton.class);
            Example.Criteria criterias = examples.createCriteria();
            criterias.andEqualTo("actDeModelKey", modelKey);
            criterias.andEqualTo("nodeId", nodeId);
            if (StrUtil.isNotBlank(processInstance.getProcessDefinitionId())) {
                criterias.andEqualTo("procdefId", processInstance.getProcessDefinitionId());
            }
            List<ActMyNodeButton> flowNodeButtons = flowNodeButtonService.selectByExample(examples);
            //  处理节点表单
            for (int i = 0; i < flowNodeButtons.size(); i++) {
                ActFormConfigure actFormConfigure = actFormConfigureService.selectByPrimaryKey(StringUtils.emptyToNull(flowNodeButtons.get(0).getFormUuid()));
                if (ObjectUtil.isNotEmpty(actFormConfigure)) {
                    flowNodeButtons.get(i).setNodeFormPath(actFormConfigure.getNodeFormPath());
                    flowNodeButtons.get(i).setAppPagePath(actFormConfigure.getAppPagePath());
                    flowNodeButtons.get(i).setNodeFormEditPath(actFormConfigure.getNodeFormEditPath());
                    flowNodeButtons.get(i).setNodeFormSavePath(actFormConfigure.getNodeFormSavePath());
                    flowNodeButtons.get(i).setNodeFormUpdatePath(actFormConfigure.getNodeFormUpdatePath());
                }
            }
            map.remove("modelKey");
            map.remove("businessTitle");
            map.remove("userNameId");
            map.remove("skipNode");
            map.remove("assignUser");
            map.remove("duplicateUser");
            map.put("uuid", uuid);
            try {
                HttpRequest.post(gateway + flowNodeButtons.get(0).getNodeFormSavePath())
                        .cookie("token=" + cookies)
                        .form(map)
                        .timeout(20000)
                        .execute().body();
            } catch (Exception e) {
                logger.error("保存业务数据失败：" + e.getMessage());
            }
            // 默认处理第一步
            if (StrUtil.isNotBlank(skipNode) && "true".equals(skipNode)) {
                Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
                map.put("outcome", "同意");
                map.put("departmentHeadId", businessSystemDataService.getleaderIdByUserNameId(authenticatedUserId));
                map.put("reportsToId", businessSystemDataService.getReportsToByUserNameId(authenticatedUserId));
//                String[] v = {"ewsd0001", "ewsd0002", "ewsd0003", "ewsd0004"};
//                map.put("assigneeList", Arrays.asList(v));
                taskService.addComment(task.getId(), task.getProcessInstanceId(), Convert.toStr(FlowEnum.SP), "同意");
                taskService.complete(task.getId(), map);
            }

            //6.下一步处理人
            if (!map.isEmpty() && null != map.get("assignUser") && StrUtil.isNotBlank(Convert.toStr(map.get("assignUser")))) {
                Task tasks = taskService.createTaskQuery().processInstanceId(processInstance.getProcessInstanceId()).singleResult();
                if (ObjectUtil.isNotNull(tasks)) {
                    String assignUserStr[] = Convert.toStr(map.get("assignUser")).split(",");
                    if (ArrayUtil.isNotEmpty(assignUserStr)) {
                        if (1 == assignUserStr.length) {
                            taskService.setAssignee(tasks.getId(), assignUserStr[0]);
                        }
                        if (assignUserStr.length > 1) {
                            for (int i = 0; i < assignUserStr.length; i++) {
                                taskService.addCandidateUser(tasks.getId(), assignUserStr[i]);
                            }
                        }
                    }
                }
            }
            //7.抄送
            if (!map.isEmpty() && null != map.get("duplicateUser") && StrUtil.isNotBlank(Convert.toStr(map.get("duplicateUser")))) {
                Task tasks = taskService.createTaskQuery().processInstanceId(processInstance.getProcessInstanceId()).singleResult();

                List<String> list = Arrays.asList(Convert.toStr(map.get("duplicateUser")).split(","));
                for (int i = 0; i <list.size() ; i++) {
                    ActMyProcessCopy actMyProcessCopy = new ActMyProcessCopy();
                    actMyProcessCopy.setUserNameId(list.get(i));
                    actMyProcessCopy.setProcessDefinitionId(processInstance.getProcessInstanceId());
                    actMyProcessCopy.setProcessInstanceId(processInstance.getProcessInstanceId());
                    actMyProcessCopy.setFormName(processInstance.getName());
                    actMyProcessCopy.setTaskName(tasks.getName());
                    actMyProcessCopy.setTaskId(tasks.getId());
                    actMyProcessCopy.setNodeId(tasks.getTaskDefinitionKey());
                    actMyProcessCopy.setBusinessKey(uuid);
                    actMyProcessCopy.setModelKey(modelKey);
                    actMyProcessCopy.setStartUserId(businessSystemDataService.getUserNameId());
                    actMyProcessCopy.setReviewStatus("review_status.01");
                    actMyProcessCopyService.insertSelective(getSaveData(actMyProcessCopy));
                }


            }

            //6. 推送消息数据
            HashMap var3 = new HashMap();
            var3.put("statusCode", CommonConstants.SUCCESS_STATUS_CODE);
            var3.put("title", CommonConstants.OPERATE_TIPS);
            var3.put("message", "流程启动成功！！！");
            var3.put("processInstanceId", processInstance.getId());
            var3.put("processDefinitionId", processInstance.getProcessDefinitionId());
            var3.put("formName", businessTitle);
            return var3;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            logger.error("流程启动失败!失败原因为" + e.getMessage());
            return failure("流程启动失败！！！");
        }
    }

    @Override
    public Result deleteProcessInstance(String processInstanceId, String reason) {
        if (StrUtil.hasBlank(processInstanceId, reason)) {
            return Result.ofFailMsg("流程实例id、作废原因不能为空，请检查!!!");
        }
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (ObjectUtil.isNotEmpty(processInstance)) {
            runtimeService.deleteProcessInstance(processInstanceId, reason);
        } else {
            historyService.deleteHistoricProcessInstance(processInstanceId);
        }
        return Result.ofSuccessMsg("流程作废成功！！！");
    }


    /**
     * @param taskId 任务节点id
     * @return
     * @return boolean (这里用一句话描述返回结果说明)
     * @Title: isMultiInstance
     * @Description: (根据任务节点id判断该节点是否为会签节点)
     */

    public boolean isMultiInstance(String taskId) {
        boolean flag = false;
        Task task = taskService.createTaskQuery() // 创建任务查询
                .taskId(taskId) // 根据任务id查询
                .singleResult();
        if (task != null) {
            // 获取流程定义id
            String processDefinitionId = task.getProcessDefinitionId();
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(processDefinitionId).singleResult();
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
            List<Process> processList = bpmnModel.getProcesses();
            Process process = processList.get(0);
            List list = (List) process.getFlowElements();
            for (Object o : list) {
                if (o instanceof UserTask) {
                    UserTask userTask = (UserTask) o;
                    //判断是不是会签节点
                    MultiInstanceLoopCharacteristics multiInstanceLoopCharacteristics = userTask.getLoopCharacteristics();
                    if (multiInstanceLoopCharacteristics != null) {
                        return true;
                    }

                }
            }
        }

        return flag;
    }

}
