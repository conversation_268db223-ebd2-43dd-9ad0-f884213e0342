package com.lms.base.operationrecords.controller;

import com.lms.base.operationrecords.model.OperationRecord;
import com.lms.base.operationrecords.service.OperationRecordService;
import com.lms.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 操作记录表Controller
 */
@RestController
@RequestMapping("/operationrecords")
@Api(value = "操作记录管理", tags = "操作记录管理")
public class OperationRecordController {

    private final OperationRecordService operationRecordService;

    public OperationRecordController(OperationRecordService operationRecordService) {
        this.operationRecordService = operationRecordService;
    }

    /**
     * 新增操作记录
     *
     * @param record 操作记录实体
     * @return 操作结果
     */
    @ApiOperation(value = "新增操作记录", httpMethod = "POST")
    @PostMapping("/add")
    public Result<Void> add(@RequestBody @ApiParam("操作记录实体") OperationRecord record) {
        operationRecordService.save(record);
        return Result.OK(null);
    }

    /**
     * 批量新增操作记录
     *
     * @param records 操作记录实体列表
     * @return 操作结果
     */
    @ApiOperation(value = "批量新增操作记录", httpMethod = "POST")
    @PostMapping("/batchAdd")
    public Result<Void> batchAdd(@RequestBody @ApiParam("操作记录实体列表") List<OperationRecord> records) {
        operationRecordService.saveBatch(records);
        return Result.OK(null);
    }

    /**
     * 根据ID删除操作记录
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @ApiOperation(value = "根据ID删除操作记录", httpMethod = "DELETE")
    @DeleteMapping("/delete/{id}")
    public Result<Void> deleteById(@PathVariable @ApiParam("主键ID") String id) {
        operationRecordService.deleteById(id);
        return Result.OK(null);
    }

    /**
     * 批量删除操作记录
     *
     * @param ids 主键ID列表
     * @return 操作结果
     */
    @ApiOperation(value = "批量删除操作记录", httpMethod = "DELETE")
    @DeleteMapping("/batchDelete")
    public Result<Void> batchDelete(@RequestBody @ApiParam("主键ID列表") List<String> ids) {
        operationRecordService.deleteBatch(ids);
        return Result.OK(null);
    }

    /**
     * 根据ID查询操作记录
     *
     * @param id 主键ID
     * @return 操作记录实体
     */
    @ApiOperation(value = "根据ID查询操作记录", httpMethod = "GET")
    @GetMapping("/getById/{id}")
    public Result<OperationRecord> getById(@PathVariable @ApiParam("主键ID") String id) {
        return Result.OK(operationRecordService.findById(id));
    }

    /**
     * 分页条件查询操作记录
     *
     * @param userId   用户ID，可为空
     * @param courseId 课程ID，可为空
     * @param sceneId  课件ID，可为空
     * @param page     页码，从1开始
     * @param size     每页条数
     * @return 操作记录列表
     */
    @ApiOperation(value = "分页条件查询操作记录", httpMethod = "GET")
    @GetMapping("/listPage")
    public Result<List<OperationRecord>> listPage(
            @ApiParam("用户ID") @RequestParam(required = false) String userId,
            @ApiParam("课程ID") @RequestParam(required = false) String courseId,
            @ApiParam("课件ID") @RequestParam(required = false) String sceneId,
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") int page,
            @ApiParam(value = "每页条数", defaultValue = "10") @RequestParam(defaultValue = "10") int size) {
        java.util.Map<String, Object> conditions = new java.util.HashMap<>();
        if (userId != null && !userId.isEmpty()) conditions.put("userId", userId);
        if (courseId != null && !courseId.isEmpty()) conditions.put("courseId", courseId);
        if (sceneId != null && !sceneId.isEmpty()) conditions.put("sceneId", sceneId);
        return Result.OK(operationRecordService.queryList(conditions, page, size));
    }

    /**
     * 批量根据ID查询操作记录
     *
     * @param ids 主键ID列表
     * @return 操作记录实体列表
     */
    @ApiOperation(value = "批量根据ID查询操作记录", httpMethod = "POST")
    @PostMapping("/getByIdList")
    public Result<List<OperationRecord>> getByIdList(@RequestBody @ApiParam("主键ID列表") List<String> ids) {
        return Result.OK(operationRecordService.findByIds(ids));
    }

} 