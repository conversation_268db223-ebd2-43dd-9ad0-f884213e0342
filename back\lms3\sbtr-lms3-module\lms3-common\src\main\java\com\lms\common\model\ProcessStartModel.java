package com.lms.common.model;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 提交或保存流程 实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessStartModel<T> {

    /**
     * model 主表对象
     */
    private JSONObject model;

    /**
     * modelClazz 主表Class对象
     */
    private Class<T> modelClazz;

    /**
     * subModelFieldName 主表中子表属性名称 传空集合或null表示没有子表
     */
    private List<String> subModelFieldNameList;

    /**
     * parentModelIdName 子表中关联主表id的字段
     */
    private String parentModelIdName;

    /**
     * businessTitlePrefix 流程标题前缀
     */
    private String businessTitlePrefix;

    private String businessTitleSuffix;

    /**
     * newCodePrefix 流程code前缀
     */
    private String newCodePrefix;

    /**
     * issubmit 提交还是保存
     */
    private Boolean issubmit;

    /**
     * modelKey 流程key
     */
    private String modelKey;

    /**
     * processInstanceId 流程实例id
     */
    private String processInstanceId;

    /**
     * taskId 任务id
     */
    private String taskId;

    /**
     * message 审批意见
     */
    private String message;

    /**
     * processDefinitionId 流程定义id
     */
    private String processDefinitionId;

    /**
     * 当前流程节点id
     */
    private String nodeId;

    /**
     * params 附加参数
     */
    private Map params;

    /**
     * 是否是流程自由跳转
     */
    private Boolean isProcessFreeJump;

    private Map<String, Object> processParams;

    public ProcessStartModel(JSONObject model, Class<T> modelClazz, List<String> subModelFieldNameList, String parentModelIdName, String businessTitlePrefix, String businessTitleSuffix, String newCodePrefix, Boolean issubmit, String modelKey) {
        this.model = model;
        this.modelClazz = modelClazz;
        this.subModelFieldNameList = subModelFieldNameList;
        this.parentModelIdName = parentModelIdName;
        this.businessTitlePrefix = businessTitlePrefix;
        this.businessTitleSuffix = businessTitleSuffix;
        this.newCodePrefix = newCodePrefix;
        this.issubmit = issubmit;
        this.modelKey = modelKey;
    }
}
