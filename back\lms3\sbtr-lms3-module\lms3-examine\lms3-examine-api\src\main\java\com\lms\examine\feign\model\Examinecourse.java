package com.lms.examine.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import lombok.Data;

/**
 * UExaminepoint entity. <AUTHOR> Persistence Tools
 */

@Data
@TableName("u_examinecourse")
public class Examinecourse extends BaseModel {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String examineid;

    private String courseid;

    private String coursename;

    private String pointid;

}
