package com.lms.examine.feign.api;

import com.lms.common.config.FeignConfig;
import com.lms.common.model.Result;
import com.lms.examine.feign.fallback.ExaminePageApiFallbackFactory;
import com.lms.examine.feign.model.Examinepage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(contextId = "examinesubjectApi",value = "lms-examine", path = "/examinepage",configuration = FeignConfig.class,fallbackFactory = ExaminePageApiFallbackFactory.class)
public interface ExaminePageApi {

    @GetMapping("/listExaminepage")
    Result<List<Examinepage>> listExaminepage(@RequestParam String planId);

    @GetMapping("/listSQLQuery")
    Result<List<Map<String, Object>>> listSQLQuery(@RequestParam String sql);

    @PostMapping("/saveAllForImport")
    Result saveAllForImport(@RequestBody List<Examinepage> examinepageList);

}
