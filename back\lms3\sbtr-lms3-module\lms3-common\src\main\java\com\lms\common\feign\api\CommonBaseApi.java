package com.lms.common.feign.api;

import com.lms.common.config.FeignConfig;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.feign.dto.Department;
import com.lms.common.feign.dto.Person;
import com.lms.common.feign.dto.Selectitem;
import com.lms.common.feign.fallback.CommonBaseApiFallbackFactory;
import com.lms.common.model.Component;
import com.lms.common.model.Result;
import org.springframework.cloud.openfeign.FeignClient;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: quanjinghua
 * @Date: 2024/7/9 19:10
 * @Description: 系统公共接口集合, 对比较常用的，全局性使用的接口，写入公共类，其他服务集成common类即可使用，
 * 无需再引用对应的服务接口，一般超过半数服务都会使用的接口，考虑纳入公共接口
 */
@FeignClient(contextId = "CommonBaseApi", value = "lms-base", url = "${feignClient.baseUrl.baseApi:}", configuration = FeignConfig.class, fallbackFactory = CommonBaseApiFallbackFactory.class)
public interface CommonBaseApi {

    @GetMapping(value = {"/department/getDepartmentById"})
    Result<Department> getDepartmentById(@RequestParam("id") String id);

    @GetMapping(value = {"/person/getPersonById"})
    Result<Person> getPersonById(@RequestParam("id") String id);

    @GetMapping(value = {"/selectitem/getById"})
    Result<Selectitem> getSelectitemById(@RequestParam("id") String id);

    @GetMapping(value = {"/selectitem/getSelectitemById/{id}"})
    Result<List<Selectitem>> getSelectitemsByType(@PathVariable("id") String id);

    @PostMapping(value = {"/person/listByCondition"})
    Result<Page<Person>> listByConditionPersons(@RequestBody PageInfo pageInfo);

    @PostMapping("/component/getComponentByIdList")
    Result<List<Component>> getComponentByIdList(@RequestBody List<String> idList);

    @PostMapping("/component/getAllChildComponentsByPidFullPath")
    Result<List<Component>> getAllChildComponentsByPidFullPath(@RequestParam("pidFullPath") String pidFullPath);

    @RequestMapping(value = {"/selectitem/getSelectitemByIdWidthEmpty/{id}"}, method = RequestMethod.GET)
    Result<List<Selectitem>> getSelectitemByIdWidthEmpty(@PathVariable("id") String typeid);

}
