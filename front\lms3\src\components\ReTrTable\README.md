# ReTrTable 组件

基于 Ant Design Vue 的 a-table 二次封装的表格组件，支持服务端和客户端分页、自定义搜索、批量/行操作按钮、配置式查询区等功能。

## 基础用法

```vue
<template>
  <ReTrTable
    v-model:table-config="tableConfig"
    v-model:query-config="queryConfig"
    :btn-config="btnConfig"
    :pagination-mode="paginationMode"
    @customButton="handleCustomButton"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ReTrTable, { type TableConfig, type BtnConfig, type QueryConfig } from '@/components/ReTrTable';

// 查询配置
const queryConfig = ref<QueryConfig>({
  items: [
    { key: 'keyword', label: '姓名', component: 'a-input' },
    { key: 'status', label: '状态', component: 'a-select', props: { options: [ { label: '全部', value: '' }, { label: '启用', value: 1 }, { label: '禁用', value: 0 } ] } }
  ],
  queryForm: { keyword: '', status: '' },
  onQuery: (form, params) => {
    // 查询逻辑
  },
  onReset: (form) => {
    // 重置逻辑
  }
});

// 表格配置
const tableConfig = ref<TableConfig>({
  tableData: [],
  total: 0,
  pageIndex: 1,
  pageSize: 10,
  sortField: 'id',
  sortOrder: 'descend',
  displayColumns: [
    { label: '姓名', prop: 'name', width: 120, fixed: 'left' },
    { label: '年龄', prop: 'age', width: 80, align: 'center', sortable: true },
    { label: '状态', prop: 'status', width: 100, align: 'center' },
    { label: '创建时间', prop: 'createTime', width: 180 }
  ]
});

// 按钮配置
const btnConfig = ref<BtnConfig[]>([
  {
    key: 'add',
    label: '新增',
    icon: PlusOutlined,
    type: 'batch',
    method: () => handleAdd(),
    props: { type: 'primary' }
  },
  {
    key: 'edit',
    label: '编辑',
    icon: EditOutlined,
    type: 'row',
    method: (row: any) => handleEdit([row]),
    props: {}
  }
]);

const paginationMode = ref<'server' | 'client'>('client');

function handleAdd() {}
function handleEdit(rows: any[]) {}
function handleCustomButton(key: string, data: any) {}
</script>
```

## 双向绑定

组件支持查询参数和排序状态的双向绑定：

```vue
<template>
  <ReTrTable v-model:query-config="queryConfig" v-model:table-config="tableConfig" />
</template>

<script setup>
// 设置查询条件
queryConfig.value.queryForm = { keyword: '张三', status: '1' };

// 设置排序状态
tableConfig.value.sortField = 'age';
tableConfig.value.sortOrder = 'ascend';
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| tableConfig | TableConfig | - | 表格配置对象，必填 |
| btnConfig | BtnConfig[] | [] | 批量/行操作按钮配置 |
| queryConfig | QueryConfig | - | 查询区配置 |
| paginationMode | 'server' \| 'client' | 'server' | 分页模式 |

### 双向绑定支持

- `v-model:table-config` - 表格配置双向绑定（分页、排序状态）
- `v-model:query-config` - 查询配置双向绑定（查询表单数据）

## 配置接口

### TableConfig

| 字段 | 类型 | 说明 |
|------|------|------|
| tableData | any[] | 表格数据 |
| total | number | 数据总条数 |
| pageIndex | number | 当前页码 |
| pageSize | number | 每页条数 |
| sortField | string | 排序字段 |
| sortOrder | 'ascend' \| 'descend' \| null | 排序顺序 |
| displayColumns | DisplayColumn[] | 列配置 |
| showCheckBox | boolean | 是否显示复选框 |
| showIndex | boolean | 是否显示序号列 |
| showOperateColumn | boolean | 是否显示操作列 |

### QueryConfig

| 字段 | 类型 | 说明 |
|------|------|------|
| items | QueryItem[] | 查询项配置 |
| queryForm | Record<string, any> | 查询表单数据 |
| onQuery | (form, params) => void | 查询事件 |
| onReset | (form) => void | 重置事件 |

### BtnConfig

| 字段 | 类型 | 说明 |
|------|------|------|
| key | string | 唯一标识 |
| label | string | 按钮文本 |
| icon | any | 图标组件 |
| type | 'batch' \| 'row' | 按钮类型 |
| method | (...args: any[]) => void | 按钮点击事件 |
| props | Record<string, any> | 按钮属性 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:tableConfig | config: TableConfig | 表格配置变化 |
| update:queryConfig | config: QueryConfig | 查询配置变化 |
| customButton | key: string, data: any | 自定义按钮点击 |
| operationButton | { key: string, record: any } | 行操作按钮点击 |
| query | queryForm: Record<string, any> | 查询事件 |
| reset | - | 重置事件 |
| paginationChange | pagination: { pageIndex, pageSize, total } | 分页变化 |
| sortChange | sort: { field, order } | 排序变化 |

## 插槽

| 插槽名 | 说明 |
|--------|------|
| quickSearch | 自定义快速搜索区域 |
| moreSearch | 自定义高级搜索区域 |
| operation | 自定义操作列内容 |
| {prop} | 自定义列内容 |

## DisplayColumn 配置

| 参数 | 类型 | 说明 |
|------|------|------|
| label | string | 列名 |
| prop | string | 字段名 |
| width | string \| number | 列宽 |
| sortable | boolean | 是否可排序 |
| fixed | boolean \| 'left' \| 'right' | 是否固定列 |
| align | 'left' \| 'right' \| 'center' | 对齐方式 |
| render | Function | 自定义渲染函数 |
| renderType | string | 预设渲染类型（tag, img, time, html, status） |
| statusMap | object | 状态映射（用于tag/status渲染） |

## 注意事项

1. 推荐使用配置驱动方式（btnConfig/queryConfig）
2. 客户端分页模式下，搜索后会自动重置到第一页
3. 服务端分页模式下，分页变化会自动触发查询
4. 列插槽名称必须与 prop 字段名一致
