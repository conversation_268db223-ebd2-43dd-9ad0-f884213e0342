package com.sbtr.workflow.controller;

import cn.ewsd.common.annotation.ControllerLog;
import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.utils.BaseUtils;
import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import com.sbtr.workflow.model.MyVariable;
import com.sbtr.workflow.service.MyVariableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Date;


/**
 * 原始流程界面流程变量
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/myVariable")
@Api(tags = {"流程变量"})
public class MyVariableController extends WorkflowBaseController {

    @Resource
    private MyVariableService myVariableService;

    private final String hql = null;

    @ControllerLog(description = "打开MyVariable模块数据列表")
    @RequestMapping("/index")
    public String index() {
        return display("workflow/index/index");
    }



    @ControllerLog(description = "打开MyVariable模块编辑页面")
    @RequestMapping("/edit")
    public String edit() {
        return display("workflow/myVariable/edit");
    }

    @ResponseBody
    @ControllerLog(description = "获取MyVariable模块列表数据")
    @RequestMapping(value = "/getPageSetDataByProcessKey", method = RequestMethod.POST)
    @ApiOperation(value = "获得流程变量分页集数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String"),
            @ApiImplicitParam(name = "processKey", value = "流程KEY", required = true, dataType = "String")
    })
    public Object getPageSetDataByProcessKey(PageParam pageParam, String processKey) throws Exception {
        String filterStr = " and process_key ='" + processKey + "'  ";
        filterStr = BaseUtils.filterSort(request, filterStr );
        PageSet<MyVariable> pageSet = myVariableService.getPageSet(pageParam, filterStr);
        return pageSet;


    }

    @ResponseBody
    @ControllerLog(description = "获取MyVariable模块列表数据")
    @RequestMapping(value = "/getPageSet")
    public Object getPageSetData(PageParam pageParam, String name) throws Exception {
        String[] nameSplit = name.split("[.]");
        String filterStr = "process_key ='" + nameSplit[0] + "'";
        PageSet<MyVariable> pageSet = myVariableService.getPageSet(pageParam, filterStr);
        return pageSet;
    }

    @ResponseBody
    @ControllerLog(description = "获取MyVariable模块详细数据")
    @RequestMapping(value = "/getDetail")
    public Object getDetail(String uuid) throws Exception {
        MyVariable myVariable = myVariableService.selectByPrimaryKey(uuid);
        return myVariable;
    }

    @Log(title = "流程变量-保存MyVariable模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @ResponseBody
    @ControllerLog(description = "保存MyVariable模块数据")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Integer save(MyVariable myVariable) throws Exception {
        setLogMessage(myVariable.getVariableName(),"");
        Integer a = myVariableService.insertSelective(getSaveData(myVariable));
        return a;
    }

    @Log(title = "流程变量-修改MyVariable模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.UPDATE, method = "getLogMessage")
    @ResponseBody
    @ControllerLog(description = "修改MyVariable模块数据")
    @ApiOperation(value = "修改变量模块数据")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Integer update(MyVariable myVariable) throws Exception {
        myVariable.setModifyTime(new Date());
        setLogMessage(myVariable.getVariableName(),"");
        Integer result = myVariableService.updateByPrimaryKeySelective(myVariable);
        return result;
    }

    @Log(title = "流程变量-删除MyVariable模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ResponseBody
    @ControllerLog(description = "删除MyVariable模块数据")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ApiOperation(value = "删除流程变量模块数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", value = "标识", required = true, dataType = "String")
    })
    public Integer delete(@RequestParam String  uuid) throws Exception {
        Integer integer=0;
        String [] u=uuid.split(",");
        StringBuilder logStr=new StringBuilder();
        for (int i=0;i<u.length;i++){
            integer = myVariableService.deleteByPrimaryKey(u[i]);
            MyVariable myVariable = myVariableService.selectByPrimaryKey(u[i]);
            if(myVariable!=null)
                logStr.append(myVariable.getVariableName()+",");
        }
        setLogMessage(logStr.toString(),"");
        return integer;
    }

    @ResponseBody
    @ControllerLog(description = "获取流程业务页面地址")
    @RequestMapping("/getVariable")
    public Object getVariable(String processKey, String variableName) {
        return myVariableService.getVariable(processKey, variableName);
    }

    @ResponseBody
    @ControllerLog(description = "获取流程业务页面地址和保存地址")
    @RequestMapping("/getVariables")
    public Object getVariables(String processKey) {
        return myVariableService.getVariables(processKey);
    }

    @Log(title = "生成流程变量",module = LogModule.WORKFLOW,businessType = BusinessType.START, method = "getLogMessage")
    @ResponseBody
    @ControllerLog(description = "生成流程变量")
    @RequestMapping(value = "/saveGenerate", method = RequestMethod.POST)
    @ApiOperation(value = "生成流程变量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "流程KEY", required = true, dataType = "String")
    })
    public Object saveGenerate(String key) {
        setLogMessage(key,"");
        return myVariableService.saveGenerate(key);
    }

}
