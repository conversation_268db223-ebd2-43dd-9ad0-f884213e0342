package com.lms.examine.examinestudent.service;

import com.lms.common.model.Result;
import com.lms.common.service.BaseService;
import com.lms.examine.feign.model.Examinepage;
import com.lms.examine.feign.model.Examinestudent;
import com.lms.examine.feign.model.VUExaminestudent;

import java.util.List;
import java.util.Map;

public interface ExaminestudentService extends BaseService<Examinestudent> {

    List<Examinestudent> getExaminestudentsByExamId(String null2String);

    List<Examinestudent> getStudentByExamine(String student, String examineid);

    void createStudentRecordByExamine(Examinestudent examinestudent, String examineid, Map<String, List> subjectMap);

    String chekcSubjectAmountByExamine(Examinepage ep, int onlyexam, Map<String, List> subjectMap);

    String chekcSubjectAmount(Examinepage examinepage, int onlyexam);

    List<Examinestudent> getLearnStatusById(String id, String studentId);

    List<Examinestudent> getValidExaminestudents();

    List<Map<String, Object>> getScoreRank(String examined, String personid);

    List<VUExaminestudent> setAttachByEsts(List<VUExaminestudent> records);

    Result batchReleaseExaminepage(List<String> idList);

    Map<String, Object> getStudentExamineStatistics(String personId);
}
