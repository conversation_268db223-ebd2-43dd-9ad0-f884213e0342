package com.lms.base.courseware.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.base.courseware.mapper.CoursewareMapper;
import com.lms.base.courseware.service.CoursewareService;
import com.lms.common.feign.dto.FlowModelDto;
import com.lms.base.feign.model.Courseware;
import com.lms.common.config.LMSConfiguration;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.dao.query.Parameter;
import com.lms.common.feign.api.ActFlowableApi;
import com.lms.common.feign.dto.ModleDto;
import com.lms.common.model.Result;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.StringHelper;
import com.lms.system.feign.api.AttachApi;
import com.lms.system.feign.model.Attach;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("CoursewareService")
public class CoursewareServiceImpl extends BaseServiceImpl<CoursewareMapper, Courseware> implements CoursewareService {

    @Resource
    private CoursewareMapper coursewareMapper;

    @Resource
    private AttachApi attachApi;

    @Resource
    private LMSConfiguration lmsConfiguration;

    @Resource
    private ActFlowableApi actFlowableApi;

    @SuppressWarnings("unchecked")
    public List<Courseware> getCoursewareList(String componentid) {
        return coursewareMapper.getCoursewareList(componentid);
    }

    public void createCourseware(Courseware courseware) {
        coursewareMapper.insert(courseware);
    }

    public void saveCourseware(Courseware courseware) {
        if (StringUtils.isNotEmpty(courseware.getId())) {
            coursewareMapper.updateById(courseware);
        } else {
            coursewareMapper.insert(courseware);
        }
    }

    public Courseware getCoursewareById(String coursewareid) {
        return coursewareMapper.getCourseware(coursewareid);
    }

    public boolean checkAttachUsedByOtherCourseware(String attachId) {
        PageInfo pageInfo = new PageInfo();
        if (!StringHelper.isEmpty(attachId)) {
            Parameter attachIdParam = Parameter.getParameter(
                    "S_EQ_attachid", attachId);
            pageInfo.getParameters().add(attachIdParam);
        }
        return this.listByCondition(pageInfo).getRecords().size() > 1;
    }

    public boolean checkAttachUsedByApproveCourseware(String attachId) {
        PageInfo pageInfo = new PageInfo();
        if (!StringHelper.isEmpty(attachId)) {
            Parameter attachIdParam = Parameter.getParameter(
                    "S_EQ_attachid", attachId);
            pageInfo.getParameters().add(attachIdParam);
            Parameter parameter2 = Parameter.getParameter(
                    "I_EQ_status", 5);
            pageInfo.getParameters().add(parameter2);
        }
        return this.listByCondition(pageInfo).getRecords().size() > 0;
    }

    public void deleteById(String id) {
        Courseware courseware = this.getById(id);
        //附件不为空,且没有被其他课件引用,且没有被已发布状态课程引用,删除附件信息，否则不删除附件！
        Result<Attach> attachResult = this.attachApi.get(courseware.getAttachid());
        if (attachResult != null && !checkAttachUsedByOtherCourseware(courseware.getAttachid()) && !checkAttachUsedByApproveCourseware(courseware.getAttachid())) {
            String filePath;
            File file;
            if (courseware.getIsscorm() != null && courseware.getIsscorm() > 0) {
                String filedir = attachResult.getResult().getFiledir();
                String substring = filedir.substring(0, filedir.lastIndexOf("\\"));
                filePath = lmsConfiguration.getScormStoragePath() + substring;
                file = new File(filePath);
                //如果是scorm文件，删除目录；
                if (file.exists()) {
                    try {
                        if (file.isDirectory()) {
                            FileUtils.deleteDirectory(file);
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                Attach attach = attachResult.getResult();
                attachApi.deleteFile(attach);
            }
            attachApi.delete(attachResult.getResult().getId());
            this.removeById(id);
        }
    }

    public boolean exsitName(Courseware courseware) {
        return coursewareMapper.exsitName(courseware);
    }

    public List<Map<String, Object>> getCoursewareAnalysisByType(String type) {
        return coursewareMapper.getCoursewareAnalysisByType(type);
    }

    public int getMaxSeqnoByCourse(String courseId) {
        Integer seqno = coursewareMapper.getMaxSeqnoByCourse(courseId);
        return seqno == null ? 0 : seqno;
    }

    public int getTotalCourseware() {
        return coursewareMapper.getTotalCourseware();
    }


    public List<Courseware> getCourseWareByCourseIdList(List<String> courseIdList) {
        if (CollectionUtils.isNotEmpty(courseIdList)) {
            LambdaQueryWrapper<Courseware> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(Courseware::getComponentid, courseIdList);
            return this.list(wrapper);
        } else {
            return null;
        }
    }

    public Boolean existCoursewareNo(Courseware courseware) {
        return coursewareMapper.existCoursewareNo(courseware.getNumber(), StringHelper.null2String(courseware.getId())).size() > 0;
    }

    @Override
    public HashMap<String, Object> saveFlowchart(FlowModelDto flowModelDto) {
        return (HashMap<String, Object>) actFlowableApi.saveCourseWareFlowchart(flowModelDto);
    }

    @Override
    public HashMap<String, Object> getFlowchart(String id) {
        return (HashMap<String, Object>) actFlowableApi.getCourseWareFlowchart(id);
    }

    @Override
    public HashMap<String, Object> updateFlowchart(ModleDto modleDto) {
        return (HashMap<String, Object>) actFlowableApi.updateCourseWareFlowchart(modleDto);
    }

    @Override
    public HashMap<String, Object> deleteFlowchart(String actDeModelId) {
        return (HashMap<String, Object>) actFlowableApi.deleteModel(actDeModelId,"",1);
    }
}
