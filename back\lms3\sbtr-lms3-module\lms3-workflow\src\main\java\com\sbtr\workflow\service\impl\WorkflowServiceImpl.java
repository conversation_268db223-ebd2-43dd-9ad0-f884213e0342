package com.sbtr.workflow.service.impl;

import cn.ewsd.common.common.Constants;
import cn.ewsd.common.utils.JsonUtils;
import cn.ewsd.common.utils.StringUtils;
import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import cn.ewsd.common.utils.easyui.PageUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.dto.*;
import com.sbtr.workflow.enums.FlowEnum;
import com.sbtr.workflow.mapper.ApiFlowableProcessDefinitionMapper;
import com.sbtr.workflow.mapper.MyVariableMapper;
import com.sbtr.workflow.model.ActHiActinst;
import com.sbtr.workflow.model.MyVariable;
import com.sbtr.workflow.model.SysUser;
import com.sbtr.workflow.model.TaskCommon;
import com.sbtr.workflow.service.ApiFlowableTaskService;
import com.sbtr.workflow.service.BusinessSystemDataService;
import com.sbtr.workflow.service.MyVariableService;
import com.sbtr.workflow.service.WorkflowService;
import com.sbtr.workflow.utils.AddHisCommentCmd;
import com.sbtr.workflow.vo.CommentVo;
import com.sbtr.workflow.vo.ProcessDefinitionVo;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceQuery;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.Task;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service("workflowService")
public class WorkflowServiceImpl extends WorkflowBaseServiceImpl<MyVariable, String> implements WorkflowService {

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private TaskService taskService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private MyVariableService myVariableService;
    @Autowired
    private ManagementService managementService;
    @Resource
    private BusinessSystemDataService businessSystemDataService;
    @Autowired
    private MyVariableMapper myVariableMapper;

    @Override
    public String[] getFlows() {
        String basePath = WorkflowServiceImpl.class.getResource("/").getPath();
        basePath = basePath;
        String diagramsPath = basePath + File.separator + "processes";
        System.out.println(diagramsPath);
        return new File(diagramsPath).list();
    }

    @Override
    public PageSet<TemplateDto> getFlowsPageSet() {
        String basePath = WorkflowServiceImpl.class.getResource("/").getPath();
        String diagramsPath = basePath + File.separator + "processes";
        diagramsPath = diagramsPath.replace("file:", "");
        //String[] files = new File(diagramsPath).list();
        //List<String> fileList = Arrays.asList(files);

        File baseFile = new File(diagramsPath);
        File[] files = baseFile.listFiles();
        if (files == null) {
            String diagramsPath2 = diagramsPath.replace("/workflow.jar!/BOOT-INF/classes!/", "").replace("file:", "");
            File baseFile2 = new File(diagramsPath2);
            files = baseFile2.listFiles();
        }

        List<Map<String, Object>> arrayList = new ArrayList<>();
        Integer total = 0;
        if (files != null) {
            for (File file : files) {
                Map<String, Object> map = new HashMap<>();
                map.put("name", file.getName());
                map.put("path", file.getPath());
                arrayList.add(map);
            }
            total = files.length;
        }

        PageSet<TemplateDto> pageSet = new PageSet(total, arrayList);
        return pageSet;
    }

    @Override
    public void deployFlow(String processName) {
        RepositoryService repositoryService = processEngine.getRepositoryService();
        if (null != processName) {
            repositoryService.createDeployment().addClasspathResource("processes/" + processName).deploy();
        }
    }


    @Autowired
    private ApiFlowableProcessDefinitionMapper apiFlowableProcessDefinitionMapper;

    @Value("${spring.datasource.dynamic.datasource.master.driver-class-name:null}")
    public String driverClassName;

    @Override
    public PageSet<ProcessDefinitionVo> getDeployedPageSet(PageParam pageParam, String category, String processModelType, String name) {
        String majorVersion = "是";
        PageHelper.startPage(pageParam.getPage(), pageParam.getRows());
        List<ProcessDefinitionVo> list = apiFlowableProcessDefinitionMapper.getPageSetMySql(category, processModelType, name, majorVersion);
        PageInfo<ProcessDefinitionVo> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    //@Override
    //public PageSet<ProcessDefinitionDto> getDeployedPageSet(PageParam pageParam, String type, String processDefinitionName, String key) {
    //    processDefinitionName = null == processDefinitionName ? "" : processDefinitionName;
    //    key = null == key ? "" : key;
    //    Integer firstResults = (pageParam.getPage() - 1) * pageParam.getRows();
    //    Integer maxResults = pageParam.getPage() * pageParam.getRows();
    //    RepositoryService repositoryService = processEngine.getRepositoryService();
    //    List<ProcessDefinition> processDefinitions;
    //    if (type.equals("all")) {
    //        processDefinitions = repositoryService.createProcessDefinitionQuery().orderByDeploymentId().processDefinitionNameLike("%" + processDefinitionName + "%").processDefinitionKeyLike("%" + key + "%").desc().listPage(firstResults, maxResults);
    //    } else {
    //        processDefinitions = repositoryService.createProcessDefinitionQuery().latestVersion()
    //                .processDefinitionNameLike("%" + processDefinitionName + "%")
    //                .processDefinitionKeyLike("%" + key + "%").orderByDeploymentId().desc().listPage(firstResults, maxResults);
    //    }
    //    List<ProcessDefinitionDto> processDefinitionDtos = new ArrayList<>();
    //    for (ProcessDefinition processDefinition : processDefinitions) {
    //        ProcessDefinitionDto processDefinitionDto = new ProcessDefinitionDto();
    //        processDefinitionDto.setId(processDefinition.getId());
    //        processDefinitionDto.setName(processDefinition.getName());
    //        processDefinitionDto.setKey(processDefinition.getKey());
    //        processDefinitionDto.setVersion(processDefinition.getVersion());
    //        processDefinitionDto.setDescription(processDefinition.getDescription());
    //        processDefinitionDto.setDeploymentId(processDefinition.getDeploymentId());
    //        processDefinitionDtos.add(processDefinitionDto);
    //    }
    //
    //    Integer total = processDefinitions.size();
    //    PageSet<ProcessDefinitionDto> pageSet = new PageSet(total, processDefinitionDtos);
    //    return pageSet;
    //}

    @Override
    public PageSet<Deployment> getDeploymentPageSet(PageParam pageParam) {
        Integer firstResults = (pageParam.getPage() - 1) * pageParam.getRows();
        Integer maxResults = pageParam.getPage() * pageParam.getRows();
        RepositoryService repositoryService = processEngine.getRepositoryService();
        List<Deployment> list = repositoryService.createDeploymentQuery().orderByDeploymenTime().desc().listPage(firstResults, maxResults);

        Integer total = list.size();
        PageSet<Deployment> pageSet = new PageSet(total, list);
        return pageSet;
    }

    @Override
    public void startProcess(String processDefinitionKey, String businessId, Map<String, Object> map) {
        RuntimeService runtimeService = processEngine.getRuntimeService();
        //runtimeService.startProcessInstanceById(processDeployId, businessId, map);
        runtimeService.startProcessInstanceByKey(processDefinitionKey, businessId, map);
    }

    @Override
    public PageSet<ProcessInstance> getStartedProcess(PageParam pageParam, String key) {
        Integer firstResults = (pageParam.getPage() - 1) * pageParam.getRows();
        Integer maxResults = pageParam.getPage() * pageParam.getRows();

        RuntimeService runtimeService = processEngine.getRuntimeService();
        List<ProcessInstance> list = null;
        if (StringUtils.isNullOrEmpty(key)) {
            list = runtimeService.createProcessInstanceQuery().orderByProcessInstanceId().desc().listPage(firstResults, maxResults);
        } else {
            list = runtimeService.createProcessInstanceQuery().processDefinitionKey(key).orderByProcessInstanceId().desc().listPage(firstResults, maxResults);

        }
        Long total = runtimeService.createProcessInstanceQuery().count();
        PageSet<ProcessInstance> pageSet = new PageSet(total, list);
        return pageSet;
    }


    @Override
    public PageSet<ProcessInstanceDto> getMyNoEndProcessPageSetData(PageParam pageParam, String currentUserNameId, String processDefinitionName, String processModelType) {
        PageHelper.startPage(pageParam.getPage(), pageParam.getRows());
        List<ProcessInstanceDto> taskLists = myVariableMapper.getMyNoEndProcessPageSetData(processDefinitionName, currentUserNameId, processDefinitionName, processModelType);
        PageInfo<ProcessInstanceDto> pageInfo = new PageInfo<>(taskLists);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public PageSet<CommonTaskDto> getTasks(PageParam pageParam, String taskName) {
        Integer firstResults = (pageParam.getPage() - 1) * pageParam.getRows();
        Integer maxResults = pageParam.getPage() * pageParam.getRows();

        TaskService taskService = processEngine.getTaskService();
        taskName = null == taskName ? "" : taskName;
        List<Task> tasks = taskService.createTaskQuery().taskNameLike("%" + taskName + "%").orderByTaskCreateTime().desc().listPage(firstResults, maxResults);

        ProcessInstanceQuery processInstanceQuery = runtimeService
                .createProcessInstanceQuery();

        List<CommonTaskDto> commonTaskDtos = new ArrayList<>();
        for (Task task : tasks) {
            CommonTaskDto commonTaskDto = new CommonTaskDto();
            commonTaskDto.setTaskId(task.getId());
            commonTaskDto.setExecutionId(task.getExecutionId());
            commonTaskDto.setTaskName(task.getName());
            commonTaskDto.setProcdefId(task.getProcessDefinitionId());
            commonTaskDto.setTaskCreateTime(task.getCreateTime());

            //获得当前任务所在的流程实例
            ProcessInstance processInstance = processInstanceQuery.
                    processInstanceId(task.getProcessInstanceId()).
                    singleResult();
            commonTaskDto.setModelKey(processInstance.getProcessDefinitionKey());

            //获得流程变量
            commonTaskDto.setVariables(taskService.getVariables(task.getId()));

            //获得到当前流程实例中的业务键（请假单ID），注入到业务Bean中
            commonTaskDto.setBusinessKey(processInstance.getBusinessKey());
            //String income = this.getTaskIncome(task.getId());
            //commonTaskDto.setIncomeing(income);
            commonTaskDto.setAssignee(task.getAssignee());

            commonTaskDtos.add(commonTaskDto);
        }

        Long total = taskService.createTaskQuery().count();
        PageSet<CommonTaskDto> pageSet = new PageSet(total, commonTaskDtos);
        return pageSet;
    }

    @Override
    public void completeTask(String taskId) {
        TaskService service = processEngine.getTaskService();
        service.complete(taskId);
    }

    /**
     * 完成任务
     *
     * @param taskId
     * @param comment
     * @param userNameId
     * @param vars
     */
    @Override
    public void completeTask(String taskId, String comment, String userNameId, Map<String, Object> vars) {
        TaskService taskService = processEngine.getTaskService();
        //获得任务对象
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();

        //设置批注的添加人
        Authentication.setAuthenticatedUserId(userNameId);
        taskService.addComment(taskId, task.getProcessInstanceId(), comment);


        if (vars.get("outcome") == null) {
            taskService.complete(taskId);
        } else {
            taskService.complete(taskId, vars);
        }

        //当我们驳回到第一步申请人的时候, 手动更新act_ru_taks,act_hi_taskinst表中处理人
        if ("驳回".equals(vars.get("outcome"))) {
            HashMap<String, String> map1 = myVariableMapper.getAssings(task.getExecutionId());
            TaskCommon map = myVariableMapper.getListByExecutionId(map1.get("procinstid"));
            if (StringUtils.isNullOrEmpty(map.getAssignee())) {
                Integer integerActRuTask = myVariableMapper.updateAssigneeById(map1.get("assignee"), map.getId());
                Integer integerActHiTaskinst = myVariableMapper.updateAssignee(map1.get("assignee"), map.getId());

            }
        }
    }


    @Override
    public Map<String, Object> getFlowElementByProcessDefinitionId(String processDefinitionId, String flowElementType) {
        Map<String, Object> map = new HashMap<String, Object>();
        //获取bpmnModel对象
        BpmnModel model1 = repositoryService.getBpmnModel(processDefinitionId);
        //  BpmnModel model1 = new BpmnJsonConverter().convertToBpmnModel(modelNode);
        //由于我们这里仅仅定义了一个Process 所以获取集合中的第一个就可以
        //Process对象封装了全部的节点、连线、以及关口等信息。拿到这个对象就能够为所欲为了。
        Process process = model1.getProcesses().get(0);

        //获取全部的FlowElement（流元素）信息
        Collection<FlowElement> flowElements = process.getFlowElements();
        for (FlowElement flowElement : flowElements) {
            //假设是开始节点
            if (flowElementType.equals("startEvent") && flowElement instanceof StartEvent) {
                StartEvent startEvent = (StartEvent) flowElement;
                map.put("startEvent_" + flowElement.getId(), startEvent);
            }
            //假设是任务节点
            if (flowElementType.equals("userTask") && flowElement instanceof UserTask) {
                UserTask userTask = (UserTask) flowElement;
                List<String> candidateUsers = userTask.getCandidateUsers();
                for (String string : candidateUsers) {
                    System.out.println("-------" + string);
                }
                map.put("userTask_" + flowElement.getId(), userTask);
            }
            //假设是结束节点
            if (flowElementType.equals("endEvent") && flowElement instanceof EndEvent) {
                EndEvent endEvent = (EndEvent) flowElement;
                map.put("endEvent_" + flowElement.getId(), endEvent);
            }
            //假设是连接线
            if (flowElementType.equals("sequenceFlow") && flowElement instanceof SequenceFlow) {
                SequenceFlow sequenceFlow = (SequenceFlow) flowElement;
                map.put("sequenceFlow_" + flowElement.getId(), sequenceFlow);
            }
        }
        return map;
    }

    @Override
    public List getVarsNameByProcessDefinitionId(String processDefinitionId) {
        Map<String, Object> flowElementMap = this.getFlowElementByProcessDefinitionId(processDefinitionId, "sequenceFlow");
        List vars = new ArrayList();
        flowElementMap.forEach((key, value) -> {
            SequenceFlow sequenceFlow = (SequenceFlow) flowElementMap.get(key);
            String conditionExpression = sequenceFlow.getConditionExpression();
            if (conditionExpression != null) {
                if (conditionExpression.contains("${")) {
                    String[] split = conditionExpression.split(" ");
                    vars.add(split[0].replace("${", ""));
                }
            }
        });

        List varsTemp = new ArrayList();
        for (int i = 0; i < vars.size(); i++) {
            if (!varsTemp.contains(vars.get(i))) {
                varsTemp.add(vars.get(i));
            }
        }

        return varsTemp;
    }

    /**
     * 使用部署ID，完成删除
     *
     * @param deploymentId
     */
    @Override
    public void deleteProcessDefinition(String deploymentId) {
        //与流程定义和部署对象相关的Service
        RepositoryService repositoryService = processEngine.getRepositoryService();
        //不带级联的删除，只能删除没有启动的流程，如果流程启动，就会抛出异常
        //repositoryService.deleteDeployment(deploymentId);
        //能级联的删除，能删除启动的流程，会删除和当前规则相关的所有信息，正在执行的信息，也包括历史信息
        repositoryService.deleteDeployment(deploymentId, true);
    }

    /*@Override
    public List<LeaveTaskDto> getTasksByAssigneeAndUserNameId(String assignee, String userNameId) {
        TaskService taskService = processEngine.getTaskService();
        RuntimeService runtimeService = processEngine.getRuntimeService();
        List<LeaveTaskDto> taskBeanList = new ArrayList<LeaveTaskDto>();
        List<Task> taskList = new ArrayList<Task>();
        if ("topjui".equals(assignee)) {
            taskList = taskService.createTaskQuery().processDefinitionKey("leave")
                    .taskAssignee(assignee)
                    .processVariableValueEquals("userNameId", userNameId)
                    .orderByTaskCreateTime().desc().list();
        } else {
            taskList = taskService.createTaskQuery().processDefinitionKey("leave")
                    .taskAssignee(assignee)
                    .orderByTaskCreateTime().desc().list();
        }

        ProcessInstanceQuery processInstanceQuery = runtimeService
                .createProcessInstanceQuery()
                .processDefinitionKey("leave");
        for (Task task : taskList) {
            LeaveTaskDto leaveTaskBean = new LeaveTaskDto();
            leaveTaskBean.getCommonTaskDto().setTaskId(task.getId());
            leaveTaskBean.getCommonTaskDto().setTaskName(task.getName());
            //获得当前任务所在的流程实例
            ProcessInstance processInstance = processInstanceQuery.
                    processInstanceId(task.getProcessInstanceId()).
                    singleResult();
            //获得到当前流程实例中的业务键（请假单ID），注入到业务Bean中
            leaveTaskBean.getCommonTaskDto().setBusinessKey(processInstance.getBusinessKey());
            String income = this.getTaskIncome(task.getId());
            leaveTaskBean.getCommonTaskDto().setIncomeing(income);
            leaveTaskBean.getCommonTaskDto().setAssignee(task.getAssignee());
            taskBeanList.add(leaveTaskBean);
        }

        return taskBeanList;
    }*/

    @Override
    public PageSet<TaskCommon> getTasksByAssignee(PageParam pageParam, String assignee, HttpServletRequest request, String processModelType) {
        String processDefinitionName = StringUtils.convertNullToEmpty(request.getParameter("processDefinitionName"));
        PageHelper.startPage(pageParam.getPage(), pageParam.getRows());
        List<TaskCommon> taskLists = myVariableMapper.taskQuery(processDefinitionName, assignee, processModelType);
        PageInfo<TaskCommon> pageInfo = new PageInfo<>(taskLists);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public PageSet<CommonTaskDto> getHistoryTasksByStartUserId(PageParam pageParam, String startUserId, String processDefinitionName) {
        Integer firstResults = (pageParam.getPage() - 1) * pageParam.getRows();
        Integer maxResults = pageParam.getPage() * pageParam.getRows();

        TaskService taskService = processEngine.getTaskService();
        List<CommonTaskDto> commonTaskDtoList = new ArrayList<>();
        List<HistoricProcessInstance> historicProcessInstances = null;
        if (StringUtils.isNullOrEmpty(processDefinitionName)) {
            historicProcessInstances = historyService.createHistoricProcessInstanceQuery().startedBy(startUserId).finished()
                    .orderByProcessInstanceEndTime().desc().listPage(firstResults, maxResults);
        } else {
            historicProcessInstances = historyService.createHistoricProcessInstanceQuery().startedBy(startUserId).finished().processDefinitionName(processDefinitionName)
                    .orderByProcessInstanceEndTime().desc().listPage(firstResults, maxResults);
        }

        Long total = historyService.createHistoricProcessInstanceQuery().startedBy(startUserId).finished().count();
        if (historicProcessInstances.size() > 0) {
            for (HistoricProcessInstance historicProcessInstance : historicProcessInstances) {
                CommonTaskDto commonTaskDto = new CommonTaskDto();
                commonTaskDto.setName(historicProcessInstance.getProcessDefinitionName());
                commonTaskDto.setTaskId(historicProcessInstance.getId());
                commonTaskDto.setTaskName(historicProcessInstance.getName());
                commonTaskDto.setInstanceId(historicProcessInstance.getId());
                commonTaskDto.setProcessStartTime(historicProcessInstance.getStartTime());
                commonTaskDto.setProcessEndTime(historicProcessInstance.getEndTime());
                commonTaskDto.setProcessDefinitionId(historicProcessInstance.getProcessDefinitionId());
                commonTaskDto.setModelKey(historicProcessInstance.getProcessDefinitionKey());
                //获得流程变量
                //String businessTitle = taskService.getVariable(task.getId(), "businessTitle").toString();
                //String businessDetailUrl = taskService.getVariable(task.getId(), "businessDetailUrl").toString();
                List<HistoricVariableInstance> historicVariableInstances = historyService.createHistoricVariableInstanceQuery()
                        .processInstanceId(historicProcessInstance.getId()).list();

                Map<String, Object> variables = new HashMap<>();
                for (int i = 0; i < historicVariableInstances.size(); i++) {
                    HistoricVariableInstance hvi = historicVariableInstances.get(i);
                    variables.put(hvi.getVariableName(), hvi.getValue());
                }
                //于永清 判断页面上按钮走流程的 还是走业务的那边的
                //variables.put("businessJudge",myVariableService.getVariables(historicProcessInstance.getProcessDefinitionKey().toString()).get(3).getVariableValue());
                commonTaskDto.setVariables(variables);

                //获得到当前流程实例中的业务键（请假单ID），注入到业务Bean中
                commonTaskDto.setBusinessKey(historicProcessInstance.getBusinessKey());
                commonTaskDtoList.add(commonTaskDto);
            }
        }


        PageSet<CommonTaskDto> pageSet = new PageSet(total, commonTaskDtoList);
        return pageSet;
    }

    /*@Override
    public List<TaskBean> getTasksByUserNameId(String userNameId) {
        TaskService taskService = processEngine.getTaskService();
        RuntimeService runtimeService = processEngine.getRuntimeService();
        List<TaskBean> taskBeanList = new ArrayList<TaskBean>();
        List<Task> taskList = new ArrayList<Task>();

        taskList = taskService.createTaskQuery()
                .processDefinitionKey("leave")
                .processVariableValueEquals("userNameId", userNameId)
                .orderByTaskCreateTime()
                .desc()
                .list();

        ProcessInstanceQuery processInstanceQuery = runtimeService
                .createProcessInstanceQuery()
                .processDefinitionKey("leave");

        for (Task task : taskList) {
            TaskBean taskBean = new TaskBean();
            taskBean.setTaskId(task.getId());
            taskBean.setTaskName(task.getName());

            //获得当前任务所在的流程实例
            ProcessInstance processInstance = processInstanceQuery.
                    processInstanceId(task.getProcessInstanceId()).
                    singleResult();

            //获得到当前流程实例中的业务键（请假单ID），注入到业务Bean中
            taskBean.setBusinessKey(processInstance.getBusinessKey());
            String income = this.getTaskIncome(task.getId());
            taskBean.setIncomeing(income);
            taskBean.setAssignee(task.getAssignee());

            taskBeanList.add(taskBean);
        }

        return taskBeanList;
    }*/

    @Override
    public PageSet<CommonTaskDto> getTasksByUserNameId(PageParam pageParam, String userNameId) {
        Integer firstResults = (pageParam.getPage() - 1) * pageParam.getRows();
        Integer maxResults = pageParam.getPage() * pageParam.getRows();

        TaskService taskService = processEngine.getTaskService();
        RuntimeService runtimeService = processEngine.getRuntimeService();
        List<CommonTaskDto> commonTaskDtoList = new ArrayList<>();
        List<Task> taskList = taskService.createTaskQuery()
                .processVariableValueEquals("userNameId", userNameId)
                .orderByTaskCreateTime()
                .desc()
                .listPage(firstResults, maxResults);
        Long total = taskService.createTaskQuery().count();

        ProcessInstanceQuery processInstanceQuery = runtimeService.createProcessInstanceQuery();

        for (Task task : taskList) {
            CommonTaskDto commonTaskDto = new CommonTaskDto();
            commonTaskDto.setTaskId(task.getId());
            commonTaskDto.setExecutionId(task.getExecutionId());
            commonTaskDto.setTaskName(task.getName());

            //获得当前任务所在的流程实例
            ProcessInstance processInstance = processInstanceQuery.
                    processInstanceId(task.getProcessInstanceId()).
                    singleResult();

            //获得流程变量
            commonTaskDto.setVariables(taskService.getVariables(task.getId()));

            //获得到当前流程实例中的业务键（请假单ID），注入到业务Bean中
            commonTaskDto.setBusinessKey(processInstance.getBusinessKey());
            //String income = this.getTaskIncome(task.getId());
            //commonTaskDto.setIncomeing(income);
            commonTaskDto.setAssignee(task.getAssignee());

            commonTaskDtoList.add(commonTaskDto);
        }

        PageSet<CommonTaskDto> pageSet = new PageSet(total, commonTaskDtoList);
        return pageSet;
    }

    /**
     * 获得当前节点的指定来路
     *
     * @param taskId
     * @return
     */
    public String getTaskIncome(String taskId) {
        TaskService taskService = processEngine.getTaskService();
        HistoryService historyService = processEngine.getHistoryService();
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        //历史所走过的节点
        List<HistoricActivityInstance> historicActivityInstanceList = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(task.getProcessInstanceId())
                .finished()
                .orderByHistoricActivityInstanceEndTime()
                .desc()
                .list();
        HistoricActivityInstance hai = null;
        List<String> outcomeList = null;
        List<String> incomeList = null;
        if (historicActivityInstanceList.size() > 0) {
            hai = historicActivityInstanceList.get(0);
            outcomeList = this.getActivityOutcomes(hai);
        }
        incomeList = this.getTaskIncomesByTaskId(taskId);
        String income = null;
        for (String incomes : incomeList) {
            for (String outcomes : outcomeList) {
                if (incomes.equals(outcomes)) {
                    income = outcomes;
                    break;
                }
            }
        }
        return income;
    }

    public String getTaskIncomeByProcessDefinitionIdAndProcessInstanceId(String processDefinitionId, String executeId) {
        //历史所走过的节点
        List<HistoricActivityInstance> historicActivityInstanceList = historyService
                .createHistoricActivityInstanceQuery()
                //.processInstanceId(processInstanceId)
                .executionId(executeId)
                .finished()
                .orderByHistoricActivityInstanceEndTime()
                .desc()
                .list();
        HistoricActivityInstance hai = null;
        List<String> outcomeList = null;
        List<String> incomeList = null;
        if (historicActivityInstanceList.size() > 0) {
            hai = historicActivityInstanceList.get(0);
            outcomeList = this.getActivityOutcomes(hai);
        }
        incomeList = this.getTaskIncomesByDefinitionIdAndExecuteId(processDefinitionId, executeId);
        String income = null;
        for (String incomes : incomeList) {
            for (String outcomes : outcomeList) {
                if (incomes.equals(outcomes)) {
                    income = outcomes;
                    break;
                }
            }
        }
        return income;
    }

    /**
     * 获得当前节点所有来路
     *
     * @param taskId
     * @return
     */
    @Override
    public List<String> getTaskIncomesByTaskId(String taskId) {
        List<String> incomingList = new ArrayList<String>();
        //根据任务ID查询任务对象
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        //获得当前流程实例
        ProcessInstance processInstance = runtimeService
                .createProcessInstanceQuery()
                //.processDefinitionKey("leave")
                //.processDefinitionKey(task.getTaskDefinitionKey())
                .processInstanceId(task.getProcessInstanceId())
                .singleResult();
        //获得流程定义的实体对象
        ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) repositoryService.getProcessDefinition(task.getProcessDefinitionId());
        // 获得当前正在活动的节点ID
        /*String activityId = processInstance.getActivityId();
        Execution execution = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstance.getId()).singleResult();
        activityId = execution.getActivityId();*/

        Execution execution = runtimeService.createExecutionQuery().executionId(task.getExecutionId()).singleResult();
        String activityId = execution.getActivityId();
//        Execution execution = runtimeService.createProcessInstanceQuery()
//                .processInstanceId(processInstance.getId()).singleResult();
//        String  activityId = execution.getActivityId();

        //6.0.0的写法
        //BpmnModel bpmnModel = new BpmnModel();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        // 获得当前活动对应的节点信息及outgoingFlows信息
        FlowNode currentFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(activityId, true);
        //List<SequenceFlow> sequenceFlowList = currentFlowNode.getOutgoingFlows();
        List<SequenceFlow> sequenceFlowList = currentFlowNode.getIncomingFlows();
        // 遍历历史活动节点，找到匹配Flow目标节点的
        for (SequenceFlow sequenceFlow : sequenceFlowList) {
            String name = sequenceFlow.getName();
            incomingList.add(name);
        }

        //5.22.0的写法
        /*ActivityImpl ai = processDefinitionEntity.findActivity(activityId);
        List<PvmTransition> pvmTransitions = ai.getIncomingTransitions();
        for (PvmTransition pvmTransition : pvmTransitions) {
            String name = (String) pvmTransition.getProperty("name");
            incomingList.add(name);
        }*/
        return incomingList;
    }

    public List<String> getTaskIncomesByDefinitionIdAndExecuteId(String processDefinitionId, String executeId) {
        List<String> incomingList = new ArrayList<String>();
        // 获得当前正在活动的节点ID
        /*String activityId = processInstance.getActivityId();
        Execution execution = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstance.getId()).singleResult();
        activityId = execution.getActivityId();*/

        Execution execution = runtimeService.createExecutionQuery().executionId(executeId).singleResult();
        String activityId = execution.getActivityId();

        //6.0.0的写法
        //BpmnModel bpmnModel = new BpmnModel();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        // 获得当前活动对应的节点信息及outgoingFlows信息
        FlowNode currentFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(activityId, true);
        //List<SequenceFlow> sequenceFlowList = currentFlowNode.getOutgoingFlows();
        List<SequenceFlow> sequenceFlowList = currentFlowNode.getIncomingFlows();
        // 遍历历史活动节点，找到匹配Flow目标节点的
        for (SequenceFlow sequenceFlow : sequenceFlowList) {
            String name = sequenceFlow.getName();
            incomingList.add(name);
        }

        //5.22.0的写法
        /*ActivityImpl ai = processDefinitionEntity.findActivity(activityId);
        List<PvmTransition> pvmTransitions = ai.getIncomingTransitions();
        for (PvmTransition pvmTransition : pvmTransitions) {
            String name = (String) pvmTransition.getProperty("name");
            incomingList.add(name);
        }*/
        return incomingList;
    }

    /**
     * 获得当前节点所有出路
     *
     * @param taskId
     * @return
     */
    @Override
    public List<String> getTaskOutcomes(String taskId) {
        RuntimeService runtimeService = processEngine.getRuntimeService();
        TaskService taskService = processEngine.getTaskService();
        RepositoryService repositoryService = processEngine.getRepositoryService();

        List<String> outcomeList = new ArrayList<String>();
        // 根据任务ID查询任务对象
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        // 根据流程定义ID获得流程定义key
        String procDefKey = repositoryService.createProcessDefinitionQuery().processDefinitionId(task.getProcessDefinitionId()).singleResult().getKey();
        // 获得当前流程实例
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processDefinitionKey(procDefKey).
                processInstanceId(task.getProcessInstanceId()).singleResult();
        // 获得流程定义的实体对象
        ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) repositoryService.
                getProcessDefinition(task.getProcessDefinitionId());
        // 获得当前正在活动的节点ID
        //String activityId = processInstance.getActivityId();
        Execution execution = runtimeService.createExecutionQuery().executionId(task.getExecutionId()).singleResult();
        String activityId = execution.getActivityId();

        //6.0.0的写法
        //BpmnModel bpmnModel = new BpmnModel();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());

        // 获得当前活动对应的节点信息及outgoingFlows信息
        FlowNode currentFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(activityId, true);
        List<SequenceFlow> sequenceFlowList = currentFlowNode.getOutgoingFlows();
        // 遍历历史活动节点，找到匹配Flow目标节点的
        for (SequenceFlow sequenceFlow : sequenceFlowList) {
            String name = sequenceFlow.getName();
            outcomeList.add(name);
        }

        //5.22.0的写法
        /*ActivityImpl ai = processDefinitionEntity.findActivity(activityId);
        List<PvmTransition> pvmTransitions = ai.getOutgoingTransitions();
        for (PvmTransition pvmTransition : pvmTransitions) {
            String name = (String) pvmTransition.getProperty("name");
            outcomeList.add(name);
        }*/
        return outcomeList;
    }

    /**
     * 获得所有活动的出路（包括开始节点）
     * 传入的是历史的活动节点的实例
     *
     * @param hai
     * @return
     */
    @Override
    public List<String> getActivityOutcomes(HistoricActivityInstance hai) {
        RepositoryService repositoryService = processEngine.getRepositoryService();

        List<String> outcomeList = new ArrayList<String>();

        // 获得流程定义的实体对象
        ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) repositoryService.
                getProcessDefinition(hai.getProcessDefinitionId());
        // 获得当前正在活动的节点ID

        //6.0.0的写法
        //BpmnModel bpmnModel = new BpmnModel();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(hai.getProcessDefinitionId());
        // 获得当前活动对应的节点信息及outgoingFlows信息
        FlowNode currentFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(hai.getActivityId(), true);
        List<SequenceFlow> sequenceFlowList = currentFlowNode.getOutgoingFlows();
        // 遍历历史活动节点，找到匹配Flow目标节点的
        for (SequenceFlow sequenceFlow : sequenceFlowList) {
            String name = sequenceFlow.getName();
            outcomeList.add(name);
        }

        //5.22.0的写法
        /*ActivityImpl ai = processDefinitionEntity.findActivity(hai.getActivityId());
        List<PvmTransition> pvmTransitions = ai.getOutgoingTransitions();
        for (PvmTransition pvmTransition : pvmTransitions) {
            String name = (String) pvmTransition.getProperty("name");
            outcomeList.add(name);
        }*/
        return outcomeList;
    }

    /**
     * 获得任务的详细信息
     *
     * @param taskId
     * @param userNameId
     * @return
     */
    @Override
    public CommonTaskDto getTaskBeanById(String taskId, String userNameId) {
        TaskService taskService = processEngine.getTaskService();
        RuntimeService runtimeService = processEngine.getRuntimeService();

        CommonTaskDto commonTaskDto = new CommonTaskDto();
        //根据taskid查询task对象
        /*Task task = taskService.createTaskQuery()
                .processDefinitionKey("leave")
                .taskId(taskId).singleResult();
        ProcessInstanceQuery processInstanceQuery = runtimeService
                .createProcessInstanceQuery()
                .processDefinitionKey("leave");*/

        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        ProcessInstanceQuery processInstanceQuery = runtimeService.createProcessInstanceQuery();

        commonTaskDto.setTaskId(task.getId());
        commonTaskDto.setExecutionId(task.getExecutionId());
        commonTaskDto.setTaskName(task.getName());

        //获得当前任务所在的流程实例
        ProcessInstance processInstance = processInstanceQuery.
                processInstanceId(task.getProcessInstanceId()).
                singleResult();

        //获得到当前流程实例中的业务键（请假单ID），注入到业务Bean中
        commonTaskDto.setBusinessKey(processInstance.getBusinessKey());
        commonTaskDto.setAssignee(task.getAssignee());

        //获得当前任务所有要往出走的线
        List<String> outcomes = this.getTaskOutcomes(taskId);
        commonTaskDto.setOutcomeList(outcomes);
        return commonTaskDto;
    }

    @Override
    public List<CommentBean> getCommentListByTaskId(String taskId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (ObjectUtil.isEmpty(task)) {
            return null;
        }
        List<HistoricActivityInstance> haiList = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(task.getProcessInstanceId())
                .activityType("userTask")
                .orderByHistoricActivityInstanceEndTime()
                .asc()
                .list();
        List<CommentBean> commentBeans = new ArrayList<>();
        for (HistoricActivityInstance hai : haiList) {
            List<CommentVo> commList = apiFlowableTaskService.getTaskCommentsByTaskId(hai.getTaskId());

            for (int i = 0; i < commList.size(); i++) {
                if (!"event".equals(commList.get(i).getType())) {
                    CommentBean commentBean = new CommentBean();
                    commentBean.setActivityName(hai.getActivityName());
                    commList.get(i).setMessage(commList.get(i).getMessage());
                    commList.get(i).setType(FlowEnum.getEnumMsgByType(commList.get(i).getType()));
                    commentBean.setComment(commList.get(i));
                    commentBean.setTaskCreateTime(task.getCreateTime());
                    commentBeans.add(commentBean);
                }
            }
        }




        List<CommentVo> commList = apiFlowableTaskService.getTaskCommentsByProcessInstanceId(haiList.get(0).getProcessInstanceId());
        List<CommentBean> commentBeanList = new ArrayList<>();

        boolean found = false;
        for (CommentVo commLists : commList) {
            for (CommentBean queryBean : commentBeans) {
                if (queryBean.getComment().getId().equals(commLists.getId())) {
                    queryBean.setTime(commLists.getTime());
                    found = true;
                }
            }
            if (!found) {
                //  LogUtil.d("不包含：" + localBean.getExperimentName());
                CommentBean commentBean = new CommentBean();
                commLists.setMessage(commLists.getMessage());
                commLists.setType(FlowEnum.getEnumMsgByType(commLists.getType()));
                commentBean.setComment(commLists);
                SysUser user = businessSystemDataService.getSysUserByUserNameId(commLists.getUserId());
                commentBean.setUserName(null == user ? null : user.getUserName());
                CommentVo commentVos = apiFlowableTaskService.getActHiTaskinstById(commLists.getTaskId());
                if (ObjectUtil.isNotEmpty(commentVos)) {
                    commentBean.setActivityName(commentVos.getActivityName());
                    commentBean.setActivityId(commentVos.getActivityId());
                }
                commentBean.setTaskCreateTime(commLists.getTime());
                commentBean.setTime(commLists.getTime());
                commentBeanList.add(commentBean);
            }
            found = false;
        }

        commentBeans.addAll(commentBeanList);

        commentBeans = commentBeans.stream()
                .sorted(Comparator.comparing(CommentBean::getTime).reversed())
                .collect(Collectors.toList());

        return commentBeans;
    }


    @Autowired
    private ApiFlowableTaskService apiFlowableTaskService;


    @Override
    public List<CommentBean> getCommentListByProcessInstanceId(String processInstanceId) {
        List<HistoricActivityInstance> haiList = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(processInstanceId)
                .activityType("userTask")
                .orderByHistoricActivityInstanceEndTime()
                .asc()
                .list();
        List<CommentBean> commentBeans = new ArrayList<>();
        for (HistoricActivityInstance hai : haiList) {
            List<CommentVo> commList = apiFlowableTaskService.getTaskCommentsByTaskId(hai.getTaskId());
            for (int i = 0; i < commList.size(); i++) {
                CommentBean commentBean = new CommentBean();
                commentBean.setActivityName(hai.getActivityName());
                commList.get(i).setMessage(commList.get(i).getMessage());
                commList.get(i).setType(FlowEnum.getEnumMsgByType(commList.get(i).getType()));
                commentBean.setComment(commList.get(i));
                SysUser user = businessSystemDataService.getSysUserByUserNameId(commList.get(i).getUserId());
                commentBean.setUserName(ObjectUtil.isEmpty(user) ? null : user.getUserName());
                commentBean.setActivityId(hai.getActivityId());
                commentBean.setTaskCreateTime(commList.get(i).getTime());
                commentBean.setTime(commList.get(i).getTime());
                commentBeans.add(commentBean);
            }
        }
        List<CommentVo> commList = apiFlowableTaskService.getTaskCommentsByProcessInstanceId(processInstanceId);
        if (commList.size() == commentBeans.size()) {
            commentBeans = commentBeans.stream()
                    .sorted(Comparator.comparing(CommentBean::getTime).reversed())
                    .collect(Collectors.toList());
            return commentBeans;
        }

        List<CommentBean> commentBeanList = new ArrayList<>();

        boolean found = false;
        for (CommentVo commLists : commList) {
            for (CommentBean queryBean : commentBeans) {
                if (queryBean.getComment().getId().equals(commLists.getId())) {
                    queryBean.setTime(commLists.getTime());
                    found = true;
                }
            }
            if (!found) {
                //  LogUtil.d("不包含：" + localBean.getExperimentName());
                CommentBean commentBean = new CommentBean();
                commLists.setMessage(commLists.getMessage());
                commLists.setType(FlowEnum.getEnumMsgByType(commLists.getType()));
                commentBean.setComment(commLists);
                SysUser user = businessSystemDataService.getSysUserByUserNameId(commLists.getUserId());
                commentBean.setUserName(null == user ? null : user.getUserName());
                CommentVo commentVos = apiFlowableTaskService.getActHiTaskinstById(commLists.getTaskId());
                if (ObjectUtil.isNotEmpty(commentVos)) {
                    commentBean.setActivityName(commentVos.getActivityName());
                    commentBean.setActivityId(commentVos.getActivityId());
                }
                commentBean.setTaskCreateTime(commLists.getTime());
                commentBean.setTime(commLists.getTime());
                commentBeanList.add(commentBean);
            }
            found = false;
        }


        commentBeans.addAll(commentBeanList);
        commentBeans = commentBeans.stream()
                .sorted(Comparator.comparing(CommentBean::getTime).reversed())
                .collect(Collectors.toList());
//        commentBeans.stream()
//                .sorted(Comparator.comparing(CommentBean::getTaskCreateTime))
//                .collect(Collectors.toList());

        return commentBeans;
    }


    @Override
    public List<CommentBeanDto> getCommentListByProcessInstanceIds(String processInstanceId) {
        List<Comment> commentList = new ArrayList<>();
        List<ActHiActinst> listActinst = myVariableMapper.getAllByProcInstIdByActType(processInstanceId, "userTask");
        List<CommentBeanDto> commentBeans = new ArrayList<>();
        for (ActHiActinst hai : listActinst) {
            List<Comment> commList = taskService.getTaskComments(hai.getTaskId());

            for (int i = 0; i < commList.size(); i++) {
                CommentBeanDto commentBean = new CommentBeanDto();
                commentBean.setActivityName(hai.getActName());
                commentBean.setComment(commList.get(i));
                if (!StringUtils.isNullOrEmpty(String.valueOf(hai.getOpeningTime()))) {
                    commentBean.setOpeningTime(hai.getOpeningTime());
                }

                SysUser user = businessSystemDataService.getSysUserByUserNameId(commList.get(i).getUserId());
                commentBean.setUserName(user.getUserName());

                commentBeans.add(commentBean);
            }

            //commentList.addAll(commList);
        }

        return commentBeans;
    }


    @Override
    public List<CommentBean> getCommentBeanByTaskId(String taskId) {
        List<CommentBean> commentList = this.getCommentListByTaskId(taskId);
        List<CommentBean> commentBeanList = new ArrayList<>();
        if (CollUtil.isNotEmpty(commentList)) {
            for (CommentBean comm : commentList) {
                CommentBean commentBean = new CommentBean();
                String userNameId = comm.getComment().getUserId();
                SysUser user = businessSystemDataService.getSysUserByUserNameId(userNameId);
                commentBean.setComment(comm.getComment());
                commentBean.setTaskCreateTime(comm.getTaskCreateTime());
                commentBean.setTime(comm.getTime());
                commentBean.setActivityName(comm.getActivityName());
                commentBean.setUserName(ObjectUtil.isEmpty(user) ? null :
                        user.getUserName()+ "(" + user.getUserNameId() + "）");
                commentBeanList.add(commentBean);
            }
        }
        return commentBeanList;
    }


    public static String dateDiff(String startTime, String endTime) {
        // 按照传入的格式生成一个simpledateformate对象
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long nd = 1000 * 24 * 60 * 60;// 一天的毫秒数
        long nh = 1000 * 60 * 60;// 一小时的毫秒数
        long nm = 1000 * 60;// 一分钟的毫秒数
        long ns = 1000;// 一秒钟的毫秒数
        long diff;
        long day = 0;
        long hour = 0;
        long min = 0;
        long sec = 0;
        // 获得两个时间的毫秒时间差异
        try {
            diff = sd.parse(endTime).getTime() - sd.parse(startTime).getTime();
            day = diff / nd;// 计算差多少天
            hour = diff % nd / nh + day * 24;// 计算差多少小时
            min = diff % nd % nh / nm + day * 24 * 60;// 计算差多少分钟
            sec = diff % nd % nh % nm / ns;// 计算差多少秒
            // 输出结果

            return "时间相差：" + day + "天" + (hour - day * 24) + "小时"
                    + (min - day * 24 * 60) + "分钟" + sec + "秒。";
        } catch (ParseException e) {
            //  Auto-generated catch block
            e.printStackTrace();
            return null;
        }

    }







    /*@Override
    public List<ProcessDefinition> getDeployedFlows() {
        RepositoryService repositoryService = processEngine.getRepositoryService();
        List<ProcessDefinition> list = repositoryService.createProcessDefinitionQuery().list();
        return list;
    }

    @Override
    public InputStream getDiagramByProcessInstanceId(String processInstanceId) {
        //获得流程实例
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId).singleResult();
        String processDefinitionId = StringUtils.EMPTY;
        if (processInstance == null) {
            //查询已经结束的流程实例
            HistoricProcessInstance processInstanceHistory =
                    historyService.createHistoricProcessInstanceQuery()
                            .processInstanceId(processInstanceId).singleResult();
            if (processInstanceHistory == null)
                return null;
            else
                processDefinitionId = processInstanceHistory.getProcessDefinitionId();
        } else {
            processDefinitionId = processInstance.getProcessDefinitionId();
        }

        //使用宋体
        String fontName = "宋体";
        //获取BPMN模型对象
        BpmnModel model = repositoryService.getBpmnModel(processDefinitionId);
        //获取流程实例当前的节点，需要高亮显示
        List<String> currentActs = Collections.EMPTY_LIST;
        if (processInstance != null)
            currentActs = runtimeService.getActiveActivityIds(processInstance.getId());

        return processEngine.getProcessEngineConfiguration()
                .getProcessDiagramGenerator()
                .generateDiagram(model, "png", currentActs, new ArrayList<String>(),
                        fontName, fontName, fontName, null, 1.0);
    }

    @Override
    public InputStream getDiagramByProcessDefinitionId(String processDefinitionId) {
        //使用宋体
        String fontName = "宋体";
        //获取BPMN模型对象
        BpmnModel model = repositoryService.getBpmnModel(processDefinitionId);

        return processEngine.getProcessEngineConfiguration()
                .getProcessDiagramGenerator()
                .generateDiagram(model, "png", fontName, fontName, fontName, null, 1.0);
    }

    @Override
    public List<CommonTaskDto> getVacationApplyList(List<String> processInstanceIds) {

        List<Task> taskList = taskService.createTaskQuery().processInstanceIdIn(processInstanceIds).list();

        List<CommonTaskDto> commonTaskDtoList = new ArrayList<>();

        ProcessInstanceQuery processInstanceQuery = runtimeService.createProcessInstanceQuery();
        for (Task task : taskList) {
            CommonTaskDto commonTaskDto = new CommonTaskDto();
            commonTaskDto.setTaskId(task.getId());
            commonTaskDto.setExecutionId(task.getExecutionId());
            commonTaskDto.setTaskName(task.getName());

            //获得当前任务所在的流程实例
            ProcessInstance processInstance = processInstanceQuery.
                    processInstanceId(task.getProcessInstanceId()).
                    singleResult();

            //获得流程变量
            //String businessTitle = taskService.getVariable(task.getId(), "businessTitle").toString();
            //String businessDetailUrl = taskService.getVariable(task.getId(), "businessDetailUrl").toString();
            Map<String, Object> variables = taskService.getVariables(task.getId());
            commonTaskDto.setVariables(variables);

            //获得到当前流程实例中的业务键（请假单ID），注入到业务Bean中
            commonTaskDto.setBusinessKey(processInstance.getBusinessKey());
            //String income = this.getTaskIncome(task.getId());
            //commonTaskDto.setIncomeing(income);
            commonTaskDto.setAssignee(task.getAssignee());
            commonTaskDtoList.add(commonTaskDto);
        }
        return commonTaskDtoList;
    }*/


    @Override
    public Object coome(String taskId, String[] userNameId, String comment, String name, String instanceId) {
        switch (name) {
            case "委派":
                return weipai(taskId, userNameId[0], comment, instanceId);
            case "转办":
                return zhuanban(taskId, userNameId[0], comment, instanceId);
            case "前加签":
                return jiaqian(false, taskId, userNameId, comment, instanceId);
            case "后加签":
                return jiaqian(true, taskId, userNameId, comment, instanceId);
            default:
                System.out.println("default");
                break;
        }

        return null;
    }


    //转办
    private Object zhuanban(String taskId, String userNameId, String comment, String instanceId) {
        TaskEntityImpl currTask = (TaskEntityImpl) taskService.createTaskQuery().taskId(taskId).singleResult();
        if (currTask != null) {
            //1.生成历史记录
            TaskEntity task = createSubTask(currTask, businessSystemDataService.getUserNameId());
            managementService.executeCommand(new AddHisCommentCmd(taskId, businessSystemDataService.getUserNameId(), instanceId,
                    "comment", comment));
            taskService.complete(task.getId());
            //3.转办
            taskService.setAssignee(taskId, userNameId);
            taskService.setOwner(taskId, businessSystemDataService.getUserNameId());
            //3.转办
            return JsonUtils.messageJson(200, Constants.OPERATE_TIPS, "转办成功");
        } else {
            return JsonUtils.messageJson(300, Constants.OPERATE_TIPS, "转办失败");

        }
    }

    //委派
    private Object weipai(String taskId, String userNameId, String comment, String instanceId) {
        TaskEntityImpl currTask = (TaskEntityImpl) taskService.createTaskQuery().taskId(taskId).singleResult();
        if (currTask != null) {
            managementService.executeCommand(new AddHisCommentCmd(taskId, businessSystemDataService.getUserNameId(), instanceId,
                    Convert.toStr(FlowEnum.WP), comment));
            //2.设置审批人就是当前登录人
            taskService.setAssignee(taskId, businessSystemDataService.getUserNameId());
            //3.执行委派
            taskService.delegateTask(taskId, userNameId);
            return JsonUtils.messageJson(200, Constants.OPERATE_TIPS, "委派成功");
        } else {
            return JsonUtils.messageJson(300, Constants.OPERATE_TIPS, "委派失败");
        }
    }

    //加签
    public Object jiaqian(Boolean flag, String taskId, String[] userNameId, String comment, String instanceId) {
        TaskEntityImpl taskEntity = (TaskEntityImpl) taskService.createTaskQuery().taskId(taskId).singleResult();
        //把当前的节点设置为空
        if (ObjectUtil.isEmpty(taskEntity)) {
            //如果是加签再加签
            String parentTaskId = taskEntity.getParentTaskId();
            if (StrUtil.isBlank(parentTaskId)) {
                taskEntity.setOwner(businessSystemDataService.getUserNameId());
                taskEntity.setAssignee(null);
                taskEntity.setCountEnabled(true);
                if (flag) {
                    taskEntity.setScopeType("after");
                } else {
                    taskEntity.setScopeType("before");
                }
                //1.2 设置任务为空执行者
                taskService.saveTask(taskEntity);
            }
            //2.添加加签数据
            createSignSubTasks(userNameId, taskEntity);
            //3.添加审批意见
            String type = flag ? "后加签" : "前加签";

            managementService.executeCommand(new AddHisCommentCmd(taskId, businessSystemDataService.getUserNameId(), taskEntity.getProcessInstanceId(), type
                    , comment));
            return JsonUtils.messageJson(200, "操作提示", flag ? ("后加签成功!") : ("前加签成功"));

        } else {
            return JsonUtils.messageJson(300, "操作提示", "任务不存在");
        }
    }

    private TaskEntity createSignSubTasks(String[] userNameId, TaskEntity taskEntity) {
        if (userNameId.length > 0) {
            String parentTaskId = taskEntity.getParentTaskId();
            if (org.apache.commons.lang.StringUtils.isBlank(parentTaskId)) {
                parentTaskId = taskEntity.getId();
            }
            String finalParentTaskId = parentTaskId;
            //1.创建被加签人的任务列表
            Arrays.asList(userNameId).forEach(userCode -> {
                if (org.apache.commons.lang.StringUtils.isNotBlank(userCode)) {
                    createSubTask((TaskEntityImpl) taskEntity, userCode);
                }
            });
            String taskId = taskEntity.getId();
            if (org.apache.commons.lang.StringUtils.isBlank(taskEntity.getParentTaskId())) {
                //2.创建加签人的任务并执行完毕
                Task task = createSubTask((TaskEntityImpl) taskEntity, businessSystemDataService.getUserNameId());
                taskId = task.getId();
            }
            Task taskInfo = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (null != taskInfo) {
                taskService.complete(taskId);
            }
            //如果是候选人，需要删除运行时候选表种的数据。
            long candidateCount = taskService.createTaskQuery().taskId(parentTaskId).taskCandidateUser(businessSystemDataService.getUserNameId()).count();
            if (candidateCount > 0) {
                taskService.deleteCandidateUser(parentTaskId, businessSystemDataService.getUserNameId());
            }
        }
        return null;
    }

    @Override
    public String getProcDefIdBykey(String key) {
        //获取最新流程定义Id
        List<ProcessDefinition> listAll = processEngine.getRepositoryService() // 获取service
                .createProcessDefinitionQuery() // 创建流程定义查询
                .orderByProcessDefinitionVersion().asc() // 根据流程定义版本升序
                .list();  // 返回一个集合
        Map<String, ProcessDefinition> map1 = new LinkedHashMap<String, ProcessDefinition>();
        for (ProcessDefinition pd : listAll) {
            map1.put(pd.getKey(), pd);
        }
        String procDefId = "";
        List<ProcessDefinition> pdList = new LinkedList<ProcessDefinition>(map1.values());
        for (ProcessDefinition pd : pdList) {
            if (pd.getKey().equals(key)) {
                procDefId = pd.getId();
                break;
            }
        }
        return procDefId;

    }

    private IdentityService identityService;

    @Override
    public Map<String, Object> startFlowable(String uuid, String userNameId, String key, String reason) {
        //获取最新的流程定义id
        String procDefId = getProcDefIdBykey(key);
        //设置流程发起人ID，可通过startedBy查询的值
        identityService.setAuthenticatedUserId(userNameId);
        //启动流程实例
        ProcessInstance processInstance = runtimeService.startProcessInstanceById(procDefId, uuid);
        Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
        //默认处理第一步
        Authentication.setAuthenticatedUserId(businessSystemDataService.getUserNameId());
        String taskId = task.getId();
        List varNames = getVarsNameByProcessDefinitionId(Convert.toStr(procDefId));
        Map<String, Object> procVars = new HashMap<>();
        for (int i = 0; i < varNames.size(); i++) {
            String varName = Convert.toStr(varNames.get(i));
            procVars.put(varName, "同意");
        }
        taskService.addComment(taskId, task.getProcessInstanceId(), reason);
        taskService.complete(taskId, procVars);

        //1.历史任务表中没有处理人导致无法驳回到申请人 强行更新act_hi_taskinst表中处理人为登录人
        Integer integer = myVariableService.updateAssigneeById(businessSystemDataService.getUserNameId(), taskId);
        return null;
    }

    @Override
    public TaskCommon getDetailsByKey(String id) {
        return myVariableMapper.getDetailsByKey(id);
    }

    @Override
    public int updateTaskCommonOpeningTimeByKey(String id, Date date) {
        return myVariableMapper.updateTaskCommonOpeningTimeByKey(id, date);
    }

    @Override
    public int updateActHiActinst(String taskId, Date date) {
        return myVariableMapper.updateActHiActinst(taskId, date);
    }

    @Override
    public Map<String, Object> getActRuTaskByProcInstId(String procInstId) {
        return myVariableMapper.getActRuTaskByProcInstId(procInstId);
    }

    @Override
    public List<Map<String, Object>> getActHiActinstByProcInstId(String procInstId, String actType) {
        return myVariableMapper.getActHiActinstByProcInstId(procInstId, actType);
    }

    @Override
    public int delectActRuTaskByKey(String id) {
        return myVariableMapper.delectActRuTaskByKey(id);
    }

    @Override
    public int addActRuTask(Map<String, Object> map) {
        return myVariableMapper.addActRuTask(map);
    }

    @Override
    public List<ActHiActinst> getAllByProcInstIdByActType(String procInstId, String actType) {
        return myVariableMapper.getAllByProcInstIdByActType(procInstId, actType);
    }

    @Override
    public List<CommentBeanDto> getCommentListViewStepsByTaskIdInformation(String taskId) {
        List<Comment> commentList = new ArrayList<>();
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        List<ActHiActinst> listActinst = myVariableMapper.getAllByProcInstIdByActType(task.getProcessInstanceId(), "userTask");
        List<CommentBeanDto> commentBeans = new ArrayList<>();
        for (ActHiActinst hai : listActinst) {
            List<Comment> commList = taskService.getTaskComments(hai.getTaskId());
            for (int i = 0; i < commList.size(); i++) {
                CommentBeanDto commentBean = new CommentBeanDto();
                commentBean.setActivityName(hai.getActName());
                commentBean.setComment(commList.get(i));
                if (!StringUtils.isNullOrEmpty(String.valueOf(hai.getOpeningTime()))) {
                    commentBean.setOpeningTime(hai.getOpeningTime());
                }
                commentBeans.add(commentBean);
            }
        }
        CommentBeanDto commentBean = new CommentBeanDto();
        commentBean.setActivityName(listActinst.get(0).getActName());
        if (!StringUtils.isNullOrEmpty(String.valueOf(listActinst.get(0).getOpeningTime()))) {
            commentBean.setOpeningTime(listActinst.get(0).getOpeningTime());
        }
        commentBeans.add(commentBean);

        return commentBeans;
    }

    @Override
    public int updateBusinessFlowStateByUuidAndTableNameStr(String uuid, String flowState, String tableName) {
        return myVariableMapper.updateBusinessFlowStateByUuidAndTableNameStr(uuid, flowState, tableName);
    }

    @Override
    public List<CommentBeanDto> getCommentListViewStepsByTaskId(String taskId) {
        List<CommentBeanDto> commentList = this.getCommentListViewStepsByTaskIdInformation(taskId);
        List<CommentBeanDto> commentBeanList = new ArrayList<>();
        if (commentList.size() > 0) {
            for (CommentBeanDto comm : commentList) {
                CommentBeanDto commentBean = new CommentBeanDto();
                String userNameId = "";
                if (comm.getComment() != null) {
                    userNameId = comm.getComment().getUserId();
                    commentBean.setComment(comm.getComment());
                    SysUser user = businessSystemDataService.getSysUserByUserNameId(userNameId);
                    commentBean.setUserName(user.getUserName());
                }
                commentBean.setActivityName(comm.getActivityName());
                if (!StringUtils.isNullOrEmpty(String.valueOf(comm.getOpeningTime()))) {
                    commentBean.setOpeningTime(comm.getOpeningTime());
                }
                commentBeanList.add(commentBean);
            }
        }
        return commentBeanList;
    }
    public TaskEntity createSubTask(TaskEntityImpl ptask, String userNameId) {
        TaskEntity task = null;
        if (ptask != null) {
            //1.生成子任务
            task = (TaskEntity) taskService.newTask(RandomUtil.randomString(32));
            task.setCategory(ptask.getCategory());
            task.setDescription(ptask.getDescription());
            task.setTenantId(ptask.getTenantId());
            task.setAssignee(userNameId);
            task.setName(ptask.getName());
            task.setParentTaskId(ptask.getId());
            task.setProcessDefinitionId(ptask.getProcessDefinitionId());
            task.setProcessInstanceId(ptask.getProcessInstanceId());
            task.setTaskDefinitionKey(ptask.getTaskDefinitionKey());
            task.setTaskDefinitionId(ptask.getTaskDefinitionId());
            task.setPriority(ptask.getPriority());
            task.setCreateTime(new Date());
            taskService.saveTask(task);
        }
        return task;
    }
}
