package com.lms.common.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.lms.common.dao.FileRepository;
import com.lms.common.model.File;
import com.lms.common.feign.dto.FileDTO;
import com.lms.common.util.BeanUtils;
import com.lms.common.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * description: ES文件服务
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2023-02-21
 */
@Slf4j
@Service
public class FileService {

    @Autowired(required = false)
    private FileRepository fileRepository;

    @Autowired(required = false)
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Autowired(required = false)
    private RestHighLevelClient restHighLevelClient;

    @Value("${spring.elasticsearch.enabled:false}")
    private boolean elasticsearchEnabled;

    /**
     * 关键字查询 - 支持ES和数据库两种方式
     *
     * @return
     */
    public List<SearchHit<File>> search(FileDTO dto) {
        if (!elasticsearchEnabled || fileRepository == null) {
            log.info("使用数据库搜索替代方案，关键词: {}", dto.getKeyword());
            return searchWithDatabase(dto);
        }
        log.info("使用Elasticsearch搜索，关键词: {}", dto.getKeyword());
        Pageable pageable = PageRequest.of(dto.getPageNo() - 1, dto.getPageSize(), Sort.Direction.DESC, "createTime");
        return fileRepository.findByFileNameOrContent(dto.getKeyword(), dto.getKeyword(), pageable);
    }

    /**
     * 数据库搜索替代方案
     */
    private List<SearchHit<File>> searchWithDatabase(FileDTO dto) {
        log.warn("数据库搜索功能待实现，当前返回空结果");
        return new ArrayList<>();
    }


    /*public SearchHits<File> searchPage(FileDTO dto) {
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        queryBuilder.withQuery(QueryBuilders.multiMatchQuery(dto.getKeyword(), "fileName", "fileContent"));
        // 设置高亮
        HighlightBuilder highlightBuilder = new HighlightBuilder();
        String[] fieldNames = {"fileName", "fileContent"};
        for (String fieldName : fieldNames) {
            highlightBuilder.field(fieldName);
        }
        highlightBuilder.preTags("<span style='color:red'>");
        highlightBuilder.postTags("</span>");
        highlightBuilder.order();
        queryBuilder.withHighlightBuilder(highlightBuilder);

        // 添加分页和排序
        queryBuilder.withSorts(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                .withPageable(PageRequest.of(dto.getPageNo() - 1, dto.getPageSize()));

        NativeSearchQuery nativeSearchQuery = queryBuilder.build();

        return elasticsearchRestTemplate.search(nativeSearchQuery, File.class);
    }*/


    public SearchHits<File> searchPage(FileDTO dto) {
        // 对查询的数据密级做过滤,如果给定的slevel在所有的文档数据中都不存在会导致查不出任何数据
        Integer slevel = ContextUtil.getSlevel();
        if (null == slevel) {
            slevel = 20;
        }
        if (slevel > 80) {
            slevel = 80;
        }
        BoolQueryBuilder should = QueryBuilders.boolQuery();
        should.should(QueryBuilders.matchPhraseQuery("fileName", dto.getKeyword()))
                .should(QueryBuilders.matchPhraseQuery("attachment.content", dto.getKeyword()));
        BoolQueryBuilder must = QueryBuilders.boolQuery();
        must.must(should);
        must.must(QueryBuilders.rangeQuery("mlevel").lte(slevel));
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        queryBuilder.withQuery(must);
        // 设置高亮
        HighlightBuilder highlightBuilder = new HighlightBuilder();
        String[] fieldNames = {"fileName", "attachment.content"};
        for (String fieldName : fieldNames) {
            highlightBuilder.field(fieldName);
        }
        highlightBuilder.preTags("<span style='color:red'>");
        highlightBuilder.postTags("</span>");
        highlightBuilder.order();
        queryBuilder.withHighlightBuilder(highlightBuilder);

        // 添加分页和排序
        queryBuilder.withSorts(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                .withPageable(PageRequest.of(dto.getPageNo() - 1, dto.getPageSize()));

        NativeSearchQuery nativeSearchQuery = queryBuilder.build();
        return elasticsearchRestTemplate.search(nativeSearchQuery, File.class);
    }


    public void save(File model) throws IOException {
        if (!elasticsearchEnabled || restHighLevelClient == null) {
            log.info("Elasticsearch已禁用，跳过文件索引保存: {}", model.getFileName());
            return;
        }
        Object id = BeanUtils.getValueByName(model, "id");
        if (id != null && id.equals("")) BeanUtils.setValueByName(model, "id", null);
        IndexRequest indexRequest = new IndexRequest().index("file")
                .id(String.valueOf(id))
                .source(JSON.toJSONString(model), XContentType.JSON)
                .setPipeline("attachment")
                .timeout(TimeValue.timeValueMinutes(10));
        restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
        log.info("文件已保存到Elasticsearch: {}", model.getFileName());
    }

    public List<File> saveAll(List<File> models) throws IOException {
        if (!elasticsearchEnabled || restHighLevelClient == null) {
            log.info("Elasticsearch已禁用，跳过批量文件索引保存，文件数量: {}",
                    ObjectUtil.isNotEmpty(models) ? models.size() : 0);
            return models;
        }
        if (ObjectUtil.isNotEmpty(models)) {
            for (File model : models) {
                Object id = BeanUtils.getValueByName(model, "id");
                if (id != null && id.equals("")) BeanUtils.setValueByName(model, "id", null);
                IndexRequest indexRequest = new IndexRequest().index("file")
                        .id(String.valueOf(id))
                        .source(JSON.toJSONString(model), XContentType.JSON)
                        .setPipeline("attachment")
                        .timeout(TimeValue.timeValueMinutes(10));
                restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            }
            log.info("批量文件已保存到Elasticsearch，数量: {}", models.size());
        }
        return models;
    }

    public void delete(String id) throws IOException {
        if (StringUtils.isEmpty(id)){
            return;
        }
        if (!elasticsearchEnabled || restHighLevelClient == null) {
            log.info("Elasticsearch已禁用，跳过文件索引删除: {}", id);
            return;
        }
        DeleteRequest deleteRequest = new DeleteRequest("file", id);
        deleteRequest.timeout(TimeValue.timeValueMinutes(10));
        log.info("delete doc request id : {}", id);
        DeleteResponse deleteResponse = restHighLevelClient.delete(deleteRequest, RequestOptions.DEFAULT);
        log.info("delete doc response : {}", JSON.toJSONString(deleteResponse));
    }


    public void batchDelete(List ids) throws IOException {
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        if (!elasticsearchEnabled || restHighLevelClient == null) {
            log.info("Elasticsearch已禁用，跳过批量文件索引删除，数量: {}", ids.size());
            return;
        }
        BulkRequest bulkRequest = new BulkRequest();
        for (Object id : ids) {
            bulkRequest.add(new DeleteRequest("file", String.valueOf(id)));
        }
        log.info("batchDelete doc request ids : {}", JSON.toJSONString(ids));
        BulkResponse response = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
        log.info("batchDelete doc response : {}", JSON.toJSONString(response));
    }

    public File get(String id) {
        if (!elasticsearchEnabled || fileRepository == null) {
            log.info("Elasticsearch已禁用，无法获取文件: {}", id);
            return null;
        }
        return fileRepository.findById(id).orElse(null);
    }

    public void deleteAll() {
        if (!elasticsearchEnabled || fileRepository == null) {
            log.info("Elasticsearch已禁用，跳过删除所有文件索引");
            return;
        }
        fileRepository.deleteAll();
    }
}
