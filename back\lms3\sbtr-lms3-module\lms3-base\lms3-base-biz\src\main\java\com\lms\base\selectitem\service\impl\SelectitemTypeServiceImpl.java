/**
 * FileName:	SelectitemService.java
 * Author:		z<PERSON>weirong
 * Time:		2017-11-22上午11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.base.selectitem.service.impl;

import java.sql.SQLException;
import java.util.List;

import com.lms.base.feign.model.SelectitemType;
import com.lms.base.selectitem.service.SelectitemTypeService;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.DBSqlUtil;
import org.springframework.stereotype.Service;

import com.lms.base.selectitem.mapper.SelectitemTypeMapper;

import javax.annotation.Resource;

/**
 * 保存项目的相关信息
 */
@Service("SelectitemTypeService")
public class SelectitemTypeServiceImpl extends BaseServiceImpl<SelectitemTypeMapper, SelectitemType> implements SelectitemTypeService {

    @Resource
    private SelectitemTypeMapper selectitemTypeMapper;
    @Resource
    private DBSqlUtil dbSqlUtil;

    public List<SelectitemType> getAllSelectitemTypes() {
        return selectitemTypeMapper.getAllSelectitemTypes();
    }

    public int getMaxSeqno() throws SQLException {
        int seqno = 0;
        String hql = "select "
                + dbSqlUtil.getIfNullFlag()
                + "(max(k.seqno),0) from b_selectitemtype k where k.status=1";
        seqno = selectitemTypeMapper.getMaxSeqno(hql);
        return seqno;
    }

    public void batchDeleteSelectitemType(List<String> ids) {
        this.removeBatchByIds(ids);
    }

}
