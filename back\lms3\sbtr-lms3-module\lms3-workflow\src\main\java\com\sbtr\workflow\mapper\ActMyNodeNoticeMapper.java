package com.sbtr.workflow.mapper;
import com.sbtr.workflow.model.ActMyNodeNotice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程每个节点所对应通知
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-12-09 11:58:35
 */
public interface ActMyNodeNoticeMapper extends tk.mybatis.mapper.common.Mapper<ActMyNodeNotice> {

    //获取分页集
    List<ActMyNodeNotice> getPageSet(@Param("filterSort") String filterSort);

   int executeDeleteBatch(Object[] var1);

    Integer insertList(@Param("list") List<ActMyNodeNotice> list);

    List<ActMyNodeNotice> getListByNodeIdAndModelKeyAndProcdefId(@Param("nodelId")String nodelId, @Param("modelKey") String modelKey, @Param("procdefId")String procdefId);

    List<ActMyNodeNotice> getListByActDeModelKeyAndProcdefId(@Param("key")String key, @Param("procdefIds") String procdefIds);

    Integer updateProcdefIdByModelKey(@Param("key") String key,@Param("procdefId") String procdefId);

    Integer updateProcdefIdIsNull(@Param("key")String key, @Param("id")String id);

    Integer deleteByModelKeyAnProcdefId(@Param("key")String key, @Param("id")String id);

    Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id);
}
