package com.sbtr.workflow.mapper;
import com.sbtr.workflow.model.ActMyModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程表单模型;
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-21 02:37:50
 */
public interface ActMyModelMapper extends tk.mybatis.mapper.common.Mapper<ActMyModel> {

    //获取分页集
    List<ActMyModel> getPageSet(@Param("filterSort") String filterSort);

   int executeDeleteBatch(Object[] var1);


    int updateFormDesign(@Param("uuid") String uuid, @Param("formDesign") String formDesign,@Param("formModel")  String formModel);

    Integer updateFormDesignByActDeModelKey(@Param("actDeModelKey")String actDeModelKey, @Param("formDesign")String formDesign, @Param("formModel")String formModel);

    Integer updateFormFieldJson(@Param("formTableName")String formTableName,@Param("formJson") String formJson, @Param("formModel")String formModel);

    List<ActMyModel> getListByModelKeyAndProcdefIdIsNull(@Param("key") String key);

    List<ActMyModel> getListByActDeModelKeyAndProcdefId(@Param("key") String key, @Param("procdefIds") String procdefIds);

    Integer updateProcdefIdIsNull(@Param("key") String key,@Param("id")  String id);

    Integer selectCountInteger(@Param("key")String key);

    Integer deleteByModelKeyAnProcdefId(@Param("key")String key, @Param("id")String id);

    Integer updateActReProcdefNameByKey(@Param("modelKey") String modelKey,@Param("majorVersion") String majorVersion, @Param("actDeModelName") String actDeModelName);

    String getDesignJson(@Param("formUuid") String formUuid);

    List<ActMyModel> getMajorModelByKey(String modelKey);

    Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id);
}
