package com.lms.system.feign.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import com.lms.common.model.SearchContent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@TableName("b_lmslog")
@Data
@ApiModel(value = "操作日志对象")
public class Log extends BaseModel {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "日志唯一标识")
    private String id;

    @Excel(name = "对象名称", width = 30, orderNum = "0")
    @ApiModelProperty(value = "对象名称")
    private String objname;

    @Excel(name = "操作描述", width = 30, orderNum = "6")
    @ApiModelProperty(value = "日志描述")
    private String description;

    @Excel(name = "日志类型", width = 15, orderNum = "4")
    @ApiModelProperty(value = "日志类型")
    private String logtype;

    @Excel(name = "操作日期", width = 15, orderNum = "2")
    @ApiModelProperty(value = "操作日期")
    private String submitdate;

    @Excel(name = "操作时间", width = 15, orderNum = "3")
    @ApiModelProperty(value = "操作时间")
    private String submittime;

    @Excel(name = "IP地址", width = 15, orderNum = "5")
    @ApiModelProperty(value = "IP地址")
    private String submitip;

    @Excel(name = "操作者", width = 15, orderNum = "1")
    @ApiModelProperty(value = "操作者账号")
    private String submitor;

    @Excel(name = "操作结果", width = 15, orderNum = "7")
    @ApiModelProperty(value = "操作结果")
    @SearchContent
    private String result;

    @ApiModelProperty(value = "操作者id")
    private String logobj;

    @ApiModelProperty(value = "操作者所属角色")
    private String role;

    @ApiModelProperty(value = "操作用户类型")
    private Integer usertype;

}
