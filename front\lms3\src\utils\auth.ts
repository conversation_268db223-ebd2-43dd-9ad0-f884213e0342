import Cookies from "js-cookie";
import { useUserStoreHook } from "@/store/modules/user";
import { storageLocal, isString, isIncludeAllChildren } from "@pureadmin/utils";

export interface DataInfo<T> {
  /** token */
  accessToken: string;
  /** `accessToken`的过期时间（时间戳） */
  expires: T;
  /** 用于调用刷新accessToken的接口时所需的token */
  refreshToken: string;
  /** 头像 */
  avatar?: string;
  /** 用户名 */
  username?: string;
  /** 昵称 */
  nickname?: string;
  /** 当前登录用户的角色 */
  roles?: Array<string>;
  /** 当前登录用户的按钮级别权限 */
  permissions?: Array<string>;
}

export interface LmsJwtInfo<T> {
  /** token */
  result: string;
  /** `accessToken`的过期时间（时间戳） */
  timestamp: T;
}

export const userKey = "user-info";
// export const TokenKey = "authorized-token";
export const TokenKey = "token";
export const expiresKey = "expires";
/**
 * 通过`multiple-tabs`是否在`cookie`中，判断用户是否已经登录系统，
 * 从而支持多标签页打开已经登录的系统后无需再登录。
 * 浏览器完全关闭后`multiple-tabs`将自动从`cookie`中销毁，
 * 再次打开浏览器需要重新登录系统
 * */
export const multipleTabsKey = "multiple-tabs";

/** 获取`token` */
export function getToken(): string | null {
  // 首先尝试从cookie获取token
  const cookieToken = Cookies.get(TokenKey);
  if (cookieToken) {
    try {
      return JSON.parse(cookieToken);
    } catch {
      return cookieToken; // 如果不是JSON格式，直接返回
    }
  }

  // 如果cookie中没有，尝试从localStorage获取
  const localToken = storageLocal().getItem<DataInfo<number>>(userKey);
  return localToken?.accessToken || null;
}

export function setToken(data: LmsJwtInfo<any>) {
  let expires = 0;
  const { result, timestamp } = data;
  expires = new Date(timestamp).getTime();

  // 存储token到cookie
  Cookies.set(TokenKey, JSON.stringify(result));

  // 存储过期时间
  expires > 0
    ? Cookies.set(expiresKey, JSON.stringify(expires), {
        expires: (expires - Date.now()) / 86400000
      })
    : Cookies.set(expiresKey, JSON.stringify(expires));

  // 设置multiple-tabs标识
  const { isRemembered, loginDay } = useUserStoreHook();
  Cookies.set(
    multipleTabsKey,
    "true",
    isRemembered
      ? {
          expires: loginDay
        }
      : {}
  );

  // 存储用户信息到localStorage
  const userInfo = {
    accessToken: result,
    expires: expires,
    refreshToken: "", // 如果没有refreshToken，设为空字符串
    avatar: "",
    username: "admin", // 默认用户名
    nickname: "管理员", // 默认昵称
    roles: ["admin"], // 默认角色
    permissions: ["*:*:*"] // 默认权限
  };

  storageLocal().setItem(userKey, userInfo);

  // 更新store中的用户信息
  useUserStoreHook().SET_USERNAME(userInfo.username);
  // useUserStoreHook().SET_NICKNAME(userInfo.nickname);
  // useUserStoreHook().SET_ROLES(userInfo.roles);
  // useUserStoreHook().SET_PERMS(userInfo.permissions);
}

/** 删除`token`以及key值为`user-info`的localStorage信息 */
export function removeToken() {
  Cookies.remove(TokenKey);
  Cookies.remove(multipleTabsKey);
  storageLocal().removeItem(userKey);
}

/** 格式化token（jwt格式） */
export const formatToken = (token: string): string => {
  return "Bearer " + token;
};

/** 是否有按钮级别的权限（根据登录接口返回的`permissions`字段进行判断）*/
export const hasPerms = (value: string | Array<string>): boolean => {
  if (!value) return false;
  const allPerms = "*:*:*";
  const { permissions } = useUserStoreHook();
  if (!permissions) return false;
  if (permissions.length === 1 && permissions[0] === allPerms) return true;
  const isAuths = isString(value)
    ? permissions.includes(value)
    : isIncludeAllChildren(value, permissions);
  return isAuths ? true : false;
};
