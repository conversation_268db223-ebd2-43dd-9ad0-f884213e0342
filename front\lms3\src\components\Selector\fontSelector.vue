<template>
  <div style="display: flex">
    <div class="pickerSpan">
      <!-- 字体类型-->
      <a-select
        v-model:value="fontFamily"
        show-search
        style="width: 150px"
        placeholder="选择字体"
        :filter-option="filterFontOption"
      >
        <a-select-option
          v-for="font in fonts"
          :key="font"
          :value="font"
          :style="{ fontFamily: font }"
        >
          {{ font }}
        </a-select-option>
      </a-select>
    </div>
    <div class="pickerSpan">
      <!-- 字体大小 -->
      <a-input-number
        v-model:value="fontSize"
        style="width: 120px"
        :min="8"
        :max="72"
        addon-after="px"
      />
    </div>
    <div class="pickerSpan">
      <a-button
        class="icon-fontType"
        :style="{
          background: textTypes.includes('bold') ? '#b3aea9' : 'none'
        }"
        @click="toggleStyles('bold')"
      >
        <BoldOutlined :style="{ fontWeight: 'bold' }" />
      </a-button>
      <a-button
        class="icon-fontType"
        :style="{
          background: textTypes.includes('italic') ? '#b3aea9' : 'none'
        }"
        @click="toggleStyles('italic')"
      >
        <ItalicOutlined :style="{ fontWeight: 'bold' }" />
      </a-button>
      <a-button
        class="icon-fontType"
        :style="{
          background: textTypes.includes('underline') ? '#b3aea9' : 'none'
        }"
        @click="toggleStyles('underline')"
      >
        <UnderlineOutlined :style="{ fontWeight: 'bold' }" />
      </a-button>
    </div>
    <!-- 颜色选择 -->
    <div class="pickerSpan">
      <div class="color-picker-wrapper">
        <!-- 隐藏原生颜色方块 -->
        <ColorPicker
          ref="colorPickerRef"
          v-model:pureColor="textColor"
          :show-alpha="true"
          :modes="['hex', 'rgb']"
        />
        <!-- 自定义图标按钮 -->
        <a-button class="icon-trigger" @click="openColorPicker">
          <FontColorsOutlined
            :style="{ color: textColor, fontWeight: 'bold' }"
          />
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ColorPicker } from "vue3-colorpicker";
import "vue3-colorpicker/style.css";
import {
  FontColorsOutlined,
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined
} from "@ant-design/icons-vue";

const props = defineProps<{
  currentStyle: object;
}>();
const emit = defineEmits<{
  styleChange: [value: any];
}>();

const fonts = ref([
  "Noto Sans SC",
  "Microsoft YaHei",
  "Arial",
  "sans-serif",
  "Tahoma",
  "SimSun",
  "Segoe UI",
  "fangsong",
  "cursive"
]);
const fontFamily = ref("Noto Sans SC"); // 默认中文字体
const fontSize = ref(14);
const textColor = ref("#000");
const textTypes = ref([]); // 存储多个样式
const colorPickerRef = ref();
const triggerEmit = ref(null);
const filterFontOption = (input, option) =>
  option.value.toLowerCase().includes(input.toLowerCase());

const textStyle = computed(() => ({
  "font-family": fontFamily.value,
  "font-size": `${fontSize.value}px`,
  fill: textColor.value,
  "font-weight": textTypes.value.includes("bold") ? "bold" : "normal",
  "font-style": textTypes.value.includes("italic") ? "italic" : "normal",
  "text-decoration": textTypes.value.includes("underline")
    ? "underline"
    : "none"
}));

const openColorPicker = () => {
  document.querySelector(".current-color").click();
};

const toggleStyles = item => {
  const index = textTypes.value.indexOf(item);
  if (index !== -1) {
    textTypes.value.splice(index, 1); // 存在则删除
  } else {
    textTypes.value.push(item); // 不存在则添加
  }
};

watch(
  () => props.currentStyle,
  style => {
    triggerEmit.value = false;
    fontFamily.value = style["font-family"] ? style["font-family"] : "";
    fontSize.value = style["font-size"]
      ? style["font-size"].replace("px", "")
      : "";
    let types = [];
    types.push(style["font-weight"] ? style["font-weight"] : "normal");
    types.push(style["font-style"] ? style["font-style"] : "normal");
    types.push(style["text-decoration"] ? style["text-decoration"] : "none");
    textTypes.value = types;
    textColor.value =
      style["fill"] || style["stroke"] || style["color"] || "#000";
  }
);
watch(textStyle, () => {
  emit("styleChange", textStyle.value);
});
</script>
<style scoped>
:deep(.vc-color-wrap) {
  width: 20px;
  height: 20px;
}
.pickerSpan {
  margin: 0 5px;
  font-size: 14px;
}

/* 图标按钮样式 */
.color-picker-wrapper {
  position: relative;
  display: inline-block;
}
.icon-fontType {
  position: relative;
  padding: 0 10px;
  transition: border-color 0.3s;
}
.icon-trigger {
  position: absolute;
  left: -2px;
  padding: 0 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  transition: border-color 0.3s;
  background: #ffffff;
}
.icon-trigger:hover {
  border-color: #1677ff;
}
.icon-down {
  position: absolute;
  left: 25px;
  padding: 0;
  border: 1px solid #d9d9d9;
  border-radius: 0 4px 4px 0px;
  transition: border-color 0.3s;
  background: #ffffff;
}
</style>
