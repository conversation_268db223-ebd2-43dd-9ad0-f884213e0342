package com.sbtr.workflow.controller;

import cn.ewsd.common.controller.BaseController;
import com.sbtr.workflow.WorkFlowContext;
import com.sbtr.workflow.vo.SecurityLogInfoVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WorkflowBaseController extends BaseController {
    protected Logger logger = LoggerFactory.getLogger(BaseController.class);

    private static final String LOG_MESSAGE_KEY = "logMessage";

    protected void setLogMessage(SecurityLogInfoVo securityLogInfo){
        WorkFlowContext.getRequestContext().set(LOG_MESSAGE_KEY,securityLogInfo);
    }
    protected void setLogMessage(String data,String dataSecurity){
        WorkFlowContext.getRequestContext().set(LOG_MESSAGE_KEY,new SecurityLogInfoVo(data,dataSecurity));
    }

    /**
     * 由反射执行该方法，不允许直接调用
     * @return 日志信息
     */
    public SecurityLogInfoVo getLogMessage(){
        Object o = WorkFlowContext.getRequestContext().get(LOG_MESSAGE_KEY);
        if(o instanceof SecurityLogInfoVo) {
            SecurityLogInfoVo message = (SecurityLogInfoVo) o;
            WorkFlowContext.getRequestContext().remove(LOG_MESSAGE_KEY);
            return message;
        }
        return new SecurityLogInfoVo();
    }
}
