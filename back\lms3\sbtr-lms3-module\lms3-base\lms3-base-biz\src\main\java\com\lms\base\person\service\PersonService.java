package com.lms.base.person.service;

import com.lms.base.feign.model.Person;
import com.lms.base.person.vo.Student;
import com.lms.common.service.BaseService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface PersonService extends BaseService<Person> {
    void adaptPersonModel(List<Person> records);

    void adaptTeacheingModel(List<Person> records);

    void adaptManageModel(List<Person> records);

    List getTeacherAnalysis(String analysetype);

    List<Person> getValidPersons();

    void setPersonImage(Person person);

    void saveRoleLink(Person person);

    boolean existPersonByCardNum(String cardNum, String s);

    List<Person> findByPersonName(String name);

    List<Object> readExcelFile(MultipartFile file, String departmentId);

    Person getPersonById(String id);

    List<String> importStudent(List<Student> studentList);

    List<String> getUserNamesBySysRoleLink(String roleid);

    List<String> getRoleIdByUserName(String userName);
}
