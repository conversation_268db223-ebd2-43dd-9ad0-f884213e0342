package com.lms.common.feign.api;

import com.lms.common.config.FeignConfig;
import com.lms.common.feign.dto.FlowModelDto;
import com.lms.common.feign.dto.ModleDto;
import com.lms.common.feign.fallback.ActFlowableApiFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(contextId = "ActFlowableApi", value = "lms-workflow",
        configuration = FeignConfig.class, fallbackFactory = ActFlowableApiFallbackFactory.class)
public interface ActFlowableApi {

    @ResponseBody
    @RequestMapping(value = "/flowModel/saveCourseWareFlowchart", method = RequestMethod.POST)
    Object saveCourseWareFlowchart(@RequestBody FlowModelDto flowModelDto);

    @ResponseBody
    @RequestMapping(value = "/flowModel/getCourseWareFlowchart", method = RequestMethod.POST)
    Object getCourseWareFlowchart(@RequestParam String modelKey);

    @ResponseBody
    @RequestMapping(value = "/flowModel/updateCourseWareFlowchart", method = RequestMethod.POST)
    Object updateCourseWareFlowchart(@RequestBody ModleDto modleDto);

    @ResponseBody
    @RequestMapping(value = "/apiFlowableModel/deleteModel", method = RequestMethod.POST)
    Object deleteModel(@RequestParam String modelId, @RequestParam String procdefId, @RequestParam int delType);

}
