import { http } from "@/utils/http";
import { baseUrlApi, systemUrlApi } from "./utils";

export const jwtLogin = (data?: object) => {
  return http.request<any>("post", systemUrlApi("jwtLogin"), { data });
};

export const Logout = (data?: object) => {
  return http.request<any>("post", baseUrlApi("login/logout"));
};

export const getUserInfo = (userid?: string) => {
  return http.request<any>("get", systemUrlApi("users/info"), {
    params: { userid }
  });
};
// 人员管理 API 服务
// 人员列表查询（分页）
export const getPersonListPage = (data?: any) => {
  return http.request<any>("post", baseUrlApi("person/listpage"), {
    data
  });
};
// 新增人员
export const addPerson = (data?: any) => {
  return http.request<any>("post", baseUrlApi("person/add"), {
    data
  });
};
// 编辑人员
export const editPerson = (data?: any) => {
  return http.request<any>("post", baseUrlApi("person/modify"), {
    data
  });
};
// 删除人员
export const batchRemovePerson = (data?: any) => {
  return http.request<any>("delete", baseUrlApi("person/batchremove"), {
    params: data
  });
};

// 系统日志 API 服务
// 列表查询（分页）
export const getLogListPage = (data?: any) => {
  return http.request<any>("post", systemUrlApi("log/listpage"), {
    data
  });
};
export const exportFile = (data?: any) => {
  return http.request<any>(
    "post",
    systemUrlApi("log/export"),
    {
      data
    },
    { responseType: "blob" }
  );
};

// 数据字典 API 服务
// 字典类型列表查询（分页）
export const getSelectitemtypeListPage = (data?: any) => {
  return http.request<any>("post", baseUrlApi("selectitemtype/listpage"), {
    data
  });
};
// 新增字典类型
export const addSelectitemType = (data?: any) => {
  return http.request<any>("post", baseUrlApi("selectitemtype/add"), {
    data
  });
};
// 编辑字典类型
export const editSelectitemType = (data?: any) => {
  return http.request<any>("post", baseUrlApi("selectitemtype/modify"), {
    data
  });
};
// 删除字典类型
export const batchRemoveSelectitemtype = (data?: any) => {
  return http.request<any>("delete", baseUrlApi("selectitemtype/batchremove"), {
    params: data
  });
};

// 字典选项列表查询（分页）
export const getSelectitemListPage = (data?: any) => {
  return http.request<any>("post", baseUrlApi("selectitem/listpage"), {
    data
  });
};
// 删除字典选项
export const batchRemoveSelectitem = (data?: any) => {
  return http.request<any>("delete", baseUrlApi("selectitem/batchremove"), {
    params: data
  });
};
// 新增字典选项
export const addSelectitem = (data?: any) => {
  return http.request<any>("post", baseUrlApi("selectitem/add"), {
    data
  });
};
// 修改字典选项
export const editSelectitem = (data?: any) => {
  return http.request<any>("post", baseUrlApi("selectitem/modify"), {
    data
  });
};
export const getSelectitemById = (data?: any) => {
  return http.request<any>(
    "get",
    baseUrlApi("selectitem/getSelectitemByIdWidthEmpty/" + data)
  );
};

export const getCodeTableByTypeWithEmpty = async (type?: any) => {
  let codeId = "";
  switch (type) {
    case "electricClassroom": // 电子教室
      codeId = "d16636903d7f4e12ab888a2e500eef6";
      break;
    case "subjectType": // 科目类型
      codeId = "75eb5c78596f4dbea4859d296a5ac92c";
      break;
    case "trainMode": // 组训方式
      codeId = "de9a3104468c4fc494095bc80475563e";
      break;
    case "evalMode": // 考核方式
      codeId = "f7aa5506f58a43f688dfeb415cdb9bd2";
      break;
    case "specialty": // 专业
      codeId = "bdce8a6593884df785dd26ec70c6f6ef";
      break;
    case "applicablePersonnel": // 适用人员、人员层次
      codeId = "1c511c00199f4fc99da984bb17e31062";
      break;
    case "equipmentType": // 装备类型
      codeId = "d89ac263bd614b93af0dd9a4a7faef25";
      break;
    case "modelNum": // 型号类型
      codeId = "d89ac263bd614b93af0dd9a4a7faef78";
      break;
    case "examSubjectType": // 题目类型
      codeId = "fc657149c8d84e79a5f3043c40d7baaa";
      break;
    case "coursewareType": // 载体类型
      codeId = "d16636903d7f4e12ab888aafe500eef6";
      break;
    case "examtype": // 试卷类型
      codeId = "4028828a60054f800160054f86690000";
      break;
    case "exammodal": // 考题类型
      codeId = "65629cc7123049249322840515828d27";
      break;
    case "examway": // 试卷考核方式
      codeId = "bc06e61c6b2a47ae990a818d73a9874b";
      break;
    case "examlevel": // 试题难度
      codeId = "10dcba700421446fb9dbd4bbf40ef5e6";
      break;
    case "levelType": // 难易度
      codeId = "10dcba700421446fb9dbd4bbf40ef5e6";
      break;
    case "examresult": // 考试结果
      codeId = "34c43f4251df4d93874a7e979a37ec23";
      break;
    case "sex": // 性别
      codeId = "395dd12b6adb43c2b8e8e48e8a3b270c";
      break;
    case "duty": // 职务
      codeId = "77176e0fb83543a6b823f051833c1ef5";
      break;
    case "nation": // 民族
      codeId = "4028828a61223d43016122d0008f0000";
      break;
    case "platetype": // 板块分类
      codeId = "24dff6c5475f416fa8239982c422417a";
      break;
    case "politics": // 政治面貌
      codeId = "f4c0eaedac8740e0ae49ae7db3545001";
      break;
    case "materialtype": // 备件类型
      codeId = "b187c98e0ff34622b5503d3f95f41c55";
      break;
    case "operatype": // 操作类型
      codeId = "24f466f9913a487eb979852bd0ae3d6f";
      break;
    case "dutyLevel": // 岗位等级
      codeId = "74FC31FE8F1441D38E493ED6A6D88C30";
      break;
    case "publishtype": // 成绩发布类型
      codeId = "f0c0348efe5811ec9d810894ef37036a";
      break;
    case "language": // 语言
      codeId = "4b70116f302211ed969cfefcfee1b6f6";
      break;
    case "education": // 学历
      codeId = "fc657149c8d84e79a5f3043c40d7baab";
      break;
    case "fileSecret": // 文件密级
      codeId = "047DA36C359F4FB5ABF276A7008438F4";
      break;
    case "personSecret": // 人员密级
      codeId = "D336B34A7FA94FC1AD690CD7B041E117";
      break;
    case "secretLimit": // 保密期限
      codeId = "BC2645BB38DA400590CE9D5292DD2DD8";
      break;
    case "needType": // 培训需求分类
      codeId = "2c90d0af92709f1e019278f6a8630001";
      break;
    case "trainForm": // 培训形式
      codeId = "2c90d0af92709f1e019278f7b2b40004";
      break;
    case "abilityType": // 能力维度
      codeId = "2c90d0af92792c5901927952c36e0000";
      break;
    case "costItem": // 费用项
      codeId = "2c90d024927b497801927f2f7dd10001";
      break;
    case "requireType": // 需求类型
      codeId = "2c90d024928f15f701929010853d0000";
      break;
    case "roomType": // 住宿场地费类型
      codeId = "2c90d0179292b27f01929938dde50009";
      break;
    case "facilityType": // 设施设备类别
      codeId = "2c90d024929d5c0b01929e6f38540005";
      break;
    case "shortPeriod": // 点检周期
      codeId = "2c90d024929d5c0b01929e6fceae0008";
      break;
    case "longPeriod": // 定检周期
      codeId = "2c90d024929d5c0b01929e70dacb000c";
      break;
    case "facilityStatus": // 设备状态
      codeId = "2c90d024929d5c0b01929e71c9820011";
      break;
    case "maintainPlan": // 维护计划
      codeId = "2c90d024929e8a0501929e9a4e080000";
      break;
    case "isPurchase": // 采购/自制
      codeId = "2c90d02492b1cc620192b1f67e170000";
      break;
    case "isPass": // 是否通过
      codeId = "2c90d02492b1cc620192b1f6e7300003";
      break;
    case "teachercategory": // 教员分类
      codeId = "2c90d01792ff6ae40192ffef57be0009";
      break;
    case "teacherlevel": // 教员等级
      codeId = "2c90d01792ff6ae40192ffeff753000c";
      break;
    case "teachercourse": // 教学课程
      codeId = "2c90d01792ff6ae40192fff1366d0010";
      break;
    case "facilityMaintainType": // 设施设备维护类型
      codeId = "2c90d01792d634970192d7e730e20000";
      break;
    case "facilitySpecificType": // 设施设备具体类型
      codeId = "2c90d01793189c0b01931a61d2bb004e";
      break;
    case "evaluateDimension": // 综合考评维度
      codeId = "2c90d017931f194a01931f3613290000";
      break;
    case "textbookType": // 教材类型
      codeId = "2c90d0af93201264019324a75fe90003";
      break;
    case "quality": // 培训性质
      codeId = "2c90d0af934d643d01934e04ba0c0008";
      break;
    case "testReliability": // 试题可靠度
      codeId = "2c90d0af93bf117f0193cd688c6d0003";
      break;
    case "trainTaskNode": // 培训任务节点
      codeId = "57DEBB327EB2416C963A267AE2C4D251";
      break;
    case "browserType": // 浏览器类型
      codeId = "92B0BE7032AB4AEF95684FBD967105B3";
      break;
    case "infosrc": // 信息来源
      codeId = "6F35DBAAD780436BBDB1CC5E02FD01EC";
      break;
    case "plancoursetrainshape": // 课程学习类型
      codeId = "F11CF1DDD6E244D7B281F13E545F37F2";
      break;
    default:
      console.log("error type:" + type);
  }
  const res = await getSelectitemById(codeId);
  return res.result;
};

// 获取包含已删除选择项数据的方法
export const initAllSelectitemType = async (type, selectitem) => {
  const res = await getCodeTableByTypeWithEmpty(type);
  if (res) {
    for (let i = 0; i < res.length; i++) {
      const value = res[i].id;
      const label = res[i].objname;
      const status = res[i].status;
      const code = res[i].code;
      const seqno = res[i].seqno;
      selectitem.push({
        value: value,
        label: label,
        status: status,
        code: code,
        seqno: seqno
      });
    }
  }
};

// 组织机构 API 服务
// 组织机构树查询（分页）
export const getDepartmentTree = (data?: any) => {
  return http.request<any>("get", baseUrlApi("department/list/" + data));
};

export const getDepartmentList = () => {
  return http.request<any>("get", baseUrlApi("department/listAll"));
};

// 新增组织机构
export const addDepartment = (data?: any) => {
  return http.request<any>("post", baseUrlApi("department/add"), { data });
};
// 编辑组织机构
export const editDepartment = (data?: any) => {
  return http.request<any>("post", baseUrlApi("department/modify"), { data });
};
// 删除组织机构
export const removeDepartment = (data?: any) => {
  return http.request<any>("delete", baseUrlApi("department/del/" + data));
};

// 用户管理 API 服务
// 用户查询（分页）
export const getUserList = (data?: any) => {
  return http.request<any>("post", systemUrlApi("users/listpage"), {
    data
  });
};
// 新增用户
export const addUser = (data?: any) => {
  return http.request<any>("post", systemUrlApi("users/add"), { data });
};
// 编辑用户
export const editUser = (data?: any) => {
  return http.request<any>("post", systemUrlApi("users/edit"), { data });
};
// 删除用户
export const removeUser = (data?: any) => {
  return http.request<any>("delete", systemUrlApi("users/del/" + data));
};
