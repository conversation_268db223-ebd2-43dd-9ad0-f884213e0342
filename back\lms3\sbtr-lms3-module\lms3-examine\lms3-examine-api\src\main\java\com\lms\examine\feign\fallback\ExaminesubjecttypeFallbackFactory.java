package com.lms.examine.feign.fallback;

import com.lms.common.model.Result;
import com.lms.examine.feign.api.ExaminesubjecttypeApi;
import com.lms.examine.feign.model.Examinesubjecttype;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ExaminesubjecttypeFallbackFactory implements FallbackFactory<ExaminesubjecttypeApi> {

    private static final Logger log = LoggerFactory.getLogger(ExaminesubjecttypeFallbackFactory.class);

    @Override
    public ExaminesubjecttypeApi create(Throwable throwable) {
        return new ExaminesubjecttypeApi() {
            @Override
            public Result<List<Examinesubjecttype>> getByExamineIdList(List<String> examineIdList) {
                return null;
            }

            @Override
            public Result saveAllForImport(List<Examinesubjecttype> examinesubjecttypeList) {
                return null;
            }
        };
    }
}
