package com.lms.common.feign.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModleDto {
    // act_my_model 主键
    private String uuid;
    private String modifierId;
    private String modifier;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date modifyTime;
    // 模型id（关联act_re_model）
    private String actDeModelId;
    // 表单设计
    private String formDesign;
    // 流程设计
    private String flowDesign;
    // 模型key
    private String actDeModelKey;
    // 数据表名
    private String formTableName;
    // 表属性
    private String formModel;
    // 模型name
    private String actDeModelName;
    //流程按钮
    private String formBtnList;
    //流程节点
    private String formFieldList;
    //流程定义Id
    private String procdefId;
    //流程分类Id
    private String category;
    //节点通知
    private String formNoticeList;

    //判断是自己写的表单还是通过拖动出来的表单  1自己写的表单  2拖动设计的
    private String modelType;

    private String nodeCodeList;
    //启动权限类型
    private String  permissionType;
    //启动权限值
    private String  permissionValue;


    // 流程模型类型  1 自定义流程界面  2 托拉拽界面
    private String processModelType;

    private String actFormConfigureUuid;
    private String sign;
}
