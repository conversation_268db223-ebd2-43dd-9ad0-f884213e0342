package com.lms.base.subject.model;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.lms.base.feign.model.Subject;
import com.lms.common.feign.api.CommonSystemApi;
import com.lms.common.model.BaseModel;
import com.lms.common.util.SpringContextUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;


public class SubjectViewModel extends BaseModel {
	/**
	 *
	 */
	private static final long serialVersionUID = -3616943288084994218L;
	private Subject subject;
	private List<String> items = new ArrayList<String>();
	private String id;

	public SubjectViewModel(Subject subject) {
		CommonSystemApi languagelabelService = SpringContextUtils.getBean(CommonSystemApi.class);
		this.subject = subject;
		this.mlevel = subject.getMlevel();
		this.mlimit = subject.getMlimit();
		if ("9e47efc0ce894454856a80171e1e6efe".equals(subject.getType())// 单选题
				|| "3ef580ec56ae436eb79b91b25d1a078e".equals(subject.getType())) {// 多选题
			this.setItems(parse(this.subject.getContent()));
		} else if ("23e162b9f27b4ebc9a5c93db09913693".equals(subject.getType())) {// 判断题
			this.items.add(languagelabelService.translateContentNotCreated("正确",""));
			this.items.add(languagelabelService.translateContentNotCreated("错误",""));
		}
	}

	private List<String> parse(String content) {
		List<String> items = new ArrayList<String>();
		try {
			Document doc = DocumentHelper.parseText(content);
			Element root = doc.getRootElement();
			Element child;
			for (Iterator<Element> it = root.elementIterator("item"); it
					.hasNext();) {
				child = it.next();
				items.add(child.getStringValue());
			}

		} catch (Exception ex) {
		}
		return items;
	}
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}


	public List<String> getItems() {
		return items;
	}

	public void setItems(List<String> items) {
		this.items = items;
	}

	public Subject getSubject() {
		return subject;
	}

	public void setSubject(Subject subject) {
		this.subject = subject;
	}

	public Subject toSubject() {
		return subject;
	}

}
