/**
 * FileName:ExaminestudentController.java
 * Author:ji<PERSON><PERSON><PERSON>
 * Time  :2013-3-7下午4:18:10
 * CopyRight: SBTR LTD.
 * decription:to-do
 */
package com.lms.examine.feign.api;

import com.lms.common.config.FeignConfig;
import com.lms.common.model.Result;
import com.lms.examine.feign.fallback.ExaminesubjecttypeFallbackFactory;
import com.lms.examine.feign.model.Examinesubjecttype;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(contextId = "examinesubjecttypeapi", value = "lms-examine", path = "/examinesubjecttype", configuration = FeignConfig.class, fallbackFactory = ExaminesubjecttypeFallbackFactory.class)
public interface ExaminesubjecttypeApi {

	@PostMapping("/getByExamineIdList")
	Result<List<Examinesubjecttype>> getByExamineIdList(@RequestBody List<String> examineIdList);

	@PostMapping("/saveAllForImport")
    Result saveAllForImport(@RequestBody List<Examinesubjecttype> examinesubjecttypeList);
}
