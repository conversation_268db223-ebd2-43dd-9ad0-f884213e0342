/**
 * FileName:ExaminestudentController.java
 * Author:ji<PERSON><PERSON><PERSON>
 * Time  :2013-3-7下午4:18:10
 * CopyRight: SBTR LTD.
 * decription:to-do
 */
package com.lms.examine.examinesubjecttype.controller;

import com.lms.common.controller.BaseController;
import com.lms.common.model.Result;
import com.lms.examine.examinesubjecttype.service.ExaminesubjecttypeService;
import com.lms.examine.feign.model.Examinesubjecttype;
import com.lms.examine.examinesubjecttype.service.impl.ExaminesubjecttypeServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 * <AUTHOR> 试题类型信息操作控制层
 */
@RestController
@RequestMapping("/examinesubjecttype")
@Api(value = "试题类型信息管理",tags = {"试题类型信息管理"})
public class ExaminesubjecttypeController extends BaseController<Examinesubjecttype> {

	@Resource
	private ExaminesubjecttypeService examinesubjecttypeService;


	@PostMapping("/getByExamineIdList")
	@ApiOperation(value = "根据id列表查询试题类型信息", httpMethod = "POST")
	public Result<List<Examinesubjecttype>> getByExamineIdList(@RequestBody List<String> examineIdList){
		return Result.OK(examinesubjecttypeService.getByExamineIdList(examineIdList));
	}

	@PostMapping("/saveAllForImport")
	@ApiOperation(value = "批量添加试题类型", httpMethod = "POST")
	public Result saveAllForImport(@RequestBody List<Examinesubjecttype> examinesubjecttypeList){
		examinesubjecttypeService.saveOrUpdateBatch(examinesubjecttypeList);
		return Result.OK();
	}

}
