package com.lms.system.storage.controller;

import com.lms.common.model.Result;
import com.lms.system.storage.service.impl.UnifiedFileStorageServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 统一文件存储控制器
 * 提供统一的文件操作API，支持多种存储策略
 */
@Slf4j
@RestController
@CrossOrigin
@RequestMapping("/api/file-storage")
@Api(value = "统一文件存储管理", tags = "统一文件存储管理")
public class UnifiedFileStorageController {

    @Autowired
    private UnifiedFileStorageServiceImpl unifiedFileStorageService;

    // ==================== 文件上传接口 ====================
    @PostMapping("/upload")
    @ApiOperation(value = "上传文件（使用默认存储策略）", httpMethod = "POST")
    @ApiImplicitParam(name = "file", value = "文件", required = true, dataType = "MultipartFile")
    public Result<String> upload(@RequestParam("file") MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                return Result.error("文件不能为空");
            }
            String fileName = unifiedFileStorageService.upload(file);
            return Result.OK(fileName, "文件上传成功");
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload/{storageType}")
    @ApiOperation(value = "使用指定存储策略上传文件", httpMethod = "POST")
    public Result<String> uploadWithStrategy(
            @ApiParam(value = "存储策略类型", required = true) @PathVariable String storageType,
            @RequestParam("file") MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                return Result.error("文件不能为空");
            }
            String fileName = unifiedFileStorageService.upload(storageType, file);
            return Result.OK(fileName, "文件上传成功，使用存储策略: " + storageType);
        } catch (Exception e) {
            log.error("文件上传失败，存储策略: {}", storageType, e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload-to-bucket")
    @ApiOperation(value = "上传文件到指定存储桶", httpMethod = "POST")
    public Result<String> uploadToBucket(
            @ApiParam(value = "存储桶名称", required = true) @RequestParam String bucketName,
            @RequestParam("file") MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                return Result.error("文件不能为空");
            }
            if (StringUtils.isEmpty(bucketName)) {
                return Result.error("存储桶名称不能为空");
            }
            String fileName = unifiedFileStorageService.upload(bucketName, file);
            return Result.OK(fileName, "文件上传成功，存储桶: " + bucketName);
        } catch (Exception e) {
            log.error("文件上传到存储桶失败，存储桶: {}", bucketName, e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload-detailed")
    @ApiOperation(value = "上传文件并返回详细信息", httpMethod = "POST")
    public Result<Map<String, String>> uploadDetailed(
            @RequestParam("file") MultipartFile file,
            @ApiParam(value = "对象名称") @RequestParam(required = false) String objectName) {
        try {
            if (file == null || file.isEmpty()) {
                return Result.error("文件不能为空");
            }
            Map<String, String> fileInfo = unifiedFileStorageService.uploadFile(file, objectName);
            return Result.OK("文件上传成功", fileInfo);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload-local-file")
    @ApiOperation(value = "上传本地文件", httpMethod = "POST")
    public Result<String> uploadLocalFile(
            @ApiParam(value = "本地文件路径", required = true) @RequestParam String fileLocation,
            @ApiParam(value = "存储桶名称", required = true) @RequestParam String bucketName,
            @ApiParam(value = "对象名称", required = true) @RequestParam String objectName) {
        try {
            if (StringUtils.isEmpty(fileLocation) || StringUtils.isEmpty(bucketName) || StringUtils.isEmpty(objectName)) {
                return Result.error("参数不能为空");
            }
            unifiedFileStorageService.uploadLocalFile(fileLocation, bucketName, objectName);
            return Result.OK(objectName, "本地文件上传成功");
        } catch (Exception e) {
            log.error("本地文件上传失败", e);
            return Result.error("本地文件上传失败: " + e.getMessage());
        }
    }

    // ==================== 文件下载接口 ====================

    @GetMapping("/download")
    @ApiOperation(value = "下载文件", httpMethod = "GET")
    public void download(
            @ApiParam(value = "文件名", required = true) @RequestParam String fileName,
            HttpServletResponse response) {
        try {
            if (StringUtils.isEmpty(fileName)) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("文件名不能为空");
                return;
            }
            unifiedFileStorageService.download(fileName, response);

        } catch (Exception e) {
            log.error("文件下载失败", e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("文件下载失败: " + e.getMessage());
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    @GetMapping("/download-from-bucket")
    @ApiOperation(value = "从指定存储桶下载文件", httpMethod = "GET")
    public void downloadFromBucket(
            @ApiParam(value = "存储桶名称", required = true) @RequestParam String bucketName,
            @ApiParam(value = "文件名", required = true) @RequestParam String fileName,
            HttpServletResponse response) {
        try {
            if (StringUtils.isEmpty(bucketName) || StringUtils.isEmpty(fileName)) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("存储桶名称和文件名不能为空");
                return;
            }
            unifiedFileStorageService.download(bucketName, fileName, response);
        } catch (Exception e) {
            log.error("从存储桶下载文件失败，存储桶: {}, 文件: {}", bucketName, fileName, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("文件下载失败: " + e.getMessage());
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    // ==================== 文件管理接口 ====================

    @GetMapping("/exists")
    @ApiOperation(value = "检查文件是否存在", httpMethod = "GET")
    public Result<Boolean> checkFileExists(
            @ApiParam(value = "文件名", required = true) @RequestParam String fileName) {
        try {
            if (StringUtils.isEmpty(fileName)) {
                return Result.error("文件名不能为空");
            }
            boolean exists = unifiedFileStorageService.checkFileIsExist(fileName);
            return Result.OK(exists ? "文件存在" : "文件不存在", exists);
        } catch (Exception e) {
            log.error("检查文件是否存在失败", e);
            return Result.error("检查文件失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除文件", httpMethod = "DELETE")
    public Result<Boolean> deleteFile(
            @ApiParam(value = "文件名", required = true) @RequestParam String fileName) {
        try {
            if (StringUtils.isEmpty(fileName)) {
                return Result.error("文件名不能为空");
            }
            boolean deleted = unifiedFileStorageService.deleteFile(fileName);
            return Result.OK(deleted ? "文件删除成功" : "文件删除失败", deleted);
        } catch (Exception e) {
            log.error("删除文件失败", e);
            return Result.error("删除文件失败: " + e.getMessage());
        }
    }

    @GetMapping("/preview-url")
    @ApiOperation(value = "获取文件预览URL", httpMethod = "GET")
    public Result<String> getPreviewUrl(
            @ApiParam(value = "文件名", required = true) @RequestParam String fileName) {
        try {
            if (StringUtils.isEmpty(fileName)) {
                return Result.error("文件名不能为空");
            }
            String previewUrl = unifiedFileStorageService.getPreviewUrl(fileName);
            return Result.OK(previewUrl, "获取预览URL成功");
        } catch (Exception e) {
            log.error("获取预览URL失败", e);
            return Result.error("获取预览URL失败: " + e.getMessage());
        }
    }

    // ==================== 存储桶管理接口 ====================

    @PostMapping("/bucket/create")
    @ApiOperation(value = "创建存储桶", httpMethod = "POST")
    public Result<Boolean> createBucket(
            @ApiParam(value = "存储桶名称", required = true) @RequestParam String bucketName) {
        try {
            if (StringUtils.isEmpty(bucketName)) {
                return Result.error("存储桶名称不能为空");
            }
            boolean created = unifiedFileStorageService.makeBucket(bucketName);
            return Result.OK(created ? "存储桶创建成功" : "存储桶创建失败", created);
        } catch (Exception e) {
            log.error("创建存储桶失败", e);
            return Result.error("创建存储桶失败: " + e.getMessage());
        }
    }

    @GetMapping("/bucket/exists")
    @ApiOperation(value = "检查存储桶是否存在", httpMethod = "GET")
    public Result<Boolean> checkBucketExists(
            @ApiParam(value = "存储桶名称", required = true) @RequestParam String bucketName) {
        try {
            if (StringUtils.isEmpty(bucketName)) {
                return Result.error("存储桶名称不能为空");
            }
            boolean exists = unifiedFileStorageService.bucketExists(bucketName);
            return Result.OK(exists ? "存储桶存在" : "存储桶不存在", exists);
        } catch (Exception e) {
            log.error("检查存储桶是否存在失败", e);
            return Result.error("检查存储桶失败: " + e.getMessage());
        }
    }

    // ==================== 系统管理接口 ====================

    @GetMapping("/config")
    @ApiOperation(value = "获取存储配置信息", httpMethod = "GET")
    public Result<String> getConfig() {
        try {
            String configInfo = unifiedFileStorageService.getConfigInfo();
            return Result.OK(configInfo, "获取配置信息成功");
        } catch (Exception e) {
            log.error("获取配置信息失败", e);
            return Result.error("获取配置信息失败: " + e.getMessage());
        }
    }

    @PostMapping("/switch-strategy")
    @ApiOperation(value = "切换默认存储策略", httpMethod = "POST")
    public Result<Boolean> switchStrategy(
            @ApiParam(value = "新的存储策略类型", required = true) @RequestParam String storageType) {
        try {
            if (StringUtils.isEmpty(storageType)) {
                return Result.error("存储策略类型不能为空");
            }
            boolean switched = unifiedFileStorageService.switchDefaultStrategy(storageType);
            return Result.OK(switched ? "存储策略切换成功" : "存储策略切换失败", switched);
        } catch (Exception e) {
            log.error("切换存储策略失败", e);
            return Result.error("切换存储策略失败: " + e.getMessage());
        }
    }

    @GetMapping("/health")
    @ApiOperation(value = "健康检查", httpMethod = "GET")
    public Result<Map<String, Boolean>> healthCheck() {
        try {
            Map<String, Boolean> healthStatus = unifiedFileStorageService.healthCheck();
            return Result.OK("健康检查完成", healthStatus);
        } catch (Exception e) {
            log.error("健康检查失败", e);
            return Result.error("健康检查失败: " + e.getMessage());
        }
    }

    @GetMapping("/storage-info")
    @ApiOperation(value = "获取当前存储策略信息", httpMethod = "GET")
    public Result<Map<String, String>> getStorageInfo() {
        try {
            Map<String, String> info = new java.util.HashMap<>();
            info.put("storageType", unifiedFileStorageService.getStorageType());
            info.put("storageDescription", unifiedFileStorageService.getStorageDescription());
            return Result.OK("获取存储策略信息成功", info);
        } catch (Exception e) {
            log.error("获取存储策略信息失败", e);
            return Result.error("获取存储策略信息失败: " + e.getMessage());
        }
    }
}
