<script setup lang="ts">
import { ref, onMounted } from "vue";
import { message } from "ant-design-vue";
import CourseAddForm from "./modules/addForm.vue";
import TrTable from "@/components/ReTrTable/index.vue";
import TrDialog from "@/components/TrDialog/index.vue";
import { getCourseList, deleteCourse } from "@/api/mrPlatform";
import { columns, queryItems, btnConfig } from "./data";
import { courseTypeOptions } from "@/data/mrPlatform";
import type { QueryConfig, TableConfig } from "@/components/ReTrTable/types";

const addModalVisible = ref(false);
const isEdit = ref(false);
const rowData = ref({});
const tableRef = ref<InstanceType<typeof TrTable>>();
const dictReady = ref(false);

const tableConfig = ref<TableConfig>({
  displayColumns: columns
});

const queryConfig = ref<QueryConfig>({
  items: queryItems,
  onQuery: () => handleSearch(),
  onReset: () => {
    // 重置查询条件
    queryConfig.value.queryForm = {};
  }
});

// 等待数据字典加载完成
onMounted(async () => {
  try {
    // 等待课程类型数据字典加载完成
    while (!courseTypeOptions || courseTypeOptions.length === 0) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    dictReady.value = true;
  } catch (error) {
    console.error('数据字典加载失败:', error);
    // 即使加载失败也设置为true，避免页面一直显示加载中
    dictReady.value = true;
  }
});

// 查询数据
const handleSearch = async () => {
  try {
    let params = {
      pageIndex: tableConfig.value.pageIndex,
      pageSize: tableConfig.value.pageSize,
      orderName: tableConfig.value.sortField,
      sortType: tableConfig.value.sortOrder
    };
    params = Object.assign(params, queryConfig.value.queryForm);

    const response = await getCourseList(params);
    if (response.success) {
      tableConfig.value.tableData = response.result.records;
      tableConfig.value.total = response.result.total;
    } else {
      message.error(response.message);
    }
  } catch (error) {
    console.error(error);
    message.error(error);
  }
};

// 按钮事件处理
const handleButtonEvent = (key: string, data: any) => {
  if (key === "add") {
    handleAdd();
  } else if (key === "edit") {
    handleEdit(data);
  } else if (key === "delete") {
    const deleteCourseWrapper = (params: any) => {
      return deleteCourse(params.ids);
    };
    tableRef.value.defaultDelete(data.id, deleteCourseWrapper);
  } else if (key === "batchDelete") {
    const deleteCourseWrapper = (params: any) => {
      return deleteCourse(params.ids);
    };
    tableRef.value.defaultBatchDelete(deleteCourseWrapper);
  }
};

// 新增课程
const handleAdd = () => {
  rowData.value = {};
  isEdit.value = false;
  addModalVisible.value = true;
};

// 编辑课程
const handleEdit = (row: any) => {
  rowData.value = row;
  isEdit.value = true;
  addModalVisible.value = true;
};
</script>

<template>
  <div v-if="dictReady" class="course-management">
    <TrTable
      ref="tableRef"
      v-model:table-config="tableConfig"
      v-model:query-config="queryConfig"
      :btn-config="btnConfig"
      @customButton="handleButtonEvent"
    />

    <!-- 新增/编辑课程弹窗 -->
    <TrDialog
      v-model:open="addModalVisible"
      :title="isEdit ? '编辑课程' : '新增课程'"
      :width="800"
      :show-footer="false"
      destroy-on-close
    >
      <CourseAddForm
        :visible="addModalVisible"
        :course="rowData"
        :is-edit="isEdit"
        @close="addModalVisible = $event"
        @submit="handleSearch"
      />
    </TrDialog>
  </div>
  <div v-else class="loading-container">
    <a-spin size="large" tip="数据字典加载中...">
      <div class="loading-content"></div>
    </a-spin>
  </div>
</template>

<style lang="scss" scoped>
.course-management {
  height: 100%;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

.loading-content {
  width: 200px;
  height: 200px;
}
</style>
