import { ref, type Ref } from "vue";
import {
  getCodeTableByTypeWithEmpty,
  getDepartmentList, getDepartmentTree
} from "@/api/system.ts";
import { message } from "ant-design-vue";
import { toRaw } from "vue";

export type DictItem = { id: string; objname: string };
type DictData = Record<string, DictItem[]>;

class SystemDataManager {
  private memoryCache: Ref<DictData> = ref({});
  private requestLocks: Record<string, boolean> = {};

  // 获取字典（带缓存）
  async getDict(dictName: string) {
    // 1. 检查内存缓存
    if (this.memoryCache.value[dictName]) {
      return toRaw(this.memoryCache.value[dictName]);
    }

    // 2. 检查本地存储
    const cached = localStorage.getItem(`dict_${dictName}`);
    if (cached) {
      this.memoryCache.value[dictName] = JSON.parse(cached);
      return toRaw(this.memoryCache.value[dictName]);
    }

    // 3. 请求锁避免重复请求
    if (this.requestLocks[dictName]) return [];
    this.requestLocks[dictName] = true;

    try {
      const res = await getCodeTableByTypeWithEmpty(dictName);
      this.memoryCache.value[dictName] = res;
      localStorage.setItem(`dict_${dictName}`, JSON.stringify(res));
      return res;
    } catch (err) {
      message.error("字典加载失败" + err);
      return [];
    } finally {
      delete this.requestLocks[dictName];
    }
  }

  // 清除缓存
  clearCache(dictName?: string) {
    if (dictName) {
      delete this.memoryCache.value[dictName];
      localStorage.removeItem(`dict_${dictName}`);
    } else {
      this.memoryCache.value = {};
      localStorage.clear();
    }
  }
  async loadDict(dictName: string) {
    const res = await this.getDict(dictName);
    return res;
  }

  async initOptions(dictName: string) {
    const res = await this.getDict(dictName);
    const itemOptions = [];
    res.forEach(item => {
      itemOptions.push({
        label: item.objname,
        value: item.id,
        code: item.code
      });
    });
    return itemOptions;
  }

  translateDict(dictData, code) {
    const itemText = dictData.find(
      item => item.value === code || item.code === code
    );
    return itemText?.label || code;
  }

  async loadDepartmentTree(pid: string) {
    const res = await getDepartmentTree(pid);
    if (res.success) {
      return res.result;
    } else {
      return [];
    }
  }

  async loadDepartmentList() {
    const res = await getDepartmentList();
    if (res.success) {
      return res.result;
    } else {
      return [];
    }
  }

  translateDepart(departData, id) {
    const depart = departData.find(item => item.id === id);
    return depart?.name || id;
  }
}

export const systemDataManager = new SystemDataManager();
