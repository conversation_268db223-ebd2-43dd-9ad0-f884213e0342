package com.lms.system.menu.service.impl;

import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.system.menu.mapper.MenuRoleLinkMapper;
import com.lms.system.feign.model.MenuRoleLink;
import com.lms.system.menu.service.MenuRoleLinkService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("MenuRoleLinkService")
public class MenuRoleLinkServiceImpl extends BaseServiceImpl<MenuRoleLinkMapper, MenuRoleLink> implements MenuRoleLinkService {

	@Resource
	private MenuRoleLinkMapper menuRoleLinkMapper;


	public void deleteMenuRole(String menuid, String roleid) {
		this.menuRoleLinkMapper.deleteMenuRole(menuid, roleid);
	}

	public List getRoleLinks() {
		return menuRoleLinkMapper.getRoleLinks();
	}
}
