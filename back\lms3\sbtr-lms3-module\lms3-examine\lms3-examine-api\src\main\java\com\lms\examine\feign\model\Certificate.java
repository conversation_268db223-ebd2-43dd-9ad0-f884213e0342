package com.lms.examine.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.feign.dto.Person;
import com.lms.common.model.BaseModel;
import com.lms.common.model.SearchContent;
import com.lms.common.model.SearchName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)

@Data
@TableName("u_certificate")
@ApiModel(value = "证书实体类")
public class Certificate extends BaseModel {

    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private String id;

    @ApiModelProperty("人员名称")
    @SearchContent
    private String name;

    @ApiModelProperty("人员id")
    private String personid;

    @ApiModelProperty("证书编号")
    @SearchName
    private String cno;

    @ApiModelProperty("培训开始时间")
    private String startdate;

    @ApiModelProperty("培训结束时间")
    private String enddate;

    @ApiModelProperty("培训内容")
    private String traincontent;

    @ApiModelProperty("发证单位")
    private String issuiingunit;

    @ApiModelProperty("发证单位名称")
    private String issuiingunitname;

    @ApiModelProperty("发证日期")
    private String issuidate;

    @ApiModelProperty("结业日期")
    private String graduatedate;

    @ApiModelProperty("创建时间")
    private String createdate;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("证书模板")
    private String model;

    @TableField(exist = false)
    @ApiModelProperty("证书模板名称")
    private String modelname;

    @ApiModelProperty("证件号")
    private String cardnum;

    @TableField(exist = false)
    @ApiModelProperty("证书模板信息")
    private Certificatemodel certificatemodel;

    @ApiModelProperty("计划id")
    private String planid;

    @ApiModelProperty("计划名称")
    @SearchContent
    private String planname;

    @ApiModelProperty("任务编号")
    private String number;

    @ApiModelProperty("序号")
    private String seqno;

    /**
     * 部长
     */
    @ApiModelProperty("部长")
    @SearchContent
    private String minister;

    /**
     * 计划岗位
     */
    @ApiModelProperty("计划岗位")
    private String planduty;

    @TableField(exist = false)
    @ApiModelProperty("计划岗位名称")
    private String plandutyname;

    @TableField(exist = false)
    @ApiModelProperty("计划型号名称")
    private String planequipmentname;


    @TableField(exist = false)
    @ApiModelProperty("人员信息")
    private Person person;

    /**
     * 人员性别
     */
    @TableField(exist = false)
    @ApiModelProperty("人员性别")
    private String personsex;

    /**
     * 人员专业
     */
    @TableField(exist = false)
    @ApiModelProperty("人员专业")
    private String personspecialityid;
    /**
     * 人员照片
     */
    @TableField(exist = false)
    @ApiModelProperty("人员照片")
    private String personimage;

    /**
     * 人员单位
     */
    @TableField(exist = false)
    @ApiModelProperty("人员单位")
    private String persondept;


}
