package com.sbtr.workflow.service.impl;

import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import cn.ewsd.common.utils.easyui.PageUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.mapper.ActMyNodeButtonMapper;
import com.sbtr.workflow.model.ActFormConfigure;
import com.sbtr.workflow.model.ActMyNodeButton;
import com.sbtr.workflow.service.ActMyNodeButtonService;
import com.sbtr.workflow.vo.ActMyNodeButtonVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("flowNodeButtonServiceImpl")
public class ActMyNodeButtonServiceImpl extends WorkflowBaseServiceImpl<ActMyNodeButton, String> implements ActMyNodeButtonService {
    @Autowired
    private ActMyNodeButtonMapper flowNodeButtonMapper;

    @Override
    public PageSet<ActMyNodeButton> getPageSet(PageParam pageParam) {
        PageHelper.startPage(pageParam.getPage(), pageParam.getRows());
        List<ActMyNodeButton> list = flowNodeButtonMapper.getPageSet();
        PageInfo<ActMyNodeButton> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public int executeDeleteBatch(String[] uuids) {
        return flowNodeButtonMapper.executeDeleteBatch(uuids);
    }

    @Override
    public Integer insertListMySql(List<ActMyNodeButton> list) {
        return flowNodeButtonMapper.insertListMySql(list);
    }

    @Override
    public Integer insertListOracle(List<ActMyNodeButton> list) {
        return flowNodeButtonMapper.insertListOracle(list);
    }

    @Override
    public List<ActMyNodeButton> getListByActDeModelKeyAndProcdefId(String key, String procdefIds) {
        return flowNodeButtonMapper.getListByActDeModelKeyAndProcdefId(key, procdefIds);
    }

    @Override
    public Integer updateProcdefIdByModelKey(String key, String procdefId) {
        return flowNodeButtonMapper.updateProcdefIdByModelKey(key, procdefId);
    }

    @Override
    public Integer updateProcdefIdIsNull(String key, String id) {
        return flowNodeButtonMapper.updateProcdefIdIsNull(key, id);
    }

    @Override
    public Integer deleteByModelKeyAnProcdefId(String key, String id) {
        return flowNodeButtonMapper.deleteByModelKeyAnProcdefId(key, id);
    }

    @Override
    public List<ActMyNodeButtonVo> getListByActDeModelKeyAndProcdefIdAndNodeId(String modelKey, String processDefinitionId, String nodeId) {
        //List<ActMyNodeButtonVo> list = new ArrayList<>();
        //ActMyNodeButtonVo actMyNodeButtonVo = new ActMyNodeButtonVo();
        //actMyNodeButtonVo.setTaskListenerSqlData("#form_data_start select * from sbtr_cloud_vue.test_datagrid where uuid = '${#form_uuid}' #form_data_end if(${#form_sex} > 1 ){ apiFlowableTaskService.updateBusinessData(\"update sbtr_cloud_vue.test_datagrid set name = '${#system_userNameId}' where uuid ='${#form_uuid}'\")}");
        //list.add(actMyNodeButtonVo);
        //return list;
       return flowNodeButtonMapper.getListByActDeModelKeyAndProcdefIdAndNodeId(modelKey,processDefinitionId,nodeId);
    }

    @Override
    public HashMap<String, Object> selectByBusinessKey(String sql) {
        return flowNodeButtonMapper.selectByBusinessKey(sql);
    }

    @Override
    public Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id) {
        return flowNodeButtonMapper.updateProcdefIdByModelKeyAndProcdef(procdefId,key,id);
    }

    @Override
    public Map<String, Object> getDetailByModelKey(String modelKey) {
        List<HashMap<String, Object>> resultList = flowNodeButtonMapper.getDetailByModelKey(modelKey);
        HashMap<String, Object> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(resultList)){
            result = resultList.get(0);
        }
        return result;
    }

}
