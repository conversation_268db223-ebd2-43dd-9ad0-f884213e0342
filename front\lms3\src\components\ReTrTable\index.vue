<script setup lang="ts">
import { ref, computed, watch, onMounted, shallowRef, nextTick } from "vue";
import { message, Modal } from "ant-design-vue";
import {
  ReloadOutlined,
  SearchOutlined,
  DeleteOutlined,
  PlusOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined
} from "@ant-design/icons-vue";
import { paginationLocale } from "@/plugins/i18n";
import type { TableConfig, TableEvents, QueryConfig } from "./types";
import type { CustomButton, OperationButton, PaginationMode } from "./types";
import {
  convertDisplayColumnsToColumns,
  generateOperationColumn,
  generateIndexColumn,
  formatTime,
  getStatusColor,
  getStatusText,
  getDefaultComponentProps,
  resetQueryForm
} from "./utils";
import { translateText } from "@/utils/translation";

/**
 * @typedef {Object} TableConfig
 * @property {Array} tableData - Table data array
 * @property {number} total - Total record count
 * @property {number} pageIndex - Current page index
 * @property {number} pageSize - Page size
 * @property {Array} displayColumns - Column configuration
 * @property {boolean} showSearchBar - Show search bar
 * @property {boolean} showMoreSearch - Show advanced search
 * @property {boolean} showCheckBox - Show row selection
 * @property {boolean} showIndex - Show index column
 * @property {boolean} showOperateColumn - Show operation column
 * @property {string} rowKey - Row key field
 * @property {string} sortField - Sort field
 * @property {string} sortOrder - Sort order
 */

/**
 * @typedef {Object} BtnConfig
 * @property {string} key - Button key
 * @property {string} label - Button label
 * @property {any} icon - Button icon
 * @property {string} type - Button type (batch|row)
 * @property {Object} props - Button props
 * @property {Function|string} [method] - Button handler
 */

/**
 * @typedef {Object} QueryConfig
 * @property {Array} items - Query item config
 * @property {Object} queryForm - Query form model
 * @property {Function} onQuery - Query handler
 * @property {Function} onReset - Reset handler
 */

// 常量定义
const CONSTANTS = {
  // 分页选项
  PAGE_SIZE_OPTIONS: ["10", "20", "50", "100"]
} as const;

// 分页和排序状态接口
interface PaginationState {
  pageIndex: number;
  pageSize: number;
  total: number;
}

interface SortState {
  field?: string;
  order?: "ascend" | "descend" | null;
}

interface TableState {
  pagination: PaginationState;
  sort: SortState;
}

// 默认配置常量
const DEFAULT_TABLE_CONFIG: TableConfig = {
  // 基础数据配置
  tableData: [],
  total: 0,
  pageIndex: 1,
  pageSize: 10,
  selector: [],

  // 显示控制配置
  showSearchBar: true,
  showMoreSearch: true,
  showCheckBox: true,
  showIndex: true,
  showOperateColumn: true,
  showHeader: true,

  // 操作列配置
  OperateColumnWidth: 180,
  operateColumnLabel: "操作",

  // 样式配置
  stripe: true,
  size: "default",

  // 列配置
  displayColumns: [],

  // 排序状态配置
  sortField: "id",
  sortOrder: "descend",

  // 行键配置
  rowKey: "id"
};

const DEFAULT_BTN_CONFIG: BtnConfig[] = [
  {
    key: "add",
    label: "新增",
    icon: PlusOutlined,
    type: "batch",
    props: { type: "primary" }
  },
  {
    key: "edit",
    label: "编辑",
    icon: EditOutlined,
    type: "row",
    props: {}
  },
  {
    key: "view",
    label: "查看",
    icon: EyeOutlined,
    type: "row",
    props: {}
  },
  {
    key: "delete",
    label: "删除",
    icon: DeleteOutlined,
    type: "row",
    props: { danger: true }
  }
];

const DEFAULT_QUERY_CONFIG: QueryConfig = {
  items: [],
  queryForm: {},
  onQuery: () => {},
  onReset: () => {}
};

const props = withDefaults(
  defineProps<
    {
      tableConfig?: TableConfig;
      btnConfig?: BtnConfig[];
      paginationMode?: PaginationMode;
      queryConfig?: QueryConfig;
    } & TableEvents
  >(),
  {
    tableConfig: () => ({
      tableData: [],
      total: 0,
      pageIndex: 1,
      pageSize: 10,
      selector: [],
      showSearchBar: true,
      showMoreSearch: true,
      showCheckBox: true,
      showIndex: true,
      showOperateColumn: true,
      showHeader: true,
      OperateColumnWidth: 180,
      operateColumnLabel: "操作",
      stripe: true,
      size: "default",
      displayColumns: [],
      sortField: "id",
      sortOrder: "descend",
      rowKey: "id"
    }),
    btnConfig: () => [],
    paginationMode: "server",
    queryConfig: () => ({
      items: [],
      queryForm: {},
      onQuery: () => {},
      onReset: () => {}
    })
  }
);

// 合并默认值的工具函数
function deepMerge(target: any, source: any) {
  if (typeof target !== "object" || typeof source !== "object") return source;
  const result = Array.isArray(target) ? [...target] : { ...target };
  for (const key in source) {
    if (
      Object.prototype.hasOwnProperty.call(source, key) &&
      source[key] !== undefined
    ) {
      if (
        typeof source[key] === "object" &&
        source[key] !== null &&
        !Array.isArray(source[key])
      ) {
        result[key] = deepMerge(target[key], source[key]);
      } else {
        result[key] = source[key];
      }
    }
  }
  return result;
}

// mergedTableConfig/mergedQueryConfig: 合并props和默认值
const mergedTableConfig = shallowRef<TableConfig>(
  deepMerge(DEFAULT_TABLE_CONFIG, props.tableConfig)
);
const mergedQueryConfig = shallowRef<QueryConfig>(
  deepMerge(DEFAULT_QUERY_CONFIG, props.queryConfig)
);

// 监听props变化，合并默认值
watch(
  () => props.tableConfig,
  (val) => {
    mergedTableConfig.value = deepMerge(DEFAULT_TABLE_CONFIG, val);
  },
  { deep: true, immediate: true }
);
watch(
  () => props.queryConfig,
  (val) => {
    mergedQueryConfig.value = deepMerge(DEFAULT_QUERY_CONFIG, val);
  },
  { deep: true, immediate: true }
);

// 所有 mergedTableConfig.value 替换为 props.tableConfig
// 所有 mergedBtnConfig.value 替换为 props.btnConfig
// 所有 mergedQueryConfig.value 替换为 props.queryConfig

const emit = defineEmits<{
  /**
   * Emitted when tableConfig changes
   * @event update:tableConfig
   * @param {TableConfig} config
   */
  "update:tableConfig": [config: TableConfig];
  /**
   * Emitted when queryConfig changes
   * @event update:queryConfig
   * @param {QueryConfig} config
   */
  "update:queryConfig": [config: QueryConfig];
  /**
   * Emitted when a custom button is clicked
   * @event customButton
   * @param {string} key
   * @param {any} data
   */
  customButton: [key: string, data: any];
  /**
   * Emitted when a row operation button is clicked
   * @event operationButton
   * @param {{ key: string; record: any }}
   */
  operationButton: [{ key: string; record: any }];
  /**
   * Emitted when pagination changes
   * @event paginationChange
   * @param {PaginationState} pagination
   */
  paginationChange: [pagination: PaginationState];
  /**
   * Emitted when sort changes
   * @event sortChange
   * @param {SortState} sort
   */
  sortChange: [sort: SortState];
  /**
   * Emitted when table state changes
   * @event stateChange
   * @param {TableState} state
   */
  stateChange: [state: TableState];
  /**
   * Emitted on any table change
   * @event change
   */
  change: [pagination: any, filters: any, sorter: any, extra: any];
  /**
   * Emitted on query
   * @event query
   * @param {Record<string, any>} queryForm
   */
  query: [queryForm: Record<string, any>];
  /**
   * Emitted on reset
   * @event reset
   */
  reset: [];
  /**
   * Emitted on query item event
   * @event queryItemEvent
   * @param {{ key: string; eventName: string; value: any; args: any[] }} event
   */
  queryItemEvent: [
    event: { key: string; eventName: string; value: any; args: any[] }
  ];
}>();

// 响应式数据
// const openMoreSearch = ref(false);
// const arrowIcon = ref("down");
const queryForm = ref<Record<string, any>>({});
// 内部状态管理（按照TrTable的模式）
const internalPaginationState = ref<PaginationState>({
  pageIndex: mergedTableConfig.value.pageIndex || 1,
  pageSize: mergedTableConfig.value.pageSize || 10,
  total: mergedTableConfig.value.total || 0
});

const internalSortState = ref<SortState>({
  field: mergedTableConfig.value.sortField || "id",
  order: mergedTableConfig.value.sortOrder || "descend"
});

function applyInitialQueryForm() {
  // 优先使用queryConfig.queryForm
  if (mergedQueryConfig.value.queryForm) {
    Object.assign(queryForm.value, mergedQueryConfig.value.queryForm);
  } else if (mergedQueryConfig.value.initialValues) {
    Object.assign(queryForm.value, mergedQueryConfig.value.initialValues);
  }
}

function initQueryFormFields() {
  const items = mergedQueryConfig.value.items || [];
  items.forEach(item => {
    if (!(item.key in queryForm.value)) {
      if (item.props && "defaultValue" in item.props) {
        queryForm.value[item.key] = item.props.defaultValue;
      } else {
        queryForm.value[item.key] = "";
      }
    }
  });
}
const defaultSearch = async (
  listMethod: (para: any) => Promise<ResponseResult>,
  params: {}
) => {
  try {
    let tc = mergedTableConfig.value;
    if (!params) {
      params = {
        pageIndex: tc.pageIndex,
        pageSize: tc.pageSize,
        orderName: tc.field,
        sortType: tc.order
      };
      params = Object.assign(params, queryForm.value);
    }
    const response = await listMethod(params);
    console.log("API 响应:", response);

    if (response.success) {
      // 更新内部状态
      internalPaginationState.value.total = response.result.total;

      // 通过 emit 更新外部 tableConfig
      const updatedConfig = {
        ...mergedTableConfig.value,
        tableData: response.result.records,
        total: response.result.total
      };

      console.log("更新 tableConfig:", updatedConfig);
      emit("update:tableConfig", updatedConfig);
    } else {
      message.error(response.message);
    }
  } catch (error) {
    console.error("查询操作失败:", error);
    message.error("查询操作失败，请重试");
  } finally {
  }
};
// 删除单个人员
const defaultDelete = (
  ids: string,
  delMethod: (params: any) => Promise<ResponseResult>
) => {
  Modal.confirm({
    title: "确认信息",
    content: "确认要删除选中的记录？",
    okText: "确认",
    cancelText: "取消",
    onOk() {
      delMethod({ ids: ids })
        .then(result => {
          if (result.success) {
            message.success(result.message);
            handleQuery();
          } else {
            message.error(result.message);
          }
        })
        .catch(error => {
          console.error("删除操作失败:", error);
          message.error("删除操作失败，请重试");
        });
    }
  });
};

// 批量删除
const defaultBatchDelete = (
  delMethod: (params: any) => Promise<ResponseResult>
) => {
  if (selector.value.length === 0) {
    message.warning("请选择要删除的记录");
    return;
  }
  let ids = selector.value.map(item => item.id).toString();
  defaultDelete(ids, delMethod);
};

// 同步查询表单到外部queryConfig
function syncQueryFormToExternal() {
  if (mergedQueryConfig.value) {
    emit("update:queryConfig", {
      ...mergedQueryConfig.value,
      queryForm: { ...queryForm.value }
    });
  }
}

// 同步排序状态到外部tableConfig
function syncSortStateToExternal() {
  emit("update:tableConfig", {
    ...mergedTableConfig.value,
    sortField: internalSortState.value.field,
    sortOrder: internalSortState.value.order
  });
}

onMounted(() => {
  // 确保合并配置完成后再初始化
  nextTick(() => {
    initQueryFormFields();
    applyInitialQueryForm();
    handleQuery();
  });
});

// 监听外部queryConfig.queryForm变化
watch(
  () => mergedQueryConfig.value?.queryForm,
  newQueryForm => {
    if (newQueryForm) {
      Object.assign(queryForm.value, newQueryForm);
    }
  },
  { deep: true, immediate: false }
);

// 监听外部tableConfig排序状态变化
watch(
  () => [mergedTableConfig.value?.sortField, mergedTableConfig.value?.sortOrder],
  ([newSortField, newSortOrder]) => {
    if (newSortField !== undefined || newSortOrder !== undefined) {
      internalSortState.value = {
        field: newSortField || internalSortState.value.field,
        order:
          newSortOrder !== undefined
            ? newSortOrder
            : internalSortState.value.order
      };
    }
  },
  { immediate: false }
);

watch(
  () => mergedQueryConfig.value,
  () => {
    initQueryFormFields();
    applyInitialQueryForm();
  },
  { immediate: false, deep: true }
);

const computedQueryItems = computed(() => {
  const items = mergedQueryConfig.value?.items || [];
  return items.map(item => {
    const baseProps = getDefaultComponentProps(
      item.component || "a-input",
      item.label,
      item.props
    );
    const eventHandlers: Record<string, (...args: any[]) => void> = {};
    if (item.events) {
      Object.keys(item.events).forEach(eventName => {
        eventHandlers[eventName] = (...args: any[]) => {
          if (item.events && item.events[eventName]) {
            item.events[eventName](...args);
          }
          emit("queryItemEvent", {
            key: item.key,
            eventName,
            value: args[0],
            args
          });
        };
      });
    }
    return {
      ...item,
      props: baseProps,
      _eventHandlers: eventHandlers
    };
  });
});

// 获取当前组件实例
// const instance = getCurrentInstance();

// 区分批量操作和行操作按钮
const batchButtons = computed(() =>
  props.btnConfig?.filter(btn => !btn.type || btn.type === "batch")
);
const rowButtons = computed(() =>
  props.btnConfig?.filter(btn => btn.type === "row")
);

// 监听外部tableConfig变化，驱动内部状态
watch(
  () => props.tableConfig,
  val => {
    if (props.paginationMode === "server") {
      internalPaginationState.value.pageIndex = val.pageIndex || 1;
      internalPaginationState.value.pageSize = val.pageSize || 10;
      internalPaginationState.value.total = val.total || 0;
    } else {
      // 前端分页时total由数据长度决定，同时需要同步pageIndex
      internalPaginationState.value.pageIndex = val.pageIndex || 1;
      internalPaginationState.value.pageSize = val.pageSize || 10;
      internalPaginationState.value.total = val.tableData?.length || 0;
    }
  },
  { deep: true, immediate: true }
);

// 前端分页数据（按照TrTable的模式）
const pagedTableData = computed(() => {
  if (props.paginationMode === "client") {
    let data = [...(mergedTableConfig.value?.tableData || [])];
    // 排序
    if (internalSortState.value.field && internalSortState.value.order) {
      data.sort((a, b) => {
        const field = internalSortState.value.field as string;
        const order = internalSortState.value.order === "ascend" ? 1 : -1;
        if (a[field] > b[field]) return order;
        if (a[field] < b[field]) return -order;
        return 0;
      });
    }
    // 分页
    const start =
      (internalPaginationState.value.pageIndex - 1) *
      internalPaginationState.value.pageSize;
    const end = start + internalPaginationState.value.pageSize;
    return data.slice(start, end);
  } else {
    return mergedTableConfig.value?.tableData || [];
  }
});

// 计算属性
const tableData = computed(() => mergedTableConfig.value?.tableData || []);
const total = computed(() => mergedTableConfig.value?.total || 0);
const pageIndex = computed(() => mergedTableConfig.value?.pageIndex || 1);
const pageSize = computed(() => mergedTableConfig.value?.pageSize || 10);

const showSearchBar = computed(
  () => mergedTableConfig.value?.showSearchBar !== false
);
const showMoreSearch = computed(
  () => mergedTableConfig.value?.showMoreSearch !== false
);

const showCheckBox = computed(() => mergedTableConfig.value?.showCheckBox !== false);
const showIndex = computed(() => mergedTableConfig.value?.showIndex === true);
const selector = computed(() => mergedTableConfig.value?.selector || []);
const showOperateColumn = computed(
  () => mergedTableConfig.value?.showOperateColumn !== false
);
const operateColumnWidth = computed(
  () => mergedTableConfig.value?.OperateColumnWidth || 180
);
const operateColumnLabel = computed(
  () => mergedTableConfig.value?.operateColumnLabel || "操作"
);
const stripe = computed(() => mergedTableConfig.value?.stripe !== false);
const size = computed(() => mergedTableConfig.value?.size || "default");
const showHeader = computed(() => mergedTableConfig.value?.showHeader !== false);
const displayColumns = computed(() => mergedTableConfig.value?.displayColumns || []);

// 表格列配置
const tableColumns = computed(() => {
  let columns = [];

  // 添加序号列
  if (showIndex.value) {
    columns.push(...generateIndexColumn());
  }

  // 添加数据列
  columns.push(...convertDisplayColumnsToColumns(displayColumns.value));

  // 添加操作列
  if (showOperateColumn.value) {
    columns.push(
      ...generateOperationColumn(
        showOperateColumn.value,
        operateColumnWidth.value,
        operateColumnLabel.value
      )
    );
  }

  return columns;
});

// 递归查找prop
function findPropByDataIndex(columns: any[], dataIndex: string) {
  for (const col of columns) {
    if (col.prop === dataIndex) return col.prop;
    if (col.children) {
      const found = findPropByDataIndex(col.children, dataIndex);
      if (found) return found;
    }
  }
  return dataIndex;
}

// 根据dataIndex查找displayColumns的prop
const getColumnPropByDataIndex = (dataIndex: string) => {
  return findPropByDataIndex(displayColumns.value, dataIndex);
};

// 行选择配置
const rowSelection = computed(() => ({
  type: "checkbox",
  selectedRowKeys: selector.value.map(item => item.id),
  onChange: (selectedRowKeys: any[], selectedRows: any[]) => {
    updateSelector(selectedRows); // 存对象数组
  }
}));

// 状态更新函数（按照TrTable的模式）
const updatePaginationState = (newPagination: PaginationState) => {
  internalPaginationState.value = {
    ...internalPaginationState.value,
    ...newPagination
  };
  emit("paginationChange", { ...internalPaginationState.value });
  console.log("updatePaginationState", internalPaginationState.value);
};

const updatePaginationTotal = (total: number) => {
  internalPaginationState.value.total = total;
};

const updateSortState = (newSort: SortState) => {
  internalSortState.value = { ...newSort };
  emit("sortChange", newSort);
  // 同步排序状态到外部tableConfig
  syncSortStateToExternal();
};

// 统一的事件处理函数（按照TrTable的模式）
const handleTableChange = (
  pagination: any,
  filters: any,
  sorter: any,
  extra: any
) => {
  // 处理排序变化
  if (sorter.field !== undefined) {
    const sortState: SortState = {
      field: sorter.field || "id",
      order: sorter.order || "descend"
    };
    updateSortState(sortState);
    // 调用queryConfig.onQuery
    if (
      mergedQueryConfig.value?.onQuery &&
      typeof mergedQueryConfig.value.onQuery === "function"
    ) {
      mergedQueryConfig.value.onQuery(queryForm.value, {
        sortField: getColumnPropByDataIndex(sortState.field),
        sortOrder: sortState.order
      });
    } else {
      emit("query", queryForm.value, {
        sortField: getColumnPropByDataIndex(sortState.field),
        sortOrder: sortState.order
      });
    }
  }

  // 处理分页变化
  if (pagination.current !== undefined || pagination.pageSize !== undefined) {
    const paginationState: PaginationState = {
      pageIndex: pagination.current || internalPaginationState.value.pageIndex,
      pageSize: pagination.pageSize || internalPaginationState.value.pageSize,
      total: internalPaginationState.value.total
    };
    updatePaginationState(paginationState);

    // 更新外部tableConfig
    emit("update:tableConfig", {
      ...mergedTableConfig.value,
      pageIndex: paginationState.pageIndex,
      pageSize: paginationState.pageSize
    });

    // 只在服务端分页模式下调用queryConfig.onQuery
    if (
      props.paginationMode === "server" &&
      mergedQueryConfig.value?.onQuery &&
      typeof mergedQueryConfig.value.onQuery === "function"
    ) {
      mergedQueryConfig.value.onQuery(queryForm.value, {
        sortField: internalSortState.value.field,
        sortOrder: internalSortState.value.order
      });
    } else if (props.paginationMode === "server") {
      emit("query", queryForm.value, {
        sortField: internalSortState.value.field,
        sortOrder: internalSortState.value.order
      });
    }
  }

  // 发出组合状态变化事件
  const tableState: TableState = {
    pagination: { ...internalPaginationState.value },
    sort: {
      field: sorter.field || undefined,
      order: sorter.order || null
    }
  };
  emit("stateChange", tableState);
  emit("change", pagination, filters, sorter, extra);
};

// 方法
const updateSelector = (selectedRows: any[]) => {
  // eslint-disable-next-line vue/no-mutating-props
  if (mergedTableConfig.value) {
    mergedTableConfig.value.selector = selectedRows;
  }
  emit("update:tableConfig", { ...mergedTableConfig.value });
};

// 查询事件
const handleQuery = (params?: any) => {
  // 重置到第一页
  const newConfig = { ...mergedTableConfig.value, pageIndex: 1 };
  emit("update:tableConfig", newConfig);

  // 同步查询表单到外部queryConfig
  syncQueryFormToExternal();

  if (
    mergedQueryConfig.value?.onQuery &&
    typeof mergedQueryConfig.value.onQuery === "function"
  ) {
    mergedQueryConfig.value.onQuery(queryForm.value, params);
  } else {
    emit("customButton", "query", queryForm.value);
  }
};

const handleReset = () => {
  resetQueryForm(queryForm.value);
  // 同步查询表单到外部queryConfig
  syncQueryFormToExternal();
  if (
    mergedQueryConfig.value?.onReset &&
    typeof mergedQueryConfig.value.onReset === "function"
  ) {
    mergedQueryConfig.value.onReset(queryForm.value);
  } else {
    Object.keys(queryForm).forEach(key => {
      queryForm[key] = undefined;
    });
    emit("reset", queryForm.value);
  }
};

// const toggleSearchBar = () => {
//   openMoreSearch.value = !openMoreSearch.value;
//   arrowIcon.value = openMoreSearch.value ? "up" : "down";
// };

// 监听 tableData 变化，自动清空 selector
watch(tableData, () => {
  updateSelector([]);
});

/**
 * Exposed methods for parent components
 * @method getPaginationState
 * @method getSortState
 * @method getTableState
 * @method setPaginationState
 * @method setSortState
 * @method setTableState
 * @method setTotal
 * @method resetPagination
 * @method resetSort
 * @method resetTableState
 * @method goToPage
 * @method setPageSize
 * @method getTableData
 * @method getSelector
 * @method setSelector
 * @method getQueryForm
 * @method setQueryForm
 * @method resetQueryForm
 * @method defaultSearch
 * @method defaultDelete
 * @method defaultBatchDelete
 */
defineExpose({
  // 状态获取
  getPaginationState: () => ({ ...internalPaginationState.value }),
  getSortState: () => ({ ...internalSortState.value }),
  getTableState: () => ({
    pagination: { ...internalPaginationState.value },
    sort: { ...internalSortState.value }
  }),

  // 状态更新
  setPaginationState: (pagination: PaginationState) => {
    updatePaginationState(pagination);
  },
  setSortState: (sort: SortState) => {
    updateSortState(sort);
  },
  setTableState: (state: TableState) => {
    updatePaginationState(state.pagination);
    updateSortState(state.sort);
  },
  setTotal: (total: number) => {
    updatePaginationTotal(total);
  },

  // 状态重置
  resetPagination: () => {
    updatePaginationState({ pageIndex: 1, pageSize: 10, total: 0 });
  },
  resetSort: () => {
    updateSortState({ field: undefined, order: null });
  },
  resetTableState: () => {
    resetPagination();
    resetSort();
  },

  // 分页跳转
  goToPage: (pageIndex: number) => {
    updatePaginationState({
      ...internalPaginationState.value,
      pageIndex
    });
  },
  setPageSize: (pageSize: number) => {
    updatePaginationState({
      ...internalPaginationState.value,
      pageSize,
      pageIndex: 1
    });
  },

  // 原有方法
  getTableData: () => tableData.value,
  getSelector: () => selector.value,
  setSelector: (newSelector: any[]) => updateSelector(newSelector),
  // 查询表单相关
  getQueryForm: () => ({ ...queryForm.value }),
  setQueryForm: (form: Record<string, any>) => {
    Object.assign(queryForm.value, form);
    // 同步到外部queryConfig
    syncQueryFormToExternal();
  },
  resetQueryForm: () => {
    resetQueryForm(queryForm.value);
    // 同步到外部queryConfig
    syncQueryFormToExternal();
  },
  defaultSearch,
  defaultDelete,
  defaultBatchDelete
});
</script>

<template>
  <div class="re-tr-table-wrapper">
    <!-- 查询区：只支持queryConfig -->
    <div
      v-if="mergedQueryConfig?.items && mergedQueryConfig.items.length > 0"
      class="re-tr-table-search-bar"
    >
      <a-form :model="queryForm" layout="inline" class="re-tr-table-query-form">
        <div class="tr-table-query-flex-row">
          <div class="tr-table-query-fields">
            <a-row>
              <template v-for="item in computedQueryItems" :key="item.key">
                <a-col>
                  <a-form-item style="margin: 10px 0" :label="item.label">
                    <component
                      :is="
                        item.componentInstance || item.component || 'a-input'
                      "
                      v-model:value="queryForm[item.key]"
                      style="width: 150px"
                      v-bind="item.props"
                      v-on="item._eventHandlers"
                    />
                  </a-form-item>
                </a-col>
              </template>
              <slot name="quickSearch" />
            </a-row>
            <a-row v-if="$slots.moreSearch">
              <a-col :span="24">
                <slot name="moreSearch" :queryForm="queryForm" />
              </a-col>
            </a-row>
          </div>
          <div class="tr-table-query-btns">
            <a-button
              class="tr-btn reset-btn"
              style="margin-right: 15px"
              @click="handleReset"
            >
              <template #icon><ReloadOutlined /></template>
              {{ translateText("重置") }}
            </a-button>
            <a-button
              type="primary"
              class="tr-btn search-btn"
              @click="handleQuery"
            >
              <template #icon><SearchOutlined /></template>
              {{ translateText("查询") }}
            </a-button>
          </div>
        </div>
      </a-form>
    </div>
    <!-- 插槽自定义查询区 -->
    <div v-else class="re-tr-table-search-bar">
      <slot name="quickSearch" />
    </div>

    <!-- 按钮工具栏 -->
    <div v-if="batchButtons.length > 0" class="re-tr-table-toolbar">
      <div class="toolbar-buttons">
        <a-button
          v-for="btn in batchButtons"
          :key="btn.key"
          v-bind="
            btn.key === 'batchDelete'
              ? { ...btn.props, disabled: selector.length === 0 }
              : btn.props
          "
          class="tr-btn custom-btn"
          @click="
            () => {
              if (typeof btn.method === 'function') {
                btn.method(selector);
              } else if (typeof btn.method === 'string' || !btn.method) {
                emit('customButton', btn.key, selector);
              }
            }
          "
        >
          <template v-if="btn.icon" #icon>
            <component :is="btn.icon" />
          </template>
          {{ translateText(btn.label) }}
        </a-button>
      </div>
    </div>

    <a-divider class="table-divider" />

    <!-- 表格区域 -->
    <div class="re-tr-table-content">
      <a-table
        :columns="tableColumns"
        :data-source="pagedTableData"
        :row-key="mergedTableConfig?.rowKey || 'id'"
        :pagination="false"
        :row-selection="rowSelection"
        :bordered="true"
        :size="size"
        :stripe="stripe"
        :show-header="showHeader"
        class="re-tr-table"
        @change="handleTableChange"
      >
        <!-- 自定义列内容插槽 -->
        <template #bodyCell="{ column, record, text, index }">
          <!-- 序号列 -->
          <template v-if="column.dataIndex === 'index'">
            <span>{{
              Math.max(
                1,
                (internalPaginationState.pageIndex - 1) *
                  internalPaginationState.pageSize +
                  index +
                  1
              )
            }}</span>
          </template>

          <!-- 自定义 render 函数/组件 -->
          <template v-else-if="column.render">
            <component
              :is="column.render(text, record, index)"
              v-if="typeof column.render(text, record, index) === 'object'"
            />
            <span v-else>{{ column.render(text, record, index) }}</span>
          </template>

          <!-- 图片渲染 待测试-->
          <template v-else-if="column.renderType === 'img'">
            <img
              :src="text"
              alt="img"
              style="
                width: 40px;
                height: 40px;
                object-fit: cover;
                border-radius: 6px;
                border: 1px solid #f0f0f0;
              "
            />
          </template>

          <!-- 标签渲染 -->
          <template v-else-if="column.renderType === 'tag'">
            <a-tag
              :color="
                getStatusColor(text, column.statusMap) ||
                column.tagColor ||
                'blue'
              "
            >
              {{
                column.statusMap ? getStatusText(text, column.statusMap) : text
              }}
            </a-tag>
          </template>

          <!-- 时间渲染 -->
          <template v-else-if="column.renderType === 'time'">
            <span>{{ formatTime(text) }}</span>
          </template>

          <!-- HTML渲染 待测试-->
          <template v-else-if="column.renderType === 'html'">
            <span v-html="text" />
          </template>

          <!-- 状态渲染 -->
          <template v-else-if="column.renderType === 'status'">
            <a-tag :color="getStatusColor(text, column.statusMap)">
              {{ getStatusText(text, column.statusMap) }}
            </a-tag>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.dataIndex === 'operation'">
            <div class="operation-buttons">
              <a-button
                v-for="btn in rowButtons"
                :key="btn.key"
                v-bind="btn.props"
                type="link"
                size="small"
                class="tr-btn custom-op-btn"
                @click="
                  () => {
                    if (typeof btn.method === 'function') {
                      btn.method(record);
                    } else if (typeof btn.method === 'string' || !btn.method) {
                      emit('customButton', btn.key, record);
                    }
                  }
                "
              >
                <template v-if="btn.icon" #icon>
                  <component :is="btn.icon" />
                </template>
                {{ btn.label }}
              </a-button>
            </div>
          </template>

          <!-- 默认渲染/插槽 -->
          <template v-else>
            <slot
              :name="column.scopedSlots?.customRender || column.dataIndex"
              :record="record"
              :column="column"
              :text="text"
              :index="index"
            >
              {{ text }}
            </slot>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 分页区域 -->
    <div v-if="pagedTableData.length > 0" class="re-tr-table-pagination">
      <a-pagination
        :current="internalPaginationState.pageIndex"
        :page-size="internalPaginationState.pageSize"
        :total="internalPaginationState.total"
        :show-size-changer="true"
        :show-quick-jumper="true"
        :show-total="
          (total, range) =>
            `共${total}条记录，当前第${internalPaginationState.pageIndex}/${Math.ceil(total / internalPaginationState.pageSize)}页`
        "
        :page-size-options="CONSTANTS.PAGE_SIZE_OPTIONS"
        :locale="paginationLocale"
        @change="
          (page, pageSize) =>
            handleTableChange({ current: page, pageSize }, {}, {}, {})
        "
        @show-size-change="
          (page, pageSize) =>
            handleTableChange({ current: page, pageSize }, {}, {}, {})
        "
      />
    </div>
  </div>
</template>

<style scoped>
.re-tr-table-wrapper {
  height: 100%;
  background: #fff;
  border-radius: var(--border-radius-md, 8px);
  box-shadow: none;
  border: 1px solid var(--gray-200, #e5e7eb);
  display: flex;
  flex-direction: column;
}

.re-tr-table-search-bar {
  padding: 16px 24px 12px 24px;
  background: #fff;
  width: 100%;
}
.re-tr-table-query-form {
  width: 100%;
}
.tr-table-query-flex-row {
  display: flex;
  flex-direction: row;
  /* align-items: flex-start; */
  width: 100%;
}
.tr-table-query-fields {
  flex: 1;
}
.tr-table-query-btns {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: unset;
  gap: 0;
}
::v-deep(.ant-form) {
  width: 100%;
}

.search-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.quick-search {
  flex: 1;
}

.search-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.more-search-area {
  padding-top: 16px;
  border-top: 1px solid var(--gray-200, #e5e7eb);
}

.re-tr-table-toolbar {
  padding: 0 24px;
  background: #fff;
}

.toolbar-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  padding: 12px 0;
}

.table-divider {
  height: 1px;
  background-color: var(--gray-200, #e5e7eb);
  margin: 0;
}

.re-tr-table-content {
  flex: 1;
  background: #fff;
  padding: 24px;
  overflow: auto;
}

.re-tr-table {
  border-radius: var(--border-radius-md, 8px);
  overflow: hidden;
  border: 1px solid var(--gray-200, #e5e7eb);
  width: 100%;
}

.re-tr-table-pagination {
  padding: 4px;
  background: #fff;
  border-top: 1px solid var(--gray-200, #e5e7eb);
  display: flex;
  align-items: center;
  justify-content: center;
}

.tr-btn {
  border-radius: var(--border-radius-sm, 6px) !important;
  font-size: 14px !important;
  height: 32px !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reset-btn {
  background: #fff !important;
  color: #4b5563 !important;
  border: 1px solid #e5e7eb !important;
}

.search-btn {
  margin-left: 0;
}

.more-search-btn {
  background: #fff !important;
  color: #4b5563 !important;
  border: 1px solid #e5e7eb !important;
}

/* .add-btn {
  background-color: #53b93e !important;
  border-color: #53b93e !important;
}

.edit-btn {
  background-color: #54c4b6 !important;
  border-color: #54c4b6 !important;
  color: #fff !important;
}

.view-btn {
  background-color: #419deb !important;
  border-color: #419deb !important;
  color: #fff !important;
}

.delete-btn {
  background-color: #ed7070 !important;
  border-color: #ed7070 !important;
}

.extend1-btn {
  background-color: #419deb !important;
  border-color: #419deb !important;
  color: #fff !important;
}

.extend2-btn {
  background-color: #5386c4 !important;
  border-color: #5386c4 !important;
  color: #fff !important;
} */

.operation-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

/* 穿透样式 */
:deep(.re-tr-table .ant-table-thead > tr > th) {
  background-color: #eff7fd !important;
  font-size: 14px;
  font-weight: 600;
  color: #4b5563;
  border-bottom: 1px solid var(--gray-200, #e5e7eb);
  padding: 10px 16px;
  position: sticky;
  top: 0;
  z-index: 2;
}

:deep(.re-tr-table .ant-table-tbody > tr > td) {
  font-size: 14px;
  color: #4b5563;
  border-bottom: 1px solid var(--gray-200, #e5e7eb);
  padding: 2px 16px;
  height: 40px;
  line-height: 40px;
}

:deep(.re-tr-table .ant-table-tbody > tr:hover > td) {
  background-color: #f5f6fa;
}

:deep(.re-tr-table .ant-table) {
  border: 0;
  border-radius: var(--border-radius-md, 8px);
  overflow: hidden;
}

:deep(.ant-table-selection-column .ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

/* 固定列样式优化 */
:deep(.ant-table-cell-fix-left),
:deep(.ant-table-cell-fix-right) {
  z-index: 1;
  background: #fff;
}

:deep(.ant-table-cell-fix-left-first) {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.12);
}

:deep(.ant-table-cell-fix-right-last) {
  box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.12);
}

/* 表头固定列样式 */
:deep(.ant-table-thead .ant-table-cell-fix-left),
:deep(.ant-table-thead .ant-table-cell-fix-right) {
  z-index: 3;
  background: #eff7fd !important;
}

:deep(.ant-table-thead .ant-table-cell-fix-left-first) {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.12);
}

:deep(.ant-table-thead .ant-table-cell-fix-right-last) {
  box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.12);
}

/* 确保表格容器正确处理滚动 */
:deep(.ant-table-container) {
  overflow: auto;
}

/* 修复表头和数据列对齐问题 */
:deep(.ant-table-thead > tr > th),
:deep(.ant-table-tbody > tr > td) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.ant-pagination) {
  .ant-pagination-item {
    border-color: var(--gray-300, #d1d5db);
    border-radius: var(--border-radius-sm, 6px);
    font-size: 14px;

    &.ant-pagination-item-active {
      background-color: var(--primary-color, #1890ff);
      border-color: var(--primary-color, #1890ff);
      color: #fff !important;
    }

    &:hover {
      border-color: var(--primary-color, #1890ff);
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    border-color: var(--gray-300, #d1d5db);
    border-radius: var(--border-radius-sm, 6px);

    &:hover {
      border-color: var(--primary-color, #1890ff);
    }
  }

  .ant-pagination-options {
    .ant-select-selector {
      font-size: 14px;
      min-width: 100px; /* 增加选择器最小宽度 */
    }

    .ant-select-selection-item {
      font-size: 14px;
    }
  }

  .ant-pagination-options-quick-jumper {
    font-size: 14px;
  }
}

:deep(.ant-pagination-item-active),
:deep(.ant-pagination-item-active a) {
  color: #fff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .re-tr-table-search-bar {
    padding: 16px 16px 0 16px;
  }

  .search-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-actions {
    justify-content: center;
  }

  .tr-btn {
    padding: 0 16px !important;
    font-size: 13px !important;
  }
}
</style>
