package com.sbtr.workflow.model;

import javax.persistence.Table;
@Table(name = "act_my_node_code")
public class ActMyNodeCode{
    public String uuid;
    public String actDeModelKey;
    public String nodeCode;
    public String nodeId;
    public String nodeName;
    public String procdefId;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getActDeModelKey() {
        return actDeModelKey;
    }

    public void setActDeModelKey(String actDeModelKey) {
        this.actDeModelKey = actDeModelKey;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getProcdefId() {
        return procdefId;
    }

    public void setProcdefId(String procdefId) {
        this.procdefId = procdefId;
    }
}

