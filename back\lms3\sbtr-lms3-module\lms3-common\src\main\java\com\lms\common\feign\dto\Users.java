package com.lms.common.feign.dto;

import com.lms.common.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class Users extends BaseModel {

    private String id;
    /**
     * 登陆名
     **/
    private String name;//为用户的证件号，唯一！
    /**
     * 密码
     **/
    private String password;
    /**
     * 用户类型
     **/
    private Integer usertype;

    /**
     * 人员id
     */
    private String personid;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;

    private String personname;

    private String department;

    /**
     * 是否首次登录
     */
    private boolean isfirstlogin;

    /**
     * 是否被锁定
     */
    private boolean islocked;

    /**
     * 密级,对应S1000D中securityClassification属性值
     **/
    private String usecurity;

    /**
     * 是否允许用户登录系统
     **/
    private boolean enable;

    /**
     * 错误密码输入次数
     **/
    private Integer psderrortimes;

    /**
     * 密码修改日期
     **/
    private String psdupdatedate;

    /**
     * 上次登录时间，可用于错误密码输入次数的重置
     **/
    private String lastlogintime;

    /**
     * 限制用户登录ip地址
     */
    private String ipaddr;

    /**
     * 限制用户登录浏览器类型
     */
    private String clientagent;

    /**
     * 角色id
     */
    private String roleid;

    /**
     * 角色列表
     */
    private List<Role> roles = new ArrayList<>();
}
