package com.lms.examine.feign.fallback;

import com.lms.common.model.Result;
import com.lms.examine.feign.api.ExaminePageApi;
import com.lms.examine.feign.model.Examinepage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


/**
 * misboot-mdata相关接口 模块降级处理
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
@Component
public class ExaminePageApiFallbackFactory implements FallbackFactory<ExaminePageApi> {

    private static final Logger log = LoggerFactory.getLogger(ExaminePageApiFallbackFactory.class);


    @Override
    public ExaminePageApi create(Throwable throwable) {
        log.error("ExaminePageApi模块服务调用失败:{}", throwable.getMessage());
        return new ExaminePageApi() {
            @Override
            public Result<List<Examinepage>> listExaminepage(String planId) {
                return null;
            }

            @Override
            public Result<List<Map<String, Object>>> listSQLQuery(String sql) {
                return null;
            }

            @Override
            public Result saveAllForImport(List<Examinepage> examinepageList) {
                return null;
            }
        };
    }
}
