package com.sbtr.workflow.model;

import cn.ewsd.common.model.MCoreBase;
import cn.hutool.core.date.DateTime;

import javax.persistence.Table;

/**
 * 流程每个节点所对应按钮
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-23 07:12:25
 */
@Table(name = "act_my_flowagent")
public class ActMyFlowAgent extends MCoreBase {
    private static final long serialVersionUID = 1L;

    /**
     * 代理人
     */
    private String agent;

    /**
     * 代理人名称
     */
    private String agentName;

    /**
     * 委托人
     */
    private String mandator;

    /**
     * 委托人名称
     */
    private String mandatorName;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 适用流程key
     */
    private String modelKeys;

    /**
     * 适用角色
     */
    private String groups;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getMandator() {
        return mandator;
    }

    public void setMandator(String mandator) {
        this.mandator = mandator;
    }

    public String getMandatorName() {
        return mandatorName;
    }

    public void setMandatorName(String mandatorName) {
        this.mandatorName = mandatorName;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getModelKeys() {
        return modelKeys;
    }

    public void setModelKeys(String modelKeys) {
        this.modelKeys = modelKeys;
    }

    public String getGroups() {
        return groups;
    }

    public void setGroups(String groups) {
        this.groups = groups;
    }
}
