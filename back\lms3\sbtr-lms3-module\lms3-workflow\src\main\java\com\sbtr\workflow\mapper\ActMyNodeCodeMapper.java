package com.sbtr.workflow.mapper;
import com.sbtr.workflow.model.ActMyNodeCode;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程每个节点所对字段是否可编辑以及是否可见
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-27 13:43:13
 */
public interface ActMyNodeCodeMapper extends tk.mybatis.mapper.common.Mapper<ActMyNodeCode> {

    //获取分页集
    List<ActMyNodeCode> getPageSet();

    Integer insertListOracle(@Param("list") List<ActMyNodeCode> list);

    Integer insertList(@Param("list") List<ActMyNodeCode> list);

    List<ActMyNodeCode> selectByParam(String nodeId,String actDeModelKey, String procdefId);

    Integer updateNodeCodeByParam(String nodeCode, String uuid);

    Integer updateProDefByParam(String procdefId, String uuid);

    void updateProcdefIdByModelKey(String key, String procdefId);

    List<ActMyNodeCode> getListByActDeModelKeyAndProcdefId(@Param("key") String key,@Param("procdefId") String procdefId);
    Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id);

    void deleteByModelKeyAnProcdefId(String key, String id);
}
