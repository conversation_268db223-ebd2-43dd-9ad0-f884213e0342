import Axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type CustomParamsSerializer
} from "axios";
import type {
  PureHttpError,
  RequestMethods,
  PureHttpResponse,
  PureHttpRequestConfig
} from "./types.d";
import { stringify } from "qs";
import NProgress from "../progress";
import { getToken, formatToken, removeToken } from "@/utils/auth";
import { message } from "ant-design-vue";
import { translateText } from "@/utils/translation";
import router from "@/router";
// import { useUserStoreHook } from "@/store/modules/user";

// 相关配置请参考：www.axios-js.com/zh-cn/docs/#axios-request-config-1
const defaultConfig: AxiosRequestConfig = {
  // 请求超时时间
  timeout: 15000,
  headers: {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest"
  },
  // 数组格式参数序列化（https://github.com/axios/axios/issues/5142）
  paramsSerializer: {
    serialize: stringify as unknown as CustomParamsSerializer
  }
};

class PureHttp {
  constructor() {
    this.httpInterceptorsRequest();
    this.httpInterceptorsResponse();
  }

  /** `token`过期后，暂存待执行的请求 */
  private static requests = [];

  /** 防止重复刷新`token` */
  private static isRefreshing = false;

  /** 防止重复显示错误消息 */
  private static isError = false;

  /** 初始化配置对象 */
  private static initConfig: PureHttpRequestConfig = {};

  /** 保存当前`Axios`实例对象 */
  private static axiosInstance: AxiosInstance = Axios.create(defaultConfig);

  /** 重连原始请求 */
  private static retryOriginalRequest(config: PureHttpRequestConfig) {
    return new Promise(resolve => {
      PureHttp.requests.push((token: string) => {
        config.headers["Authorization"] = formatToken(token);
        resolve(config);
      });
    });
  }

  /** 请求拦截 */
  private httpInterceptorsRequest(): void {
    PureHttp.axiosInstance.interceptors.request.use(
      async (config: PureHttpRequestConfig): Promise<any> => {
        // 开启进度条动画
        NProgress.start();
        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof config.beforeRequestCallback === "function") {
          config.beforeRequestCallback(config);
          return config;
        }
        if (PureHttp.initConfig.beforeRequestCallback) {
          PureHttp.initConfig.beforeRequestCallback(config);
          return config;
        }
        /** 请求白名单，放置一些不需要`token`的接口（通过设置请求白名单，防止`token`过期后再请求造成的死循环问题） */
        // const whiteList = ["/refresh-token", "/login"];
        const whiteList = ["/system/jwtLogin"];
        return whiteList.some(url => config.url.endsWith(url))
          ? config
          : new Promise(resolve => {
              const data = getToken();
              // console.log(data);
              if (data) {
                const now = new Date().getTime();
                const expired = parseInt(data.expires) - now <= 0;
                if (expired) {
                  if (!PureHttp.isRefreshing) {
                    PureHttp.isRefreshing = true;
                    // token过期刷新
                    // useUserStoreHook()
                    //   .handRefreshToken({ refreshToken: data.refreshToken })
                    //   .then(res => {
                    //     const token = res.data.accessToken;
                    //     config.headers["Authorization"] = formatToken(token);
                    //     PureHttp.requests.forEach(cb => cb(token));
                    //     PureHttp.requests = [];
                    //   })
                    //   .finally(() => {
                    //     PureHttp.isRefreshing = false;
                    //   });
                    const token = getToken();
                    config.headers["token"] = token;
                    // config.headers["Authorization"] = formatToken(token);
                    PureHttp.requests.forEach(cb => cb(token));
                    PureHttp.requests = [];
                    PureHttp.isRefreshing = false;
                  }
                  resolve(PureHttp.retryOriginalRequest(config));
                } else {
                  config.headers["token"] = data;
                  // config.headers["Authorization"] = formatToken(data);
                  resolve(config);
                }
              } else {
                resolve(config);
              }
            });
      },
      error => {
        return Promise.reject(error);
      }
    );
  }

  /** 响应拦截 */
  private httpInterceptorsResponse(): void {
    const instance = PureHttp.axiosInstance;
    instance.interceptors.response.use(
      (response: PureHttpResponse) => {
        const $config = response.config;
        // 关闭进度条动画
        NProgress.done();
        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof $config.beforeResponseCallback === "function") {
          $config.beforeResponseCallback(response);
          return response.data;
        }
        if (PureHttp.initConfig.beforeResponseCallback) {
          PureHttp.initConfig.beforeResponseCallback(response);
          return response.data;
        }
        return response.data;
      },
      (error: PureHttpError) => {
        const $error = error;
        $error.isCancelRequest = Axios.isCancel($error);
        // 关闭进度条动画
        NProgress.done();

        // 错误处理逻辑
        const res = error.response;
        let msg;

        if (!res?.data?.message) {
          if (res && res.status) {
            switch (res.status) {
              case 400:
                msg = translateText("服务器内部错误");
                break;
              case 401:
                msg = translateText("未授权，请登陆");
                removeToken();
                router.replace("/login");
                // 401错误不需要显示消息，直接跳转登录页
                return Promise.reject($error);
              case 403:
                msg = translateText("拒绝访问");
                break;
              case 404:
                msg = translateText("请求地址无法访问");
                break;
              case 408:
                msg = translateText("请求超时");
                break;
              case 500:
                msg = translateText("服务器内部错误");
                break;
              case 503:
                msg = translateText("服务不可用");
                break;
              case 504:
                msg = translateText("网关超时");
                break;
              default:
                msg = translateText("网络错误");
                break;
            }
          } else {
            const message = error.message;
            if (
              message === "Error: Network Error" ||
              message === "Network Error"
            ) {
              msg = translateText("网络异常");
            } else if (message.indexOf("timeout") > -1) {
              msg = translateText("请求超时");
            } else {
              msg = translateText("网络错误");
            }
          }

          if (res?.data) {
            res.data.message = msg;
          }
        }

        // 处理列表接口的特殊情况
        if (res?.config?.url?.indexOf("/list") > 0 && !res?.data?.result) {
          res.data.result = {
            content: [],
            total: 0
          };
        }

        // 显示错误消息（除了401错误）
        if (msg && res?.status !== 401) {
          // 防止重复显示错误消息
          if (!PureHttp.isError) {
            PureHttp.isError = true;
            message.error({
              content: msg,
              duration: 2000
            });
            // 2秒后重置错误状态
            setTimeout(() => {
              PureHttp.isError = false;
            }, 2000);
          }
        }

        // 所有的响应异常 区分来源为取消请求/非取消请求
        return Promise.reject($error);
      }
    );
  }

  /** 通用请求工具函数 */
  public request<T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: PureHttpRequestConfig
  ): Promise<T> {
    const config = {
      method,
      url,
      ...param,
      ...axiosConfig
    } as PureHttpRequestConfig;

    // 单独处理自定义请求/响应回调
    return new Promise((resolve, reject) => {
      PureHttp.axiosInstance
        .request(config)
        .then((response: undefined) => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  /** 单独抽离的`post`工具函数 */
  public post<T, P>(
    url: string,
    params?: AxiosRequestConfig<P>,
    config?: PureHttpRequestConfig
  ): Promise<T> {
    return this.request<T>("post", url, params, config);
  }

  /** 单独抽离的`get`工具函数 */
  public get<T, P>(
    url: string,
    params?: AxiosRequestConfig<P>,
    config?: PureHttpRequestConfig
  ): Promise<T> {
    return this.request<T>("get", url, params, config);
  }
}

export const http = new PureHttp();
