package com.sbtr.workflow.mapper;

import com.sbtr.workflow.model.ActMyFlowAgent;

import java.util.List;

/**
 * 流程每个节点所对应按钮
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-23 07:12:25
 */
public interface ActMyFlowAgentMapper extends tk.mybatis.mapper.common.Mapper<ActMyFlowAgent> {

    //获取分页集
    List<ActMyFlowAgent> getPageSet();

   int executeDeleteBatch(Object[] var1);

    List<String> getMandatorsByAgent(String agent,String currentTime);
}
