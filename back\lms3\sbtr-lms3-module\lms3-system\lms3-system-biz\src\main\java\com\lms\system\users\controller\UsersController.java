package com.lms.system.users.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.lms.common.enums.RoleEum;
import com.lms.system.feign.model.Log;
import com.lms.system.feign.model.Users;
import com.lms.system.feign.model.VUsers;
import com.lms.system.log.service.LogService;
import com.lms.system.users.service.UsersService;
import com.lms.system.users.service.VUsersService;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.model.JwtUser;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.DateHelper;
import com.lms.common.util.EncryptHelper;
import com.lms.common.util.StringHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import com.lms.common.util.ContextUtil;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/users")
@Api(value = "用户管理", tags = "用户管理")
public class UsersController extends BaseController<Users> {

    @Value("${lockedTimes}")
    private int lockedTimes;
    @Resource
    private UsersService usersService;

    @Resource
    private VUsersService vusersService;

    @Resource
    private ContextUtil contextUtil;

    @Resource
    private LogService logService;

    @GetMapping(value = {"/login"})
    @ApiOperation(value = "用户登录", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "登录用户名", required = true),
            @ApiImplicitParam(name = "password", value = "登录用户密码", required = true),
            @ApiImplicitParam(name = "ipaddr", value = "用户客户端ip地址"),
            @ApiImplicitParam(name = "clientagent", value = "用户客户端浏览器类型")
    })
    public Result<JwtUser> login(@RequestParam("userName") String userName, @RequestParam("password") String password,
                                 @RequestParam(required = false) String ipaddr, @RequestParam(required = false) String clientagent) {
        return usersService.login(userName, password, ipaddr, clientagent);
    }

    @GetMapping(value = {"/info"})
    @ApiOperation(value = "获取当前登录用户", httpMethod = "GET")
    public Result<JwtUser> getUserInfo() {
        JwtUser jwtUser = ContextUtil.getCurrentUser();
        if (jwtUser != null) {
            return Result.OK(jwtUser);
        }
        return Result.error("用户不存在！", null);
    }

    // 列表显示(有分页)
    @PostMapping(value = {"/listpage"})
    @ApiOperation(value = "列表显示(有分页)", httpMethod = "POST")
    public Result listpage(@RequestBody JSONObject request) {
        PageInfo pageInfo = getPageInfo(request);
//        pageInfo.setOrderName("name");
//        pageInfo.setSort(pageInfo.ASC);
        Page<VUsers> users = vusersService.listByCondition(pageInfo);
        return Result.OK(users);
    }

    // 列表显示ALL（无分页）
    @RequestMapping(value = {"/get"}, method = RequestMethod.GET)
    @ApiOperation(value = "列表显示ALL（无分页）", httpMethod = "GET")
    public Result getUser() {
        List<Users> users = this.usersService.list();
        return Result.OK(users);
    }

    @GetMapping(value = "/parseUserToken")
    @ApiOperation(value = "解析用户token", httpMethod = "GET")
    public Result<JwtUser> parseUserToken(HttpServletRequest request) {
        contextUtil.loadJwtUser(request);
        JwtUser jwtUser = ContextUtil.getCurrentUser();
        if(jwtUser != null){
            return Result.OK(jwtUser);
        } else {
            return Result.error("token解析失败", null);
        }
    }

    @PostMapping(value = "/refreshToken")
    @ApiOperation(value = "刷新用户token", httpMethod = "POST")
    public Result<String> refreshToken(HttpServletRequest request) {
        try {
            // 1. 从请求中获取当前token
            String currentToken = contextUtil.getToken(request);
            if (StringHelper.isEmpty(currentToken)) {
                return Result.error("当前token不能为空", null);
            }
            // 2. 验证当前token是否有效
            if (contextUtil.isTokenExpired(currentToken)) {
                return Result.error("当前token已过期，请重新登录", null);
            }
            // 3. 解析当前token获取用户信息
            contextUtil.loadJwtUser(request);
            JwtUser jwtUser = ContextUtil.getCurrentUser();
            if (jwtUser == null) {
                return Result.error("无法获取用户信息，请重新登录", null);
            }
            // 4. 生成新的token
            String newToken = contextUtil.createJWT(jwtUser);
            if (StringHelper.isEmpty(newToken)) {
                return Result.error("token刷新失败", null);
            }
            return Result.OK(newToken, "token刷新成功");
        } catch (Exception e) {
            log.error("刷新token失败", e);
            return Result.error("token刷新失败：" + e.getMessage(), null);
        }
    }

    @RequestMapping(value = {"/getById"}, method = RequestMethod.GET)
    @ApiOperation(value = "根据id查询", httpMethod = "GET")
    public Result<Users> getUserById(@RequestParam("id") String id) {
        Users users = this.usersService.getById(id);
        return Result.OK(users);
    }

    @RequestMapping(value = {"/getUsersByUserName"}, method = RequestMethod.GET)
    @ApiOperation(value = "根据用户名查询用户信息", httpMethod = "GET")
    public Result<Users> getUsersByUserName(@RequestParam("userName") String userName) {
        Users users = this.usersService.getUsersByUserName(userName);
        return Result.OK(users);
    }


    @RequestMapping(value = {"/isExsitAccountName"}, method = RequestMethod.GET)
    @ApiOperation(value = "判断用户是否存在", httpMethod = "GET")
    public Result isExsitAccountName(@RequestParam("name") String name, @RequestParam("id") String id) {
        Boolean isExsit = this.usersService.existUser(name, id);
        return Result.OK(isExsit);
    }


    // 更新Action
    @LMSLog(desc = "编辑用户信息", otype = LogType.Update)
    @PostMapping(value = {"/edit"})
    @ApiOperation(value = "编辑用户信息", httpMethod = "POST")
    public Result updateUsers(@RequestBody Users us) {
        String id = us.getId();
        Users users = this.usersService.getById(id);
        if (usersService.existUser(us.getName(), id)) {
            return Result.error("用户" + us.getName() + "已存在！");
        }
        if (users == null) {
            return Result.error("用户不存在！");
        } else if (us.isEnable() && ObjectUtil.isEmpty(users.getRoleid())) {
            return Result.error("用户账号未设置所属角色，无法启用！");
        }
        users.setName(us.getName());
        users.setIpaddr(us.getIpaddr());
        users.setClientagent(us.getClientagent());
        if (!StringHelper.isEmpty(us.getPassword())) {
            users.setPassword(EncryptHelper.md5Password(us.getPassword()));
            users.setPsdupdatedate(DateHelper.getCurrentDate());
        }
        users.setEnable(us.isEnable());
        users.setStatus(1);
        users.setIslocked(us.isIslocked());
        if (!users.isIslocked()) users.setPsderrortimes(0);
        usersService.saveOrUpdate(users);
        usersService.clearUserCache(users);
        return Result.OK(users);
    }

    @LMSLog(desc = "切换用户角色", otype = LogType.Update)
    @RequestMapping(value = {"/switchRole"}, method = RequestMethod.POST)
    @ApiOperation(value = "切换用户角色", httpMethod = "POST")
    public Result switchRole(@RequestBody Users us) {
        Users users = this.usersService.getById(us.getId());
        if (users == null) {
            return Result.error("用户不存在,无法切换！");
        } else if (users.isEnable() && ObjectUtil.isEmpty(users.getRoleid())) {
            return Result.error("用户账号未设置所属角色，无法切换！");
        }
        String roleId = us.getRoleid();
        int usertype = RoleEum.getCodeByValue(roleId);
        users.setRoleid(roleId);
        users.setUsertype(usertype);
        this.usersService.saveOrUpdate(users);
        usersService.clearUserCache(users);
        return Result.OK(users);
    }

    // 删除
    @LMSLog(desc = "删除用户", otype = LogType.Delete)
    @RequestMapping(value = {"/del/{id}"}, method = RequestMethod.DELETE)
    @ApiOperation(value = "删除用户", httpMethod = "DELETE")
    public Result deleteUser(@PathVariable("id") String id) {
        this.usersService.removeById(id);
        // 删除用户时，需要修改userCache
        // userCache.removeUserFromCache(users.getName());
        return Result.OK();
    }

    @DeleteMapping("/batchDeleteByPersonId")
    @ApiOperation(value = "根据人员id批量删除用户", httpMethod = "DELETE")
    public Result batchDeleteByPersonId(@RequestParam("ids") String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        List<Users> users = usersService.getUsersByPersonIds(idList);
        usersService.removeBatchByIds(users);
        return Result.OK();
    }

    // 人员新增的处理
    @LMSLog(desc = "添加用户", otype = LogType.Save, order = 1, method = "setUsersLog")
    @PostMapping("/add")
    @ApiOperation(value = "添加用户", httpMethod = "POST")
    public Result<Users> saveUser(@RequestBody Users users) {
        usersService.saveOrUpdate(users);
        return Result.OK(users);
    }

    @PostMapping("/saveAllUser")
    @ApiOperation(value = "批量更新用户", httpMethod = "POST")
    public Result saveAllUser(@RequestBody List<Users> users) {
        usersService.saveBatch(users);
        return Result.OK();
    }

    @RequestMapping(value = {"/isRightPassword"}, method = RequestMethod.POST)
    @ApiOperation(value = "判断密码是否正确", httpMethod = "POST")
    public Result isRightPassword(@RequestParam("oldpassword") String oldpassword,
                                  @RequestParam(value = "username", required = false) String username) {
        Users user = null;
        if (StringUtils.isNotEmpty(username)) {
            user = usersService.getUsersByUserName(username);
        } else {
            if (StringUtils.isNotEmpty(ContextUtil.getUserId())) {
                user = usersService.getById(ContextUtil.getUserId());
            }
        }
        if (user != null) {
            if (user.getPassword().equals(
                    EncryptHelper.md5Password(oldpassword))) {
                return Result.OK(true);
            } else {
                return Result.error("原密码不正确");
            }
        }
        return Result.error("用户不存在，无法比较密码");
    }

    @RequestMapping(value = {"/savepassword"}, method = RequestMethod.POST)
    @ApiOperation(value = "修改密码", httpMethod = "POST")
    public Result savePassword(@RequestParam("oldpassword") String oldpassword,
                               @RequestParam("newpassword") String newpassword,
                               @RequestParam(value = "username", required = false) String username) {

        Users user = null;
        if (StringUtils.isNotEmpty(username)) {
            user = usersService.getUsersByUserName(username);
        } else {
            if (StringUtils.isNotEmpty(ContextUtil.getUserId())) {
                user = usersService.getById(ContextUtil.getUserId());
            }
        }
        if (user != null) {
            if (user.getPassword().equals(
                    EncryptHelper.md5Password(oldpassword))) {
                user.setPassword(EncryptHelper.md5Password(newpassword));
                user.setPsdupdatedate(DateHelper.getCurrentDate());
                this.usersService.saveOrUpdate(user);
                commonSystemApi.saveLog(user.getName(), "修改密码", LogType.Save, "操作成功");
                return Result.OK(true);
            } else {
                commonSystemApi.saveLog(user.getName(), "修改密码", LogType.Save, "旧密码不正确");
                return Result.error("旧密码不正确");
            }
        }
        commonSystemApi.saveLog(StringUtils.isNotEmpty(username) ? username : ContextUtil.getUserName(), "修改密码", LogType.Save, "用户不存在，无法更新密码");
        return Result.error("用户不存在，无法更新密码");
    }

    @RequestMapping(value = {"/checkpassword"}, method = RequestMethod.GET)
    @ApiOperation(value = "检查密码是否符合规则", httpMethod = "POST")
    public Result checkpassword(@RequestParam(value = "password", required = false) String password) {
        if (StringUtils.isEmpty(password)) {
            return Result.error("请输入密码");
        }
        return usersService.checkpassword(password);
    }


    public String setUsersLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.Save)) { //新增数据日志
            Users user = (Users) (args[0]);
            objname = StringHelper.null2String(user.getName());
        } else if (lmslog.otype().equals(LogType.Update)) { //编辑数据日志
            Users p = (Users) (args[0]);
            objname = StringHelper.null2String(p.getName());
        }
        return objname;
    }


    @GetMapping("/getUsersByPersonId")
    @ApiOperation(value = "根据人员id查询用户信息", httpMethod = "GET")
    public Result<Users> getUsersByPersonId(@RequestParam String PersonId) {
        return Result.OK(usersService.getUsersByPersonId(PersonId));
    }

}
