{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "node",
    "strict": false,
    "strictFunctionTypes": false,
    "noImplicitThis": true,
    "noImplicitAny": false,
    "jsx": "preserve",
    "importHelpers": true,
    "experimentalDecorators": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true, // 或者使用 "emitDeclarationOnly": true
    "allowImportingTsExtensions": true,
    "sourceMap": true,
    "baseUrl": ".",
    "allowJs": false,
    "resolveJsonModule": true,
    "lib": ["ESNext", "DOM"],
    "paths": {
      "baseUrl": ["."],
      "@/*": ["src/*"],
      "@build/*": ["build/*"],
      "vue": ["node_modules/vue/dist/vue.runtime.esm-bundler.js"],
      "vue-i18n": ["node_modules/vue-i18n/dist/vue-i18n.runtime.esm-browser.js"]
    },
    "types": [
      "node",
      "vite/client",
      "element-plus/global",
      "@pureadmin/table/volar",
      "unplugin-icons/types/vue",
      "@pureadmin/descriptions/volar"
    ]
  },
  "include": [
    "mock/*.ts",
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "types/*.d.ts",
    "vite.config.ts"
  ],
  "exclude": ["dist", "**/*.js", "node_modules"]
}
