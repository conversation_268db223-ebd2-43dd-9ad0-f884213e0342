package com.lms.examine.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import com.lms.system.feign.model.Attach;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * VUExaminestudent entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)

@Data
@TableName("v_u_examinestudent")
public class VUExaminestudent extends BaseModel {
	@TableId(type= IdType.ASSIGN_ID)
	private String id;
	private String pid;
	private String name;
	private String planid;
	private String type;
	private String specialid;
	private String levelid;
	private Double totalpoint;
	private Double pass;
	private Double choiceness;
	private Double excellence;
	private String senddowndate;
	private String finishdate;
	private Integer examinetime;
	private String examinedate;
	private String examineendtime;
	private String remark;
	private Integer status;
	private String creatorid;
	private String createdate;
	private String modal;
	private String way;
	private String examinedept;
	private String principal;
	private String content;
	private String createdept;
	private String equiptypeid;
	private String examinetype;
	private String personid;
	private String receivetime;
	private String begintime;
	private String endtime;
	private String examinedtime;
	private String submittime;
	private Double score;
	private String result;
	private Integer studentstatus;
	private String personname;
	private String examinestatus;
	private String deptname;
	private String duty;
	private String tel;
	private String specialityid;
	private String equipmentid;
	private String sex;
	private String publishdate;
	private String publishtype;
	private Integer allowview;
	private Integer showranking;
	private Integer score_rank;
	private String attachid;
	@TableField(exist = false)
	private Attach attachinfo;
}
