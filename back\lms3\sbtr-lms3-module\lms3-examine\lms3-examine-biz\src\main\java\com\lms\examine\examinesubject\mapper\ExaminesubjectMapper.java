package com.lms.examine.examinesubject.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.examine.feign.model.Examinesubject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface ExaminesubjectMapper extends BaseMapper<Examinesubject> {

    @Select("${sql}")
	List<Examinesubject> getExaminesubjectById(@Param("sql") String sql);

}
