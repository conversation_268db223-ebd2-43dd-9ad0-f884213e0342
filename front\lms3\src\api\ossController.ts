import { http } from "@/utils/http";
import { systemUrlApi, editorUrlApi } from "./utils";

// 上传文件(返回文件名)
export const uploadOssFile = data => {
  return http.request<any>(
    "post",
    systemUrlApi("oss/upload"),
    { data },
    {
      timeout: null,
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

// 上传文件(返回详细信息)
export const uploadOssFileDetail = data => {
  return http.request<any>(
    "post",
    systemUrlApi("oss/uploadFile"),
    { data },
    {
      timeout: null,
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

// 下载文件
export const downloadOssFile = (fileName: string) => {
  return http.request<any>(
    "get",
    systemUrlApi("oss/download?fileName=" + fileName),
    {},
    { timeout: null }
  );
};

// 删除文件
export const removeOssObject = (objectName: string) => {
  return http.request<any>(
    "post",
    systemUrlApi("oss/removeObject"),
    {},
    {
      params: { objectName }
    }
  );
};

// 获取文件预览URL
export const getPreviewUrl = (objectName: string) => {
  return http.request<any>(
    "post",
    systemUrlApi("oss/getPreviewUrl"),
    {},
    {
      params: { objectName }
    }
  );
};

// 轻量化分片上传文件
export const uploadFileChunk = (formData: FormData) => {
  return http.request<any>(
    "post",
    editorUrlApi("Store/UploadFileAndLoadCloud"),
    { data: formData },
    {
      timeout: null,
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};
