package com.sbtr.workflow.service.impl;

import com.sbtr.workflow.service.BusinessSystemDataService;
import com.sbtr.workflow.utils.BaseUtils;
import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import cn.ewsd.common.utils.easyui.PageUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.mapper.MyVariableMapper;
import com.sbtr.workflow.model.*;
import com.sbtr.workflow.service.MyVariableService;
import com.sbtr.workflow.vo.TaskVo;
import org.flowable.ui.modeler.domain.AbstractModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service("myVariableService")
public class MyVariableServiceImpl extends WorkflowBaseServiceImpl<MyVariable, String> implements MyVariableService {

    @Autowired
    private MyVariableMapper myVariableMapper;
    @Resource
    public BusinessSystemDataService businessSystemDataService;

//    @Autowired
//    public UserInfo userInfo;

    @Override
    public PageSet<MyVariable> getPageSet(PageParam pageParam, String filterStr) {
        PageHelper.startPage(pageParam.getPage(), pageParam.getRows());
        List<MyVariable> list = myVariableMapper.getPageSet(filterStr);
        PageInfo<MyVariable> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public String getVariable(String processKey, String variableName) {
        MyVariable myVariable = myVariableMapper.getVariable(processKey, variableName);
        return null == myVariable ? null : myVariable.getVariableValue();
    }

    @Override
    public List<MyVariable> getListByProcessKey(String processkey) {
        return myVariableMapper.getListByProcessKey(processkey);
    }


    @Override
    public List<MyVariable> getVariables(String processKey) {
        return myVariableMapper.getVariables(processKey);
    }

    @Override
    public Integer saveGenerate(String key) {
        String[] variableDesc = {"业务新增", "业务详情", "业务编辑", "业务保存", "业务更新", "流程处理", "页面判断"};
        String[] variableName = {"businessAddUrl", "businessDetailUrl", "businessEditUrl", "businessSaveUrl", "businessUpdateUrl", "completeTaskUrl", "businessJudge"};
        List<MyVariable> list = new ArrayList<>();
        MyVariable myVariable = null;
        Integer integer = 0;
        for (int i = 0; i < 7; i++) {
            myVariable = new MyVariable();
            myVariable.setUuid(BaseUtils.UUIDGenerator());
            myVariable.setCreatorId(businessSystemDataService.getUserNameId());
            myVariable.setCreator(businessSystemDataService.getUserName());
            myVariable.setCreateTime(new Date());
            myVariable.setCreatorOrgId(Integer.parseInt(businessSystemDataService.getOrgId()));
            myVariable.setProcessKey(key);
            myVariable.setVariableDesc(variableDesc[i]);
            myVariable.setVariableName(variableName[i]);
            if ("completeTaskUrl".equals(variableName[i])) {
                myVariable.setVariableValue("/workflow/workflow/completeTask");
            } else if ("businessAddUrl".equals(variableName[i])) {
                myVariable.setVariableValue("/xxx/xxx/add");
            } else if ("businessDetailUrl".equals(variableName[i])) {
                myVariable.setVariableValue("/xxx/xxx/getDetailByUuid");
            } else if ("businessEditUrl".equals(variableName[i])) {
                myVariable.setVariableValue("/xxx/xxx/edit");
            } else if ("businessSaveUrl".equals(variableName[i])) {
                myVariable.setVariableValue("/xxx/xxx/save");
            } else if ("businessUpdateUrl".equals(variableName[i])) {
                myVariable.setVariableValue("/xxx/xxx/update");
            } else if ("businessJudge".equals(variableName[i])) {
                myVariable.setVariableValue("currency");

            }
            //custom:按钮在业务那边  currency: 按钮在流程这边        于永清用车  业务保存按钮在业务那边 暂时不启用
//            else if("businessJudge".equals(variableName[i])){
//                myVariable.setVariableValue("custom");
//            }
            //判断不存在才添加进去
            // list.add(myVariable);
            if (true) {   //通过  key + variableName 判断添加成
                integer += myVariableMapper.insertSelective(myVariable);
            }

        }
        return integer;//
    }

    @Override
    public List<User> getListByUserNamdIdOrUserName(String filter) {
        return myVariableMapper.getListByUserNamdIdOrUserName(filter);
    }

    @Override
    public List<Organization> getListByText(String filter) {
        return myVariableMapper.getListByText(filter);
    }

    @Override
    public List<AbstractModel> getModelsByModelType(String filterSort, int modelType) {
        return myVariableMapper.getModelsByModelType(filterSort, modelType);
    }

    @Override
    public Integer updateAssigneeById(String userNameId, String id) {
        return myVariableMapper.updateAssigneeById(userNameId, id);
    }


    @Override
    public PageSet<TaskVo> gettoDoWithPageSetData(PageParam pageParam, String filterStr, String userNameId, String processDefinitionName) {
        PageHelper.startPage(pageParam.getPage(), pageParam.getRows());
        List<TaskVo> list = myVariableMapper.getApplyedTasks(userNameId, processDefinitionName, processDefinitionName,"1");
        PageInfo<TaskVo> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public List<Map<String, Object>> getListBydisActivityId(String disActivityId, String processInstanceId) {
        return myVariableMapper.getListBydisActivityId(disActivityId, processInstanceId);
    }

    @Override
    public List<Map<String, Object>> getListBydisEndTime(String endTime, String processInstanceId) {
        return myVariableMapper.getListBydisEndTime(endTime, processInstanceId);
    }

    @Override
    public Integer deleteRunActinstsByIds(List<String> runActivityIds) {
        return myVariableMapper.deleteRunActinstsByIds(runActivityIds);
    }

    @Override
    public Integer deleteHisActinstsByIds(List<String> runActivityIds) {
        return myVariableMapper.deleteHisActinstsByIds(runActivityIds);
    }

    @Override
    public List<Map<String, Object>> getTitle(String processInstanceId) {
        return myVariableMapper.getTitle(processInstanceId);
    }

}
