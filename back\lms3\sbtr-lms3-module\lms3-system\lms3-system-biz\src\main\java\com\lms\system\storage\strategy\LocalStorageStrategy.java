package com.lms.system.storage.strategy;

import com.lms.common.config.LMSConfiguration;
import com.lms.common.enums.StorageTypeEnum;
import com.lms.common.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 本地文件存储策略实现
 */
@Slf4j
@Component("localStorageStrategy")
public class LocalStorageStrategy extends AbstractFileStorageStrategy {
    
    @Autowired
    private LMSConfiguration lmsConfiguration;
    
    @Override
    protected StorageTypeEnum getStorageTypeEnum() {
        return StorageTypeEnum.LOCAL;
    }
    
    @Override
    public String upload(MultipartFile file) throws Exception {
        return upload("default", file);
    }
    
    @Override
    public String upload(String bucketName, MultipartFile file) throws Exception {
        validateFile(file);
        
        String objectName = generateUniqueFileName(file.getOriginalFilename());
        String storePath = buildStorePath(bucketName, objectName);
        
        try {
            File targetFile = new File(storePath);
            
            // 确保目录存在
            File parentDir = targetFile.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 保存文件
            file.transferTo(targetFile);
            
            logOperation("文件上传", objectName, true, "保存到路径: " + storePath);
            return objectName;
            
        } catch (Exception e) {
            logOperation("文件上传", objectName, false, e.getMessage());
            throw new RuntimeException("本地文件上传失败", e);
        }
    }
    
    @Override
    public Map<String, String> uploadFile(MultipartFile file, String objectName) throws Exception {
        validateFile(file);
        
        if (StringUtils.isEmpty(objectName)) {
            objectName = generateUniqueFileName(file.getOriginalFilename());
        }
        
        String storePath = buildStorePath("default", objectName);
        
        try {
            File targetFile = new File(storePath);
            
            // 确保目录存在
            File parentDir = targetFile.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 保存文件
            file.transferTo(targetFile);
            
            String fileUrl = generateFileUrl("default", objectName);
            
            Map<String, String> result = new HashMap<>();
            result.put("fileName", objectName);
            result.put("fileUrl", fileUrl);
            result.put("fileSize", String.valueOf(file.getSize()));
            result.put("contentType", file.getContentType());
            result.put("filePath", storePath);
            
            logOperation("文件上传", objectName, true, "返回详细信息");
            return result;
            
        } catch (Exception e) {
            logOperation("文件上传", objectName, false, e.getMessage());
            throw new RuntimeException("本地文件上传失败", e);
        }
    }
    
    @Override
    public void uploadLocalFile(String fileLocation, String bucketName, String objectName) throws Exception {
        if (StringUtils.isEmpty(fileLocation) || StringUtils.isEmpty(objectName)) {
            throw new IllegalArgumentException("文件路径和对象名称不能为空");
        }
        
        File sourceFile = new File(fileLocation);
        if (!sourceFile.exists()) {
            throw new IllegalArgumentException("源文件不存在: " + fileLocation);
        }
        
        String targetPath = buildStorePath(bucketName, objectName);
        
        try {
            File targetFile = new File(targetPath);
            
            // 确保目录存在
            File parentDir = targetFile.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 复制文件
            Files.copy(sourceFile.toPath(), targetFile.toPath());
            
            logOperation("本地文件上传", objectName, true, "从路径: " + fileLocation);
            
        } catch (Exception e) {
            logOperation("本地文件上传", objectName, false, e.getMessage());
            throw new RuntimeException("本地文件复制失败", e);
        }
    }
    
    @Override
    public void download(String fileName, HttpServletResponse response) throws Exception {
        download("default", fileName, response);
    }
    
    @Override
    public void download(String bucketName, String fileName, HttpServletResponse response) throws Exception {
        if (StringUtils.isEmpty(fileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        
        String filePath = buildStorePath(bucketName, fileName);
        File file = new File(filePath);
        
        if (!file.exists()) {
            throw new FileNotFoundException("文件不存在: " + fileName);
        }
        
        try (FileInputStream inputStream = new FileInputStream(file);
             OutputStream outputStream = response.getOutputStream()) {
            
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            response.setContentLengthLong(file.length());
            
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
            
            logOperation("文件下载", fileName, true, "从路径: " + filePath);
            
        } catch (Exception e) {
            logOperation("文件下载", fileName, false, e.getMessage());
            throw new RuntimeException("本地文件下载失败", e);
        }
    }
    
    @Override
    public InputStream download(String fileName) throws Exception {
        return download("default", fileName);
    }
    
    @Override
    public InputStream download(String bucketName, String fileName) throws Exception {
        if (StringUtils.isEmpty(fileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        
        String filePath = buildStorePath(bucketName, fileName);
        File file = new File(filePath);
        
        if (!file.exists()) {
            throw new FileNotFoundException("文件不存在: " + fileName);
        }
        
        try {
            FileInputStream inputStream = new FileInputStream(file);
            logOperation("文件流下载", fileName, true, "从路径: " + filePath);
            return inputStream;
        } catch (Exception e) {
            logOperation("文件流下载", fileName, false, e.getMessage());
            throw new RuntimeException("本地文件下载失败", e);
        }
    }
    
    @Override
    public boolean checkFileIsExist(String fileName) {
        return checkFileIsExist("default", fileName);
    }
    
    @Override
    public boolean checkFileIsExist(String bucketName, String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return false;
        }
        
        String filePath = buildStorePath(bucketName, fileName);
        File file = new File(filePath);
        return file.exists() && file.isFile();
    }
    
    @Override
    public boolean deleteFile(String fileName) {
        return deleteFile("default", fileName);
    }
    
    @Override
    public boolean deleteFile(String bucketName, String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return false;
        }
        
        String filePath = buildStorePath(bucketName, fileName);
        File file = new File(filePath);
        
        try {
            if (file.exists() && file.isFile()) {
                boolean deleted = file.delete();
                logOperation("文件删除", fileName, deleted, "从路径: " + filePath);
                return deleted;
            }
            return false;
        } catch (Exception e) {
            logOperation("文件删除", fileName, false, e.getMessage());
            return false;
        }
    }
    
    @Override
    public boolean bucketExists(String bucketName) {
        if (StringUtils.isEmpty(bucketName)) {
            return false;
        }
        
        String bucketPath = buildBucketPath(bucketName);
        File bucketDir = new File(bucketPath);
        return bucketDir.exists() && bucketDir.isDirectory();
    }
    
    @Override
    public boolean makeBucket(String bucketName) {
        if (StringUtils.isEmpty(bucketName)) {
            return false;
        }
        
        try {
            String bucketPath = buildBucketPath(bucketName);
            File bucketDir = new File(bucketPath);
            
            if (!bucketDir.exists()) {
                boolean created = bucketDir.mkdirs();
                logOperation("存储桶创建", bucketName, created, "路径: " + bucketPath);
                return created;
            }
            return true;
        } catch (Exception e) {
            logOperation("存储桶创建", bucketName, false, e.getMessage());
            return false;
        }
    }
    
    @Override
    public String getPreviewUrl(String fileName) {
        return getPreviewUrl("default", fileName);
    }
    
    @Override
    public String getPreviewUrl(String bucketName, String fileName) {
        return generateFileUrl(bucketName, fileName);
    }
    
    @Override
    public String generateFileUrl(String bucketName, String objectName) {
        if (StringUtils.isEmpty(bucketName) || StringUtils.isEmpty(objectName)) {
            return "";
        }
        
        try {
            // 生成相对于web根目录的URL
            String relativePath = String.format("files/%s/%s", bucketName, objectName);
            return "/" + relativePath;
        } catch (Exception e) {
            log.error("生成文件URL出错: {}", e.getMessage());
            return String.format("/files/%s/%s", bucketName, objectName);
        }
    }
    
    /**
     * 构建存储路径
     * 
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 完整存储路径
     */
    private String buildStorePath(String bucketName, String objectName) {
        String basePath = getBasePath();
        return Paths.get(basePath, bucketName, objectName).toString();
    }
    
    /**
     * 构建存储桶路径
     * 
     * @param bucketName 存储桶名称
     * @return 存储桶路径
     */
    private String buildBucketPath(String bucketName) {
        String basePath = getBasePath();
        return Paths.get(basePath, bucketName).toString();
    }
    
    /**
     * 获取基础存储路径
     * 
     * @return 基础存储路径
     */
    private String getBasePath() {
        String storagePath = lmsConfiguration.getStoragepath();
        if (StringUtils.isEmpty(storagePath)) {
            storagePath = System.getProperty("user.dir") + File.separator + "storage";
        }
        return storagePath;
    }
}
