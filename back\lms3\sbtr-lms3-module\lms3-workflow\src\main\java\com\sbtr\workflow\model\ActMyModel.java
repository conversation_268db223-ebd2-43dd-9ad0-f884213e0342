package com.sbtr.workflow.model;

import cn.ewsd.common.model.MCoreBase;

import javax.persistence.Table;

/**
 * 流程表单模型
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-21 02:37:50
 */
@Table(name = "act_my_model")
public class ActMyModel extends MCoreBase {
    private static final long serialVersionUID = 1L;

    /**
     * 模型id（关联act_re_model）
     */
    private String actDeModelId;

    /**
     * 表单设计
     */
    private String formDesign;

    /**
     * app 表单设计
     */
    private String appPagePath;

    /**
     * 流程设计
     */
    private String flowDesign;

    /**
     * 模型key
     */
    private String actDeModelKey;

    /**
     * 数据表名
     */
    private String formTableName;

    /**
     * 表属性
     */
    private String formModel;

    /**
     * 模型name
     */
    private String actDeModelName;

    /**
     * 流程定义Id
     */
    private String procdefId;

    /**
     * 判断是自己写的表单还是通过拖动出来的表单  1自己写的表单  2拖动设计的
     */
    private String modelType;

    /**
     * 启动权限类型
     */
    private String  permissionType;

    /**
     * 启动权限值
     */
    private String  permissionValue;

    /**
     * 是否开启手写签名
     */
    private String  sign;

    /**
     * 表单uuid
     */
    private String  formUuid;

    public String getAppPagePath() {
        return appPagePath;
    }

    public void setAppPagePath(String appPagePath) {
        this.appPagePath = appPagePath;
    }

    public String getFormUuid() {
        return formUuid;
    }

    public void setFormUuid(String formUuid) {
        this.formUuid = formUuid;
    }

    public String getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(String permissionType) {
        this.permissionType = permissionType;
    }

    public String getPermissionValue() {
        return permissionValue;
    }

    public void setPermissionValue(String permissionValue) {
        this.permissionValue = permissionValue;
    }

    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public String getProcdefId() {
        return procdefId;
    }

    public void setProcdefId(String procdefId) {
        this.procdefId = procdefId;
    }

    public String getActDeModelName() {
        return actDeModelName;
    }

    public void setActDeModelName(String actDeModelName) {
        this.actDeModelName = actDeModelName;
    }

    public String getFormModel() {
        return formModel;
    }

    public void setFormModel(String formModel) {
        this.formModel = formModel;
    }

    /**
     * 设置：模型id（关联act_re_model）
     */
    public void setActDeModelId(String actDeModelId) {
        this.actDeModelId = actDeModelId;
    }

    /**
     * 获取：模型id（关联act_re_model）
     */
    public String getActDeModelId() {
        return actDeModelId;
    }

    /**
     * 设置：表单设计
     */
    public void setFormDesign(String formDesign) {
        this.formDesign = formDesign;
    }

    /**
     * 获取：表单设计
     */
    public String getFormDesign() {
        return formDesign;
    }

    /**
     * 设置：流程设计
     */
    public void setFlowDesign(String flowDesign) {
        this.flowDesign = flowDesign;
    }

    /**
     * 获取：流程设计
     */
    public String getFlowDesign() {
        return flowDesign;
    }

    /**
     * 设置：模型key
     */
    public void setActDeModelKey(String actDeModelKey) {
        this.actDeModelKey = actDeModelKey;
    }

    /**
     * 获取：模型key
     */
    public String getActDeModelKey() {
        return actDeModelKey;
    }

    /**
     * 设置：数据库名
     */
    public void setFormTableName(String formTableName) {
        this.formTableName = formTableName;
    }

    /**
     * 获取：数据库名
     */
    public String getFormTableName() {
        return formTableName;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}
