package com.lms.common.config;

import com.antgroup.tugraph.TuGraphDbRpcClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class TuGraphConfig {

    @Value("${tugraph.rpc}")
    private String TuGraph_RPC;

    @Value("${tugraph.user}")
    private String TuGraph_RPC_USER;

    @Value("${tugraph.password}")
    private String TuGraph_RPC_PASSWORD;

    /**
     * 构造函数
     */
    @Bean
    public TuGraphDbRpcClient getTuGraphDbRpcClient() throws Exception {
        return new TuGraphDbRpcClient(TuGraph_RPC, TuGraph_RPC_USER, TuGraph_RPC_PASSWORD);
    }
}

