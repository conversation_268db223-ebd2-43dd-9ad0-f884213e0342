package com.lms.system.feign.fallback;

import com.lms.common.feign.dto.Setting;
import com.lms.common.feign.dto.Users;
import com.lms.common.model.JwtUser;
import com.lms.common.model.Result;
import com.lms.system.feign.api.LmsSystemApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class LmsSystemApiFallbackFactory implements FallbackFactory<LmsSystemApi> {

    private static final Logger log = LoggerFactory.getLogger(LmsSystemApiFallbackFactory.class);

    @Override
    public LmsSystemApi create(Throwable throwable) {
        log.error("LmsSystemApi模块服务调用失败:{}", throwable.getMessage());

        return new LmsSystemApi() {

            @Override
            public Result<Users> saveUser(Users users) {
                return null;
            }

            @Override
            public Result saveAllUser(List<Users> users) {
                return null;
            }

            @Override
            public Result batchDeleteByPersonId(String ids) {
                return null;
            }

            @Override
            public Result<JwtUser> getUserInfo(String userid) {
                return null;
            }

            @Override
            public Result<JwtUser> login(String userName, String password, String ipaddr, String clientagent) {
                return null;
            }

            @Override
            public Result<Users> getUsersByPersonId(String PersonId) {
                return null;
            }

            @Override
            public Result<Users> getUsersByUserName(String userName) {
                return null;
            }
        };
    }
}
