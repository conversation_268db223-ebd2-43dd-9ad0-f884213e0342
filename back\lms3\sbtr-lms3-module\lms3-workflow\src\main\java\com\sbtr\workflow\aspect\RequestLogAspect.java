//package com.sbtr.workflow.aspect;
//
//import cn.ewsd.common.bean.Audience;
//import cn.ewsd.common.utils.CookieUtil;
//import cn.ewsd.common.utils.JwtUtil;
//import cn.ewsd.common.utils.StringUtils;
//import cn.hutool.core.convert.Convert;
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.extra.servlet.ServletUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.sbtr.workflow.annotation.Log;
//import com.sbtr.workflow.client.SystemClient;
//import com.sbtr.workflow.constants.RequestLogType;
//import io.jsonwebtoken.Claims;
//import org.aspectj.lang.JoinPoint;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.Signature;
//import org.aspectj.lang.annotation.*;
//import org.aspectj.lang.reflect.MethodSignature;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//import org.springframework.web.context.request.RequestAttributes;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.lang.reflect.Method;
//import java.util.HashMap;
//import java.util.Map;
//
//
///**
// * 切面处理系统日志
// *
// * <AUTHOR>
// * @Version 5.4.21
// * @Email <EMAIL>
// * @Date 2022-06-16 13:31:44
// */
//@Aspect
//@Component
//@Order(1)
//public class RequestLogAspect {
//
//    private static final Logger logger = LoggerFactory.getLogger(RequestLogAspect.class);
//
//
//    @Pointcut("execution(public * com.sbtr.*.controller.*.*(..))")
//    public void webLog() {
//    }
//
//    @Autowired
//    private Audience audience;
//
//
//    //@Autowired
//    private SystemClient systemClient;
//
//
//    @Before("webLog()")
//    public void doBefore(JoinPoint joinPoint) throws Throwable {
//    }
//
//    @AfterReturning(value = "webLog()", returning = "ret")
//    public void doAfterReturning(Object ret) throws Throwable {
//    }
//
//    @Around("webLog()")
//    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
//        long startTime = System.currentTimeMillis();
//        //执行方法
//        Object result = joinPoint.proceed();
//        //执行耗时(毫秒)
//        long time = System.currentTimeMillis() - startTime;
//        //保存日志
//        saveSysLog(joinPoint, time, result);
//        return result;
//    }
//
//    private void saveSysLog(ProceedingJoinPoint joinPoint, long time, Object result) {
//        //获取当前请求对象
//        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//        HttpServletRequest request = attributes.getRequest();
//        //记录请求信息(通过Logstash传入Elasticsearch)
//        try {
//            Signature signature = joinPoint.getSignature();
//            MethodSignature methodSignature = (MethodSignature) signature;
//            Method method = methodSignature.getMethod();
//            Log requestLogAnnotation = method.getAnnotation(Log.class);
//            if (null != requestLogAnnotation) {
//                Map<String, Object> map = new HashMap<>();
//                Claims var12 = JwtUtil.parseJWT(getToken(request), this.audience.getBase64Secret());
//                if(ObjectUtil.isEmpty(var12)){
//                    return;
//                }
//                map.put("operationDesc", requestLogAnnotation.title());
//                map.put("userNameId", Convert.toStr(var12.get("userNameId")));
//                map.put("userName", Convert.toStr(var12.get("userName")));
//                map.put("operationIp", ServletUtil.getClientIP(request));
//                map.put("operationUserAgent", request.getHeader("User-Agent"));
//                map.put("operationUrl", Convert.toStr(request.getRequestURL()));
//                map.put("operationMethodName", joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName());
//                map.put("operationMethod", request.getMethod());
//                map.put("operationArgs", Convert.toStr(joinPoint.getArgs()));
////                map.put("operationArgs",HttpUtil.toParams(request.getParameterMap()));
//                map.put("operationTime", time + "ms");
//                map.put("operationType", RequestLogType.SUCCESS_SERVER_NAME);
//                map.put("operationResponeArgs", Convert.toStr(JSONObject.toJSON(result)));
//                map.put("operationModular", "sbtr-workflow");
//                systemClient.save(map);
//            }
//
//        } catch (Exception exception) {
//            logger.error("记录日志失败={}", exception.getMessage());
//        }
//    }
//
//    /**
//     * 异常返回通知，用于拦截异常日志信息 连接点抛出异常后执行
//     *
//     * @param joinPoint 切入点
//     * @param e         异常信息
//     */
//    @AfterThrowing(pointcut = "webLog() ", throwing = "e")
//    public void saveServiceExceptionLog(JoinPoint joinPoint, Throwable e) {
//        // 获取RequestAttributes
//        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
//        // 从获取RequestAttributes中获取HttpServletRequest的信息
//        HttpServletRequest request = (HttpServletRequest) requestAttributes
//                .resolveReference(RequestAttributes.REFERENCE_REQUEST);
//        try {
//
//            // 从切面织入点处通过反射机制获取织入点处的方法
//            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
//            // 获取切入点所在的方法
//            Method method = signature.getMethod();
//            Log requestLogAnnotation = method.getAnnotation(Log.class);
//            if (null != requestLogAnnotation) {
//                Map<String, Object> map = new HashMap<>();
//                // 获取请求的类名
//                String className = joinPoint.getTarget().getClass().getName();
//                // 获取请求的方法名
//                String methodName = method.getName();
//                Claims var12 = JwtUtil.parseJWT(getToken(request), this.audience.getBase64Secret());
//                if(ObjectUtil.isEmpty(var12)){
//                    return;
//                }
//                map.put("operationDesc", requestLogAnnotation.value());
//                map.put("userNameId", Convert.toStr(var12.get("userNameId")));
//                map.put("userName", Convert.toStr(var12.get("userName")));
//                map.put("operationIp", ServletUtil.getClientIP(request));
//                map.put("operationUserAgent", request.getHeader("User-Agent"));
//                map.put("operationUrl", Convert.toStr(request.getRequestURL()));
//                map.put("operationMethodName", joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName());
//                map.put("operationMethod", request.getMethod());
//                map.put("operationArgs", getParams(joinPoint));
////           map.put("operationTime(time + "ms");
//                map.put("operationType", RequestLogType.ERROR_SERVER_NAME);
//                map.put("operationResponeArgs", stackTraceToString(e.getClass().getName(), e.getMessage(), e.getStackTrace()));
//                map.put("operationModular", "sbtr-mdata");
//                systemClient.save(map);
//            }
//
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//    }
//
//
//
//    /**
//     * 转换异常信息为字符串
//     *
//     * @param exceptionName    异常名称
//     * @param exceptionMessage 异常信息
//     * @param elements         堆栈信息
//     */
//    public String stackTraceToString(String exceptionName, String exceptionMessage, StackTraceElement[] elements) {
//        StringBuffer strbuff = new StringBuffer();
//        for (StackTraceElement stet : elements) {
//            strbuff.append(stet + "\n");
//        }
//        String message = exceptionName + ":" + exceptionMessage + "\n\t" + strbuff.toString();
//        return message;
//    }
//
//    //请求参数
//    private String getParams(JoinPoint joinPoint) {
//        String params = "";
//        if (joinPoint.getArgs() != null && joinPoint.getArgs().length > 0) {
//            for (int i = 0; i < joinPoint.getArgs().length; i++) {
//                Object arg = joinPoint.getArgs()[i];
//                if ((arg instanceof HttpServletResponse) || (arg instanceof HttpServletRequest)
//                        || (arg instanceof MultipartFile) || (arg instanceof MultipartFile[])) {
//                    continue;
//                }
//                try {
//                    params += JSONObject.toJSONString(joinPoint.getArgs()[i]);
//                } catch (Exception e1) {
//
//                }
//            }
//        }
//        return params;
//    }
//
//    /**
//     * 获取Token
//     */
//    private String getToken(HttpServletRequest request) {
//        String var7 = CookieUtil.getCookieByName(request, ConstParamUtil.X_ACCESS_TOKEN);
//        String paramToken = request.getParameter(ConstParamUtil.X_ACCESS_TOKEN);
//        if (!StringUtils.isNullOrEmpty(paramToken)) {
//            var7 = paramToken;
//        }
//        if (StringUtils.isNullOrEmpty(var7)) {
//            var7 = request.getHeader(ConstParamUtil.X_ACCESS_TOKEN);
//        }
//        return var7;
//    }
//}
