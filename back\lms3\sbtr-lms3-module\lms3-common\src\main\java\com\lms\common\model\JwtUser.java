package com.lms.common.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
@Data
@Component
public class JwtUser extends BaseModel {
    private String userid;
    private String username;
    private String personid;
    private String personname;
    private String departmentname;
    private String departid;
    private String specialityid;
    private String personlevelid;
    private String dutyid;
    private String equipmentid;
    private String teachingmodel;
    private String managemodel;
    private String persontype;
    private Integer usertype;
    private String logindate;
    private String personimage;
    private String cardNum;
    private String roleid;
    private List<String> roles;
    private String rolename;
    @JsonIgnore
    private String password;
    private List<String> rights = new ArrayList<String>();
    private String language;
    private Integer slevel;
    private boolean islocked;
    private List<String> currentusercomponentid;
//    public JwtUser(Users users, Person person, Department departInfo) {
//        if (users == null)
//            return;
//        this.userid = users.getId();
//        this.username = users.getName();
//        this.personid = person.getId();
//        this.personname = person.getName();
//        this.specialityid = person.getSpecialityid();
//        this.dutyid = person.getDuty();
//        this.equipmentid = person.getEquipmentid();
//        if (departInfo != null) {
//            this.departid = departInfo.getId();
//            this.departmentname = departInfo.getName();
//        }
//        this.persontype = person.getPersontype();
//        this.logindate = DateHelper.getCurrentDate();
//        this.cardNum = person.getCardNum();
//        this.roleid = StringHelper.isEmpty(users.getRoleid())?person.getPersontype().split(",")[0]:users.getRoleid();
//    }

}
