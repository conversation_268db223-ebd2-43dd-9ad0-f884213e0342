package com.sbtr.workflow.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


public class OaLeaveCommon {
    //任务Id
    private String taskId;
    //任务Name
    private String taskName;
    //业务Id
    private String businessKey;
    //实例Id
    private String processInstanceId;
    //定义Id
    private String processDefinitionId;
    //以下业务字段
    private String startTime;
    private String assage;
    private String title;
    private String days;
    private String beginTime;
    private String endTime;
    private String reason;
    private String item;


    private String serchName;
    private String serchTile;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date serchTime;
    private String serchRemake;

    OaLeave oaLeave;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getAssage() {
        return assage;
    }

    public void setAssage(String assage) {
        this.assage = assage;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDays() {
        return days;
    }

    public void setDays(String days) {
        this.days = days;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getItem() {
        return item;
    }

    public void setItem(String item) {
        this.item = item;
    }

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getSerchName() {
        return serchName;
    }

    public void setSerchName(String serchName) {
        this.serchName = serchName;
    }

    public String getSerchTile() {
        return serchTile;
    }

    public void setSerchTile(String serchTile) {
        this.serchTile = serchTile;
    }

    public Date getSerchTime() {
        return serchTime;
    }

    public void setSerchTime(Date serchTime) {
        this.serchTime = serchTime;
    }

    public String getSerchRemake() {
        return serchRemake;
    }

    public void setSerchRemake(String serchRemake) {
        this.serchRemake = serchRemake;
    }

    public OaLeave getOaLeave() {
        return oaLeave;
    }

    public void setOaLeave(OaLeave oaLeave) {
        this.oaLeave = oaLeave;
    }
}
