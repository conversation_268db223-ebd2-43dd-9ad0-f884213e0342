package com.lms.base.scopecourseware.rtefile;

import lms.rte.CAM.Model.SeqActivityTree;
import lms.rte.Common.DataProviderType;
import lms.rte.DataManager.RTEDataManager;
import org.apache.commons.logging.LogFactory;

public class RteTreeTask implements ITask {
	// / <summary>
	// / 人员ID
	// / </summary>
	private String m_Userid;

	// / <summary>
	// / 课程ID
	// / </summary>
	private String m_CourseId;

	// / <summary>
	// / 学习模式
	// / </summary>
	private DataProviderType m_ProviderType;

	// / <summary>
	// / 是否复习
	// / </summary>
	private boolean m_Review;

	// / <summary>
	// / 树数据
	// / </summary>
	private SeqActivityTree m_TreeData;

	public RteTreeTask(String userid, String courseId,
			DataProviderType providerType, boolean review,
			SeqActivityTree treeData) {
		if (treeData == null)
			return;
		m_CourseId = courseId;
		m_ProviderType = providerType;
		m_Userid = userid;
		m_Review = review;
		m_TreeData = treeData;
	}

	public boolean Excute() {
		boolean result = RTEDataManager.getInstance().SaveLearnerActivityTree(
				m_TreeData, m_ProviderType);
		org.apache.commons.logging.Log log = LogFactory
				.getLog(RteTreeTask.class);
		if (result) {
			log.info("课程：" + m_TreeData.getCourseId() + "用户:"
					+ m_TreeData.getLearnerId() + "树数据保存任务已经成功执行");
		} else {
			log.info("课程：" + m_TreeData.getCourseId() + "用户:"
					+ m_TreeData.getLearnerId() + "树数据保存任务已经执行，但是执行出现错误");

		}
		return result;
	}

}
