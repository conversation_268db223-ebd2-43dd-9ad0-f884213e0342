package com.lms.base.scopecourseware.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lms.base.courseware.service.CoursewareService;
import com.lms.base.feign.model.Courseware;
import com.lms.base.feign.model.ScopeCourseware;
import com.lms.base.scopecourseware.rtefile.RteContent;
import com.lms.base.scopecourseware.rtefile.RteTree;
import com.lms.base.scopecourseware.rtefile.Session;
import com.lms.base.scopecourseware.rtefile.TreeProvider;
import com.lms.base.scopecourseware.rtefile.ViewData;
import com.lms.base.scopecourseware.service.ScopeCoursewareService;
import com.lms.common.config.LMSConfiguration;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.dao.query.Parameter;
import com.lms.common.model.Result;
import com.lms.common.util.ContextUtil;
import com.lms.system.feign.api.AttachApi;
import com.lms.system.feign.model.Attach;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lms.rte.CAM.Model.SeqActivityTree;
import lms.rte.Common.DataProviderType;
import lms.rte.Common.LaunchData;
import lms.rte.DataManager.RTEDataManager;
import lms.rte.Sequencer.SequenceNavigate.LaunchInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR> BBS主题标签信息操作控制层
 */
@RestController
@RequestMapping("/scopecourseware")
@Api(value = "scorm课件管理", tags = "scorm课件管理")
public class ScopeCoursewareController extends BaseController<ScopeCourseware> {


    @Resource
    private ScopeCoursewareService scopecoursewareService;

    @Resource
    private CoursewareService coursewareService;

    @Resource
    private AttachApi attachApi;

    @Resource
    private LMSConfiguration lmsConfiguration;

    protected static final Logger logger = LoggerFactory.getLogger(ScopeCoursewareController.class);

    private Session session = new Session();

    private ViewData viewdata = new ViewData();

    @RequestMapping(value = "/initScormCourseware", method = RequestMethod.POST)
    @ApiOperation(value = "初始化scorm课件信息", httpMethod = "POST")
    public Result initScormCourseware(@RequestParam("coursewareId") String coursewareId) {
        boolean result = false;
        SeqActivityTree activityTree = RTEDataManager.getInstance().GetCommonActivityTree(coursewareId, DataProviderType.Normal);
        if (activityTree != null) {
            activityTree.setLearnerId(ContextUtil.getPersonId());
            activityTree.setCourseId(coursewareId);
            activityTree.setOriginalCoursewareID(coursewareId);
            result = RTEDataManager.getInstance().LearnerRegisterCourse(activityTree, DataProviderType.Free);
        }
        if (result) {
            return Result.OK();
        } else {
            return Result.error("Scorm课件初始化失败！");
        }
    }

    /* 右边内容接口连接处，返回是一个URL */
    @RequestMapping(value = {"/getConcent"}, method = RequestMethod.GET)
    @ApiOperation(value = "获取scorm课件信息,返回是一个URL", httpMethod = "GET")
    public Result findscopecoursewareconcent(@RequestBody JSONObject jsonObject) throws Exception {
        PageInfo pageInfo = super.getPageInfo(jsonObject);
        List<Parameter> filterCondition = pageInfo.getParameters();
        String courseID = "", userid = "", treeNodeId = "", buttonName = "";
        for (int i = 0; i < filterCondition.size(); i++) {
            String str = filterCondition.get(i).getKeys().get(0);
            if (str.contains("courseID")) {
                courseID = (String) filterCondition.get(i).getValues().get(0);
            } else if (str.contains("userId")) {
                userid = (String) filterCondition.get(i).getValues().get(0);
            } else if (str.contains("treeNodeId")) {
                treeNodeId = (String) filterCondition.get(i).getValues().get(0);
            } else if (str.contains("buttonName")) {
                buttonName = (String) filterCondition.get(i).getValues().get(0);
            }
        }

        Courseware courseware = coursewareService.getById(courseID);
        Attach attach = attachApi.get(courseware.getAttachid()).getResult();
        String path = lmsConfiguration.getViewurl() + attach.getFiledir();
        String state = "1";// 课件类型，1:表示是scorm，0:表示非scorm
        session.setState(state);
        session.setCourseId(courseID);
        DataProviderType stateType = state == "1" ? DataProviderType.Free
                : DataProviderType.Normal;
        boolean review = false;
        if (stateType == DataProviderType.Free || review) {
            TreeProvider treeProvider = new TreeProvider();
            treeProvider.RemoveCacheTree(userid, courseID, stateType, review);
            LaunchData launch = rteConcent(path, treeNodeId, buttonName, userid);
            if (!launch.getLocation().equals("")) {
                return Result.OK(path + "/" + launch.getLocation());
            } else {
                return Result.error("获取路径信息失败！");
            }
        }
        return Result.OK();
    }

    /* 左边树接口连接处，返回是树结构内容 */
    @RequestMapping(value = {"/get"}, method = RequestMethod.POST)
    @ApiOperation(value = "获取scorm课件信息,返回是树结构内容", httpMethod = "POST")
    public Result getscopecourseware(JSONObject jsonObject)
            throws Exception {
        PageInfo pageInfo = super.getPageInfo(jsonObject);
        List<Parameter> filterCondition = pageInfo.getParameters();
        String courseID = "", userid = "";
        for (int i = 0; i < filterCondition.size(); i++) {
            String str = filterCondition.get(i).getKeys().get(0);
            if (str.contains("courseID")) {
                courseID = (String) filterCondition.get(i).getValues().get(0);
            } else if (str.contains("userId")) {
                userid = (String) filterCondition.get(i).getValues().get(0);
            }
        }
        //String path = LMSConfiguration.GetScormViewUrlPath() + "/" + courseID;// scorm文件的绝对路径
        //String path = "C:\\scormTempPath\\common" + "\\" + courseID;
        String scormStoragePath = lmsConfiguration.getScormStoragePath();
        String path = scormStoragePath + "common/" + courseID;
        String state = "1";// 课件类型，1:表示是scorm，0:表示非scorm
        session.setState(state);
        session.setCourseId(courseID);
        DataProviderType stateType = state == "1" ? DataProviderType.Free
                : DataProviderType.Normal;
        boolean review = false;
        JSONArray resArray = new JSONArray();
        if (stateType == DataProviderType.Free || review) {
            TreeProvider treeProvider = new TreeProvider();
            treeProvider.RemoveCacheTree(userid, courseID, stateType, review);
            rteConcent(path, "", "", userid);
            resArray = rteTree(path, userid);
        }
        return Result.OK(resArray);
    }

    @RequestMapping(value = {"/setValue"}, method = RequestMethod.POST)
    @ApiOperation(value = "设置scorm课件信息", httpMethod = "POST")
    public Result setValue(@RequestParam("element") String element,
                           @RequestParam("value") double value) {
        System.out.println(element);
        System.out.println(value);
        return Result.OK();    //返回前端的状态代码statusCode：1表示正确
    }

    private LaunchData rteConcent(String path, String treeNodeName,
                                  String buttonName, String userid) throws Exception {
        String courseID = session.getCourseId();
        String state = session.getState();
        DataProviderType stateType = state == "1" ? DataProviderType.Free : DataProviderType.Normal;
        viewdata.setServerHost(lmsConfiguration.getLocalhost());// 格式：localhost:8081/

        RteContent rteContent = new RteContent(userid, courseID, stateType,
                treeNodeName, false);
        rteContent.RegisteTreeProvider(new TreeProvider());
        LaunchInfo actual = rteContent.DoSequence(buttonName, courseID);

        viewdata.setOrginalCourseId(courseID);
        viewdata.setScormStoragePath("ScormStoragePath");
        // host + "ScormStoragePath/" + ViewData["OrginalCourseId"] + "/" +
        // launchData.Location + launchData.Parameters
        LaunchData launch = new LaunchData();
        if (actual.getActivityId() != null)// 获取右边展示内容
        {
            launch = RTEDataManager.getInstance().GetItemInfo(
                    actual.getActivityId(), courseID,
                    DataProviderType.Normal);
            viewdata.setLaunchData(launch);
            viewdata.setContentUrl(String.format("{0}/{1}/{2}{3}",
                    path + "ScormStoragePath",
                    viewdata.getOrginalCourseId(), launch.getLocation(),
                    launch.getParameters()));
        }
        return launch;
    }

    public JSONArray rteTree(String path, String userid) throws Exception {
        String courseID = session.getCourseId();
        String state = session.getState();
        viewdata.setServerHost(path);
        DataProviderType stateType = state == "1" ? DataProviderType.Free
                : DataProviderType.Normal;
        RteTree rteTree = new RteTree(userid, courseID, stateType, false);
        rteTree.RegisteTreeProvider(new TreeProvider());
        JSONArray scriptContent = new JSONArray();
        scriptContent = rteTree.GetScriptContent();
        if (scriptContent != null && !scriptContent.equals("")) {// 树构造成功
            return scriptContent;
        } else {
            JSONArray content = new JSONArray();
            return content;
        }
    }
}
