package com.lms.system.login.controller;

import com.alibaba.fastjson.JSONObject;
import com.lms.common.controller.BaseController;
import com.lms.common.model.JwtUser;
import com.lms.common.model.Result;
import com.lms.common.util.*;
import com.lms.system.log.service.LogService;
import com.lms.system.users.service.UsersService;
import io.jsonwebtoken.Claims;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Api(value = "系统登录类",tags = {"系统登录类"})
@RestController
@RefreshScope
public class SysLoginController extends BaseController {
    @Resource
    private UsersService usersService;
    @Resource
    private LogService logService;
    @Resource
    public ContextUtil contextUtil;

    @Value("${jwt.audience.base64Secret}")
    private String secret;

    /**
     * @MethodName jwtLogin
     * @Description 登录校验
     * <AUTHOR> @ sbtr.com>
     * @Date 2021-1-7 11:46
     */
    @ApiOperation(value = "用户登录")
    @PostMapping("/jwtLogin")
    @ResponseBody
    public Result jwtLogin(@RequestBody JSONObject jsonObject) {
        // 检查数据格式是否正确
        if (!jsonObject.containsKey("username") || !jsonObject.containsKey("password")) {
            return Result.error("登录信息无效，请重新登录！！！");
        }
        //帐号
        String username = jsonObject.getString("username");
        //加密的密码
        String password = jsonObject.getString("password").trim();
        HttpServletRequest request = ServletUtil.getRequest();
        String ipAddr = request.getHeader("ipaddr");
        String clientAgent = request.getHeader("User-Agent");
        Result result = usersService.login(username, password, ipAddr, clientAgent);//校验用户密码，成功后返回用户信息
        if (!result.isSuccess()) {
            return result;
        }
        JwtUser jwtUser = (JwtUser) result.getResult();
        String jwtToken = contextUtil.createJWT(jwtUser);
        //写入登录日志
        logService.saveLogin(jwtUser);
        return Result.OK(jwtToken);
    }

    @ApiOperation(value = "解析用户token")
    @GetMapping("/parseJwt")
    @ResponseBody
    public Result<JwtUser> parseJwt(HttpServletRequest request) {
        String token = StringHelper.null2String(request.getHeader(ConstParamUtil.X_ACCESS_TOKEN));
        //处理头部token
        if (StringHelper.isEmpty(token)) {
            token = StringHelper.null2String(CookieHelper.getCookie(request, ConstParamUtil.X_ACCESS_TOKEN));
        }
        if (StringUtils.isEmpty(token)) {
            return Result.error("token不能为空", null);
        }
        Claims claims = ContextUtil.parseJWT(token, secret);
        if (claims == null) {
            return Result.error("token已过期", null);
        }
        String key = ConstParamUtil.USER_TOKEN_KEY + claims.get("id");
        JwtUser jwtUser = redisUtil.get(key) == null ? new JwtUser() : JSONObject.toJavaObject(redisUtil.getJson(key),JwtUser.class);
        return Result.OK(jwtUser);
    }


}
