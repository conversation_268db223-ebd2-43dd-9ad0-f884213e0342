package com.lms.common.model;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({java.lang.annotation.ElementType.METHOD})
@Documented
public @interface LMSLog
{
  String desc() default "";

  String otype() default "";

  int order() default 0;

  String method() default "";

  String objname() default "";

}
