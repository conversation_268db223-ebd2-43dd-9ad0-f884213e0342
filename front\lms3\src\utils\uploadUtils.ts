import { message } from "ant-design-vue";
import { uploadOssFileDetail, uploadFileChunk } from "@/api/ossController";

/**
 * 判断文件是否为图片类型
 * @param file 文件对象
 * @returns 是否为图片
 */
export const isImage = (file: File): boolean => {
  return file.type.startsWith("image/");
};

/**
 * 判断文件是否为视频类型
 * @param file 文件对象
 * @returns 是否为视频
 */
export const isVideo = (file: File): boolean => {
  return file.type.startsWith("video/");
};

/**
 * 判断文件是否为3D模型类型
 * @param file 文件对象
 * @returns 是否为3D模型
 */
export const is3DModel = (file: File): boolean => {
  const modelExtensions = [
    ".obj",
    ".fbx",
    ".gltf",
    ".glb",
    ".dae",
    ".3ds",
    ".ply",
    ".stl",
    ".vrp",
    ".vrpc"
  ];
  return modelExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
};

/**
 * 判断是否使用分片上传
 * 当文件大于50MB时进行分片上传，无论文件类型
 * @param file 文件对象
 * @returns 是否使用分片上传
 */
export const shouldUseChunkUpload = (file: File): boolean => {
  const fileSizeInMB = file.size / 1024 / 1024;
  return fileSizeInMB > 50;
};

/**
 * 判断是否使用uploadFileChunk轻量化上传接口
 * 当文件类型为vrp或vrpc时使用uploadFileChunk轻量化上传接口
 * @param file 文件对象
 * @returns 是否使用uploadFileChunk接口
 */
export const shouldUseUploadFileChunk = (file: File): boolean => {
  return (
    file.name.toLowerCase().endsWith(".vrp") ||
    file.name.toLowerCase().endsWith(".vrpc")
  );
};

/**
 * 分片上传文件
 * @param file 文件对象
 * @param onProgress 进度回调函数
 * @returns 上传结果
 */
export const uploadFileInChunks = async (
  file: File,
  onProgress?: (progress: number) => void
): Promise<any> => {
  const shardSize = 2 * 1024 * 1024; // 2MB per chunk
  const shardCount = Math.ceil(file.size / shardSize);
  let currentChunk = 0;
  let uploadResult: any = null;

  const uploadChunk = async (chunkIndex: number): Promise<any> => {
    if (chunkIndex >= shardCount) {
      return uploadResult;
    }

    const start = chunkIndex * shardSize;
    const end = Math.min(file.size, start + shardSize);
    const chunk = file.slice(start, end);

    const formData = new FormData();
    formData.append("data", chunk);
    formData.append("lastModified", file.lastModified.toString());
    formData.append("fileName", file.name);
    formData.append("total", shardCount.toString());
    formData.append("index", (chunkIndex + 1).toString());

    try {
      const result = await uploadFileChunk(formData);
      const parsedResult =
        typeof result === "string" ? JSON.parse(result) : result;

      if (parsedResult && parsedResult.mergeOk) {
        // 文件合并完成
        uploadResult = parsedResult;
        return uploadResult;
      } else if (parsedResult && parsedResult.number !== undefined) {
        // 继续上传下一个分片
        currentChunk = parsedResult.number;
        // 更新上传进度
        const progress = Math.round((currentChunk / shardCount) * 100);
        onProgress?.(progress);
        return await uploadChunk(currentChunk);
      } else {
        throw new Error("上传分片失败");
      }
    } catch (error) {
      console.error("分片上传失败:", error);
      throw error;
    }
  };

  return await uploadChunk(currentChunk);
};

/**
 * 普通文件上传
 * @param file 文件对象
 * @returns 上传结果
 */
export const uploadFileNormal = async (file: File): Promise<any> => {
  const form_data = new FormData();
  form_data.append("file", file);
  return await uploadOssFileDetail(form_data);
};

/**
 * 统一文件上传函数
 * 优先判断文件类型：vrp/vrpc文件无论大小都使用uploadFileInChunks
 * 其他文件类型根据大小判断：50MB以上使用新接口（暂时使用uploadFileNormal），50MB以下使用uploadFileNormal
 * @param file 文件对象
 * @param onProgress 进度回调函数
 * @returns 上传结果
 */
export const uploadFile = async (
  file: File,
  onProgress?: (progress: number) => void
): Promise<any> => {
  try {
    let uploadResult: any = null;

    // 首先判断文件类型：vrp或vrpc文件无论大小都使用uploadFileInChunks
    if (shouldUseUploadFileChunk(file)) {
      console.log("vrp/vrpc文件，使用uploadFileInChunks接口:", file.name);
      // uploadResult = await uploadFileInChunks(file, onProgress);
      uploadResult = await uploadFileNormal(file, onProgress);
    } else {
      // 其他文件类型（图片、视频等），根据大小判断
      if (shouldUseChunkUpload(file)) {
        // 文件大于50MB，暂时使用uploadFileNormal接口（临时方案，后续会提供新接口）
        console.log(
          "其他文件类型且大于50MB，暂时使用uploadFileNormal接口:",
          file.name
        );
        uploadResult = await uploadFileNormal(file);
      } else {
        // 文件小于等于50MB，使用uploadFileNormal接口
        console.log(
          "其他文件类型且小于等于50MB，使用uploadFileNormal接口:",
          file.name
        );
        uploadResult = await uploadFileNormal(file);
      }
    }

    return uploadResult;
  } catch (error) {
    console.error("文件上传失败:", error);
    throw error;
  }
};

/**
 * 文件上传前的验证
 * @param file 文件对象
 * @returns 是否通过验证
 */
export const beforeUpload = (file: File): boolean => {
  const isValidType =
    file.type.includes("model") ||
    file.name.endsWith(".vrp") ||
    file.name.endsWith(".vrpc") ||
    file.type.includes("image") ||
    file.type.includes("video");

  if (!isValidType) {
    message.error("请上传有效的3D模型文件！");
    return false;
  }

  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M) {
    message.error("文件大小不能超过 100MB！");
    return false;
  }

  return false; // 阻止自动上传，手动处理
};
