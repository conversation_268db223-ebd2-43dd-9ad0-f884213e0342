<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ActFormConfigureMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sbtr.workflow.model.ActFormConfigure" id="actFormConfigureMap">
        <result property="uuid" column="uuid"/>
        <result property="createTime" column="create_time"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorOrgId" column="creator_org_id"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="nodeFormSavePath" column="node_form_save_path"/>
        <result property="nodeFormPath" column="node_form_path"/>
        <result property="nodeFormUpdatePath" column="node_form_update_path"/>
        <result property="nodeFormEditPath" column="node_form_edit_path"/>
        <result property="puuid" column="puuid"/>
        <result property="tablename" column="tablename"/>
        <result property="primarykey" column="primarykey"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid
        ,  	  	     			 create_time
		  ,  	  	     			 creator
		  ,  	  	     			 creator_id
		  ,  	  	     			 creator_org_id
		  ,  	  	     			 modifier_id
		  ,  	  	     			 modifier
		  ,  	  	     			 modify_time
		  ,  	  	     			 node_form_save_path
		  ,  	  	     			 node_form_path
		  ,  	  	     			 node_form_update_path
		  ,  	  	     			 node_form_edit_path
		  ,  	  	     			 puuid
		  ,  	  	     			 name
		  ,  	  	     			 status
		  ,  	  	     			 code
		  ,  	  	     			 node_form_complete_path
		  ,  	  	     			 app_page_path
		  ,  	  	     			 tablename
		  ,  	  	     			 primarykey
    </sql>

    <select id="getPageSetMySql" resultType="com.sbtr.workflow.model.ActFormConfigure">
        select
        <include refid="Base_Column_List"></include>
        from act_form_configure
        <where>
            <if test=" puuid != null and  puuid!=''">
                puuid like concat('%',#{puuid},'%') and
            </if>
            <if test=" name != null and  name!=''">
                name like concat('%',#{name},'%') and
            </if>
            <if test=" status != null and  status!=''">
                status = #{status} and
            </if>
            <if test="filterSort != null">
                ${filterSort}
            </if>
        </where>
    </select>

    <select id="getPageSetOracle" resultType="com.sbtr.workflow.model.ActFormConfigure">
        select
        <include refid="Base_Column_List"></include>
        from act_form_configure
        <where>
            <if test=" puuid != null and  puuid!=''">
                puuid like '%'||#{puuid}||'%' and
            </if>
            <if test=" name != null and  name!=''">
                name like '%'||#{name}||'%' and
            </if>
            <if test=" status != null and  status!=''">
                status = #{status} and
            </if>
            <if test="filterSort != null">
                ${filterSort}
            </if>
        </where>
    </select>


    <delete id="executeDeleteBatch">
        delete from act_form_configure where uuid in
        <foreach item="uuid" collection="array" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </delete>

    <update id="updateStatusByUuid">
        update  act_form_configure
        set status = #{status}
        where uuid = #{uuid}
    </update>

</mapper>
