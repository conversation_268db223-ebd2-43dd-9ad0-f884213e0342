package com.lms.base.person.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 教员 导出
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Teacher implements Serializable{

    @Excel(name = "密级", width = 15)
    private String mlevel;

    @Excel(name = "教员名称", width = 15)
    private String name;

    @Excel(name = "教员性别", width = 15)
    private String sex;

    @Excel(name = "生日", width = 15)
    private String birthday;

    @Excel(name = "工作年限", width = 15)
    private String seniority;

    @Excel(name = "毕业院校", width = 15)
    private String graduate;

    @Excel(name = "所学专业", width = 15)
    private String major;

    @Excel(name = "工作单位", width = 15)
    private String departmentname;

    @Excel(name = "岗位", width = 15)
    private String duty;

    @Excel(name = "职务", width = 15)
    private String job;

    @Excel(name = "教学型号列表", width = 40)
    private String modelnum;

    @Excel(name = "教员类别", width = 15)
    private String category;

    @Excel(name = "教员等级", width = 15)
    private String level;

    @Excel(name = "教学课程", width = 15)
    private String course;

    @Excel(name = "专业及主要成果", width = 15)
    private String achievement;

    @Excel(name = "详细课程名称", width = 15)
    private String courseDetailName;

}
