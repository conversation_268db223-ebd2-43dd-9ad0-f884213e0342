<script setup lang="ts">
import type { Organization } from "./data.ts";

import { onMounted, ref } from "vue";

import { message } from "ant-design-vue";

import OrganizationForm from "./modules/form.vue";
import OrganizationTree from "./modules/tree.vue";
import { getDepartmentTree } from "@/api/system.ts";
const organizations = ref<Organization[]>([]);
const selectedOrganization = ref<null | Organization>(null);

const handleSelectOrganization = (organization: null | Organization) => {
  selectedOrganization.value = organization;
};

const handleAddOrganization = (newOrganization: Organization) => {
  if (!newOrganization || typeof newOrganization !== "object") {
    message.error("无效的组织数据");
    return;
  }
  // 如果有上级单位，添加到对应的children中
  if (newOrganization.pid) {
    const addToParent = (orgs: Organization[]): boolean => {
      if (!orgs || !Array.isArray(orgs)) {
        return false;
      }

      for (const org of orgs) {
        if (org.id === newOrganization.pid) {
          if (!org.children) {
            org.children = [];
          }
          org.children.push(newOrganization);
          return true;
        }
        if (org.children && addToParent(org.children)) {
          return true;
        }
      }
      return false;
    };

    if (!addToParent(organizations.value)) {
      // 如果没找到父级，添加到根级
      organizations.value.push(newOrganization);
    }
  } else {
    // 没有上级单位，添加到根级
    organizations.value.push(newOrganization);
  }
};

const handleEditOrganization = (id: string, formData: Organization) => {
  if (!id || !formData || typeof formData !== "object") {
    message.error("无效的编辑数据");
    return;
  }
  const updateOrganization = (orgs: Organization[]): boolean => {
    if (!orgs || !Array.isArray(orgs)) {
      return false;
    }

    for (let i = 0; i < orgs.length; i++) {
      if (!orgs[i] || typeof orgs[i] !== "object") {
        continue;
      }

      if (orgs[i]?.id === id) {
        const oldpid = orgs[i]?.pid;
        const updatedOrg = { ...orgs[i], ...formData };
        orgs[i] = updatedOrg;
        selectedOrganization.value = updatedOrg;

        // 如果上级单位发生变化，需要移动组织位置
        if (oldpid !== formData.pid) {
          moveOrganization(id, oldpid, formData.pid);
        }
        return true;
      }
      if (orgs[i]?.children && updateOrganization(orgs[i]?.children!)) {
        return true;
      }
    }
    return false;
  };

  updateOrganization(organizations.value);
};

const moveOrganization = (orgId: string, oldpid?: string, newpid?: string) => {
  if (!orgId) return;

  // 从原位置移除
  const removeFromOld = (orgs: Organization[]): null | Organization => {
    if (!orgs || !Array.isArray(orgs)) {
      return null;
    }

    for (let i = 0; i < orgs.length; i++) {
      if (orgs[i]?.id === orgId) {
        const removedOrg = orgs.splice(i, 1)[0];
        return removedOrg || null;
      }
      if (orgs[i].children && Array.isArray(orgs[i].children)) {
        const removed = removeFromOld(orgs[i].children!);
        if (removed) return removed;
      }
    }
    return null;
  };

  const removedOrg = removeFromOld(organizations.value);
  if (!removedOrg) return;

  // 添加到新位置
  if (newpid) {
    const addToNew = (orgs: Organization[]): boolean => {
      if (!orgs || !Array.isArray(orgs)) {
        return false;
      }

      for (const org of orgs) {
        if (org.id === newpid) {
          if (!org.children) {
            org.children = [];
          }
          org.children.push(removedOrg);
          return true;
        }
        // 修复递归调用：确保children存在且为数组
        if (
          org.children &&
          Array.isArray(org.children) &&
          addToNew(org.children)
        ) {
          return true;
        }
      }
      return false;
    };

    if (!addToNew(organizations.value)) {
      organizations.value.push(removedOrg);
    }
  } else {
    organizations.value.push(removedOrg);
  }
};

const handleDeleteOrganization = (id: string) => {
  if (!id) {
    message.error("无效的组织ID");
    return;
  }

  const deleteOrganization = (orgs: Organization[]): boolean => {
    if (!orgs || !Array.isArray(orgs)) {
      return false;
    }
    for (let i = 0; i < orgs.length; i++) {
      if (orgs[i]?.id === id) {
        orgs.splice(i, 1);
        selectedOrganization.value = null;
        return true;
      }
      if (
        orgs[i].children &&
        Array.isArray(orgs[i].children) &&
        deleteOrganization(orgs[i].children!)
      ) {
        return true;
      }
    }
    return false;
  };

  deleteOrganization(organizations.value);
  message.success("组织删除成功");
};
onMounted(async () => {
  const response = await getDepartmentTree("1");
  if (response.success) {
    organizations.value = response.result;
  } else {
    message.error(response.message || "获取组织机构信息失败");
  }
});
</script>

<template>
  <div class="organization-management">
    <!-- 左侧组织树 -->
    <div class="sidebar">
      <!-- 标题 -->
      <!-- <div class="sidebar-header">
        <h2 class="sidebar-title">组织机构管理</h2>
      </div> -->
      <div class="sidebar-content">
        <OrganizationTree
          :organizations="organizations"
          :selected-id="selectedOrganization?.id"
          @select="handleSelectOrganization"
        />
      </div>
    </div>

    <!-- 右侧详情表单 -->
    <div class="main-content">
      <!-- 右侧标题 -->
      <div class="main-header">
        <h2 class="main-title" />
      </div>
      <div class="main-body">
        <OrganizationForm
          :current-organization="selectedOrganization"
          :organizations="organizations"
          @add="handleAddOrganization"
          @edit="handleEditOrganization"
          @delete="handleDeleteOrganization"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.organization-management {
  height: 100%;
  display: flex;
  background-color: var(--gray-50);
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.sidebar {
  width: 320px;
  background-color: white;
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--gray-200);
  background-color: white;
}

.sidebar-title {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--gray-900);
  margin: 0;
}

.sidebar-content {
  flex: 1;
  overflow: hidden;
}

.main-content {
  flex: 1;
  overflow: auto;
  display: flex;
  //flex-direction: column;
}

.main-header {
  padding: var(--spacing-6) var(--spacing-6) var(--spacing-3) var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
  background-color: white;
}

.main-title {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--gray-900);
  margin: 0;
}

.main-body {
  flex: 1;
  overflow: auto;
}
</style>
