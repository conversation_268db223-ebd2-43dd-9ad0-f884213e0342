package com.sbtr.workflow.mapper;

import com.sbtr.workflow.model.ActMyForm;
import com.sbtr.workflow.vo.FormLayoutVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表单信息
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-10 11:10:49
 */
public interface ActMyFormMapper extends tk.mybatis.mapper.common.Mapper<ActMyForm> {

    //分页
    List<ActMyForm> getPageSet(@Param("filterSort") String filterSort);

    //删除
    int executeDeleteBatch(Object[] var1);

    ActMyForm selectByProcessInstanceId(@Param("processInstanceId") String processInstanceId,
                                        @Param("modelKey") String modelKey,
                                        @Param("processDefinitionId") String processDefinitionId);

    Integer updateFormByPrimaryKeySelective(@Param("uuid")String uuid, @Param("formDesign")String formDesign);

    List<ActMyForm> getListDataByModelKeyAndProcdefId(@Param("modelKey")String modelKey, @Param("procdefId")String procdefId);

    String getFormLayoutFormJSON(@Param("formLayou")String formLayou);

    List<FormLayoutVo> getFormLayoutForm(@Param("formUuid")String  formUuid );

    Integer updateFormDesignByUuid(@Param("json")String json, @Param("uuid")String uuid);
}
