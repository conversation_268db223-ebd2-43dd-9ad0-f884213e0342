package com.lms.system.log.service.impl;

import com.lms.common.model.JwtUser;
import com.lms.common.model.LogType;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.ConstParamUtil;
import com.lms.common.util.DateHelper;
import com.lms.common.util.ServletUtil;
import com.lms.system.languagelabel.service.LanguagelabelService;
import com.lms.system.log.mapper.LogMapper;
import com.lms.system.feign.model.Log;
import com.lms.system.log.service.LogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("LogService")
public class LogServiceImpl extends BaseServiceImpl<LogMapper, Log> implements LogService {

	@Resource
	private LogMapper logMapper;
	@Resource
	private LanguagelabelService languagelabelService;

	public void saveLogin(JwtUser jwtUser) {
		// 常见日志实体对象
		Log log = new Log();
		log.setLogobj(jwtUser.getUserid());
		log.setSubmitdate(DateHelper.getCurrentDate());
		log.setObjname(jwtUser.getUsername());
		log.setDescription("用户登录");
		log.setSubmitor(jwtUser.getUsername());
		log.setSubmitdate(DateHelper.getCurrentDate());
		log.setSubmittime(DateHelper.getCurDateTime());
		log.setSubmitip(ServletUtil.getIpAddr());
		log.setLogtype(languagelabelService.translateContent(LogType.Login));
		log.setResult(languagelabelService.translateContent(ConstParamUtil.OPERATE_SUCCESS_MSG));
		log.setRole(jwtUser.getRoleid());
		log.setUsertype(jwtUser.getUsertype());
		log.setMlevel(20);
		this.saveOrUpdate(log);
	}
}
