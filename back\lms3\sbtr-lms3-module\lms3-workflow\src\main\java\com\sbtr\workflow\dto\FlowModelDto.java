package com.sbtr.workflow.dto;



/**
 * 流程 表单模型
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-06-13 09:39:40
 */
public class FlowModelDto {

    /**
     * 模型id
     */
    private String modelId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 表单设计
     */
    private String formJson;

    /**
     * 流程设计
     */
    private String flowJson;

    /**
     * 模型key
     */
    private String modelKey;

    /**
     * 模型数据表名key
     */
    private String formTableName;

    /**
     * 表单属性
     */
    private String formModel;

    /**
     * 模型名称
     */
    private String actDeModelName;

    /**
     * 按钮
     */
    private String formBtnList;

    /**
     * 字段是否可编辑可查看
     */
    private String formFieldList;

    /**
     * 节点通知
     */
    private String formNoticeList;

    /**
     * 判断是自己写的表单还是通过拖动出来的表单  1自己写的表单  2拖动设计的
     */
    private String modelType;

    /**
     * 启动权限类型
     */
    private String  permissionType;

    /**
     * 启动权限值
     */
    private String  permissionValue;

    /**
     * 流程模型类型  1 自定义流程界面  2 托拉拽界面
     */
    private String processModelType;

    /**
     * 外置表单的uuid
     */
    private String actFormConfigureUuid;
    //流程分类Id
    private String category;
    //节点权限码
    private String nodeCodeList;

    public String getProcessModelType() {
        return processModelType;
    }

    public void setProcessModelType(String processModelType) {
        this.processModelType = processModelType;
    }

    public String getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(String permissionType) {
        this.permissionType = permissionType;
    }

    public String getPermissionValue() {
        return permissionValue;
    }

    public void setPermissionValue(String permissionValue) {
        this.permissionValue = permissionValue;
    }

    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public String getFormNoticeList() {
        return formNoticeList;
    }

    public void setFormNoticeList(String formNoticeList) {
        this.formNoticeList = formNoticeList;
    }

    public String getFormFieldList() {
        return formFieldList;
    }

    public void setFormFieldList(String formFieldList) {
        this.formFieldList = formFieldList;
    }

    public String getFormBtnList() {
        return formBtnList;
    }

    public void setFormBtnList(String formBtnList) {
        this.formBtnList = formBtnList;
    }

    public String getActDeModelName() {
        return actDeModelName;
    }

    public void setActDeModelName(String actDeModelName) {
        this.actDeModelName = actDeModelName;
    }

    public String getFormModel() {
        return formModel;
    }

    public void setFormModel(String formModel) {
        this.formModel = formModel;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getFormJson() {
        return formJson;
    }

    public void setFormJson(String formJson) {
        this.formJson = formJson;
    }

    public String getFlowJson() {
        return flowJson;
    }

    public void setFlowJson(String flowJson) {
        this.flowJson = flowJson;
    }

    public String getModelKey() {
        return modelKey;
    }

    public void setModelKey(String modelKey) {
        this.modelKey = modelKey;
    }

    public String getFormTableName() {
        return formTableName;
    }

    public void setFormTableName(String formTableName) {
        this.formTableName = formTableName;
    }

    public String getActFormConfigureUuid() {
        return actFormConfigureUuid;
    }

    public void setActFormConfigureUuid(String actFormConfigureUuid) {
        this.actFormConfigureUuid = actFormConfigureUuid;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getNodeCodeList() {
        return nodeCodeList;
    }

    public void setNodeCodeList(String nodeCodeList) {
        this.nodeCodeList = nodeCodeList;
    }
}
