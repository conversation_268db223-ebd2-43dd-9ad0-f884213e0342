package com.sbtr.workflow.controller;

import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.MultiInstanceLoopCharacteristics;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.UserTask;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEntityEvent;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.event.AbstractFlowableEngineEventListener;
import org.flowable.engine.delegate.event.FlowableProcessStartedEvent;
import org.flowable.engine.delegate.event.impl.FlowableProcessEventImpl;
import org.flowable.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

@Component
public class GlobalTaskListener extends AbstractFlowableEngineEventListener {

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private RepositoryService repositoryService;

    @Override
    protected void taskCreated(FlowableEngineEntityEvent event) {
        super.taskCreated(event);
    }

    @Override
    protected void processCreated(FlowableEngineEntityEvent event) {
        super.processCreated(event);
    }

    @Override
    protected void processStarted(FlowableProcessStartedEvent event) {
        super.processStarted(event);

        FlowableProcessEventImpl eventImpl = (FlowableProcessEventImpl) event;

        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(eventImpl.getProcessDefinitionId()).singleResult();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
        List<Process> processList = bpmnModel.getProcesses();
        Process process = processList.get(0);
        List list = (List) process.getFlowElements();
        HashMap<String, Object> variables = new HashMap<>();
        for(Object o : list){
            if(o instanceof UserTask){
                String  inputDataItem;
                UserTask userTask = (UserTask) o;
                //判断是不是会签节点
                MultiInstanceLoopCharacteristics multiInstanceLoopCharacteristics = userTask.getLoopCharacteristics();
                if(multiInstanceLoopCharacteristics != null){
                    //会签节点
                    //获取设置参数名称
                    inputDataItem = multiInstanceLoopCharacteristics.getInputDataItem().replace("${","").replace("}","");
                    //获取前端设置的用户或者组
                    if(userTask.getCandidateGroups().size() >= 0){
                        List candidateGroups = userTask.getCandidateUsers();
                        //如果是用户组查询组里的用户
//                        String[] v = {"ewsd0001", "ewsd0002", "ewsd0003", "ewsd0004"};
                        //设置参数
                        variables.put(inputDataItem, candidateGroups);
                    }
                }else{
                    //获取设置参数名称
//                    inputDataItem = userTask.getAssignee().replace("${","").replace("}","");
//                    //非会签节点
//                    if(userTask.getCandidateUsers().size() >= 0){
//                        //设置参数
////                        variables.put(inputDataItem,userTask.getCandidateUsers());
//                        variables.put(inputDataItem,businessSystemDataService.getUserNameId());
//                    }
                }

            }
        }
        //将variables设置进去
        runtimeService.setVariables(eventImpl.getExecutionId(),variables);
    }
    }