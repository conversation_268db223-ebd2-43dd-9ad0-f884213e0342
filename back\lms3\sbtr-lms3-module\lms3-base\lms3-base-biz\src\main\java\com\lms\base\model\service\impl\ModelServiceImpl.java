package com.lms.base.model.service.impl;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huaban.analysis.jieba.JiebaSegmenter;
import com.huaban.analysis.jieba.SegToken;
import com.lms.base.model.service.ModelService;
import com.lms.base.model.vo.ModelVo;
import com.lms.base.modelAttribute.service.ModelAttributeService;
import com.lms.base.modelAttribute.vo.ModelAttributeVo;
import com.lms.common.util.TuGraphUtil;
import com.lms.common.constants.Constants;
import com.lms.common.feign.api.CommonSystemApi;
import com.lms.common.model.Result;
import com.lms.common.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service("ModelService")
public class ModelServiceImpl implements ModelService {

    @Resource
    private TuGraphUtil tuGraphUtil;

    @Autowired
    private ModelAttributeService modelAttributeService;

    @Resource
    public CommonSystemApi commonSystemApi;

    /**
     * 查询全部
     *
     * @return 模型列表
     */
    public List<ModelVo> listAll() {
        try {
            String result = tuGraphUtil.queryNodes("default", Constants.MODEL, null);
            List<Map<String, Object>> nodes = parseTuGraphResult(result);
            // 2. 转换为Model列表
            return nodes.stream()
                    .map(node -> ModelVo.fromTuGraphNode((Map<String, Object>) node.get("a")))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("从TuGraph查询模型失败", e);
        }
    }

    /**
     * 分页查询
     *
     * @param jsonObject 分页条件参数
     * @return 模型分页列表
     */
    @Override
    public Page<ModelVo> listPage(JSONObject jsonObject) throws Exception {
        int pageNum = jsonObject.containsKey("pageIndex") ? jsonObject.getIntValue("pageIndex") : 1;
        int pageSize = jsonObject.containsKey("pageSize") ? jsonObject.getIntValue("pageSize") : 20;
        // 1. 解析条件
        String nameLike = jsonObject.getString("name");
        String descLike = jsonObject.getString("desc");
        List<Map<String, String>> attrConds = new ArrayList<>();
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            String key = entry.getKey();
            if (!"pageIndex".equals(key) && !"pageSize".equals(key) && !"name".equals(key) && !"desc".equals(key)) {
                Map<String, String> param = new HashMap<>();
                param.put("name", key);
                param.put("value", entry.getValue().toString());
                attrConds.add(param);
            }
        }
        // 构建查询语句
        String cypherPrefix = "MATCH (a:模型) ";
        String cypherSuffix = String.format(" RETURN a ORDER BY a.uploadTime DESC SKIP %s LIMIT %s", pageSize * (pageNum - 1), pageSize);
        StringBuilder cypherWhere = new StringBuilder();
        List<String> conds = new ArrayList<>();
        for (Map<String, String> cond : attrConds) {
            String searchCondStr = "name:" + cond.get("name") + "," + "value:" + cond.get("value");
            conds.add("a.searchAttributeHelp CONTAINS '" + TuGraphUtil.escapeString(searchCondStr) + "'");
        }
        if (nameLike != null) {
            conds.add("a.name CONTAINS '" + TuGraphUtil.escapeString(nameLike) + "'");
        }
        if (descLike != null) {
            conds.add("a.desc CONTAINS '" + TuGraphUtil.escapeString(descLike) + "'");
        }
        if (!conds.isEmpty()) {
            cypherWhere.append("WHERE ").append(String.join(" AND ", conds));
        }
        String cypher = cypherPrefix + cypherWhere + cypherSuffix;
        // 查询总数
        StringBuilder countCypher = new StringBuilder();
        countCypher.append(cypherPrefix);
        if (!conds.isEmpty()) {
            countCypher.append("WHERE ").append(String.join(" AND ", conds));
        }
        countCypher.append(" RETURN count(a) as total");
        String countResult = tuGraphUtil.executeCypher(countCypher.toString(), "default");
        int total = 0;
        List<Map<String, Object>> countRows = parseTuGraphResult(countResult);
        if (!countRows.isEmpty() && countRows.get(0).get("total") != null) {
            Object totalObj = countRows.get(0).get("total");
            if (totalObj instanceof Integer) {
                total = (Integer) totalObj;
            } else if (totalObj instanceof Long) {
                total = ((Long) totalObj).intValue();
            } else if (totalObj instanceof String) {
                total = Integer.parseInt((String) totalObj);
            }
        }
        Map<String, ModelVo> modelMap = new HashMap<>();
        String result = tuGraphUtil.executeCypher(cypher, "default");
        List<Map<String, Object>> rows = parseTuGraphResult(result);
        for (Map<String, Object> row : rows) {
            ModelVo vo = ModelVo.fromTuGraphNode((Map<String, Object>) row.get("a"));
            modelMap.put(vo.getId(), vo);
        }
        Page<ModelVo> page = new Page<>(pageNum, pageSize);
        ArrayList<ModelVo> records = new ArrayList<>(modelMap.values());
        records.sort(Comparator.comparing(ModelVo::getUploadTime, Comparator.nullsLast(Date::compareTo)).reversed());
        page.setRecords(records);
        page.setTotal(total);
        return page;
    }

    @Override
    public ModelVo getById(String id) throws IOException {
        Map<String, ModelVo> modelMap = new HashMap<>();
        // 查询模型
        String queryModelCypher = "MATCH (a:模型) WHERE  a.id =  '" + id + "' RETURN a";
        List<Map<String, Object>> modelRows = parseTuGraphResult(tuGraphUtil.executeCypher(queryModelCypher, "default"));
        for (Map<String, Object> row : modelRows) {
            ModelVo vo = ModelVo.fromTuGraphNode((Map<String, Object>) row.get("a"));
            modelMap.put(vo.getId(), vo);
        }
        // 查询模型属性
        String queryModelAttrCypher = "MATCH (a:模型)-[e]->(b:属性) WHERE a.id =  '" + id + "' RETURN a, e, b";
        List<Map<String, Object>> attrRows = parseTuGraphResult(tuGraphUtil.executeCypher(queryModelAttrCypher, "default"));
        List<ModelVo> modelHasAttr = attrRows.stream()
                .map(row -> ModelVo.fromTuGraphNode((Map<String, Object>) row.get("a"), (Map<String, Object>) row.get("b")))
                .collect(Collectors.toList());
        // 组合模型map与模型属性
        modelHasAttr.forEach(m -> {
            ModelVo modelVo = modelMap.get(m.getId());
            if (modelVo != null) {
                if (CollectionUtils.isEmpty(modelVo.getAttributeList())) {
                    modelVo.setAttributeList(new ArrayList<>());
                }
                List<ModelAttributeVo> modelAttributeVoList = modelVo.getAttributeList();
                modelAttributeVoList.add(m.getAttribute());
            }
        });
        // 查询子模型
        String querySubModelCypher = "MATCH (a:模型)-[e]->(b:模型) WHERE a.id =  '" + id + "' RETURN a, e, b";
        List<Map<String, Object>> querySubModelRows = parseTuGraphResult(tuGraphUtil.executeCypher(querySubModelCypher, "default"));
        List<ModelVo> modelHasSubModel = querySubModelRows.stream()
                .map(row -> ModelVo.fromTuGraphNode((Map<String, Object>) row.get("a"), null, (Map<String, Object>) row.get("b")))
                .collect(Collectors.toList());
        // 组合模型map与子模型
        modelHasSubModel.forEach(m -> {
            ModelVo modelVo = modelMap.get(m.getId());
            if (modelVo != null) {
                if (CollectionUtils.isEmpty(modelVo.getSubModelList())) {
                    modelVo.setSubModelList(new ArrayList<>());
                }
                List<ModelVo> subModelList = modelVo.getSubModelList();
                subModelList.add(m.getSubModel());
            }
        });
        return modelMap.get(id);
    }

    /**
     * 新增或更新模型
     */
    @Override
    public void saveOrUpdateModel(ModelVo modelVo, boolean adaptSubModelFlag) throws Exception {
        Snowflake snowflake = new Snowflake();
        boolean isNew = StringUtils.isBlank(modelVo.getId());
        if (isNew) {
            modelVo.setId(snowflake.nextIdStr());
            modelVo.setUploadPerson(ContextUtil.getCurrentUser().getUsername());
            modelVo.setUploadTime(new Date());
        }
        StringBuilder searchAttributeHelp = new StringBuilder();
        if (CollectionUtils.isNotEmpty(modelVo.getAttributeList())) {
            for (ModelAttributeVo modelAttributeVo : modelVo.getAttributeList()) {
                // 对value使用结巴(搜索引擎)分词
                JiebaSegmenter segmenter = new JiebaSegmenter();
                List<SegToken> process = segmenter.process(modelAttributeVo.getValue(), JiebaSegmenter.SegMode.SEARCH);
                List<String> words = process.stream().map(token -> token.word).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(words)) {
                    for (String word : words) {
                        searchAttributeHelp.append("[name:").append(modelAttributeVo.getName()).append(",").append("value:").append(word).append("],");
                    }
                }
                if (StringUtils.isBlank(modelAttributeVo.getId())) {
                    modelAttributeVo.setId(snowflake.nextIdStr());
                }
                modelAttributeVo.setModelId(modelVo.getId());
            }
        }
        modelVo.setSearchAttributeHelp(searchAttributeHelp.toString());
        Map<String, Object> properties = transProperties(modelVo);
        if (isNew) {
            String res = tuGraphUtil.createNode("default", Constants.MODEL, properties);
            if (!res.contains("id")) {
                throw new RuntimeException("保存模型失败！");
            }
        }
        if (!isNew && !adaptSubModelFlag) {
            String result = tuGraphUtil.modifyNode("default", Constants.MODEL, modelVo.getId(), properties);
            if (result.equals("[]")) {
                throw new RuntimeException("更新模型失败！");
            }
            // 删除模型与模型属性之间的关系
            List<ModelAttributeVo> modelAttributeVos = modelAttributeService.queryModelAttribute(modelVo.getId());
            if (CollectionUtils.isNotEmpty(modelAttributeVos)) {
                for (ModelAttributeVo modelAttributeVo : modelAttributeVos) {
                    String deleteResult = tuGraphUtil.deleteRelationship(Constants.MODEL, Constants.MODEL_ATTRIBUTE, "id", modelAttributeVo.getModelId(),
                            "id", modelAttributeVo.getId(), modelAttributeVo.getRelationship(), "default");
                    if (deleteResult.equals("[]")) {
                        throw new RuntimeException("删除模型属性关系失败！");
                    }
                }
            }
        }
        if (!isNew && adaptSubModelFlag){
            // 删除模型与模型之间的关系（子模型关系）  所有以当前模型为父节点的包含关系
            String deleteResult = tuGraphUtil.deleteRelationship(Constants.MODEL, Constants.MODEL, "id", modelVo.getId(), "包含", "default");
            if (deleteResult.equals("[]")) {
                throw new RuntimeException("删除模型关系失败！");
            }
        }
        // 保存模型属性
        if (CollectionUtils.isNotEmpty(modelVo.getAttributeList())) {
            for (ModelAttributeVo modelAttributeVo : modelVo.getAttributeList()) {
                modelAttributeService.saveModelAttribute(modelAttributeVo);
            }
        }
        // 保存子模型并构建父模型与子模型关系
        if (CollectionUtils.isNotEmpty(modelVo.getSubModelList())) {
            for (ModelVo subModel : modelVo.getSubModelList()) {
                subModel.setParentId(modelVo.getId());
                saveOrUpdateModel(subModel, true);
                tuGraphUtil.createRelationship(Constants.MODEL, Constants.MODEL, "id", modelVo.getId(),
                        "id", subModel.getId(), "包含", "default");
            }
        }
    }

    /**
     * 模型编辑器修改或保存模型(包含子模型)
     *
     * @param modelVo 模型对象
     */
    @Override
    public void updateModelByEditor(ModelVo modelVo) throws Exception {
        // 1. 本系统新建模型后 通过编辑器编辑模型
        if (StringUtils.isNotEmpty(modelVo.getId())) {
            ModelVo oldModel = getById(modelVo.getId());
            if (oldModel == null) {
                throw new RuntimeException("模型不存在！");
            }
            // todo 确定编辑器调用时会传什么数据过来 不会传的数据要从旧模型中取
            modelVo.setId(oldModel.getId());
            modelVo.setAttributeList(oldModel.getAttributeList());
            modelVo.setDesc(oldModel.getDesc());
            modelVo.setNumber(oldModel.getNumber());
            modelVo.setUploadPerson(oldModel.getUploadPerson());
            modelVo.setUploadTime(oldModel.getUploadTime());
        }
        // 2. 编辑器直接保存模型
        if (StringUtils.isEmpty(modelVo.getId())) {
            Snowflake snowflake = new Snowflake();
            modelVo.setId(snowflake.nextIdStr());
            modelVo.setUploadPerson(ContextUtil.getCurrentUser().getUsername());
            modelVo.setUploadTime(new Date());
        }
        saveOrUpdateModel(modelVo, false);
    }

    @Override
    public void deleteModel(String ids) throws Exception {
        List<String> idList = Arrays.stream(ids.split(","))
                .map(String::trim)  // 去除空格
                .collect(Collectors.toList());
        for (String id : idList) {
            // 删除模型与模型属性之间的关系
            List<ModelAttributeVo> modelAttributeVos = modelAttributeService.queryModelAttribute(id);
            if (CollectionUtils.isNotEmpty(modelAttributeVos)) {
                for (ModelAttributeVo modelAttributeVo : modelAttributeVos) {
                    tuGraphUtil.deleteRelationship(Constants.MODEL, Constants.MODEL_ATTRIBUTE, "id", modelAttributeVo.getModelId(),
                            "id", modelAttributeVo.getId(), modelAttributeVo.getRelationship(), "default");
                }
            }
        }
        String result = tuGraphUtil.batchDeleteNodesByIds("default", Constants.MODEL, idList);
        if (result.equals("[]")) {
            throw new RuntimeException("删除模型失败！");
        }
    }

    @Override
    public Result<Map<String, Object>> queryTuGraph(String modelId) throws Exception {
        String result = tuGraphUtil.getAllRelatedNodeIdsAndRelationships("default", modelId, 20);
        if (!result.equals("[]")) {
            List<Map<String, Object>> list = parseTuGraphResult(result);
            return Result.OK(extractAndConvert(list, modelId));
        } else {
            // 没有任何子模型或属性，单独查当前模型节点
            String cypher = String.format("MATCH (m:%s {id: '%s'}) RETURN m as n", Constants.MODEL, modelId);
            String nodeResult = tuGraphUtil.executeCypher(cypher, "default");
            List<Map<String, Object>> nodeList = parseTuGraphResult(nodeResult);
            if (!nodeList.isEmpty()) {
                // 构造只包含当前模型节点的返回结构
                Map<String, Object> node = (Map<String, Object>) nodeList.get(0).get("n");
                Map<String, Object> modelNode = convertModelNode(node);
                Map<String, Object> data = new HashMap<>();
                data.put("nodes", Collections.singletonList(modelNode));
                data.put("links", Collections.emptyList());
                return Result.OK(data);
            } else {
                // 节点也不存在
                return Result.OK(null);
            }
        }
    }

    @Override
    public void createModelRelationship(String startModelId, String endModelId, String relationship) throws Exception {
        String res = tuGraphUtil.createRelationship(Constants.MODEL,
                Constants.MODEL, "id", startModelId, "id", endModelId, relationship, "default");
        if (res.equals("[]")) {
            throw new RuntimeException("模型关联失败！");
        }
    }

    /**
     * 提取TuGraph返回的图结果，递归收集当前模型及其所有下级模型和属性，
     * 只保留这些模型的“模型->属性”和“模型->模型”边及相关节点。
     *
     * @param graphResults TuGraph返回的原始图结果（每项包含n节点、m节点、r关系）
     * @param rootModelId  当前查询的根模型id
     * @return 只包含根模型及其所有下级模型和属性的点和边的Map
     */
    public Map<String, Object> extractAndConvert(List<Map<String, Object>> graphResults, String rootModelId) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> nodeList = new ArrayList<>();
        List<Map<String, Object>> edgeList = new ArrayList<>();
        // 用于去重，避免重复添加节点
        Map<String, Boolean> nodeIdExists = new HashMap<>();
        // identity->id 映射
        Map<Object, String> identityToNodeId = new HashMap<>();
        // 1. 递归收集所有相关模型id（根模型及其所有下级模型）
        Set<String> allRelatedModelIds = new HashSet<>();
        allRelatedModelIds.add(rootModelId);
        boolean hasNewModel;
        do {
            hasNewModel = false;
            for (Map<String, Object> record : graphResults) {
                Map<String, Object> sourceNode = (Map<String, Object>) record.get("n");
                Map<String, Object> targetNode = (Map<String, Object>) record.get("m");
                String sourceLabel = (String) sourceNode.get("label");
                String targetLabel = (String) targetNode.get("label");
                String sourceId = ((Map<String, Object>) sourceNode.get("properties")).get("id").toString();
                String targetId = ((Map<String, Object>) targetNode.get("properties")).get("id").toString();
                // 只递归模型->模型
                if (Constants.MODEL.equals(sourceLabel)
                        && Constants.MODEL.equals(targetLabel)
                        && allRelatedModelIds.contains(sourceId)
                        && !allRelatedModelIds.contains(targetId)) {
                    allRelatedModelIds.add(targetId);
                    hasNewModel = true;
                }
            }
        } while (hasNewModel);
        // 2. 收集所有相关节点和边
        for (Map<String, Object> record : graphResults) {
            Map<String, Object> sourceNode = (Map<String, Object>) record.get("n");
            Map<String, Object> targetNode = (Map<String, Object>) record.get("m");
            Map<String, Object> sourceProps = (Map<String, Object>) sourceNode.get("properties");
            Map<String, Object> targetProps = (Map<String, Object>) targetNode.get("properties");
            Object sourceIdentity = sourceNode.get("identity");
            Object targetIdentity = targetNode.get("identity");
            String sourceId = sourceProps.get("id").toString();
            String targetId = targetProps.get("id").toString();
            identityToNodeId.put(sourceIdentity, sourceId);
            identityToNodeId.put(targetIdentity, targetId);
            String sourceLabel = (String) sourceNode.get("label");
            String targetLabel = (String) targetNode.get("label");
            // 节点收集和去重
            if (!nodeIdExists.containsKey(sourceId)) {
                if (Constants.MODEL_ATTRIBUTE.equals(sourceLabel)) {
                    nodeList.add(convertAttributeNode(sourceNode));
                } else {
                    nodeList.add(convertModelNode(sourceNode));
                }
                nodeIdExists.put(sourceId, true);
            }
            if (!nodeIdExists.containsKey(targetId)) {
                if (Constants.MODEL_ATTRIBUTE.equals(targetLabel)) {
                    nodeList.add(convertAttributeNode(targetNode));
                } else {
                    nodeList.add(convertModelNode(targetNode));
                }
                nodeIdExists.put(targetId, true);
            }
            // 只保留source为相关模型id的模型->属性、模型->模型的边
            if (!Constants.MODEL.equals(sourceLabel) || !allRelatedModelIds.contains(sourceId)) {
                continue;
            }
            if (!Constants.MODEL_ATTRIBUTE.equals(targetLabel) && !Constants.MODEL.equals(targetLabel)) {
                continue;
            }
            // 关系
            Map<String, Object> edge = (Map<String, Object>) record.get("r");
            Object srcIdentity = edge.get("src");
            Object dstIdentity = edge.get("dst");
            String edgeSourceId = identityToNodeId.get(srcIdentity);
            String edgeTargetId = identityToNodeId.get(dstIdentity);
            Map<String, Object> edgeMap = new HashMap<>();
            edgeMap.put("source", edgeSourceId);
            edgeMap.put("target", edgeTargetId);
            edgeMap.put("value", edge.get("label"));
            edgeList.add(edgeMap);
        }
        // 只保留有边的节点
        Set<String> nodeIdsWithEdges = new HashSet<>();
        for (Map<String, Object> edge : edgeList) {
            nodeIdsWithEdges.add((String) edge.get("source"));
            nodeIdsWithEdges.add((String) edge.get("target"));
        }
        List<Map<String, Object>> filteredNodes = new ArrayList<>();
        for (Map<String, Object> node : nodeList) {
            String id = (String) node.get("id");
            if (nodeIdsWithEdges.contains(id)) {
                filteredNodes.add(node);
            }
        }
        result.put("nodes", filteredNodes);
        result.put("links", edgeList);
        return result;
    }

    private Map<String, Object> convertModelNode(Map<String, Object> node) {
        Map<String, Object> properties = (Map<String, Object>) node.get("properties");
        Map<String, Object> modelNode = new HashMap<>();
        modelNode.put("id", properties.get("id"));
        modelNode.put("name", properties.get("name"));
        modelNode.put("category", Constants.MODEL);
        return modelNode;
    }

    private Map<String, Object> convertAttributeNode(Map<String, Object> node) {
        Map<String, Object> properties = (Map<String, Object>) node.get("properties");
        Map<String, Object> attributeNode = new HashMap<>();
        attributeNode.put("id", properties.get("id"));
        attributeNode.put("name", properties.get("name"));
        attributeNode.put("value", properties.get("value"));
        attributeNode.put("category", Constants.MODEL_ATTRIBUTE);
        return attributeNode;
    }

    /**
     * 转换模型属性
     */
    private Map<String, Object> transProperties(ModelVo modelVo) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("id", modelVo.getId());
        properties.put("name", modelVo.getName());
        properties.put("number", commonSystemApi.getNewCode("MD"));
        properties.put("desc", modelVo.getDesc());
        properties.put("fileUrl", modelVo.getFileUrl());
        properties.put("previewUrl", modelVo.getPreviewUrl());
        properties.put("uploadPerson", modelVo.getUploadPerson());
        properties.put("uploadTime", DateUtil.format(modelVo.getUploadTime(), "yyyy-MM-dd HH:mm:ss"));
        properties.put("fileType", modelVo.getFileType());
        properties.put("searchAttributeHelp", modelVo.getSearchAttributeHelp());
        return properties;
    }

    /**
     * 解析 TuGraph 返回的 JSON 结果
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> parseTuGraphResult(String jsonResult) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(jsonResult);

        List<Map<String, Object>> result = new ArrayList<>();
        for (JsonNode node : rootNode) {
            result.add(mapper.convertValue(node, Map.class));
        }
        return result;
    }
}
