package com.lms.base.model.vo;

import com.lms.base.model.vo.EditorVo;
import lombok.extern.slf4j.Slf4j;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class FormDataParser {

    // 匹配嵌套的_children结构：_children[0][_children][1][id] 等
    private static final Pattern NESTED_PATTERN = Pattern.compile("_children\\[([\\d]+)\\](?:\\[_children\\]\\[([\\d]+)\\])*(?:\\[_children\\])?\\[([^\\]]+)\\]");

    public static EditorVo parseFormData(Map<String, String> params) {
        EditorVo editorVo = new EditorVo();

        // 解析根级别字段
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            if ("id".equals(key)) {
                editorVo.setId(value);
            } else if ("name".equals(key)) {
                editorVo.setName(value);
            }
        }

        // 解析嵌套的children结构
        Map<String, EditorVo> allNodes = new HashMap<>();
        parseAllChildrenNodes(params, allNodes);

        // 构建树形结构
        List<EditorVo> rootChildren = buildTreeStructure(allNodes);
        if (!rootChildren.isEmpty()) {
            editorVo.set_children(rootChildren);
        }

        return editorVo;
    }

    /**
     * 解析所有children节点，不使用递归
     */
    private static void parseAllChildrenNodes(Map<String, String> params, Map<String, EditorVo> allNodes) {
        // 按路径分组所有children参数
        Map<String, Map<String, String>> pathGroups = new HashMap<>();

        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            if (key.startsWith("_children[") && !key.equals("_children[]")) {
                ParsedPath parsedPath = parseComplexPath(key);
                if (parsedPath != null) {
                    pathGroups.computeIfAbsent(parsedPath.nodePath, k -> new HashMap<>())
                            .put(parsedPath.fieldName, value);
                }
            }
        }

        // 创建所有节点
        for (Map.Entry<String, Map<String, String>> group : pathGroups.entrySet()) {
            String nodePath = group.getKey();
            Map<String, String> fields = group.getValue();

            EditorVo node = new EditorVo();
            for (Map.Entry<String, String> field : fields.entrySet()) {
                String fieldName = field.getKey();
                String fieldValue = field.getValue();

                if ("id".equals(fieldName)) {
                    node.setId(fieldValue);
                } else if ("name".equals(fieldName)) {
                    node.setName(fieldValue);
                }
            }

            allNodes.put(nodePath, node);
            log.debug("创建节点: {} -> id={}, name={}", nodePath, node.getId(), node.getName());
        }
    }

    /**
     * 构建树形结构
     */
    private static List<EditorVo> buildTreeStructure(Map<String, EditorVo> allNodes) {
        // 找出所有根节点（第一层children）
        Map<Integer, EditorVo> rootNodes = new HashMap<>();

        for (String path : allNodes.keySet()) {
            if (path.matches("_children\\[\\d+\\]$")) {
                int index = extractFirstIndex(path);
                if (index >= 0) {
                    rootNodes.put(index, allNodes.get(path));
                }
            }
        }

        // 为每个节点找到其直接子节点
        for (Map.Entry<String, EditorVo> entry : allNodes.entrySet()) {
            String currentPath = entry.getKey();
            EditorVo currentNode = entry.getValue();

            List<EditorVo> directChildren = findDirectChildren(currentPath, allNodes);
            if (!directChildren.isEmpty()) {
                currentNode.set_children(directChildren);
                log.debug("节点 {} 包含 {} 个子节点", currentPath, directChildren.size());
            }
        }

        // 转换为有序列表
        List<EditorVo> result = new ArrayList<>();
        for (int i = 0; i <= Collections.max(rootNodes.keySet()); i++) {
            if (rootNodes.containsKey(i)) {
                result.add(rootNodes.get(i));
            }
        }

        return result;
    }

    /**
     * 找到指定节点的直接子节点
     */
    private static List<EditorVo> findDirectChildren(String parentPath, Map<String, EditorVo> allNodes) {
        Map<Integer, EditorVo> children = new HashMap<>();
        String childrenPrefix = parentPath + "[_children][";

        for (String path : allNodes.keySet()) {
            if (path.startsWith(childrenPrefix)) {
                String remaining = path.substring(childrenPrefix.length());

                // 检查是否是直接子节点（不包含更深层的[_children]）
                int nextChildrenIndex = remaining.indexOf("][_children][");
                if (nextChildrenIndex == -1) {
                    // 这是直接子节点
                    int closeBracketIndex = remaining.indexOf(']');
                    if (closeBracketIndex > 0) {
                        try {
                            String indexStr = remaining.substring(0, closeBracketIndex);
                            int index = Integer.parseInt(indexStr);
                            children.put(index, allNodes.get(path));
                        } catch (NumberFormatException e) {
                            log.warn("无法解析子节点索引: {}", remaining);
                        }
                    }
                }
            }
        }

        // 转换为有序列表
        List<EditorVo> result = new ArrayList<>();
        if (!children.isEmpty()) {
            for (int i = 0; i <= Collections.max(children.keySet()); i++) {
                if (children.containsKey(i)) {
                    result.add(children.get(i));
                }
            }
        }

        return result;
    }

    /**
     * 解析复杂路径
     */
    private static ParsedPath parseComplexPath(String key) {
        // 找到最后一个字段名
        int lastBracketStart = key.lastIndexOf('[');
        int lastBracketEnd = key.lastIndexOf(']');

        if (lastBracketStart == -1 || lastBracketEnd == -1 || lastBracketStart >= lastBracketEnd) {
            return null;
        }

        String fieldName = key.substring(lastBracketStart + 1, lastBracketEnd);
        String nodePath = key.substring(0, lastBracketStart);

        return new ParsedPath(nodePath, fieldName);
    }

    /**
     * 提取第一个索引
     */
    private static int extractFirstIndex(String path) {
        Pattern pattern = Pattern.compile("_children\\[(\\d+)\\]");
        Matcher matcher = pattern.matcher(path);
        if (matcher.find()) {
            try {
                return Integer.parseInt(matcher.group(1));
            } catch (NumberFormatException e) {
                return -1;
            }
        }
        return -1;
    }

    /**
     * 路径解析结果
     */
    private static class ParsedPath {
        final String nodePath;
        final String fieldName;

        ParsedPath(String nodePath, String fieldName) {
            this.nodePath = nodePath;
            this.fieldName = fieldName;
        }
    }
}