package com.sbtr.workflow.controller;

import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.utils.BaseUtils;
import com.sbtr.workflow.model.ActMyForm;
import com.sbtr.workflow.service.ActMyFormService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 表单信息
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-10 11:10:49
 */
@Api(tags = {"表单信息"})
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/actMyForm")
public class ActMyFormController extends WorkflowBaseController {

    @Autowired
    private ActMyFormService actMyFormService;

    @ApiOperation(value = "获得ActMyForm分页集数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String"),
    })
    @ResponseBody
    @RequestMapping(value = "/getPageSet", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam) {
        String filterSort = "";
        filterSort = BaseUtils.filterSort(request, filterSort);
        PageSet<ActMyForm> pageSet = actMyFormService.getPageSet(pageParam, filterSort);
        return pageSet;
    }

    @ApiOperation(value = "获得ActMyForm模块详细数据")
    @ApiImplicitParam(name = "uuid", value = "获得ActMyForm模块详细数据", required = true, dataType = "String")
    @ResponseBody
    @RequestMapping(value = "/getDetailByUuid")
    public Object getDetailByUuid(String uuid) {
        ActMyForm actMyForm = actMyFormService.selectByPrimaryKey(uuid);
        return actMyForm;
    }


    @Log(title = "表单信息-保存ActMyForm模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @ApiOperation(value = "保存ActMyForm模块数据")
    @ApiImplicitParam(name = "actMyForm", value = "保存ActMyForm模块数据", required = true, dataType = "ActMyForm")
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Object save(@ModelAttribute ActMyForm actMyForm) {
        setLogMessage(actMyForm.getActDeModelName()+":"+actMyForm.getFormTableName(),"");
        int result = actMyFormService.insertSelective(getSaveData(actMyForm));
        return result > 0 ? success("保存成功！") : failure("保存失败！");
    }


    @Log(title = "表单信息-更新ActMyForm模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.UPDATE, method = "getLogMessage")
    @ApiOperation(value = "更新ActMyForm模块数据")
    @ApiImplicitParam(name = "actMyForm", value = "更新ActMyForm模块数据", required = true, dataType = "ActMyForm")
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Object update(@ModelAttribute ActMyForm actMyForm) {
        setLogMessage(actMyForm.getActDeModelName()+":"+actMyForm.getFormTableName(),"");
        int result = actMyFormService.updateByPrimaryKeySelective(getUpdateData(actMyForm));
        return result > 0 ? success("更新成功！") : failure("更新失败！");
    }

    @Log(title = "表单信息-删除ActMyForm模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ApiOperation(value = "删除ActMyForm模块数据")
    @ApiImplicitParam(name = "actMyForm", value = "删除ActMyForm模块数据", required = true, dataType = "ActMyForm")
    @ResponseBody
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.POST)
    public Object deleteBatch(@RequestParam String[] uuid) {
        if(uuid!=null&&uuid.length>0){
            StringBuilder logStr=new StringBuilder();
            for (String s : uuid) {
                ActMyForm actMyNodeButton = actMyFormService.selectByPrimaryKey(s);
                if(actMyNodeButton!=null)
                    logStr.append(actMyNodeButton.getActDeModelName()+":"+actMyNodeButton.getFormTableName()+",");
            }
            setLogMessage(logStr.toString(),"");
        }
        int result = actMyFormService.executeDeleteBatch(uuid);
        return result > 0 ? success("删除成功！") : failure("删除失败！");
    }

}
