/**
 * FileName:ExaminestudentrecordController.java
 * Author:ji<PERSON><PERSON><PERSON>
 * Time  :2013-3-7下午4:18:10
 * CopyRight: SBTR LTD.
 * decription:to-do
 */
package com.lms.examine.examinestudentrecord.controller;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.alibaba.fastjson.JSONObject;
import com.lms.base.feign.api.LmsBaseApi;
import com.lms.base.feign.model.Subject;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.feign.dto.Selectitem;
import com.lms.common.model.Result;
import com.lms.common.util.DateHelper;
import com.lms.common.util.NumberHelper;
import com.lms.common.util.StringHelper;
import com.lms.examine.examinepage.service.ExaminePageService;
import com.lms.examine.examinestudent.service.ExaminestudentService;
import com.lms.examine.examinestudentrecord.service.ExaminestudentrecordService;
import com.lms.examine.examinesubjecttype.service.ExaminesubjecttypeService;
import com.lms.examine.feign.model.Examinesubjecttype;
import com.lms.examine.feign.model.Examinestudent;
import com.lms.examine.feign.model.Examinestudentrecord;
import com.lms.examine.feign.model.ExaminestudentrecordViewModel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR> 试题信息操作控制层
 */
@RestController
@RequestMapping("/examinestudentrecord")
public class ExaminestudentrecordController extends BaseController<Examinestudentrecord> {
    @Resource
    private ExaminestudentrecordService examinestudentrecordService;
    @Resource
    private ExaminestudentService examinestudentService;
    @Resource
    private ExaminesubjecttypeService examinesubjecttypeService;
    @Resource
    private ExaminePageService examinepageService;

    @Resource
    private LmsBaseApi lmsBaseApi;


    @PostMapping(value = {"/listpage"})
    public Result getExaminestudentrecordList(@RequestBody JSONObject request) {
        // 设置分页参数,查询参数
        // super.setParameter(request);
        PageInfo pageInfo = super.getPageInfo(request);
        Page<Examinestudentrecord> examinestudentrecords = examinestudentrecordService.listByCondition(pageInfo);
        List<ExaminestudentrecordViewModel> sModelList = new ArrayList<ExaminestudentrecordViewModel>();
        List<Selectitem> slist = commonBaseApi.getSelectitemsByType("fc657149c8d84e79a5f3043c40d7baaa").getResult();
        List<Examinestudentrecord> sortList = new ArrayList<Examinestudentrecord>();
        for (Selectitem s : slist) {
            String sid = StringHelper.null2String(s.getId());
            for (Examinestudentrecord e : examinestudentrecords.getRecords()) {
                if (sid.equals(StringHelper.null2String(e.getType()))) {
                    sortList.add(e);
                }
                Subject subject = lmsBaseApi.getSubjectById(e.getExaminesubjectid()).getResult();
                e.setSoftcode(subject.getSoftcode() != null ? subject.getSoftcode() : null);
            }
        }
        sModelList = examinestudentrecordService.addTitleSubject(sortList);
        return Result.OK(createPage(sModelList, examinestudentrecords));
    }

    /*
     * 获取所有试题信息
     */
    @RequestMapping(value = {"/list"}, method = RequestMethod.GET)
    public Result getValidExaminestudentrecord() {
        List<Examinestudentrecord> examinestudentrecords = this.examinestudentrecordService.list();
        return Result.OK(examinestudentrecords);
    }

    /*
     * 根据ID获取试题信息
     */
    @RequestMapping(value = {"/getInfo/{id}"}, method = RequestMethod.GET)
    public Result getExaminestudentrecordInfo(@PathVariable("id") String pid) {
        Examinestudentrecord examinestudentrecord = this.examinestudentrecordService.getById(pid);
        return Result.OK(examinestudentrecord);
    }

    // 更新Action
    // @LMSLog(desc = "编辑用户", otype = LogType.Update, order = 1, method =
    // "getName()")
    @RequestMapping(value = {"/modify"}, method = RequestMethod.POST)
    public Result updateExaminestudentrecord(@RequestBody Examinestudentrecord examinestudentrecord) {
        // Examinestudentrecord p = this.examinestudentrecordService.get(id);
        examinestudentrecordService.saveOrUpdate(examinestudentrecord);
        return Result.OK(examinestudentrecord);
    }

    // 删除
    // @LMSLog(desc = "删除员工", otype = LogType.Delete, method = "", order = { 0
    // })
    @RequestMapping(value = {"/del/{id}"}, method = RequestMethod.DELETE)
    public Result deleteExaminestudentrecord(@PathVariable("id") String id) {
        Examinestudentrecord examinestudentrecord = examinestudentrecordService.getById(id);
        this.examinestudentrecordService.removeById(examinestudentrecord);
        // 删除用户时，需要修改examinestudentrecordCache
        // examinestudentrecordCache.removeExaminestudentrecordFromCache(examinestudentrecords.getName());
        return Result.OK();
    }

    // 批量删除
    @RequestMapping(value = {"/batchremove"}, method = RequestMethod.DELETE)
    public Result batchDeleteExaminestudentrecord(@RequestParam("ids") String ids) {
        String[] idarray = ids.split(",");
        List<String> idList = new ArrayList<String>();
        for (int i = 0; i < idarray.length; i++) {
            String id = idarray[i];
            idList.add(id);
        }
        this.examinestudentrecordService.removeBatchByIds(idList);
        return Result.OK();
    }

    /*
     * 处理在线、自测考试和评分阅卷的后台服务
     *
     * @param submitytype case 0 :表示在线考试或自测提交试卷 case 1 :表示在线或自测考试关闭窗口，自动保存 case
     * 2 :表示教员评卷提交 考试学员的状态分为（Examinestatus）：(0：未下发 1：已下发 2：已接收 3：考试中 4：已提交 5、已评分)
     */
    @RequestMapping(value = {"/submit/{submittype}"}, method = RequestMethod.POST)
    public Result saveExaminestudentrecord(
            @PathVariable("submittype") String subimittype,
            @RequestBody Examinestudentrecord[] examinestudentrecords) {
        String studentid = "";
        Double score = 0.00;
        List<Examinestudentrecord> returnRecords = new ArrayList<Examinestudentrecord>();
        for (int i = 0; i < examinestudentrecords.length; i++) {
            Examinestudentrecord examinestudentrecord = examinestudentrecords[i];
            if (studentid.isEmpty())
                studentid = StringHelper.null2String(examinestudentrecord
                        .getStudentid());
            if (subimittype.equals("0") || subimittype.equals("1")) {
                Double sorcepoint = examinestudentrecordService
                        .createRecordScore(examinestudentrecord);
                examinestudentrecord.setScore(sorcepoint);
                score = score + sorcepoint;
                examinestudentrecordService.saveOrUpdate(examinestudentrecord);
                returnRecords.add(examinestudentrecord);
            } else if (subimittype.equals("2")) {
                Examinestudentrecord rd = examinestudentrecordService
                        .getById(examinestudentrecord.getId());
                if (studentid.isEmpty())
                    studentid = StringHelper.null2String(rd
                            .getStudentid());
                Double recordscore = NumberHelper.string2Double(
                        examinestudentrecord.getScore(), 0);
                score = score + recordscore;
                rd.setScore(recordscore);
                examinestudentrecordService.saveOrUpdate(rd);
            }

        }
        if (!studentid.isEmpty()) {
            Examinestudent examinestudent = examinestudentService.getById(studentid);
            examinestudent.setPerson(null);
            String exmineid = StringHelper.null2String(examinestudent.getExamineid());
            String examinetype = StringHelper.null2String(examinepageService.getById(exmineid).getExaminetype());
            String result = examinepageService.getScoreResult(exmineid, score);
            if (examinetype.equals("1")) { // 试卷类型 0表示自测试卷 1表示考试试卷
                if (subimittype.equals("0")) {
                    examinestudent.setSubmittime(DateHelper.getCurDateTime());
                    examinestudent.setExaminestatus("4");
                    examinestudentService.saveOrUpdate(examinestudent);
                } else if (subimittype.equals("2")) {
//					result = examinepageService.getScoreResult(exmineid, score);
                    //examinestudent.setExaminestatus("5");
                    examinestudent.setScore(score);
                    examinestudent.setResult(result);
                    examinestudentService.saveOrUpdate(examinestudent);
                }
            } else if (examinetype.equals("0")) {
                if (subimittype.equals("1")) {
                    examinestudent.setExaminestatus("3");
                    examinestudentService.saveOrUpdate(examinestudent);
//					examinestudent.setScore(score);
                } else {
                    examinestudent.setExaminestatus("5");
                    examinestudent.setScore(score);
                    examinestudent.setSubmittime(DateHelper.getCurDateTime());
                    examinestudent.setResult(result);
                    examinestudentService.saveOrUpdate(examinestudent);
                    String planId = examinepageService.getById(exmineid).getPlanid();
                }

            }
        }

        List<ExaminestudentrecordViewModel> sModelList = new ArrayList<ExaminestudentrecordViewModel>();
        // 对返回的数据进行封装，加入该试卷题目的selectitem
        sModelList = examinestudentrecordService.addTitleSubject(returnRecords);
        return Result.OK(sModelList);
    }

    @RequestMapping(value = {"/getSubjectAnalysis"}, method = RequestMethod.GET)
    public Result getSubjectAnalysis(
            HttpServletRequest request) {
        String examined = StringHelper.null2String(request.getParameter("examined"));
        String personid = StringHelper.null2String(request.getParameter("personid"));
        List<Examinestudentrecord> examinestudentrecords = this.examinestudentrecordService.getExaminestudentrecordsByExamineId(examined, personid);
        List<Map> result = new ArrayList<Map>();
        int totalSubject = examinestudentrecords.size();
        Map resultmap = new HashMap();
        Map totalmap = new HashMap();
        for (Examinestudentrecord esr : examinestudentrecords) {
            Double score = esr.getScore();
            String subjecttype = StringHelper.null2String(esr.getType());
            if (!subjecttype.equals("9e47efc0ce894454856a80171e1e6efe") && !subjecttype.equals("3ef580ec56ae436eb79b91b25d1a078e") && !subjecttype.equals("23e162b9f27b4ebc9a5c93db09913693")) {
                continue;
            }
            if (!resultmap.containsKey(subjecttype)) {
                resultmap.put(subjecttype, 0);
            }
            String examsubject = StringHelper.null2String(esr.getExaminesubjectid());
            if (examined.isEmpty()) {
                examined = StringHelper.null2String(esr.getExamineid());
            }
            List<Examinesubjecttype> examinesubjecttypes = examinesubjecttypeService.getExaminesubjecttypeByIdAndType(examined, subjecttype);
            int totaltypenum = NumberHelper.string2Int(totalmap.get(subjecttype), 0);
            totaltypenum++;
            totalmap.put(subjecttype, totaltypenum);
            if (examinesubjecttypes.size() > 0) {
                Examinesubjecttype examinesubjecttype = examinesubjecttypes.get(0);
                Double perpoint = examinesubjecttype.getPerpoint();
                if (StringHelper.numberFormat2(score).equals(StringHelper.numberFormat2(perpoint))) {
                    int thistypenum = NumberHelper.string2Int(resultmap.get(subjecttype), 0);
                    thistypenum++;
                    resultmap.put(subjecttype, thistypenum);
                }
            }
        }

        for (Object key : resultmap.keySet()) {
            Map returnModel = new HashMap();
            String keyname = StringHelper.null2String(key);
            int typecount = NumberHelper.string2Int(resultmap.get(key), 0);
            int totalcount = NumberHelper.string2Int(totalmap.get(key), 0);
            String passRate = "";
            String failRate = "";
            NumberFormat nf = NumberFormat.getInstance();
            nf.setMaximumFractionDigits(2);
            if (typecount == 0) {
                passRate = "0";
                failRate = "100";
            } else {
                float correctside = (float) typecount / (float) totalcount * 100;
                float wrongside = 100 - correctside;
                passRate = nf.format(correctside);
                failRate = nf.format(wrongside);
            }
//			passRate = passRate + "%";
//			failRate = failRate + "%";
//			returnModel.put("passcount", typecount);
//			returnModel.put("failcount", totalcount - typecount);
            returnModel.put("passrate", passRate);
            returnModel.put("failrate", failRate);
            returnModel.put("type", keyname);
            result.add(returnModel);
        }

        List<Map<String, Object>> scorelist = examinesubjecttypeService.listSQLQuery(" select p.name,IFNULL(a.score,0) score from u_examinestudent a  left join p_person p on p.id=a.PERSONID\n" +
                "where a.EXAMINEID='" + examined + "' ");
        List<Map<String, Object>> avgscore = examinesubjecttypeService.listSQLQuery(" select ROUND(AVG(IFNULL(a.score,0)),2) as avgScore  from u_examinestudent a where a.EXAMINEID='" + examined + "' ");
        Map statistics = new HashMap();
        statistics.put("scorelist", scorelist);
        statistics.put("avgscore", avgscore);
        result.add(statistics);
        return Result.OK(result);
    }

    @RequestMapping(value = {"/submitCwexamine/{submittype}"}, method = RequestMethod.POST)
    public Result saveCoursewareExaminestudentrecord(
            @PathVariable("submittype") String subimittype,
            @RequestBody Examinestudentrecord[] examinestudentrecords) {
        String studentid = "";
        double score = 0.00;
        List<Examinestudentrecord> returnRecords = new ArrayList<Examinestudentrecord>();
        for (int i = 0; i < examinestudentrecords.length; i++) {
            Examinestudentrecord examinestudentrecord = examinestudentrecords[i];
            if (studentid.isEmpty())
                studentid = StringHelper.null2String(examinestudentrecord
                        .getStudentid());
            double sorcepoint = examinestudentrecordService.createRecordScore2(examinestudentrecord);
            examinestudentrecord.setScore(sorcepoint);
            score = score + sorcepoint;
            examinestudentrecordService.saveOrUpdate(examinestudentrecord);
            returnRecords.add(examinestudentrecord);
        }
        if (!studentid.isEmpty()) {
            Examinestudent examinestudent = examinestudentService.getById(studentid);
            if (subimittype.equals("1")) {
                examinestudent.setExaminestatus("3");
                examinestudent.setScore(score);
            } else {
                examinestudent.setExaminestatus("5");
                examinestudent.setScore(score);
            }
            examinestudentService.saveOrUpdate(examinestudent);
        }
//		List<ExaminestudentrecordViewModel> sModelList = new ArrayList<ExaminestudentrecordViewModel>();
//		// 对返回的数据进行封装，加入该试卷题目的selectitem
//		sModelList = examinestudentrecordService.addTitleSubject(returnRecords,
//				examinepageService, commonBaseApi);
//		mm.put(ModelMapKey.RESULTKEY, examinestudent);
        return Result.OK();
    }

}
