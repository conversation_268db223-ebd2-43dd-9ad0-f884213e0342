package com.sbtr.workflow.service;

import com.sbtr.workflow.model.ActMyFlowAgent;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;

import java.util.List;


/**
 * 流程每个节点所对应按钮
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-23 07:12:25
 */
public interface ActMyFlowAgentService extends WorkflowBaseService<ActMyFlowAgent, String> {

    /**
     * 分页
     *
     * @return
     * @Param pageParam 分页参数
     * @Param buttonCode 按钮标识
     * @Param buttonName 按钮名称
     */
    PageSet<ActMyFlowAgent> getPageSet(PageParam pageParam, String agentName,String mandatorName,String agent,String mandator);

    /**
     * 批量删除
     *
     * @return
     * @Param uuid 主键
     */
    int executeDeleteBatch(String[] uuids);

    /**
     * 批量删除
     *
     * @return
     * @Param uuid 主键
     */
    List<String> getMandatorsByAgent(String agent,String currentTime);

    Integer save(ActMyFlowAgent flowAgent);
}
