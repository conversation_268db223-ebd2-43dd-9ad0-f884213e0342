package com.lms.common.util;

import cn.hutool.core.util.ObjectUtil;
import org.apache.commons.io.IOUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Enumeration;

public class ServletUtil {

    public static HttpServletRequest getRequest() {
//            ServerWebExchangeContext context = RequestContextHolder.getCurrentRequestAttributes();
//            if (context instanceof ServerWebExchangeContext) {
//                return ((ServerWebExchangeContext) context).getExchange();
//            }
//            return null;
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        return request;
    }

    public static String getIpAddr() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();;
        String ip = request.getRemoteAddr();
        if (StringHelper.isEmpty(ip) && "127.0.0.1".equals(ip)) {
            try {
                ip = InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException e) {
                e.printStackTrace();
            }
        }
        return ip;
    }

    public static String getToken() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            HttpServletRequest request = requestAttributes.getRequest();
            String token = StringHelper.null2String(request.getHeader(ConstParamUtil.X_ACCESS_TOKEN));
            return token;
        }
        return null;
    }

    private String getToken2(HttpServletRequest request) {
        String paramToken = request.getParameter(ConstParamUtil.X_ACCESS_TOKEN);
        return paramToken;
    }

    public static String getRequestDetail() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        StringBuffer requestDetail = new StringBuffer();
        //记录请求方法，请求地址信息
        requestDetail.append("Method Name : " + request.getMethod());
        requestDetail.append("REQUEST URL Name : "+request.getRequestURL());
        doLineSeparator(requestDetail);

        //记录请求头信息
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            requestDetail.append("Header Name : " + headerName + ", Value : " + headerValue);
            doLineSeparator(requestDetail);
        }

        //记录请求参数信息
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String parameterName = parameterNames.nextElement();
            String parameterValue = request.getParameter(parameterName);
            requestDetail.append("Paramter Name : " + parameterName + ", Value : " + parameterValue);
            doLineSeparator(requestDetail);
        }
        //记录请求Body信息
        try {
            String requestBody = IOUtils.toString(request.getInputStream(), "UTF-8");
            requestDetail.append("RequestBody Info: " + requestBody);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return requestDetail.toString();
    }

    private static StringBuffer doLineSeparator(StringBuffer requestDetail) {
        requestDetail.append(System.lineSeparator());
        requestDetail.append("==============");
        requestDetail.append(System.lineSeparator());
        return requestDetail;
    }


}
