package com.lms.system.storage.service.impl;

import com.lms.common.config.FileStorageProperties;
import com.lms.system.storage.factory.FileStorageFactory;
import com.lms.system.storage.service.FileStorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.Map;

/**
 * 统一文件存储服务实现
 * 根据配置自动选择合适的存储策略
 */
@Slf4j
@Service("unifiedFileStorageService")
public class UnifiedFileStorageServiceImpl implements FileStorageService {

    @Autowired
    private FileStorageFactory fileStorageFactory;

    @Autowired
    private FileStorageProperties fileStorageProperties;

    /**
     * 获取默认存储策略
     *
     * @return 默认存储策略实例
     */
    private FileStorageService getDefaultStrategy() {
        String defaultStrategyType = fileStorageProperties.getDefaultStrategy();
        return fileStorageFactory.getStrategy(defaultStrategyType);
    }

    /**
     * 根据存储类型获取策略
     *
     * @param storageType 存储类型，如果为空则使用默认策略
     * @return 存储策略实例
     */
    private FileStorageService getStrategy(String storageType) {
        if (storageType == null || storageType.trim().isEmpty()) {
            return getDefaultStrategy();
        }
        return fileStorageFactory.getStrategy(storageType);
    }

    @Override
    public String upload(MultipartFile file) throws Exception {
        FileStorageService strategy = getDefaultStrategy();
        String result = strategy.upload(file);
        log.info("使用默认策略 [{}] 上传文件: {}",
                strategy.getStorageType(), file.getOriginalFilename());
        return result;
    }

    /**
     * 使用指定存储策略上传文件
     *
     * @param storageType 存储策略类型
     * @param file        要上传的文件
     * @return 文件标识符
     * @throws Exception 上传异常
     */
    public String upload(String storageType, MultipartFile file) throws Exception {
        FileStorageService strategy = getStrategy(storageType);
        String result = strategy.upload(file);
        log.info("使用指定策略 [{}] 上传文件: {}",
                strategy.getStorageType(), file.getOriginalFilename());
        return result;
    }

    /**
     * 使用指定存储策略上传文件到指定存储桶
     *
     * @param storageType 存储策略类型
     * @param bucketName  存储桶名称
     * @param file        要上传的文件
     * @return 文件标识符
     * @throws Exception 上传异常
     */
    public String upload(String storageType, String bucketName, MultipartFile file) throws Exception {
        FileStorageService strategy = getStrategy(storageType);
        String result = strategy.upload(bucketName, file);
        log.info("使用指定策略 [{}] 上传文件到存储桶 [{}]: {}",
                strategy.getStorageType(), bucketName, file.getOriginalFilename());
        return result;
    }

    @Override
    public Map<String, String> uploadFile(MultipartFile file, String objectName) throws Exception {
        FileStorageService strategy = getDefaultStrategy();
        Map<String, String> result = strategy.uploadFile(file, objectName);
        // 在结果中添加存储策略信息
        result.put("storageType", strategy.getStorageType());
        result.put("storageDescription", strategy.getStorageDescription());

        log.info("使用默认策略 [{}] 上传文件并返回详细信息: {}",
                strategy.getStorageType(), objectName);
        return result;
    }

    @Override
    public void uploadLocalFile(String fileLocation, String bucketName, String objectName) throws Exception {
        FileStorageService strategy = getDefaultStrategy();
        strategy.uploadLocalFile(fileLocation, bucketName, objectName);
        log.info("使用默认策略 [{}] 上传本地文件: {} -> {}/{}",
                strategy.getStorageType(), fileLocation, bucketName, objectName);
    }

    @Override
    public void download(String fileName, HttpServletResponse response) throws Exception {
        FileStorageService strategy = getDefaultStrategy();
        strategy.download(fileName, response);
        log.info("使用默认策略 [{}] 下载文件: {}",
                strategy.getStorageType(), fileName);
    }

    @Override
    public void download(String bucketName, String fileName, HttpServletResponse response) throws Exception {
        FileStorageService strategy = getDefaultStrategy();
        strategy.download(bucketName, fileName, response);
        log.info("使用默认策略 [{}] 从存储桶 [{}] 下载文件: {}",
                strategy.getStorageType(), bucketName, fileName);
    }

    @Override
    public InputStream download(String fileName) throws Exception {
        FileStorageService strategy = getDefaultStrategy();
        InputStream result = strategy.download(fileName);
        log.info("使用默认策略 [{}] 获取文件流: {}",
                strategy.getStorageType(), fileName);
        return result;
    }

    @Override
    public InputStream download(String bucketName, String fileName) throws Exception {
        FileStorageService strategy = getDefaultStrategy();
        InputStream result = strategy.download(bucketName, fileName);
        log.info("使用默认策略 [{}] 从存储桶 [{}] 获取文件流: {}",
                strategy.getStorageType(), bucketName, fileName);
        return result;
    }

    @Override
    public boolean checkFileIsExist(String fileName) {
        FileStorageService strategy = getDefaultStrategy();
        boolean exists = strategy.checkFileIsExist(fileName);
        log.debug("使用默认策略 [{}] 检查文件是否存在: {} -> {}",
                strategy.getStorageType(), fileName, exists);
        return exists;
    }

    @Override
    public boolean checkFileIsExist(String bucketName, String fileName) {
        FileStorageService strategy = getDefaultStrategy();
        boolean exists = strategy.checkFileIsExist(bucketName, fileName);
        log.debug("使用默认策略 [{}] 检查文件是否存在: {}/{} -> {}",
                strategy.getStorageType(), bucketName, fileName, exists);
        return exists;
    }

    @Override
    public boolean deleteFile(String fileName) {
        FileStorageService strategy = getDefaultStrategy();
        boolean deleted = strategy.deleteFile(fileName);
        log.info("使用默认策略 [{}] 删除文件: {} -> {}",
                strategy.getStorageType(), fileName, deleted ? "成功" : "失败");
        return deleted;
    }

    @Override
    public boolean deleteFile(String bucketName, String fileName) {
        FileStorageService strategy = getDefaultStrategy();
        boolean deleted = strategy.deleteFile(bucketName, fileName);
        log.info("使用默认策略 [{}] 从存储桶 [{}] 删除文件: {} -> {}",
                strategy.getStorageType(), bucketName, fileName, deleted ? "成功" : "失败");
        return deleted;
    }

    @Override
    public boolean bucketExists(String bucketName) {
        FileStorageService strategy = getDefaultStrategy();
        return strategy.bucketExists(bucketName);
    }

    @Override
    public boolean makeBucket(String bucketName) {
        FileStorageService strategy = getDefaultStrategy();
        boolean created = strategy.makeBucket(bucketName);
        log.info("使用默认策略 [{}] 创建存储桶: {} -> {}",
                strategy.getStorageType(), bucketName, created ? "成功" : "失败");
        return created;
    }

    @Override
    public String getPreviewUrl(String fileName) {
        FileStorageService strategy = getDefaultStrategy();
        return strategy.getPreviewUrl(fileName);
    }

    @Override
    public String getPreviewUrl(String bucketName, String fileName) {
        FileStorageService strategy = getDefaultStrategy();
        return strategy.getPreviewUrl(bucketName, fileName);
    }

    @Override
    public String generateFileUrl(String bucketName, String objectName) {
        FileStorageService strategy = getDefaultStrategy();
        return strategy.generateFileUrl(bucketName, objectName);
    }

    @Override
    public String getStorageType() {
        FileStorageService strategy = getDefaultStrategy();
        return strategy.getStorageType();
    }

    @Override
    public String getStorageDescription() {
        FileStorageService strategy = getDefaultStrategy();
        return strategy.getStorageDescription();
    }

    /**
     * 切换默认存储策略
     *
     * @param storageType 新的存储策略类型
     * @return 切换是否成功
     */
    public boolean switchDefaultStrategy(String storageType) {
        try {
            // 验证新策略是否可用
            FileStorageService newStrategy = fileStorageFactory.getStrategy(storageType);
            if (newStrategy == null) {
                log.error("切换存储策略失败: 策略不存在 - {}", storageType);
                return false;
            }
            // 检查策略是否启用
            if (!fileStorageProperties.isStorageEnabled(storageType)) {
                log.error("切换存储策略失败: 策略未启用 - {}", storageType);
                return false;
            }
            // 更新默认策略配置
            String oldStrategy = fileStorageProperties.getDefaultStrategy();
            fileStorageProperties.setDefaultStrategy(storageType);
            log.info("成功切换默认存储策略: {} -> {}", oldStrategy, storageType);
            return true;
        } catch (Exception e) {
            log.error("切换存储策略异常: {} - {}", storageType, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取当前配置信息
     *
     * @return 配置信息
     */
    public String getConfigInfo() {
        return fileStorageProperties.getConfigSummary();
    }

    /**
     * 健康检查 - 检查所有启用的存储策略是否正常
     *
     * @return 健康检查结果
     */
    public Map<String, Boolean> healthCheck() {
        Map<String, Boolean> healthStatus = new java.util.HashMap<>();
        // 检查OSS
        if (fileStorageProperties.getOss().isEnabled()) {
            try {
                FileStorageService ossStrategy = fileStorageFactory.getStrategy("oss");
                healthStatus.put("oss", ossStrategy != null);
            } catch (Exception e) {
                healthStatus.put("oss", false);
            }
        }
        // 检查MinIO
        if (fileStorageProperties.getMinio().isEnabled()) {
            try {
                FileStorageService minioStrategy = fileStorageFactory.getStrategy("minio");
                healthStatus.put("minio", minioStrategy != null);
            } catch (Exception e) {
                healthStatus.put("minio", false);
            }
        }
        // 检查本地存储
        if (fileStorageProperties.getLocal().isEnabled()) {
            try {
                FileStorageService localStrategy = fileStorageFactory.getStrategy("local");
                healthStatus.put("local", localStrategy != null);
            } catch (Exception e) {
                healthStatus.put("local", false);
            }
        }
        return healthStatus;
    }
}
