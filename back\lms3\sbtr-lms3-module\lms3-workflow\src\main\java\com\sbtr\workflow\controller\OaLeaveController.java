package com.sbtr.workflow.controller;

import cn.ewsd.common.annotation.ControllerLog;
import cn.ewsd.common.controller.BaseController;
import cn.ewsd.common.utils.DateUtils;
import com.google.gson.Gson;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.utils.BaseUtils;
import cn.ewsd.common.utils.StringUtils;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sbtr.workflow.model.ActMyModel;
import com.sbtr.workflow.model.OaLeave;
import com.sbtr.workflow.service.*;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.vo.ProcessDefinitionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;

/**
 * 休假流程案例
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
@Api(tags = {"在业务中启动流程"})
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/oaLeave")
public class OaLeaveController extends BaseController {

    @Autowired
    private OaLeaveService oaLeaveService;
    @Resource
    public BusinessSystemDataService businessSystemDataService;
    private static final Logger LOGGER = LoggerFactory.getLogger(OaLeaveController.class);


    @ApiOperation(value = "获得OaLeave分页集数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String"),
    })
    @ResponseBody
    @RequestMapping(value = "/getPageSet", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam, String title) {
        String filterSort = "";
        filterSort = BaseUtils.filterSort(request, filterSort);
        PageSet<OaLeave> pageSet = oaLeaveService.getPageSet(pageParam, filterSort, title);
        return pageSet;
    }

    @Autowired
    private ApiFlowableProcessDefinitionService apiFlowableProcessDefinitionService;


    //
    @ResponseBody
    @RequestMapping(value = "/getCustomInterface", method = RequestMethod.POST)
    public List getCustomInterface() {
        List<ProcessDefinitionVo> list = apiFlowableProcessDefinitionService.getCustomInterface();
        return list;
    }


    @Autowired
    private ApiFlowableProcessInstanceService apiFlowableProcessInstanceService;

    @ApiOperation(value = "保存请假业务信息，并且启动流程")
    @ApiImplicitParam(name = "oaLeave", value = "保存OaLeave模块数据", required = true, dataType = "OaLeave")
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ControllerLog(description = "保存OaLeave模块数据")
    public Object save(@ModelAttribute OaLeave oaLeave) {
        try {
            String businessUuid = "";
            oaLeave.setState("审核中");
            //判断是草稿提交按钮的话就更新
            if (!StringUtils.isNullOrEmpty(oaLeave.getUuid())) {
                businessUuid = oaLeave.getUuid();
                oaLeaveService.updateByPrimaryKeySelective(getUpdateData(oaLeave));
            } else {
                businessUuid = BaseUtils.UUIDGenerator();
                //启动流程接口
                //@param modelKey      模型Key 必填
                //* @param businessUuid  业务Id 必填
                //* @param businessTitle 标题  必填
                //* @param assignUser    下一步处理人工号 非必填
                //* @param duplicateUser    抄送人工号,多个逗号隔开 非必填
                //* @param skipNode    是否默认处理第一步 非必填
                //* @param Map   表单数据 防止第一个任务节点后马上接分支  非必填 但是第一个任务节点后有分支必填该字段
                JSONObject json = JSONObject.parseObject(JSON.toJSONString(oaLeave));
                Object object = apiFlowableProcessInstanceService.startProcessInstanceByKey(
                        "process1713920470673",
                        businessUuid,
                        /*businessSystemDataService.getUserNameId() + "发起流程",*/
                        "请假审批申请",
                        null,
                        null,
                        "true",
                        json
                        );
                if (((HashMap) object).get("statusCode").equals(300)) {
//                    return failure("提交出现异常，原因为===" + ((HashMap) object).get("message"));
                    return failure("提交出现异常，请确认存在该流程数据并已发布流程");
                }
                oaLeave.setUuid(businessUuid);
                oaLeaveService.insertSelective(getSaveData(oaLeave));
            }
            return success("保存成功！");
        } catch (Exception e) {
            //TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure("保存失败！" + e.getMessage());
        }

    }


    @ResponseBody
    @RequestMapping(value = "/saveOaLeaveData")
    @ControllerLog(description = "保存OaLeave模块数据")
    public Object saveOaLeaveData(@ModelAttribute OaLeave oaLeave) {
        oaLeave.setState("审核中");
        oaLeave.setCreatorId(this.getCurrentUserNameId());
        oaLeave.setCreator(this.getCurrentUserName());
        oaLeave.setCreateTime(new Date());
        oaLeave.setCreatorOrgId(Integer.parseInt(businessSystemDataService.getOrgId()));
        int result = oaLeaveService.insertSelective(oaLeave);
        return result > 0 ? success("保存成功！") : failure("保存失败！");

    }

    @ResponseBody
    @RequestMapping(value = "/startDmflow")
    public Object startDmflow(String id,String modelKey) {
        HashMap<String, Object> businessDataMap = apiFlowableProcessInstanceService.selectByKey("select * from pro_dm where id='"+id+"'");
        JSONObject json = JSONObject.parseObject(new Gson().toJson(businessDataMap));
        json.put("ASD","jianghaisu,dudu");
        return apiFlowableProcessInstanceService.startProcessInstanceByKey(
                modelKey,
                id,
                /*businessSystemDataService.getUserNameId() + "发起流程",*/
                "DM审批申请"+ DateUtils.getCurrentDatetime("yyyy-MM-dd HH:mm:ss"),
                null,
                null,
                "false",
                json
        );
    }

    @ResponseBody
    @RequestMapping(value = "/getDetailByUuid")
    public Object getDetailByUuid(String uuid) {
        OaLeave info = oaLeaveService.selectByPrimaryKey(uuid);
        return info;
    }


    @ApiOperation(value = "更新OaLeave模块数据")
    @ApiImplicitParam(name = "oaLeave", value = "更新OaLeave模块数据", required = true, dataType = "OaLeave")
    @ResponseBody
    @RequestMapping(value = "/update")
    public Object update(@ModelAttribute OaLeave oaLeave) {
        int result = oaLeaveService.updateByPrimaryKeySelective(getUpdateData(oaLeave));
        return result > 0 ? success("更新成功！") : failure("更新失败！");
    }

    @ApiOperation(value = "删除OaLeave模块数据")
    @ApiImplicitParam(name = "oaLeave", value = "删除OaLeave模块数据", required = true, dataType = "OaLeave")
    @ResponseBody
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.POST)
    public Object deleteBatch(@RequestParam String[] uuid) {
        int result = oaLeaveService.executeDeleteBatch(uuid);
        return result > 0 ? success("删除成功！") : failure("删除失败！");
    }

    @ApiOperation(value = "草稿")
    @ApiImplicitParam(name = "oaLeave", value = "保存OaLeave模块数据", required = true, dataType = "OaLeave")
    @ResponseBody
    @RequestMapping(value = "/draft", method = RequestMethod.POST)
    @ControllerLog(description = "保存OaLeave模块数据")
    public Object draft(@ModelAttribute OaLeave oaLeave) {
        if (!StringUtils.isNullOrEmpty(oaLeave.getUuid())) {
            oaLeaveService.updateByPrimaryKeySelective(getUpdateData(oaLeave));
            return success("修改成功！");
        }
        oaLeave.setState("草稿");
        int result = oaLeaveService.insertSelective(getSaveData(oaLeave));
        return result > 0 ? success("暂存成功！") : failure("暂存失败！");
    }


    @Autowired
    private HistoryService historyService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private ActMyNodeButtonService flowNodeButtonService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private ActMyNodeFieldService flowNodeFieldService;

    @Autowired
    private ActMyModelService flowModelService;

    @Autowired

    private RepositoryService repositoryService;


    @ApiOperation(value = "点击流程图")
    @ResponseBody
    @RequestMapping(value = "/clickFlowChart", method = RequestMethod.POST)
    // 点击流程图
    public Object clickFlowChart(String uuid) {
        /**1:使用历史的流程实例查询，返回历史的流程实例对象，获取流程实例ID*/
        HistoricProcessInstance hpi = historyService.createHistoricProcessInstanceQuery()//对应历史的流程实例表
                .processInstanceBusinessKey(uuid)//使用BusinessKey字段查询
                .singleResult();
        if (ObjectUtil.isNull(hpi)) {
            LOGGER.error("流程历史数据被删除了！！！");
            return failure("查询不到历史流程数据！！！,该流程数据已被删除");
        }
        //流程实例ID
        String processInstanceId = hpi.getId();
        //List<Comment> list = taskService.getProcessInstanceComments(processInstanceId);
        String processDefinitionId = hpi.getProcessDefinitionId();
        ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) repositoryService.getProcessDefinition(processDefinitionId);
        String modelKey = processDefinitionEntity.getKey();
        String taskId = "";
        Example example = new Example(ActMyModel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modelKey);
        if (org.apache.commons.lang.StringUtils.isNotBlank(processDefinitionId)) {
            criteria.andEqualTo("procdefId", processDefinitionId);
        }
        List<ActMyModel> lists = flowModelService.selectByExample(example);
        if (CollectionUtils.isEmpty(lists)) {
            return failure("流程与表单关联表查找不到数据,请检查");
        }
        HashMap<String, Object> map = new HashMap<>();
        //if (org.apache.commons.lang.StringUtils.isNotBlank(taskId)) {
        //    List<CommentBean> commentBeanList = workflowService.getCommentBeanByTaskId(taskId);
        //    map.put("commentBeanList", commentBeanList);
        //}
        map.put("lists", lists.get(0));
        // 查询历史节点表 ACT_HI_ACTINST
        List<HistoricActivityInstance> historicActivityInstanceList = getHistoricActivityInstAsc(processInstanceId);
        //1 正在执行的节点
        List<String> runningActivitiIdList = new ArrayList<String>();
        //2 获取已流经的流程线
        List<String> highLightedFlowIds = new ArrayList<>();
        //3.已执行历史节点
        List<String> executedActivityIdList = new ArrayList<String>();
        historicActivityInstanceList.forEach(historicActivityInstance -> {
            //1
            if (null == historicActivityInstance.getEndTime()) {
                runningActivitiIdList.add(historicActivityInstance.getActivityId());
            }
            //2
            if (historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                highLightedFlowIds.add(historicActivityInstance.getActivityId());
            }
            //3
            if (null != historicActivityInstance.getEndTime() && !historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                executedActivityIdList.add(historicActivityInstance.getActivityId());
            }

        });

        map.put("inProgress", runningActivitiIdList);
        highLightedFlowIds.addAll(executedActivityIdList);
        map.put("notInProgress", highLightedFlowIds);

        //4查询当前节点的按钮

        //4.通过流程定义id与modelKey查询 流程节点按钮
        //List<ActMyNodeButton> flowNodeButtons = flowNodeButtonService.getListByActDeModelKeyAndProcdefId(modelKey, processDefinitionId);
        //  处理节点表单
        //for (int i = 0; i < flowNodeButtons.size(); i++) {
        //    flowNodeButtons.get(i).setNodeFormPath(flowNodeButtons.get(0).getNodeFormPath());
        //}
        //map.put("flowNodeButtons", flowNodeButtons);
        map.put("flowNodeButtons", null);
        //  以下为特殊处理 自己写的表单页面挂载流程需要返回业务详情数据  广州赛宝腾睿信息科技有限公司
        //2  通过任务对象获取流程实例
        //ProcessInstance pi = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        ////3 通过流程实例获取“业务键”
        //String businessKey = pi.getBusinessKey();
        ////表单获取详情数据的请求地址
        //String str = flowNodeButtons.get(0).getNodeFormEditPath();
        //JSONObject json_test = null;
        //if (org.apache.commons.lang.StringUtils.isNotBlank(str)) {
        //    String gateway = request.getHeader("Gateway");
        //    String token = request.getHeader(ConstParamUtil.X_ACCESS_TOKEN);
        //    //链式构建请求，带cookie请求
        //    Map<String, Object> paramMap = new HashMap<>();
        //    paramMap.put("uuid", businessKey);
        //    String result2 = HttpRequest.post(gateway + str)
        //            .cookie("token=" + token)
        //            .form(paramMap)
        //            .timeout(20000)
        //            .execute().body();
        //    json_test = JSONObject.parseObject(result2);
        //}
        //map.put("businessData", json_test);
        map.put("businessData", null);
        //5 查询当前节点的字段属性
        map.put("flowNodeField", "");
        return map;

    }

    /**
     * 通过流程实例ID获取流程中已经执行的节点，按照执行先后顺序排序
     *
     * @param procInstId
     * @return
     */
    public List<HistoricActivityInstance> getHistoricActivityInstAsc(String procInstId) {
        return historyService.createHistoricActivityInstanceQuery().processInstanceId(procInstId)
                .orderByHistoricActivityInstanceStartTime().asc().list();
    }

    @ApiOperation(value = "根据业务id获取审批意见")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "businessKey", value = "业务ID", defaultValue = "businessKey", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/getListCommentsByBusinessKey", method = RequestMethod.POST)
    public Object getListCommentsByBusinessKey(String businessKey) {
        return apiFlowableProcessInstanceService.getListCommentsByBusinessKey(businessKey);
    }


    @ApiOperation(value = "已完成数据点击流程图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "businessKey", value = "业务ID", defaultValue = "businessKey", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/historyClickDetails", method = RequestMethod.POST)
    public Object historyClickDetails(String uuid) {
        /**1:使用历史的流程实例查询，返回历史的流程实例对象，获取流程实例ID*/
        HistoricProcessInstance hpi = historyService.createHistoricProcessInstanceQuery()//对应历史的流程实例表
                .processInstanceBusinessKey(uuid)//使用BusinessKey字段查询
                .singleResult();
        //流程实例ID
        String processInstanceId = hpi.getId();

        String processDefinitionId = hpi.getProcessDefinitionId();
        ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) repositoryService.getProcessDefinition(processDefinitionId);
        String modelKey = processDefinitionEntity.getKey();

        Example example = new Example(ActMyModel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modelKey);
        if (org.apache.commons.lang.StringUtils.isNotBlank(processDefinitionId)) {
            criteria.andEqualTo("procdefId", processDefinitionId);
        }
        List<ActMyModel> lists = flowModelService.selectByExample(example);
        if (CollectionUtils.isEmpty(lists)) {
            return failure("流程与表单关联表查找不到数据,请检查");
        }

        HashMap<String, Object> map = new HashMap<>();
        //if (org.apache.commons.lang.StringUtils.isNotBlank(processInstanceId)) {
        //    List<CommentBean> commentBeanList = workflowService.getCommentListByProcessInstanceId(processInstanceId);
        //    map.put("commentBeanList", commentBeanList);
        //}
        map.put("lists", lists.get(0));

        // 查询历史节点表 ACT_HI_ACTINST
        List<HistoricActivityInstance> historicActivityInstanceList = getHistoricActivityInstAsc(processInstanceId);
        //1 正在执行的节点
        List<String> runningActivitiIdList = new ArrayList<String>();
        //2 获取已流经的流程线
        List<String> highLightedFlowIds = new ArrayList<>();
        //3.已执行历史节点
        List<String> executedActivityIdList = new ArrayList<String>();
        historicActivityInstanceList.forEach(historicActivityInstance -> {
            //1
            if (null == historicActivityInstance.getEndTime()) {
                runningActivitiIdList.add(historicActivityInstance.getActivityId());
            }
            //2
            if (historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                highLightedFlowIds.add(historicActivityInstance.getActivityId());
            }
            //3
            if (null != historicActivityInstance.getEndTime() && !historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                executedActivityIdList.add(historicActivityInstance.getActivityId());
            }

        });

        map.put("inProgress", runningActivitiIdList);
        highLightedFlowIds.addAll(executedActivityIdList);
        map.put("notInProgress", highLightedFlowIds);

        //4.通过流程定义id与modelKey查询查询所有流程节点按钮 并去重,再去遍历去查找详情数据
        //List<ActMyNodeButtonVo> flowNodeButtons = flowNodeButtonService.getListByActDeModelKeyAndProcdefIdAndNodeId(modelKey, processDefinitionId, nodeId);
        //for (ActMyNodeButtonVo actMyNodeButtonVo : flowNodeButtons) {
        //    String str = actMyNodeButtonVo.getNodeFormEditPath();
        //    if (org.apache.commons.lang.StringUtils.isNotBlank(str)) {
        //        String gateway = request.getHeader("Gateway");
        //        String token = request.getHeader(ConstParamUtil.X_ACCESS_TOKEN);
        //        //链式构建请求，带cookie请求
        //        Map<String, Object> paramMap = new HashMap<>();
        //        paramMap.put("uuid", uuid);
        //        String result2 = HttpRequest.post(gateway + str)
        //                .cookie("token=" + token)
        //                .form(paramMap)
        //                .timeout(20000)
        //                .execute().body();
        //        Map<String, Object> map1 = new HashMap<>();
        //        map1.put("businessData", JSONObject.parseObject(result2));
        //        actMyNodeButtonVo.setMap(map1);
        //    }
        //}
        //map.put("flowNodeButtons", flowNodeButtons);
        map.put("flowNodeButtons", null);
        map.put("businessData", null);
        return map;
    }

}
