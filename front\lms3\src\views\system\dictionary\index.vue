<template>
  <div class="dictionary-management">
    <!-- 页面标题 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <h2 class="sidebar-title">{{ translateText("数据字典类型") }}</h2>
      </div>
      <div class="sidebar-content">
        <!-- 筛选区域 -->
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item">
              <label class="filter-label">{{ translateText("字典类型") }}</label>
              <a-input
                v-model:value="filters.objname"
                :placeholder="translateText('请输入字典类型')"
                class="filter-input"
                allow-clear
              />
            </div>

            <div class="filter-actions">
              <a-button class="reset-btn" @click="handleReset">{{ translateText("重置") }}</a-button>
              <a-button
                type="primary"
                class="search-btn"
                :loading="loading"
                @click="handleSearch"
              >
                {{ translateText("查询") }}
              </a-button>
            </div>
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-section">
          <div class="action-buttons">
            <a-button type="primary" class="action-btn" @click="handleAdd">
              <template #icon>
                <Icon icon="ant-design:plus-outlined" />
              </template>
              {{ translateText("新增") }}
            </a-button>
          </div>
        </div>

        <!-- 数据表格 -->
        <div class="table-section">
          <div class="table-container">
            <a-table
              :columns="columns"
              :data-source="dictionaries"
              :loading="loading"
              :pagination="false"
              row-key="id"
              size="middle"
              class="dictionary-table"
            >
              <!-- 序号列 -->
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'index'">
                  {{ (pagination.current - 1) * pagination.size + index + 1 }}
                </template>

                <!-- 操作列 -->
                <template v-else-if="column.key === 'action'">
                  <div class="action-links">
                    <a-button
                      type="link"
                      size="small"
                      @click="handleEditSingle(record)"
                    >
                      {{ translateText("编辑") }}
                    </a-button>
                    <a-button
                      type="link"
                      size="small"
                      @click="handleListChildren(record)"
                    >
                      {{ translateText("子项") }}
                    </a-button>
                    <a-button
                      type="text"
                      size="small"
                      danger
                      style="padding: 0"
                      @click="handleDelete(record)"
                    >
                      {{ translateText("删除") }}
                    </a-button>
                  </div>
                </template>
              </template>
            </a-table>
          </div>
        </div>
        <!-- 分页栏 -->
        <div
          v-if="!loading && dictionaries.length > 0"
          class="pagination-section"
        >
          <div class="pagination-container">
            <a-pagination
              v-model:current="pagination.current"
              v-model:page-size="pagination.size"
              :total="pagination.total"
              :show-size-changer="true"
              :show-quick-jumper="true"
              :show-total="
                total =>
                  translateText('当前第{current}/{total}页', {
                    current: pagination.current,
                    total: Math.ceil(total / pagination.size)
                  })
              "
              :page-size-options="['10', '20', '50', '100']"
              :locale="paginationLocale"
              @change="handlePageChange"
              @show-size-change="handlePageSizeChange"
            />
          </div>
        </div>
      </div>

      <!-- 新增/编辑数据字典弹窗 -->
      <DictionaryAddForm
        :visible="addModalVisible"
        :dictionary="currentDictionary"
        :is-edit="isEdit"
        @close="addModalVisible = $event"
        @submit="fetchDictionaryList"
      />
    </div>
    <!-- 右侧子项区域 -->
    <div class="left-main-content">
      <div class="page-header">
        <h1 class="page-title">{{ translateText("数据字典选项") }}</h1>
      </div>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-item">
            <label class="filter-label">{{ translateText("选项名称") }}</label>
            <a-input
              v-model:value="filters2.objname"
              :placeholder="translateText('请输入选项名称')"
              class="filter-input"
              allow-clear
            />
          </div>

          <div class="filter-actions">
            <a-button class="reset-btn" @click="handleReset2">{{ translateText("重置") }}</a-button>
            <a-button
              type="primary"
              class="search-btn"
              :loading="loading"
              :disabled="!currentDictionary"
              @click="handleSearch2"
            >
              {{ translateText("查询") }}
            </a-button>
          </div>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-section">
        <div class="action-buttons">
          <a-button
            type="primary"
            class="action-btn"
            :disabled="!currentDictionary"
            @click="handleAdd2"
          >
            <template #icon>
              <Icon icon="ant-design:plus-outlined" />
            </template>
            {{ translateText("新增") }}
          </a-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <div class="table-container">
          <a-table
            :columns="columns2"
            :data-source="dictionaryItems"
            :loading="loading"
            :pagination="false"
            row-key="id"
            size="middle"
            class="dictionary-table"
          >
            <!-- 序号列 -->
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (pagination2.current - 1) * pagination2.size + index + 1 }}
              </template>

              <!-- 操作列 -->
              <template v-else-if="column.key === 'action'">
                <div class="action-links">
                  <a-button
                    type="link"
                    size="small"
                    @click="handleEditSingle2(record)"
                  >
                    {{ translateText("编辑") }}
                  </a-button>
                  <a-button
                    type="text"
                    size="small"
                    danger
                    style="padding: 0"
                    @click="handleDelete2(record)"
                  >
                    {{ translateText("删除") }}
                  </a-button>
                </div>
              </template>
            </template>
          </a-table>
        </div>
      </div>
      <!-- 分页栏 -->
      <div
        v-if="!loading && dictionaryItems.length > 0"
        class="pagination-section"
      >
        <div class="pagination-container">
          <a-pagination
            v-model:current="pagination2.current"
            v-model:page-size="pagination2.size"
            :total="pagination2.total"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :show-total="
              total =>
                translateText('当前第{current}/{total}页', {
                  current: pagination2.current,
                  total: Math.ceil(total / pagination2.size)
                })
            "
            :page-size-options="['10', '20', '50', '100']"
            :locale="paginationLocale"
            @change="handlePageChange"
            @show-size-change="handlePageSizeChange"
          />
        </div>
      </div>
      <!-- 新增/编辑数据字典弹窗 -->
      <DictionaryItemAddForm
        :visible="addItemModalVisible"
        :dictionaryitem="currentDictionaryItem"
        :is-edit="isEdit"
        @close="addItemModalVisible = $event"
        @submit="fetchDictionaryItemList"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { message, Modal } from "ant-design-vue";
import { Icon } from "@iconify/vue";
import DictionaryAddForm from "./modules/addForm.vue";
import DictionaryItemAddForm from "./modules/addItemForm.vue";
import type {
  Dictionary,
  DictionaryFilter,
  DictionaryItem
} from "@/types/system";
import { paginationLocale } from "@/plugins/i18n";
import { translateText } from "@/utils/translation";

const loading = ref(false);
const dictionaries = ref<Dictionary[]>([]);
const dictionaryItems = ref<[]>([]);
const addModalVisible = ref(false);
const addItemModalVisible = ref(false);
const currentDictionary = ref<Dictionary | null>(null);
const currentDictionaryItem = ref<DictionaryItem | null>(null);
const isEdit = ref(false);

import {
  getSelectitemtypeListPage,
  getSelectitemListPage,
  batchRemoveSelectitem,
  batchRemoveSelectitemtype
} from "@/api/system.ts";
// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
  pages: 0
});
const pagination2 = reactive({
  current: 1,
  size: 10,
  total: 0,
  pages: 0
});
// 筛选条件
const filters = reactive<DictionaryFilter>({
  objname: ""
});
// 筛选条件
const filters2 = reactive<DictionaryFilter>({
  objname: ""
});

// 表格列定义
const columns = [
  {
    title: translateText("序号"),
    dataIndex: "seqno",
    key: "seqno",
    width: 200,
    align: "center"
  },
  {
    title: translateText("字典类型"),
    dataIndex: "objname",
    key: "objname",
    width: 200
  },
  {
    title: translateText("描述"),
    dataIndex: "objdesc",
    key: "objdesc",
    ellipsis: true
  },
  {
    title: translateText("操作"),
    key: "action",
    width: 200,
    align: "center"
  }
];

// 表格列定义
const columns2 = [
  {
    title: translateText("序号"),
    dataIndex: "seqno",
    key: "seqno",
    width: 200,
    align: "center"
  },
  {
    title: translateText("选项名称"),
    dataIndex: "objname",
    key: "objname",
    width: 200
  },
  {
    title: translateText("操作"),
    key: "action",
    width: 200,
    align: "center"
  }
];

// 获取数据字典列表
const fetchDictionaryList = async () => {
  try {
    loading.value = true;
    let params = {
      pageIndex: pagination.current,
      pageSize: pagination.size,
      PARAMETER_S_Like_objname: filters.objname,
      PARAMETER_I_EQ_status: 1,
      orderName: "seqno",
      sortType: "asc"
    };
    const response = await getSelectitemtypeListPage(params);
    if (response.success) {
      dictionaries.value = response.result.records;
      pagination.total = response.result.total;
      pagination.pages = response.result.pages;
    } else {
      message.error(response.message || translateText("获取模型列表失败"));
    }
  } catch (error) {
    console.error("获取数据字典列表失败:", error);
    message.error(translateText("获取数据字典列表失败"));
  } finally {
    loading.value = false;
  }
};

const fetchDictionaryItemList = async () => {
  try {
    loading.value = true;
    let params = {
      pageIndex: pagination2.current,
      pageSize: pagination2.size,
      PARAMETER_S_Like_objname: filters2.objname,
      PARAMETER_I_EQ_status: 1,
      PARAMETER_S_EQ_typeid: currentDictionary.value.id,
      orderName: "seqno",
      sortType: "asc"
    };
    const response = await getSelectitemListPage(params);
    if (response.success) {
      dictionaryItems.value = response.result.records;
      pagination2.total = response.result.total;
      pagination2.pages = response.result.pages;
    } else {
      message.error(response.message || translateText("获取模型列表失败"));
    }
  } catch (error) {
    console.error("获取数据字典列表失败:", error);
    message.error(translateText("获取数据字典列表失败"));
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchDictionaryList();
};
const handleSearch2 = () => {
  pagination2.current = 1;
  fetchDictionaryItemList();
};

// 重置筛选条件
const handleReset = () => {
  filters.objname = "";
};

// 重置筛选条件
const handleReset2 = () => {
  filters2.objname = "";
};

// 新增数据字典
const handleAdd = () => {
  currentDictionary.value = null;
  isEdit.value = false;
  addModalVisible.value = true;
};
// 新增数据字典选项
const handleAdd2 = () => {
  if (!currentDictionary.value) {
    message.warn(translateText("请先选择一个数据字典,点击'子项'"));
    return;
  } else {
    currentDictionaryItem.value = { typeid: currentDictionary.value.id };
  }
  isEdit.value = false;
  addItemModalVisible.value = true;
};

// 编辑单个数据字典
const handleEditSingle = (dict: Dictionary) => {
  currentDictionary.value = dict;
  isEdit.value = true;
  addModalVisible.value = true;
};
// 编辑单个数据字典
const handleEditSingle2 = (dictionaryItem: DictionaryItem) => {
  currentDictionaryItem.value = dictionaryItem;
  isEdit.value = true;
  addItemModalVisible.value = true;
};

const handleListChildren = formData => {
  // router.push({ path: "/system/dictionaryitem", query: { pid: formData.id } });
  currentDictionary.value = formData;
  handleSearch2();
};

const handleDelete = async params => {
  Modal.confirm({
    title: translateText("确认信息"),
    content: translateText("确认要删除选中的记录？"),
    okText: translateText("确认"),
    cancelText: translateText("取消"),
    onOk() {
      batchRemoveSelectitemtype({ ids: params.id }).then(result => {
        if (result.success) {
          message.success(result.message || "");
          handleSearch();
        } else {
          message.error(result.message || "");
        }
      });
    }
  });
};

const handleDelete2 = async params => {
  Modal.confirm({
    title: translateText("确认信息"),
    content: translateText("确认要删除选中的记录？"),
    okText: translateText("确认"),
    cancelText: translateText("取消"),
    onOk() {
      batchRemoveSelectitem({ ids: params.id }).then(result => {
        if (result.success) {
          message.success(result.message || "");
          handleSearch2();
        } else {
          message.error(result.message || "");
        }
      });
    }
  });
};

// 分页变化
const handlePageChange = (page: number, pageSize: number) => {
  pagination.current = page;
  pagination.size = pageSize;
  fetchDictionaryList();
};

// 页面大小变化
const handlePageSizeChange = (current: number, size: number) => {
  pagination.current = 1;
  pagination.size = size;
  fetchDictionaryList();
};

// 页面加载时获取数据
onMounted(() => {
  fetchDictionaryList();
});
</script>

<style lang="scss" scoped>
.dictionary-management {
  height: 100%;
  display: flex;
  background-color: var(--gray-50);
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}
.left-main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.sidebar {
  width: 60%;
  background-color: white;
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--gray-200);
}

.sidebar-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--gray-200);
}

.sidebar-title {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--gray-900);
  margin: 0;
}

.sidebar-content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.page-header {
  padding: 15px;
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
}

.page-title {
  font-size: var(--text-xl);
  font-weight: var(--font-medium);
  color: var(--gray-900);
  margin: 0;
}

.filter-section {
  padding: var(--spacing-6);
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
}

.filter-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.filter-label {
  font-size: var(--text-sm);
  color: var(--gray-600);
  white-space: nowrap;
  min-width: 80px;
}

.filter-input {
  width: 200px;
}

.filter-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-left: auto;
}

.action-links {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-4);
}

.search-btn,
.reset-btn {
  padding: 0 var(--spacing-6);
}

.action-section {
  padding: var(--spacing-3) var(--spacing-6);
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.table-section {
  flex: 1;
  overflow: auto;
  background-color: white;
  min-height: 0;
}

.table-container {
  padding: var(--spacing-6);
}

.pagination-section {
  padding: var(--spacing-1);
  background-color: white;
  border-top: 1px solid var(--gray-200);
}

.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 表格样式
:deep(.dictionary-table .ant-table-thead > tr > th) {
  background-color: #eff7fd !important;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
  padding: 12px 16px;
}

:deep(.dictionary-table .ant-table-tbody > tr > td) {
  font-size: var(--text-sm);
  color: var(--gray-600);
  border-bottom: 1px solid var(--gray-200);
  padding: 12px 16px;
}

:deep(.dictionary-table .ant-table-tbody > tr:hover > td) {
  background-color: var(--gray-50);
}

:deep(.dictionary-table .ant-table) {
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

:deep(.dictionary-table .ant-table-container) {
  border: 0;
}

// 分页样式
:deep(.ant-pagination) {
  .ant-pagination-item {
    border-color: var(--gray-300);

    &:hover {
      border-color: var(--primary-color);
    }

    &.ant-pagination-item-active {
      background-color: var(--primary-color);
      border-color: var(--primary-color);

      // 选中页文字颜色调整为白色
      a {
        color: white !important;
      }
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    border-color: var(--gray-300);

    &:hover {
      border-color: var(--primary-color);
    }
  }
}

// 链接按钮样式
:deep(.ant-btn-link) {
  color: var(--primary-color);
  padding: 0;
  height: auto;

  &:hover {
    color: var(--primary-hover);
  }
}

// 按钮样式
:deep(.ant-btn) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn .anticon) {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
