package com.lms.base.scopecourseware.rtefile;

import lms.rte.Common.DataProviderType;
import lms.rte.DataManager.RTEDataManager;

public class UserCourseInfoTask implements ITask {
	// / <summary>
	// / 用户ID
	// / </summary>
	private String m_UserId;

	// / <summary>
	// / 课程ID
	// / </summary>
	private String m_CoureseId;

	// / <summary>
	// / 是否暂停状态
	// / </summary>
	private boolean m_SuspendStatus;

	public UserCourseInfoTask(String userId, String courseId,
                              boolean suspendStatus) {
		if (userId == null || userId.contains("")) {
			// throw new Exception("userid:获取树时用户ID为空");
		}
		if (courseId == null || courseId.contains("")) {
			// throw new Exception("courseId:获取树时课程ID为空");
		}

		m_UserId = userId;
		m_CoureseId = courseId;
		m_SuspendStatus = suspendStatus;
	}

	// / <summary>
	// / 执行任务
	// / </summary>
	// / <returns></returns>
	public boolean Excute() {
		boolean result = false;
		try {
			result = RTEDataManager.getInstance().UpdateUserCourseInfo(
					m_CoureseId, m_UserId, m_SuspendStatus,
					DataProviderType.Normal);
			if (result) {
				// Log.Debug(this + "任务已经执行");
			} else {
				// Log.Debug(this +
				// "任务已经执行但是未执行成功Userid="+m_UserId+",CourseID="+m_CoureseId);
			}
		} catch (Exception ex) {
			// Log.Error(ex.getMessage()+"\r\n"+ex.getStackTrace());
		}
		return result;
	}
}
