package com.sbtr.workflow.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.PageUtil.PageUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.sbtr.workflow.mapper.ActFormMapper;
import com.sbtr.workflow.model.ActForm;
import com.sbtr.workflow.service.ActFormService;

/**
 * 行为形式服务impl
 *
 * <AUTHOR>
 * @Version V5.4.21
 * @Email <EMAIL>
 * @Date 2023/03/20
 */
@Service("actFormServiceImpl")
public class ActFormServiceImpl extends WorkflowBaseServiceImpl<ActForm, String> implements ActFormService {
    @Autowired
    private ActFormMapper actFormMapper;

    /**
     * 获取页面设置
     *
     * @param pageParam  页面参数
     * @param filterSort 过滤排序
     * @return {@link PageSet}<{@link ActForm}>
     */
    @Override
    public PageSet<ActForm> getPageSet(PageParam pageParam, String filterSort) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<ActForm> list = actFormMapper.getPageSet(filterSort);
        PageInfo<ActForm> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    /**
     * 执行删除批处理
     *
     * @param uuid uuid
     * @return int
     */
    @Override
    public int executeDeleteBatch(String[] uuid) {
        return actFormMapper.executeDeleteBatch(uuid);
    }


}
