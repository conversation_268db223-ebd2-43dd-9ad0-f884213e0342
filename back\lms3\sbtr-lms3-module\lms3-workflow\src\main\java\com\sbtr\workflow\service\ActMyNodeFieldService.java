package com.sbtr.workflow.service;

import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import com.sbtr.workflow.model.ActMyNodeField;
import com.sbtr.workflow.vo.FlowNodeFieldVo;

import java.util.List;

/**
 * 流程每个节点所对字段是否可编辑以及是否可见
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-27 13:43:13
 */
public interface ActMyNodeFieldService extends WorkflowBaseService<ActMyNodeField, String> {

    /**
     * 分页
     *
     * @return
     * @Param pageParam 分页参数
     */
    PageSet<ActMyNodeField> getPageSet(PageParam pageParam);

    /**
     * 批量删除
     *
     * @return
     * @Param uuid 主键
     */
	int executeDeleteBatch(String[] uuids);

    /**
     * 批量插入节点属性数据
     *
     * @return
     */
    Integer insertList(List<ActMyNodeField> list1);

    /**
     * 据模型key 节点id 以及流程定义查询数据
     *
     * @return
     */
    List<FlowNodeFieldVo> selectByModelKeyAndId(String modelKey, String nodeId,String processDefinitionId);

    /**
     * 根据模型key以及流程定义 查询详细数据
     *
     * @return
     */
    List<ActMyNodeField> getListByActDeModelKeyAndProcdefId(String key, String procdefIds);


    /**
     * 根据模型key以及流程定义更新数据
     *
     * @return
     */
    Integer updateProcdefIdByModelKey(String key, String procdefId);


    /**
     * 根据模型key以及流程定义 吧流程定义Id置为空
     *
     * @return
     */
    Integer updateProcdefIdIsNull(String key, String id);

    /**
     * 据模型key以及流程定义 删除数据
     *
     * @return
     */
    Integer deleteByModelKeyAnProcdefId(String key, String id);

    Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id);
}
