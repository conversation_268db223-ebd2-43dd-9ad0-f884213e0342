package com.lms.system.feign.model;

import java.util.ArrayList;
import java.util.List;

import com.lms.common.model.BaseModel;
import lombok.Data;

@Data
public class MenuModel extends BaseModel {

	private String id;
	private String icon;
	private String hilightIcon;
	private String title;
	private Boolean isShowSub;
	private String url;
	private List<MenuModel> children = new ArrayList<MenuModel>();
	private Boolean ischecked;
	private String mid;
}
