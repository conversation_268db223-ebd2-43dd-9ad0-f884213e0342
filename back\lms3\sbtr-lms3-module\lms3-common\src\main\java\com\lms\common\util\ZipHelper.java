/**
 * SBTR LTD.
 */
package com.lms.common.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.util.Enumeration;
//import java.util.zip.ZipEntry;
//import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipFile;

/**
 * 文件压缩解压常用方法工具类
 *
 * <AUTHOR>
public class ZipHelper {

    /**
     * 压缩一层目录下的所有文件或压缩单个文件
     *
     * @param filePath   要压缩的文件路径 （单个文件需含文件名）
     * @param toFilePath 压缩后的文件路径 含文件名
     * @param isDelete   是否删除原路径 true为删除 false为不删除
     */
    public static void zip(String filePath, String toFilePath, boolean isDelete) {
        File file = new File(filePath);
        if (file.isDirectory()) {// 是否为文件目录，是为true
            zipFiles(filePath, toFilePath);
            if (isDelete) {
                String[] files = file.list();
                for (int i = 0; i < files.length; i++) {
                    File f = new File(filePath + "\\" + files[i]);
                    f.delete();
                }
                file.delete();
            }
        } else {
            zipFile(filePath, toFilePath);
            if (isDelete) {
                file.delete();
            }
        }

    }

    /**
     * 压缩某路径的单个文件。
     *
     * @param filePath 要压缩的文件的路径（含文件名 如：d:\\tt.txt）
     * @param zipname  压缩后的路径（含压缩后文件名 如：d:\\tt.zip）
     */
    public static void zipFile(String filePath, String zipname){
        try(FileOutputStream fos = new FileOutputStream(zipname);
            ZipOutputStream zipOut = new ZipOutputStream(fos);
            FileInputStream fis = new FileInputStream(filePath);){
            File f = new File(filePath);
            ZipEntry ze = new ZipEntry(f.getName());
            zipOut.putNextEntry(ze);
            int c = 0;
            while ((c = fis.read()) != -1) {
                zipOut.write(c);
            }
        } catch (FileNotFoundException ex) {
            ex.printStackTrace();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }

    /**
     * 压缩某路径下所有文件。
     *
     * @param filePath 要压缩的路径
     * @param zipname  压缩后的路径（含压缩后文件名 如：d:\\tt.zip）
     */
    public static void zipFiles(String filePath, String zipname) {
        FileOutputStream fos = null;
        ZipOutputStream zipOut = null;
        String[] files = (new File(filePath)).list();
        try {
            fos = new FileOutputStream(zipname);// 创建文件输出流（低级流）
            zipOut = new ZipOutputStream(fos);// 创建zip文件输出流
            int i = 0;

            for (i = 0; i < files.length; i++) {
                File f = new File(files[i]);
                FileInputStream fis = new FileInputStream(filePath + "\\"
                        + files[i]);
                ZipEntry ze = new ZipEntry(f.getName());
                zipOut.putNextEntry(ze);
                int c = 0;
                while ((c = fis.read()) != -1) {
                    zipOut.write(c);
                }
                fis.close();

            }
            zipOut.close();
        } catch (FileNotFoundException ex) {
            ex.printStackTrace();
        } catch (IOException ex) {
            ex.printStackTrace();
        }

    }

    /**
     * 解压文件至指定路径。
     *
     * @param zipfile 压缩文件路径
     * @param destDir 目标文件路径
     */
    public static void unZip(String zipfile, String destDir) throws IOException {
        destDir = destDir.endsWith("\\") ? destDir : destDir + "\\";
        byte b[] = new byte[1024];
        int length;
        ZipFile zipFile = null;
        try {
            zipFile = new ZipFile(new File(zipfile), "GBK");
            Enumeration enumeration = zipFile.getEntries();
            ZipEntry zipEntry = null;
            while (enumeration.hasMoreElements()) {
                zipEntry = (ZipEntry) enumeration.nextElement();
                if (zipEntry.isDirectory()) {
                    File loadFile = new File(destDir + zipEntry.getName());
                    loadFile.mkdir();
                    continue;
                }
                File loadFile = new File(destDir + zipEntry.getName());
                if (!loadFile.getParentFile().exists()) {
                    loadFile.getParentFile().mkdirs();
                }
                try (OutputStream outputStream = new FileOutputStream(loadFile);
                     InputStream inputStream = zipFile.getInputStream(zipEntry)) {
                    if (inputStream != null) {
                        while ((length = inputStream.read(b)) > 0) {
                            outputStream.write(b, 0, length);
                        }
                    }
                }
            }
            System.out.println(" 文件解压成功 ");
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
			if (zipFile != null){
				zipFile.close();
			}
		}
    }
}
