package com.lms.system.feign.fallback;

import com.lms.common.model.Result;
import com.lms.system.feign.api.AttachApi;
import com.lms.system.feign.model.Attach;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.util.List;


/**
 * misboot-mdata相关接口 模块降级处理
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
@Component
public class AttachApiFallbackFactory implements FallbackFactory<AttachApi> {

    private static final Logger log = LoggerFactory.getLogger(AttachApiFallbackFactory.class);


    @Override
    public AttachApi create(Throwable throwable) {
        log.error("AttachApi模块服务调用失败:{}", throwable.getMessage());
        return new AttachApi() {

            @Override
            public Result<Attach> get(String id) {
                return null;
            }

            @Override
            public Result saveOrUpdate(Attach attach) {
                return null;
            }

            @Override
            public Result<String> uploadFile(MultipartFile file) {
                return null;
            }

            @Override
            public Result<String> saveAttachByFilePath(File file) {
                return null;
            }

            @Override
            public Result<Boolean> checkFileIsExist(String objName) {
                return null;
            }

            @Override
            public void downLoadFile(HttpServletResponse response, String attachid) {

            }

            @Override
            public InputStream downLoadStream(String attachid) {
                return null;
            }

            @Override
            public Result<List<Attach>> getByIdList(List<String> attachIdList) {
                return null;
            }

            @Override
            public Result deleteFile(Attach attach) {
                return null;
            }

            @Override
            public Result delete(String id) {
                return null;
            }
        };
    }
}
