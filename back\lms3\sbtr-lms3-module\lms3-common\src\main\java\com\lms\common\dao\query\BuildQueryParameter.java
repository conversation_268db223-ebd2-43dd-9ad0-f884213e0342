package com.lms.common.dao.query;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.lms.common.util.StringHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR> <PERSON>
 * @version V1.0
 * @fileoverview: <br>Copyright @ 2017-2020 SBTR  LTD.
 * <br> This program is protected by copyright laws.
 * <br> Project Name:SBTR-TSS
 * <br> 模块名称:com.lms.common.dao.query.BuildQueryParameter
 * <br> 功能:
 * @create 2017-11-23 9:00
 */
public class BuildQueryParameter {
    public static List<Parameter> buildParameterList(JSONObject jsonObject) {
        List<Parameter> parameterList = new ArrayList<Parameter>();
        Map<String, Object> filterParamMap = getParametersStartingWith(jsonObject);
        for (Map.Entry<String, Object> entry : filterParamMap.entrySet()) {
            String filterName = entry.getKey();
            String value = "";
            if (entry.getValue() instanceof String[]) {
                for (String v : (String[]) entry.getValue()) {
                    if (!"".equals(v)) {
                        value = v;
                    }
                }
            } else {
                value = (String) entry.getValue();
            }
            // 如果value值为空,则忽略此filter.
            if (StringUtils.isNotBlank(value)) {
                Parameter parameter = Parameter.getParameter(filterName, value);
                parameterList.add(parameter);
            }
        }
        return parameterList;
    }


    private static Map getParametersStartingWith(JSONObject request) {
        Map map = request.toJavaObject(Map.class);
        Map params = new HashMap();
        map.forEach((key, value) -> {
            String keyStr = key.toString();
            String valStr = value.toString();
            String prefix = "PARAMETER_";
            if (ObjectUtil.isNotEmpty(valStr) && keyStr.startsWith(prefix)) {
                String unprefixed = keyStr.substring(prefix.length());
                params.put(unprefixed, valStr);
            }
        });

//        while (paramNames != null && paramNames.hasMoreElements()) {
//            String paramName = (String) paramNames.nextElement();
//            if ("".equals(prefix) || paramName.startsWith(prefix)) {
//                String unprefixed = paramName.substring(prefix.length());
//                if(request.getAttribute("paramSourceType") == null){
//                    String[] values = request.getParameterValues(paramName);
//                    if (values == null || values.length == 0) {
//                        // Do nothing, no values found at all.
//                    } else if (values.length > 1) {
//                        params.put(unprefixed, values);
//                    }else {
//                        params.put(unprefixed, values[0]);
//                    }
//                }else if(request.getAttribute("paramSourceType").equals("END")){
//                    String value = request.getAttribute(paramName).toString();
//                    if(value!=null && !value.equals("")){
//                        params.put(unprefixed, value);
//                    }
//                }
//
//            }
//        }
        return params;
    }
}
