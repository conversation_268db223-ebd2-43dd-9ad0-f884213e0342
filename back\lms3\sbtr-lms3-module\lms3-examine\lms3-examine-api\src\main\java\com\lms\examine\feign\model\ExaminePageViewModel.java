package com.lms.examine.feign.model;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.lms.common.model.BaseModel;
import com.lms.common.util.StringHelper;

public class ExaminePageViewModel extends BaseModel {
    private static final long serialVersionUID = -3616943288084994218L;
    private Examinepage examinepage;
    private String id;
    private List<Examinestudent> students = new ArrayList<Examinestudent>();

    private Map items = new HashMap();

    public ExaminePageViewModel(Examinepage examinepage, List<Examinestudent> students) {
        this.id = examinepage.getId();
        this.examinepage = examinepage;
        this.students = students;
        this.setItems(parse(students));
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    private Map parse(List<Examinestudent> students) {

        Map map = new HashMap();
        int examAmount = 0; //考试人数
        int passAmount = 0; //及格人数
        String passRate = ""; //及格率

        for (Examinestudent student : students) {
            int status = student.getStatus();
            String examinestatus = StringHelper.null2String(student.getExaminestatus());
            String result = StringHelper.null2String(student.getResult());
            examAmount++;
            if ((result.equals("4939f022ca4d4d30a0321f1e3b36236f") || result.equals("78681997db8e43979ed600f1ef341325") || result.equals("f6d1d2fbb4274362bf83991c5d07cca7")) && Integer.parseInt(examinestatus) >= 4) {
                passAmount++;
            }
        }
        NumberFormat nf = NumberFormat.getInstance();
        nf.setMaximumFractionDigits(2);
        if (examAmount == 0) {
            passRate = "0";
        } else {
            passRate = nf.format((float) passAmount / (float) examAmount * 100);
        }
        passRate = passRate + "%";
        map.put("examamount", examAmount);
        map.put("passamount", passAmount);
        map.put("passrate", passRate);
        return map;
    }

    public Map getItems() {
        return items;
    }

    public void setItems(Map items) {
        this.items = items;
    }

    public Examinepage getExaminepage() {
        return examinepage;
    }

    public void setExaminepage(Examinepage examinepage) {
        this.examinepage = examinepage;
    }

    public List<Examinestudent> getStudents() {
        return students;
    }

    public void setStudents(List<Examinestudent> students) {
        this.students = students;
    }
}
