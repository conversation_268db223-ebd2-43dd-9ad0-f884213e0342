package com.lms.examine.examinepage.service;

import com.alibaba.fastjson.JSONObject;
import com.lms.common.service.BaseService;
import com.lms.examine.feign.model.Examinepage;

import java.io.IOException;
import java.util.List;

public interface ExaminePageService extends BaseService<Examinepage> {

    JSONObject calcSubjectScoreRate(String subjectid);

    List<Examinepage> listExaminepage(String planId);

    String createWord(Examinepage examine) throws IOException;

    void calcSubjectKKD(String id);

    String findExamineIdByCourse(String courseid, String planid);

    String isRelease(String id);

    void adaptComponent(List<Examinepage> singletonList);

    Examinepage getEagerData(String id);

    String getScoreResult(String examineid, Double score);
}
