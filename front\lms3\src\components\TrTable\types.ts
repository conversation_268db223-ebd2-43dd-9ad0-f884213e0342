// 查询项配置接口
export interface QueryItem {
  key: string;
  label: string;
  component?: string;
  componentInstance?: any;
  props?: Record<string, any>;
  events?: Record<string, (value: any, ...args: any[]) => void>; // 新增：事件监听配置
}

// 按钮配置接口（合并CustomButton和OperationButton）
export interface ButtonConfig {
  key: string;
  label: string;
  props?: Record<string, any>;
}

// 状态映射项接口
export interface StatusMapItem {
  status: "success" | "error" | "warning" | "processing" | "default";
  text: string;
}

// 表格列配置接口
export interface TableColumn {
  title: string;
  dataIndex: string;
  key: string;
  width?: number | string;
  fixed?: "left" | "right";
  align?: "left" | "center" | "right";
  renderType?: "img" | "tag" | "time" | "html" | "status";
  tagColor?: string;
  statusMap?: Record<string, StatusMapItem>;
  render?: (text: any, record: TableRecord, index: number) => any;
  [key: string]: any;
}

// 表格记录接口
export interface TableRecord {
  [key: string]: any;
}

// 分页状态接口
export interface PaginationState {
  pageIndex: number;
  pageSize: number;
  total: number;
}

// 排序状态接口
export interface SortState {
  field?: string;
  order?: "ascend" | "descend" | null;
}

// 表格状态接口
export interface TableState {
  pagination: PaginationState;
  sort: SortState;
}

// 类型别名，保持向后兼容
export type CustomButton = ButtonConfig;
export type OperationButton = ButtonConfig;
