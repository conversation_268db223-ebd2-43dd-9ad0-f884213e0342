package com.lms.base.course.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lms.base.component.service.ComponentService;
import com.lms.base.course.service.CourseService;
import com.lms.base.feign.model.Component;
import com.lms.base.feign.model.Course;
import com.lms.base.courseware.service.CoursewareService;
import com.lms.base.feign.model.Courseware;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.dao.query.Parameter;
import com.lms.common.feign.dto.TreeNode;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.DateHelper;
import com.lms.common.util.StringHelper;
import com.lms.examine.feign.api.CoursewareexamineApi;
import com.lms.examine.feign.api.ExaminesubjectApi;
import com.lms.examine.feign.model.Coursewareexamine;
import com.lms.examine.feign.model.Examinesubject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> 课程管理控制层
 */


@RestController
@RequestMapping("/course")
@Api(value = "课程管理", tags = "课程管理")
public class CourseController extends BaseController<Course> {

    @Resource
    private CourseService courseService;

    @Resource
    private CoursewareService coursewareService;

    @Resource
    private CoursewareexamineApi coursewareexamineApi;

    @Resource
    private ExaminesubjectApi examinesubjectApi;

    @Value("${business.newCodePrefix.course}")
    private String newCodePrefix;
    @Value("${business.newCodePrefix.courseware}")
    private String newCodePrefixForCourseware;

    @Resource
    private ComponentService componentService;


    protected static final Logger logger = LoggerFactory.getLogger(CourseController.class);

    // 根据节点请求资源
    @ApiOperation(value = "根据型号节点Id获取所有课程列表", httpMethod = "POST")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "componentId", value = "型号节点Id", required = true, dataTypeClass = String.class)}
    )
    @PostMapping("/list/{componentId}")
    public Result<Page<Course>> courseList(@PathVariable("componentId") String componentId, @RequestBody JSONObject jsonObject) {

        PageInfo pageInfo = super.getPageInfo(jsonObject);
        if (!StringHelper.isEmpty(componentId) && !componentId.equals("listAll")) {
            Parameter componentpar = Parameter.getParameter("S_LIKE_equipmentid", componentId);
            pageInfo.getParameters().add(componentpar);
        }
        Page<Course> page = courseService.listByCondition(pageInfo);
        componentService.adaptComponent(page.getRecords());
        return Result.OK(page);
    }


    @PostMapping("/list")
    @ApiOperation(value = "查询课程分页列表", httpMethod = "POST")
    public Result<Page<Course>> getCourseList(@RequestBody JSONObject jsonObject) {
        PageInfo pageInfo = super.getPageInfo(jsonObject);
        Page<Course> page = courseService.listByCondition(pageInfo);
        return Result.OK(page);
    }

    @GetMapping("/countTotal")
    @ApiOperation(value = "统计课程数", httpMethod = "GET")
    public Result<Long> countTotal() {
        Long c = courseService.getTotalCourse();
        return Result.OK(c);
    }

    @PostMapping("/add")
    @ApiOperation(value = "保存课程信息", httpMethod = "POST")
    public Result<Course> saveCourse(@RequestBody Course course) {

        Boolean existCourse = courseService.existCourse(course);
        if (existCourse) {
            return Result.error(commonSystemApi.translateContent("已存在相同版本号的课程名称，请勿重复录入！"));
        }
        Boolean existCourseNo = courseService.existCourseNo(course);
        if (existCourseNo) {
            return Result.error(commonSystemApi.translateContent("系统已存在相同编号的课程，请勿重复录入！"));
        }
        if (StringHelper.isEmpty(course.getNumber())) course.setNumber(commonSystemApi.getNewCode(newCodePrefix));
        course.setEditDate(DateHelper.getCurrentDate());
        course.setCreatorid(ContextUtil.getCurrentUser().getPersonid());
        course.setStatus(1);
        //course.setCourseType("e18ed14d0c4e44feb15f367489908e54");//课程类型默认学习课程
        if (StringUtils.isNotEmpty(course.getComponentId())){
            Component component = componentService.getById(course.getComponentId());
            if (component != null){
                course.setEquipmentid(component.getFullpath());
            }
        }
        this.courseService.saveOrUpdate(course);
        return Result.OK(course);
    }

    @PostMapping(value = {"/edit"})
    @ApiOperation(value = "编辑课程信息", httpMethod = "POST")
    public Result<Course> updateCourse(@RequestBody Course course) {

        Boolean existCourse = courseService.existCourse(course);
        if (existCourse) {
            return Result.error(commonSystemApi.translateContent("已存在相同版本号的课程名称，请勿重复录入！"), null);
        }
        Boolean existCourseNo = courseService.existCourseNo(course);
        if (existCourseNo) {
            return Result.error(commonSystemApi.translateContent("系统已存在相同编号的课程，请勿重复录入！"), null);
        }
        if (StringHelper.isEmpty(course.getNumber())) course.setNumber(commonSystemApi.getNewCode(newCodePrefix));
        String copyId = StringHelper.null2String(course.getCopyid());
        if (StringHelper.isEmpty(course.getEditDate())) {
            course.setEditDate(DateHelper.getCurrentDate());
        }
        if (StringHelper.isEmpty(course.getCreatorid())) {
            course.setCreatorid(ContextUtil.getCurrentUser().getPersonid());
        }
        if (StringUtils.isNotEmpty(course.getComponentId())){
            Component component = componentService.getById(course.getComponentId());
            if (component != null){
                course.setEquipmentid(component.getFullpath());
            }
        }
        courseService.saveOrUpdate(course);
        if (!copyId.isEmpty()) {//复制课程，级联复制课件、课件试题信息
            String newCourseId = course.getId();
            List<Courseware> coursewares = coursewareService.getCoursewareList(copyId);
            for (Courseware courseware : coursewares) {
                Courseware newcw = new Courseware();
                BeanUtils.copyProperties(courseware, newcw);
                newcw.setId(null);
                newcw.setComponentid(newCourseId);
                if (StringUtils.isNotEmpty(course.getComponentId())){
                    Component component = componentService.getById(course.getComponentId());
                    if (component != null){
                        course.setEquipmentid(component.getFullpath());
                    }
                }
                newcw.setNumber(commonSystemApi.getNewCode(newCodePrefixForCourseware));
                coursewareService.saveOrUpdate(newcw);
                List<Coursewareexamine> coursewareexamines = coursewareexamineApi.getByCourseware(courseware.getId()).getResult();
                for (Coursewareexamine coursewareexamine : coursewareexamines) {
                    Coursewareexamine newexamine = new Coursewareexamine();
                    BeanUtils.copyProperties(coursewareexamine, newexamine);
                    newexamine.setId(null);
                    newexamine.setCoursewareid(newcw.getId());
                    coursewareexamineApi.save(newexamine);
                    List<Examinesubject> examinesubjects = examinesubjectApi.getSubjectByExamineId(coursewareexamine.getId()).getResult();
                    for (Examinesubject examinesubject : examinesubjects) {
                        Examinesubject newexaminesubject = new Examinesubject();
                        BeanUtils.copyProperties(examinesubject, newexaminesubject);
                        newexaminesubject.setId(null);
                        newexaminesubject.setExamineid(newexamine.getId());
                        examinesubjectApi.save(newexaminesubject);
                    }
                }
            }
        }
        return Result.OK(course);

    }

    @ApiOperation(value = "删除课程信息", httpMethod = "DELETE")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "ids", value = "需要删除的课程id以,分隔", required = true, dataTypeClass = String.class)}
    )
    @LMSLog(desc = "删除课程信息", otype = LogType.Delete, order = 1, method = "setCourseLog")
    @DeleteMapping("/delete")
    public Result<Void> batchDeleteCourse(@RequestParam("ids") String ids) {
        String[] idList = ids.split(",");
        for (String id : idList) {
            this.courseService.removeById(id);
        }
        return Result.OK(null);
    }

    @ApiOperation(value = "查询课程信息", httpMethod = "GET")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "courseId", value = "课程id", required = true, dataTypeClass = String.class)}
    )
    @GetMapping("/getById")
    public Result getCourseware(@RequestParam("courseId") String courseId) {
        Course course = courseService.getById(courseId);
        componentService.adaptComponent(Collections.singletonList(course));
        return Result.OK(course);
    }

    @ApiOperation(value = "获取课程型号树", httpMethod = "POST")
    @PostMapping(value = {"/getCourseStructureTree"})
    public Result getCourseStructureTree(@RequestBody JSONObject jsonObject) {
        PageInfo pageInfo = super.getPageInfo(jsonObject);
        List<TreeNode> resArray = courseService.getCourseStructureTree(pageInfo);
        return Result.OK(resArray);
    }

    /*
     * 发布课程
     */
    @ApiOperation(value = "发布课程", httpMethod = "GET")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "ids", value = "课程Id以,分隔", required = true, dataTypeClass = String.class)}
    )
    @LMSLog(desc = "发布课程", otype = LogType.BatchUpdate, order = 2, method = "setCourseLog")
    @GetMapping(value = "/batchrelease")
    public Result<List<String>> batchReleaseCourse(@RequestParam("ids") String ids) {
        String[] idarray = ids.split(",");
        List<String> idList = new ArrayList<String>();
        for (int i = 0; i < idarray.length; i++) {
            String id = idarray[i];
            idList.add(id);
        }
        String returnMsg = "";
        String errorCourseMsg = "";
        String errorCourseIds = "";

        String errorCoursewareMsg = "";
        String errorCoursewareIds = "";

        String errorCoursewareexamineMsg = "";
        String errorCoursewareexamineIds = "";

        String notEnoughexamineMsg = "";
        String notEnougheexamineIds = "";
        for (int i = 0; i < idList.size(); i++) {
            String courseid = idList.get(i);
            Course course = courseService.getById(courseid);
            String name = course.getCourseName();
            String ct = course.getCourseType();
            List<Courseware> coursewares = coursewareService.getCoursewareList(courseid);
            if (coursewares.size() == 0) {// 校验课程是否已添加课件
                if (errorCourseIds.isEmpty()) {
                    errorCourseMsg = name;
                    errorCourseIds = courseid;
                } else {
                    errorCourseMsg = errorCourseMsg + "," + name;
                    errorCourseIds = errorCourseIds + "," + courseid;
                }
            }
        }
        int index = 1;
        if (!StringHelper.isEmpty(errorCourseIds)) {
            errorCourseMsg = index++ + "、" + commonSystemApi.translateContent("课程[?]未添加课件，不符合发布条件。", "", errorCourseMsg) + "\n";
        }
        if (!StringHelper.isEmpty(errorCoursewareIds)) {
            errorCoursewareMsg = index++ + "、" + commonSystemApi.translateContent("课件[?]未添加试卷，不符合发布条件。", "", errorCoursewareMsg) + "\n";
        }
        if (!StringHelper.isEmpty(errorCoursewareexamineIds)) {
            errorCoursewareexamineMsg = index++ + "、" + commonSystemApi.translateContent("课件试卷[?]未添加试题，不符合发布条件。", "", errorCoursewareexamineMsg) + "\n";
        }
        if (!StringHelper.isEmpty(notEnougheexamineIds)) {
            notEnoughexamineMsg = index++ + "、" + commonSystemApi.translateContent("课件试卷[?]已添加试题数小于答题通过数，不符合发布条件。", "", notEnoughexamineMsg) + "\n";
        }
        returnMsg = errorCourseMsg + errorCoursewareMsg + errorCoursewareexamineMsg + notEnoughexamineMsg;
        if (!StringHelper.isEmpty(returnMsg)) {
            return Result.error(returnMsg);
        }
        this.courseService.batchReleaseCourse(idList);
        return Result.OK(idList);
    }

    /*
     * 结束课程
     * */
    @ApiOperation(value = "结束考试", httpMethod = "GET")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "ids", value = "课程Id以,分隔", required = true, dataTypeClass = String.class)}
    )
    @LMSLog(desc = "结束考试", otype = LogType.BatchUpdate, order = 2, method = "setCourseLog")
    @GetMapping("/finishCourse")
    public void finishCourse(@RequestParam("ids") String ids) {
        String[] idarray = ids.split(",");
        for (String id : idarray) {
            Course course = this.courseService.getById(id);
            course.setStatus(10);
            courseService.saveOrUpdate(course);
        }

    }

    public String setCourseLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.Save)) { // 新增数据日志
            Course p = (Course) (args[0]);
            objname = StringHelper.null2String(p.getCourseName());
        } else if (lmslog.otype().equals(LogType.Update)) { // 编辑数据日志
            Course p = (Course) (args[0]);
            objname = StringHelper.null2String(p.getCourseName());
        } else if (lmslog.otype().equals(LogType.Delete) || lmslog.otype().equals(LogType.BatchUpdate)) { // 删除数据日志
            String ids = (String) (args[0]);
            String[] idarray = ids.split(",");
            for (String id : idarray) {
                Course p = courseService.getById(id);
                objname = objname.isEmpty() ? StringHelper.null2String(p.getCourseName()) : objname + "," + StringHelper.null2String(p.getCourseName());
            }
        }
        return objname;
    }


    @ApiOperation(value = "根据ID查询课程", httpMethod = "GET")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "courseId", value = "课程Id", required = true, dataTypeClass = String.class)}
    )
    @GetMapping("/getByCourseId")
    public Result<Course> getByCourseId(String courseId) {
        Course course = courseService.getById(courseId);
        componentService.adaptComponent(Collections.singletonList(course));
        return Result.OK(course);
    }

    @ApiOperation(value = "根据ID列表查询课程", httpMethod = "POST")
    @PostMapping("/getByCourseIdList")
    public Result<List<Course>> getByCourseIdList(@RequestBody List<String> courseIdList) {
        List<Course> courseList = courseService.getByCourseIdList(courseIdList);
        componentService.adaptComponent(courseList);
        return Result.OK(courseList);
    }

    @ApiOperation(value = "远程调用-根据型号id列表查询课程", httpMethod = "POST")
    @PostMapping("/getCourseListByComponentIdList")
    public Result<List<Course>> getCourseListByComponentIdList(@RequestBody List<String> componentIdList){
        List<Course> courseList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(componentIdList)){
            courseList = courseService.getCourseListByComponentIdList(componentIdList);
        }
        return Result.OK(courseList);
    }

}
