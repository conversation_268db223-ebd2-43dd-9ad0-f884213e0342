<!-- eslint-disable unicorn/no-array-reduce -->
<script setup lang="ts">
import type { Organization } from "../data.ts";

import { computed, ref, watch } from "vue";

interface Props {
  organizations: Organization[];
  selectedId?: string;
}

interface Emits {
  (e: "select", organization: null | Organization): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const searchValue = ref("");
const selectedKeys = ref<string[]>([]);

// 监听外部选中状态
watch(
  () => props.selectedId,
  newId => {
    selectedKeys.value = newId ? [newId] : [];
  },
  { immediate: true }
);

const filteredTreeData = computed(() => {
  if (!props.organizations || !Array.isArray(props.organizations)) {
    return [];
  }

  if (!searchValue.value) {
    return props.organizations;
  }

  const filterTree = (nodes: Organization[]): Organization[] => {
    if (!nodes || !Array.isArray(nodes)) {
      return [];
    }

    // eslint-disable-next-line unicorn/no-array-reduce
    return nodes.reduce((acc: Organization[], node) => {
      if (!node || typeof node !== "object") {
        return acc;
      }

      const matchesSearch =
        node.name &&
        typeof node.name === "string" &&
        node.name.toLowerCase().includes(searchValue.value.toLowerCase());
      const filteredChildren =
        node.children && Array.isArray(node.children)
          ? filterTree(node.children)
          : [];

      if (matchesSearch || filteredChildren.length > 0) {
        acc.push({
          ...node,
          children:
            filteredChildren.length > 0 ? filteredChildren : node.children
        });
      }

      return acc;
    }, []);
  };

  return filterTree(props.organizations);
});

const onSearch = (value: string) => {
  searchValue.value = value;
};

const onSelect = (selectedKeys: string[], { node }: any) => {
  if (selectedKeys.length > 0) {
    const selectedNode = node.dataRef || node;
    if (selectedNode && typeof selectedNode === "object") {
      emit("select", selectedNode);
    } else {
      emit("select", null);
    }
  } else {
    emit("select", null);
  }
};
</script>

<template>
  <div class="organization-tree">
    <div class="tree-header">
      <a-input-search
        v-model:value="searchValue"
        placeholder="请输入单位名称"
        class="search-input"
        @search="onSearch"
      />
    </div>

    <div class="tree-content">
      <!--      <div class="tree-title">用户单位</div>-->
      <a-tree
        v-model:selected-keys="selectedKeys"
        :tree-data="filteredTreeData"
        :field-names="{ children: 'children', title: 'name', key: 'id' }"
        class="organization-tree-content"
        @select="onSelect"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.organization-tree {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-right: 1px solid var(--gray-200);
}

.tree-header {
  padding: var(--spacing-4);
  background-color: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
}

.search-input {
  width: 100%;
}

.tree-content {
  flex: 1;
  padding: var(--spacing-4);
  background-color: white;
  overflow: auto;
}

.tree-title {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-600);
  margin-bottom: var(--spacing-3);
}

.organization-tree-content {
  font-size: var(--text-sm);

  :deep(.ant-tree-node-content-wrapper) {
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius);
    transition: all var(--transition-normal);

    &:hover {
      background-color: rgba(24, 144, 255, 0.1);
      transform: scale(1.02);
    }

    &.ant-tree-node-selected {
      background-color: rgba(24, 144, 255, 0.15);
      color: var(--primary-color);
      font-weight: var(--font-medium);
    }
  }

  :deep(.ant-tree-title) {
    font-size: var(--text-sm);
  }
}
</style>
