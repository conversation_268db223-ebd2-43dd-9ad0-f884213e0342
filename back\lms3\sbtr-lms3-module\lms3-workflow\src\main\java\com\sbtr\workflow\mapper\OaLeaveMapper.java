package com.sbtr.workflow.mapper;

import com.sbtr.workflow.bean.WorkflowMapper;
import com.sbtr.workflow.model.OaLeave;
import com.sbtr.workflow.model.OaLeaveCommon;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * oa离开映射器
 *
 * <AUTHOR>
 * @Version V5.4.21
 * @Email <EMAIL>
 * @Date 2023/03/09
 */
public interface OaLeaveMapper extends WorkflowMapper<OaLeave> {

    List<OaLeaveCommon> getToDoTasks(@Param("filterSort") String filterSort, @Param("userNmeId") String userNmeId);

    List<OaLeaveCommon> getTasksInProgress(@Param("filterSort") String filterSort,@Param("userNmeId") String userNmeId);

    List<OaLeaveCommon> getHistoryTasks(@Param("filterSort") String filterSort,@Param("userNmeId") String userNmeId);

    int executeDeleteBatch(Object[] var1);

    Integer updateStateByUuid(@Param("businessKey")String businessKey,@Param("state") String state);


}
