package com.sbtr.workflow.model;

import cn.ewsd.common.model.MCoreBase;

import javax.persistence.Table;


@Table(name = "act_my_variable")
public class MyVariable extends MCoreBase {

    private String processKey;
    private String variableDesc;
    private String variableName;
    private String variableValue;

    public MyVariable() {
        super();
    }

    public String getProcessKey() {
        return processKey;
    }

    public void setProcessKey(String processKey) {
        this.processKey = processKey;
    }

    public String getVariableName() {
        return variableName;
    }

    public void setVariableName(String variableName) {
        this.variableName = variableName;
    }

    public String getVariableValue() {
        return variableValue;
    }

    public void setVariableValue(String variableValue) {
        this.variableValue = variableValue;
    }

    public String getVariableDesc() {
        return variableDesc;
    }

    public void setVariableDesc(String variableDesc) {
        this.variableDesc = variableDesc;
    }
}
