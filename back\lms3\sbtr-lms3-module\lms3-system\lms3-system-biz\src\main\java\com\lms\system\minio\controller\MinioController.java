package com.lms.system.minio.controller;

import com.lms.common.model.Result;
import com.lms.system.minio.service.MinioService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@RequestMapping("/testMinio")
@Api(value = "minio文件上传管理", tags = "minio文件上传管理")
public class MinioController {

    @Resource
    private MinioService minioService;

    // 上传
    @PostMapping("/upload")
    @ApiOperation(value = "上传文件", httpMethod = "POST")
    public Result upload(@RequestParam("file") MultipartFile multipartFile) throws Exception {
        String url =  this.minioService.upload(multipartFile);
        return Result.OK(url);
    }

    // 下载文件
    @GetMapping("/download")
    @ApiOperation(value = "下载文件", httpMethod = "GET")
    public void download(@RequestParam("fileName")String fileName, HttpServletResponse response) throws Exception {
        this.minioService.download(fileName,response);
    }

    // 列出所有存储桶名称
    @PostMapping("/list")
    @ApiOperation(value = "列出所有存储桶名称", httpMethod = "POST")
    public Result list() throws Exception {
        return Result.OK(this.minioService.listBucketNames());
    }

    // 创建存储桶
    @PostMapping("/createBucket")
    @ApiOperation(value = "创建存储桶", httpMethod = "POST")
    public Result createBucket(String bucketName) throws Exception {
        return Result.OK(this.minioService.makeBucket(bucketName));
    }

    // 删除存储桶
    @PostMapping("/deleteBucket")
    @ApiOperation(value = "删除存储桶", httpMethod = "POST")
    public Result deleteBucket(String bucketName) throws Exception {
        return Result.OK(this.minioService.removeBucket(bucketName));
    }

    // 列出存储桶中的所有对象名称
    @PostMapping("/listObjectNames")
    @ApiOperation(value = "列出存储桶中的所有对象名称", httpMethod = "POST")
    public Result listObjectNames(String bucketName) throws Exception {
        return Result.OK(this.minioService.listObjectNames(bucketName));
    }

    // 删除一个对象
    @PostMapping("/removeObject")
    @ApiOperation(value = "删除一个对象", httpMethod = "POST")
    public Result removeObject(String objectName){
        return Result.OK(this.minioService.removeObject("",objectName));
    }

    // 文件访问路径
    @PostMapping("/getPreviewUrl")
    @ApiOperation(value = "获取文件访问路径", httpMethod = "GET")
    public Result getPreviewUrl(String objectName){
        return Result.OK(this.minioService.getPreviewUrl("",objectName));
    }
}
