package com.sbtr;

import com.flowable.conf.AppDispatcherServletConfiguration;
import com.flowable.conf.ApplicationConfiguration;
import com.lms.common.filter.JwtFilter;
import com.lms.common.util.ConstParamUtil;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import tk.mybatis.spring.annotation.MapperScan;


/**
 *  流程模块启动类
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
@Slf4j
@Import({
        ApplicationConfiguration.class,
        AppDispatcherServletConfiguration.class,
        RedisUtil.class,
        ContextUtil.class,
        JwtFilter.class
})
@SpringBootApplication(exclude = {SecurityAutoConfiguration.class, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.class})
@ComponentScan(basePackages = {"cn.ewsd.**.*", "com.flowable.**.*", "com.sbtr.**.*","com.lms.common.redis","com.lms.base.feign"})
@EnableFeignClients(basePackages = {ConstParamUtil.commonFeignPackage})
@MapperScan({"com.sbtr.*.mapper"})
public class LmsWorkflowApplication {

    public static void main(String[] args) {
//        System.setProperty("nacos.logging.default.config.enabled", "false");
        SpringApplication.run(LmsWorkflowApplication.class, args);
    }

}
