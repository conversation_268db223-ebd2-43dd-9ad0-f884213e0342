package com.sbtr.workflow.service.impl;

import cn.ewsd.common.common.Constants;
import cn.ewsd.common.model.MCoreBase;
import com.sbtr.workflow.mapper.BaseMapper;
import com.sbtr.workflow.service.BaseService;
import com.sbtr.workflow.service.BusinessSystemDataService;
import com.sbtr.workflow.utils.BaseUtils;
import cn.ewsd.common.utils.JsonUtils;
import cn.ewsd.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @MethodName
 * @Description
 * @Param null
 * @Return
 * <AUTHOR>
 * @Date 2020-10-26 9:40
 */
public class BaseServiceImpl implements BaseService {

    @Resource
    private BaseMapper baseMapper;
    @Resource
    public BusinessSystemDataService businessSystemDataService;
    public String getCurrentUserNameId() {
        return businessSystemDataService.getUserNameId();
    }

    public String getCurrentUserName() {
        return businessSystemDataService.getUserName();
    }

    public Object success(String message) {
        return JsonUtils.messageJson(200, Constants.OPERATE_TIPS, message);
    }

    public Object failure(String message) {
        return JsonUtils.messageJson(300,  Constants.OPERATE_TIPS, message);
    }


    public <T> T getSaveData(T entity) {
        if (StringUtils.isNullOrEmpty(((MCoreBase)entity).getUuid())) {
            ((MCoreBase)entity).setUuid(BaseUtils.UUIDGenerator());
        }

        ((MCoreBase)entity).setCreatorId(this.getCurrentUserNameId());
        ((MCoreBase)entity).setCreator(this.getCurrentUserName());
        ((MCoreBase)entity).setCreateTime(new Date());
        ((MCoreBase)entity).setCreatorOrgId(Integer.parseInt(businessSystemDataService.getOrgId()));
        return entity;
    }

    public <T> T getModifyData(T entity) {
        ((MCoreBase)entity).setModifierId(this.getCurrentUserNameId());
        ((MCoreBase)entity).setModifier(this.getCurrentUserName());
        ((MCoreBase)entity).setModifyTime(new Date());
        return entity;
    }

    @Override
    public String selectStr(String sql) {
        return baseMapper.selectStr(sql);
    }

    @Override
    public HashMap<String, Object> selectByKey(String sql) {
        return baseMapper.selectByKey(sql);
    }

    @Override
    public List<Map> selectBysql(String sql) {
        return baseMapper.selectBysql(sql);
    }

}
