package com.sbtr.workflow.utils.PageUtil;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;

/**
 * 封装获取分页数据装换
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-05-18 13:40:36
 */
public class PageUtils {

    public PageUtils() {
    }

    public static <T> PageSet<T> getPageSet(PageInfo<T> pageInfo) {
        PageSet var1 = new PageSet(
                pageInfo.getList(),
                pageInfo.getPageSize(),
                pageInfo.getPageNum(),
                pageInfo.getPages(),
                (int)pageInfo.getTotal());
        BeanUtils.copyProperties(pageInfo, var1);
        return var1;
    }

}