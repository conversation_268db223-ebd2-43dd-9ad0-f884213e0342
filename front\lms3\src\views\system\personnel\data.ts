import { systemDataManager } from "@/utils/SystemDataManager.ts";
import {
  genderOptions,
  positionOptions,
  specialtyOptions
} from "@/data/system";
import { translateText } from "@/utils/translation";
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined
} from "@ant-design/icons-vue";
// 查询条件配置
export const queryItems = [
  {
    key: "PARAMETER_S_Like_name",
    label: translateText("人员名称"),
    component: "a-input",
    props: {
      placeholder: translateText("请输入人员名称"),
      allowClear: true
    }
  },
  {
    key: "PARAMETER_S_Like_cardNum",
    label: translateText("证件号"),
    component: "a-input",
    props: {
      placeholder: translateText("请输入证件号"),
      allowClear: true
    }
  }
  // {
  //   key: "PARAMETER_S_EQ_departmentid",
  //   label: translateText("工作单位"),
  //   componentInstance: DepartmetTree, // 使用独立组件文件（推荐）
  //   props: {
  //     placeholder: "请选择工作单位"
  //   }
  // }
];

// 列表字段配置
export const columns = [
  {
    label: "姓名",
    prop: "name",
    width: 120
  },
  {
    label: "证件号",
    prop: "cardNum",
    width: 80,
    align: "center",
    sortable: true
  },
  {
    label: "性别",
    prop: "sex",
    width: 80,
    align: "center",
    sortable: true,
    render: (current: string) => {
      return systemDataManager.translateDict(genderOptions, current);
    }
  },
  {
    label: "出生日期",
    prop: "birthday",
    width: 120,
    align: "center"
  },
  {
    label: "工作单位",
    prop: "departmentname",
    sortable: true,
    width: 120
  },
  {
    label: "所在岗位",
    prop: "duty",
    width: 120,
    render: (current: string) => {
      return systemDataManager.translateDict(positionOptions, current);
    }
  },
  {
    label: "专业",
    prop: "specialityid",
    width: 100,
    align: "center",
    render: (current: string) => {
      return systemDataManager.translateDict(specialtyOptions, current);
    }
  }
];

// 页面按钮配置
export const btnConfig = [
  {
    key: "add",
    label: "新增",
    icon: PlusOutlined,
    type: "batch",
    props: { type: "primary" }
  },
  {
    key: "batchDelete",
    label: "批量删除",
    icon: DeleteOutlined,
    type: "batch",
    props: { danger: true }
  },
  {
    key: "edit",
    label: "编辑",
    icon: EditOutlined,
    type: "row",
    props: { type: "primary" }
  },
  {
    key: "delete",
    label: "删除",
    icon: DeleteOutlined,
    type: "row",
    props: { danger: true }
  }
];
