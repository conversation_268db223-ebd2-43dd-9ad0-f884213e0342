<template>
  <TrDialog
    v-model:open="isOpen"
    destroyOnClose
    :width="1000"
    :title="translateText(`新增题目`)"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :label-col="{ span: 4 }"
    >
      <a-row>
        <a-col :span="24">
          <a-form-item
            :label="translateText('题目类型')"
            :label-col="{ span: 2 }"
            name="type"
          >
            <a-radio-group
              v-model:value="formData.type"
              name="type"
              :options="subjecttypeOptions"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item
            :label="translateText('标题')"
            :label-col="{ span: 2 }"
            name="title"
          >
            <a-textarea v-model:value="formData.title" :rows="2" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row
        v-if="
          formData.type === '9e47efc0ce894454856a80171e1e6efe' ||
          formData.type === '3ef580ec56ae436eb79b91b25d1a078e'
        "
      >
        <a-col :span="24">
          <a-form-item
            :label="translateText('选项内容')"
            :label-col="{ span: 2 }"
          >
            <a-card>
              <a-row>
                <a-col :span="11">
                  <a-form-item label="A" name="itemA">
                    <a-textarea v-model:value="formData.itemA" :rows="2" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="B"
                    name="itemB"
                    :label-col="{ offset: 2 }"
                  >
                    <a-textarea v-model:value="formData.itemB" :rows="2" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="11">
                  <a-form-item label="C" name="itemC">
                    <a-textarea v-model:value="formData.itemC" :rows="2" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="D"
                    name="itemD"
                    :label-col="{ offset: 2 }"
                  >
                    <a-textarea v-model:value="formData.itemD" :rows="2" />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-card>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item
            :label="translateText('参考答案')"
            :label-col="{ span: 2 }"
            name="correctresponse"
          >
            <a-textarea
              v-if="
                formData.type === 'db2b6c993b4c4edfa146385764cd51cc' ||
                formData.type === 'b96c5cf475e04cd6a44726e186791408'
              "
              v-model:value="formData.correctresponse"
              :rows="4"
            />
            <a-checkbox-group
              v-else-if="formData.type === '3ef580ec56ae436eb79b91b25d1a078e'"
              v-model:value="formData.correctresponse"
              :options="itemOptions"
              name="correctresponse"
            />
            <a-radio-group
              v-else-if="formData.type === '23e162b9f27b4ebc9a5c93db09913693'"
              v-model:value="formData.correctresponse"
              name="correctresponse"
            >
              <a-radio value="1">{{ translateText("正确") }}</a-radio>
              <a-radio value="0">{{ translateText("错误") }}</a-radio>
            </a-radio-group>
            <a-radio-group
              v-else
              v-model:value="formData.correctresponse"
              name="correctresponse"
              :options="itemOptions"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-item :label="translateText('使用范围')" name="onlyexam">
            <a-radio-group v-model:value="formData.onlyexam">
              <a-radio
                v-for="item in onlyexamlOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ translateText(item.label) }}
              </a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="translateText('难易度')">
            <a-select
              v-model:value="formData.levelid"
              allowClear
              style="width: 90%"
              :options="levelOptions"
              :placeholder="translateText('请选择难易度')"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-item :label="translateText('责任人')">
            <a-input-search
              v-model:value="formData.principal"
              :placeholder="translateText('请选择责任人')"
              style="width: 90%"
              @search="showPersonSelector"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="translateText('责任单位')">
            <departmet-tree
              :tree-value="formData.dutyunit"
              :field-names="{
                children: 'children',
                label: 'name',
                value: 'name'
              }"
              style="width: 90%"
              @change="
                data => {
                  formData.dutyunit = data.value;
                }
              "
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <person-selector
      :visible="selectorVisible"
      :multiple="false"
      @close="selectorVisible = $event"
      @confirm="handlePersonConfirm"
    />
  </TrDialog>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import TrDialog from "@/components/TrDialog/index.vue";
import { translateText } from "@/utils/translation";
import DepartmetTree from "@/components/Selector/departmetTree.vue";
import {
  onlyexamlOptions,
  subjecttypeOptions,
  levelOptions,
  itemOptions,
  rules
} from "@/views/modelPlatform/subject/data.js";
import type { FormInstance } from "ant-design-vue";
import { saveSubject } from "@/api/subject.ts";
import { message } from "ant-design-vue";
import PersonSelector from "@/components/Selector/personSelector.vue";
export interface Props {
  visible: boolean;
  object?: object | null;
  isEdit?: boolean;
}
export interface Emits {
  (e: "close", visible: boolean): void;
  (e: "submit", data: object): void;
}
const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const isOpen = ref(props.visible);
const formRef = ref<FormInstance>();
const selectorVisible = ref(false);
const loading = ref(false);
const formData = ref<any>({});

// 监听弹窗显示状态，初始化表单数据
watch(
  () => props.visible,
  visible => {
    isOpen.value = visible;
    if (visible) {
      if (props.object) {
        formData.value = props.object;
      } else {
        formData.value = { type: "9e47efc0ce894454856a80171e1e6efe" };
      }
    }
  }
);
const handlePersonConfirm = data => {
  const firstItem = data[0]; // 提取首项
  if (!firstItem) return; // 防御空数据

  formData.value.principal = firstItem.name;
  selectorVisible.value = false;
};
const showPersonSelector = () => {
  selectorVisible.value = true;
};
// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;
    let para = Object.assign({}, formData.value);
    para.correctresponse = para.correctresponse.toString();
    let res = await saveSubject(para);
    if (res.success) {
      message.success(res.message);
      emit("submit");
      handleCancel();
    } else {
      message.error(res.message);
    }
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    loading.value = false;
  }
};

const handleCancel = () => {
  emit("close", false);
};
</script>
