package com.lms.examine.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * UExaminesubject entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)

@Data
@TableName("u_examinesubject")
@ApiModel("试卷题目配置对象")
public class Examinesubject extends BaseModel {

	// Fields
	@TableId(type= IdType.ASSIGN_ID)
	@ApiModelProperty("试卷题目唯一标识")
	private String id;

	@ApiModelProperty("试卷id")
	private String examineid;

	@ApiModelProperty("题目id")
	private String subjectid;

	@ApiModelProperty("题目标题")
	private String title;

	@ApiModelProperty("题目内容")
	private String content;

	@ApiModelProperty("题目类型")
	private String type;

	@ApiModelProperty("参考答案")
	private String correctresponse;

	@ApiModelProperty("序号")
	private Integer seqno;

	@ApiModelProperty("题目难度")
	private String levelid;

	@ApiModelProperty("专业")
	private String specialid;

	@ApiModelProperty("型号")
	private String equipmentid;

	@ApiModelProperty("题目标题(全)")
	@TableField(exist = false)
	private String fulltitle;

	@ApiModelProperty("题目附件")
	private String attach;

	@ApiModelProperty("题目附件预览地址")
	@TableField(exist = false)
	private String imgUrl;
}
