package com.lms.base.operationanalysis.controller;

import com.lms.base.operationanalysis.model.OperationAnalysis;
import com.lms.base.operationanalysis.service.OperationAnalysisService;
import com.lms.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/operationanalysis")
@Api(value = "操作结果分析管理", tags = "操作结果分析管理")
public class OperationAnalysisController {
    private final OperationAnalysisService operationAnalysisService;

    public OperationAnalysisController(OperationAnalysisService operationAnalysisService) {
        this.operationAnalysisService = operationAnalysisService;
    }

    @ApiOperation(value = "新增分析记录", httpMethod = "POST")
    @PostMapping("/add")
    public Result<Void> add(@RequestBody @ApiParam("分析记录实体") OperationAnalysis entity) {
        operationAnalysisService.save(entity);
        return Result.OK(null);
    }

    @ApiOperation(value = "批量新增分析记录", httpMethod = "POST")
    @PostMapping("/batchAdd")
    public Result<Void> batchAdd(@RequestBody @ApiParam("分析记录实体列表") List<OperationAnalysis> entities) {
        operationAnalysisService.saveBatch(entities);
        return Result.OK(null);
    }

    @ApiOperation(value = "根据ID删除分析记录", httpMethod = "DELETE")
    @DeleteMapping("/delete/{id}")
    public Result<Void> deleteById(@PathVariable @ApiParam("主键ID") String id) {
        operationAnalysisService.deleteById(id);
        return Result.OK(null);
    }

    @ApiOperation(value = "批量删除分析记录", httpMethod = "DELETE")
    @DeleteMapping("/batchDelete")
    public Result<Void> batchDelete(@RequestBody @ApiParam("主键ID列表") List<String> ids) {
        operationAnalysisService.deleteBatch(ids);
        return Result.OK(null);
    }

    @ApiOperation(value = "根据ID查询分析记录", httpMethod = "GET")
    @GetMapping("/getById/{id}")
    public Result<OperationAnalysis> getById(@PathVariable @ApiParam("主键ID") String id) {
        return Result.OK(operationAnalysisService.findById(id));
    }

    @ApiOperation(value = "分页条件查询分析记录", httpMethod = "GET")
    @GetMapping("/listPage")
    public Result<List<OperationAnalysis>> listPage(
            @ApiParam("用户ID") @RequestParam(required = false) String userId,
            @ApiParam("课程ID") @RequestParam(required = false) String courseId,
            @ApiParam("课件ID") @RequestParam(required = false) String sceneId,
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") int page,
            @ApiParam(value = "每页条数", defaultValue = "10") @RequestParam(defaultValue = "10") int size) {
        java.util.Map<String, Object> conditions = new java.util.HashMap<>();
        if (userId != null && !userId.isEmpty()) conditions.put("userId", userId);
        if (courseId != null && !courseId.isEmpty()) conditions.put("courseId", courseId);
        if (sceneId != null && !sceneId.isEmpty()) conditions.put("sceneId", sceneId);
        return Result.OK(operationAnalysisService.queryList(conditions, page, size));
    }

    @ApiOperation(value = "批量根据ID查询分析记录", httpMethod = "POST")
    @PostMapping("/getByIdList")
    public Result<List<OperationAnalysis>> getByIdList(@RequestBody @ApiParam("主键ID列表") List<String> ids) {
        return Result.OK(operationAnalysisService.findByIds(ids));
    }
}
