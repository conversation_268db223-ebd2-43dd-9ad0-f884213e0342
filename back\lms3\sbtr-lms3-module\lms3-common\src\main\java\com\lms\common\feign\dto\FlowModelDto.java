package com.lms.common.feign.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 流程 表单模型
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-06-13 09:39:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlowModelDto implements Serializable {

    /**
     * 模型id
     */
    private String modelId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 表单设计
     */
    private String formJson;

    /**
     * 流程设计
     */
    private String flowJson;

    /**
     * 模型key
     */
    private String modelKey;

    /**
     * 模型数据表名key
     */
    private String formTableName;

    /**
     * 表单属性
     */
    private String formModel;

    /**
     * 模型名称
     */
    private String actDeModelName;

    /**
     * 按钮
     */
    private String formBtnList;

    /**
     * 字段是否可编辑可查看
     */
    private String formFieldList;

    /**
     * 节点通知
     */
    private String formNoticeList;

    /**
     * 判断是自己写的表单还是通过拖动出来的表单  1自己写的表单  2拖动设计的
     */
    private String modelType;

    /**
     * 启动权限类型
     */
    private String  permissionType;

    /**
     * 启动权限值
     */
    private String  permissionValue;

    /**
     * 流程模型类型  1 自定义流程界面  2 托拉拽界面
     */
    private String processModelType;

    /**
     * 外置表单的uuid
     */
    private String actFormConfigureUuid;
    //流程分类Id
    private String category;
    //节点权限码
    private String nodeCodeList;

}
