package com.lms.gateway.core.utils;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 字符串处理工具类
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
public class StringUtils {

    /**
     * 转换null字符串为空值
     *
     * @param str
     * @return
     */
    public static String convertNullToEmpty(String str) {
        return str == null ? "" : str;
    }

    /**
     * 替换字符串中的空格
     *
     * @param str 要替换的字符串
     * @return 返回替换后的字符串
     */
    public static String replaceWithBlank(String str) {
        if (str != null && !"".equals(str)) {
            Pattern p = Pattern.compile("\\s*|\t|\r|\n");
            Matcher m = p.matcher(str);
            String strNoBlank = m.replaceAll("");
            return strNoBlank;
        } else {
            return str;
        }

    }

    /**
     * 将以逗号分割的字段字符串转成带单引号的逗号分割的字符串
     *
     * @param str
     * @return
     */
    public static String str2SqlInStr(String str) {
        String[] strArray = str.split(",");
        String sqlInStr = "";
        for (int i = 0; i < strArray.length; i++) {
            if (i == strArray.length - 1) {
                sqlInStr += "'" + strArray[i] + "'";
            } else {
                sqlInStr += "'" + strArray[i] + "',";
            }
        }
        return sqlInStr;
    }

    /**
     * 检测以分隔符分开的字符串中是否包含某个字符串
     *
     * @param containStr 包含的字符串
     * @param str        检测的字符串
     * @param delimiter  分隔符
     * @return 返回boolean值
     */
    public static boolean isContainInStrByDelimiter(String containStr, String str, String delimiter) {
        return isContainInArray(convertToArray(str, delimiter), containStr);
    }

    /**
     * 字符串数组中是否包含某个字符串
     *
     * @param strArray   字符串数组
     * @param containStr 包含的字符串
     * @return 返回boolean值
     */
    public static boolean isContainInArray(String[] strArray, String containStr) {
        // 利用list的包含方法,进行判断
        if (Arrays.asList(strArray).contains(containStr)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 以固定分隔符分开的字符串转换为数组
     *
     * @param str       字符串
     * @param delimiter 分隔符
     * @return 返回数组
     */
    public static String[] convertToArray(String str, String delimiter) {
        String[] strArray = null;
        strArray = str.split(delimiter); //拆分字符为"," ,然后把结果交给数组strArray
        return strArray;
    }

    /**
     * 把第一个字母变为小写<br>
     * 如：<br>
     * <code>str = "UserDao";</code><br>
     * <code>return "userDao";</code>
     *
     * @param str
     * @return
     */
    public static String convertFirstCharToLowercase(String str) {
        if (!(str.isEmpty() || str.equals(""))) {
            return str.substring(0, 1).toLowerCase() + str.substring(1);
        } else {
            return "";
        }
    }

    /**
     * 把第一个字母变为小写<br>
     * 如：<br>
     * <code>str = "UserDao";</code><br>
     * <code>return "userDao";</code>
     *
     * @param str
     * @return
     */
    public static String convertFirstCharToUppercase(String str) {
        if (!(str.isEmpty() || str.equals(""))) {
            return str.substring(0, 1).toUpperCase() + str.substring(1);
        } else {
            return "";
        }
    }

    /**
     * 判断字符串是否为null或空值
     *
     * @param str
     * @return boolean 为null或空值返回true
     */
    public static boolean isNullOrEmpty(String str) {
        return (str == null || "".equals(str)) ? true : false;
    }

    public static boolean hasLength(String str) {
        return !(str.equals(null) || str.equals(""));
    }

    /**
     * str转list
     *
     * @param str
     * @param separator
     * @return
     */
    public static List<String> str2List(String str, String separator) {
        return Arrays.asList(str.replace("'", "").split(separator));
    }

    /**
     * str转list字符，逗号分隔
     *
     * @param str
     * @return
     */
    public static List<String> str2ListByComma(String str) {
        return str2List(str, ",");
    }

    /**
     * str转list对象，逗号分隔
     *
     * @param str
     * @return
     */
    public static List<Object> str2ListObjByComma(String str) {
        return (List<Object>) (List<?>) str2ListByComma(str);
    }

    /**
     * 驼峰命名转下划线
     *
     * @param name
     * @return
     */
    public static String humpToUnderline(String name) {
        StringBuilder sb = new StringBuilder(name);
        int temp = 0;//定位
        for (int i = 0; i < name.length(); i++) {
            if (Character.isUpperCase(name.charAt(i))) {
                sb.insert(i + temp, "_");
                temp += 1;
            }
        }
        return sb.toString().toLowerCase();
    }

    /**
     * 驼峰转换为下划线
     *
     * @param camelCaseName
     * @return
     */
    public static String camelCase2underscore(String camelCaseName) {
        StringBuilder result = new StringBuilder();
        if (camelCaseName != null && camelCaseName.length() > 0) {
            result.append(camelCaseName.substring(0, 1).toLowerCase());
            for (int i = 1; i < camelCaseName.length(); i++) {
                char ch = camelCaseName.charAt(i);
                if (Character.isUpperCase(ch)) {
                    result.append("_");
                    result.append(Character.toLowerCase(ch));
                } else {
                    result.append(ch);
                }
            }
        }
        return result.toString();
    }

    /**
     * 下划线转换为驼峰
     *
     * @param underscoreName
     * @return
     */
    public static String underscore2camelCase(String underscoreName) {
        StringBuilder result = new StringBuilder();
        if (underscoreName != null && underscoreName.length() > 0) {
            boolean flag = false;
            for (int i = 0; i < underscoreName.length(); i++) {
                char ch = underscoreName.charAt(i);
                if ("_".charAt(0) == ch) {
                    flag = true;
                } else {
                    if (flag) {
                        result.append(Character.toUpperCase(ch));
                        flag = false;
                    } else {
                        result.append(ch);
                    }
                }
            }
        }
        return result.toString();
    }


    /**
     *  * 获取指定url中的某个参数
     *  * @param url
     *  * @param name
     *  * @return
     *  
     */
    public static String getParamByUrl(String url) {
        String regEx = "(\\?|&+)(.+?)=([^&]*)";//匹配参数名和参数值的正则表达式
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(url.toString());
        Map<String, String> paramMap = new LinkedHashMap<String, String>();
        while (m.find()) {
            String paramName = m.group(2);//获取参数名
            String paramVal = m.group(3);//获取参数值
            paramMap.put(paramName, paramVal);
        }

        return paramMap.get("token");
    }
}
