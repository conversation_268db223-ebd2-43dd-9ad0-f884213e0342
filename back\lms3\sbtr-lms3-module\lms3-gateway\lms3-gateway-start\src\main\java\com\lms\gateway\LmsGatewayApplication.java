package com.lms.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityFilterAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;


/**
 *  系统模块启动类
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2024-07-16 13:31:39
 */
@SpringBootApplication(exclude= {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"com.lms.gateway.**"})
public class LmsGatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(LmsGatewayApplication.class, args);
    }

    /**
     * 注册JWT拦截器，可以在配置类中，也可以直接在SpringBoot的入口类中
     *
     * @return FilterRegistrationBean
     */
//    @Bean
//    public FilterRegistrationBean jwtFilter() {
//        final FilterRegistrationBean registrationBean = new FilterRegistrationBean();
//        registrationBean.setFilter(new JwtFilter());
//        //添加需要拦截的url
//        List<String> urlPatterns = Lists.newArrayList();`
//        urlPatterns.add("*");
//        registrationBean.addUrlPatterns(urlPatterns.toArray(new String[urlPatterns.size()]));
//        return registrationBean;
//    }

}
