package com.sbtr.workflow;

import java.util.HashMap;

public class WorkFlowContext {

    private static HashMap<String,Object> innerMap=new HashMap<>();

    private static final class WorkFlowContextHolder {
        static final WorkFlowContext applicationContextHolder = new WorkFlowContext();
    }

    public static WorkFlowContext getRequestContext() {
        return WorkFlowContextHolder.applicationContextHolder;
    }

    public Object get(String key){
        if(innerMap.containsKey(key))
            return innerMap.get(key);
        return null;
    }

    public void set(String key,Object obj){
        innerMap.put(key,obj);
    }

    public void remove(String key){
        if(innerMap.containsKey(key))
            innerMap.remove(key);
    }
}
