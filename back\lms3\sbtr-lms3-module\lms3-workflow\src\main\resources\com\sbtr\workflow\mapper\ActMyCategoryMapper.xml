<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ActMyCategoryMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sbtr.workflow.model.ActMyCategory" id="workflowCategoryMap">
        <result property="uuid" column="uuid"/>
        <result property="createTime" column="create_time"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorOrgId" column="creator_org_id"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="categoryCode" column="category_code"/>
        <result property="categoryName" column="category_name"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="Base_Column_List" >
        uuid,
        create_time,
        creator,
        creator_id,
        creator_org_id,
        modifier_id,
        modifier,
        modify_time,
        category_code,
        category_name
    </sql>

    <select id="getListByCodeAndUuid" resultType="java.lang.Integer">
		SELECT count(category_code) FROM act_my_category WHERE category_code = #{categoryCode} AND uuid != #{uuid}
    </select>

    <delete id="executeDeleteBatch">
		delete from act_my_category where uuid in
		<foreach item="uuid" collection="array" open="(" separator="," close=")">
			#{uuid}
		</foreach>
	</delete>

</mapper>
