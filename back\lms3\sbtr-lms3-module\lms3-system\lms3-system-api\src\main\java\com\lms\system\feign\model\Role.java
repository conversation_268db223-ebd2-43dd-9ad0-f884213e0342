package com.lms.system.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.system.feign.model.Users;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lms.common.model.BaseModel;
import com.lms.common.model.LogObjname;
import com.lms.common.model.SearchContent;
import com.lms.common.model.SearchName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * Role entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)

@TableName("p_role")
@Data
@ApiModel(value = "用户角色实体类")
public class Role extends BaseModel {
    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    @SearchName
    private String id;

    @LogObjname
    @ApiModelProperty("名称")
    @SearchContent
    private String name;

    @ApiModelProperty("描述")
    @SearchContent
    private String description;

    @ApiModelProperty("角色类型")
    private Integer rolekind;// 角色类型,见{@link RoleTypeEnum}

    @JsonIgnore
    @TableField(exist = false)
    @ApiModelProperty("主键id")
    private List<Users> users;


}
