package com.sbtr.workflow.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.PageUtil.PageUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.mapper.ActFormConfigureMapper;
import com.sbtr.workflow.model.ActFormConfigure;
import com.sbtr.workflow.service.ActFormConfigureService;
import tk.mybatis.mapper.entity.Example;

/**
 * 常用外置表单数据
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:35:23
 */
@Service("actFormConfigureServiceImpl")
public class ActFormConfigureServiceImpl extends WorkflowBaseServiceImpl<ActFormConfigure, String> implements ActFormConfigureService {

    @Autowired
    private ActFormConfigureMapper actFormConfigureMapper;

    /**
     * 分页
     *
     * @param pageParam
     * @param filterSort
     * @param puuid
     * @param name
     * @return PageSet<ActFormConfigure>
     */
    @Override
    public PageSet<ActFormConfigure> getPageSet(PageParam pageParam, String filterSort, String puuid, String name, String status) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        Example example = new Example(ActFormConfigure.class);
        Example.Criteria criteria = example.createCriteria();
        if (StrUtil.isNotBlank(status)) {
            criteria.andEqualTo("status", status);
        }
        if (StrUtil.isNotBlank(name)) {
            criteria.orLike("name", "%" + name + "%");
        }
        if (StrUtil.isNotBlank(puuid)) {
            criteria.orLike("puuid", "%" + puuid + "%");
        }
        List<ActFormConfigure> list = actFormConfigureMapper.selectByExample(example);
        PageInfo<ActFormConfigure> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    /**
     * 删除
     *
     * @param uuid
     * @return int
     */
    @Override
    public int executeDeleteBatch(String[] uuid) {
        return actFormConfigureMapper.executeDeleteBatch(uuid);
    }

    /**
     * uuid更新状态
     *
     * @param uuid   uuid
     * @param status 状态
     * @return int
     */
    @Override
    public int updateStatusByUuid(String uuid, String status) {
        return actFormConfigureMapper.updateStatusByUuid(uuid, status);
    }


}
