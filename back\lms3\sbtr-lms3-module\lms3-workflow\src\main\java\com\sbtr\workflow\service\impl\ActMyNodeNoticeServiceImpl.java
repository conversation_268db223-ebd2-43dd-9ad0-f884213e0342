package com.sbtr.workflow.service.impl;

import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import cn.ewsd.common.utils.easyui.PageUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.mapper.ActMyNodeNoticeMapper;
import com.sbtr.workflow.model.ActMyNodeNotice;
import com.sbtr.workflow.service.ActMyNodeNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("flowNodeNoticeServiceImpl")
public class ActMyNodeNoticeServiceImpl extends WorkflowBaseServiceImpl<ActMyNodeNotice, String> implements ActMyNodeNoticeService {
	@Autowired
	private ActMyNodeNoticeMapper flowNodeNoticeMapper;

    @Override
    public PageSet<ActMyNodeNotice> getPageSet(PageParam pageParam, String filterSort) {
        PageHelper.startPage(pageParam.getPage(), pageParam.getRows());
        List<ActMyNodeNotice> list = flowNodeNoticeMapper.getPageSet(filterSort);
        PageInfo<ActMyNodeNotice> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

	@Override
	public int executeDeleteBatch(String[] uuids){
		return flowNodeNoticeMapper.executeDeleteBatch(uuids);
	}

    @Override
    public Integer insertList(List<ActMyNodeNotice> list) {
        return flowNodeNoticeMapper.insertList(list);
    }

    @Override
    public List<ActMyNodeNotice> getListByNodeIdAndModelKeyAndProcdefId(String nodelId, String modelKey, String procdefId) {
        return flowNodeNoticeMapper.getListByNodeIdAndModelKeyAndProcdefId(nodelId,modelKey,procdefId);
    }

    @Override
    public List<ActMyNodeNotice> getListByActDeModelKeyAndProcdefId(String key, String procdefIds) {
        return flowNodeNoticeMapper.getListByActDeModelKeyAndProcdefId(key,procdefIds);
    }

    @Override
    public Integer updateProcdefIdByModelKey(String key, String procdefId) {
        return flowNodeNoticeMapper.updateProcdefIdByModelKey(key,procdefId);
    }

    @Override
    public Integer updateProcdefIdIsNull(String key, String id) {
        return flowNodeNoticeMapper.updateProcdefIdIsNull(key,id);
    }

    @Override
    public int deleteByModelKeyAnProcdefId(String key, String id) {
        return flowNodeNoticeMapper.deleteByModelKeyAnProcdefId(key,id);
    }

    @Override
    public Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id) {
        return flowNodeNoticeMapper.updateProcdefIdByModelKeyAndProcdef(procdefId,key,id);
    }


}
