package com.lms.base.scopecourseware.rtefile;

import java.util.Calendar;

import com.lms.common.util.LMSCacheManager;
import org.apache.poi.hwpf.usermodel.DateAndTime;


import lms.rte.CAM.Model.SeqActivityTree;
import lms.rte.Common.DataProviderType;
import lms.rte.DataManager.ITreeProvider;
import lms.rte.DataManager.RTEDataManager;

public class TreeProvider implements ITreeProvider {
	public SeqActivityTree GetTree(String userId, String CourseId,
			DataProviderType state, boolean review) {
		String cacheKey = "userId=" + userId + "CourseId=" + CourseId
				+ "state=" + state + "reviw=" + review;

		// 如果缓存有值且合法
		Object treeData = LMSCacheManager.GetDataFromCach(cacheKey);
		SeqActivityTree tree = null;
		if (treeData != null) {
			tree = (SeqActivityTree) treeData;
		}

		// 如果没有则获取数据并添加至缓存
		if (tree == null) {
			RTEDataManager DataManager = new RTEDataManager();
			tree = DataManager.GetLearnerActivityTree(userId, CourseId, state);
			if (tree != null) {
				DateAndTime timer = new DateAndTime();
				timer.getDate().add(Calendar.MINUTE, 30);
				LMSCacheManager.addDataToCach(cacheKey, tree, timer);
			}
		}

		return tree;
	}

	public void RemoveCacheTree(String userId, String CourseId,
			DataProviderType state, boolean review) {
		String cacheKey = this + "userId=" + userId + "CourseId=" + CourseId
				+ "state=" + state + "reviw=" + review;
		LMSCacheManager.removeCache(cacheKey);
	}

	public void PersistTree(SeqActivityTree tree, DataProviderType state,
			boolean review) {
		if (tree == null) {
			return;
		}
		String cacheKey = this + "userId=" + tree.getLearnerId() + "CourseId="
				+ tree.getCourseId() + "state=" + state + "reviw=" + review;
		LMSCacheManager.RefreshCache(cacheKey, tree, 30);

		RteTreeTask treeTask = new RteTreeTask(tree.getLearnerId(),
				tree.getCourseId(), state, review, tree);
		DefaultHandlerTask.getInstance().ExcuteTask(treeTask);
	}
}
