# TrDialog 组件

一个基于 Ant Design Vue 的对话框组件，支持拖拽、全屏等功能。

## 功能特性

- 继承 a-modal 所有原生功能
- 支持拖拽移动（使用优化的自定义指令）
- 支持全屏切换
- 自定义底部按钮
- TypeScript 类型支持

## 基础用法

```vue
<template>
  <div>
    <a-button @click="showDialog = true">打开对话框</a-button>
    
    <TrDialog
      v-model:open="showDialog"
      title="基础对话框"
      width="600px"
    >
      <p>这是对话框的内容</p>
    </TrDialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import TrDialog from '@/components/TrDialog/index.vue';

const showDialog = ref(false);
</script>
```

## 拖拽功能

```vue
<template>
  <TrDialog
    v-model:open="showDialog"
    title="可拖拽对话框"
    :draggable="true"
  >
    <p>拖拽标题栏可以移动对话框</p>
  </TrDialog>
</template>
```

## 全屏功能

```vue
<template>
  <TrDialog
    v-model:open="showDialog"
    title="全屏对话框"
    :fullscreen="true"
  >
    <p>点击右上角全屏按钮切换全屏模式</p>
  </TrDialog>
</template>
```

## 自定义底部按钮

```vue
<template>
  <TrDialog
    v-model:open="showDialog"
    title="自定义按钮"
    :footer-buttons="footerButtons"
  >
    <p>自定义底部按钮</p>
  </TrDialog>
</template>

<script setup lang="ts">
import type { ButtonConfig } from '@/components/TrDialog/types';

const footerButtons: ButtonConfig[] = [
  {
    text: '保存',
    type: 'primary',
    onClick: (e) => {
      console.log('保存');
    }
  },
  {
    text: '删除',
    type: 'default',
    danger: true,
    onClick: (e) => {
      console.log('删除');
    }
  }
];
</script>
```

## Props

### 继承 a-modal 的属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| open | boolean | false | 对话框是否可见 |
| title | string \| VNode | - | 对话框标题 |
| width | string \| number | 600 | 对话框宽度 |
| confirmLoading | boolean | false | 确认按钮loading状态 |
| okText | string | "确定" | 确认按钮文字 |
| cancelText | string | "取消" | 取消按钮文字 |
| okType | string | "primary" | 确认按钮类型 |
| okButtonProps | object | - | 确认按钮属性 |
| cancelButtonProps | object | - | 取消按钮属性 |
| closable | boolean | true | 是否显示关闭按钮 |
| maskClosable | boolean | true | 点击遮罩是否关闭 |
| destroyOnClose | boolean | false | 关闭时销毁子元素 |
| centered | boolean | false | 是否居中显示 |
| zIndex | number | 1000 | 对话框层级 |
| bodyStyle | object | - | 内容区域样式 |
| maskStyle | object | - | 遮罩样式 |
| wrapClassName | string | - | 容器类名 |
| keyboard | boolean | true | 是否支持键盘ESC关闭 |
| footer | VNode \| null | - | 自定义底部内容 |

### 扩展属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| draggable | boolean | false | 是否可拖拽 |
| fullscreen | boolean | false | 是否全屏显示 |
| showFooter | boolean | true | 是否显示底部按钮 |
| footerButtons | ButtonConfig[] | - | 自定义底部按钮配置 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:open | (value: boolean) | 对话框显示状态变化 |
| ok | (e: MouseEvent) | 确认按钮点击 |
| cancel | (e: MouseEvent) | 取消按钮点击 |
| fullscreen-change | (value: boolean) | 全屏状态变化 |
| afterClose | - | 对话框关闭后回调 |

## 类型定义

```typescript
interface ButtonConfig {
  text: string;                    // 按钮文字
  type?: 'primary' | 'default' | 'dashed' | 'link' | 'text'; // 按钮类型
  loading?: boolean;               // 是否显示 loading
  danger?: boolean;                // 是否为危险按钮
  onClick: (e: MouseEvent) => void; // 点击事件
  props?: Record<string, any>;     // 其他按钮属性
}
```

## 插槽

| 插槽名 | 说明 |
|--------|------|
| default | 对话框内容 |
| title | 自定义标题 |
| footer | 自定义底部内容 |

## 样式定制

组件使用 CSS 变量，可以通过以下方式定制样式：

```scss
.trdialog-header {
  background: var(--gray-50, #f5f5f5);
  border-bottom: 1px solid var(--gray-200, #e5e7eb);
}

.trdialog-footer {
  border-top: 1px solid var(--gray-200, #e5e7eb);
}
```
