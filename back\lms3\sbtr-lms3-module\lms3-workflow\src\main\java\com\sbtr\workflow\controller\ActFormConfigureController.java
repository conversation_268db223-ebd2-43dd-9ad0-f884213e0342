package com.sbtr.workflow.controller;

import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.utils.BaseUtils;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.sbtr.workflow.model.ActFormConfigure;
import com.sbtr.workflow.service.ActFormConfigureService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.StringUtil.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 常用外置表单数据主表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:35:23
 */
@Api(tags = {"常用外置表单数据主表"})
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/actFormConfigure")
public class ActFormConfigureController extends WorkflowBaseController {

    @Autowired
    private ActFormConfigureService actFormConfigureService;

    @ApiOperation(value = "获得ActFormConfigure分页集数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String"),
    })
    @ResponseBody
    @RequestMapping(value = "/getPageSet", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam, String puuid, String name,String status) {
        String filterSort = "";
//        filterSort = BaseUtils.filterSort(request, filterSort);
        PageSet<ActFormConfigure> pageSet = actFormConfigureService.getPageSet(pageParam, filterSort, puuid, name,status);
        return pageSet;
    }

    @ApiOperation(value = "获得ActFormConfigure模块详细数据")
    @ApiImplicitParam(name = "uuid", value = "获得ActFormConfigure模块详细数据", required = true, dataType = "String")
    @ResponseBody
    @RequestMapping(value = "/getDetailByUuid")
    public Object getDetailByUuid(String uuid) {
        ActFormConfigure actFormConfigure = actFormConfigureService.selectByPrimaryKey(StringUtils.emptyToNull(uuid));
        return actFormConfigure;
    }


    @Log(title = "ActFormConfigure模块-保存",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @ApiOperation(value = "保存ActFormConfigure模块数据")
    @ApiImplicitParam(name = "actFormConfigure", value = "保存ActFormConfigure模块数据", required = true, dataType = "ActFormConfigure")
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Object save(@ModelAttribute ActFormConfigure actFormConfigure) {
        setLogMessage(actFormConfigure.getName(),"");
        int result = actFormConfigureService.insertSelective(getSaveData(actFormConfigure));
        return result > 0 ? success("保存成功！") : failure("保存失败！");
    }


    @Log(title = "ActFormConfigure模块-更新",module = LogModule.WORKFLOW,businessType = BusinessType.UPDATE, method = "getLogMessage")
    @ApiOperation(value = "更新ActFormConfigure模块数据")
    @ApiImplicitParam(name = "actFormConfigure", value = "更新ActFormConfigure模块数据", required = true, dataType = "ActFormConfigure")
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Object update(@ModelAttribute ActFormConfigure actFormConfigure) {
        setLogMessage(actFormConfigure.getName(),"");
        int result = actFormConfigureService.updateByPrimaryKeySelective(getUpdateData(actFormConfigure));
        return result > 0 ? success("更新成功！") : failure("更新失败！");
    }

    @Log(title = "ActFormConfigure模块-删除",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ApiOperation(value = "删除ActFormConfigure模块数据")
    @ApiImplicitParam(name = "actFormConfigure", value = "删除ActFormConfigure模块数据", required = true, dataType = "ActFormConfigure")
    @ResponseBody
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.POST)
    public Object deleteBatch(@RequestParam String[] uuid) {
        if (ArrayUtil.isEmpty(uuid)) {
            return failure("必要参数不能为空！！！");
        }
        ActFormConfigure actFormConfigure = actFormConfigureService.selectByPrimaryKey(StringUtils.emptyToNull(uuid[0]));
        if (ObjectUtil.isEmpty(actFormConfigure)) {
            return failure("查询不到要删除的数据！！！");
        }
        setLogMessage(actFormConfigure.getName(),"");
        //状态
        String KEY_PRE = "true";
        if (KEY_PRE.equals(actFormConfigure.getStatus())) {
            return failure("已启用的表单数据不建议删除！！！");
        }
        int result = actFormConfigureService.executeDeleteBatch(uuid);
        return result > 0 ? success("删除成功！") : failure("删除失败！");
    }

    @ApiOperation(value = "更新状态")
    @ResponseBody
    @RequestMapping(value = "/updateStatusByUuid", method = RequestMethod.POST)
    public Object updateStatusByUuid(String uuid, String status) {
        if (StrUtil.hasBlank(uuid, status)) {
            return failure("必要参数不能为空");
        }
        int result = actFormConfigureService.updateStatusByUuid(uuid, status);
        return result > 0 ? success("更新成功！") : failure("更新失败！");
    }

}
