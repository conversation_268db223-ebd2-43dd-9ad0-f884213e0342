
package com.lms.examine.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.SearchContent;
import com.lms.common.model.SearchName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@TableName("u_coursewareexamine")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Coursewareexamine implements java.io.Serializable {

    @TableId(type= IdType.ASSIGN_ID)
    @SearchName
    private String id;

    @TableField("NAME")
    @SearchContent
    private String name;

    @TableField("COURSEWAREID")
    private String coursewareid;

    @TableField("EXAMSECOND")
    private Integer examsecond;

    @TableField("CREATOR")
    private String creator;

    @TableField("SEQNO")
    private Integer seqno;

    @TableField("REPLAYSECOND")
    private Integer replaysecond;

    @TableField("PASSRATE")
    private Integer passrate;


}
