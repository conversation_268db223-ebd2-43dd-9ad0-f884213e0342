package com.lms.base.selectitem.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.base.feign.model.SelectitemType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * 下拉框组件数据库操作接口
 *
 * <AUTHOR>
@Mapper
public interface SelectitemTypeMapper extends BaseMapper<SelectitemType> {

    @Select(value = "select * from b_selectitemtype order by seqno asc,objname asc")
    List<SelectitemType> getAllSelectitemTypes();

    @Select("${sql}")
    int getMaxSeqno(@Param("sql") String sql);
}
