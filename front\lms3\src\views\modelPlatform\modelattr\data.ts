// 静态函数变量定义
// 表单验证规则
export const rules = {
  name: [{ required: true, message: "请输入属性名" }],
  value: [{ required: true, message: "请输入属性值" }]
};

// 表格列配置 - 展示所有渲染类型和高级配置
export const columns = [
  {
    title: "属性名",
    dataIndex: "name",
    width: 300
  },
  {
    title: "属性值",
    dataIndex: "value",
    align: "center",
    width: 300
    // sorter: true
  }
];

export const queryItems = [
  {
    key: "name",
    label: "属性名",
    component: "a-input",
    props: {
      placeholder: "请输入属性名",
      allowClear: true
    }
  },
  {
    key: "value",
    label: "属性值",
    component: "a-input",
    props: {
      placeholder: "请输入属性值",
      allowClear: true
    }
  }
];
