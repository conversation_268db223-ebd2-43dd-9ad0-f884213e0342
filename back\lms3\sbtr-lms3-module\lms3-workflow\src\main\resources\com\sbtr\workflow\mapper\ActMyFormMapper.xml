<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ActMyFormMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sbtr.workflow.model.ActMyForm" id="actMyFormMap">
        <result property="uuid" column="uuid"/>
        <result property="createTime" column="create_time"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorOrgId" column="creator_org_id"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="actDeModelKey" column="act_de_model_key"/>
        <result property="formTableName" column="form_table_name"/>
        <result property="formDesign" column="form_design"/>
        <result property="formModel" column="form_model"/>
        <result property="actDeModelName" column="act_de_model_name"/>
        <result property="procdefId" column="procdef_id"/>
        <result property="actDeModelId" column="act_de_model_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid
        ,  	  	     			 create_time
		  ,  	  	     			 creator
		  ,  	  	     			 creator_id
		  ,  	  	     			 creator_org_id
		  ,  	  	     			 modifier_id
		  ,  	  	     			 modifier
		  ,  	  	     			 modify_time
		  ,  	  	     			 act_de_model_key
		  ,  	  	     			 form_table_name
		  ,  	  	     			 form_design
		  ,  	  	     			 form_model
		  ,  	  	     			 act_de_model_name
		  ,  	  	     			 procdef_id
		  ,  	  	     			 act_de_model_id
		  ,  	  	     			 process_instance_id
    </sql>

    <update id="updateFormByPrimaryKeySelective">
        update act_my_form
        set form_design =#{formDesign}
        where uuid = #{uuid}
    </update>
    <update id="updateFormDesignByUuid">
        update form_layout set design_json=#{json} where uuid=#{uuid}
    </update>

    <select id="getPageSet" resultType="com.sbtr.workflow.model.ActMyForm">
        select
        <include refid="Base_Column_List"></include>
        from act_my_form
        <where>

            <if test="filterSort != null">
                ${filterSort}
            </if>
        </where>
    </select>

    <select id="selectByProcessInstanceId" resultType="com.sbtr.workflow.model.ActMyForm">
        select
        <include refid="Base_Column_List"></include>
        from act_my_form
        where procdef_id =#{processDefinitionId} and process_instance_id =#{processInstanceId} and act_de_model_key
        =#{modelKey}

    </select>

    <select id="getListDataByModelKeyAndProcdefId" resultType="com.sbtr.workflow.model.ActMyForm">
        select
        <include refid="Base_Column_List"></include>
        from act_my_form
        where act_de_model_key=#{modelKey}
    </select>
    <select id="getFormLayoutFormJSON" resultType="java.lang.String">
        select
            design_json
        from form_layout
        where layout_code=#{formLayou}
    </select>
    <select id="getFormLayoutForm" resultType="com.sbtr.workflow.vo.FormLayoutVo">
        select
            *
        from form_layout where basic_configure_uuid=#{formUuid}
    </select>


    <delete id="executeDeleteBatch">
        delete from act_my_form where uuid in
        <foreach item="uuid" collection="array" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </delete>

</mapper>
