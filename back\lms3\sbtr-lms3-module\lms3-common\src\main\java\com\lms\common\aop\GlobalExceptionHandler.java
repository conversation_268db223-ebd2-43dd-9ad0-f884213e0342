package com.lms.common.aop;

import com.lms.common.model.DataException;
import com.lms.common.model.Result;
import com.lms.common.util.LogUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.annotation.Resource;

@ControllerAdvice
public class GlobalExceptionHandler {
    @Resource
    private LogUtil logUtil;

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public ResponseEntity<Result> handleException(Exception e) {
        if(e instanceof DataException){
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Result.error(e.getMessage()));
        }else if(e instanceof MaxUploadSizeExceededException){
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Result.error("文件内容过大，无法上传！"));
        }else{
            e.printStackTrace();
            Result result = logUtil.Error(e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

}

