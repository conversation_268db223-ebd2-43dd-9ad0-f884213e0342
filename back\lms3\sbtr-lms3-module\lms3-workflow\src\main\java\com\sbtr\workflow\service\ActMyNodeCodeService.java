package com.sbtr.workflow.service;

import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import com.sbtr.workflow.model.ActMyNodeCode;
import com.sbtr.workflow.vo.FlowNodeFieldVo;

import java.util.List;

/**
 * 流程每个节点所对字段是否可编辑以及是否可见
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-27 13:43:13
 */
public interface ActMyNodeCodeService extends WorkflowBaseService<ActMyNodeCode, String> {

    /**
     * 分页
     *
     * @return
     * @Param pageParam 分页参数
     */
    PageSet<ActMyNodeCode> getPageSet(PageParam pageParam);

    /**
     * 批量插入节点属性数据
     *
     * @return
     */
    Integer insertList(List<ActMyNodeCode> list1);

    /**
     * 根据节点和procdefId查询数据
     *
     * @return
     */
    List<ActMyNodeCode> selectByParam(String nodeId,String key, String procdefId);


    /**
     * 根据节点和procdefId更新数据
     *
     * @return
     */
    Integer updateNodeCodeByParam(String nodeCode, String uuid);

    void saveOrUpdateNodeCode(List<ActMyNodeCode> actMyNodeCodes,String actDeModelKey,String procdefId);

    void updateProcdefIdByModelKey(String key, String procdefId);

    List<ActMyNodeCode> getListByActDeModelKeyAndProcdefId(String key, String procdefId);

    Integer updateProDefByParam(String procdefId, String uuid);

    Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id);

    void deleteByModelKeyAnProcdefId(String key, String id);
}
