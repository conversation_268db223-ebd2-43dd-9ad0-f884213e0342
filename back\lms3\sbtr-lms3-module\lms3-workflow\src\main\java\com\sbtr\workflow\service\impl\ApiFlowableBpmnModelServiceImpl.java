package com.sbtr.workflow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.sbtr.workflow.constants.DriverClassName;
import com.sbtr.workflow.dto.FormBtnListDto;
import com.sbtr.workflow.dto.FormFieldListDto;
import com.sbtr.workflow.mapper.ApiFlowableModelMapper;
import com.sbtr.workflow.service.ApiFlowableBpmnModelService;
import com.sbtr.workflow.service.ApiFlowableModelService;
import com.sbtr.workflow.vo.ModelVo;
import org.apache.commons.lang.StringUtils;
import org.flowable.bpmn.BpmnAutoLayout;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.editor.language.json.converter.BpmnJsonConverter;
import org.flowable.editor.language.json.converter.util.CollectionUtils;
import org.flowable.engine.RepositoryService;
import org.flowable.ui.common.service.exception.BadRequestException;
import org.flowable.ui.common.service.exception.InternalServerErrorException;
import org.flowable.ui.common.util.XmlUtil;
import org.flowable.ui.modeler.domain.AbstractModel;
import org.flowable.ui.modeler.domain.Model;
import org.flowable.ui.modeler.model.ModelKeyRepresentation;
import org.flowable.ui.modeler.model.ModelRepresentation;
import org.flowable.ui.modeler.repository.ModelRepository;
import org.flowable.ui.modeler.serviceapi.ModelService;
import org.flowable.validation.ProcessValidator;
import org.flowable.validation.ProcessValidatorFactory;
import org.flowable.validation.ValidationError;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service("apiFlowableBpmnModelServiceImpl")
public class ApiFlowableBpmnModelServiceImpl extends BaseServiceImpl implements ApiFlowableBpmnModelService {

    @Value("${spring.datasource.dynamic.datasource.master.driver-class-name:null}")
    public String driverClassName;

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiFlowableBpmnModelServiceImpl.class);

    @Autowired
    protected RepositoryService repositoryService;

    //验证模板
    protected BpmnXMLConverter bpmnXmlConverter = new BpmnXMLConverter();

    protected BpmnJsonConverter bpmnJsonConverter = new BpmnJsonConverter();

    @Autowired
    private ApiFlowableModelMapper apiFlowableModelMapper;
    @Autowired
    protected ModelRepository modelRepository;

    @Autowired
    protected ModelService modelService;

    @Autowired
    protected ObjectMapper objectMapper;

    @Override
    public BpmnModel getBpmnModelByProcessDefId(String processDefId) {
        return repositoryService.getBpmnModel(processDefId);
    }

    @Override
    public boolean checkActivitySubprocessByActivityId(String processDefId, String activityId) {
        boolean flag = true;
        List<FlowNode> activities = findFlowNodesByActivityId(processDefId, activityId);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(activities)) {
            flag = false;
        }
        return flag;
    }

    public List<FlowNode> findFlowNodesByActivityId(String processDefId, String activityId) {
        List<FlowNode> activities = new ArrayList<>();
        BpmnModel bpmnModel = this.getBpmnModelByProcessDefId(processDefId);
        List<Process> processes = bpmnModel.getProcesses();
        for (Process process : processes) {
            FlowElement flowElement = process.getFlowElement(activityId);
            if (flowElement != null) {
                FlowNode flowNode = (FlowNode) flowElement;
                activities.add(flowNode);
            }
        }
        return activities;
    }

    @Override
    public List<EndEvent> findEndFlowElement(String processDefId) {
        BpmnModel bpmnModel = this.getBpmnModelByProcessDefId(processDefId);
        if (bpmnModel != null) {
            Process process = bpmnModel.getMainProcess();
            return process.findFlowElementsOfType(EndEvent.class);
        } else {
            return null;
        }
    }


    @Override
    public Activity findActivityByName(String processDefId, String name) {
        Activity activity = null;
        BpmnModel bpmnModel = getBpmnModelByProcessDefId(processDefId);
        Process process = bpmnModel.getMainProcess();
        Collection<FlowElement> list = process.getFlowElements();
        for (FlowElement f : list) {
            if (StringUtils.isNotBlank(name)) {
                if (name.equals(f.getName())) {
                    activity = (Activity) f;
                    break;
                }
            }
        }
        return activity;
    }

    @Override
    public FlowNode findFlowNodeByActivityId(String processDefId, String activityId) {
        FlowNode activity = null;
        BpmnModel bpmnModel = getBpmnModelByProcessDefId(processDefId);
        List<Process> processes = bpmnModel.getProcesses();
        for (Process process : processes) {
            FlowElement flowElement = process.getFlowElementMap().get(activityId);
            if (flowElement != null) {
                activity = (FlowNode) flowElement;
                break;
            }
        }
        return activity;
    }

    @Override
    public Object addModel(ModelVo modelVo, String formFieldList, String formBtnList) {
        InputStream inputStream = new ByteArrayInputStream(modelVo.getXml().getBytes());
        return createModel(inputStream, modelVo, formFieldList, formBtnList);
    }

    @Autowired
    private ApiFlowableModelService apiFlowableModelService;

    @Transactional
    public Object createModel(InputStream inputStream, ModelVo modelVo, String formFieldList, String formBtnList) {
        try {
            XMLInputFactory xif = XmlUtil.createSafeXmlInputFactory();
            InputStreamReader xmlIn = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
            XMLStreamReader xtr = xif.createXMLStreamReader(xmlIn);
            // 把XML转换成BpmnModel对象
            BpmnModel bpmnModel = bpmnXmlConverter.convertToBpmnModel(xtr);

            //模板验证
//            ProcessValidator validator = new ProcessValidatorFactory().createDefaultProcessValidator();
//            List<ValidationError> errors = validator.validate(bpmnModel);
//            if (CollectionUtils.isNotEmpty(errors)) {
//                StringBuffer es = new StringBuffer();
//                errors.forEach(ve -> es.append(ve.toString()).append("/n"));
//                LOGGER.error("流程设计有问题，原因是: {}", Convert.toStr(es));
//                return failure("请按照规范设计流程或者直接通过追加方式设计流程或者检查是否设置分支条件");
//            }
            String fileName = bpmnModel.getMainProcess().getName();
            if (CollectionUtils.isEmpty(bpmnModel.getProcesses())) {
                return failure("No process found in definition " + fileName);
            }
            if (bpmnModel.getLocationMap().size() == 0) {
                BpmnAutoLayout bpmnLayout = new BpmnAutoLayout(bpmnModel);
                bpmnLayout.execute();
            }
            ObjectNode modelNode = bpmnJsonConverter.convertToJson(bpmnModel);
            org.flowable.bpmn.model.Process process = bpmnModel.getMainProcess();
            //userTasks校验
            List<UserTask> userTasks = process.findFlowElementsOfType(UserTask.class);
            if (CollUtil.isEmpty(userTasks)) {
                //return failure("请先设计用户任务");
            }
            for (int i = 0; i < userTasks.size(); i++) {
                if (StrUtil.isBlank(userTasks.get(i).getName())) {
                    return failure("用户任务节点名称不能为空，请检查");
                }
                if (CollUtil.isEmpty(userTasks.get(i).getIncomingFlows())) {
                    return failure("请设计用户任务【" + userTasks.get(i).getName() + "】前面连线");
                }

                if (CollUtil.isEmpty(userTasks.get(i).getOutgoingFlows())) {
                    return failure("请设计用户任务【" + userTasks.get(i).getName() + "】后面连线");
                }
                if(userTasks.get(i).getOutgoingFlows().size() > 1){
                    int  conditionCount = userTasks.get(i).getOutgoingFlows().size();
                    for (int j = 0; j < userTasks.get(i).getOutgoingFlows().size(); j++) {
                        String conditionExpression = userTasks.get(i).getOutgoingFlows().get(j).getConditionExpression();
                        if(StrUtil.isBlank(conditionExpression)){
                            conditionCount = conditionCount - 1;
                        }
                    }
                    if(conditionCount == 0){
                        return failure("用户任务【" + userTasks.get(i).getName() + "】后面存在多条路径分支，需要为每条分支设置条件(也可以有一条分支不设置条件)");
                    }

                }
                //节点之间连线校验
                //校验是否选择了表单
//                String strs = formFieldList.replace("\\", "");
//                List<FormFieldListDto> formFieldListDtos = JSONObject.parseArray(strs, FormFieldListDto.class);
//                if (CollUtil.isEmpty(formFieldListDtos)) {
//                    return failure("请先绑定表单");
//                }
                String userTasksId = userTasks.get(i).getId();
//                boolean containsField = formFieldListDtos.stream()
//                        .filter(item -> Optional.ofNullable(item.getId()).isPresent())
//                        .anyMatch(item -> item.getId().equals(userTasksId));
//                if (containsField) {
//                } else {
//                    return failure("请设计用户任务【" + userTasks.get(i).getName() + "】后在【表单配置】中添加表单");
//                }
                //校验是否选择按钮
                if (StringUtils.isNotEmpty(formBtnList)){
                    String btnListStr = formBtnList.replace("\\", "");
                    List<FormBtnListDto> formBtnLists = JSONObject.parseArray(btnListStr, FormBtnListDto.class);
                    if (CollUtil.isEmpty(formBtnLists)) {
                        return failure("按钮数据不能为空！！！");
                    }
                    boolean b = formBtnLists.stream()
                            .filter(item -> Optional.ofNullable(item.getId()).isPresent())
                            .anyMatch(item -> item.getId().equals(userTasksId));
                    if (b) {
                    } else {
                        return failure("请设计用户任务【" + userTasks.get(i).getName() + "】后在【按钮配置】中添加按钮");
                    }
                }
            }
            //StartEvent校验
            List<StartEvent> startEventList = process.findFlowElementsOfType(StartEvent.class);
            if (CollUtil.isEmpty(startEventList)) {
                return failure("请先设计开始事件");
            }
            for (StartEvent startEvent : startEventList) {
                List<SequenceFlow> outGoingFlows = startEvent.getOutgoingFlows();
                if (CollectionUtils.isEmpty(outGoingFlows)) {
                    String name = StrUtil.isBlank(startEvent.getName()) ? "开始事件" : startEvent.getName();
                    return failure("请设计开始事件【" + name + "】后面连线");
                }
            }
            //EndEvent校验
            List<EndEvent> endEvents = process.findFlowElementsOfType(EndEvent.class);
            if (CollUtil.isEmpty(endEvents)) {
                return failure("请先设计结束事件");
            }
            for (EndEvent endEvent : endEvents) {
                List<SequenceFlow> incomingFlows = endEvent.getIncomingFlows();
                if (CollectionUtils.isEmpty(incomingFlows)) {
                    String name = StrUtil.isBlank(endEvent.getName()) ? "结束事件" : endEvent.getName();
                    return failure("请设计结束事件【" + name + "】前面连线");
                }
            }
            String name = process.getId();
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(process.getName())) {
                name = process.getName();
            }
            //判断模型key值，必须NCName。NCName只能以下划线(_)或字母开头，只能包含中划线(-)，下划线，字母和数字
            String modelKey = process.getId();
            String pattern = "^[a-zA-Z_][-_a-zA-Z0-9]*";
            Pattern r = Pattern.compile(pattern);
            Matcher m = r.matcher(modelKey);
            if (!m.matches()) {
                //return failure("流程标识只能以下划线(_)或字母开头，只能包含中划线(-)，下划线，字母和数字");
            }
            String description = process.getDocumentation();
            //查询是否已经存在流程模板
            Model newModel = new Model();
            List<Model> models = null;
            if (DriverClassName.POSTGRESQL_NAME.equals(driverClassName)) {
                models = apiFlowableModelMapper.findByKeyAndType(modelVo.getProcessId(), AbstractModel.MODEL_TYPE_BPMN);
            } else {
                models = modelRepository.findByKeyAndType(modelVo.getProcessId(), AbstractModel.MODEL_TYPE_BPMN);
            }
            if (CollectionUtils.isNotEmpty(models)) {
                Model updateModel = models.get(0);
                newModel.setId(updateModel.getId());
            }
            newModel.setName(name);
            newModel.setKey(process.getId());
            newModel.setModelType(AbstractModel.MODEL_TYPE_BPMN);
            newModel.setCreated(new Date());
            newModel.setCreatedBy(businessSystemDataService.getUserNameId());
            newModel.setDescription(description);
            newModel.setModelEditorJson(Convert.toStr(modelNode));
            newModel.setLastUpdated(new Date());
            newModel.setLastUpdatedBy(businessSystemDataService.getUserNameId());
            Model model = modelService.createModel(newModel, businessSystemDataService.getUser());
            //# 不重写实体类,直接修改ACT_DE_MODEL表的process_model_type字段
            Integer integer = apiFlowableModelService.updateProcessModelTypeById(model.getId(), modelVo.getProcessModelType());
            HashMap var3 = new HashMap();
            var3.put("statusCode", 200);
            var3.put("title", "操作提示");
            var3.put("message", "成功");
            var3.put("modelId", model.getId());
            var3.put("modelKey", model.getKey());
            var3.put("modelName", process.getName());
            return var3;
        } catch (BadRequestException e) {
            LOGGER.info("失败原因为===" + e.getMessage());
            throw e;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure(e.getMessage());
        }
    }

    @Override
    public Object importProcessModel(@RequestParam("file") MultipartFile file) throws IOException {
        String fileName = file.getOriginalFilename();
        if (fileName != null && (fileName.endsWith(".bpmn") || fileName.endsWith(".bpmn20.xml"))) {
            return failure("Invalid file name, only .bpmn and .bpmn20.xml files are supported not " + fileName);
        }
        InputStream inputStream = file.getInputStream();
        try {
            createModel(inputStream, null, null, null);
            return success("导入成功");
        } catch (Exception e) {
            return failure("导入失败:异常信息为===" + e.getMessage());
        } finally {
            inputStream.close();
        }
    }


    @Override
    public ObjectNode editorModelData(String modelId) {
        Model model = null;
        if (DriverClassName.POSTGRESQL_NAME.equals(driverClassName)) {
            model = apiFlowableModelMapper.getModel(modelId.trim());
        } else {
            model = this.modelService.getModel(modelId);
        }
        ObjectNode modelNode = this.objectMapper.createObjectNode();
        modelNode.put("modelId", model.getId());
        modelNode.put("name", model.getName());
        modelNode.put("key", model.getKey());
        modelNode.put("description", model.getDescription());
        modelNode.putPOJO("lastUpdated", model.getLastUpdated());
        modelNode.put("lastUpdatedBy", model.getLastUpdatedBy());
        ObjectNode editorJsonNode;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(model.getModelEditorJson())) {
            try {
                editorJsonNode = (ObjectNode) this.objectMapper.readTree(model.getModelEditorJson());
                editorJsonNode.put("modelType", "model");
                modelNode.set("model", editorJsonNode);
            } catch (Exception var6) {
                LOGGER.error("Error reading editor json {}", modelId, var6);
                throw new InternalServerErrorException("Error reading editor json " + modelId);
            }
        } else {
            editorJsonNode = this.objectMapper.createObjectNode();
            editorJsonNode.put("id", "canvas");
            editorJsonNode.put("resourceId", "canvas");
            ObjectNode stencilSetNode = this.objectMapper.createObjectNode();
            stencilSetNode.put("namespace", "http://b3mn.org/stencilset/bpmn2.0#");
            editorJsonNode.put("modelType", "model");
            modelNode.set("model", editorJsonNode);
        }

        return modelNode;
    }


    protected ModelRepresentation updateModel(Model model, MultiValueMap<String, String> values, boolean forceNewVersion) {
        String name = values.getFirst("name");
        String key = values.getFirst("key");
        String description = values.getFirst("description");
        String isNewVersionString = values.getFirst("newversion");
        String newVersionComment = null;
        ModelKeyRepresentation modelKeyInfo = this.modelService.validateModelKey(model, model.getModelType(), key);
        if (modelKeyInfo.isKeyAlreadyExists()) {
            throw new BadRequestException("Model with provided key already exists " + key);
        } else {
            boolean newVersion = false;
            if (forceNewVersion) {
                newVersion = true;
                newVersionComment = values.getFirst("comment");
            } else if (isNewVersionString != null) {
                newVersion = "true".equals(isNewVersionString);
                newVersionComment = values.getFirst("comment");
            }

            String json = values.getFirst("json_xml");

            try {
                model = this.modelService.saveModel(model.getId(), name, key, description, json, newVersion, newVersionComment, businessSystemDataService.getUser());
                return new ModelRepresentation(model);
            } catch (Exception var13) {
                LOGGER.error("Error saving model {}", model.getId(), var13);
                throw new BadRequestException("Process model could not be saved " + model.getId());
            }
        }
    }


}
