package com.sbtr.workflow.service.impl;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.constants.DriverClassName;
import com.sbtr.workflow.dto.CommentBean;
import com.sbtr.workflow.mapper.ActMyProcessCopyMapper;
import com.sbtr.workflow.model.ActMyModel;
import com.sbtr.workflow.model.ActMyNodeButton;
import com.sbtr.workflow.model.ActMyProcessCopy;
import com.sbtr.workflow.service.*;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.PageUtil.PageUtils;
import com.sbtr.workflow.vo.FlowNodeFieldVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("actMyProcessCopyServiceImpl")
public class ActMyProcessCopyServiceImpl extends WorkflowBaseServiceImpl<ActMyProcessCopy, String> implements ActMyProcessCopyService {
	@Autowired
	private ActMyProcessCopyMapper actMyProcessCopyMapper;
	@Resource
    private BusinessSystemDataService businessSystemDataService;
    @Value("${spring.datasource.dynamic.datasource.master.driver-class-name:null}")
    public String driverClassName;

    @Override
    public PageSet<ActMyProcessCopy> getPageSet(PageParam pageParam ,String formName,String userNameId) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<ActMyProcessCopy> list= actMyProcessCopyMapper.getPageSet(userNameId,"");
        PageInfo<ActMyProcessCopy> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

	@Override
	public int executeDeleteBatch(String[] uuids){
		return actMyProcessCopyMapper.executeDeleteBatch(uuids);
	}


    @Autowired
    private ActMyModelService flowModelService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private ActMyNodeFieldService flowNodeFieldService;

    @Autowired
    public HttpServletRequest request;

    @Autowired
    private ActMyNodeButtonService flowNodeButtonService;

    @Autowired
    private ApiFlowableProcessInstanceService apiFlowableProcessInstanceService;
    @Autowired
    private RuntimeService runtimeService;

    @Override
    public Object clickDetail(String taskId,String processInstanceId,String modelKey,String processDefinitionId,String nodeId) throws Exception {
        if (StringUtils.isBlank(modelKey) || StringUtils.isBlank(taskId) || StringUtils.isBlank(processInstanceId) || StringUtils.isBlank(nodeId)) {
           return failure("modelKey/taskId/processInstanceId/nodeId不能为空,请检查");
        }
        Example example = new Example(ActMyModel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modelKey);
        if (StringUtils.isNotBlank(processDefinitionId)) {
            criteria.andEqualTo("procdefId", processDefinitionId);
        }
        List<ActMyModel> lists = flowModelService.selectByExample(example);
        if (CollectionUtils.isEmpty(lists)) {
            return failure("流程与表单关联表查找不到数据,请检查");
        }
        HashMap<String, Object> map = new HashMap<>();
        if (StringUtils.isNotBlank(taskId)) {
            List<CommentBean> commentBeanList = workflowService.getCommentBeanByTaskId(taskId);
            map.put("commentBeanList", commentBeanList);
        }
        map.put("lists", lists.get(0));
        // 查询历史节点表 ACT_HI_ACTINST
        List<HistoricActivityInstance> historicActivityInstanceList = getHistoricActivityInstAsc(processInstanceId);
        //1 正在执行的节点
        List<String> runningActivitiIdList = new ArrayList<String>();
        //2 获取已流经的流程线
        List<String> highLightedFlowIds = new ArrayList<>();
        //3.已执行历史节点
        List<String> executedActivityIdList = new ArrayList<String>();
        historicActivityInstanceList.forEach(historicActivityInstance -> {
            //1
            if (null == historicActivityInstance.getEndTime()) {
                runningActivitiIdList.add(historicActivityInstance.getActivityId());
            }
            //2
            if (historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                highLightedFlowIds.add(historicActivityInstance.getActivityId());
            }
            //3
            if (null != historicActivityInstance.getEndTime() && !historicActivityInstance.getActivityType().equals("sequenceFlow")) {
                executedActivityIdList.add(historicActivityInstance.getActivityId());
            }

        });

        map.put("inProgress", runningActivitiIdList);
        highLightedFlowIds.addAll(executedActivityIdList);
        map.put("notInProgress", highLightedFlowIds);

        //4查询当前节点的按钮
        Example examples = new Example(ActMyNodeButton.class);
        Example.Criteria criterias = examples.createCriteria();
        criterias.andEqualTo("actDeModelKey", modelKey);
        criterias.andEqualTo("nodeId", nodeId);
        if (StringUtils.isNotBlank(processDefinitionId)) {
            criterias.andEqualTo("procdefId", processDefinitionId);
        }
        List<ActMyNodeButton> flowNodeButtons = flowNodeButtonService.selectByExample(examples);
        //  处理节点表单
        //for (int i = 0; i < flowNodeButtons.size(); i++) {
        //    flowNodeButtons.get(i).setNodeFormPath(flowNodeButtons.get(0).getNodeFormPath());
        //}
        map.put("flowNodeButtons", flowNodeButtons);
        //  以下为特殊处理 自己写的表单页面挂载流程需要返回业务详情数据  广州赛宝腾睿信息科技有限公司
        //2  通过任务对象获取流程实例
        ProcessInstance pi = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        //3 通过流程实例获取“业务键”
        String businessKey = pi.getBusinessKey();
        //表单获取详情数据的请求地址
//        String str = flowNodeButtons.get(0).getNodeFormEditPath();
//        JSONObject json_test = null;
//        if (StringUtils.isNotBlank(str)) {
//            String gateway = request.getHeader("Gateway");
//            String token = request.getHeader(ConstParamUtil.X_ACCESS_TOKEN);
//            //链式构建请求，带cookie请求
//            Map<String, Object> paramMap = new HashMap<>();
//            paramMap.put("uuid", businessKey);
//            String result2 = HttpRequest.post(gateway + str)
//                    .cookie("token=" + token)
//                    .form(paramMap)
//                    .timeout(20000)
//                    .execute().body();
//            json_test = JSONObject.parseObject(result2);
//        }
        String tablename = flowNodeButtons.get(0).getTablename();
        String primarykey = flowNodeButtons.get(0).getPrimarykey();
        JSONObject jsonObject = businessSystemDataService.getBusinessData(tablename,primarykey,businessKey,modelKey);
        map.put("businessData", jsonObject);
        //5 查询当前节点的字段属性
        if (StringUtils.isBlank(processDefinitionId)) {
            processDefinitionId = "";
        }
        List<FlowNodeFieldVo> flowNodeField = flowNodeFieldService.selectByModelKeyAndId(modelKey, nodeId, processDefinitionId);
        map.put("flowNodeField", flowNodeField);

        return map;


    }

    @Override
    public Integer deleteByProcessDefinitionId(String processDefinitionId) {
        return actMyProcessCopyMapper.deleteByProcessDefinitionId(processDefinitionId);
    }

    @Override
    public PageSet<ActMyProcessCopy> getMyPageSet(PageParam pageParam, String formName, String userNameId) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<ActMyProcessCopy> list= actMyProcessCopyMapper.getMyPageSet(userNameId,formName);
        PageInfo<ActMyProcessCopy> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public Integer updateReviewStatusByUuid(String uuid, String reviewStatus) {
        return actMyProcessCopyMapper.updateReviewStatusByUuid(uuid,reviewStatus);
    }

    /**
     * 通过流程实例ID获取流程中已经执行的节点，按照执行先后顺序排序
     *
     * @param procInstId
     * @return
     */
    public List<HistoricActivityInstance> getHistoricActivityInstAsc(String procInstId) {
        return historyService.createHistoricActivityInstanceQuery().processInstanceId(procInstId)
                .orderByHistoricActivityInstanceStartTime().asc().list();
    }
}
