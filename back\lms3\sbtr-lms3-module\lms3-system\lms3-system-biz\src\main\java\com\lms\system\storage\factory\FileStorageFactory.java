package com.lms.system.storage.factory;

import com.lms.common.enums.StorageTypeEnum;
import com.lms.system.storage.service.FileStorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 文件存储策略工厂
 * 负责创建和管理不同的文件存储策略实例
 */
@Slf4j
@Component
public class FileStorageFactory {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    /**
     * 策略实例缓存
     * Key: 存储类型代码, Value: 策略实例
     */
    private final Map<String, FileStorageService> strategyCache = new ConcurrentHashMap<>();
    
    /**
     * Bean名称映射
     * Key: 存储类型代码, Value: Spring Bean名称
     */
    private final Map<String, String> beanNameMapping = new ConcurrentHashMap<>();
    
    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        // 注册策略Bean名称映射
        beanNameMapping.put(StorageTypeEnum.OSS.getCode(), "ossStorageStrategy");
        beanNameMapping.put(StorageTypeEnum.MINIO.getCode(), "minioStorageStrategy");
        beanNameMapping.put(StorageTypeEnum.LOCAL.getCode(), "localStorageStrategy");
        
        // 预加载所有策略实例
        for (StorageTypeEnum storageType : StorageTypeEnum.values()) {
            try {
                FileStorageService strategy = createStrategy(storageType.getCode());
                if (strategy != null) {
                    strategyCache.put(storageType.getCode(), strategy);
                    log.info("成功加载存储策略: {} - {}", storageType.getCode(), storageType.getDescription());
                }
            } catch (Exception e) {
                log.warn("加载存储策略失败: {} - {}", storageType.getCode(), e.getMessage());
            }
        }
        
        log.info("文件存储策略工厂初始化完成，共加载 {} 个策略", strategyCache.size());
    }
    
    /**
     * 根据存储类型获取策略实例
     * 
     * @param storageType 存储类型代码
     * @return 文件存储策略实例
     * @throws IllegalArgumentException 不支持的存储类型
     */
    public FileStorageService getStrategy(String storageType) {
        if (storageType == null || storageType.trim().isEmpty()) {
            throw new IllegalArgumentException("存储类型不能为空");
        }
        
        String normalizedType = storageType.toLowerCase().trim();
        
        // 从缓存中获取策略实例
        FileStorageService strategy = strategyCache.get(normalizedType);
        if (strategy != null) {
            return strategy;
        }
        
        // 缓存中没有，尝试创建新实例
        strategy = createStrategy(normalizedType);
        if (strategy != null) {
            strategyCache.put(normalizedType, strategy);
            log.info("动态创建存储策略: {}", normalizedType);
            return strategy;
        }
        
        throw new IllegalArgumentException("不支持的存储类型: " + storageType);
    }
    
    /**
     * 根据存储类型枚举获取策略实例
     * 
     * @param storageTypeEnum 存储类型枚举
     * @return 文件存储策略实例
     */
    public FileStorageService getStrategy(StorageTypeEnum storageTypeEnum) {
        return getStrategy(storageTypeEnum.getCode());
    }
    
    /**
     * 检查是否支持指定的存储类型
     * 
     * @param storageType 存储类型代码
     * @return 是否支持
     */
    public boolean isSupported(String storageType) {
        if (storageType == null || storageType.trim().isEmpty()) {
            return false;
        }
        
        String normalizedType = storageType.toLowerCase().trim();
        return StorageTypeEnum.isValid(normalizedType) && 
               (strategyCache.containsKey(normalizedType) || beanNameMapping.containsKey(normalizedType));
    }
    
    /**
     * 获取所有支持的存储类型
     * 
     * @return 支持的存储类型数组
     */
    public StorageTypeEnum[] getSupportedTypes() {
        return StorageTypeEnum.values();
    }
    
    /**
     * 获取已加载的策略数量
     * 
     * @return 策略数量
     */
    public int getLoadedStrategyCount() {
        return strategyCache.size();
    }
    
    /**
     * 清除策略缓存
     */
    public void clearCache() {
        strategyCache.clear();
        log.info("文件存储策略缓存已清除");
    }
    
    /**
     * 重新加载指定策略
     * 
     * @param storageType 存储类型代码
     * @return 是否重新加载成功
     */
    public boolean reloadStrategy(String storageType) {
        if (storageType == null || storageType.trim().isEmpty()) {
            return false;
        }
        
        String normalizedType = storageType.toLowerCase().trim();
        
        try {
            // 移除旧的策略实例
            strategyCache.remove(normalizedType);
            
            // 创建新的策略实例
            FileStorageService strategy = createStrategy(normalizedType);
            if (strategy != null) {
                strategyCache.put(normalizedType, strategy);
                log.info("成功重新加载存储策略: {}", normalizedType);
                return true;
            }
        } catch (Exception e) {
            log.error("重新加载存储策略失败: {} - {}", normalizedType, e.getMessage());
        }
        
        return false;
    }
    
    /**
     * 创建策略实例
     * 
     * @param storageType 存储类型代码
     * @return 策略实例，如果创建失败返回null
     */
    private FileStorageService createStrategy(String storageType) {
        try {
            // 验证存储类型是否有效
            if (!StorageTypeEnum.isValid(storageType)) {
                log.warn("无效的存储类型: {}", storageType);
                return null;
            }
            
            // 获取对应的Bean名称
            String beanName = beanNameMapping.get(storageType);
            if (beanName == null) {
                log.warn("未找到存储类型对应的Bean名称: {}", storageType);
                return null;
            }
            
            // 从Spring容器中获取Bean实例
            if (applicationContext.containsBean(beanName)) {
                FileStorageService strategy = applicationContext.getBean(beanName, FileStorageService.class);
                log.debug("成功创建存储策略实例: {} -> {}", storageType, beanName);
                return strategy;
            } else {
                log.warn("Spring容器中未找到Bean: {}", beanName);
                return null;
            }
            
        } catch (Exception e) {
            log.error("创建存储策略实例失败: {} - {}", storageType, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 获取策略实例的详细信息
     * 
     * @param storageType 存储类型代码
     * @return 策略信息字符串
     */
    public String getStrategyInfo(String storageType) {
        try {
            FileStorageService strategy = getStrategy(storageType);
            return String.format("存储类型: %s, 描述: %s, 实现类: %s", 
                strategy.getStorageType(), 
                strategy.getStorageDescription(), 
                strategy.getClass().getSimpleName());
        } catch (Exception e) {
            return "获取策略信息失败: " + e.getMessage();
        }
    }
}
