
package com.sbtr.workflow.utils;

/**
 * 封装返回工具类
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
public class Result<R> {


    /**
     * 状态码 默认 200 成功  300 失败
     */
    private Integer statusCode;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private R data;

    /**
     * 响应成功 并且携带数据返回
     * 默认 message success
     * 默认 statuscode 200
     *
     * @param data
     * @param <R>
     * @return
     */
    public static <R> Result<R> ofSuccess(R data) {
        return new Result<R>()
                .setMessage("success")
                .setStatusCode(200)
                .setData(data);
    }


    /**
     * 响应成功 并且返回响应成功提示
     * 比如用在 保存成功
     *
     * @param msg
     * @param <R>
     * @return
     */
    public static <R> Result<R> ofSuccessMsg(String msg) {
        return new Result<R>()
                .setStatusCode(200)
                .setMessage(msg);
    }

    public static <R> Result<R> ofFailMsg(String msg) {
        return new Result<R>()
                .setStatusCode(300)
                .setMessage(msg);
    }

    public static <R> Result<R> ofFail(Integer statusCode, String msg) {
        Result<R> result = new Result<>();
        result.setStatusCode(statusCode);
        result.setMessage(msg);
        return result;
    }

    public static <R> Result<R> ofThrowable(Integer statusCode, Throwable throwable) {
        Result<R> result = new Result<>();
        result.setStatusCode(statusCode);
        result.setMessage(throwable.getClass().getName() + ", " + throwable.getMessage());
        return result;
    }


    public Integer getStatusCode() {
        return statusCode;
    }

    public Result<R> setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public Result<R> setMessage(String message) {
        this.message = message;
        return this;
    }

    public R getData() {
        return data;
    }

    public Result<R> setData(R data) {
        this.data = data;
        return this;
    }

    @Override
    public String toString() {
        return "Result{" +
                ", statusCode=" + statusCode +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
