package com.lms.examine.feign.model;

import com.lms.common.model.BaseModel;

import java.util.List;

public class ExportDataViewModel extends BaseModel {
	/**
	 *
	 */
	private static final long serialVersionUID = -3616943288084994218L;
	private Examinesubjecttype examinesubjecttype;
	private List<ExaminesubjectViewModel> items;
	public Examinesubjecttype getExaminesubjecttype() {
		return examinesubjecttype;
	}
	public void setExaminesubjecttype(Examinesubjecttype examinesubjecttype) {
		this.examinesubjecttype = examinesubjecttype;
	}
	public List<ExaminesubjectViewModel> getItems() {
		return items;
	}
	public void setItems(List<ExaminesubjectViewModel> items) {
		this.items = items;
	}


}
