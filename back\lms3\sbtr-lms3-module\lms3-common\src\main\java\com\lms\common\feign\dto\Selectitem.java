package com.lms.common.feign.dto;

import com.lms.common.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * BSelectitemId entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Selectitem extends BaseModel {

    private String id;

    private String objname;

    private String code;

    private String objdesc;

    private String pid;

    private String typeid;

    private int seqno;

    private int status;

    private String parentname;

    private int issystemlevel;

    public static Selectitem getEmptyItem() {
        Selectitem item = new Selectitem();
        item.setId("");
        item.setObjname("");
        return item;
    }

}
