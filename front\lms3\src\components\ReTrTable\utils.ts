import type { TableConfig, DisplayColumn } from "./types";
import dayjs from "dayjs";

// 状态映射常量
const STATUS_MAPPINGS = {
  positive: [
    "true",
    "1",
    "active",
    "enabled",
    "success",
    "normal",
    "启用",
    "正常",
    "成功"
  ],
  negative: [
    "false",
    "0",
    "inactive",
    "disabled",
    "error",
    "locked",
    "禁用",
    "锁定",
    "失败"
  ],
  warning: ["warning", "pending", "processing", "警告", "待处理", "处理中"],
  processing: ["processing", "running", "处理中", "运行中"]
};

// 将displayColumns转换为a-table的columns格式
export function convertDisplayColumnsToColumns(
  displayColumns: DisplayColumn[]
): any[] {
  return displayColumns.map(col => {
    const column: any = {
      title: col.label,
      dataIndex: col.prop,
      key: col.prop,
      width: col.width,
      align: col.align || "left",
      fixed: col.fixed,
      resizable: col.resizable !== false,
      sorter: col.sortable,
      className: col.classname,
      titleClassName: col.labelclassname,
      minWidth: col.minWidth,
      ellipsis: col.showtooltip,
      customRender: col.formatter
        ? ({ text, record, index }: any) => {
            return col.formatter!(record, column, text);
          }
        : undefined
    };

    // 透传自定义渲染相关属性
    if (col.render) column.render = col.render;
    if (col.renderType) column.renderType = col.renderType;
    if (col.statusMap) column.statusMap = col.statusMap;
    if (col.tagColor) column.tagColor = col.tagColor;

    // 新增：处理自定义列插槽
    if (col.scopedSlots && col.scopedSlots.customRender) {
      column.scopedSlots = {
        customRender: col.scopedSlots.customRender
      };
    }

    // 处理子列
    if (col.children && col.children.length > 0) {
      column.children = convertDisplayColumnsToColumns(col.children);
    }

    return column;
  });
}

// 生成操作列配置
export function generateOperationColumn(
  showOperateColumn: boolean,
  operateColumnWidth: number,
  operateColumnLabel: string
): any[] {
  if (!showOperateColumn) return [];

  return [
    {
      title: operateColumnLabel,
      key: "operation",
      dataIndex: "operation",
      width: operateColumnWidth,
      fixed: "right",
      align: "center",
      customRender: ({ record }: any) => {
        // 这里会通过插槽渲染操作按钮
        return null;
      }
    }
  ];
}

// 生成序号列配置
export function generateIndexColumn(): any[] {
  return [
    {
      title: "序号",
      key: "index",
      dataIndex: "index",
      width: 60,
      fixed: "left",
      align: "center",
      customRender: ({ index }: any) => {
        return index + 1;
      }
    }
  ];
}

// 时间格式化
export function formatTime(val: any): string {
  return val ? dayjs(val).format("YYYY-MM-DD HH:mm:ss") : "";
}

// 获取状态映射
function getStatusMapping(value: any): string {
  if (typeof value === "boolean") return value ? "positive" : "negative";
  if (typeof value === "number") return value === 1 ? "positive" : "negative";
  if (typeof value === "string") {
    const lowerValue = value.toLowerCase();
    for (const [key, values] of Object.entries(STATUS_MAPPINGS)) {
      if (values.includes(lowerValue)) return key;
    }
  }
  return "default";
}

// 状态颜色常量，与TrTable保持一致
export const CONSTANTS = {
  STATUS_COLORS: {
    success: "green",
    error: "red",
    warning: "orange",
    processing: "blue",
    default: "default"
  }
};

// 获取状态颜色
export function getStatusColor(
  value: any,
  statusMap?: Record<string, { status: string; text: string; color?: string }>
): string {
  if (statusMap?.[value]) {
    const statusItem = statusMap[value];
    // 优先使用直接指定的颜色值
    if (statusItem.color) {
      return statusItem.color;
    }
    // 如果没有直接指定颜色，则使用status映射
    const status = statusItem.status;
    return (
      CONSTANTS.STATUS_COLORS[status as keyof typeof CONSTANTS.STATUS_COLORS] ||
      "default"
    );
  }

  const mapping = getStatusMapping(value);
  const colorMap = {
    positive: "green",
    negative: "red",
    warning: "orange",
    processing: "blue",
    default: "default"
  };
  return colorMap[mapping as keyof typeof colorMap];
}

// 获取状态文本
export function getStatusText(
  value: any,
  statusMap?: Record<string, { status: string; text: string; color?: string }>
): string {
  if (statusMap?.[value]) return statusMap[value].text;

  const mapping = getStatusMapping(value);
  const textMap = {
    positive: "正常",
    negative: "锁定",
    warning: "警告",
    processing: "处理中",
    default: String(value)
  };
  return textMap[mapping as keyof typeof textMap];
}

// 获取组件默认属性（移植自TrTable）
export function getDefaultComponentProps(
  component: string,
  label: string,
  userProps?: Record<string, any>
): Record<string, any> {
  const defaultProps: Record<string, any> = {};

  if (component === "a-input") {
    defaultProps.allowClear = true;
    defaultProps.placeholder = `请输入${label}`;
  } else if (component === "a-select") {
    defaultProps.allowClear = true;
    defaultProps.placeholder = `请选择${label}`;
  }

  return { ...defaultProps, ...userProps };
}

// 重置查询表单（移植自TrTable）
export function resetQueryForm(queryForm: Record<string, any>): void {
  Object.keys(queryForm).forEach(key => {
    queryForm[key] = undefined;
  });
}
