package com.sbtr.workflow.service;

import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.model.ActMyButton;

/**
 * 流程按钮表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-23 08:10:59
 */
public interface ActMyButtonService extends WorkflowBaseService<ActMyButton, String> {

    /**
     * 分页
     *
     * @return
     * @Param pageParam 分页参数
     * @Param buttonCode 按钮标识
     * @Param buttonName 按钮名称
     */
    PageSet<ActMyButton> getPageSet(PageParam pageParam, String buttonCode,String buttonName);

    /**
     * 批量删除
     *
     * @return
     * @Param uuid 主键
     */
	int executeDeleteBatch(String[] uuids);

    /**
     * 批量删除
     *
     * @return
     * @Param uuid 主键
     */
    Boolean getListByCodeAndUuid(String buttonCode, String uuid);
}
