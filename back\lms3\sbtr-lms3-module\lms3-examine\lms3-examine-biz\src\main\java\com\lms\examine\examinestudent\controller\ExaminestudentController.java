/**
 * FileName:ExaminestudentController.java
 * Author:ji<PERSON><PERSON><PERSON>
 * Time  :2013-3-7下午4:18:10
 * CopyRight: SBTR LTD.
 * decription:to-do
 */
package com.lms.examine.examinestudent.controller;

import java.sql.SQLException;
import java.util.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.alibaba.fastjson.JSONObject;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.feign.dto.Person;
import com.lms.common.model.Result;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.DateHelper;
import com.lms.common.util.StringHelper;
import com.lms.examine.examinepage.service.ExaminePageService;
import com.lms.examine.examinestudent.service.ExaminestudentService;
import com.lms.examine.examinestudent.service.VUExaminestudentService;
import com.lms.examine.examinestudentrecord.service.ExaminestudentrecordService;
import com.lms.examine.examinesubject.service.ExaminesubjectService;
import com.lms.examine.feign.model.Examinepage;
import com.lms.examine.feign.model.Examinestudent;
import com.lms.examine.feign.model.VUExaminestudent;
import com.lms.examine.feign.model.Examinestudentrecord;
import com.lms.examine.feign.model.Examinesubject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> 试题信息操作控制层
 */
@RestController
@RequestMapping("/examinestudent")
public class ExaminestudentController extends BaseController<Examinestudent> {
    @Resource
    private ExaminestudentService examinestudentService;
    @Resource
    private VUExaminestudentService vuexaminestudentService;
    @Resource
    private ExaminestudentrecordService examinestudentrecordService;
    @Resource
    private ExaminesubjectService examinesubjectService;
    @Resource
    private ExaminePageService examinepageService;

    @PostMapping(value = {"/listpage"})
    public Result getExaminestudentList(@RequestBody JSONObject request) {
        PageInfo pageInfo = super.getPageInfo(request);
        Page<Examinestudent> examinestudents = examinestudentService
                .listByCondition(pageInfo);
        for (Examinestudent examinestudent : examinestudents.getRecords()) {
            String examineid = StringHelper.null2String(examinestudent
                    .getExamineid());
            Examinepage examinepage = examinepageService.getById(examineid);
            String examinename = StringHelper
                    .null2String(examinepage.getName());
            examinestudent.setExaminename(examinename);
        }
        return Result.OK(examinestudents);
    }

    @PostMapping(value = {"/listview"})
    public Result getVUExaminestudentList(@RequestBody JSONObject request) {
        // 设置分页参数,查询参数
        // super.setParameter(request);
        PageInfo pageInfo = super.getPageInfo(request);
        LinkedHashMap orderMap = new LinkedHashMap();
        if (pageInfo.getOrderName() != null && pageInfo.getSort() != null) {
            orderMap.put(pageInfo.getOrderName(), pageInfo.getSort());
        }
        orderMap.put("begintime", PageInfo.DESC);
        pageInfo.setOrderMap(orderMap);
        List<Examinestudent> examinestudents = examinestudentService.getValidExaminestudents();
        String ispersonal = StringHelper.null2String(request.getString("ispersonal"));
        for (int i = 0; i < examinestudents.size(); i++) {
            Examinestudent examinestudent = (Examinestudent) examinestudents
                    .get(i);
            String endtime = StringHelper.null2String(examinestudent
                    .getEndtime());
            String examstatus = StringHelper.null2String(examinestudent
                    .getExaminestatus());
            if (examstatus.equals("3")) {
                String nowtime = DateHelper.getCurDateTime();
                boolean isover = DateHelper.compare(endtime, nowtime);
                if (!endtime.isEmpty() && isover) {
                    examinestudent.setExaminestatus("4");
                    examinestudent.setSubmittime(endtime);
                    examinestudentService.saveOrUpdate(examinestudent);
                }
            }
        }
        Page<VUExaminestudent> vuexaminestudents = vuexaminestudentService
                .listByCondition(pageInfo);

        for (VUExaminestudent view : vuexaminestudents.getRecords()) {
            String examined = StringHelper.null2String(view.getPid());
            String personid = StringHelper.null2String(view.getPersonid());
            if (view.getStatus() >= 3) {//考试结束后再显示排名
                List<Map<String, Object>> rankList = examinestudentService.getScoreRank(examined, personid);
                if (rankList.size() > 0) {
                    Map<String, Object> map = rankList.get(0);
                    view.setScore_rank(Integer.parseInt(Objects.toString(map.get("score_rank"))));
                }
            }
            if (ispersonal.equals("1")) {
                String publishtype = StringHelper.null2String(view.getPublishtype());
                String publishdate = StringHelper.null2String(view.getPublishdate());
                if (publishtype.equals("4028828a81db4f0d0181db5855270002")) {//不发布
                    view.setScore(null);
                    view.setResult(null);
                } else if (publishtype.equals("4028828a81db4f0d0181db5887220004")) {//按时间发布
                    if (publishdate.isEmpty()) {
                        view.setScore(null);
                        view.setResult(null);
                    }
                    if (DateHelper.compare(DateHelper.getCurDateTime(), publishdate)) {
                        view.setScore(null);
                        view.setResult(null);
                    }
                } else if (publishtype.equals("4028828a81db4f0d0181db583c930000")) {//结束发布
                    if (view.getStatus() < 3) {//判断是否考试结束
                        view.setScore(null);
                        view.setResult(null);
                    }
                }
            }

        }
        examinestudentService.setAttachByEsts(vuexaminestudents.getRecords());
        return Result.OK(vuexaminestudents);
    }

    private int getObjectValue(Object value) {
        System.out.println(value.getClass().getName());
        int result = 0;
        if (value instanceof Integer) {
            result = ((Integer) value).intValue();
        } else if (value instanceof Byte) {
            result = ((Byte) value).intValue();
        } else if (value instanceof Double) {
            result = ((Double) value).intValue();
        }
        return result;
    }

    /*
     * 根据ID获取试题信息
     */
    @RequestMapping(value = {"/getInfo/{id}"}, method = RequestMethod.GET)
    public Result getExaminestudentInfo(@PathVariable("id") String pid) {
        Examinestudent examinestudent = this.examinestudentService.getById(pid);
        return Result.OK(examinestudent);
    }

    /*
     * 根据ID获取试题信息
     */
    @RequestMapping(value = {"/getInfoView/{id}"}, method = RequestMethod.GET)
    public Result getExaminestudentInfoView(@PathVariable("id") String pid) {
        VUExaminestudent vuexaminestudent = this.vuexaminestudentService.getById(pid);
        return Result.OK(vuexaminestudent);
    }

    // 更新Action
    @LMSLog(desc = "学员考试情况变更", otype = LogType.Update, order = 1, method =
            "createExaminestudentLog")
    @RequestMapping(value = {"/modify/{edittype}"}, method = RequestMethod.POST)
    public Result updateExaminestudent(@RequestBody Examinestudent examinestudent,
									   @PathVariable("edittype") String edittype) throws SQLException {
        Examinestudent es = examinestudentService.getById(examinestudent.getId());
        String examineid = es.getExamineid();
        Examinepage Exampage = examinepageService.getById(examineid);
        String modal = Exampage.getModal();
        String etype = Exampage.getType();
        int titledisturb = Exampage.getTitledisturb();
        int answerdisturb = Exampage.getAnswerdisturb();
        es.setExaminestatus(examinestudent.getExaminestatus());
        if (edittype.equals("0")) {// 修改数据
            Double score = examinestudent.getScore();
            if (!StringHelper.null2String(score).isEmpty()) {
                String result = examinepageService.getScoreResult(examineid, score);
                // examinestudent.setExaminestatus("5");
                examinestudent.setScore(score);
                examinestudent.setResult(result);
            }
            examinestudentService.saveOrUpdate(examinestudent);
        } else if (edittype.equals("1")) { // 接收试卷
            Map<String, List> subjectMap = new HashMap();
            String checkMsg = examinestudentService
                    .chekcSubjectAmountByExamine(Exampage, 1, subjectMap);
            boolean noSubject = modal
                    .equals("12058eacd08a454fb12ebbf19a5df651");
            if (checkMsg.indexOf("createSuccess") < 0 && noSubject) {
                return Result.error(checkMsg);
            }
            es.setReceivetime(DateHelper.getCurDateTime());
            examinestudentService.saveOrUpdate(es);
            if (modal.equals("bfd41a28350847019428224e7785eb82")) {// 固定试题
                List<Examinesubject> examinesubjects = examinesubjectService.getExaminesubjectById(examineid, titledisturb);
                int seqno = 1;
                String type = "";
                for (Examinesubject examinesubject : examinesubjects) {
                    // Object obj = examinesubjects.get(i);
                    Examinestudentrecord record = new Examinestudentrecord();
                    String thistype = StringHelper.null2String(examinesubject.getType());
                    if (type.isEmpty()) {
                        type = thistype;
                    } else if (type.equals(thistype)) {
                        seqno++;
                    } else {
                        seqno = 1;
                        type = thistype;
                    }
                    record.setExamineid(examineid);
                    record.setExaminesubjectid(StringHelper
                            .null2String(examinesubject.getSubjectid()));
                    String content = StringHelper.null2String(examinesubject.getContent());
                    String correctresponse = StringHelper.null2String(examinesubject.getCorrectresponse());
                    if (answerdisturb == 1
                            && (type.equals("9e47efc0ce894454856a80171e1e6efe") || type
                            .equals("3ef580ec56ae436eb79b91b25d1a078e"))) {
                        // 打乱试题答案顺序
                        content = content.replace("<single><item>", "");
                        content = content.replace("</item></single>", "");
                        content = content.replaceAll("</item><item>", ",");
                        String[] arr = content.split(",");
                        String[] answers = correctresponse.split(",");
                        String newanswers = "";
                        Map map = new HashMap();
                        for (int i = 0; i < answers.length; i++) {
                            String letter = answers[i];
                            if (letter.equals("A") && arr.length > 0) {
                                map.put("A", arr[0]);
                            } else if (letter.equals("B") && arr.length > 1) {
                                map.put("B", arr[1]);
                            } else if (letter.equals("C") && arr.length > 2) {
                                map.put("C", arr[2]);
                            } else if (letter.equals("D") && arr.length > 3) {
                                map.put("D", arr[3]);
                            } else if (letter.equals("E") && arr.length > 4) {
                                map.put("E", arr[4]);
                            } else if (letter.equals("F") && arr.length > 5) {
                                map.put("F", arr[5]);
                            } else if (letter.equals("G") && arr.length > 6) {
                                map.put("G", arr[6]);
                            }
                        }

                        if (arr.length > 0) {
                            Random random = new Random();
                            StringBuffer newContent = new StringBuffer();
                            newContent.append("<single>");
                            for (int j = arr.length; j > 0; j--) {
                                int current = random.nextInt(j);
                                String temp = arr[current];
                                arr[current] = arr[j - 1];
                                arr[j - 1] = temp;
                            }
                            for (int m = 0; m < arr.length; m++) {
                                newContent.append("<item>");
                                newContent.append(arr[m]);
                                newContent.append("</item>");
                                if (map.containsValue(arr[m])) {
                                    switch (m) {
                                        case 0:
                                            newanswers = newanswers.isEmpty() ? "A"
                                                    : newanswers + ",A";
                                            break;
                                        case 1:
                                            newanswers = newanswers.isEmpty() ? "B"
                                                    : newanswers + ",B";
                                            break;
                                        case 2:
                                            newanswers = newanswers.isEmpty() ? "C"
                                                    : newanswers + ",C";
                                            break;
                                        case 3:
                                            newanswers = newanswers.isEmpty() ? "D"
                                                    : newanswers + ",D";
                                            break;
                                        case 4:
                                            newanswers = newanswers.isEmpty() ? "E"
                                                    : newanswers + ",E";
                                            break;
                                        case 5:
                                            newanswers = newanswers.isEmpty() ? "F"
                                                    : newanswers + ",F";
                                            break;
                                        case 6:
                                            newanswers = newanswers.isEmpty() ? "G"
                                                    : newanswers + ",G";
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            }
                            newContent.append("</single>");
                            content = newContent.toString();
                            correctresponse = newanswers;
                        }

                    }
                    record.setContent(content);
                    record.setCorrectresponse(correctresponse);
                    record.setExaminetype(0);
                    record.setPersonid(es.getPersonid());
                    record.setSeqno(seqno);
                    record.setTitle(StringHelper.null2String(examinesubject.getTitle()));
                    record.setType(thistype);
                    record.setStudentid(es.getId());
                    examinestudentrecordService.saveOrUpdate(record);
                }
            } else { // 随机试题
                examinestudentService.createStudentRecordByExamine(es,
                        examineid, subjectMap);
            }
        } else if (edittype.equals("2")) {// 开始考试
            String examway = StringHelper.null2String(Exampage.getWay());// 考核方式：、固定时间长度
            int examtime = Exampage.getExaminetime();
            String begintime = StringHelper.null2String(es.getBegintime());
            if (examway.equals("8363606ac3ac4f89a3e002ca093fec92")) {// 固定时间点
                String endtime = DateHelper.dayMoveDateTime(begintime, 0, 0, 0,
                        0, examtime, 0);
                Long betweentime = DateHelper.getMinutesBetween(begintime
                        .replace(" ", "/"), DateHelper.getCurDateTime()
                        .replace(" ", "/"));
                Long betweentime2 = DateHelper.getMinutesBetween(endtime
                        .replace(" ", "/"), DateHelper.getCurDateTime()
                        .replace(" ", "/"));
                if (betweentime2 <= 0) {
                    es.setExaminestatus("4");
                    examinestudentService.saveOrUpdate(es);
                } else {
                    if (StringHelper.null2String(es.getEndtime()).isEmpty()) {
                        es.setEndtime(endtime);
                    }
                    examinestudentService.saveOrUpdate(es);
                }
            } else if (examway.equals("3186bca1c5c6499baecf31256305a97c")) {// 固定时间长度
                String newbegin = DateHelper.getCurDateTime();
                String endtime = DateHelper.dayMoveDateTime(newbegin, 0, 0, 0,
                        0, examtime, 0);
                String examineendtime = StringHelper.null2String(es
                        .getExaminedtime());// 考试截止时间
                Long betweentime = DateHelper.getMinutesBetween(
                        newbegin.replace(" ", "/"),
                        examineendtime.replace(" ", "/"));
                Long betweentime2 = DateHelper.getMinutesBetween(
                        endtime.replace(" ", "/"),
                        examineendtime.replace(" ", "/"));
                if (!examineendtime.isEmpty() && betweentime >= 0) {
                    es.setExaminestatus("4");
                    examinestudentService.saveOrUpdate(es);
                } else {
                    if (!examineendtime.isEmpty() && betweentime2 > 0)
                        endtime = examineendtime;
                    es.setBegintime(newbegin);
                    es.setEndtime(endtime);
                    examinestudentService.saveOrUpdate(es);
                }
            }
        }
        return Result.OK(es);
    }


    // 批量删除
    @LMSLog(desc = "删除试卷学员", otype = LogType.Delete, order = 1, method = "createExaminestudentLog")
    @RequestMapping(value = {"/batchremove"}, method = RequestMethod.DELETE)
    public Result batchDeleteExaminestudent(@RequestParam("ids") String ids) {
        String[] idarray = ids.split(",");
        List<String> idList = new ArrayList<String>();
        for (int i = 0; i < idarray.length; i++) {
            String id = idarray[i];
            idList.add(id);
        }
        this.examinestudentService.removeBatchByIds(idList);
        return Result.OK();
    }

    /*
     * 添加试卷的学员信息
     */
    @LMSLog(desc = "新增试卷学员", otype = LogType.Save, order = 1, method =
            "createExaminestudentLog")
    @RequestMapping(value = {"/add"}, method = RequestMethod.POST)
    public Result saveExaminestudent(
            @RequestBody Examinestudent[] examinestudents) {
        // 所有新增的用户都是普通用户
        String existIds = "";
        if (examinestudents.length > 0) {
            List<Examinestudent> es = examinestudentService
                    .getExaminestudentsByExamId(StringHelper
                            .null2String(examinestudents[0].getExamineid()));
            for (Examinestudent e : es) {
                String personid = StringHelper.null2String(e.getPersonid());
                existIds = existIds.isEmpty() ? personid : existIds + ","
                        + personid;
            }
        }
        for (int i = 0; i < examinestudents.length; i++) {
            Examinestudent examinestudent = examinestudents[i];
            String pid = examinestudent.getPersonid();
            if (existIds.indexOf(pid) > -1) {
                continue;
            }
            Examinepage examinepage = examinepageService.getById(StringHelper
                    .null2String(examinestudent.getExamineid()));
            examinestudent.setMlevel(examinepage.getMlevel());
            examinestudent.setExaminedtime(StringHelper.null2String(examinepage
                    .getExamineendtime()));// 考试截止时间
            String begintime = StringHelper.null2String(examinepage
                    .getExaminedate());// 考试开始时间
            int examtime = examinepage.getExaminetime();// 考试时长
            if (!begintime.isEmpty()) {// 根据考试时长和考试开始时间，计算考试结束时间
                examinestudent.setBegintime(begintime);
                String endtime = DateHelper.dayMoveDateTime(begintime, 0, 0, 0,
                        0, examtime, 0);
                examinestudent.setEndtime(endtime);
                if (examinepage.getWay().equals(
                        "8363606ac3ac4f89a3e002ca093fec92")) {
                    examinestudent.setExaminedtime(endtime);
                }
            }
            if (!(StringHelper.null2String(examinepage.getType()).equals(
                    "4028828a6005533d0160055343e90000") ||
                    StringHelper.null2String(examinepage.getType()).equals(
                            "4028828a6005818101600581778ce000"))) {// 在线考试 vr
                examinestudent.setExaminestatus("4");
            }
            this.examinestudentService.saveOrUpdate(examinestudent);
        }
        return Result.OK(examinestudents);
    }

    /*
     * 下发试卷，对该试卷的所有学员的考卷状态更新为已下发
     */
    @LMSLog(desc = "下发试卷", otype = LogType.Update, order = 2, method =
            "createExaminestudentLog")
    @RequestMapping(value = {"/batchrelease"}, method = RequestMethod.DELETE)
    public Result batchReleaseExaminepage(@RequestParam("ids") String ids) {

        String[] idarray = ids.split(",");
        List<String> idList = new ArrayList<String>();
        for (int i = 0; i < idarray.length; i++) {
            String id = idarray[i];
            idList.add(id);
        }
        return this.examinestudentService.batchReleaseExaminepage(idList);
    }

    @PostMapping(value = {"/getStudentsByPerson"})
    public Result getStudentsByPerson(@RequestBody JSONObject request) {
        // List<Examinestudent> examinestudents =
        // this.examinestudentService.listByCondition();
        // super.setParameter(request);
        PageInfo pageInfo = super.getPageInfo(request);
        Page<Examinestudent> examinestudents = examinestudentService
                .listByCondition(pageInfo);
        List<Examinestudent> newes = new ArrayList<Examinestudent>();
        for (Examinestudent examinestudent : examinestudents.getRecords()) {
            String examineid = StringHelper.null2String(examinestudent
                    .getExamineid());
            Examinepage examinepage = examinepageService.getById(examineid);

            String examinename = StringHelper.null2String(examinepage
                    .getName());
            String publishtype = StringHelper.null2String(examinepage.getPublishtype());
            String publishdate = StringHelper.null2String(examinepage.getPublishdate());
            String examinetype = StringHelper.null2String(examinepage
                    .getExaminetype());
            if (examinetype.equals("1")) {
                examinestudent.setExaminename(examinename);
                if (publishtype.equals("4028828a81db4f0d0181db5855270002")) {//不发布
                    continue;
                } else if (publishtype.equals("4028828a81db4f0d0181db5887220004")) {//按时间发布
                    if (publishdate.isEmpty()) {
                        continue;
                    }
                    if (DateHelper.compare(DateHelper.getCurDateTime(), publishdate)) {
                        continue;
                    }
                } else if (publishtype.equals("4028828a81db4f0d0181db583c930000")) {//结束发布
                    if (examinepage.getStatus() < 3) {//判断是否考试结束
                        continue;
                    }
                }
                newes.add(examinestudent);
            }
        }
        return Result.OK(createPage(newes, examinestudents));
    }

    @PostMapping(value = "/getdayexaming")
    public Result getdayteaching(@RequestBody JSONObject request) {
        PageInfo pageInfo = super.getPageInfo(request);
        List<Object[]> planStudentList = vuexaminestudentService.getDayExamine(ContextUtil.getPersonId());
        return Result.OK(planStudentList);
    }

    @LMSLog(desc = "登记学员考试得分", otype = LogType.Update, order = 3, method =
            "createExaminestudentLog")
    @RequestMapping(value = {"/batchedit"}, method = RequestMethod.POST)
    public Result batchmodifyExaminestudent(@RequestBody Examinestudent[] examinestudents) {
        for (int i = 0; i < examinestudents.length; i++) {
            Examinestudent examinestudent = examinestudentService.getById(examinestudents[i].getId());
            examinestudent.setScore(examinestudents[i].getScore());
            examinestudent.setAttachid(examinestudents[i].getAttachid());
            String result = examinepageService.getScoreResult(examinestudents[i].getExamineid(),
                    examinestudents[i].getScore());
            // examinestudent.setExaminestatus("5");
            examinestudent.setResult(result);
            examinestudentService.saveOrUpdate(examinestudent);
        }
        return Result.OK(examinestudents);
    }


    @RequestMapping(value = {"/getStudentExamineStatistics"}, method = RequestMethod.GET)
    public Result getStudentExamineStatistics(@RequestParam("personId") String personId) {
        Map<String, Object> result = examinestudentService.getStudentExamineStatistics(personId);
        return Result.OK(result);
    }


    public String createExaminestudentLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        String a = commonSystemApi.translateContent("试卷");
        String b = commonSystemApi.translateContent("学员");
        if (lmslog.otype().equals(LogType.Save)) { //新增数据日志
            Examinepage ep = new Examinepage();
            for (Examinestudent e : (Examinestudent[]) (args[0])) {
                Person ps = commonBaseApi.getPersonById(e.getPersonid()).getResult();
                ep = examinepageService.getById(e.getExamineid());
                objname = objname.isEmpty() ? StringHelper.null2String(ps.getName()) : objname + "," + StringHelper.null2String(ps.getName());
            }
            objname = a + "(" + StringHelper.null2String(ep.getName()) + ")-" + b + "(" + objname + ")";
        } else if (lmslog.otype().equals(LogType.Update)) { //编辑数据日志

            switch (lmslog.order()) {
                case 1:
                    Examinestudent es = (Examinestudent) (args[0]);
                    es = examinestudentService.getById(es.getId());
                    String edittype = (String) (args[1]);
                    Person ps = commonBaseApi.getPersonById(es.getPersonid()).getResult();
                    Examinepage ep = examinepageService.getById(es.getExamineid());
                    if (edittype.equals("0")) {
                        objname = commonSystemApi.translateContent("修改信息-试卷") + "(" + StringHelper.null2String(ep.getName()) + ")-" + b + "(" + StringHelper.null2String(ps.getName()) + ")";
                    } else if (edittype.equals("1")) {
                        objname = commonSystemApi.translateContent("接收试卷-试卷") + "(" + StringHelper.null2String(ep.getName()) + ")-" + b + "(" + StringHelper.null2String(ps.getName()) + ")";
                    } else if (edittype.equals("2")) {
                        objname = commonSystemApi.translateContent("开始考试-试卷") + "(" + StringHelper.null2String(ep.getName()) + ")-" + b + "(" + StringHelper.null2String(ps.getName()) + ")";
                    }
                    break;
                case 2:
                    String ids = (String) (args[0]);
                    String[] idarray = ids.split(",");
                    for (String id : idarray) {
                        Examinepage ep2 = examinepageService.getById(id);
                        String name = ep2.getName();
                        objname = objname.isEmpty() ? StringHelper.null2String(ep2.getName()) : objname + "," + StringHelper.null2String(ep2.getName());
                    }
                    break;
                case 3:
                    Examinepage ep2 = new Examinepage();
                    for (Examinestudent e : (Examinestudent[]) (args[0])) {
                        Person ps2 = commonBaseApi.getPersonById(e.getPersonid()).getResult();
                        ep2 = examinepageService.getById(e.getExamineid());
                        objname = objname.isEmpty() ? StringHelper.null2String(ps2.getName()) : objname + "," + StringHelper.null2String(ps2.getName());
                    }
                    objname = a + "(" + StringHelper.null2String(ep2.getName()) + ")-" + b + "(" + objname + ")";
                    break;
                default:
                    break;
            }
        } else if (lmslog.otype().equals(LogType.Export)) { //编辑数据日志
            Examinepage p = examinepageService.getById(args[0].toString());
            objname = StringHelper.null2String(p.getName());
        } else if (lmslog.otype().equals(LogType.Delete)) { //删除数据日志
            String ids = (String) (args[0]);
            String[] idarray = ids.split(",");
            Examinepage ep = new Examinepage();
            for (String id : idarray) {
                Examinestudent p = examinestudentService.getById(id);
                Person ps = commonBaseApi.getPersonById(p.getPersonid()).getResult();
                ep = examinepageService.getById(p.getExamineid());
                objname = objname.isEmpty() ? StringHelper.null2String(ps.getName()) : objname + "," + StringHelper.null2String(ps.getName());
            }
            objname = a + "(" + StringHelper.null2String(ep.getName()) + ")-" + b + "(" + objname + ")";
        }
        return objname;
    }
}
