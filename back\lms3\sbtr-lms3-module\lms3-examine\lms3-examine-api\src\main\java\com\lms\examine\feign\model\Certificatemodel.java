package com.lms.examine.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import com.lms.common.model.SearchContent;
import com.lms.common.model.SearchName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)

@Data
@TableName("u_certificatemodel")
@ApiModel(value = "证书模板实体类")
public class Certificatemodel extends BaseModel {

    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    @SearchName
    private String id;

    @ApiModelProperty("模板名称")
    @SearchContent
    private String name;

    @ApiModelProperty("背景图片")
    private String backgroundimg;

    @ApiModelProperty("印章图片")
    private String sealimg;

    @ApiModelProperty("证书模板介绍")
    @SearchContent
    private String content;

    @ApiModelProperty("创建人")
    private String creator;

    @TableField(exist = false)
    @ApiModelProperty("背景图片地址")
    private String bgsrc;

    @TableField(exist = false)
    @ApiModelProperty("印章图片地址")
    private String sealsrc;


}
