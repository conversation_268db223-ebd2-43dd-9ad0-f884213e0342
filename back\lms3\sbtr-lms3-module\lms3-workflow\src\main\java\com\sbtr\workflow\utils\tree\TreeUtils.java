package com.sbtr.workflow.utils.tree;


import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 树形结构工具类，如：菜单、机构等
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-05-18 13:40:36
 */
public class TreeUtils {



    /**
     * 构建树节点（系统必须至少包含一个根目录的部门，且该部门不包含任何人员）
     */
    public static <T extends TreeNode> List<T> build(List<T> treeNodes) {
        List<T> result = new ArrayList<>();
        // list转map
        Map<String, T> nodeMap = new LinkedHashMap<>(treeNodes.size());
        for(T treeNode : treeNodes){
            nodeMap.put(treeNode.getId(), treeNode);
        }
        for(T node : nodeMap.values()) {
            T parent = nodeMap.get(node.getPid());
            if(parent != null && !(node.getId().equals(parent.getId()))){
                parent.getChildren().add(node);
                continue;
            }
            result.add(node);
        }

        return result;
    }


}