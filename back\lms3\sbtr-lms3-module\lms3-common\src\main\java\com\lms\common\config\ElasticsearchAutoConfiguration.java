package com.lms.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * Elasticsearch自动配置控制类
 * 根据spring.elasticsearch.enabled配置决定是否启用ES自动配置
 *
 * <AUTHOR> Team
 * @date 2025-01-11
 */
@Configuration
@AutoConfigureBefore(ElasticsearchRestClientAutoConfiguration.class)
@Slf4j
public class ElasticsearchAutoConfiguration {

    /**
     * ES启用时的配置
     */
    @Configuration
    @ConditionalOnProperty(name = "spring.elasticsearch.enabled", havingValue = "true")
    @Slf4j
    public static class ElasticsearchEnabledAutoConfiguration {

        @PostConstruct
        public void init() {
            log.info("Elasticsearch自动配置已启用");
        }
    }

    /**
     * ES禁用时的配置
     */
    @Configuration
    @ConditionalOnProperty(name = "spring.elasticsearch.enabled", havingValue = "false", matchIfMissing = true)
    @Slf4j
    public static class ElasticsearchDisabledAutoConfiguration {

        @PostConstruct
        public void init() {
            log.info("Elasticsearch自动配置已禁用");
        }
    }
}
