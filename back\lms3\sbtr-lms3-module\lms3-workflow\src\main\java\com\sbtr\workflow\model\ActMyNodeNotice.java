package com.sbtr.workflow.model;

import cn.ewsd.common.model.MCoreBase;

import javax.persistence.Table;

/**
 * 流程每个节点所对应通知
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-12-09 11:58:35
 */
@Table(name = "act_my_node_notice")
public class ActMyNodeNotice extends MCoreBase {
    private static final long serialVersionUID = 1L;

    /**
     * 模型id
     */
    private String actDeModelId;
    /**
     * 模型key
     */
    private String actDeModelKey;
    /**
     * 通知名称
     */
    private String noticeName;
    /**
     * 通知code
     */
    private String noticeCode;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * 节点Id
     */
    private String nodeId;
    /**
     * 流程表单uuid
     */
    private String flowModelUuid;
    /**
     * 流程定义Id(没发布一个版本就会存在所对应的表单数据)
     */
    private String procdefId;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getActDeModelId() {
        return actDeModelId;
    }

    public void setActDeModelId(String actDeModelId) {
        this.actDeModelId = actDeModelId;
    }

    public String getActDeModelKey() {
        return actDeModelKey;
    }

    public void setActDeModelKey(String actDeModelKey) {
        this.actDeModelKey = actDeModelKey;
    }

    public String getNoticeName() {
        return noticeName;
    }

    public void setNoticeName(String noticeName) {
        this.noticeName = noticeName;
    }

    public String getNoticeCode() {
        return noticeCode;
    }

    public void setNoticeCode(String noticeCode) {
        this.noticeCode = noticeCode;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getFlowModelUuid() {
        return flowModelUuid;
    }

    public void setFlowModelUuid(String flowModelUuid) {
        this.flowModelUuid = flowModelUuid;
    }

    public String getProcdefId() {
        return procdefId;
    }

    public void setProcdefId(String procdefId) {
        this.procdefId = procdefId;
    }
}
