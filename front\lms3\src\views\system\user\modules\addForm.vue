<template>
  <a-modal
    :open="visible"
    :confirm-loading="loading"
    width="600px"
    :destroy-on-close="true"
    class="user-add-modal"
    @cancel="handleCancel"
  >
    <!-- 自定义标题 -->
    <template #title>
      <div class="modal-title">
        <h3>编辑用户账号信息</h3>
      </div>
    </template>

    <div class="user-add-form">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="horizontal"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="账号" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入账号" />
        </a-form-item>

        <a-form-item label="单位">
          {{ formData.deptname }}
        </a-form-item>

        <a-form-item label="角色">
          {{ formData.rolename }}
        </a-form-item>

        <a-form-item label="重置密码">
          <a-switch v-model:checked="formData.resetPassword" />
        </a-form-item>

        <a-form-item v-if="formData.resetPassword" label="密码" name="password">
          <a-input-password
            v-model:value="formData.password"
            placeholder="请输入密码"
          />
        </a-form-item>

        <a-form-item
          v-if="formData.resetPassword"
          label="确认密码"
          name="confirmPassword"
        >
          <a-input-password
            v-model:value="formData.confirmPassword"
            placeholder="请再次输入密码"
          />
        </a-form-item>

        <a-form-item label="IP地址" name="ipaddr">
          <a-input
            v-model:value="formData.ipaddr"
            placeholder="请输入IP地址（可选）"
          />
        </a-form-item>

        <a-form-item label="账号状态">
          {{ formData.islocked ? "锁定" : "正常" }}
        </a-form-item>

        <a-form-item label="是否启用" name="enable">
          <a-radio-group v-model:value="formData.enable" name="enable">
            <a-radio :value="true">是</a-radio>
            <a-radio :value="false">否</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </div>

    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" :loading="loading" @click="handleSubmit">
          提交
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { message } from "ant-design-vue";
import type { FormInstance } from "ant-design-vue";
import type { User, UserForm } from "@/types/system";
import { editUser } from "@/api/system.ts";

interface Props {
  visible: boolean;
  user?: User | null;
  isEdit?: boolean;
}

interface Emits {
  (e: "close", visible: boolean): void;
  (e: "submit", data: UserForm): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const loading = ref(false);

// 表单数据
let formData = {};

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入账号名" }],
  password: [
    { required: true, message: "请输入密码" },
    { min: 6, message: "密码长度不能少于6位" }
  ],
  confirmPassword: [
    { required: true, message: "请再次输入密码" },
    {
      validator: (rule: any, value: string) => {
        if (value !== formData.password) {
          return Promise.reject("两次输入的密码不一致");
        }
        return Promise.resolve();
      }
    }
  ],
  enable: [{ required: true, message: "请选择状态" }]
};

// 监听弹窗显示状态，初始化表单数据
watch(
  () => props.visible,
  visible => {
    if (visible) {
      if (!props.user) return;
      formData = props.user;
    }
  }
);

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;
    delete formData.confirmPassword;
    delete formData.resetPassword;
    delete formData.deptname;
    delete formData.rolename;
    delete formData.departmentid;
    let res = await editUser(formData);
    if (res.success) {
      message.success(res.message);
      emit("submit", { ...formData });
      handleCancel();
    } else {
      message.error(res.message);
    }
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit("close", false);
};
</script>

<style lang="scss" scoped>
.modal-title {
  background-color: var(--gray-100);
  margin: -24px -24px 24px -24px;
  padding: var(--spacing-6);

  h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
    color: var(--gray-900);
    margin: 0;
  }
}

.user-add-form {
  max-height: 500px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-3);
}

// 表单样式
:deep(.ant-form-item) {
  margin-bottom: var(--spacing-4);
}

:deep(.ant-form-item-label) {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
}

:deep(.ant-input),
:deep(.ant-select),
:deep(.ant-input-password) {
  width: 100%;
}

// 自定义标题样式
:deep(.ant-modal-header) {
  padding: 0;
  border: 0;
}

:deep(.ant-modal-title) {
  padding: 0;
}
</style>
