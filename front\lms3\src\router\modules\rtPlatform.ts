// import { $t } from "@/plugins/i18n";

export default {
  path: "/rtPlatform",
  redirect: "/rtPlatform/presentation",
  meta: {
    icon: "mdi:teach-poll",
    // showLink: false,
    title: "远程课堂平台",
    rank: 3
  },
  children: [
    // {
    //   path: "/rtPlatform/presentation",
    //   name: "presentation",
    //   component: () => import("@/views/rtPlatform/presentation/index.vue"),
    //   meta: {
    //     // title: $t("menus.pureFourZeroOne"),
    //     title: "远程授课"
    //   }
    // }
  ]
} satisfies RouteConfigsTable;
