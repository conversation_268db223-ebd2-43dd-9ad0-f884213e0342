// 用户相关类型定义
export interface User {
  id: string;
  account: string;
  name: string;
  department: string;
  role: string;
  accountStatus: string;
  isEnabled: string;
  ipAddress: string;
  level: string;
}

export interface UserListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  result: {
    records: User[];
    total: number;
    size: number;
    current: number;
    pages: number;
  };
  timestamp: number;
}

export interface UserFilter {
  name?: string;
  personname?: string;
  department?: string;
  role?: string;
  accountStatus?: string;
  isEnabled?: string;
  keyword?: string;
}

export interface UserListParams extends UserFilter {
  current?: number;
  size?: number;
}

export interface UserForm {
  account: string;
  name: string;
  department: string;
  role: string;
  password?: string;
  confirmPassword?: string;
  ipAddress?: string;
  level: string;
  isEnabled: string;
}

// 人员信息相关类型定义
export interface Personnel {
  id?: string;
  name?: string;
  sex?: string;
  cardNum?: string;
  birthday?: string;
  departmentid?: string;
  duty?: string;
  specialityid?: string;
  persontype?: string;
}

export interface PersonnelListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  result: {
    records: Personnel[];
    total: number;
    size: number;
    current: number;
    pages: number;
  };
  timestamp: number;
}

export interface PersonnelFilter {
  name?: string;
}

export interface PersonnelListParams extends PersonnelFilter {
  current?: number;
  size?: number;
}

export interface PersonnelForm {
  name: string;
  sex: string;
  cardNum: string;
  birthday: string;
  departmentid: string;
  duty: string;
  specialityid: string;
  persontype: string;
}

// 数据字典相关类型定义
export interface Dictionary {
  id: string;
  objname: string;
  seqno: number;
  objdesc: string;
  issystemlevel: number;
  status: number;
}
// 数据字典选项相关类型定义
export interface DictionaryItem {
  id?: string;
  objname?: string;
  typeid?: string;
  seqno?: number;
  code?: string;
  objdesc?: string;
  issystemlevel?: number;
  status?: number;
}

export interface DictionaryListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  result: {
    records: Dictionary[];
    total: number;
    size: number;
    current: number;
    pages: number;
  };
  timestamp: number;
}

export interface DictionaryFilter {
  objname?: string;
  code?: string;
  objdesc?: string;
  seqno?: number;
}

export interface DictionaryListParams extends DictionaryFilter {
  current?: number;
  size?: number;
}

export interface DictionaryForm {
  objname?: string;
  objdesc?: string;
  seqno?: number;
}

// 系统日志相关类型定义
export interface SystemLog {
  id: string;
  operatorName: string;
  operator: string;
  operationTime: string;
  logType: string;
  ipAddress: string;
  operationDescription: string;
  operationResult: string;
  level: string;
}

export interface SystemLogListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  result: {
    records: SystemLog[];
    total: number;
    size: number;
    current: number;
    pages: number;
  };
  timestamp: number;
}

export interface SystemLogFilter {
  logtype?: string;
  objname?: string;
  submitor?: string;
  startDate?: string;
  endDate?: string;
}

export interface SystemLogListParams extends SystemLogFilter {
  current?: number;
  size?: number;
}
