<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref } from "vue";
import type { Model } from "@/types/model.d.ts";
import { message } from "ant-design-vue";
import {
  UserOutlined,
  EditOutlined,
  DeleteOutlined,
  LoadingOutlined,
  WarningOutlined,
  PlayCircleOutlined,
  PictureOutlined,
  FileOutlined,
  AppstoreOutlined,
  PlayCircleFilled
} from "@ant-design/icons-vue";
import ModelDetailModal from "./detailModal.vue";
import ModelEditForm from "./editForm.vue";
import { deleteModel, getById } from "@/api/model";
import { useModelStore } from "@/store/modules/model";

interface Props {
  model: Model;
}
interface Emits {
  (e: "delete-success"): void;
  (e: "edit-success"): void;
}
const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const editorModalVisible = ref(false);
const editFormVisible = ref(false);
const detailModalVisible = ref(false);
const deleteModalVisible = ref(false);
const deleteLoading = ref(false);

const windowWidth = ref(window.innerWidth);
const windowHeight = ref(window.innerHeight);
const scaleMode = ref("fit");
const showScrollbars = ref(false);
const formData = ref<any>(null);
const defaultImage = "";

// 视频相关状态
const videoElement = ref<HTMLVideoElement>();
const videoThumbnail = ref<string>("");
const videoLoading = ref(false);
const videoDuration = ref<number>(0);

const modelStore = useModelStore();

// 修复a-tooltip报错，定义mergedArrow为true，显示默认箭头
const mergedArrow = true;

// 检查是否有有效的预览图
const hasValidPreview = computed(() => {
  const previewUrl = props.model.previewUrl || props.model.fileUrl;
  return (
    previewUrl &&
    previewUrl !== "" &&
    previewUrl !== "undefined" &&
    previewUrl !== "null" &&
    !previewUrl.includes("placeholder") &&
    !previewUrl.includes("default")
  );
});

// 文件类型判断
const isImageFile = computed(() => {
  const fileUrl = props.model.fileUrl || "";
  const imageExtensions = [
    ".jpg",
    ".jpeg",
    ".png",
    ".gif",
    ".bmp",
    ".webp",
    ".svg"
  ];
  const imageTypes = ["image/"];

  return (
    imageExtensions.some(ext => fileUrl.toLowerCase().includes(ext)) ||
    imageTypes.some(type => fileUrl.toLowerCase().includes(type))
  );
});

const isVideoFile = computed(() => {
  const fileUrl = props.model.fileUrl || "";
  const videoExtensions = [
    ".mp4",
    ".avi",
    ".mov",
    ".wmv",
    ".flv",
    ".webm",
    ".mkv",
    ".m4v"
  ];
  const videoTypes = ["video/"];

  return (
    videoExtensions.some(ext => fileUrl.toLowerCase().includes(ext)) ||
    videoTypes.some(type => fileUrl.toLowerCase().includes(type))
  );
});

const is3DModelFile = computed(() => {
  const fileUrl = props.model.fileUrl || "";
  const modelExtensions = [
    ".obj",
    ".fbx",
    ".gltf",
    ".glb",
    ".dae",
    ".3ds",
    ".ply",
    ".stl",
    ".blend",
    ".max",
    ".ma",
    ".mb",
    ".vrp",
    ".vrpc"
  ];
  const modelTypes = ["model/", "application/octet-stream"];

  return (
    modelExtensions.some(ext => fileUrl.toLowerCase().includes(ext)) ||
    modelTypes.some(type => fileUrl.toLowerCase().includes(type))
  );
});

// 获取文件类型文本
const getFileTypeText = (): string => {
  if (isImageFile.value) return "图片";
  if (isVideoFile.value) return "视频";
  if (is3DModelFile.value) return "3D模型";
  return "文件";
};

// 截取视频首帧
const captureVideoFrame = async () => {
  if (!videoElement.value) return;

  // 先查缓存
  const cached = modelStore.getVideoThumbnail(props.model.id);
  if (cached) {
    videoThumbnail.value = cached;
    videoLoading.value = false;
    return;
  }

  try {
    console.log("🎬 开始截取视频首帧:", props.model.name);
    videoLoading.value = true;

    const video = videoElement.value;

    // 等待视频元数据加载完成
    if (video.readyState < 1) {
      await new Promise(resolve => {
        video.addEventListener("loadedmetadata", resolve, { once: true });
      });
    }

    // 获取视频时长
    videoDuration.value = video.duration;

    // 设置视频时间到第一帧（0.1秒，避免黑屏）
    video.currentTime = 0.1;

    // 等待视频帧加载
    await new Promise(resolve => {
      video.addEventListener("seeked", resolve, { once: true });
    });

    // 创建canvas来截取帧
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    if (!ctx) {
      throw new Error("无法创建canvas上下文");
    }

    // 设置canvas尺寸（保持视频比例）
    const aspectRatio = video.videoWidth / video.videoHeight;
    const maxWidth = 400;
    const maxHeight = 200;

    let canvasWidth = maxWidth;
    let canvasHeight = maxWidth / aspectRatio;

    if (canvasHeight > maxHeight) {
      canvasHeight = maxHeight;
      canvasWidth = maxHeight * aspectRatio;
    }

    canvas.width = canvasWidth;
    canvas.height = canvasHeight;

    // 绘制视频帧到canvas
    ctx.drawImage(video, 0, 0, canvasWidth, canvasHeight);

    // 转换为base64图片
    const thumbnailDataUrl = canvas.toDataURL("image/jpeg", 0.8);
    videoThumbnail.value = thumbnailDataUrl;

    // 存入缓存
    modelStore.setVideoThumbnail(props.model.id, thumbnailDataUrl);

    console.log("✅ 视频首帧截取成功");
  } catch (error) {
    console.error("❌ 视频首帧截取失败:", error);
    videoThumbnail.value = "";
  } finally {
    videoLoading.value = false;
  }
};

// 处理视频加载错误
const handleVideoError = (event: Event) => {
  console.error("❌ 视频加载失败:", event);
  videoLoading.value = false;
  videoThumbnail.value = "";
};

// 格式化视频时长
const formatDuration = (duration: number): string => {
  if (!duration || isNaN(duration)) return "";

  const minutes = Math.floor(duration / 60);
  const seconds = Math.floor(duration % 60);

  if (minutes > 0) {
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  } else {
    return `0:${seconds.toString().padStart(2, "0")}`;
  }
};

// 原始iframe尺寸
const ORIGINAL_WIDTH = 1920;
const ORIGINAL_HEIGHT = 1080;

// 表格列定义
const tableColumns = [
  {
    title: "属性名",
    dataIndex: "propertyName",
    key: "propertyName",
    width: "33%"
  },
  {
    title: "属性值",
    dataIndex: "propertyValue",
    key: "propertyValue",
    width: "33%"
  },
  {
    title: "属性关系",
    dataIndex: "propertyRelation",
    key: "propertyRelation",
    width: "34%"
  }
];

// 表格数据 - 从model.field转换
const tableData = computed(() => {
  if (!props.model.field || typeof props.model.field !== "object") {
    return [];
  }

  return Object.entries(props.model.field).map(([key, value], index) => ({
    key: (index + 1).toString(),
    propertyName: key,
    propertyValue: value,
    propertyRelation: "附有" // 默认关系
  }));
});

const updateWindowSize = () => {
  windowWidth.value = window.innerWidth;
  windowHeight.value = window.innerHeight;
};

onMounted(() => {
  if (isVideoFile.value && hasValidPreview.value) {
    nextTick(() => {
      videoLoading.value = true;
    });
  }

  window.addEventListener("resize", updateWindowSize);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateWindowSize);
});

// 计算弹窗尺寸
const modalWidth = computed(() => {
  return Math.min(windowWidth.value - 40, 1600);
});

const modalHeight = computed(() => {
  return Math.min(windowHeight.value - 120, 900);
});

// 计算实际缩放比例
const actualScale = computed(() => {
  if (scaleMode.value === "fit") {
    const scaleX = modalWidth.value / ORIGINAL_WIDTH;
    const scaleY = modalHeight.value / ORIGINAL_HEIGHT;
    return Math.min(scaleX, scaleY, 1);
  } else {
    return parseInt(scaleMode.value) / 100;
  }
});

// 计算缩放后的实际尺寸
const scaledWidth = computed(() => ORIGINAL_WIDTH * actualScale.value);
const scaledHeight = computed(() => ORIGINAL_HEIGHT * actualScale.value);

// 容器样式
const containerStyle = computed(() => {
  if (showScrollbars.value) {
    return {
      width: `${modalWidth.value}px`,
      height: `${modalHeight.value}px`,
      overflow: "auto"
    };
  } else {
    return {
      width: `${modalWidth.value}px`,
      height: `${modalHeight.value}px`,
      overflow: "hidden",
      display: "flex",
      alignItems: "center",
      justifyContent: "center"
    };
  }
});

// iframe样式
const iframeStyle = computed(() => {
  const scale = actualScale.value;

  return {
    width: `${ORIGINAL_WIDTH}px`,
    height: `${ORIGINAL_HEIGHT}px`,
    transform: `scale(${scale})`,
    transformOrigin: showScrollbars.value ? "top left" : "center center",
    border: "none",
    display: "block",
    background: "white"
  };
});

// 格式化日期
const formatDate = (dateStr: string | null): string => {
  if (!dateStr) return "未知时间";
  try {
    return new Date(dateStr).toLocaleString("zh-CN");
  } catch {
    return dateStr;
  }
};

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = defaultImage;
};

const handleCardClick = id => {
  getById(id).then(res => {
    if (res.success) {
      formData.value = res.result;
      detailModalVisible.value = true;
    } else {
      message.error(res.message);
    }
  });
};

const handleEdit = id => {
  getById(id).then(res => {
    if (res.success) {
      formData.value = res.result;
      editFormVisible.value = true;
    } else {
      message.error(res.message);
    }
  });
};

const handleDelete = () => {
  deleteModalVisible.value = true;
};
const confirmDelete = async () => {
  try {
    deleteModalVisible.value = true;
    const response = await deleteModel(props.model.id);

    if (response.success) {
      message.success(response.message);
      deleteModalVisible.value = false;
      emit("delete-success");
    } else {
      message.error(response.message || "删除失败");
    }
  } catch (error) {
    console.error("删除失败:", error);
    message.error("删除失败");
  } finally {
    deleteModalVisible.value = false;
  }
};
const handleEditBasicInfo = () => {
  editFormVisible.value = true;
};

const handleEditSubmit = (formData: any) => {
  try {
    console.log("编辑提交的数据:", formData, props);

    // 这里可以调用API更新模型信息
    // await updateModel(props.model)

    // message.success("编辑成功");

    // 触发父组件刷新列表
    emit("edit-success");
  } catch (error) {
    console.error("编辑模型失败:", error);
    // message.error("编辑失败");
  }
};

const openModelEditor = () => {
  detailModalVisible.value = false;
  editorModalVisible.value = true;

  nextTick(() => {
    updateWindowSize();
  });
};

const handleScaleModeChange = () => {
  if (scaleMode.value !== "fit") {
    const scale = parseInt(scaleMode.value) / 100;
    const needsScrollbars =
      ORIGINAL_WIDTH * scale > modalWidth.value ||
      ORIGINAL_HEIGHT * scale > modalHeight.value;
    showScrollbars.value = needsScrollbars;
  } else {
    showScrollbars.value = false;
  }
};

const handleScrollbarChange = () => {
  // 滚动条状态改变时的处理
};

// 处理保存成功
const handleSaveSuccess = () => {
  // 触发父组件刷新列表
  emit("edit-success");
};
</script>
<template>
  <div class="model-card" @click="handleCardClick(model.id)">
    <!-- 模型图片 -->
    <!-- <div class="model-image">
      <img
        :src="model.previewUrl || model.fileUrl || defaultImage"
        :alt="model.name"
        @error="handleImageError"
      />
    </div> -->
    <div class="model-image">
      <!-- 有预览图时显示图片 -->
      <img
        v-if="isImageFile"
        :src="model.previewUrl || model.fileUrl"
        :alt="model.name"
        class="preview-image"
        crossOrigin="anonymous"
        @error="handleImageError"
      />
      <!-- 视频类型：显示视频首帧截图 -->
      <div v-else-if="isVideoFile" class="video-thumbnail-container">
        <!-- 视频元素（隐藏，仅用于截取首帧） -->
        <video
          ref="videoElement"
          :src="model.previewUrl || model.fileUrl"
          class="hidden-video"
          preload="metadata"
          crossOrigin="anonymous"
          muted
          @loadedmetadata="captureVideoFrame"
          @error="handleVideoError"
        />

        <!-- 显示截取的首帧 -->
        <img
          v-if="videoThumbnail"
          :src="videoThumbnail"
          :alt="model.name"
          class="preview-image video-frame"
        />

        <!-- 视频加载中状态 -->
        <div v-else-if="videoLoading" class="video-loading">
          <LoadingOutlined class="loading-icon" />
          <p class="loading-text">正在生成预览图...</p>
        </div>

        <!-- 视频加载失败，显示默认视频效果 -->
        <div v-else class="default-video">
          <PlayCircleOutlined class="default-icon" />
          <p class="default-text">视频预览</p>
          <p class="default-hint">无法生成预览图</p>
        </div>

        <!-- 视频播放图标覆盖层 -->
        <div class="video-play-overlay">
          <PlayCircleFilled class="play-icon" />
        </div>

        <!-- 视频时长标识（如果能获取到） -->
        <div v-if="videoDuration" class="video-duration">
          {{ formatDuration(videoDuration) }}
        </div>
      </div>

      <!-- 无预览图时显示默认效果 -->
      <div v-else class="default-preview">
        <!-- 根据文件类型显示不同的默认效果 -->
        <div v-if="isImageFile" class="default-image">
          <PictureOutlined class="default-icon" />
          <p class="default-text">图片预览</p>
          <p class="default-hint">暂无预览图</p>
        </div>

        <div v-else-if="isVideoFile" class="default-video">
          <PlayCircleOutlined class="default-icon" />
          <p class="default-text">视频预览</p>
          <p class="default-hint">暂无预览图</p>
        </div>

        <div v-else-if="is3DModelFile" class="default-model">
          <AppstoreOutlined class="default-icon" />
          <p class="default-text">3D模型</p>
          <p class="default-hint">暂无预览图</p>
        </div>

        <div v-else class="default-file">
          <FileOutlined class="default-icon" />
          <p class="default-text">文件预览</p>
          <p class="default-hint">暂无预览图</p>
        </div>

        <!-- 文件类型标识 -->
        <div class="file-type-badge">
          {{ getFileTypeText() }}
        </div>
      </div>
    </div>
    <!-- 模型信息 -->
    <div class="model-info">
      <!-- 标题和分类 -->
      <div class="model-header">
        <h3 class="model-title">{{ model.name }}</h3>
        <span class="model-number">{{ model.number }}</span>
      </div>

      <!-- 描述 -->
      <p class="model-desc">{{ model.desc || "暂无描述" }}</p>

      <!-- 底部操作区 -->
      <div class="model-footer">
        <!-- 上传信息 -->
        <div class="upload-info">
          <UserOutlined class="icon" />
          {{ model.uploadPerson }}
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <a-tooltip placement="top" :arrow="mergedArrow">
            <template #title>
              <span>编辑</span>
            </template>
            <a-button
              type="text"
              size="small"
              class="action-btn"
              @click.stop="handleEdit(model.id)"
            >
              <EditOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip placement="top" :arrow="mergedArrow">
            <template #title>
              <span>删除</span>
            </template>
            <a-button
              type="text"
              size="small"
              class="action-btn"
              @click.stop="handleDelete"
            >
              <DeleteOutlined />
            </a-button>
          </a-tooltip>
          <!-- <a-dropdown placement="bottomRight">
            <a-button type="text" size="small" class="action-btn">
              <ShareAltOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item key="edit" @click="handleEdit">编辑</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown> -->
        </div>
      </div>

      <!-- 模型详情弹窗 -->
      <ModelDetailModal
        :visible="detailModalVisible"
        :model="formData"
        @update:visible="detailModalVisible = $event"
        @save-success="handleSaveSuccess"
      />

      <!-- 模型编辑弹窗 -->
      <ModelEditForm
        :visible="editFormVisible"
        :model="formData"
        @update:visible="editFormVisible = $event"
        @submit="handleEditSubmit"
      />

      <!-- 删除确认弹窗 -->
      <a-modal
        :open="deleteModalVisible"
        title="确认删除"
        :confirm-loading="deleteLoading"
        @ok="confirmDelete"
        @cancel="deleteModalVisible = false"
      >
        <p>确定要删除模型"{{ model.name }}"吗？</p>
        <p class="delete-warning">
          <WarningOutlined class="warning-icon" />
          删除后将无法恢复，请谨慎操作！
        </p>
      </a-modal>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.model-card {
  background-color: white;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all var(--transition-normal);
  cursor: pointer;

  &:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
  }
}

.model-image {
  aspect-ratio: 2/1;
  background-color: var(--gray-100);
  position: relative;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 默认预览效果样式
.default-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .default-image,
  .default-video,
  .default-model,
  .default-file {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    text-align: center;
    width: 100%;
    height: 100%;
    padding: var(--spacing-4);
  }

  // 图片类型默认效果
  .default-image {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    .default-icon {
      font-size: 32px;
      color: white;
    }
  }

  // 视频类型默认效果
  .default-video {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;

    .default-icon {
      font-size: 32px;
      color: white;
    }
  }

  // 3D模型类型默认效果
  .default-model {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;

    .default-icon {
      font-size: 32px;
      color: white;
    }
  }

  // 其他文件类型默认效果
  .default-file {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: var(--gray-700);

    .default-icon {
      font-size: 32px;
      color: var(--gray-600);
    }
  }

  .default-text {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    margin: 0;
    line-height: 1.2;
  }

  .default-hint {
    font-size: var(--text-xs);
    opacity: 0.8;
    margin: 0;
    line-height: 1.2;
  }

  // 文件类型标识
  .file-type-badge {
    position: absolute;
    top: var(--spacing-2);
    right: var(--spacing-2);
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    backdrop-filter: blur(4px);
  }
}

.model-info {
  padding: var(--spacing-3);
}

.model-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.model-title {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-900);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin: 0;
}

.model-number {
  font-size: var(--text-xs);
  color: var(--gray-500);
  background-color: var(--gray-100);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  text-align: center;
  flex-shrink: 0;
}

.model-desc {
  font-size: var(--text-xs);
  color: var(--gray-600);
  margin: 0 0 var(--spacing-2) 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.model-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.upload-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: var(--text-xs);
  color: var(--gray-500);
  gap: var(--spacing-1);

  .icon {
    width: 12px;
    height: 12px;
  }
}

.action-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 2px;
}

.action-btn {
  padding: var(--spacing-1);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none !important;
  box-shadow: none !important;

  &:hover {
    background-color: var(--gray-100);
  }
}

// 删除确认弹窗样式
.delete-warning {
  color: var(--error-color);
  margin-top: var(--spacing-2);
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--spacing-1);

  .warning-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }
}
</style>
