# TrPreview 文件预览组件

一个功能完整的文件预览组件，支持图片、视频、3D模型和其他文件类型的预览。

## 功能特性

- **图片预览** - 支持 jpg, jpeg, png, gif, bmp, webp, svg 等格式
- **视频预览** - 支持 mp4, avi, mov, wmv, flv, webm, mkv, m4v 等格式
- **3D模型预览** - 支持 obj, fbx, gltf, glb, dae, 3ds, ply, stl, blend, max, ma, mb, vrp, vrpc 等格式
- **其他文件预览** - 显示文件图标、名称、URL和下载按钮
- **自动类型识别** - 根据文件扩展名和MIME类型自动识别文件类型
- **加载状态管理** - 显示加载状态和错误处理
- **事件回调** - 提供完整的事件回调机制
- **可定制样式** - 支持自定义样式和配置

## 基本用法

```vue
<template>
  <TrPreview
    :file="fileInfo"
    width="400px"
    height="300px"
    @load="handleLoad"
    @error="handleError"
    @download="handleDownload"
  />
</template>

<script setup lang="ts">
import { TrPreview, type FileInfo } from '@/components/TrPreview';

const fileInfo: FileInfo = {
  name: '示例图片.jpg',
  url: 'https://example.com/image.jpg',
  previewUrl: 'https://example.com/preview.jpg'
};

const handleLoad = (file: FileInfo) => {
  console.log('文件加载完成:', file);
};

const handleError = (error: Error, file: FileInfo) => {
  console.error('文件加载失败:', error, file);
};

const handleDownload = (file: FileInfo) => {
  console.log('下载文件:', file);
};
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| file | `FileInfo` | - | 文件信息对象（必填） |
| width | `string \| number` | `'100%'` | 容器宽度 |
| height | `string \| number` | `'100%'` | 容器高度 |
| showTypeBadge | `boolean` | `true` | 是否显示文件类型徽章 |
| showDownloadButton | `boolean` | `true` | 是否显示下载按钮（仅对非3D模型文件有效） |
| showEditButton | `boolean` | `true` | 是否显示编辑按钮（仅对3D模型文件有效） |
| showFullscreenButton | `boolean` | `false` | 是否显示全屏预览按钮（仅对3D模型文件有效） |
| class | `string` | `''` | 自定义样式类名 |
| disabled | `boolean` | `false` | 是否禁用交互 |
| modelIframeSrc | `string` | `'/player/data/GUI/web.html'` | 3D模型iframe源地址 |
| modelEditorUrl | `string` | `'/editor/data/GUI/STUDIO.html'` | 3D模型编辑器地址 |
| defaultImageUrl | `string` | `'https://lms3.oss-cn-guangzhou.aliyuncs.com/model.png'` | 默认图片地址 |

## FileInfo 接口

```typescript
interface FileInfo {
  /** 文件名称 */
  name: string;
  /** 文件URL */
  url: string;
  /** 预览URL（可选） */
  previewUrl?: string;
  /** 文件类型（可选，不提供则自动识别） */
  type?: FileType;
  /** 文件大小（可选） */
  size?: number;
  /** 文件扩展名（可选） */
  extension?: string;
}
```

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| load | `(file: FileInfo)` | 文件加载完成 |
| error | `(error: Error, file: FileInfo)` | 文件加载错误 |
| download | `(file: FileInfo)` | 下载文件 |
| edit | `(file: FileInfo)` | 编辑3D模型 |
| fullscreen | `(file: FileInfo)` | 全屏预览3D模型 |
| retry | `(file: FileInfo)` | 重新加载 |

## 文件类型支持

### 图片文件

- 扩展名：`.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`, `.webp`, `.svg`
- MIME类型：`image/`

### 视频文件

- 扩展名：`.mp4`, `.avi`, `.mov`, `.wmv`, `.flv`, `.webm`, `.mkv`, `.m4v`
- MIME类型：`video/`

### 3D模型文件

- 扩展名：`.obj`, `.fbx`, `.gltf`, `.glb`, `.dae`, `.3ds`, `.ply`, `.stl`, `.blend`, `.max`, `.ma`, `.mb`, `.vrp`, `.vrpc`
- MIME类型：`model/`, `application/octet-stream`

## 高级用法

### 自定义文件类型

```vue
<template>
  <TrPreview
    :file="fileInfo"
    :show-type-badge="true"
    :show-download-button="true"
    :show-edit-button="false"
    @load="handleLoad"
  />
</template>

<script setup lang="ts">
import { TrPreview, type FileInfo, FileType } from '@/components/TrPreview';

const fileInfo: FileInfo = {
  name: '3D模型.fbx',
  url: 'https://example.com/model.fbx',
  type: FileType.MODEL_3D // 明确指定文件类型
};
</script>
```

### 处理3D模型预览

```vue
<template>
  <TrPreview
    :file="modelFile"
    :show-edit-button="true"
    :show-fullscreen-button="true"
    @edit="handleEdit"
    @fullscreen="handleFullscreen"
  />
</template>

<script setup lang="ts">
import { TrPreview, type FileInfo } from '@/components/TrPreview';

const modelFile: FileInfo = {
  name: '红色方块.vrp',
  url: 'https://example.com/building.vrp'
};

const handleEdit = (file: FileInfo) => {
  // 处理模型编辑
  console.log('编辑模型:', file);
};

const handleFullscreen = (file: FileInfo) => {
  // 处理全屏预览
  console.log('全屏预览:', file);
};
</script>
```
