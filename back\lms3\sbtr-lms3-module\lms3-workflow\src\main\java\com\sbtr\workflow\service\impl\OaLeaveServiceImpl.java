package com.sbtr.workflow.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.mapper.OaLeaveMapper;
import com.sbtr.workflow.model.OaLeave;
import com.sbtr.workflow.model.OaLeaveCommon;
import com.sbtr.workflow.service.OaLeaveService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.PageUtil.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.HashMap;
import java.util.List;

@Service("oaLeaveServiceImpl")
public class OaLeaveServiceImpl extends WorkflowBaseServiceImpl<OaLeave, String> implements OaLeaveService {

    @Autowired
    private OaLeaveMapper oaLeaveMapper;

    @Override
    public PageSet<OaLeaveCommon> getToDoTasks(PageParam pageParam, String filterSort, String userNmeId) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<OaLeaveCommon> list = oaLeaveMapper.getToDoTasks(filterSort, userNmeId);
        PageInfo<OaLeaveCommon> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public PageSet<OaLeaveCommon> getTasksInProgress(PageParam pageParam, String filterSort, String userNmeId) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<OaLeaveCommon> list = oaLeaveMapper.getTasksInProgress(filterSort, userNmeId);
        PageInfo<OaLeaveCommon> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public PageSet<OaLeaveCommon> getHistoryTasks(PageParam pageParam, String filterSort, String userNmeId) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<OaLeaveCommon> list = oaLeaveMapper.getHistoryTasks(filterSort, userNmeId);
        PageInfo<OaLeaveCommon> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public PageSet<OaLeave> getPageSet(PageParam pageParam, String filterSort, String title) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        Example example = new Example(OaLeave.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if(StrUtil.isNotBlank(title)){
            criteria.orLike("title", "%" + title + "%");
        }
        List<OaLeave> list = oaLeaveMapper.selectByExample(example);
        PageInfo<OaLeave> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public int executeDeleteBatch(String[] uuids) {
        return oaLeaveMapper.executeDeleteBatch(uuids);
    }

    @Override
    public Integer updateStateByUuid(String businessKey, String state) {
        return oaLeaveMapper.updateStateByUuid(businessKey, state);
    }

}
