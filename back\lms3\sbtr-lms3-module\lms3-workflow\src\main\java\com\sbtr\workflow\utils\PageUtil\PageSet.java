package com.sbtr.workflow.utils.PageUtil;

import java.io.Serializable;
import java.util.List;

/**
 * 分页相关参数
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-05-18 13:40:36
 */
public class PageSet<T> implements Serializable {

    /**
     * 总数据
     */
    private List<T> rows;

    /**
     * 每页多少数据
     */
    private int pageSize;

    /**
     * 当前是第几页
     */
    private int pageNo;

    /**
     * 总共多少页
     */
    private int totalPage;

    /**
     * 总共多少条数据
     */
    private int totalCount;

    public PageSet() {
    }

    public PageSet( List<T> rows, int pageSize, int pageNo,int totalPage, int totalCount) {
        this.rows = rows;
        this.pageSize = pageSize;
        this.pageNo = pageNo;
        this.totalPage = totalPage;
        this.totalCount = totalCount;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public List<T> getRows() {
        return this.rows;
    }

    public void setRows(List<T> rows) {
        this.rows = rows;
    }

    @Override
    public String toString() {
        return "PageSet{" +
                "rows=" + rows +
                ", pageSize=" + pageSize +
                ", pageNo=" + pageNo +
                ", totalPage=" + totalPage +
                ", totalCount=" + totalCount +
                '}';
    }
}

