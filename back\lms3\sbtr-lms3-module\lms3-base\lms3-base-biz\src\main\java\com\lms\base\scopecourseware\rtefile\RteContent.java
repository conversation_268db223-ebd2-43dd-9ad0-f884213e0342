package com.lms.base.scopecourseware.rtefile;

import lms.rte.CAM.Model.SeqActivityTree;
import lms.rte.CAM.Model.Tracking.AttempProgress;
import lms.rte.CAM.Model.Tracking.AttempProgressHelper;
import lms.rte.CAM.Model.Tracking.ObjectiveStatus;
import lms.rte.CAM.Model.Tracking.ObjectiveStatusHelper;
import lms.rte.Common.DataProviderType;
import lms.rte.DataManager.ITreeProvider;
import lms.rte.Sequencer.Navigation.NavigationType;
import lms.rte.Sequencer.SequenceNavigate.LaunchInfo;
import lms.rte.Sequencer.SequenceNavigate.SequencerNavigation;

public class RteContent {
    // / <summary>
    // / 学习ID
    // / </summary>
    private String m_Userid;

    // / <summary>
    // / 课程ID
    // / </summary>
    private String m_CourseID;

    // / <summary>
    // / 大纲ID
    // / </summary>
    private DataProviderType m_StateType;

    // / <summary>
    // / 目标活动ID
    // / </summary>
    private String m_TargetId;

    // / <summary>
    // / 是否复习
    // / </summary>
    private boolean m_Review;

    // / <summary>
    // / 树结构提供类
    // / </summary>
    private ITreeProvider m_TreeProvider;

    // / <summary>
    // / 构造函数
    // / </summary>
    // / <param name="userid"></param>
    // / <param name="courseId"></param>
    // / <param name="stateType"></param>
    // / <param name="targerId"></param>
    // / <param name="review"></param>
    public RteContent(String userid, String courseId,
                      DataProviderType stateType, String targerId, boolean review)
            throws Exception {
        if (userid == null || userid.equals("")) {
            throw new Exception("userid:获取树时用户ID为空");
        }
        if (courseId == null || courseId.equals("")) {
            throw new Exception("courseId:获取树时课程ID为空");
        }
        m_Userid = userid;
        m_CourseID = courseId;
        m_StateType = stateType;
        m_TargetId = targerId;
        m_Review = review;
    }

    // / <summary>
    // / 注册树提供程序
    // / </summary>
    // / <param name="treeProvider"></param>
    public void RegisteTreeProvider(ITreeProvider treeProvider) {
        m_TreeProvider = treeProvider;
    }

    // / <summary>
    // / 处理编序
    // / </summary>
    // / <returns></returns>
    public LaunchInfo DoSequence(String request, String orginalCourseId)
            throws Exception {
        orginalCourseId = "";
        // 获取当前树
        //获取值为空；无法进行下去；
        SeqActivityTree tree = m_TreeProvider.GetTree(m_Userid, m_CourseID,
                m_StateType, m_Review);

        if (tree != null) {
            orginalCourseId = tree.getOriginalCoursewareID();
            // tree.InitGlobalObjectives();
            SequencerNavigation target = new SequencerNavigation();
            target.setActivityTree(tree);
            target.setNavigate(ParseRequest(request));
            if (target.getNavigate() == NavigationType.Invalid) {
                if (tree.getSuspendedActivity() != null) {
                    target.setNavigate(NavigationType.ResumeAll);
                } else {
                    target.setNavigate(NavigationType.Start);
                }
            }

            target.setTargetId(m_TargetId);
            LaunchInfo actual = target.DoNavigate();

            m_TreeProvider.PersistTree(tree, m_StateType, m_Review);

            if (!m_Review && m_StateType == DataProviderType.Normal) {
                // 持久化暂停信息
                if (target.getNavigate() == NavigationType.SuspendAll
                        && tree.getSuspendedActivity() != null) {
                    PersistUserCourseInfo(true);
                } else if (target.getNavigate() == NavigationType.ResumeAll
                        && tree.getSuspendedActivity() == null) {
                    PersistUserCourseInfo(false);
                }
                // 持久化课程信息
                PresisitUserCourseStatus(tree.getRootActivity()
                        .GetAttemptObjectiveStatus(), tree.getRootActivity()
                        .GetAttemptObjectiveMeasure(), tree.getRootActivity()
                        .GetAttemptAttemptProgressStatus());
            }
            return actual;
        } else {
            throw new Exception("获取不到树数据");
        }
    }

    // / <summary>
    // / 是否暂停状态
    // / </summary>
    // / <param name="isSuspend">暂停状态</param>
    private void PersistUserCourseInfo(boolean isSuspend) {
        UserCourseInfoTask userCourseInfo = new UserCourseInfoTask(m_Userid,
                m_CourseID, isSuspend);
        DefaultHandlerTask.getInstance().ExcuteTask(userCourseInfo);
    }

    // / <summary>
    // / 持久化课程信息
    // / </summary>
    // / <param name="objectStatus"></param>
    // / <param name="measure"></param>
    // / <param name="attempProgress"></param>
    private void PresisitUserCourseStatus(ObjectiveStatus objectStatus,
                                          float measure, AttempProgress attempProgress) {
        UserCourseStatusTask task = new UserCourseStatusTask(m_Userid,
                m_CourseID, ObjectiveStatusHelper.GetTrackString(objectStatus),
                AttempProgressHelper.GetTrackString(attempProgress), measure
                + "");
        DefaultHandlerTask.getInstance().ExcuteTask(task);
    }

    // / <summary>
    // / 解析导航请求
    // / </summary>
    // / <param name="request"></param>
    // / <returns></returns>
    private NavigationType ParseRequest(String request) {
        String requestStr = request.toLowerCase();
        NavigationType navigateRequest = NavigationType.Invalid;
        if (requestStr.equals("start")) {
            navigateRequest = NavigationType.Start;
        } else if (requestStr.equals("abandon")) {
            navigateRequest = NavigationType.Abandon;
        } else if (requestStr.equals("abandonall")) {
            navigateRequest = NavigationType.AbandonAll;
        } else if (requestStr.equals("choice")) {// 运行
            navigateRequest = NavigationType.Choice;
        } else if (requestStr.equals("exit")) {// 退出
            navigateRequest = NavigationType.Exit;
        } else if (requestStr.equals("exitall")) {
            navigateRequest = NavigationType.ExitAll;
        } else if (requestStr.equals("previous")) {// 后退
            navigateRequest = NavigationType.Previous;
        } else if (requestStr.equals("continue")) {// 前进
            navigateRequest = NavigationType.Continue;
        } else if (requestStr.equals("resumeall")) {// 恢复
            navigateRequest = NavigationType.ResumeAll;
        } else if (requestStr.equals("suspendall")) {// 暂停
            navigateRequest = NavigationType.SuspendAll;
        }
        return navigateRequest;

    }
}
