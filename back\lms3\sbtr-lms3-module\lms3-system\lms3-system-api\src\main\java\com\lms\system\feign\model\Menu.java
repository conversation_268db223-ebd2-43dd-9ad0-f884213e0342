package com.lms.system.feign.model;

import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lms.common.model.BaseModel;
import com.lms.common.model.LogObjname;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * PMenuId entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)

@TableName("p_menu")
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "菜单对象")
public class Menu extends BaseModel {
    // Fields
    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private String id;

    @LogObjname
    @ApiModelProperty("菜单名称")
    private String menuname;

    @ApiModelProperty("父菜单id")
    private String pid;

    @ApiModelProperty("菜单地址")
    private String url;

    @ApiModelProperty("是否根目录")
    private Integer menutype;

    @ApiModelProperty("菜单图标")
    private String iconfile;

    @ApiModelProperty("高亮配置")
    private String hilighticonfile;

    @ApiModelProperty("菜单顺序")
    private Integer seqno;

    @ApiModelProperty("是否隐藏")
    private Integer isleaf;

    @ApiModelProperty("状态")
    private Integer status;

    @JsonIgnore
    @TableField(exist = false)
    @ApiModelProperty("子菜单列表")
    private List<Menu> childMenus;
}
