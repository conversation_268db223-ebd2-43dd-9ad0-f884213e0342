package com.lms.base.model.vo;

import com.lms.base.model.vo.EditorVo;
import com.lms.base.model.vo.ModelVo;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class EditorVoConverter {
    
    /**
     * 将EditorVo转换为ModelVo
     */
    public static ModelVo convertToModelVo(EditorVo editorVo) {
        if (editorVo == null) {
            return null;
        }
        
        ModelVo modelVo = new ModelVo();
        modelVo.setId(editorVo.getId());
        modelVo.setName(editorVo.getName());
        
        // 转换子模型列表
        if (editorVo.get_children() != null && !editorVo.get_children().isEmpty()) {
            List<ModelVo> subModelList = convertChildrenToSubModels(editorVo.get_children(), editorVo.getId());
            modelVo.setSubModelList(subModelList);
            log.info("转换了 {} 个子模型", subModelList.size());
        }
        
        return modelVo;
    }
    
    /**
     * 递归转换子模型列表
     */
    private static List<ModelVo> convertChildrenToSubModels(List<EditorVo> children, String parentId) {
        List<ModelVo> subModels = new ArrayList<>();
        
        for (EditorVo child : children) {
            ModelVo subModel = new ModelVo();
            subModel.setId(child.getId());
            subModel.setName(child.getName());
            subModel.setParentId(parentId);
            
            // 递归处理更深层的子模型
            if (child.get_children() != null && !child.get_children().isEmpty()) {
                List<ModelVo> nestedSubModels = convertChildrenToSubModels(child.get_children(), child.getId());
                subModel.setSubModelList(nestedSubModels);
                log.debug("子模型 {} 包含 {} 个嵌套子模型", child.getName(), nestedSubModels.size());
            }
            
            subModels.add(subModel);
        }
        
        return subModels;
    }

}