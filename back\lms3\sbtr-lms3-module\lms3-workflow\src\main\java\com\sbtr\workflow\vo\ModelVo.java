package com.sbtr.workflow.vo;


import java.io.Serializable;


/**
 * 流程模型保存
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2019-08-16 17:35:23
 */
public class ModelVo implements Serializable {
    //流程id
    private String processId;
    //流程名称
    private String processName;
    //流程的xml
    private String xml;
    // 流程模型类型  1 自定义流程界面  2 托拉拽界面
    private String processModelType;

    public String getProcessModelType() {
        return processModelType;
    }

    public void setProcessModelType(String processModelType) {
        this.processModelType = processModelType;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getXml() {
        return xml;
    }

    public void setXml(String xml) {
        this.xml = xml;
    }

}
