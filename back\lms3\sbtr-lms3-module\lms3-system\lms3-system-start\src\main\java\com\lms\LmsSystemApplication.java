package com.lms;

import com.lms.common.util.ConstParamUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;


/**
 *  系统模块启动类
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2024-07-16 13:31:39
 */
@SpringBootApplication
@ComponentScan(basePackages = {ConstParamUtil.commonScanPackage,ConstParamUtil.systemScanPackage,ConstParamUtil.commonFeignPackage})
@EnableFeignClients(basePackages = {ConstParamUtil.commonFeignPackage})
public class LmsSystemApplication {
    public static void main(String[] args) {
        SpringApplication.run(LmsSystemApplication.class, args);
    }
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder){
        return builder.setConnectTimeout(Duration.ofSeconds(5))
                .setReadTimeout(Duration.ofSeconds(5))
                .build();
    }

}
