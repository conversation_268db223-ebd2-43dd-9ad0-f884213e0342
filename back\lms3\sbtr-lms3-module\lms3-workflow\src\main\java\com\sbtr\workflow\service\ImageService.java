package com.sbtr.workflow.service;

import java.io.InputStream;

public interface ImageService {

    //https://github.com/wellzhi/springboot-flowable/tree/master/src/main/java/com/dapeng/flow/repository/service/impl
    /**
     * 根据流程实例标识，生成流程跟踪图示（高亮）
     *
     * @param procInstId 流程实例标识
     * @return
     * @throws Exception
     */
    InputStream generateImageByProcInstId(String procInstId) throws Exception;
}
