package com.lms.base.courseware.service;

import com.lms.common.feign.dto.FlowModelDto;
import com.lms.base.feign.model.Courseware;
import com.lms.common.feign.dto.ModleDto;
import com.lms.common.service.BaseService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface CoursewareService extends BaseService<Courseware> {

    List<Courseware> getCoursewareList(String componentid);

    List<Courseware> getCourseWareByCourseIdList(List<String> idList);

    List<Map<String, Object>> getCoursewareAnalysisByType(String analysetype);

    Boolean existCoursewareNo(Courseware courseware);

    int getMaxSeqnoByCourse(String componentid);

    int getTotalCourseware();

    HashMap<String, Object> saveFlowchart(FlowModelDto flowModelDto);

    HashMap<String, Object> getFlowchart(String id);

    HashMap<String, Object> updateFlowchart(ModleDto modleDto);

    HashMap<String, Object> deleteFlowchart(String actDeModelId);
}
