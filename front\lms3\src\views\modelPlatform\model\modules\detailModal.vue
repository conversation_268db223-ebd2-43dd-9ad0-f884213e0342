<script setup lang="ts">
import {
  ref,
  reactive,
  computed,
  watch,
  onMounted,
  onUnmounted,
  nextTick
} from "vue";
import { useAppStoreHook } from "@/store/modules/app";
import { message } from "ant-design-vue";
import {
  FileTextOutlined,
  FileOutlined,
  DownloadOutlined,
  CloudUploadOutlined,
  CloseOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
  PlusOutlined,
  BorderOutlined,
  EditOutlined,
  AppstoreOutlined,
  PictureOutlined,
  PlayCircleOutlined,
  WarningOutlined,
  EyeOutlined,
  FullscreenOutlined
} from "@ant-design/icons-vue";
import type { FormInstance } from "ant-design-vue";
import type { Model } from "@/types/model";
import ModelChart from "./modelChart.vue";

interface Property {
  id: number | null;
  modelId: number | null;
  name: string;
  relationship: string;
  value: string;
}

interface Props {
  visible: boolean;
  model: Model | null;
}

interface Emits {
  (e: "update:visible", visible: boolean): void;
  (e: "save-success"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const appStore = useAppStoreHook();

const formRef = ref<FormInstance>();
const loading = ref(false);
const modelIframe = ref<HTMLIFrameElement>();
const fullscreenIframe = ref<HTMLIFrameElement>();
const fullscreenVisible = ref(false);
const previewVisible = ref(false);
const iframeLoading = ref(false);
const iframeError = ref(false);
const iframeWrapper = ref<HTMLDivElement>();
const windowWidth = ref(window.innerWidth);
const windowHeight = ref(window.innerHeight);
const scaleMode = ref("fit");
const showScrollbars = ref(false);

const chartModalVisible = ref(false);

// 当前iframe实例
let currentIframe: HTMLIFrameElement | null = null;

const defaultImage = "";

// 原始iframe尺寸
const ORIGINAL_WIDTH = 1920;
const ORIGINAL_HEIGHT = 1080;
// 表单数据
// const formData = reactive({
//   id: "",
//   name: "",
//   number: "",
//   desc: "",
//   previewUrl: "",
//   fileUrl: "",
//   uploadTime: "",
//   uploadPerson: ""
// });
const formData = reactive({
  name: "",
  number: "",
  desc: "",
  uploadPerson: ""
});

// 属性数据
const properties = ref<Property[]>([]);

// 表单验证规则
const rules = {
  // name: [{ required: true, message: "请输入模型名称" }]
  // number: [{ required: true, message: "请输入编号" }],
  // desc: [{ required: true, message: "请输入描述" }],
  // uploadPerson: [{ required: true, message: "请输入上传人" }]
};

// 属性表格列定义
const propertyColumns = [
  {
    title: "属性名",
    dataIndex: "name",
    key: "name",
    width: "34%"
  },
  {
    title: "属性值",
    dataIndex: "value",
    key: "value",
    width: "33%"
  },
  {
    title: "属性关系",
    dataIndex: "relationship",
    key: "relationship",
    width: "33%"
  }
];

// 计算当前文件URL
const currentFileUrl = computed(() => {
  return props.model?.previewUrl || props.model?.fileUrl || "";
});

// 文件类型判断
const isImageFile = computed(() => {
  if (!currentFileUrl.value) return false;
  const imageExtensions = [
    ".jpg",
    ".jpeg",
    ".png",
    ".gif",
    ".bmp",
    ".webp",
    ".svg"
  ];
  const imageTypes = ["image/"];

  return (
    imageExtensions.some(ext =>
      currentFileUrl.value.toLowerCase().includes(ext)
    ) ||
    imageTypes.some(type =>
      currentFileUrl.value.toLowerCase().includes(type)
    ) ||
    (props.model?.fileUrl &&
      imageTypes.some(type => props.model.fileUrl.toLowerCase().includes(type)))
  );
});

const isVideoFile = computed(() => {
  if (!currentFileUrl.value) return false;
  const videoExtensions = [
    ".mp4",
    ".avi",
    ".mov",
    ".wmv",
    ".flv",
    ".webm",
    ".mkv",
    ".m4v"
  ];
  const videoTypes = ["video/"];

  return (
    videoExtensions.some(ext =>
      currentFileUrl.value.toLowerCase().includes(ext)
    ) ||
    videoTypes.some(type =>
      currentFileUrl.value.toLowerCase().includes(type)
    ) ||
    (props.model?.fileUrl &&
      videoTypes.some(type => props.model.fileUrl.toLowerCase().includes(type)))
  );
});

const is3DModelFile = computed(() => {
  if (!currentFileUrl.value) return false;
  const modelExtensions = [
    ".obj",
    ".fbx",
    ".gltf",
    ".glb",
    ".dae",
    ".3ds",
    ".ply",
    ".stl",
    ".blend",
    ".max",
    ".ma",
    ".mb",
    ".vrp",
    ".vrpc"
  ];
  const modelTypes = ["model/", "application/octet-stream"];

  return (
    modelExtensions.some(ext =>
      currentFileUrl.value.toLowerCase().includes(ext)
    ) ||
    modelTypes.some(type =>
      currentFileUrl.value.toLowerCase().includes(type)
    ) ||
    (props.model?.fileUrl &&
      modelExtensions.some(ext =>
        props.model.fileUrl.toLowerCase().includes(ext)
      ))
  );
});

// 获取文件类型文本
const getFileTypeText = (): string => {
  if (isImageFile.value) return "图片文件";
  if (isVideoFile.value) return "视频文件";
  if (is3DModelFile.value) return "3D模型文件";
  return "其他文件";
};

// 监听弹窗显示状态，初始化数据
watch(
  () => props.visible,
  visible => {
    if (visible && props.model) {
      initFormData();
    }
  }
);

// 监听窗口大小变化
const updateWindowSize = () => {
  windowWidth.value = window.innerWidth;
  windowHeight.value = window.innerHeight;
};

onMounted(() => {
  window.addEventListener("resize", updateWindowSize);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateWindowSize);
  // 清理iframe
  if (currentIframe) {
    currentIframe.remove();
    currentIframe = null;
  }
});

// 计算容器尺寸
const containerWidth = computed(() => {
  return Math.min(windowWidth.value - 80, 1600);
});

const containerHeight = computed(() => {
  return Math.min(windowHeight.value - 160, 900);
});

// 计算实际缩放比例
const actualScale = computed(() => {
  if (scaleMode.value === "fit") {
    const scaleX = containerWidth.value / ORIGINAL_WIDTH;
    const scaleY = containerHeight.value / ORIGINAL_HEIGHT;
    return Math.min(scaleX, scaleY, 1);
  } else {
    return parseInt(scaleMode.value) / 100;
  }
});

// 容器样式
const containerStyle = computed(() => {
  if (showScrollbars.value) {
    return {
      width: `${containerWidth.value}px`,
      height: `${containerHeight.value}px`,
      overflow: "auto"
    };
  } else {
    return {
      width: `${containerWidth.value}px`,
      height: `${containerHeight.value}px`,
      overflow: "hidden",
      display: "flex",
      alignItems: "center",
      justifyContent: "center"
    };
  }
});

// iframe样式
const iframeStyle = computed(() => {
  const scale = actualScale.value;

  return {
    width: `${ORIGINAL_WIDTH}px`,
    height: `${ORIGINAL_HEIGHT}px`,
    transform: `scale(${scale})`,
    transformOrigin: showScrollbars.value ? "top left" : "center center",
    border: "none",
    display: "block",
    background: "white"
  };
});

// 初始化表单数据
const initFormData = () => {
  if (!props.model) return;

  // 基本信息
  // Object.assign(formData, {
  //   id: props.model.id,
  //   name: props.model.name,
  //   number: props.model.number,
  //   desc: props.model.desc,
  //   fileUrl: props.model.fileUrl,
  //   previewUrl: props.model.previewUrl,
  //   uploadPerson: props.model.uploadPerson,
  //   uploadTime: props.model.uploadTime
  // });
  Object.assign(formData, {
    name: props.model.name,
    number: props.model.number,
    desc: props.model.desc,
    uploadPerson: props.model.uploadPerson
  });

  // 属性信息 - 从model.field转换，适应新的数据结构
  if (props.model.attributeList && Array.isArray(props.model.attributeList)) {
    properties.value = props.model.attributeList.map(item => ({
      id: item.id || null,
      modelId: item.modelId || null,
      name: item.name || "",
      relationship: item.relationship || "",
      value: item.value || ""
    }));
  } else {
    // 如果没有属性或格式不正确，初始化空数组
    properties.value = [];
  }
};

// 格式化日期
const formatDate = (dateStr: string | null): string => {
  if (!dateStr) return "未知时间";
  try {
    return new Date(dateStr).toLocaleString("zh-CN");
  } catch {
    return dateStr;
  }
};

// iframe加载完成处理
const handleIframeLoad = () => {
  console.log("🎯 主iframe加载完成");
  iframeLoading.value = false;
  iframeError.value = false;

  // 发送模型数据到iframe
  sendModelDataToIframe(modelIframe.value);

  // 应用缩放样式到主iframe
  // if (modelIframe.value) {
  //   applyIframeScaling(modelIframe.value);
  // }
};

// iframe加载错误处理
const handleIframeError = () => {
  console.error("❌ 主iframe加载失败");
  iframeLoading.value = false;
  iframeError.value = true;
  message.error("3D模型加载失败");
};

// 全屏iframe加载完成处理
const handleFullscreenIframeLoad = () => {
  console.log("🎯 全屏iframe加载完成");

  // 发送模型数据到全屏iframe
  sendModelDataToIframe(fullscreenIframe.value);
};

// 应用iframe缩放样式
const applyIframeScaling = (iframe: HTMLIFrameElement) => {
  if (!iframe) return;

  try {
    // 获取iframe的父容器尺寸
    const container = iframe.parentElement;
    if (!container) return;

    const containerRect = container.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    // 计算缩放比例
    const scaleX = containerWidth / ORIGINAL_WIDTH;
    const scaleY = containerHeight / ORIGINAL_HEIGHT;
    const scale = Math.min(scaleX, scaleY, 1); // 不放大，只缩小

    // 应用缩放样式
    iframe.style.width = `${ORIGINAL_WIDTH}px`;
    iframe.style.height = `${ORIGINAL_HEIGHT}px`;
    iframe.style.transform = `scale(${scale})`;
    iframe.style.transformOrigin = "center center";
    iframe.style.border = "none";
    iframe.style.display = "block";
    iframe.style.background = "white";

    console.log(
      `🔧 应用iframe缩放: ${scale.toFixed(3)} (${containerWidth}x${containerHeight} -> ${ORIGINAL_WIDTH}x${ORIGINAL_HEIGHT})`
    );

    // 向iframe内部发送缩放信息
    setTimeout(() => {
      if (iframe.contentWindow) {
        iframe.contentWindow.postMessage(
          {
            type: "IFRAME_SCALE_UPDATE",
            scale: scale,
            containerWidth: containerWidth,
            containerHeight: containerHeight
          },
          "*"
        );
      }
    }, 500);
  } catch (error) {
    console.error("❌ 应用iframe缩放失败:", error);
  }
};

// 发送模型数据到iframe
const sendModelDataToIframe = (iframe: HTMLIFrameElement | undefined) => {
  if (!iframe || !iframe.contentWindow || !props.model) return;

  // 等待一段时间确保iframe完全加载
  setTimeout(() => {
    const modelDataSrc = props.model.fileUrl;
    try {
      iframe.contentWindow.postMessage(modelDataSrc, "*");
      message.success("模型预览已打开");
    } catch (error) {
      console.error("发送消息失败:", error);
    }
  }, 1000);
};

// 重新加载iframe
const retryLoadIframe = () => {
  if (modelIframe.value) {
    iframeLoading.value = true;
    iframeError.value = false;
    modelIframe.value.src = modelIframe.value.src;
  }
};

// 预览错误处理
const handlePreviewError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = defaultImage;
  console.error("预览加载失败:", event);
};

// 缩略图错误处理
const handleThumbnailError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.style.display = "none";
  console.error("缩略图加载失败:", event);
};

// 下载文件
const downloadFile = () => {
  if (!currentFileUrl.value) {
    message.error("文件地址不存在");
    return;
  }

  try {
    const link = document.createElement("a");
    link.href = currentFileUrl.value;
    link.download = props.model?.name || "download";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    message.success("开始下载文件");
  } catch (error) {
    console.error("下载失败:", error);
    message.error("下载失败");
  }
};

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = defaultImage;
};

// 添加属性 - 禁用状态
const handleAddProperty = () => {
  message.warning("详情模式下不允许添加属性");
};

// 删除属性 - 禁用状态
const handleDeleteProperty = (index: number) => {
  message.warning("详情模式下不允许删除属性");
};

// 保存修改 - 禁用状态
const handleSave = async () => {
  message.warning("详情模式下不允许保存修改");
};

// 构建field对象
// const buildFieldObject = (): Record<string, string> => {
//   const field: Record<string, string> = {};
//   properties.value.forEach(prop => {
//     if (prop.key.trim() && prop.value.trim()) {
//       field[prop.key.trim()] = prop.value.trim();
//     }
//   });
//   return field;
// };

// 保存修改
// const handleSave = async () => {
//   try {
//     await formRef.value?.validate();
//
//     // 验证属性表格
//     const hasEmptyProperty = properties.value.some(
//       prop => !prop.key.trim() || !prop.value.trim()
//     );
//
//     if (hasEmptyProperty) {
//       message.error("请完善所有属性信息");
//       return;
//     }
//
//     loading.value = true;
//
//     // 构建提交数据
//     const submitData = {
//       ...formData,
//       field: buildFieldObject()
//     };
//
//     // 调用更新模型接口
//     if (props.model) {
//       await updateModel(submitData);
//       message.success("模型保存成功");
//       emit("save-success");
//       handleCancel();
//     }
//   } catch (error) {
//     console.error("保存模型失败:", error);
//     message.error("保存模型失败");
//   } finally {
//     loading.value = false;
//   }
// };
// 打开知识图谱
const openKnowledgeGraph = () => {
  // message.info("知识图谱功能开发中...");
  chartModalVisible.value = true;
  appStore.TOGGLE_SIDEBAR(false, "resize"); // 强制关闭侧边栏
};

// 创建iframe
const createIframe = (): HTMLIFrameElement => {
  const iframe = document.createElement("iframe");
  iframe.src = "/player/data/GUI/web.html";
  iframe.style.cssText = `
    width: ${ORIGINAL_WIDTH}px;
    height: ${ORIGINAL_HEIGHT}px;
    border: none;
    display: block;
    background: white;
  `;
  iframe.setAttribute("frameborder", "0");
  iframe.setAttribute("allow", "fullscreen");
  iframe.setAttribute("allowfullscreen", "true");
  iframe.setAttribute("scrolling", showScrollbars.value ? "auto" : "no");

  return iframe;
};

// 打开模型预览 - 独立容器方式
const openModelPreview = () => {
  console.log(props.model.fileUrl);
  if (!props.model) {
    message.error("模型信息不存在");
    return;
  }

  previewVisible.value = true;
  iframeLoading.value = true;
  appStore.TOGGLE_SIDEBAR(false, "resize"); // 强制关闭
  nextTick(() => {
    updateWindowSize();

    // 动态创建iframe
    if (iframeWrapper.value) {
      // 清理之前的iframe
      if (currentIframe) {
        currentIframe.remove();
      }

      // 创建新的iframe
      currentIframe = createIframe();

      // iframe加载完成事件
      currentIframe.onload = () => {
        iframeLoading.value = false;

        // 等待一段时间确保iframe完全加载
        setTimeout(() => {
          if (currentIframe && currentIframe.contentWindow && props.model) {
            const modelDataSrc = props.model.fileUrl;
            // const modelDataSrc =
            //   "https://oss.dianclouds.vrp3d.com/project/1432/2025/02/25/16/19/35/%E5%86%9C%E7%94%A8%E6%9C%BA%E5%B1%95%E7%A4%BA%E6%A1%88%E4%BE%8B1.vrpc?sign=f0f7b84c2ec179e858dca8aaf66b9e3c&time=684AD5E5";
            try {
              currentIframe.contentWindow.postMessage(modelDataSrc, "*");
              message.success("模型预览已打开");
            } catch (error) {
              console.error("发送消息失败:", error);
            }
          }
        }, 1000);
      };

      // iframe加载错误事件
      currentIframe.onerror = () => {
        iframeLoading.value = false;
        message.error("模型预览加载失败");
      };

      // 将iframe添加到容器
      iframeWrapper.value.appendChild(currentIframe);
    }
  });
};

// 关闭模型预览
const closeModelPreview = () => {
  previewVisible.value = false;
  iframeLoading.value = false;

  // 清理iframe
  if (currentIframe) {
    currentIframe.remove();
    currentIframe = null;
  }
};

// 打开全屏预览
const openFullscreenPreview = () => {
  if (!props.model) {
    message.error("模型信息不存在");
    return;
  }

  fullscreenVisible.value = true;

  nextTick(() => {
    updateWindowSize();
  });
};

// 打开模型编辑器 - 新标签页方式
const openModelEditor = () => {
  if (!props.model) {
    message.error("模型信息不存在");
    return;
  }

  // 构建编辑器URL
  const editorUrl = "/editor/data/GUI/STUDIO.html";

  // 打开新标签页
  const newWindow = window.open(editorUrl, "_blank");

  if (newWindow) {
    // 等待新窗口加载完成后发送模型数据
    const checkWindow = setInterval(() => {
      try {
        if (
          newWindow.document &&
          newWindow.document.readyState === "complete"
        ) {
          clearInterval(checkWindow);

          // 发送模型数据到新窗口
          const modelData = {};

          newWindow.postMessage(modelData, "*");
          message.success("模型编辑器已在新标签页中打开");
        }
      } catch (error) {
        // 跨域限制，使用延时发送
        if (Date.now() - startTime > 3000) {
          // 3秒后超时
          clearInterval(checkWindow);

          // 延时发送消息
          setTimeout(() => {
            const modelData = {
              type: "MODEL_EDIT_DATA",
              data: {
                id: props.model!.id,
                name: props.model!.name,
                fileUrl: props.model!.fileUrl,
                previewUrl: props.model!.previewUrl,
                mode: "edit"
              }
            };
            newWindow.postMessage(modelData, "*");
          }, 1000);

          message.success("模型编辑器已在新标签页中打开");
        }
      }
    }, 100);

    const startTime = Date.now();
  } else {
    message.error("无法打开新标签页，请检查浏览器设置");
  }
};

const handleScaleModeChange = () => {
  if (scaleMode.value !== "fit") {
    const scale = parseInt(scaleMode.value) / 100;
    const needsScrollbars =
      ORIGINAL_WIDTH * scale > containerWidth.value ||
      ORIGINAL_HEIGHT * scale > containerHeight.value;
    showScrollbars.value = needsScrollbars;
  } else {
    showScrollbars.value = false;
  }

  // 更新iframe样式
  if (currentIframe) {
    const scale = actualScale.value;
    currentIframe.style.transform = `scale(${scale})`;
    currentIframe.style.transformOrigin = showScrollbars.value
      ? "top left"
      : "center center";
    currentIframe.setAttribute(
      "scrolling",
      showScrollbars.value ? "auto" : "no"
    );
  }
};

const handleScrollbarChange = () => {
  // 更新iframe滚动条设置
  if (currentIframe) {
    currentIframe.setAttribute(
      "scrolling",
      showScrollbars.value ? "auto" : "no"
    );
  }
};

// 取消操作
const handleCancel = () => {
  emit("update:visible", false);
};
</script>

<template>
  <a-modal
    :open="visible"
    :title="null"
    :footer="null"
    width="1200px"
    centered
    class="model-detail-modal"
    @cancel="handleCancel"
  >
    <div class="modal-content">
      <!-- 左侧图片区域 -->
      <!-- <div class="modal-image">
        <img
          :src="model?.previewUrl || model?.fileUrl || defaultImage"
          :alt="model?.name"
          @error="handleImageError"
        />
      </div> -->
      <!-- 左侧预览区域 - 调整为70%宽度，预览图占满整个区域 -->
      <div class="modal-preview">
        <!-- 图片预览 -->
        <div v-if="isImageFile" class="image-preview">
          <img
            :src="currentFileUrl"
            :alt="model?.name"
            class="preview-image"
            @error="handlePreviewError"
          />
          <div class="preview-overlay">
            <div class="file-type-badge image-badge">
              <PictureOutlined />
              图片
            </div>
          </div>
        </div>

        <!-- 视频预览 -->
        <div v-else-if="isVideoFile" class="video-preview">
          <video
            :src="currentFileUrl"
            class="preview-video"
            controls
            preload="metadata"
            @error="handlePreviewError"
          >
            您的浏览器不支持视频播放
          </video>
          <div class="preview-overlay">
            <div class="file-type-badge video-badge">
              <PlayCircleOutlined />
              视频
            </div>
          </div>
        </div>

        <!-- 3D模型预览 -->
        <div v-else-if="is3DModelFile" class="model-preview">
          <div class="model-preview-container">
            <!-- 3D模型缩略图或占位符 - 占满整个区域 -->
            <div class="model-thumbnail">
              <!-- 直接嵌入iframe，占满整个区域 -->
              <iframe
                ref="modelIframe"
                src="/player/data/GUI/web.html"
                class="model-iframe scaled-iframe"
                frameborder="0"
                allowfullscreen
                allow="fullscreen"
                scrolling="no"
                @load="handleIframeLoad"
                @error="handleIframeError"
              />

              <!-- 加载遮罩 -->
              <div v-if="iframeLoading" class="iframe-loading-overlay">
                <a-spin size="large" />
                <p>正在加载3D模型...</p>
              </div>

              <!-- 错误遮罩 -->
              <div v-if="iframeError" class="iframe-error-overlay">
                <WarningOutlined class="error-icon" />
                <p>模型加载失败</p>
                <a-button type="primary" size="small" @click="retryLoadIframe">
                  重新加载
                </a-button>
              </div>
            </div>

            <!-- 3D模型操作按钮 - 悬浮在底部 -->
            <!-- <div class="model-actions">
              <a-button
                type="primary"
                class="preview-btn"
                @click="openModelPreview"
              >
                <template #icon>
                  <EyeOutlined />
                </template>
                预览模型
              </a-button>
              <a-button
                type="primary"
                ghost
                class="edit-btn"
                @click="openModelEditor"
              >
                <template #icon>
                  <EditOutlined />
                </template>
                编辑模型
              </a-button>
            </div> -->
          </div>

          <div class="preview-overlay">
            <div class="file-type-badge model-badge">
              <AppstoreOutlined />
              3D模型
            </div>
          </div>

          <!-- 模型控制按钮 - 悬浮在右下角 -->
          <div class="model-controls">
            <!--            <a-button-->
            <!--              type="primary"-->
            <!--              ghost-->
            <!--              class="control-btn"-->
            <!--              @click="openFullscreenPreview"-->
            <!--            >-->
            <!--              <template #icon>-->
            <!--                <FullscreenOutlined />-->
            <!--              </template>-->
            <!--              全屏预览-->
            <!--            </a-button>-->
            <a-button
              type="primary"
              ghost
              class="control-btn"
              @click="openModelEditor"
            >
              <template #icon>
                <EditOutlined />
              </template>
              编辑模型
            </a-button>
          </div>
        </div>

        <!-- 其他文件类型 -->
        <div v-else class="file-preview">
          <div class="file-preview-container">
            <FileOutlined class="file-icon" />
            <p class="file-name">{{ model?.name }}</p>
            <p class="file-url">{{ currentFileUrl }}</p>
            <a-button type="primary" class="download-btn" @click="downloadFile">
              <template #icon>
                <DownloadOutlined />
              </template>
              下载文件
            </a-button>
          </div>

          <div class="preview-overlay">
            <div class="file-type-badge file-badge">
              <FileOutlined />
              文件
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧信息区域 -->
      <div class="modal-info">
        <h2 class="info-title">{{ model?.name }}</h2>
        <!-- 基本信息区域 - 灰色背景 -->
        <div class="basic-info">
          <div class="info-details">
            <div class="info-row">
              <span><strong>编号:</strong> {{ model?.number }}</span>
              <span
                ><strong>描述:</strong> {{ model?.desc || "暂无描述" }}</span
              >
            </div>
            <div class="info-row">
              <span
                ><strong>上传时间:</strong>
                {{ formatDate(model?.uploadTime) }}</span
              >
              <span><strong>上传人:</strong> {{ model?.uploadPerson }}</span>
            </div>
          </div>
        </div>

        <!-- 表单区域 -->
        <!--        <div class="form-section">-->
        <!--          <a-form-->
        <!--            ref="formRef"-->
        <!--            :model="formData"-->
        <!--            :rules="rules"-->
        <!--            layout="horizontal"-->
        <!--            :label-col="{ span: 6 }"-->
        <!--            :wrapper-col="{ span: 18 }"-->
        <!--          >-->
        <!--            <div class="form-vertical">-->
        <!--              <a-form-item label="模型名称" name="name">-->
        <!--                <a-input-->
        <!--                  v-model:value="formData.name"-->
        <!--                  placeholder="请输入模型名称"-->
        <!--                  :disabled="true"-->
        <!--                  class="disabled-input"-->
        <!--                />-->
        <!--              </a-form-item>-->

        <!--              &lt;!&ndash; <a-form-item label="编号" name="number" disabled>-->
        <!--                <a-input-->
        <!--                  v-model:value="formData.number"-->
        <!--                  placeholder="请输入编号"-->
        <!--                />-->
        <!--              </a-form-item> &ndash;&gt;-->

        <!--              <a-form-item label="描述" name="desc">-->
        <!--                <a-textarea-->
        <!--                  v-model:value="formData.desc"-->
        <!--                  placeholder="请输入描述"-->
        <!--                  :rows="3"-->
        <!--                  :disabled="true"-->
        <!--                  class="disabled-input"-->
        <!--                />-->
        <!--              </a-form-item>-->

        <!--              &lt;!&ndash; <a-form-item label="上传人" name="uploadPerson" disabled>-->
        <!--                <a-input-->
        <!--                  v-model:value="formData.uploadPerson"-->
        <!--                  placeholder="请输入上传人"-->
        <!--                />-->
        <!--              </a-form-item> &ndash;&gt;-->
        <!--            </div>-->
        <!--          </a-form>-->
        <!--        </div>-->
        <!-- 属性表格 -->
        <div class="properties-section">
          <div class="properties-header">
            <h4>模型属性</h4>
            <!-- 添加属性按钮 -->
            <!--            <a-button-->
            <!--              type="primary"-->
            <!--              size="small"-->
            <!--              class="add-property-btn"-->
            <!--              :disabled="true"-->
            <!--              @click="handleAddProperty"-->
            <!--            >-->
            <!--              <template #icon>-->
            <!--                <PlusOutlined />-->
            <!--              </template>-->
            <!--              添加属性-->
            <!--            </a-button>-->
          </div>

          <a-table
            :columns="propertyColumns"
            :data-source="properties"
            :pagination="false"
            size="small"
            class="property-table readonly-table"
            :scroll="{ y: 200 }"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'key'">
                <a-input
                  v-model:value="record.key"
                  placeholder="请输入属性名"
                  size="small"
                  :disabled="true"
                  class="disabled-input"
                />
              </template>
              <template v-else-if="column.key === 'value'">
                <a-input
                  v-model:value="record.value"
                  placeholder="请输入属性值"
                  size="small"
                  :disabled="true"
                  class="disabled-input"
                />
              </template>
              <template v-else-if="column.key === 'relationship'">
                <a-select
                  v-model:value="record.relationship"
                  placeholder="请选择关系"
                  size="small"
                  class="w-full disabled-input"
                  :disabled="true"
                >
                  <a-select-option value="拥有">拥有</a-select-option>
                  <a-select-option value="位于">位于</a-select-option>
                  <a-select-option value="附有">附有</a-select-option>
                  <a-select-option value="包含">包含</a-select-option>
                  <a-select-option value="属于">属于</a-select-option>
                </a-select>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button
                  type="text"
                  danger
                  size="small"
                  :disabled="true"
                  class="disabled-btn"
                  @click="handleDeleteProperty(index)"
                >
                  <template #icon>
                    <DeleteOutlined />
                  </template>
                </a-button>
              </template>
            </template>
          </a-table>
        </div>

        <!-- 底部按钮 -->
        <div class="modal-actions">
          <!-- <a-button @click="handleCancel">取消</a-button> -->
          <!-- <a-button type="primary" ghost @click="openModelPreview"
            >模型预览</a-button
          > -->
          <a-button type="primary" ghost @click="openKnowledgeGraph">
            查看知识图谱</a-button
          >
          <!-- <a-button type="primary" @click="openModelEditor">模型编辑</a-button> -->
          <!--          <a-button type="primary" :loading="loading" @click="handleSave"-->
          <!--            >保存</a-button-->
          <!--          >-->
        </div>
      </div>
    </div>

    <!-- 独立的iframe容器 - 在modal外部 -->
    <div
      v-if="previewVisible"
      class="iframe-overlay"
      @click="closeModelPreview"
    >
      <div class="iframe-container" @click.stop>
        <!-- 关闭按钮 -->
        <div class="iframe-header">
          <span class="iframe-title">{{ model?.name }} - 模型预览</span>
          <div class="iframe-controls">
            <!-- 缩放控制 -->
            <!-- <div class="control-group">
              <span>缩放:</span>
              <a-select
                v-model:value="scaleMode"
                size="small"
                class="scale-select"
                @change="handleScaleModeChange"
              >
                <a-select-option value="fit">适应</a-select-option>
                <a-select-option value="50">50%</a-select-option>
                <a-select-option value="75">75%</a-select-option>
                <a-select-option value="100">100%</a-select-option>
                <a-select-option value="125">125%</a-select-option>
                <a-select-option value="150">150%</a-select-option>
              </a-select>
            </div> -->

            <!-- 滚动条控制 -->
            <!-- <div class="control-group">
              <a-checkbox
                v-model:checked="showScrollbars"
                size="small"
                @change="handleScrollbarChange"
              >
                显示滚动条
              </a-checkbox>
            </div> -->

            <a-button type="text" class="close-btn" @click="closeModelPreview">
              <CloseOutlined />
            </a-button>
          </div>
        </div>

        <!-- iframe内容区域 -->
        <div
          class="iframe-content"
          :class="{ 'with-scrollbars': showScrollbars }"
          :style="containerStyle"
        >
          <!-- 加载状态 -->
          <div v-if="iframeLoading" class="iframe-loading">
            <a-spin size="large" />
            <p>正在加载模型预览应用...</p>
          </div>

          <!-- 动态创建的iframe -->
          <div
            v-show="!iframeLoading"
            ref="iframeWrapper"
            class="iframe-wrapper"
            :style="iframeStyle"
          />
        </div>
      </div>
    </div>

    <!-- 模型图表预览模态框 -->
    <a-modal
      v-model:open="chartModalVisible"
      title="知识图谱"
      width="800px"
      centered
      ok-text="确认"
      cancel-text="取消"
      :footer="null"
      @cancel="() => (chartModalVisible = false)"
    >
      <ModelChart :model="props.model" />
    </a-modal>
  </a-modal>
</template>

<style lang="scss" scoped>
.modal-content {
  display: flex;
  height: 600px;
}

.modal-image {
  width: 65%;
  // background-color: var(--gray-100);
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

.modal-preview {
  width: 65%;
  background-color: var(--gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius-md);
}

.preview-overlay {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
  z-index: 10;
}

.file-type-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: white;

  &.image-badge {
    background-color: #52c41a;
  }

  &.video-badge {
    background-color: #1890ff;
  }

  &.model-badge {
    background-color: #722ed1;
  }

  &.file-badge {
    background-color: var(--gray-500);
  }
}

// 图片预览样式 - 占满整个区域
.image-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover; // 改为cover让图片占满整个区域
    border-radius: var(--border-radius-md);
  }
}

// 视频预览样式 - 占满整个区域
.video-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .preview-video {
    width: 100%;
    height: 100%;
    object-fit: cover; // 改为cover让视频占满整个区域
    border-radius: var(--border-radius-md);
  }
}

// 3D模型预览样式 - 占满整个区域
.model-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .model-preview-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .model-thumbnail {
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    position: relative;
    background-color: #f5f5f5;

    .model-iframe {
      width: 100%;
      height: 100%;
      border: none;
      display: block;
      background: white;

      // 添加缩放样式类
      &.scaled-iframe {
        transform-origin: center center;
        transition: transform 0.3s ease;
      }
    }

    .thumbnail-image {
      width: 100%;
      height: 100%;
      object-fit: cover; // 改为cover让缩略图占满整个区域
    }

    .model-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-4);
      text-align: center;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

      .model-icon {
        font-size: 80px;
        color: var(--primary-color);
      }

      .model-text {
        font-size: var(--text-xl);
        font-weight: var(--font-medium);
        color: var(--gray-700);
        margin: 0;
      }

      .model-name {
        font-size: var(--text-base);
        color: var(--gray-500);
        margin: 0;
        word-break: break-all;
        max-width: 80%;
      }
    }
    // 加载遮罩
    .iframe-loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.9);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-4);
      z-index: 20;

      p {
        color: var(--gray-600);
        margin: 0;
        font-size: var(--text-sm);
      }
    }

    // 错误遮罩
    .iframe-error-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.95);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-3);
      z-index: 20;

      .error-icon {
        font-size: 48px;
        color: var(--error-color);
      }

      p {
        color: var(--error-color);
        margin: 0;
        font-size: var(--text-base);
        font-weight: var(--font-medium);
      }
    }
  }

  .model-actions {
    position: absolute;
    bottom: var(--spacing-4);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: var(--spacing-3);
    background-color: rgba(255, 255, 255, 0.9);
    padding: var(--spacing-3);
    border-radius: var(--border-radius-md);
    backdrop-filter: blur(8px);
    box-shadow: var(--shadow-md);

    .preview-btn,
    .edit-btn {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }
  }

  // 模型控制按钮 - 悬浮在右下角
  .model-controls {
    position: absolute;
    bottom: var(--spacing-4);
    right: var(--spacing-4);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    z-index: 15;

    .control-btn {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      background-color: rgba(255, 255, 255, 0.9);
      border-color: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(8px);
      box-shadow: var(--shadow-sm);

      &:hover {
        background-color: rgba(255, 255, 255, 1);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
      }
    }
  }
}

// 文件预览样式 - 占满整个区域
.file-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .file-preview-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-4);
    text-align: center;
    padding: var(--spacing-6);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    .file-icon {
      font-size: 80px;
      color: white;
    }

    .file-name {
      font-size: var(--text-xl);
      font-weight: var(--font-medium);
      color: white;
      margin: 0;
      word-break: break-all;
    }

    .file-url {
      font-size: var(--text-sm);
      color: rgba(255, 255, 255, 0.8);
      margin: 0;
      word-break: break-all;
      max-width: 80%;
    }

    .download-btn {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      background-color: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.3);
      color: white;

      &:hover {
        background-color: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}

.modal-info {
  width: 35%;
  padding: var(--spacing-6);
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.basic-info {
  background-color: var(--gray-100);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-4);
}

.info-title {
  font-size: var(--text-xl);
  font-weight: var(--font-medium);
  color: var(--gray-900);
  margin: 0 0 var(--spacing-3) 0;
}

.info-details {
  font-size: var(--text-sm);
  color: var(--gray-600);

  .info-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-2);

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.form-section {
  margin-bottom: var(--spacing-4);
}

.form-vertical {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.properties-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: var(--spacing-4);
}

.properties-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);

  h4 {
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    color: var(--gray-900);
    margin: 0;
  }
}

.add-property-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-1);

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.property-table {
  flex: 1;

  &.readonly-table {
    opacity: 0.8;
  }
}

.modal-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-3);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--gray-200);
}

// 禁用状态样式
.disabled-input {
  :deep(.ant-input),
  :deep(.ant-textarea),
  :deep(.ant-select-selector) {
    background-color: var(--gray-100) !important;
    color: var(--gray-500) !important;
    cursor: not-allowed !important;
    border-color: var(--gray-300) !important;

    &:hover {
      border-color: var(--gray-300) !important;
    }

    &:focus {
      border-color: var(--gray-300) !important;
      box-shadow: none !important;
    }
  }
}

.disabled-btn {
  opacity: 0.3;
  cursor: not-allowed;

  &:hover {
    background-color: transparent !important;
  }
}

// 独立iframe容器样式
.iframe-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.iframe-container {
  background-color: white;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  max-width: 100%;
  max-height: 100%;
  display: flex;
  flex-direction: column;
}

.iframe-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) var(--spacing-6);
  background-color: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
}

.iframe-title {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--gray-900);
}

.iframe-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.control-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--text-sm);
  color: var(--gray-600);
}

.scale-select {
  width: 96px;
}

.close-btn {
  color: var(--gray-500);

  &:hover {
    color: var(--gray-700);
    background-color: var(--gray-100);
  }
}

.iframe-content {
  position: relative;
  background: var(--gray-50);

  &.with-scrollbars {
    overflow: auto !important;
  }
}

.iframe-wrapper {
  transition: transform var(--transition-slow);
}

.iframe-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;

  p {
    color: var(--gray-500);
    margin-top: var(--spacing-4);
    margin-bottom: 0;
  }
}

// 属性表格样式
:deep(.property-table .ant-table-thead > tr > th) {
  background-color: #eff7fd !important;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
  padding: 8px 12px;
}

:deep(.property-table .ant-table-tbody > tr > td) {
  font-size: var(--text-sm);
  border-bottom: 1px solid var(--gray-200);
  padding: 8px 12px;
}

:deep(.property-table .ant-table-tbody > tr:hover > td) {
  background-color: var(--gray-50);
}

:deep(.property-table .ant-table) {
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

:deep(.property-table .ant-table-container) {
  border: 0;
}

// 表格内输入框样式 - 禁用状态
:deep(.readonly-table .ant-input),
:deep(.readonly-table .ant-select) {
  border: 0;
  box-shadow: none;
  background-color: var(--gray-100) !important;
  color: var(--gray-500) !important;
  cursor: not-allowed;

  &:focus {
    border-color: var(--gray-300) !important;
    box-shadow: none !important;
    background-color: var(--gray-100) !important;
  }
}

:deep(.readonly-table .ant-select-selector) {
  border: 0;
  box-shadow: none;
  background-color: var(--gray-100) !important;
  cursor: not-allowed;
}

:deep(.readonly-table .ant-select-focused .ant-select-selector) {
  border-color: var(--gray-300) !important;
  box-shadow: none !important;
  background-color: var(--gray-100) !important;
}

// 表单样式
:deep(.ant-form-item) {
  margin-bottom: 0;
}

:deep(.ant-form-item-label) {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
}

:deep(.ant-input),
:deep(.ant-textarea) {
  width: 100%;
}

// 删除按钮样式 - 禁用状态
:deep(.ant-btn-text.ant-btn-dangerous.disabled-btn) {
  color: var(--gray-300) !important;

  &:hover {
    color: var(--gray-300) !important;
    background-color: transparent !important;
  }
}

// 弹窗样式覆盖
:deep(.model-detail-modal .ant-modal-content) {
  padding: 0;
  overflow: hidden;
}

:deep(.model-detail-modal .ant-modal-body) {
  padding: 0;
}

:deep(.model-detail-modal .ant-modal-close) {
  top: var(--spacing-4);
  right: var(--spacing-4);
  z-index: 10;
}

:deep(.model-detail-modal .ant-modal-close-x) {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-500);

  &:hover {
    color: var(--gray-700);
  }
}
</style>
