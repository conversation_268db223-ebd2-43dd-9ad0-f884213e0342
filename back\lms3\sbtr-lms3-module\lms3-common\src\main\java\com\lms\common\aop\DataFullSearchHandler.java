package com.lms.common.aop;

import cn.hutool.core.util.ObjectUtil;
import com.lms.common.feign.api.CommonBaseApi;
import com.lms.common.feign.dto.Selectitem;
import com.lms.common.model.*;
import com.lms.common.service.FileService;
import com.lms.common.util.BeanUtils;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.DateHelper;
import com.lms.common.util.StringHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

@Aspect
@Component
public class DataFullSearchHandler {

    @Resource
    private FileService fileService;

    @Resource
    private CommonBaseApi commonBaseApi;

    /**
     * 配置织入点
     */
    @Pointcut("@annotation(com.lms.common.model.DataSearch)")
    public void logPointCut() {
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "logPointCut()", returning = "jsonResult")
    public void doDataToFile(JoinPoint joinPoint, Object jsonResult) throws Exception {
        Object result = null;
        // 拦截的方法参数
        Object[] args = joinPoint.getArgs();
        // 拦截的放参数类型
        Signature sig = joinPoint.getSignature();
        MethodSignature msig = null;
        if (!(sig instanceof MethodSignature)) {
            throw new IllegalArgumentException("该注解只能用于方法");
        }
        msig = (MethodSignature) sig;
        // 获得被拦截的方法
        Method method = msig.getMethod();
        // 判断是否包含自定义的注解，说明一下这里的DataToFile就是我自己自定义的注解
        if (method.isAnnotationPresent(DataSearch.class)) {
            Object objects = args[0];
            List<File> fileList = new ArrayList<>();
            if (objects instanceof BaseModel) {
                File file = createDataFullSearch(method, objects);
                if (StringUtils.isNotEmpty(file.getFileName()) && StringUtils.isNotEmpty(file.getContent())) {
                    fileService.save(file);
                }
            } else if (objects instanceof List) {
                for (Object object : (List) objects) {
                    File file = createDataFullSearch(method, object);
                    if (StringUtils.isNotEmpty(file.getFileName()) && StringUtils.isNotEmpty(file.getContent())) {
                        fileList.add(file);
                    }
                }
                fileService.saveAll(fileList);
            }
        }
    }

    private File createDataFullSearch(Method method, Object object) throws Exception {
        ApiModel apiModel = object.getClass().getAnnotation(ApiModel.class);
        String fileCategory = ObjectUtil.isEmpty(apiModel) ? object.getClass().getSimpleName() : apiModel.value();
        Field[] fields = object.getClass().getDeclaredFields();
        String fileName = "";
        StringBuilder fileContent = new StringBuilder();
        Field mlevelField = object.getClass().getField("mlevel");
        String mlevel = StringHelper.null2String(Optional.ofNullable(mlevelField.get(object)).orElse(20));
        for (Field field : fields) {
            SearchName searchName = field.getAnnotation(SearchName.class);
            if (searchName != null) {
                fileName = StringHelper.null2String(BeanUtils.getValueByName(object, field.getName()));
            }
            SearchContent searchContent = field.getAnnotation(SearchContent.class);
            if (searchContent != null) {
                String fieldVal = StringHelper.null2String(BeanUtils.getPropertyContentByName(object, field.getName()));
                if (!ObjectUtil.isEmpty(fieldVal)) {
                    fileContent.append(fieldVal);
                }
            }
            SearchSelectItemContent searchSelectItemContent = field.getAnnotation(SearchSelectItemContent.class);
            if (searchSelectItemContent != null) {
                String fieldVal = StringHelper.null2String(BeanUtils.getPropertyValueByName(object, field.getName()));
                if (!ObjectUtil.isEmpty(fieldVal)) {
                    Result<Selectitem> result = commonBaseApi.getSelectitemById(fieldVal);
                    if (result != null && result.getResult() != null) {
                        Selectitem selectitem = result.getResult();
                        String objectName = field.getName();
                        ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
                        objectName = ObjectUtil.isEmpty(apiModelProperty) ? objectName : apiModelProperty.value();
                        fileContent.append("【").append(objectName).append(":").append(selectitem.getObjname()).append("】");
                    }
                }
            }
            // todo 人员或组织 字段翻译 型号
        }
        if (StringUtils.isEmpty(fileName)) {
            fileName = StringHelper.null2String(BeanUtils.getValueByName(object, "number")); //默认为number字段作为检索字段
        }
        String fileId = StringHelper.null2String(BeanUtils.getValueByName(object, "id"));
        File file = null;
        try {
            file = fileService.get(fileId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (file == null) {
            file = new File();
            file.setId(fileId);
            file.setFileName(fileName);
            file.setFileType("database");
            file.setFileCategory(fileCategory);
            file.setCreateBy(ContextUtil.getCurrentUser().getPersonname());
            file.setCreateTime(DateHelper.getCurDateTime());
        } else {
            file.setFileName(fileName);
        }
        if (StringUtils.isNotEmpty(fileContent.toString())){
            file.setContent(Base64.getEncoder().encodeToString(fileContent.toString().getBytes()));
        }else {
            file.setContent("");
        }
        file.setMlevel(Integer.parseInt(mlevel));
        return file;
    }
}
