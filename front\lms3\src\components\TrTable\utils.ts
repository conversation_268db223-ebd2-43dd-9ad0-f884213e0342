import dayjs from "dayjs";

// 状态映射常量
const STATUS_MAPPINGS = {
  positive: ["正常", "启用", "成功", "active", "enabled", "success"],
  negative: ["锁定", "禁用", "失败", "locked", "disabled", "error"],
  warning: ["警告", "warning"],
  processing: ["处理中", "processing"]
} as const;

// 常量定义
export const CONSTANTS = {
  // 默认值
  DEFAULT_ROW_KEY: "id",
  DEFAULT_SCROLL_X: "max-content",

  // 特殊列标识
  INDEX_COLUMN_KEY: "__index",
  OPERATION_COLUMN_KEY: "__operation",

  // 样式类名
  SELECTED_ROW_CLASS: "tr-table-row-selected",

  // 分页选项
  PAGE_SIZE_OPTIONS: ["10", "20", "50", "100"],

  // 默认列配置
  DEFAULT_INDEX_COLUMN: {
    title: "序号",
    dataIndex: "__index",
    key: "__index",
    width: 60,
    fixed: "left" as const,
    align: "center"
  },

  DEFAULT_OPERATION_COLUMN: {
    title: "操作",
    dataIndex: "__operation",
    key: "__operation",
    fixed: "right" as const,
    width: 180,
    align: "center"
  },

  // 状态颜色映射
  STATUS_COLORS: {
    success: "green",
    error: "red",
    warning: "orange",
    processing: "blue",
    default: "default"
  } as const
} as const;

// 时间格式化
export function formatTime(val: any): string {
  return val ? dayjs(val).format("YYYY-MM-DD HH:mm:ss") : "";
}

// 获取行键值
export function getRowKey(
  record: any,
  rowKey: string | ((record: any) => string)
): string {
  return typeof rowKey === "function" ? rowKey(record) : record[rowKey];
}

// 计算分页数据
export function getPagedData<T>(
  data: T[],
  current: number,
  pageSize: number
): T[] {
  const start = (current - 1) * pageSize;
  return data.slice(start, start + pageSize);
}

// 计算序号
export function calculateIndex(
  current: number,
  pageSize: number,
  index: number
): number {
  return (current - 1) * pageSize + index + 1;
}

// 重置查询表单
export function resetQueryForm(queryForm: Record<string, any>): void {
  Object.keys(queryForm).forEach(key => {
    queryForm[key] = undefined;
  });
}

// 检查列是否存在
export function hasColumn(columns: any[], dataIndex: string): boolean {
  return columns.some(col => col.dataIndex === dataIndex);
}

// 获取状态映射
function getStatusMapping(value: any): string {
  if (typeof value === "boolean") return value ? "positive" : "negative";
  if (typeof value === "number") return value === 1 ? "positive" : "negative";
  if (typeof value === "string") {
    const lowerValue = value.toLowerCase();
    for (const [key, values] of Object.entries(STATUS_MAPPINGS)) {
      if (values.includes(lowerValue)) return key;
    }
  }
  return "default";
}

// 获取状态颜色
export function getStatusColor(
  value: any,
  statusMap?: Record<string, { status: string; text: string }>
): string {
  if (statusMap?.[value]) {
    const status = statusMap[value].status;
    return (
      CONSTANTS.STATUS_COLORS[status as keyof typeof CONSTANTS.STATUS_COLORS] ||
      "default"
    );
  }

  const mapping = getStatusMapping(value);
  const colorMap = {
    positive: "green",
    negative: "red",
    warning: "orange",
    processing: "blue",
    default: "default"
  };
  return colorMap[mapping as keyof typeof colorMap];
}

// 获取状态文本
export function getStatusText(
  value: any,
  statusMap?: Record<string, { status: string; text: string }>
): string {
  if (statusMap?.[value]) return statusMap[value].text;

  const mapping = getStatusMapping(value);
  const textMap = {
    positive: "正常",
    negative: "锁定",
    warning: "警告",
    processing: "处理中",
    default: String(value)
  };
  return textMap[mapping as keyof typeof textMap];
}

// 获取组件默认属性
export function getDefaultComponentProps(
  component: string,
  label: string,
  userProps?: Record<string, any>
): Record<string, any> {
  const defaultProps: Record<string, any> = {};

  if (component === "a-input") {
    defaultProps.allowClear = true;
    defaultProps.placeholder = `请输入${label}`;
  } else if (component === "a-select") {
    defaultProps.allowClear = true;
    defaultProps.placeholder = `请选择${label}`;
  }

  return { ...defaultProps, ...userProps };
}
