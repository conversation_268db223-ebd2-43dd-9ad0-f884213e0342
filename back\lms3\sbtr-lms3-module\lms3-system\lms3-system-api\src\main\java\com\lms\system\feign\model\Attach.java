package com.lms.system.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * PMenuId entity. <AUTHOR> Persistence Tools
 */

@EqualsAndHashCode(callSuper = true)

@TableName("b_attach")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "附件实体类")
public class Attach extends BaseModel {

    // Fields

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private String id;

    @TableField("OBJNAME")
    @ApiModelProperty("附件名称")
    private String objname;

    @TableField("ENCRYPTNAME")
    @ApiModelProperty("加密名称")
    private String encryptname;

    @TableField("FILEDIR")
    @ApiModelProperty("访问地址")
    private String filedir;

    @TableField("FILETYPE")
    @ApiModelProperty("文件类型")
    private String filetype;

    @TableField("ISZIP")
    @ApiModelProperty("是否是zip包")
    private Boolean iszip;

    @TableField("ISENCRYPT")
    @ApiModelProperty("是否加密")
    private Boolean isencrypt;

    @TableField("ISDELETE")
    @ApiModelProperty("是否删除")
    private Boolean isdelete;

    @TableField("FILESIZE")
    @ApiModelProperty("文件大小")
    private Long filesize;

    @TableField("ISPUBLIC")
    @ApiModelProperty("是否公开")
    private Boolean ispublic;

    @TableField("UPLOADTIME")
    @ApiModelProperty("上传时间")
    private String uploadtime;

    @TableField("SUFFIX")
    @ApiModelProperty("文件后缀")
    private String suffix;

    @TableField("WIDTH")
    @ApiModelProperty("图片宽度")
    private Integer width;

    @TableField("HEIGHT")
    @ApiModelProperty("图片高度")
    private Integer height;

    @TableField("ISDOWNLOAD")
    @ApiModelProperty("是否下载标志")
    private Boolean isDownload;

    @TableField("FILEDIREXPIRETIME")
    @ApiModelProperty("文件访问过期时间")
    private String filedirexpiretime;

    /**
     * minimal constructor
     */
    public Attach(String id) {
        this.id = id;
    }

    public boolean isOffice() {
        return isWord() || isExcel() || isPowerPoint() || isOffice1();
    }

    public boolean isImage() {
        return this.getFiletype().toLowerCase().indexOf("image") > -1;
    }

    public boolean isWord() {
        boolean isword = this.getFiletype().toLowerCase().indexOf("word") > -1;
        if (!isword && this.objname != null) {
            isword = this.objname.toLowerCase().endsWith(".doc") || this.objname.toLowerCase().endsWith(".docx");
        }
        return isword;
    }

    public boolean isOffice1() {
        return this.getFiletype().toLowerCase().indexOf("office") > -1;
    }

    public boolean isExcel() {
        boolean isexcel = this.getFiletype().toLowerCase().indexOf("excel") > -1;
        if (!isexcel && this.objname != null) {
            isexcel = this.objname.toLowerCase().endsWith(".xls") || this.objname.toLowerCase().endsWith(".xlsx");
        }
        return isexcel;
    }

    public boolean isPowerPoint() {
        return this.getFiletype().toLowerCase().indexOf("powerpoint") > -1;
    }

    public boolean isPDF() {
        boolean ispdf = this.getFiletype().toLowerCase().indexOf("pdf") > -1;
        if (!ispdf && this.objname != null) {
            ispdf = this.objname.toLowerCase().endsWith(".pdf");
        }
        return ispdf;
    }

    public boolean isTxt() {
        boolean istxt = this.getFiletype().toLowerCase().indexOf("text") > -1;
        if (!istxt && this.objname != null) {
            istxt = this.objname.toLowerCase().endsWith(".txt");
        }
        return istxt;
    }

    public boolean isApplication() {//rar、jsp等
        boolean result = this.getObjname().toLowerCase().indexOf("rar") > -1
                || this.getObjname().toLowerCase().indexOf("zip") > -1
                || this.getObjname().toLowerCase().indexOf("jsp") > -1;
        if (!result) {
            result = this.getFiletype().toLowerCase().indexOf("octet-stream") > -1;
            if (result) {
                if (isWord())
                    return false;
                if (isExcel())
                    return false;
                if (isPDF())
                    return false;
                if (isTxt())
                    return false;
            }
        }

        return result;
    }
}
