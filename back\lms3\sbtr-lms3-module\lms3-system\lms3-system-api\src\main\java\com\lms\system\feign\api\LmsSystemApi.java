package com.lms.system.feign.api;

import com.lms.common.config.FeignConfig;
import com.lms.common.feign.dto.Setting;
import com.lms.common.feign.dto.Users;
import com.lms.common.model.JwtUser;
import com.lms.common.model.Result;
import com.lms.system.feign.fallback.LmsSystemApiFallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: quanjinghua
 * @Date: 2024/7/9 19:10
 * @Description: LMS-SYSTEM服务对外接口，对比较多服务使用到的接口，统一在common模块提供接口
 */
@FeignClient(contextId = "lmsSystemApi",value = "lms-system",url = "${feignClient.baseUrl.systemApi:}",
        configuration = FeignConfig.class,fallbackFactory = LmsSystemApiFallbackFactory.class)
public interface LmsSystemApi {

//    @GetMapping(value = {"/setting/getById"})
//    Result<Setting> getById(@RequestParam String id);

    @PostMapping("/users/add")
    Result<Users> saveUser(@RequestBody Users users);

    @PostMapping("/users/saveAllUser")
    Result saveAllUser(@RequestBody List<Users> users);

    @DeleteMapping("/users/batchDeleteByPersonId")
    Result batchDeleteByPersonId(@RequestParam("ids") String ids);

    @GetMapping("/users/info")
    Result<JwtUser> getUserInfo(@RequestParam("userid") String userid);

    @GetMapping("/users/login")
    Result<JwtUser> login(@RequestParam("userName") String userName, @RequestParam("password") String password,
                          @RequestParam(required = false) String ipaddr, @RequestParam(required = false) String clientagent);

    @GetMapping("/users/getUsersByPersonId")
    Result<Users> getUsersByPersonId(@RequestParam String PersonId);

    @GetMapping("/users/getUsersByUserName")
    Result<Users> getUsersByUserName(@RequestParam("userName") String userName);
}
