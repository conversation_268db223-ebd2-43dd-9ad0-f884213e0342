package com.lms.examine.examinestudentrecord.service;

import com.lms.common.service.BaseService;
import com.lms.examine.feign.model.Examinestudentrecord;
import com.lms.examine.feign.model.ExaminestudentrecordViewModel;

import java.util.List;

public interface ExaminestudentrecordService extends BaseService<Examinestudentrecord> {

    List<ExaminestudentrecordViewModel> addTitleSubject(List<Examinestudentrecord> sortList);

    Double createRecordScore(Examinestudentrecord examinestudentrecord);

    List<Examinestudentrecord> getExaminestudentrecordsByExamineId(String examined, String personid);

    double createRecordScore2(Examinestudentrecord examinestudentrecord);

}
