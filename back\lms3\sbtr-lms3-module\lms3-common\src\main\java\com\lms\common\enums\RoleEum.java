package com.lms.common.enums;

import cn.hutool.core.util.ObjectUtil;

/**
 * <AUTHOR>
 * @version V1.0
 * @fileoverview: <br>Copyright @ 2017-2020 SBTR  LTD.
 * <br> This program is protected by copyright laws.
 * <br> Project Name:SBTR-TSS
 * <br> 模块名称:com.lms.util.PlanStatus
 * <br> 功能:计划状态枚举类
 * @create 2017-11-29 8:44
 */
public enum RoleEum {

    empty("", "", 0),
    student("2e55eb426aa84654944ebc0cdbf02af8", "学员", 1),
    teacher("5142f4720518455fbc9c8c21311f20ae", "教员", 5),
    admin("1be79c2956684d97a8f8712588c34026", "培训管理员", 10),
    leader("0b5d47ba0f2046b78577ac2e82e5fb4c", "培训主管部门负责人", 15),
    sysManager("bb83706a3aba4a13acd8593759e76a39", "系统管理员", 50),
    securityOfficer("b5c3192bfb0d44779151689f320381ba", "授权管理员", 60),
    safetyAuditor("2708a87b2c31492fa23d4052c0b852c9", "安全审计员", 70),
    superAdmin("6503783137ab412790e0ff0cbd887a4f", "超级管理员", 100);
    private String value;  //角色ID
    private String label;  //角色名称
    private int code;//角色类型码

    RoleEum(String value, String label, int code) {
        this.value = value;
        this.label = label;
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public static RoleEum getByCode(int code) {
        if (ObjectUtil.isNotEmpty(code)) {
            RoleEum[] roleEums = values();
            for (RoleEum roleEum : roleEums) {
                if (roleEum.getCode() == code) {
                    return roleEum;
                }
            }
        }
        return empty;
    }

    public static RoleEum getByValue(String value) {
        if (ObjectUtil.isNotEmpty(value)) {
            RoleEum[] roleEums = values();
            for (RoleEum roleEum : roleEums) {
                if (roleEum.getValue().equals(value)) {
                    return roleEum;
                }
            }
        }
        return empty;
    }

    public static int getCodeByValue(String value) {
        if (ObjectUtil.isNotEmpty(value)) {
            RoleEum[] roleEums = values();
            for (RoleEum roleEum : roleEums) {
                if (roleEum.getValue().equals(value)) {
                    return roleEum.getCode();
                }
            }
        }
        return 0;
    }

    public static String getValueByCode(int code) {
        if (ObjectUtil.isNotEmpty(code)) {
            RoleEum[] roleEums = values();
            for (RoleEum roleEum : roleEums) {
                if (roleEum.getCode() == code) {
                    return roleEum.getValue();
                }
            }
        }
        return "";
    }
}
