package com.sbtr.workflow.filter;


import cn.ewsd.common.bean.Audience;
import cn.ewsd.common.bean.LoginInfo;
import cn.ewsd.common.bean.UserInfo;
import cn.ewsd.common.utils.CookieUtil;
import cn.ewsd.common.utils.JwtUtil;
import cn.ewsd.common.utils.StringUtils;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.lms.common.util.ContextUtil;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.filter.loginInfo.LoginInfoVo;
import com.sbtr.workflow.filter.loginInfo.UserInfoVo;
import com.sbtr.workflow.utils.BaseUtils;
import com.sbtr.workflow.utils.DefaultSkipUrl;
import io.jsonwebtoken.Claims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;
import org.springframework.web.cors.reactive.CorsUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @MethodName
 * @Description   针对workflow权限拦截
 * 注意
 * 1.请求地址后携带.api默认放开拦截(例如: @PostMapping("/getDisableDaysInfo.api" ))
 * 2.如果放开js/css/或请求地址不携带.api等文件则在 utils ---> DefaultSkipUrl 中增加 DEFAULT_SKIP_URL.add("/v2/api-docs");
 * 3.放开接口地址时需要在gateway(/system/v2/api-docs)项目以及接口所在项目(/v2/api-docs)同时放开
 * <AUTHOR>
 * @Date 2020-11-6 10:28
 */
public class JwtFilter extends OncePerRequestFilter {
    static Logger logger = LoggerFactory.getLogger(JwtFilter.class);

    public JwtFilter() {
    }

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String var4 = request.getRequestURI();
//        if (var4.contains(".api")) {
//            logger.info("请求地址：" + var4 + " 放开拦截");
//            UserInfo var10 = new UserInfo();
//            LoginInfo.add(var10);
//            filterChain.doFilter(request, response);
//        } else {
//            String var5 = request.getHeader("requestType");
//            var5 = var5 == null ? "" : var5;
//            // 不需要认证的请求地址
//            if (!isSkip(var4)) {
                String var7 = getToken(request);
//                if (var5.equals("feign") && StringUtils.isNullOrEmpty(var7)) {
//                    filterChain.doFilter(request, response);
//                } else {
//                    if (StringUtils.isNullOrEmpty(var7)) {
//                        var7 = "eyJ0eXBlIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.eyJ1dWlkIjoiZDI0ZDk1YjI1OGZhNGQ4MmI0OWIwMzg1NmViMjlmZDQiLCJ1c2VyTmFtZUlkIjoiZXdzZDAwMDEiLCJ1c2VyTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIm9yZ0lkIjoiNCIsInJvbGVJZCI6IjM0IiwidGVuYW50VXVpZCI6IjEyMzQ1NiIsImV4cCI6MTcxNDcwMzU4OSwibmJmIjoxNzE0MDk4Nzg5LCJpc3MiOiJzeXN0ZW1Kd3QiLCJhdWQiOiIwOThmNmJjZDQ2MjFkMzczY2FkZTRlODMyNjI3YjRmNiJ9.3Oyw2sbrJiKAIiguUpyF-C0n0LmHPlDcf7lswQoducA";
////                        logger.info("请求地址：" + var4 + " 没有携带token");
//                    }
//                    String var8 = "bearer;" + var7;
//                    if ("OPTIONS".equals(request.getMethod())) {
//                        response.setStatus(200);
//                        filterChain.doFilter(request, response);
//                    } else {
//                        if (!var8.startsWith("bearer;")) {
//                            logger.info("token does not start with bearer;！");
//                        }
//                        try {
//                            if (this.audience == null) {
//                                WebApplicationContext var9 = WebApplicationContextUtils.getRequiredWebApplicationContext(request.getServletContext());
//                                this.audience = (Audience) var9.getBean("audience");
//                            }
//                            String base64Secret = "MDk4ZjZiY2Q0NjIxZDM3M2NhZGU0ZTgzMjYyN2I0ZjY=";
//                            Claims var12 = JwtUtil.parseJWT(var7, base64Secret);
//                            if (var12 == null) {
//                                logger.info("token解析用户数据出现问题");
//                                logger.info("this.audience.getBase64Secret()====" + this.audience.getBase64Secret());
//
//                            }
//                            request.setAttribute("claims", var12);
//                            UserInfo var10 = new UserInfo();
//                            var10.setUuid(Convert.toStr(var12.get("uuid")));
//                            var10.setUserNameId(Convert.toStr(var12.get("userNameId")));
//                            var10.setUserName(Convert.toStr(var12.get("userName")));
//                            var10.setRoleId(Convert.toStr(var12.get("roleId")));
//                            var10.setOrgId(Convert.toStr(var12.get("orgId")));
//                            LoginInfo.add(var10);
//                            //为了支持多租户重写UserInfo LoginInfo
//                            UserInfoVo var11 = new UserInfoVo();
//                            var11.setTenantUuid(null != var12.get("tenantUuid")
//                                    ? Convert.toStr(var12.get("tenantUuid"))
//                                    : null);
//                            LoginInfoVo.add(var11);
//                            response.setHeader("Access-Control-Allow-Origin","*");
//                            response.setHeader("Access-Control-Allow-Methods","GET,POST,DELETE,PUT,OPTIONS");
//                            response.setHeader("Access-Control-Allow-Headers","Origin,X-Requested-With,Content-Type,Accept");
//                            response.setHeader("Access-Control-Max-Age","3600");
//                            filterChain.doFilter(request, response);
//                        } catch (Exception var11) {
//                            logger.info("token解析用户数据出现问题,失败原因为=" + var11.getMessage());
//                        }
//                    }
//                }
//            } else {
//                logger.info("请求地址：" + var4 + " 没有携带token");
//                UserInfo var10 = new UserInfo();
//                LoginInfo.add(var10);
//                filterChain.doFilter(request, response);
//            }
//        }
        // 默认一个登录用户
//        UserInfo user = new UserInfo();
//        user.setUuid("d24d95b258fa4d82b49b03856eb29fd4");
//        user.setUserNameId("cxz");
//        user.setUserName("流程管理员");
////        user.setRoleId("111");
//        user.setOrgId("222");
//        LoginInfo.add(user);
        filterChain.doFilter(request, response);
    }


    //获取Token
    private String getToken(HttpServletRequest request) {
        String var7 = CookieUtil.getCookieByName(request, CommonConstants.X_ACCESS_TOKEN);
        String paramToken = request.getParameter(CommonConstants.X_ACCESS_TOKEN);
        if (!StringUtils.isNullOrEmpty(paramToken)) {
            var7 = paramToken;
        }
        if (StringUtils.isNullOrEmpty(var7)) {
            var7 = request.getHeader(CommonConstants.X_ACCESS_TOKEN);
        }
        try {
            //  特殊处理 swagger模块token问题
            Enumeration<String> enumeration = request.getHeaders("Referer");
            if (null != enumeration) {
                if (StringUtils.isNullOrEmpty(var7) && request.getHeaders("Referer").nextElement().contains("swagger")) {
                    var7 = getParamByUrl(request.getHeaders("Referer").nextElement());
                }
            }
        }catch (Exception e){
            var7 = StrUtil.isBlank(var7) ? null : var7;
        }
        return var7;
    }


    //获取指定url中的某个参数
    public static String getParamByUrl(String url) {
        String regEx = "(\\?|&+)(.+?)=([^&]*)";//匹配参数名和参数值的正则表达式
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(url);
        Map<String, String> paramMap = new LinkedHashMap<String, String>();
        while (m.find()) {
            String paramName = m.group(2);//获取参数名
            String paramVal = m.group(3);//获取参数值
            paramMap.put(paramName, paramVal);
        }
        return paramMap.get(CommonConstants.X_ACCESS_TOKEN);
    }

    //匹配默认跳过请求地址
    private boolean isSkip(String path) {
        return DefaultSkipUrl.getDefaultSkipUrl().stream().anyMatch(pattern -> antPathMatcher.match(pattern, path));
    }

}
