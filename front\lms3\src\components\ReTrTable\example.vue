<template>
  <div class="example-container">
    <ReTrTable
      v-model:table-config="tableConfig"
      v-model:query-config="queryConfig"
      :btn-config="btnConfig"
      :pagination-mode="paginationMode"
      @customButton="handleCustomButton"
    >
      <!-- 自定义渲染 -->
      <!-- <template #status="{ record }">
        {{ record.status === 1 ? "启用" : "禁用" }}
      </template>
      <template #age="{ record }"> {{ record.age }}岁 </template> -->
      <!-- 快速搜索 -->
      <!-- <template #quickSearch>
        <a-form-item label="姓名">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="请输入姓名关键词"
            style="width: 200px"
          />
        </a-form-item>
      </template> -->

      <!-- 高级搜索 -->
      <!-- <template #moreSearch>
        <a-form layout="inline">
          <a-form-item label="状态">
            <a-select v-model:value="searchForm.status" style="width: 120px">
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="1">启用</a-select-option>
              <a-select-option value="0">禁用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="年龄范围">
            <a-input-number
              v-model:value="searchForm.minAge"
              placeholder="最小年龄"
              style="width: 100px"
            />
            <span style="margin: 0 8px">-</span>
            <a-input-number
              v-model:value="searchForm.maxAge"
              placeholder="最大年龄"
              style="width: 100px"
            />
          </a-form-item>
        </a-form>
      </template> -->

      <!-- 自定义操作列 -->
      <!-- <template #operation="{ record, operationButtons }">

        <a-button type="link" size="small" @click="handleView([record])">
          <template #icon><EyeOutlined /></template>
          查看
        </a-button>
        <a-button type="link" size="small" @click="handleEdit([record])">
          <template #icon><EditOutlined /></template>
          编辑
        </a-button>
        <a-button
          type="link"
          size="small"
          danger
          @click="handleDelete([record])"
        >
          <template #icon><DeleteOutlined /></template>
          删除
        </a-button>

        <a-button
          v-for="btn in operationButtons"
          :key="btn.key"
          v-bind="btn.props"
          type="link"
          size="small"
          class="tr-btn custom-op-btn"
          @click="() => handleOperationButton({ key: btn.key, record })"
        >
          <template v-if="btn.icon" #icon>
            <component :is="btn.icon" />
          </template>
          {{ btn.label }}
        </a-button>
      </template> -->
    </ReTrTable>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { message } from "ant-design-vue";
import {
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  DownloadOutlined
} from "@ant-design/icons-vue";
import ReTrTable, { type TableConfig } from "./index";
import type { QueryItem, QueryConfig } from "./types";

// 模拟数据
const mockData = [
  {
    id: 1,
    name: "张三",
    age: 25,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 2,
    name: "李四",
    age: 30,
    status: 0,
    createTime: "2024-01-02 11:00:00"
  },
  {
    id: 3,
    name: "张三",
    age: 25,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 4,
    name: "李四",
    age: 30,
    status: 0,
    createTime: "2024-01-02 11:00:00"
  },
  {
    id: 5,
    name: "张三",
    age: 25,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 6,
    name: "李四",
    age: 30,
    status: 2,
    createTime: "2024-01-02"
  },
  {
    id: 7,
    name: "张三",
    age: 25,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 8,
    name: "李四",
    age: 30,
    status: 0,
    createTime: "2024-01-02 11:00:00"
  },
  {
    id: 9,
    name: "张三",
    age: 25,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 10,
    name: "李四",
    age: 30,
    status: 0,
    createTime: "2024-01-02 11:00:00"
  },
  {
    id: 11,
    name: "张三",
    age: 25,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 12,
    name: "李四",
    age: 30,
    status: 0,
    createTime: "2024-01-02 11:00:00"
  }
];

// 表格配置
const tableConfig = ref<TableConfig>({
  tableData: [],
  total: 0,
  pageIndex: 1,
  pageSize: 10,
  selector: [],
  showSearchBar: true,
  showMoreSearch: true,
  showCheckBox: true,
  showIndex: true,
  showOperateColumn: true,
  OperateColumnWidth: 200,
  operateColumnLabel: "操作",
  stripe: true,
  size: "default",
  sortField: "id",
  sortOrder: "descend",
  displayColumns: [
    {
      label: "姓名",
      prop: "name",
      width: 120,
      fixed: "left"
    },
    {
      label: "年龄",
      prop: "age",
      width: 80,
      align: "center",
      sortable: true,
      render: (text: any, record: any) => `${record.age}岁`
    },
    {
      label: "状态",
      prop: "status",
      width: 100,
      align: "center",
      renderType: "tag",
      statusMap: {
        1: { status: "success", text: "启用", color: "green" },
        0: { status: "error", text: "禁用", color: "red" },
        2: { status: "warning", text: "待审核", color: "orange" }
      }
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: 180,
      renderType: "time"
    }
  ]
});

// 按钮配置
const btnConfig = ref([
  {
    key: "add",
    label: "新增",
    icon: PlusOutlined,
    type: "batch",
    method: () => handleAdd(),
    props: { type: "primary" }
  },
  // 新增：字符串方法名示例
  {
    key: "handleAdd",
    label: "字符串新增",
    icon: PlusOutlined,
    type: "batch",
    method: "handleAdd",
    props: { type: "primary" }
  },
  {
    key: "delete",
    label: "批量删除",
    icon: DeleteOutlined,
    type: "batch",
    method: (rows: any[]) => handleDelete(rows),
    props: { danger: true }
  },
  {
    key: "export",
    label: "导出",
    icon: DownloadOutlined,
    type: "batch",
    method: () => handleCustomButton("export"),
    props: { type: "primary" }
  },
  {
    key: "import",
    label: "导入",
    icon: PlusOutlined,
    type: "batch",
    method: () => handleCustomButton("import"),
    props: { type: "default" }
  },
  // 行操作按钮
  {
    key: "view",
    label: "查看",
    icon: EyeOutlined,
    type: "row",
    method: (row: any) => handleView([row]),
    props: {}
  },
  {
    key: "edit",
    label: "编辑",
    icon: EditOutlined,
    type: "row",
    method: (row: any) => handleEdit([row]),
    props: {}
  },
  {
    key: "deleteRow",
    label: "删除",
    icon: DeleteOutlined,
    type: "row",
    method: (row: any) => handleDelete([row]),
    props: { danger: true }
  },
  {
    key: "customOp",
    label: "自定义操作",
    icon: PlusOutlined,
    type: "row",
    method: (row: any) =>
      handleOperationButton({ key: "customOp", record: row }),
    props: { danger: true }
  }
]);

// 查询项配置（配置模式）
const queryItems: QueryItem[] = [
  {
    key: "keyword",
    label: "姓名",
    component: "a-input"
  },
  {
    key: "status",
    label: "状态",
    component: "a-select",
    props: {
      style: "width: 120px",
      options: [
        { label: "全部", value: "" },
        { label: "启用", value: 1 },
        { label: "禁用", value: 0 }
      ]
    }
  },
  {
    key: "age",
    label: "年龄",
    component: "a-input-number",
    props: { style: "width: 100px" }
  }
];

// 响应式查询配置
const queryConfig = ref<QueryConfig>({
  items: queryItems,
  queryForm: {
    keyword: "",
    status: "",
    age: ""
  },
  onQuery: (
    queryForm: Record<string, any>,
    params?: { sortField?: string; sortOrder?: string }
  ) => {
    console.log("搜索条件:", queryForm, "排序:", params);
    message.info(
      `执行搜索操作${params && params.sortField ? `，排序字段: ${params.sortField}，顺序: ${params.sortOrder}` : ""}`
    );

    // 模拟搜索
    let filteredData = mockData.filter(item => {
      if (queryForm.keyword && !item.name.includes(queryForm.keyword)) {
        return false;
      }
      if (
        queryForm.status !== "" &&
        item.status !== parseInt(queryForm.status)
      ) {
        return false;
      }
      if (
        queryForm.age !== undefined &&
        queryForm.age !== "" &&
        item.age !== queryForm.age
      ) {
        return false;
      }
      return true;
    });

    // 演示前端排序
    if (params && params.sortField) {
      filteredData = filteredData.slice().sort((a, b) => {
        const field = params.sortField;
        const order = params.sortOrder === "ascend" ? 1 : -1;
        if (a[field] > b[field]) return order;
        if (a[field] < b[field]) return -order;
        return 0;
      });
    }

    tableConfig.value.tableData = filteredData;
    tableConfig.value.total = filteredData.length;
    // 搜索后重置到第一页
    tableConfig.value.pageIndex = 1;
  },
  onReset: (form: Record<string, any>) => {
    console.log("重置搜索条件");
    // 直接触发一次空条件查询
    queryConfig.value.onQuery?.({
      keyword: "",
      status: "",
      age: ""
    });
  }
});

// 监听查询配置变化
watch(
  () => queryConfig.value.queryForm,
  newQueryForm => {
    console.log("查询表单变化:", newQueryForm);
  },
  { deep: true }
);

// 监听表格配置中的排序状态变化
watch(
  () => [tableConfig.value.sortField, tableConfig.value.sortOrder],
  ([sortField, sortOrder]) => {
    console.log("排序状态变化:", { sortField, sortOrder });
  }
);

// 分页模式
const paginationMode = ref<"server" | "client">("client");

const handleAdd = () => {
  message.info("执行新增操作");
};

const handleEdit = (rows: any[]) => {
  message.info(`执行编辑操作，选中 ${rows.length} 条数据`);
  console.log("编辑数据:", rows);
};

const handleView = (rows: any[]) => {
  message.info(`执行查看操作，选中 ${rows.length} 条数据`);
  console.log("查看数据:", rows);
};

const handleDelete = (rows: any[]) => {
  message.info(`执行删除操作，选中 ${rows.length} 条数据`);
  console.log("删除数据:", rows);
};

// 自定义按钮示例
// const customButtons = [
//   {
//     key: "export",
//     label: "导出",
//     icon: DownloadOutlined,
//     props: { type: "primary" }
//   },
//   {
//     key: "import",
//     label: "导入",
//     icon: PlusOutlined,
//     props: { type: "default" }
//   }
// ];

// 操作列自定义按钮示例
// const operationButtons = [
//   {
//     key: "customOp",
//     label: "自定义操作",
//     icon: PlusOutlined,
//     props: { danger: true }
//   }
// ];

// 自定义按钮事件
const handleCustomButton = (key: string, data: any) => {
  if (key === "export") {
    message.success("点击了导出");
  } else if (key === "import") {
    message.success("点击了导入");
  } else {
    message.success(`点击了自定义按钮: ${key}`);
  }
};

// 操作列自定义按钮事件
const handleOperationButton = ({
  key,
  record
}: {
  key: string;
  record: any;
}) => {
  message.success(
    `点击了操作列自定义按钮: ${key}, 行数据：${JSON.stringify(record)}`
  );
};

// 初始化数据
onMounted(() => {
  tableConfig.value.tableData = mockData;
  tableConfig.value.total = mockData.length;
  tableConfig.value.pageIndex = 1;
  tableConfig.value.pageSize = 10;

  // 演示：可以通过修改queryConfig.queryForm来设置查询条件
  setTimeout(() => {
    console.log("演示：设置查询条件");
    queryConfig.value.queryForm = {
      keyword: "张三",
      status: "1",
      age: 25
    };
  }, 2000);

  // 演示：可以通过修改tableConfig.sortField/sortOrder来设置排序
  setTimeout(() => {
    console.log("演示：设置排序状态");
    tableConfig.value.sortField = "age";
    tableConfig.value.sortOrder = "ascend";
  }, 4000);
});
</script>

<style scoped>
.example-container {
  height: 100%;
}
</style>
