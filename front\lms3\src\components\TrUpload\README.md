# TrUpload 文件上传组件

基于 Ant Design Vue 的文件上传组件二次封装，集成了项目现有的所有上传功能。

## 功能特性

- 智能上传方式：自动选择分片上传或普通上传
- 文件类型支持：图片、视频、3D模型、文档等
- 实时预览：支持图片、视频预览
- 进度显示：上传进度条和状态提示
- 文件验证：类型、大小、格式验证
- 拖拽上传：支持拖拽文件到上传区域
- 多文件支持：可配置单文件或多文件上传
- 自定义配置：支持自定义上传参数和回调函数

## 基础用法

```vue
<template>
  <TrUpload v-model:fileList="fileList" @change="handleChange" />
</template>

<script setup>
import { ref } from 'vue';
import { TrUpload } from '@/components/TrUpload';
import type { UploadFile } from 'ant-design-vue';

const fileList = ref([]);

const handleChange = (info) => {
  console.log('文件变化:', info);
};
</script>
```

## 高级用法

```vue
<template>
  <TrUpload
    v-model:fileList="fileList"
    :multiple="true"
    :max-size="100"
    :accept="['image/*', 'video/*']"
    :show-preview="true"
    :auto-upload="true"
    :drag="true"
    button-text="选择文件"
    drag-text="点击或拖拽文件到此区域上传"
    hint="支持图片和视频文件"
    @change="handleChange"
    @success="handleSuccess"
    @error="handleError"
    @remove="handleRemove"
    @preview="handlePreview"
  />
</template>

<script setup>
import { ref } from 'vue';
import { TrUpload } from '@/components/TrUpload';
import type { UploadFile } from 'ant-design-vue';

const fileList = ref([]);

const handleChange = (info) => {
  console.log('文件变化:', info);
};

const handleSuccess = (file, response) => {
  console.log('上传成功:', file, response);
};

const handleError = (file, error) => {
  console.log('上传失败:', file, error);
};

const handleRemove = (file) => {
  console.log('文件移除:', file);
};

const handlePreview = (file) => {
  console.log('文件预览:', file);
};
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| fileList | 文件列表 | `UploadFile[]` | `[]` |
| multiple | 是否支持多文件上传 | `boolean` | `false` |
| maxSize | 最大文件大小（MB） | `number` | `100` |
| accept | 接受的文件类型 | `string[]` | `[]` |
| showPreview | 是否显示预览 | `boolean` | `true` |
| autoUpload | 是否自动上传 | `boolean` | `true` |
| showUploadList | 是否显示上传列表 | `boolean` | `false` |
| drag | 是否支持拖拽上传 | `boolean` | `true` |
| disabled | 是否禁用 | `boolean` | `false` |
| loading | 是否加载中 | `boolean` | `false` |
| buttonText | 上传按钮文本 | `string` | `"选择文件"` |
| dragText | 拖拽区域文本 | `string` | `"点击或拖拽文件到此区域上传"` |
| hint | 提示文本 | `string` | `"支持单个文件上传"` |
| placeholder | 占位符文本 | `string` | `-` |
| fileTypes | 文件类型限制 | `string[]` | `[]` |
| customUpload | 自定义上传函数 | `(file: File) => Promise<any>` | `-` |

### Events

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| update:fileList | 文件列表变化 | `(fileList: UploadFile[])` |
| change | 文件变化 | `(info: any)` |
| success | 上传成功 | `(file: UploadFile, response: any)` |
| error | 上传失败 | `(file: UploadFile, error: any)` |
| remove | 文件移除 | `(file: UploadFile)` |
| preview | 文件预览 | `(file: UploadFile)` |

### 方法

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| upload | 手动上传文件 | `(options: any)` |
| removeFile | 移除文件 | `(file: UploadFile)` |

## 文件类型支持

### 图片文件

- 格式：jpg, jpeg, png, gif, bmp, webp, svg
- 预览：支持图片预览

### 视频文件

- 格式：mp4, avi, mov, wmv, flv, webm, mkv
- 预览：支持视频播放

### 3D模型文件

- 格式：obj, fbx, gltf, glb, stl, dae, 3ds, max, blend, vrp, vrpc
- 预览：显示模型信息

### 文档文件

- 格式：pdf, doc, docx, xls, xlsx, ppt, pptx, txt, md
- 预览：显示文件信息

## 上传方式

### 普通上传

- 适用于：小文件（< 10MB）
- 特点：直接上传，速度快

### 分片上传

- 适用于：大文件（≥ 10MB）或特定类型文件（.vrp, .vrpc）
- 特点：分片上传，支持断点续传

## 注意事项

1. 组件会自动根据文件大小和类型选择合适的上传方式
2. 支持的文件类型可以通过 `accept` 属性进行限制
3. 文件大小限制可以通过 `maxSize` 属性设置
4. 预览功能可以通过 `showPreview` 属性控制
5. 自定义上传函数优先级高于默认上传逻辑
