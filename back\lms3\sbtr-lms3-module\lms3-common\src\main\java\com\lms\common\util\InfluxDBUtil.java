package com.lms.common.util;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import com.influxdb.client.QueryApi;
import com.influxdb.client.WriteApi;
import com.influxdb.client.WriteApiBlocking;
import com.influxdb.client.domain.Bucket;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.DeleteApi;
import com.influxdb.query.FluxTable;
import lombok.Getter;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Objects;
import java.util.stream.Collectors;
import java.time.OffsetDateTime;

/**
 * InfluxDB工具类，封装连接、数据写入、查询、聚合、删除、元数据管理等常用操作。
 * <p>
 * 推荐所有POJO类加@Measurement、@Column注解，便于类型安全的数据读写。
 * </p>
 */
public class InfluxDBUtil {
    // ================== 连接与基础配置 ==================
    /** InfluxDB访问Token */
    private static final String TOKEN = "gKcIUDeQh_xJdT8xlM-H_Xsj7n_bdmhdQQCIU5JCGGNyUY4CJtvX0WF93neWGNizxZVYDtuMTI2bV2hjH-Jv6w==";
    /** InfluxDB bucket名称 */
    private static final String BUCKET = "cbt";
    /** InfluxDB组织名 */
    private static final String ORG = "cbt";
    /** InfluxDB服务URL */
    private static final String URL = "http://localhost:8086";
    /** InfluxDB客户端单例 */
    @Getter
    private static final InfluxDBClient client = InfluxDBClientFactory.create(URL, TOKEN.toCharArray(), ORG, BUCKET);
    private InfluxDBUtil() {}

    // ================== 私有基础方法 ==================
    /**
     * 生成基础Flux语句（带时间区间和measurement过滤）。
     * @param measurement 测量名
     * @param start 开始时间（Flux支持的时间格式，如-1h、2024-01-01T00:00:00Z等）
     * @param stop 结束时间（Flux支持的时间格式，如now()、2024-01-01T23:59:59Z等）
     * @return 带measurement和时间区间过滤的Flux查询语句字符串
     */
    private static String baseFlux(String measurement, String start, String stop) {
        return String.format("from(bucket: \"%s\") |> range(start: %s, stop: %s) |> filter(fn: (r) => r._measurement == \"%s\")", BUCKET, start, stop, measurement);
    }
    /**
     * 通用Flux查询，返回表结构。
     * @param fluxQuery Flux查询语句
     * @return 查询结果表List，每个FluxTable对应一组结果
     */
    private static List<FluxTable> doQuery(String fluxQuery) {
        return client.getQueryApi().query(fluxQuery, ORG);
    }
    /**
     * 通用Flux查询并转为POJO对象列表。
     * @param fluxQuery Flux查询语句
     * @param clazz POJO类类型
     * @param <T> 泛型类型
     * @return POJO对象列表
     */
    private static <T> List<T> doQueryToPojo(String fluxQuery, Class<T> clazz) {
        return client.getQueryApi().query(fluxQuery, ORG, clazz);
    }
    /**
     * 通用删除（按条件）。
     * @param predicate 删除条件（如 _measurement="xxx" AND tag="yyy"）
     * @param start 开始时间（ISO8601格式）
     * @param stop 结束时间（ISO8601格式）
     */
    private static void doDelete(String predicate, String start, String stop) {
        DeleteApi deleteApi = client.getDeleteApi();
        OffsetDateTime startTime = OffsetDateTime.parse(start);
        OffsetDateTime stopTime = OffsetDateTime.parse(stop);
        deleteApi.delete(startTime, stopTime, predicate, BUCKET, ORG);
    }
    /**
     * 通用写入（支持单个/批量、同步/异步）。
     * @param data 单个POJO对象或POJO对象列表
     * @param async 是否异步写入（true为异步，false为同步）
     */
    private static void doWrite(Object data, boolean async) {
        try {
            System.out.println("[InfluxDBUtil] doWrite, async=" + async + ", data=" + data);
            if (async) {
                WriteApi writeApi = client.getWriteApi();
                if (data instanceof List) {
                    writeApi.writeMeasurements(WritePrecision.MS, (List<?>) data);
                } else {
                    writeApi.writeMeasurement(WritePrecision.MS, data);
                }
            } else {
                WriteApiBlocking writeApi = client.getWriteApiBlocking();
                if (data instanceof List) {
                    writeApi.writeMeasurements(WritePrecision.MS, (List<?>) data);
                } else {
                    writeApi.writeMeasurement(WritePrecision.MS, data);
                }
            }
            System.out.println("[InfluxDBUtil] doWrite写入完成");
        } catch (Exception e) {
            System.err.println("[InfluxDBUtil] doWrite写入异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    // ================== 写入相关API ==================
    /**
     * 同步写入单个POJO对象。
     * @param pojo POJO对象，需加@Measurement、@Column注解
     */
    public static void writePojo(Object pojo) {
        try {
            System.out.println("[InfluxDBUtil] 写入POJO: " + pojo);
            doWrite(pojo, false);
            System.out.println("[InfluxDBUtil] 写入成功");
        } catch (Exception e) {
            System.err.println("[InfluxDBUtil] 写入异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    /**
     * 同步批量写入POJO对象。
     * @param pojoList POJO对象列表，元素需加@Measurement、@Column注解
     */
    public static void writeBatch(List<?> pojoList) {
        doWrite(pojoList, false);
    }
    /**
     * 异步写入单个POJO对象。
     * @param pojo POJO对象，需加@Measurement、@Column注解
     * <p>注意：异步写入需在应用关闭时调用writeApi.close()以保证数据落盘。</p>
     */
    public static void writeAsync(Object pojo) {
        doWrite(pojo, true);
    }
    /**
     * 异步批量写入POJO对象。
     * @param pojoList POJO对象列表，元素需加@Measurement、@Column注解
     * <p>注意：异步写入需在应用关闭时调用writeApi.close()以保证数据落盘。</p>
     */
    public static void writeBatchAsync(List<?> pojoList) {
        doWrite(pojoList, true);
    }
    /**
     * 写入LineProtocol原始数据。
     * @param line LineProtocol格式字符串
     */
    public static void writeLineProtocol(String line) {
        WriteApiBlocking writeApi = client.getWriteApiBlocking();
        writeApi.writeRecord(WritePrecision.MS, line);
    }
    /**
     * 写入Map数据（无POJO场景）。
     * @param measurement 测量名
     * @param fields 字段名-值Map
     * @param tags tag名-值Map
     */
    public static void writeMap(String measurement, Map<String, Object> fields, Map<String, String> tags) {
        Map<String, Object> data = new HashMap<>();
        if (fields != null) data.putAll(fields);
        if (tags != null) data.putAll(tags);
        data.put("_measurement", measurement);
        data.put("_time", System.currentTimeMillis());
        WriteApiBlocking writeApi = client.getWriteApiBlocking();
        writeApi.writeMeasurement(WritePrecision.MS, data);
    }

    // ================== 查询相关API ==================
    /**
     * 通用Flux查询，返回表结构。
     * @param fluxQuery Flux查询语句
     * @return 查询结果表List，每个FluxTable对应一组结果
     */
    public static List<FluxTable> query(String fluxQuery) {
        return doQuery(fluxQuery);
    }
    /**
     * 通用Flux查询并转为POJO对象列表。
     * @param fluxQuery Flux查询语句
     * @param clazz POJO类类型
     * @param <T> 泛型类型
     * @return POJO对象列表
     */
    public static <T> List<T> queryToPojo(String fluxQuery, Class<T> clazz) {
        return doQueryToPojo(fluxQuery, clazz);
    }
    /**
     * 查询最近N条数据。
     * @param measurement 测量名
     * @param n 查询数量
     * @param clazz POJO类类型
     * @param <T> 泛型类型
     * @return POJO对象列表
     */
    public static <T> List<T> queryRecent(String measurement, int n, Class<T> clazz) {
        String flux = baseFlux(measurement, "-30d", OffsetDateTime.now().toString()) + String.format(" |> sort(columns:[\"_time\"], desc:true) |> limit(n:%d)", n);
        return doQueryToPojo(flux, clazz);
    }
    /**
     * 查询某时间段内的原始数据。
     * @param measurement 测量名
     * @param start 开始时间（Flux支持的时间格式）
     * @param stop 结束时间（Flux支持的时间格式）
     * @param clazz POJO类类型
     * @param <T> 泛型类型
     * @return POJO对象列表
     */
    public static <T> List<T> queryRaw(String measurement, String start, String stop, Class<T> clazz) {
        String flux = baseFlux(measurement, start, stop);
        return doQueryToPojo(flux, clazz);
    }
    /**
     * 查询某measurement的记录数。
     * @param measurement 测量名
     * @param start 开始时间（Flux支持的时间格式）
     * @param stop 结束时间（Flux支持的时间格式）
     * @return 记录数
     */
    public static long count(String measurement, String start, String stop) {
        String flux = baseFlux(measurement, start, stop) + " |> count()";
        return doQuery(flux).stream().flatMap(table -> table.getRecords().stream()).mapToLong(r -> ((Number) Objects.requireNonNull(r.getValueByKey("_value"))).longValue()).sum();
    }
    /**
     * 查询某measurement的最新时间戳。
     * @param measurement 测量名
     * @return 最新时间戳字符串（ISO8601格式），无数据时返回null
     */
    public static String latestTime(String measurement) {
        String flux = baseFlux(measurement, "-30d", OffsetDateTime.now().toString()) + " |> last()";
        return doQuery(flux).stream().flatMap(table -> table.getRecords().stream()).map(r -> String.valueOf(r.getTime())).findFirst().orElse(null);
    }
    /**
     * 查询某measurement的最早时间戳。
     * @param measurement 测量名
     * @return 最早时间戳字符串（ISO8601格式），无数据时返回null
     */
    public static String earliestTime(String measurement) {
        String flux = baseFlux(measurement, "-30d", OffsetDateTime.now().toString()) + " |> first()";
        return doQuery(flux).stream().flatMap(table -> table.getRecords().stream()).map(r -> String.valueOf(r.getTime())).findFirst().orElse(null);
    }
    /**
     * 分页查询（通过limit）。
     * @param flux Flux查询语句
     * @param limit 限制条数
     * @return 查询结果表List
     */
    public static List<FluxTable> queryWithLimit(String flux, int limit) {
        return doQuery(flux + String.format(" |> limit(n:%d)", limit));
    }

    // ================== 聚合与统计API ==================
    /**
     * 查询某时间段内的聚合（如sum、max、min、count）。
     * @param measurement 测量名
     * @param field 字段名
     * @param agg 聚合函数（sum、max、min、count等）
     * @param start 开始时间（Flux支持的时间格式）
     * @param stop 结束时间（Flux支持的时间格式）
     * @return 聚合结果表List
     */
    public static List<FluxTable> aggregate(String measurement, String field, String agg, String start, String stop) {
        String flux = baseFlux(measurement, start, stop) + String.format(" |> filter(fn: (r) => r._field == \"%s\") |> %s()", field, agg);
        return doQuery(flux);
    }

    // ================== 删除相关API ==================
    /**
     * 按条件删除指定measurement下的数据。
     * @param measurement 测量名
     * @param start 开始时间(ISO8601)
     * @param stop 结束时间(ISO8601)
     * @param predicate 过滤条件（不含measurement部分，可为null或空字符串）
     */
    public static void delete(String measurement, String start, String stop, String predicate) {
        String fullPredicate = String.format("_measurement=\"%s\"%s", measurement,
            (predicate != null && !predicate.trim().isEmpty()) ? " AND " + predicate : "");
        doDelete(fullPredicate, start, stop);
    }
    /**
     * 按tag删除数据。
     * @param measurement 测量名
     * @param tagKey tag键
     * @param tagValue tag值
     * @param start 开始时间（ISO8601格式）
     * @param stop 结束时间（ISO8601格式）
     */
    public static void deleteByTag(String measurement, String tagKey, String tagValue, String start, String stop) {
        doDelete(String.format("_measurement=\"%s\" AND %s=\"%s\"", measurement, tagKey, tagValue), start, stop);
    }
    /**
     * 按时间区间删除数据。
     * @param measurement 测量名
     * @param start 开始时间（ISO8601格式）
     * @param stop 结束时间（ISO8601格式）
     */
    public static void deleteByTime(String measurement, String start, String stop) {
        doDelete(String.format("_measurement=\"%s\"", measurement), start, stop);
    }

    // ================== 元数据API ==================
    /**
     * 获取所有bucket名称。
     * @return bucket名称列表
     */
    public static List<String> listBuckets() {
        return client.getBucketsApi().findBuckets().stream().map(Bucket::getName).collect(Collectors.toList());
    }
    /**
     * 获取所有measurement名称。
     * @return measurement名称列表
     */
    public static List<String> listMeasurements() {
        String flux = String.format("import \"influxdata/influxdb/schema\"\nschema.measurements(bucket: \"%s\")", BUCKET);
        return doQuery(flux).stream()
            .flatMap(table -> table.getRecords().stream())
            .map(record -> (String) record.getValueByKey("_value"))
            .collect(Collectors.toList());
    }

    // ================== 健康检查与关闭 ==================
    /**
     * 检查InfluxDB服务是否可用。
     * @return true-可用，false-不可用
     */
    public static boolean isHealthy() {
        return client.ping();
    }
    /**
     * 关闭InfluxDB资源。
     */
    public static void shutdown() {
        client.close();
    }

} 