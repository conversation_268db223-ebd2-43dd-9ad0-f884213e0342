package com.sbtr.workflow.controller;

import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.model.ActMyNodeField;
import com.sbtr.workflow.service.ActMyNodeFieldService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
/**
 * 流程每个节点所对字段是否可编辑以及是否可见
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-27 13:43:13
 */
@Api(tags = {"流程节点属性" })
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/flowNodeField")
public class ActMyNodeFieldController extends WorkflowBaseController {

    @Autowired
    private ActMyNodeFieldService flowNodeFieldService;

    @ApiOperation(value = "获得ActMyForm分页集数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String"),
    })
    @ResponseBody
    @RequestMapping(value = "/getPageSet", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam) {
        PageSet<ActMyNodeField> pageSet = flowNodeFieldService.getPageSet(pageParam);
        return pageSet;
    }

    @ResponseBody
    @RequestMapping(value = "/getDetailByUuid")
    public Object getDetailByUuid(String uuid) {
        ActMyNodeField flowNodeField =flowNodeFieldService.selectByPrimaryKey(uuid);
        return flowNodeField;
    }


    @Log(title = "流程节点属性-保存FlowNodeField模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Object save(@ModelAttribute ActMyNodeField flowNodeField) {
        setLogMessage(flowNodeField.getFiledName(),"");
        int result = flowNodeFieldService.insertSelective(getSaveData(flowNodeField));
        return result > 0 ? success("保存成功！") : failure("保存失败！");
    }


    @Log(title = "流程节点属性-更新FlowNodeField模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.UPDATE, method = "getLogMessage")
    @ApiOperation(value = "更新FlowNodeField模块数据")
    @ApiImplicitParam(name = "flowNodeField", value = "更新FlowNodeField模块数据", required = true, dataType = "FlowNodeNotice")
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Object update(@ModelAttribute ActMyNodeField flowNodeField) {
        setLogMessage(flowNodeField.getFiledName(),"");
        int result = flowNodeFieldService.updateByPrimaryKeySelective(getUpdateData(flowNodeField));
        return result > 0 ? success("更新成功！") : failure("更新失败！");
    }

    @Log(title = "流程节点属性-删除FlowNodeField模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ApiOperation(value = "删除FlowNodeField模块数据")
    @ApiImplicitParam(name = "flowNodeField", value = "删除FlowNodeField模块数据", required = true, dataType = "FlowNodeNotice")
    @ResponseBody
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.POST)
    public Object deleteBatch(@RequestParam String[] uuid) {
        if(uuid!=null&&uuid.length>0){
            StringBuilder logStr=new StringBuilder();
            for (String s : uuid) {
                ActMyNodeField actMyNodeField = flowNodeFieldService.selectByPrimaryKey(s);
                if(actMyNodeField!=null)
                    logStr.append(actMyNodeField.getFiledName()+",");
            }
            setLogMessage(logStr.toString(),"");
        }
        int result = flowNodeFieldService.executeDeleteBatch(uuid);
        return result > 0 ? success("删除成功！") : failure("删除失败！");
    }




}
