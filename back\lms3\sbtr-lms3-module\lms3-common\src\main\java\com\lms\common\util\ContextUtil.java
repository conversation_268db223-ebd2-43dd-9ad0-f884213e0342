package com.lms.common.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lms.common.model.JwtUser;
import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpCookie;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.DatatypeConverter;
import java.io.File;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.Date;
import java.util.List;

/**
 * Jwt工具类
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
@Slf4j
@Component
public class ContextUtil {
    @Resource
    private RedisUtil redisUtil;
    @Value("${jwt.audience.clientId}")
    private String clientId;
    @Value("${jwt.audience.base64Secret}")
    private String secret;
    @Value("${jwt.audience.name}")
    private String audienceName;

    public static final ThreadLocal<JwtUser> jwtUserTL = new ThreadLocal();

    /**
     * 设置用户数据
     * @param jwtUser 用户信息
     */
    public static void setCurrentUser(JwtUser jwtUser)
    {
        jwtUserTL.set(jwtUser);
    }

    /**
     * 获取当前用户
     * @return userId
     */
    public static JwtUser getCurrentUser()
    {
        return jwtUserTL.get();
    }

    public static String getUserId()
    {
        return jwtUserTL.get().getUserid();
    }
    public static String getPersonId()
    {
        return jwtUserTL.get().getPersonid();
    }
    public static String getUserName()
    {
        return jwtUserTL.get().getUsername();
    }

    public static String getPersonName()
    {
        return jwtUserTL.get().getPersonname();
    }

    public static String getDepartId()
    {
        return jwtUserTL.get().getDepartid();
    }

    public static String getRoleId()
    {
        return jwtUserTL.get().getRoleid();
    }

    public static String getEquipmentId()
    {
        return jwtUserTL.get().getEquipmentid();
    }

    public static String getTeachingModel(){
        return jwtUserTL.get().getTeachingmodel();
    }

    public static String getManageModel(){
        return jwtUserTL.get().getManagemodel();
    }

    public static List<String> getRoles()
    {
        return jwtUserTL.get().getRoles();
    }

    public static Integer getSlevel()
    {
        return jwtUserTL.get().getSlevel();
    }

    /**
     * 清理当前用户信息
     */
    public static void clear()
    {
        jwtUserTL.remove();
    }

    public static Claims parseJWT(String jsonWebToken, String base64Security) {
        try {
            Claims var2 = (Claims) Jwts.parser().
                    setAllowedClockSkewSeconds(300L).
                    setSigningKey(DatatypeConverter.parseBase64Binary(base64Security)).parseClaimsJws(jsonWebToken).getBody();
            return var2;
        } catch (Exception var3) {
            System.out.println(var3);
            System.out.println(var3.getMessage());
            return null;
        }
    }

    public static Boolean isTokenExpired2(String token) {
        Claims claims;
        try {
            claims = (Claims) Jwts.parser().
                    setAllowedClockSkewSeconds(300L).
                    setSigningKey(DatatypeConverter.parseBase64Binary(ConstParamUtil.JWT_SECRET)).parseClaimsJws(token).getBody();
        } catch (ExpiredJwtException e) {
            claims = e.getClaims();
        }
        Date expiration = claims.getExpiration();
        //和当前时间进行对比来判断是否过期
        return new Date(System.currentTimeMillis()).after(expiration);
    }
    public String createJWT(JwtUser jwtUser) {
        SignatureAlgorithm var10 = SignatureAlgorithm.HS256;
        long var11 = System.currentTimeMillis();
        Date var13 = new Date(var11);
        String id = jwtUser.getUserid();
        String name = jwtUser.getUsername();
//        String base64Security = this.audience.getBase64Secret();
//        String audience = this.audience.getClientId();
//        String issuer = this.audience.getName();
        long TTLMillis = ConstParamUtil.TOKEN_EXPIRATION_TIME * 60 * 1000; //设置token过期时间过期时间
        byte[] var14 = DatatypeConverter.parseBase64Binary(secret);
        SecretKeySpec var15 = new SecretKeySpec(var14, var10.getJcaName());
        JwtBuilder var16 = Jwts.builder().setHeaderParam("type", "JWT").claim("id", id).claim("name", name).setIssuer(audienceName).setAudience(clientId).signWith(var10, var15);
        if (TTLMillis >= 0L) {
            long var17 = var11 + TTLMillis;
            Date var19 = new Date(var17);
            var16.setExpiration(var19).setNotBefore(var13);
        }
        String token = var16.compact();
        //缓存当前登录用户
//        redisUtil.set(ConstParamUtil.USER_TOKEN_KEY + name,token,TTLMillis);
        redisUtil.set(ConstParamUtil.USER_TOKEN_KEY + id, JSON.toJSONString(jwtUser), ConstParamUtil.TOKEN_EXPIRATION_TIME);
        return token;
    }

    public static void main(String[] args) {
        SignatureAlgorithm var10 = SignatureAlgorithm.HS256;
        long var11 = System.currentTimeMillis();
        Date var13 = new Date(var11);
        String id = "2";
        String name = "admin";
//        String base64Security = this.audience.getBase64Secret();
//        String audience = this.audience.getClientId();
//        String issuer = this.audience.getName();
        long TTLMillis = ConstParamUtil.TOKEN_EXPIRATION_TIME * 60 * 1000; //设置token过期时间过期时间
        byte[] var14 = DatatypeConverter.parseBase64Binary(ConstParamUtil.JWT_SECRET);
        SecretKeySpec var15 = new SecretKeySpec(var14, var10.getJcaName());
        JwtBuilder var16 = Jwts.builder().setHeaderParam("type", "JWT").claim("id", id).claim("name", name).setIssuer("systemJwt").setAudience("098f6bcd4621d373cade4e832627b4f6").signWith(var10, var15);
        if (TTLMillis >= 0L) {
            long var17 = var11 + TTLMillis;
            Date var19 = new Date(var17);
            var16.setExpiration(var19).setNotBefore(var13);
        }
        String token = var16.compact();
        //缓存当前登录用户
//        redisUtil.set(ConstParamUtil.USER_TOKEN_KEY + name,token,TTLMillis);
        System.out.println("token = " + token);
    }

    public void loadJwtUser(HttpServletRequest request) {
        String key = "";
        if (StringUtils.isNotEmpty(request.getRequestURI()) &&
                (request.getRequestURI().contains("isRightPassword") ||
                request.getRequestURI().contains("setting") ||
                request.getRequestURI().contains("jwtLogin") ||
                request.getRequestURI().contains("savepassword") ||
                request.getRequestURI().contains("/log/saveByParam") ||
                        request.getRequestURI().contains("checkpassword"))){
            return;
        }
        String token = getToken(request);
        if (ObjectUtil.isNotEmpty(token)) {
            Claims user = ContextUtil.parseJWT(token, secret);
            if (user != null) {
                key = ConstParamUtil.USER_TOKEN_KEY + Convert.toStr(user.get("id"));
            }
            JwtUser jwtUser = redisUtil.getJson(key) == null ? new JwtUser() : JSONObject.toJavaObject(redisUtil.getJson(key),JwtUser.class);
            setCurrentUser(jwtUser);
        }
    }

    public boolean isTokenExpired(String token) {
        Claims user;
        try {
            // 尝试正常解析JWT令牌
            user = (Claims) Jwts.parser().
                    setSigningKey(DatatypeConverter.parseBase64Binary(secret)).parseClaimsJws(token).getBody();
            log.debug("JWT令牌解析成功，继续检查Redis状态");
        } catch (ExpiredJwtException e) {
            // JWT过期，但仍可以从异常中获取claims来检查Redis
            user = e.getClaims();
            if (user == null) {
                log.error("无法从过期的JWT令牌中获取用户信息: {}", e.getMessage());
                return true;
            }
            log.debug("JWT令牌已过期但仍可解析用户信息，继续检查Redis状态");
        } catch (Exception e) {
            log.error("JWT令牌解析失败: {}", e.getMessage());
            return true;
        }
        String userId = Convert.toStr(user.get("id"));
        String key = ConstParamUtil.USER_TOKEN_KEY + userId;
        // 只检查Redis中的用户信息是否存在
        if (!redisUtil.hasKey(key)) {
            log.info("Redis中用户信息已过期或不存在，用户ID: {}", userId);
            return true;
        }
        // 检查Redis中的剩余过期时间（单位：分钟）
        long remainingMinutes = redisUtil.getExpire(key);
        if (remainingMinutes <= 0) {
            log.info("Redis中用户信息已过期，用户ID: {}", userId);
            return true;
        }
        // 如果剩余时间少于5分钟，自动延长到2小时
        if (remainingMinutes <= 5) {
            log.info("用户会话即将过期，剩余时间: {} 分钟，自动延长到2小时，用户ID: {}", remainingMinutes, userId);
            boolean success = redisUtil.expire(key, ConstParamUtil.TOKEN_EXPIRATION_TIME); // 2小时
            if (success) {
                log.info("成功延长用户会话时间，用户ID: {}, 新过期时间: 120 分钟", userId);
            } else {
                log.error("延长用户会话时间失败，用户ID: {}", userId);
            }
        }
        return false;
    }

    public static String getResourceDirectory() throws URISyntaxException {
        // 获取当前类的ClassLoader
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        // 获取资源目录的URL
        URL resourceUrl = classLoader.getResource("");
        if (resourceUrl == null) {
            throw new IllegalStateException("无法找到资源目录");
        }
        // 将URL转换为File对象，获取资源目录的路径
        return new File(resourceUrl.toURI()).getPath();
    }


    public static boolean isSelfSupport(String browser, String key) {
        String supportTypes = "";
        if (StringUtils.equals(browser, "Chrome")) {
            supportTypes = "mp4,jpg,png,pdf,swf,txt,mp3,ogg,wav";
        }
        if (StringUtils.equals(browser, "IE")) {
            supportTypes = "avi,swf,flv,mp4,jpg,txt,mp3,ogg,wav";
        }
        if (StringUtils.isNotEmpty(supportTypes)) {
            String[] types = StringHelper.string2Array(supportTypes, ",");
            for (String type : types) {
                if (StringUtils.contains(key.toLowerCase(), type)) {
                    return true; //可以预览,不需要下载
                }
            }
        }
        return false; //不可以预览,需要下载
    }

    public String getToken(HttpServletRequest request) {
        String token = StringHelper.null2String(request.getHeader(ConstParamUtil.X_ACCESS_TOKEN));
        //处理头部token
        if (StringHelper.isEmpty(token)) {
            token = StringHelper.null2String(CookieHelper.getCookie(request, ConstParamUtil.X_ACCESS_TOKEN));
        }
        return token;
    }
}
