package com.lms.system.menu.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.system.feign.model.Menu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.repository.query.Param;


/**
 * 下拉框选项数据库操作接口
 *
 * <AUTHOR>
@Mapper
public interface MenuMapper extends BaseMapper<Menu> {

    @Select("select * from p_menu where status = 1 and id in (select menuid from p_menurolelink where roleid=#{currentRoleId}) order by seqno")
    List<Menu> getMenuListByRoleId(String currentRoleId);

    @Select("select * from p_menu where status = 1 order by seqno")
    List<Menu> getAll();

    @Select(value = "update p_menu set status=0 where id in (#{ids})")
    void batchSetDelete(@Param(value = "ids") List<String> idList);
}
