<template>
  <TrDialog
    v-model:open="isOpen"
    destroyOnClose
    :width="800"
    :title="translateText(`选择属性`)"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <TrTable
      ref="mas"
      :scroll="{ y: 360 }"
      :columns="columns"
      :loading="loading"
      :dataSource="dataSource"
      :queryItems="queryItems"
      :show-btns="false"
      :show-operation="false"
      @query="handleSearch"
    />
  </TrDialog>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from "vue";
import TrTable from "@/components/TrTable/index.vue";
import { message } from "ant-design-vue";
import { queryItems, columns } from "./data.ts";
import { getModelAttributeList } from "@/api/model.ts";
import { translateText } from "@/utils/translation";
import TrDialog from "@/components/TrDialog/index.vue";
export interface Props {
  visible: boolean;
  multiple: boolean;
}
export interface Emits {
  (e: "close", visible: boolean): void;
  (e: "confirm", data: object): void;
}
const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const isOpen = ref(false);
const multSelected = ref(false);

// 表格组件实例
const mas = ref<InstanceType<typeof TrTable>>();
const loading = ref(false);
const dataSource = ref([]);
const columns = [
  {
    title: "属性名",
    dataIndex: "name",
    width: 300
  },
  {
    title: "属性值",
    dataIndex: "value",
    align: "center",
    width: 300,
    sorter: true
  }
];
const queryItems = [
  {
    key: "name",
    label: "属性名",
    component: "a-input",
    props: {
      placeholder: "请输入属性名",
      allowClear: true
    }
  },
  {
    key: "value",
    label: "属性值",
    component: "a-input",
    props: {
      placeholder: "请输入属性值",
      allowClear: true
    }
  }
];

async function handleSearch(form) {
  try {
    await nextTick(); // 等待 DOM 更新
    let tableRef = mas.value.getTableState();
    loading.value = true;
    let para = {
      pageIndex: tableRef.pagination.pageIndex,
      pageSize: tableRef.pagination.pageSize,
      orderName: tableRef.sort.field,
      sortType: tableRef.sort.order
    };
    para = Object.assign(para, form);
    const response = await getModelAttributeList(para);
    if (response.success) {
      dataSource.value = response.result.records;
      mas.value.setTotal(response.result.total);
      mas.value.clearSelection();
    } else {
      message.error(response.message);
    }
  } catch (error) {
    console.error(error);
    message.error(error);
  } finally {
    loading.value = false;
  }
}

const handleSubmit = () => {
  try {
    let selectedRows = mas.value.getSelectedRows();
    if (selectedRows.length === 0) {
      message.info("请选择至少一个属性");
      return;
    }
    if (!multSelected.value && selectedRows.length > 1) {
      message.info("只能选择一个属性");
      return;
    }
    emit("confirm", selectedRows);
    handleCancel();
  } catch (error) {
    console.error("选择属性失败:", error);
  } finally {
    loading.value = false;
  }
};

const handleCancel = () => {
  emit("close", false);
};

watch(
  () => props.visible,
  visible => {
    isOpen.value = visible;
    multSelected.value = props.multiple;
  }
);
</script>

<style scoped>
/* 让页面容器占用100%高度 */
.table-page {
  height: 100%;
}
</style>
