package com.sbtr.workflow.service.impl;

import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import cn.ewsd.common.utils.easyui.PageUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.constants.DriverClassName;
import com.sbtr.workflow.mapper.ActMyNodeCodeMapper;
import com.sbtr.workflow.model.ActMyNodeCode;
import com.sbtr.workflow.service.ActMyNodeCodeService;
import com.sbtr.workflow.utils.BaseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("flowNodeCodeServiceImpl")
public class ActMyNodeCodeServiceImpl extends WorkflowBaseServiceImpl<ActMyNodeCode, String> implements ActMyNodeCodeService {
	@Autowired
	private ActMyNodeCodeMapper flowNodeCodeMapper;

    @Value("${spring.datasource.dynamic.datasource.master.driver-class-name:null}")
    public String driverClassName;

    @Override
    public PageSet<ActMyNodeCode> getPageSet(PageParam pageParam ) {
        PageHelper.startPage(pageParam.getPage(), pageParam.getRows());
        List<ActMyNodeCode> list = flowNodeCodeMapper.getPageSet();
        PageInfo<ActMyNodeCode> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }


    @Override
    public Integer insertList(List<ActMyNodeCode> list1) {
        if(DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)){
            return flowNodeCodeMapper.insertListOracle(list1);
        }
        return flowNodeCodeMapper.insertList(list1);
    }

    @Override
    public List<ActMyNodeCode> selectByParam(String nodeId,String actDeModelKey, String procdefId) {
        return flowNodeCodeMapper.selectByParam(nodeId,actDeModelKey,procdefId);
    }

    @Override
    public Integer updateNodeCodeByParam(String nodeCode, String uuid) {
        return flowNodeCodeMapper.updateNodeCodeByParam(nodeCode, uuid);
    }

    @Override
    public void saveOrUpdateNodeCode(List<ActMyNodeCode> actMyNodeCodes,String actDeModelKey,String procdefId) {
        List<ActMyNodeCode> newCodeList = new ArrayList<>();
        for(int i=0;i<actMyNodeCodes.size();i++){
            ActMyNodeCode actMyNodeCode = actMyNodeCodes.get(i);
            actMyNodeCode.setActDeModelKey(actDeModelKey);
            actMyNodeCode.setProcdefId(procdefId);
            String nodeId = actMyNodeCode.getNodeId();
            String nodeCode = actMyNodeCode.getNodeCode();
            List<ActMyNodeCode> exists = selectByParam(nodeId,actDeModelKey,procdefId);
            if(exists.size()>0){
                // 当前procdefId+nodeId节点下已存在权限码，执行更新操作
                String uuid = exists.get(0).getUuid();
                updateNodeCodeByParam(nodeCode,uuid);
            }else{
                // 当前procdefId+nodeId节点下不存在权限码，执行插入操作
                actMyNodeCode.setUuid(BaseUtils.UUIDGenerator());
                newCodeList.add(actMyNodeCode);
            }
        }
        if(newCodeList.size()>0)
            insertList(newCodeList);
    }

    @Override
    public void updateProcdefIdByModelKey(String key, String procdefId) {
        flowNodeCodeMapper.updateProcdefIdByModelKey(key,procdefId);
    }

    @Override
    public List<ActMyNodeCode> getListByActDeModelKeyAndProcdefId(String key, String procdefIds) {
        return flowNodeCodeMapper.getListByActDeModelKeyAndProcdefId(key, procdefIds);
    }

    @Override
    public Integer updateProDefByParam(String procdefId, String uuid) {
        return flowNodeCodeMapper.updateProDefByParam(procdefId,uuid);
    }

    @Override
    public Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id) {
        return flowNodeCodeMapper.updateProcdefIdByModelKeyAndProcdef(procdefId,key,id);
    }

    @Override
    public void deleteByModelKeyAnProcdefId(String key, String id) {
        flowNodeCodeMapper.deleteByModelKeyAnProcdefId(key,id);
    }


}
