
/**
 * FileName:	ClientLanguagelabel.java
 * Author:		<PERSON><PERSON><PERSON><PERSON>
 * Time:		2013-3-4����11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.system.languagelabel.service.impl;

import java.util.*;
import java.util.regex.*;

import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.DateHelper;
import com.lms.common.util.StringHelper;
import com.lms.system.languagelabel.mapper.LanguagelabelMapper;
import com.lms.system.feign.model.Languagelabel;
import com.lms.system.languagelabel.service.LanguagelabelService;
import com.lms.system.feign.model.Setting;
import com.lms.system.setting.service.SettingService;
import com.lms.system.setting.service.impl.SettingServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * languagelabel服务类
 */
@Service("LanguagelabelService")
public class LanguagelabelServiceImpl extends BaseServiceImpl<LanguagelabelMapper, Languagelabel> implements LanguagelabelService {

    @Resource
    private LanguagelabelMapper languagelabelDao;

    @Resource
    private SettingService settingService;

    @Value("initLabel")
    private String isInit;

    private static final Logger logger = LoggerFactory.getLogger(LanguagelabelServiceImpl.class);



    @Override
    public List<Languagelabel> listAll() {
        Setting setting = settingService.getById("90f77810bad911ec85425c4f7a74d8de");//获取当前系统语言设置
        String language = setting.getItemvalue();
        return languagelabelDao.listAllByLanguage(language);
    }

    public boolean isChinese(String keyword) {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(keyword);
        return m.find();
    }

    /* param (keyword,settingLanguage,module)
     * 创建新的待翻译数据的方法,遍历当前系统语言选项，生成所有语言的
     * 初始化译文数据，如果keyword非中文（即keyid），创建的译文没有中文内容，
     * 需要手动修改中文内容才能支持翻译
     * author quanjinghua
     * created 20220909
     */
    public void createLanguagelabel(String keyword, String settinglg, String moduledesc) throws Exception {
        List selectitems = languagelabelDao.getSelectitemsByTypeId("4b70116f302211ed969cfefcfee1b6f6");
        List<Languagelabel> languagelabelList = languagelabelDao.getByKeword(keyword, "");
        String chinese = "";
        String chn = "";
        // 遍历所有语言，没有该keyword的翻译数据的语言，新生成一条
        for (int i = 0; i < selectitems.size(); i++) {
            Map map = (Map) selectitems.get(i);
            String language = StringHelper.null2String(map.get("OBJNAME"));
            boolean exist = false;
            for (Languagelabel label : languagelabelList) {
                chn = StringHelper.isEmpty(chn) ? label.getChinese() : chn;
                if (label.getLanguage().equals(language)) {
                    // keyword在当前语言已有翻译，不需要再生成，若模块名称为空且模块名称测试不为空，则更新模块名称
                    exist = true;
                    String desc = label.getModuledesc();
                    if (StringHelper.isEmpty(desc) && !moduledesc.equals(desc) && settinglg.equals(language)) {
                        label.setModuledesc(moduledesc);
                        this.saveOrUpdate(label);
                    }
                }
            }
            if (!exist) {
				/* 如果keyword不是中文，要求Languagelabel表必须至少存在一条keyword可匹配到的数据，从任意一条keyword匹配的数据中,
			              获取chinese字段作为新生成数据的chinese值。如果没有匹配的数据，chinese字段值仍设值为keyword*/
                chinese = isChinese(keyword) || StringHelper.isEmpty(chn) ? keyword : chn;
                Languagelabel languagelabel = new Languagelabel();
                languagelabel.setKeyword(keyword);
                languagelabel.setLanguage(language);
                languagelabel.setChinese(chinese);
                languagelabel.setModuledesc(settinglg.equals(language) ? moduledesc : "");
                languagelabel.setCreatedate(DateHelper.getCurrentDate());
                if (!language.equals("Chinese")) {
                    languagelabel.setContent("");//语言不是中文，翻译的内容全部置空
                } else {
                    languagelabel.setContent(chinese);//语言是中文，翻译的内容等于chinese字段值
                }
                this.saveOrUpdate(languagelabel);
            }
        }
    }


    /* param (keyword,module)
     * 获取多语言翻译的译文的方法，根据传入的中文代码和当前语言进行获取，
     * 如果没有找到匹配的译文数据则初始化一条新的待翻译数据,其中module参数
     * 是模块名称，该参数也应调用当前方法进行翻译后再传入
     * 注意（selectitem 等基础数据需要在set方法中翻译的，要通过translateLanguageNotCreated进行初始化）
     * author quanjinghua
     * created 20220909
     */
    @SuppressWarnings("finally")
    public String translateLanguage(String keyword, String moduledesc) {
        String content = keyword;
        try {
            if (false && !StringHelper.isEmpty(keyword)) {
                Setting st = settingService.getById("90f77810bad911ec85425c4f7a74d8de");//获取当前系统语言设置
                String language = st.getItemvalue();
                // 根据key和语言选项查询是否存在该语言的翻译内容，此方法应优先从缓存获取匹配数据
                List<Languagelabel> languagelabelList = languagelabelDao.getByKeword(keyword, language);
                //List languagelabelList = languagelabelDao.listSQLQuery("select b.content from b_languagelabel b where b.keyword='"+keyword+"' and b.language='"+language+"'");
                if (languagelabelList.size() > 0) { // 存在翻译内容则获取第一个满足条件的值，读取内容返回
                    Languagelabel label = languagelabelList.get(0);
                    content = StringHelper.null2String(label.getContent(), content);
                } else {// 不存在翻译内容则插入一条新的
                    createLanguagelabel(keyword, language, moduledesc);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            this.logger.error(e.getMessage());
        } finally {
            return content;
        }
    }

    public String translateLanguageNotCreated(String keyword, String moduledesc) {
        String content = keyword;
        try {
            if (false && !StringHelper.isEmpty(keyword)) {
                Setting st = settingService.getById("90f77810bad911ec85425c4f7a74d8de");//获取当前系统语言设置
                String language = st.getItemvalue();
                // 根据key和语言选项查询是否存在该语言的翻译内容，此方法应优先从缓存获取匹配数据
                List<Languagelabel> languagelabelList = languagelabelDao.getByKeword(keyword, language);
                //List languagelabelList = languagelabelDao.listSQLQuery("select b.content from b_languagelabel b where b.keyword='"+keyword+"' and b.language='"+language+"'");
                if (languagelabelList.size() > 0) { // 存在翻译内容则获取第一个满足条件的值，读取内容返回
                    Languagelabel label = languagelabelList.get(0);
                    content = StringHelper.null2String(label.getContent(), content);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            this.logger.error(e.getMessage());
        } finally {
            return content;
        }
    }

    public String translateContent(String keyword) {
        return translateLanguage(keyword, "");
    }

    public String translateContent(String keyword, String moduledesc) {
        return translateLanguage(keyword, moduledesc);
    }

    public String translateContent(String keyword, String moduledesc, String parameter) {
        String content = translateLanguage(keyword, moduledesc);
        content = content.replace("[?]", parameter);
        return content;
    }

    public String translateContent(String keyword, String moduledesc, String[] parameters) {
        String content = translateLanguage(keyword, moduledesc);
        for (int i = 0; i < parameters.length; i++) {
            String parameter = parameters[i];
            content = content.replaceFirst("[?]", parameter);
        }
        return content;
    }

    public void modify(Languagelabel languagelabel) {
        this.saveOrUpdate(languagelabel);

    }

    public String translateContentNotCreated(String keyword) {
        return translateLanguageNotCreated(keyword, "");
    }

    public String translateContentNotCreated(String keyword, String moduledesc) {
        return translateLanguageNotCreated(keyword, moduledesc);
    }

    public String translateContentNotCreated(String keyword, String moduledesc,
                                             String[] parameters) {
        String content = translateLanguageNotCreated(keyword, moduledesc);
        for (int i = 0; i < parameters.length; i++) {
            String parameter = parameters[i];
            content = content.replaceFirst("[?]", parameter);
        }
        return content;
    }

    public String translateContentNotCreated(String keyword, String moduledesc,
                                             String parameter) {
        String content = translateLanguageNotCreated(keyword, moduledesc);
        content = content.replace("[?]", parameter);
        return content;
    }

    public String checkImportData(Map map) throws Exception {
        StringBuffer ckMsg = new StringBuffer();
        if (map.size() > 0) {
            for (int i = 0; i < map.size(); i++) {
                Map excelMap = (Map) map.get(i);
                int rowNum = i + 1;
                String id = StringHelper.null2String(excelMap.get(0));
                if (this.getById(id) == null) {
                    ckMsg.append(this.translateContent("导入更新第([?])行数据出现错误，数据ID'" + id + "'不存在！", "", String.valueOf(rowNum)) + "\r\n");
                }
            }
        } else {
            ckMsg.append(this.translateContent("导入失败，上传的数据为空!"));
        }
        return ckMsg.toString();
    }

    public int saveImportData(Map map) throws Exception {
        int savenum = 0;
        String msg = "";
        for (int i = 0; i < map.size(); i++) {
            Map excelMap = (Map) map.get(i);
            String id = StringHelper.null2String(excelMap.get(0));
            Languagelabel languagelabel = this.getById(id);
            String content = StringHelper.null2String(excelMap.get(3));
            languagelabel.setContent(content);
            saveOrUpdate(languagelabel);
            savenum++;
        }
        return savenum;
    }

}
