package com.lms.system.attach.service;

import com.lms.common.model.Result;
import com.lms.common.service.BaseService;
import com.lms.system.feign.model.Attach;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.util.List;

public interface AttachService extends BaseService<Attach> {

    boolean checkFileIsExist(String objName);

    String saveAttachByFilePath(File file);

    Result uploadScormFile(MultipartFile file);

    Result uploadFile(MultipartFile file) throws Exception;

    Boolean deleteFile(Attach attach);

    void downLoadFile(String filename, HttpServletResponse response) throws Exception;

    InputStream downLoadStream(String filename) throws Exception;

    List getByIdList(List<String> attachIdList);
}
