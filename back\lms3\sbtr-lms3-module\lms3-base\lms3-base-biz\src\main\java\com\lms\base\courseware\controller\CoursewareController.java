package com.lms.base.courseware.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lms.base.component.service.ComponentService;
import com.lms.base.courseware.service.CoursewareService;
import com.lms.base.courseware.vo.TextBook;
import com.lms.base.coursewarelearnrecord.service.CoursewareLearnRecordService;
import com.lms.base.department.service.impl.DepartmentServiceImpl;
import com.lms.base.feign.model.Component;
import com.lms.base.feign.model.Courseware;
import com.lms.base.feign.model.CoursewareLearnRecord;
import com.lms.base.feign.model.Department;
import com.lms.base.feign.model.Person;
import com.lms.base.feign.model.Selectitem;
import com.lms.base.person.service.impl.PersonServiceImpl;
import com.lms.base.selectitem.service.impl.SelectitemServiceImpl;
import com.lms.common.config.LMSConfiguration;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.dao.query.Parameter;
import com.lms.common.feign.dto.FlowModelDto;
import com.lms.common.feign.dto.ModleDto;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.DateHelper;
import com.lms.common.util.ExcelUtils;
import com.lms.common.util.StringHelper;
import com.lms.common.util.ZipHelper;
import com.lms.system.feign.api.AttachApi;
import com.lms.system.feign.model.Attach;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lms.rte.CAM.ScormParser.LMSManifestAnalyzer;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/courseware")
@Api(value = "课件管理", tags = "课件管理")
public class CoursewareController extends BaseController<Courseware> {

    @Resource
    private CoursewareService coursewareService;

    @Value("${business.newCodePrefix.courseware}")
    private String newCodePrefix;

    @Resource
    private LMSConfiguration lmsConfiguration;

    @Resource
    private CoursewareLearnRecordService coursewareLearnRecordService;

    @Resource
    private AttachApi attachApi;

    @Resource
    private PersonServiceImpl personService;

    @Resource
    private SelectitemServiceImpl selectitemService;

    @Resource
    private DepartmentServiceImpl departmentService;

    @Resource
    private ComponentService componentService;

    // 根据节点请求资源
    @ApiOperation(value = "查询当前课程下的所有课件", httpMethod = "POST")
    @PostMapping("/list/{componentid}")
    public Result<Page<Courseware>> courseWareList(@ApiParam(value = "课程id") @PathVariable("componentid") String componentid, @RequestBody JSONObject jsonObject) {
        PageInfo pageInfo = super.getPageInfo(jsonObject);
        if (!StringHelper.isEmpty(componentid) && !componentid.equals("0")) {
            Component component = componentService.getById(componentid);
            if (component != null) {
                Parameter componentpar = Parameter.getParameter("S_Like_equipmentid", componentid);
                pageInfo.getParameters().add(componentpar);
            } else {
                Parameter componentpar = Parameter.getParameter("S_Like_componentid", componentid);
                pageInfo.getParameters().add(componentpar);
            }
        }
        Page<Courseware> page = coursewareService.listByCondition(pageInfo);
        componentService.adaptComponent(page.getRecords());
        return Result.OK(page);
    }


    @PostMapping("/batchAddCourseware")
    @ApiOperation(value = "添加课件", httpMethod = "POST")
    public Result batchAddCourseware(@RequestParam("file") MultipartFile file, @RequestParam("courseId") String courseId) throws Exception {
        //对添加的文件进行索引；
        String attachId = attachApi.uploadFile(file).getResult();
        Attach attach = attachApi.get(attachId).getResult();
        if (attach == null) {
            return Result.error("文件上传失败");
        }
        Courseware courseware = new Courseware();
        courseware.setAttachid(attachId);
        courseware.setName(attach.getObjname());
        courseware.setLocation(attach.getFiledir());
        courseware.setCoursewaretype(attach.getSuffix());
        courseware.setStatus(1);
        courseware.setComponentid(courseId);
        courseware.setModifydate(DateHelper.getCurDateTime());
        courseware.setCreatedate(DateHelper.getCurrentDate());
        courseware.setCreatorid(ContextUtil.getCurrentUser().getPersonid());
        courseware.setNumber(commonSystemApi.getNewCode(newCodePrefix));
        courseware.setCreatedeptid(ContextUtil.getCurrentUser().getDepartid());
        courseware.setPrincipal(ContextUtil.getCurrentUser().getPersonname());//责任人
        courseware.setDutyunit(ContextUtil.getCurrentUser().getDepartmentname());//责任部门
        courseware.setVersion("0");
        courseware.setMlevel(attach.getMlevel());
        courseware.setMlimit(attach.getMlimit());
        // 获取课件型号
        Selectitem selectitem = selectitemService.getSelectitemByFileName(attach.getSuffix());
        if (null != selectitem) {
            courseware.setCoursewaretype(selectitem.getId());
        } else {
            courseware.setCoursewaretype("4028828a81b8cab00181b8e8377a001c"); // 其他文件
        }

        Result<List<Courseware>> coursewareList = getCoursewareList(courseId);
        if (null != coursewareList && CollectionUtils.isNotEmpty(coursewareList.getResult())) {
            courseware.setSeqno(coursewareList.getResult().size() + 1);
        } else {
            courseware.setSeqno(1);
        }
        courseware.setIsscorm(0);
        if (StringUtils.isNotEmpty(courseware.getComponentid())) {
            Component component = componentService.getById(courseware.getComponentid());
            if (component != null) {
                courseware.setEquipmentid(component.getFullpath());
            }
        }
        coursewareService.saveOrUpdate(courseware);
        commonSystemApi.saveLog(courseware.getName(), "添加课件", LogType.Save, "操作成功");
        return Result.OK(commonSystemApi.translateContent("[?] 上传成功!", "", courseware.getName()), courseware);
    }

    @ApiOperation(value = "分页查询所有课件", httpMethod = "POST")
    @RequestMapping(value = {"/listpage"}, method = RequestMethod.POST)
    @LMSLog(desc = "查询课件", otype = LogType.List, order = 1, objname = "课件列表")
    public Result<Page<Courseware>> courseWareListpage(@RequestBody JSONObject jsonObject) {
        PageInfo pageInfo = super.getPageInfo(jsonObject);
        return Result.OK(coursewareService.listByCondition(pageInfo));
    }

    // 根据节点请求资源
    @GetMapping("/countTotal")
    @ApiOperation(value = "统计课件总数", httpMethod = "GET")
    public Result countTotal() {
        int c = coursewareService.getTotalCourseware();
        return Result.OK(c);
    }

    @ApiOperation(value = "课件学习记录", httpMethod = "POST")
    @PostMapping("/coursewarelearnlist/{courseId}/{cardNum}")
    public Result<Page<Courseware>> courseWareList(
            @ApiParam(value = "课程id") @PathVariable("courseId") String courseId,
            @ApiParam(value = "人员证件号") @PathVariable("cardNum") String cardNum,
            @RequestBody JSONObject jsonObject) {

        PageInfo pageInfo = super.getPageInfo(jsonObject);
        pageInfo.setOrderName("seqno");
        pageInfo.setSort(PageInfo.ASC);
        if (!StringHelper.isEmpty(courseId)) {
            Parameter componentpar = Parameter.getParameter("S_Like_componentid", courseId);
            pageInfo.getParameters().add(componentpar);
        }
        Page<Courseware> coursewareList = coursewareService.listByCondition(pageInfo);
        componentService.adaptComponent(coursewareList.getRecords());
        Map<String, Object> map = new HashMap<>();
        //课程id、课件id、学员身份证号搜索学习记录；
        map.put("courseId", courseId);
        map.put("cardNum", cardNum);
        for (Courseware courseware : coursewareList.getRecords()) {
            map.put("coursewareId", courseware.getId());
            CoursewareLearnRecord coursewareLearnRecord = coursewareLearnRecordService.getCoursewareLearnRecord(map);
            if (coursewareLearnRecord != null) {
                courseware.setLearnstatus(coursewareLearnRecord.getStatus());
                courseware.setLearnDate(coursewareLearnRecord.getLearnDate());
                courseware.setSpeed(coursewareLearnRecord.getSpeed());
                courseware.setStudynum(coursewareLearnRecord.getStudynum());
            } else {
                courseware.setLearnstatus(0);
            }
        }
        return Result.OK(coursewareList);
    }

    @ApiOperation(value = "新增课件信息", httpMethod = "POST")
    @LMSLog(desc = "新增课件信息", otype = LogType.Save, method = "setCoursewareLog", order = 1)
    @RequestMapping(value = {"/add"}, method = RequestMethod.POST)
    public Result<ArrayList<Courseware>> createCourseware(@RequestBody Courseware cw) throws URISyntaxException, IOException {
        ArrayList<Courseware> list = new ArrayList<>();
        String attachids = StringHelper.null2String(cw.getAttachid());
        Boolean existCoursewareNo = coursewareService.existCoursewareNo(cw);
        if (existCoursewareNo) {
            return Result.error("系统已存在相同编号的课件，请勿重复录入！", null);
        }
        int seqno = coursewareService.getMaxSeqnoByCourse(cw.getComponentid());
        for (int i = 0; i < attachids.split(",").length; i++) {
            String attachid = attachids.split(",")[i];
            Attach attach = Optional.ofNullable(this.attachApi.get(attachid).getResult()).orElse(new Attach());
            seqno++;
            Courseware courseware = new Courseware();
            BeanUtils.copyProperties(cw, courseware);
            if (StringHelper.isEmpty(courseware.getName())) {
                courseware.setName(attach.getObjname());
            }
            courseware.setSeqno(seqno);
            courseware.setAttachid(attachid);
            courseware.setLocation(attach.getFiledir());
            courseware.setIsscorm(0);
            courseware.setStatus(1);
            courseware.setModifydate(DateHelper.getCurDateTime());
            courseware.setCreatedate(DateHelper.getCurrentDate());
            courseware.setCreatorid(ContextUtil.getPersonId());
            courseware.setNumber(commonSystemApi.getNewCode(newCodePrefix));
            courseware.setCreatedeptid(ContextUtil.getDepartId());
            if (StringUtils.isNotEmpty(courseware.getComponentid())) {
                Component component = componentService.getById(courseware.getComponentid());
                if (component != null) {
                    courseware.setEquipmentid(component.getFullpath());
                }
            }
            coursewareService.saveOrUpdate(courseware);
            list.add(courseware);
        }

        return Result.OK(list);
    }

    private boolean dealScormCourseWare(Attach attach, Courseware courseware) throws IOException {
        //保存相对路径
        String suffix = "htm";
        if ("5dd88fc9b7db4966b15cd92767a4ca5a".equals(courseware.getCoursewaretype())) {//scorm课件
            String pathName = lmsConfiguration.getScormStoragePath() + "/" + attach.getFiledir();
            File temp = new File(pathName);
            if (!temp.exists()) {
                return true;
            }
            //解压缩文件
            ZipHelper.unZip(pathName, temp.getParentFile().getPath());
            String[] names = StringHelper
                    .string2Array(attach.getObjname(), ".");
            String name = names[0];
            String fileName = temp.getParentFile().getPath() + File.separator + name + "." + suffix;
            File htmlFile = new File(fileName);
            if (htmlFile.exists()) {
                //Cortana 3D课件，三位课件
                courseware.setIsscorm(2);
                String htmlFilePath = htmlFile.getAbsolutePath().substring(lmsConfiguration.getScormStoragePath().length());
                courseware.setLocation(lmsConfiguration.getViewurl().replace("*", "") + htmlFilePath);
                attach.setFiledir(htmlFilePath);
            } else {
                //ICPS课件，出版课件；
                courseware.setIsscorm(1);
                courseware.setLocation(attach.getFiledir());
                attach.setFiledir(temp.getAbsolutePath().substring(lmsConfiguration.getStoragepath().length()));
            }
            attach.setSuffix("scorm");

            //解析scorm课件； ICPS课件，多媒体课件例外方法处理
            if (courseware.getIsscorm() == 1) {
                System.out.println(temp.getParentFile().getPath());
                LMSManifestAnalyzer analyzer = new LMSManifestAnalyzer(
                        temp.getParentFile().getPath(), courseware.getId());
                if (analyzer.ProcessPackage()) {
                    System.out.println("课件解析成功！");
                } else {
                    System.out.println("课件解析失败！");
                }
                attach.setFiledir(temp.getParentFile().getPath().substring(lmsConfiguration.getStoragepath().length()));
            }
            this.attachApi.saveOrUpdate(attach);
        }
        return false;
    }

    @ApiOperation(value = "修改课件信息", httpMethod = "POST")
    @LMSLog(desc = "修改课件信息", otype = LogType.Update, method = "setCoursewareLog", order = 1)
    @PostMapping("/save")
    public Result<Courseware> saveCourseware(@RequestBody Courseware courseware) {
        Courseware sourcecourseware = courseware;
        Boolean existCoursewareNo = coursewareService.existCoursewareNo(courseware);
        if (existCoursewareNo) {
            return Result.error("系统已存在相同编号的课件，请勿重复录入！", null);
        }
        if (StringUtils.isNotEmpty(courseware.getComponentid())) {
            Component component = componentService.getById(courseware.getComponentid());
            if (component != null) {
                courseware.setEquipmentid(component.getFullpath());
            }
        }
        if (!StringHelper.isEmpty(courseware.getId())) {
            sourcecourseware = coursewareService.getById(courseware.getId());
            sourcecourseware.setDutyunit(courseware.getDutyunit());
            sourcecourseware.setPrincipal(courseware.getPrincipal());
            sourcecourseware.setVersion(courseware.getVersion());
            sourcecourseware.setDescription(courseware.getDescription());
            sourcecourseware.setLanguage(courseware.getLanguage());
            sourcecourseware.setKeyword(courseware.getKeyword());
            sourcecourseware.setSpecialid(courseware.getSpecialid());
            sourcecourseware.setModifydate(DateHelper.getCurDateTime());
            sourcecourseware.setSeqno(courseware.getSeqno());
            sourcecourseware.setLength(courseware.getLength());
            sourcecourseware.setSoftcode(courseware.getSoftcode());
            sourcecourseware.setSpecialityid(courseware.getSpecialityid());
            sourcecourseware.setPersonlevelid(courseware.getPersonlevelid());
        } else {
            sourcecourseware.setModifydate(DateHelper.getCurDateTime());
            sourcecourseware.setCreatedate(DateHelper.getCurrentDate());
            sourcecourseware.setCreatedeptid(ContextUtil.getCurrentUser().getDepartid());
            sourcecourseware.setCreatorid(ContextUtil.getCurrentUser().getPersonid());
            courseware.setNumber(commonSystemApi.getNewCode(newCodePrefix));
            sourcecourseware.setStatus(1);
        }

        if (StringHelper.isEmpty(sourcecourseware.getId())) {
            this.coursewareService.saveOrUpdate(sourcecourseware);
        } else {
            this.coursewareService.saveOrUpdate(sourcecourseware);
        }
        return Result.OK(sourcecourseware);
    }

    @ApiOperation(value = "通过文件路径添加课件", httpMethod = "POST")
    @PostMapping("/addCoursewareByFileDir")
    public Result<List<String>> addCoursewareByFileDir(
            @ApiParam(value = "课程id") @RequestParam("courseId") String courseId,
            @ApiParam(value = "文件路径") @RequestParam("fileDir") String fileDir) {
        String storePath = "LMSConfiguration.GetStoragePath()"; // todo
        List<String> resultInformation = new ArrayList<String>();
        if (fileDir.startsWith(storePath) && !fileDir.equals(storePath)) {
            File dir = new File(fileDir);
            if (dir.exists()) {
                File[] fileList = dir.listFiles();
                for (File file : fileList) {
                    if (file.getName().toLowerCase().endsWith("mp4")) {
                        String attachId = attachApi.saveAttachByFilePath(file).getResult();
                        Attach attach = attachApi.get(attachId).getResult();
                        Courseware courseware = new Courseware();
                        courseware.setAttachid(attachId);
                        if (attach != null) {
                            courseware.setName(attach.getObjname());
                            courseware.setLocation(attach.getFiledir());
                        }
                        courseware.setStatus(1);
                        courseware.setComponentid(courseId);
                        courseware.setModifydate(DateHelper.getCurDateTime());
                        courseware.setCreatedate(DateHelper.getCurrentDate());
                        courseware.setCreatorid(ContextUtil.getCurrentUser().getPersonid());
                        courseware.setNumber(commonSystemApi.getNewCode(newCodePrefix));
                        courseware.setCoursewaretype("fb3fade9db294252b7e14389ed8455e0");//视频
                        courseware.setCreatedeptid(ContextUtil.getCurrentUser().getDepartid());
                        courseware.setSeqno(coursewareService.getCoursewareList(courseId).size() + 1);
                        courseware.setIsscorm(0);
                        if (StringUtils.isNotEmpty(courseware.getComponentid())) {
                            Component component = componentService.getById(courseware.getComponentid());
                            if (component != null) {
                                courseware.setEquipmentid(component.getFullpath());
                            }
                        }
                        coursewareService.saveOrUpdate(courseware);
                        resultInformation.add(commonSystemApi.translateContent("[?] 导入成功!", "", courseware.getName()));
                    }
                }
            }
        } else {
            resultInformation.add(commonSystemApi.translateContent("上传文件需放在系统配置的目录的子目录中：[?]", "", storePath));
        }
        return Result.OK(resultInformation);
    }

    @ApiOperation(value = "根据id查询课件", httpMethod = "GET")
    @GetMapping("/get/{coursewareid}")
    public Result<Courseware> getCourseware(
            @ApiParam(value = "课件id", required = true) @PathVariable("coursewareid") String coursewareid) {
        return Result.OK(coursewareService.getById(coursewareid));
    }

    @ApiOperation(value = "删除课件", httpMethod = "DELETE")
    @LMSLog(desc = "删除课件", otype = LogType.Delete, method = "setCoursewareLog", order = 1)
    @DeleteMapping("/delete")
    public Result<Void> deleteCourseware(
            @ApiParam(value = "课件id以,分隔") @RequestParam("ids") String ids) {
        String[] idArray = StringHelper.string2Array(ids, ",");
        if (idArray != null) {
            for (String id : idArray) {
                this.coursewareService.removeById(id);
            }
        }
        return Result.OK(null);
    }

    @ApiOperation(value = "根据指定字段统计课程数量", httpMethod = "GET")
    @GetMapping("/coursewareAnalysis")
    public Result<List<Map<String, Object>>> getCoursewareAnalysisByType(
            @ApiParam(value = "类型字段") @RequestParam("analysetype") String analysetype) {
        return Result.OK(coursewareService.getCoursewareAnalysisByType(analysetype));
    }

    @ApiOperation(value = "上传文件", httpMethod = "GET")
    @RequestMapping(value = {"/uploadfile"})
    public Result<String> uploadfile(
            @ApiParam(value = "文件类型") @RequestParam("filetype") String coureseWareType,
            @ApiParam(value = "文件") @RequestParam("file") MultipartFile file) {
        String attachId = attachApi.uploadFile(file).getResult();
        return Result.OK(attachId);
    }


    @ApiOperation(value = "根据课程id,查询课件", httpMethod = "GET")
    @GetMapping("/getCourseWareByCourseId")
    public Result<Courseware> getCourseWareByCourseId(@ApiParam(value = "课程id") @RequestParam String id) {
        List<Courseware> coursewareList = coursewareService.getCourseWareByCourseIdList(Collections.singletonList(id));
        componentService.adaptComponent(coursewareList);
        return Result.OK(coursewareList == null && coursewareList.size() > 0 ? coursewareList.get(0) : null);
    }

    @ApiOperation(value = "根据课程id列表,查询课件", httpMethod = "GET")
    @GetMapping("/getCourseWareByCourseIdList")
    Result<List<Courseware>> getCourseWareByCourseIdList(@ApiParam(value = "课程id列表") @RequestParam List<String> idList) {
        List<Courseware> coursewareList = coursewareService.getCourseWareByCourseIdList(idList);
        componentService.adaptComponent(coursewareList);
        return Result.OK(coursewareList);
    }


    @ApiOperation(value = "导出课件信息", httpMethod = "POST")
    @PostMapping("/exportCourseWareByIds")
    @LMSLog(desc = "导出课件信息", otype = LogType.Export, objname = "导出课件信息")
    public void exportCourseWareByIds(@RequestBody List<String> ids, HttpServletResponse response) {
        List<Courseware> coursewareList;
        if (CollectionUtils.isEmpty(ids)) {
            coursewareList = coursewareService.list();
        } else {
            coursewareList = coursewareService.listIds(ids);
        }
        componentService.adaptComponent(coursewareList);
        List<TextBook> textBookList = new ArrayList<>();
        if (CollectionUtils.isEmpty(coursewareList)) {
            return;
        }
        Set<String> typeIds = new HashSet<>();
        coursewareList.forEach(c -> {
            typeIds.add(c.getTextbooktype());
            typeIds.add(c.getCoursewaretype());
        });
        Map<String, Selectitem> selectitemMap = new HashMap<>();
        List<Selectitem> selectitemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(typeIds)) {
            selectitemList = selectitemService.listIds(new ArrayList<>(typeIds));
        }
        if (CollectionUtils.isNotEmpty(selectitemList)) {
            selectitemMap = selectitemList.stream().collect(Collectors.toMap(Selectitem::getId, s -> s));
        }
        for (Courseware courseware : coursewareList) {
            TextBook textBook = new TextBook();
            textBook.setMlevel(courseware.getMlevel());
            textBook.setTextbooktype(Optional.ofNullable(selectitemMap.get(courseware.getTextbooktype())).orElse(new Selectitem()).getObjname());
            textBook.setId(courseware.getId());
            String equipmentname = courseware.getEquipmentname();
            if (StringUtils.isNotEmpty(equipmentname)) {
                if (equipmentname.contains("/")) {
                    String[] split = equipmentname.split("/");
                    textBook.setEquipment(split[split.length - 1]);
                } else {
                    textBook.setEquipment(equipmentname);
                }
            }
            textBook.setBookname(courseware.getName());
            textBook.setNumber(courseware.getNumber());
            Person person = personService.getPersonById(Optional.ofNullable(courseware.getCreatorid()).orElse(""));
            if (person != null) {
                if (ObjectUtil.isNotEmpty(person.getDepartmentid())) {
                    Department department = departmentService.getById(person.getDepartmentid());
                    String departName = ObjectUtil.isNotEmpty(department) ? department.getName() : "";
                    person.setDepartmentname(departName);
                    person.setDepartment(department);
                    textBook.setCreatordepartment(departName);
                }
                textBook.setCreator(person.getName());
                textBook.setJob(person.getJob());
            }
            textBook.setIntro(courseware.getIntro());
            textBook.setTbtype(Optional.ofNullable(selectitemMap.get(courseware.getCoursewaretype())).orElse(new Selectitem()).getObjdesc()); //载体类型
            textBookList.add(textBook);
        }
        ExcelUtils.exportExcel(textBookList, "教材导入模板", "教材列表", TextBook.class, "教材导入模板" + DateHelper.getCurDateTime(), response);
    }

    // 以下是远程调用暴露
    @ApiOperation(value = "根据课程id,查询课件", httpMethod = "GET")
    @GetMapping("/getCoursewareList")
    public Result<List<Courseware>> getCoursewareList(@RequestParam String componentid) {
        List<Courseware> coursewares = coursewareService.getCoursewareList(componentid);
        return Result.OK(coursewares);
    }

    @ApiOperation(value = "保存课件流程图", httpMethod = "POST")
    @PostMapping("/saveFlowchart")
    @LMSLog(desc = "保存课件流程图", otype = LogType.Save, objname = "保存课件流程图")
    public Result saveFlowchart(FlowModelDto flowModelDto) {
        String modelKey = flowModelDto.getModelKey();
        String coursewareId = "";
        if (modelKey != null && modelKey.contains("_")) {
            coursewareId = modelKey.substring(modelKey.indexOf("_") + 1);
        }
        Courseware courseware = coursewareService.getById(coursewareId);
        if (courseware == null) {
            return Result.error("课件不存在");
        }
        flowModelDto.setModelType("1");
        flowModelDto.setActFormConfigureUuid(modelKey);
        flowModelDto.setProcessModelType("1");
        flowModelDto.setPermissionType("all");
        flowModelDto.setModelId(modelKey);
        HashMap<String, Object> resultMap = coursewareService.saveFlowchart(flowModelDto);
        return getResult(resultMap);
    }

    @ApiOperation(value = "修改课件流程图", httpMethod = "POST")
    @PostMapping("/updateFlowchart")
    @LMSLog(desc = "修改课件流程图", otype = LogType.Update, objname = "修改课件流程图")
    public Result updateFlowchart(ModleDto modleDto) {
        String modelKey = modleDto.getActDeModelKey();
        String coursewareId = "";
        if (modelKey != null && modelKey.contains("_")) {
            coursewareId = modelKey.substring(modelKey.indexOf("_") + 1);
        }
        Courseware courseware = coursewareService.getById(coursewareId);
        if (courseware == null) {
            return Result.error("课件不存在");
        }
        HashMap<String, Object> resultMap = coursewareService.updateFlowchart(modleDto);
        return getResult(resultMap);
    }

    @ApiOperation(value = "删除课件流程图", httpMethod = "POST")
    @PostMapping("/deleteFlowchart")
    @LMSLog(desc = "删除课件流程图", otype = LogType.Delete, objname = "删除课件流程图")
    public Result deleteFlowchart(@RequestParam String id) {
        HashMap<String, Object> queryResultMap = coursewareService.getFlowchart(id);
        Integer queryStatusCode = (Integer) queryResultMap.get("statusCode");
        String actDeModelId = "";
        if (queryStatusCode != null && queryStatusCode == 200) {
            Object flowModel = queryResultMap.get("flowModel");
            JSONObject flowModelMap = JSON.parseObject(JSON.toJSONString(flowModel));
            actDeModelId = flowModelMap.getString("actDeModelId");
        }
        if (queryStatusCode == null || queryStatusCode != 200 || StringUtils.isEmpty(actDeModelId)){
            return Result.error("操作失败！没有获取到流程图数据", null);
        }
        HashMap<String, Object> resultMap = coursewareService.deleteFlowchart(actDeModelId);
        return getResult(resultMap);
    }

    @NotNull
    private Result getResult(HashMap<String, Object> resultMap) {
        Integer statusCode = (Integer) resultMap.get("statusCode");
        if (statusCode != null && statusCode == 200) {
            return Result.OK(Optional.ofNullable(resultMap.get("message")).orElse("操作成功！").toString());
        } else {
            return Result.error(Optional.ofNullable(resultMap.get("message")).orElse("操作失败！").toString(), null);
        }
    }

    @ApiOperation(value = "根据课件id获取课件流程图", httpMethod = "POST")
    @GetMapping("/getFlowchart")
    @LMSLog(desc = "根据课件id获取课件流程图", otype = LogType.Get, objname = "根据课件id获取课件流程图")
    public Result<HashMap<String, Object>> getFlowchart(@RequestParam String id) {
        HashMap<String, Object> resultMap = coursewareService.getFlowchart(id);
        Integer statusCode = (Integer) resultMap.get("statusCode");
        if (statusCode != null && statusCode == 200) {
            return Result.OK(Optional.ofNullable(resultMap.get("message")).orElse("操作成功！").toString(), resultMap);
        } else {
            return Result.error(Optional.ofNullable(resultMap.get("message")).orElse("操作失败！").toString(), null);
        }
    }

    public String setCoursewareLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.Save)) { // 新增数据日志
            Courseware p = (Courseware) (args[0]);
            objname = StringHelper.null2String(p.getName());
        } else if (lmslog.otype().equals(LogType.Update)) { // 编辑数据日志
            Courseware p = (Courseware) (args[0]);
            objname = StringHelper.null2String(p.getName());
        } else if (lmslog.otype().equals(LogType.Delete)) { // 删除数据日志
            String ids = (String) (args[0]);
            String[] idarray = ids.split(",");
            for (String id : idarray) {
                Courseware p = coursewareService.getById(id);
                objname = objname.isEmpty() ? StringHelper.null2String(p.getName()) : objname + "," + StringHelper.null2String(p.getName());
            }
        }
        return objname;
    }

}
