/**
 * FileName:	Clientimportexcel.java
 * Author:		<PERSON><PERSON><PERSON><PERSON>
 * Time:		2013-3-4锟斤拷锟斤拷11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.base.importexcel.service.impl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.base.feign.model.Subject;
import com.lms.base.subject.mapper.SubjectMapper;
import com.lms.common.service.impl.BaseServiceImpl;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service("ImportExcelService")
public class ImportExcelServiceImpl extends BaseServiceImpl<BaseMapper<Subject>, Subject> {

	@Resource
	private SubjectMapper subjectDao;

	/*导入Excel文件进行解析，然后将内容填充到数据库里*/

	private int colNum;

	public Map readExcelFile(String path){
		return readExcelFile(path,2);
	}

	public Map readExcelFile(String path,int Index){
		String excelPath = path;
		Map<Integer,Map<Integer,Object>> map = new HashMap<Integer,Map<Integer,Object>>();
		HSSFWorkbook wb=null;
		XSSFWorkbook wb1=null;
		try{
			File excel = new File(excelPath);
			if(excel.isFile()&&excel.exists()){
				String[] split = excel.getName().split("\\.");
				if("xls".equals(split[1])){
					FileInputStream fileStream = new FileInputStream(excel);
					wb = new HSSFWorkbook(fileStream);
					HSSFSheet sheet = wb.getSheetAt(0);
					HSSFRow row =sheet.getRow(Index-1);//第二行是列名
					//1.获取标题总列数和列名
					colNum=row.getPhysicalNumberOfCells();
					String[] title = new String[colNum];
					for(int i=0;i<colNum;i++){
						title[i] =row.getCell(i).getStringCellValue();
						title[i] = title[i].replaceAll("\r|\n|","");
						title[i] = title[i].replace(" ","");//去掉空格
					}
					//2.读取excel表中的内容
					map=readExcelContent(wb,Index);
					//3.将数据存到数据库里面
//					resultInformation=this.importexcelDao.insetExcelDataToDataBase(formatName,title,map);
				}else if("xlsx".equals(split[1])){
					FileInputStream fileStream = new FileInputStream(excel);
					wb1 = new XSSFWorkbook(fileStream);
					XSSFSheet sheet = wb1.getSheetAt(0);
					XSSFRow row =sheet.getRow(Index-1);//第二行是列名
					//1.获取标题总列数和列名
					colNum=row.getPhysicalNumberOfCells();
					String[] title = new String[colNum];
					for(int i=0;i<colNum;i++){
						title[i] =row.getCell(i).getStringCellValue();
						title[i] = title[i].replaceAll("\r|\n|","");
						title[i] = title[i].replace(" ","");//去掉空格
					}
					//2.读取excel表中的内容
					map=readExcelContent1(wb1,Index);
					//3.将数据存到数据库里面
//					resultInformation=this.importexcelDao.insetExcelDataToDataBase(formatName,title,map);
				}else{
					wb = null;
					wb1 = null;
				}
			}
		}catch (IOException e){
			e.printStackTrace();
		}
		return map;
	}
	public Map<Integer,Map<Integer,Object>> readExcelContent(HSSFWorkbook wb){
		return readExcelContent(wb,2);
	}
	public Map<Integer,Map<Integer,Object>> readExcelContent(HSSFWorkbook wb,int index){
		if(wb==null){
		}
		Map<Integer,Map<Integer,Object>> content=new HashMap<Integer, Map<Integer, Object>>();
		HSSFSheet sheet =wb.getSheetAt(0);
		//得到总行数
		int rowNum = sheet.getLastRowNum();
		//正文内容应该从第三行开始,第一行是大表头。第二行是列名
		for(int i=index;i<=rowNum;i++){
			HSSFRow row=sheet.getRow(i);
			int j=0;
			Map<Integer,Object> cellValue=new HashMap<Integer, Object>();
			while (j<colNum) {
				HSSFCell cell=row.getCell(j);
				Object obj = getCellFormatValue(cell);
				cellValue.put(j,obj);
				j++;
			}
			content.put(i-index,cellValue);
		}
		return content;
	}
	public Map<Integer,Map<Integer,Object>> readExcelContent1(HSSFWorkbook wb){
		return readExcelContent(wb,2);
	}
	public Map<Integer,Map<Integer,Object>> readExcelContent1(XSSFWorkbook wb,int index){
		if(wb==null){
		}
		Map<Integer,Map<Integer,Object>> content=new HashMap<Integer, Map<Integer, Object>>();
		XSSFSheet sheet =wb.getSheetAt(0);
		//得到总行数
		int rowNum = sheet.getLastRowNum();
		//正文内容应该从第三行开始,第一行是大表头。第二行是列名
		for(int i=index;i<=rowNum;i++){
			XSSFRow row=sheet.getRow(i);
			int j=0;
			Map<Integer,Object> cellValue=new HashMap<Integer, Object>();
			while (j<colNum) {
				XSSFCell cell=row.getCell(j);
				Object obj = getCellFormatValue(cell);
				cellValue.put(j,obj);
				j++;
			}
			content.put(i-index,cellValue);
		}
		return content;
	}
	//根据cell类型设置数据
	private Object getCellFormatValue(Cell cell){
		Object cellvalue ="";
		if(cell !=null) {
			switch (cell.getCellType()){
				case NUMERIC://
				case FORMULA:{
					if(DateUtil.isCellDateFormatted(cell)){
						Date date = cell.getDateCellValue();
						cellvalue=date;
					}else{
						cellvalue=String.valueOf((int)cell.getNumericCellValue());
					}
					break;
				}
				case STRING:
					cellvalue=cell.getRichStringCellValue().getString();
					break;
				default:
					cellvalue="";
			}
		}else{
			cellvalue="";
		}
		return cellvalue;
	}

}
