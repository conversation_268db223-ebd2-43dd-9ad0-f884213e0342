package com.lms.system.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.LogObjname;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Setting entity. <AUTHOR> Persistence Tools
 */

@TableName("B_SETTING")
@Data
@ApiModel(value = "系统配置对象")
public class Setting implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "系统配置唯一标识")
    private String id;

    @ApiModelProperty(value = "系统配置名称")
    @LogObjname
    private String itemname;

    @ApiModelProperty(value = "系统配置值")
    private String itemvalue;

    @ApiModelProperty(value = "系统配置描述")
    private String itemdesc;

    @ApiModelProperty(value = "序号")
    private String itemorder;

    @ApiModelProperty(value = "状态")
    private Integer status;


}
