import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 模型列表
export const getModel = (data?: any) => {
  return http.request<any>("post", baseUrlApi("model/list"), { data });
};
// 模型列表（分页）
export const getModelByPageList = (data?: any) => {
  return http.request<any>("post", baseUrlApi("model/listPage"), { data });
};
// 新增模型
export const addModel = (data?: any) => {
  return http.request<any>("post", baseUrlApi("model/add"), { data });
};
// 修改模型
export const updateModel = (data?: any) => {
  return http.request<any>("post", baseUrlApi("model/update"), { data });
};
// 删除模型
export const deleteModel = (data?: any) => {
  return http.request<any>("delete", baseUrlApi("model/delete?ids=" + data));
};

// 查询模型属性
export const getModelAttributeList = (data?: any) => {
  return http.request<any>("post", baseUrlApi("modelAttribute/listPage"), {
    data
  });
};
// 查询模型属性
export const getModelAttribute = (data?: any) => {
  return http.request<any>(
    "get",
    baseUrlApi("model/Attribute/queryModelAttribute"),
    {
      data
    }
  );
};
// 新增模型属性
export const addModelAttribute = (data?: any) => {
  return http.request<any>("post", baseUrlApi("modelAttribute/add"), {
    data
  });
};
// 修改模型属性
export const updateModelAttribute = (data?: any) => {
  return http.request<any>("post", baseUrlApi("modelAttribute/update"), {
    data
  });
};
// 删除模型属性
export const deleteModelAttribute = (data?: any) => {
  return http.request<any>("delete", baseUrlApi("modelAttribute/delete"), {
    params: data
  });
};

// 查询模型知识图谱
export const getModelCharts = (data?: any) => {
  return http.request<any>(
    "get",
    baseUrlApi("model/queryTuGraph?modelId=") + data
  );
};
export const getById = (data?: any) => {
  return http.request<any>("get", baseUrlApi("model/getById/" + data));
};
