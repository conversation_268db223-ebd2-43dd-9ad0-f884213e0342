package com.lms.base.coursewarelearnrecord.service.impl;

import com.lms.base.coursewarelearnrecord.service.CoursewareLearnRecordService;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.dao.query.Parameter;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.NumberHelper;
import com.lms.common.util.StringHelper;
import com.lms.base.coursewarelearnrecord.mapper.CoursewareLearnRecordMapper;
import com.lms.base.feign.model.CoursewareLearnRecord;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service("CoursewareLearnRecordService")
public class CoursewareLearnRecordServiceImpl extends BaseServiceImpl<CoursewareLearnRecordMapper, CoursewareLearnRecord> implements CoursewareLearnRecordService {

    @Resource
    private CoursewareLearnRecordMapper coursewareLearnRecordMapper;


    public int getMinCoursewareByCourse(Map params) {
        String hql = "select c.sequencenumber from u_courseware c left join u_coursewarelearnrecord d on c.id=d.coursewareid and d.courseId='"
                + StringHelper.null2String(params.get("courseId")) + "' and d.cardNum='" + StringHelper.null2String(params.get("cardNum")) + "' where c.componentid= '"
                + StringHelper.null2String(params.get("courseId")) + "' and IFNULL(d.status,0)=0 order by sequencenumber limit 1";
        Map result = coursewareLearnRecordMapper.getMinCoursewareByCourse(hql);
        return NumberHelper.string2Int(result.get("sequencenumber"), 9999);
    }


    public CoursewareLearnRecord getCoursewareLearnRecord(Map params) {
        PageInfo pageInfo = new PageInfo();
        Parameter courseIdParam = Parameter.getParameter(
                "S_EQ_courseId", StringHelper.null2String(params.get("courseId")));
        pageInfo.getParameters().add(courseIdParam);
        Parameter courewareIdParam = Parameter.getParameter(
                "S_EQ_coursewareid", StringHelper.null2String(params.get("coursewareId")));
        pageInfo.getParameters().add(courewareIdParam);
        Parameter cardnumParam = Parameter.getParameter(
                "S_EQ_cardNum", StringHelper.null2String(params.get("cardNum")));
        pageInfo.getParameters().add(cardnumParam);
        List<CoursewareLearnRecord> coursewareLearnRecordList = this.listByCondition(pageInfo).getRecords();
        CoursewareLearnRecord coursewareLearnRecord = null;
        if (coursewareLearnRecordList.size() > 0) {
            coursewareLearnRecord = coursewareLearnRecordList.get(0);
        }
        return coursewareLearnRecord;
    }

}
