import { translateText } from "@/utils/translation";
import { systemDataManager } from "@/utils/SystemDataManager.ts";
import { courseTypeOptions } from "@/data/mrPlatform";
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined
} from "@ant-design/icons-vue";

// 查询条件配置
export const queryItems = [
  {
    key: "PARAMETER_S_LIKE_courseName",
    label: translateText("课程名称"),
    component: "a-input",
    props: {
      placeholder: translateText("请输入课程名称"),
      allowClear: true
    }
  },
  {
    key: "PARAMETER_S_LIKE_number",
    label: translateText("课程编号"),
    component: "a-input",
    props: {
      placeholder: translateText("请输入课程编号"),
      allowClear: true
    }
  },
  {
    key: "PARAMETER_S_LIKE_teacherName",
    label: translateText("教员"),
    component: "a-input",
    props: {
      placeholder: translateText("请输入教员"),
      allowClear: true
    }
  },
  {
    key: "PARAMETER_S_EQ_courseType",
    label: translateText("课程类型"),
    component: "a-select",
    props: {
      placeholder: translateText("请选择课程类型"),
      allowClear: true,
      options: courseTypeOptions
    }
  },
  {
    key: "PARAMETER_S_EQ_status",
    label: translateText("状态"),
    component: "a-select",
    props: {
      placeholder: translateText("请选择状态"),
      allowClear: true,
      options: [
        { label: translateText("发布"), value: 5 },
        { label: translateText("拟制"), value: 1 }
      ]
    }
  }
];

// 列表字段配置
export const columns = [
  {
    label: translateText("课程名称"),
    prop: "courseName",
    width: 150,
    align: "center"
  },
  {
    label: translateText("课程编号"),
    prop: "number",
    width: 120,
    align: "center"
  },
  {
    label: translateText("课程类型"),
    prop: "courseType",
    width: 100,
    align: "center",
    render: (current: string) => {
      return systemDataManager.translateDict(courseTypeOptions, current);
    }
  },
  {
    label: translateText("教员"),
    prop: "teacherName",
    width: 100,
    align: "center"
  },
  {
    label: translateText("课时"),
    prop: "classHour",
    width: 80,
    align: "center"
  },
  {
    label: translateText("版本"),
    prop: "version",
    width: 80,
    align: "center"
  },
  {
    label: translateText("状态"),
    prop: "status",
    width: 100,
    align: "center",
    renderType: "status",
    statusMap: {
      5: { status: "default", text: translateText("发布"), color: "purple" },
      1: { status: "success", text: translateText("拟制"), color: "blue" }
    }
  }
  // {
  //   label: translateText("密级"),
  //   prop: "mlimit",
  //   width: 80,
  //   align: "center"
  // }
];

// 页面按钮配置
export const btnConfig = [
  {
    key: "add",
    label: translateText("新增"),
    icon: PlusOutlined,
    type: "batch",
    props: { type: "primary" }
  },
  {
    key: "batchDelete",
    label: translateText("批量删除"),
    icon: DeleteOutlined,
    type: "batch",
    props: { danger: true }
  },
  {
    key: "edit",
    label: translateText("编辑"),
    icon: EditOutlined,
    type: "row",
    props: { type: "primary" }
  },
  {
    key: "delete",
    label: translateText("删除"),
    icon: DeleteOutlined,
    type: "row",
    props: { danger: true }
  }
];
