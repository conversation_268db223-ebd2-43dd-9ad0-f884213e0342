package com.sbtr.workflow.client;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * @MethodName
 * @Description   调用sbtr-message相关接口
 * @Description   本地开发时可以指定 ip+网关端口进行访问  在configuration中配置 url = "http://*************:9774/messsage"
 * @Param null
 * @Return
 * <AUTHOR>
 * @Date 2020-11-23 14:37
 */
//@FeignClient(
//        name = FeignClientName.MESSAGE_SERVER_NAME,
//        configuration = FeignConfig.class,
//        fallbackFactory = MessageClientFallbackFactory.class)
public interface MessageClient {


    /**
     * @MethodName sendSimpleMail
     * @Description  发送邮件
     * @Param map
     * @Param toMail 接收人邮箱
     * @Param subject 主题
     * @Param content 发送内容
     * @Return java.lang.Object
     * <AUTHOR>
     * @Date 2020-11-23 14:36
     */
    @PostMapping("/messageMail/sendSimpleMail")
    Object sendSimpleMail(@RequestParam Map<String, Object> map);


    /**
     * @MethodName sendSms
     * @Description  发送短信
     * @Param phoneNumber 接收人手机号码
     * @Param signName 签名
     * @Param templateCode 模板code
     * @Param templateParam 模板参数
     * @Param SenderId 发送人id
     * @Return java.lang.Object
     * <AUTHOR>
     * @Date 2020-11-23 14:36
     */
    @PostMapping(value = "/msgShortMessage/sendSms")
    Object sendSms(@RequestParam("phoneNumber") String phoneNumber,
                   @RequestParam("signName") String signName,
                   @RequestParam("templateCode") String templateCode,
                   @RequestParam("templateParam") String templateParam,
                   @RequestParam("SenderId") String SenderId);


    /**
     * @MethodName save
     * @Description  发送站内信
     * @Param map
     * @Param title 标题
     * @Param type   类型
     * @Param description 描述
     * @Param receiverId 接收人工号
     * @Param url url
     * @Param content 内容
     * @Return java.lang.Object
     * <AUTHOR>
     * @Date 2020-11-23 14:35
     */
    @PostMapping(value = "/msgWebNotice/save")
    Object save(@RequestParam Map<String, Object> map);
}
