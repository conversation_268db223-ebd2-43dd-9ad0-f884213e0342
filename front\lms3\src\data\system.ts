// 用户管理相关选项数据
import { systemDataManager } from "@/utils/SystemDataManager.ts";

export const roleOptions = [
  { label: "教员", value: "5142f4720518455fbc9c8c21311f20ae" },
  { label: "培训管理员", value: "1be79c2956684d97a8f8712588c34026" },
  { label: "学员", value: "2e55eb426aa84654944ebc0cdbf02af8" }
];

export const departTreeOptions =
  await systemDataManager.loadDepartmentTree("1");

// 人员信息管理相关选项数据
export const genderOptions = await systemDataManager.initOptions("sex");

export const positionOptions = await systemDataManager.initOptions("duty");

export const specialtyOptions =
  await systemDataManager.initOptions("specialty");

// 系统日志管理相关选项数据
export const logTypeOptions = [
  { label: "登录", value: "登录" },
  { label: "新增", value: "新增" },
  { label: "修改", value: "修改" },
  { label: "删除", value: "删除" },
  { label: "查询", value: "查询" },
  { label: "导出", value: "导出" },
  { label: "导入", value: "导入" }
];
