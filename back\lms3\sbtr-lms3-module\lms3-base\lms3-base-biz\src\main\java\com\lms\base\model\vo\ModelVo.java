package com.lms.base.model.vo;


import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lms.base.modelAttribute.vo.ModelAttributeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Data
@ApiModel(value = "模型维护VO")
public class ModelVo {

    @ApiModelProperty(value = "模型id")
    private String id;

    @ApiModelProperty(value = "模型名称")
    private String name;

    @ApiModelProperty(value = "模型编号")
    private String number;

    @ApiModelProperty(value = "模型描述")
    private String desc;

    /**
     * 属性名加属性值字符串,用于搜索
     */
    private String searchAttributeHelp;

    /**
     * 单个属性 用于封装tugraph返回的属性
     */
    private ModelAttributeVo attribute;

    @ApiModelProperty(value = "模型属性列表")
    private List<ModelAttributeVo> attributeList;

    @ApiModelProperty(value = "父模型id")
    private String parentId;

    /**
     * 单个子模型 用于封装tugraph返回的子模型
     */
    private ModelVo subModel;

    @ApiModelProperty(value = "子模型列表")
    private List<ModelVo> subModelList;


    @ApiModelProperty(value = "模型文件地址")
    private String fileUrl;

    @ApiModelProperty(value = "模型预览地址")
    private String previewUrl;

    @ApiModelProperty(value = "上传人")
    private String uploadPerson;

    @ApiModelProperty(value = "上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date uploadTime;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    // 从TuGraph节点数据构造Model对象
    public static ModelVo fromTuGraphNode(Map<String, Object> nodeData) {
        return fromTuGraphNode(nodeData, null);
    }

    public static ModelVo fromTuGraphNode(Map<String, Object> nodeData, Map<String, Object> attributes) {
        return fromTuGraphNode(nodeData, attributes, null);
    }

    // 从TuGraph节点数据构造Model对象 带模型属性数据
    public static ModelVo fromTuGraphNode(Map<String, Object> nodeData, Map<String, Object> attributes, Map<String, Object> subModel) {
        ModelVo model = new ModelVo();
        @SuppressWarnings("unchecked")
        Map<String, Object> properties = (Map<String, Object>) nodeData.get("properties");
        model.setId((String) properties.get("id"));
        model.setName((String) properties.get("name"));
        model.setNumber((String) properties.get("number"));
        model.setDesc((String) properties.get("desc"));
        model.setFileUrl((String) properties.get("fileUrl"));
        model.setPreviewUrl((String) properties.get("previewUrl"));
        model.setUploadPerson((String) properties.get("uploadPerson"));
        model.setFileType((String) properties.get("fileType"));
        model.setSearchAttributeHelp((String) properties.get("searchAttributeHelp"));
        // 处理日期
        String uploadTimeStr = (String) properties.get("uploadTime");
        try {
            model.setUploadTime(new SimpleDateFormat("yyyy-MM-dd").parse(uploadTimeStr));
        } catch (ParseException e) {
            model.setUploadTime(null);
        }
        // 处理模型属性
        if (attributes != null) {
            Map<String, Object> attributeProperties = (Map<String, Object>) attributes.get("properties");
            attributeProperties.replaceAll((k, v) -> "null".equals(v) ? null : v);
            model.setAttribute(JSON.parseObject(JSON.toJSONString(attributeProperties), ModelAttributeVo.class));
        }
        // 处理子模型
        if (subModel != null) {
            Map<String, Object> subModelProperties = (Map<String, Object>) subModel.get("properties");
            subModelProperties.replaceAll((k, v) -> "null".equals(v) ? null : v);
            model.setSubModel(JSON.parseObject(JSON.toJSONString(subModelProperties), ModelVo.class));
        }
        return model;
    }


    public static String getIdFromNode(Map<String, Object> nodeData) {
        @SuppressWarnings("unchecked")
        Map<String, Object> properties = (Map<String, Object>) nodeData.get("properties");
        return (String) properties.get("id");
    }
}
