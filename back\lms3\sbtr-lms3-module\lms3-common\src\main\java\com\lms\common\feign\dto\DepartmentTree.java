package com.lms.common.feign.dto;

import cn.hutool.core.util.ObjectUtil;
import com.lms.common.model.BaseModel;
import com.lms.common.util.StringHelper;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Data
public class DepartmentTree extends BaseModel {

    private String id;

    private String code;

    private String name;

    private String shortname;

    private Integer seqno;

    private Integer status;

    private String pid;

    private String parentname;

    private String fullpath;

    private String fullpathname;

    private Integer orgFlag; //组训单位标识

    private List<DepartmentTree> children = new ArrayList<>(); // 子节点列表

    public void addChild(DepartmentTree child) {
        this.children.add(child);
    }

    /**
     * 将平铺的数据组装成树形结构
     *
     * @param nodes 平铺的节点列表
     * @return 树形结构的根节点列表
     */
    public static List<DepartmentTree> buildTree(List<DepartmentTree> nodes, String rootid) {
        // 用于存储所有节点的Map，key为节点ID，value为节点对象
        Map<String, DepartmentTree> nodeMap = new HashMap<>();
        // 根节点列表
        List<DepartmentTree> rootNodes = new ArrayList<>();
        // 将所有节点存入Map
        for (DepartmentTree node : nodes) {
            nodeMap.put(node.getId(), node);
        }

        // 遍历所有节点，构建树形结构
        for (DepartmentTree node : nodes) {
            String pid = StringHelper.null2String(node.getPid());
            String id = StringHelper.null2String(node.getId());
            if (StringHelper.null2String(rootid).equals(id) || pid.isEmpty()) {
                // 如果pid为空值或者id等于rootid，说明是根节点
                rootNodes.add(node);
            } else {
                // 否则，找到父节点，并将当前节点添加到父节点的子节点列表中
                DepartmentTree parent = nodeMap.get(pid);
                if (ObjectUtil.isNotEmpty(parent)) {
                    parent.addChild(node);
                }
            }
        }
        return rootNodes;
    }
}
