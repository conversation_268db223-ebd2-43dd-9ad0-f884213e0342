@use "theme";
@use "transition";
@use "element-plus";
@use "sidebar";
@use "dark";

/* 自定义全局 CssVar */
:root {
  /* 左侧菜单展开、收起动画时长 */
  --pure-transition-duration: 0.3s;

  /* 常用border-color 需要时可取用 */
  --pure-border-color: rgb(5 5 5 / 6%);

  /* switch关闭状态下的color 需要时可取用 */
  --pure-switch-off-color: #a6a6a6;

  /** 主题色 */
  --pure-theme-sub-menu-active-text: initial;
  --pure-theme-menu-bg: none;
  --pure-theme-menu-hover: none;
  --pure-theme-sub-menu-bg: transparent;
  --pure-theme-menu-text: initial;
  --pure-theme-sidebar-logo: none;
  --pure-theme-menu-title-hover: initial;
  --pure-theme-menu-active-before: transparent;

  /* 颜色变量 */
  --primary-color: #1890ff;
  --primary-hover: #40a9ff;
  --primary-active: #096dd9;

  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --info-color: #1890ff;

  /* 灰度色彩 */
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #e8e8e8;
  --gray-300: #d9d9d9;
  --gray-400: #bfbfbf;
  --gray-500: #8c8c8c;
  --gray-600: #595959;
  --gray-700: #434343;
  --gray-800: #262626;
  --gray-900: #1f1f1f;

  /* 文字颜色 */
  --text-primary: rgba(0, 0, 0, 0.85);
  --text-secondary: rgba(0, 0, 0, 0.65);
  --text-disabled: rgba(0, 0, 0, 0.25);

  /* 背景颜色 */
  --bg-white: #ffffff;
  --bg-gray: #fafafa;
  --bg-light: #f0f2f5;

  /* 边框颜色 */
  --border-color: #d9d9d9;
  --border-light: #f0f0f0;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md:
    0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg:
    0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* 圆角 */
  --border-radius-sm: 2px;
  --border-radius: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;

  /* 间距 */
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-8: 32px;
  --spacing-10: 40px;
  --spacing-12: 48px;
  --spacing-16: 64px;

  /* 字体大小 */
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;

  /* 字体粗细 */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* 行高 */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;

  /* 过渡动画 */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.2s ease-in-out;
  --transition-slow: 0.3s ease-in-out;
}

/* 灰色模式 */
.html-grey {
  filter: grayscale(100%);
}

/* 色弱模式 */
.html-weakness {
  filter: invert(80%);
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Ant Design Vue 自定义样式 */
.ant-btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.ant-btn-primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.ant-tree .ant-tree-node-content-wrapper {
  transition: all var(--transition-normal);
}

.ant-input:focus,
.ant-input-focused {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

// 通用工具类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

/* 间距工具类 */
.p-0 {
  padding: 0;
}

.p-1 {
  padding: var(--spacing-1);
}

.p-2 {
  padding: var(--spacing-2);
}

.p-3 {
  padding: var(--spacing-3);
}

.p-4 {
  padding: var(--spacing-4);
}

.p-5 {
  padding: var(--spacing-5);
}

.p-6 {
  padding: var(--spacing-6);
}

.m-0 {
  margin: 0;
}

.m-1 {
  margin: var(--spacing-1);
}

.m-2 {
  margin: var(--spacing-2);
}

.m-3 {
  margin: var(--spacing-3);
}

.m-4 {
  margin: var(--spacing-4);
}

.m-5 {
  margin: var(--spacing-5);
}

.m-6 {
  margin: var(--spacing-6);
}

/* 文本颜色 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-disabled {
  color: var(--text-disabled);
}

/* 背景颜色 */
.bg-white {
  background-color: var(--bg-white);
}

.bg-gray {
  background-color: var(--bg-gray);
}

.bg-light {
  background-color: var(--bg-light);
}
