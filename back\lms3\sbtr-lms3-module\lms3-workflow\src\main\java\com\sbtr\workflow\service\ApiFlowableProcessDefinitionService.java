package com.sbtr.workflow.service;


import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.vo.ProcessDefinitionVo;

import java.util.List;

/**
 * 流程定义
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:35:23
 */
public interface ApiFlowableProcessDefinitionService extends BaseService {

    /**
     * 流程定义分页数据查询
     *
     * @param pageParam 分页参数
     * @param category 分类标识
     * @param processModelType 流程模型类型  1 自定义流程界面  2 托拉拽界面
     * @param name 流程名称
     */
    PageSet<ProcessDefinitionVo> getPageSet(PageParam pageParam, String category,String processModelType,String name);


    /**
     * 删除流程定义
     *
     * @param deploymentId 部署id
     */
    Object deleteDeployment(String deploymentId);

    /**
     * 流程定义激活挂起
     *
     * @param processDefinitionId 流动定义Id
     * @param suspensionState 1挂起  2激活
     */
    Object suspendOrActivateProcessDefinitionById(String processDefinitionId, int suspensionState);

    /**
     * 根据模型key和主板本查询所有数据
     *
     * @param modelKey 模型key
     * @param majorVersion 是否主版本
     */
    List<ProcessDefinitionVo> getListByIdAndModelKey(String modelKey, String majorVersion);

    /**
     * 根据流程定义和模型key更新是否主版本
     *
     * @param procdefId 流动定义Id
     * @param modelKey 模型key
       * @param majorVersion 是否主版本
     */
    Integer updateMajorVersion(String procdefId, String modelKey, String majorVersion);

    /**
     * 根据modelKey查询有多少条数据
     *
     * @param modelKey 模型key
     */
    Integer getCountSumByModelKey(String modelKey);

    /**
     * 根据模型key更新是否主版本
     *
     * @param modelKey 模型key
     */
    Integer updateMajorVersionByModelKey(String modelKey,String majorVersion);

    /**
     * 根据部署Id查询流程定义Id
     *
     * @param id 流程部署Id
     */
    String getProdefIdByDeployId(String id);


    /**
     * 根据定义Id查询详情数据
     *
     * @param processDefinitionId 流程部署Id
     */
    ProcessDefinitionVo getProdefIdById(String processDefinitionId);

    //
    /**
     * 获取所有自定界面发布的流程
     *
     */
    List<ProcessDefinitionVo> getCustomInterface();

    List<ProcessDefinitionVo> getAllData(PageParam pageParam, String category, String processModelType, String name);
}
