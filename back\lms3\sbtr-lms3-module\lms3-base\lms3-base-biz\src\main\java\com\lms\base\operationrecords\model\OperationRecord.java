package com.lms.base.operationrecords.model;

import com.influxdb.annotations.Column;
import com.influxdb.annotations.Measurement;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.Instant;

/**
 * 操作记录表实体，对应InfluxDB measurement：u_operation_records
 */
@Data
@Measurement(name = "u_operation_records")
@ApiModel(value = "OperationRecord", description = "操作记录表实体")
public class OperationRecord {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @Column(tag = true)
    private String id;

    /**
     * 时间戳，ISO-8601字符串，如2024-01-01T10:00:00Z
     */
    @ApiModelProperty(value = "时间戳，ISO-8601字符串，如2024-01-01T10:00:00Z")
    @Column(name = "_time", timestamp = true)
    private Instant timestamp;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @Column(tag = true)
    private String userId;

    /**
     * 课程ID
     */
    @ApiModelProperty(value = "课程ID")
    @Column(tag = true)
    private String courseId;

    /**
     * 课件ID
     */
    @ApiModelProperty(value = "课件ID")
    @Column(tag = true)
    private String sceneId;

    /**
     * 头部位置X
     */
    @ApiModelProperty(value = "头部位置X")
    @Column
    private Double positionX;
    /**
     * 头部位置Y
     */
    @ApiModelProperty(value = "头部位置Y")
    @Column
    private Double positionY;
    /**
     * 头部位置Z
     */
    @ApiModelProperty(value = "头部位置Z")
    @Column
    private Double positionZ;
    /**
     * 头部旋转X
     */
    @ApiModelProperty(value = "头部旋转X")
    @Column
    private Double rotationX;
    /**
     * 头部旋转Y
     */
    @ApiModelProperty(value = "头部旋转Y")
    @Column
    private Double rotationY;
    /**
     * 头部旋转Z
     */
    @ApiModelProperty(value = "头部旋转Z")
    @Column
    private Double rotationZ;
    /**
     * 左手位置X
     */
    @ApiModelProperty(value = "左手位置X")
    @Column
    private Double handPositionLeftX;
    /**
     * 左手位置Y
     */
    @ApiModelProperty(value = "左手位置Y")
    @Column
    private Double handPositionLeftY;
    /**
     * 左手位置Z
     */
    @ApiModelProperty(value = "左手位置Z")
    @Column
    private Double handPositionLeftZ;
    /**
     * 右手位置X
     */
    @ApiModelProperty(value = "右手位置X")
    @Column
    private Double handPositionRightX;
    /**
     * 右手位置Y
     */
    @ApiModelProperty(value = "右手位置Y")
    @Column
    private Double handPositionRightY;
    /**
     * 右手位置Z
     */
    @ApiModelProperty(value = "右手位置Z")
    @Column
    private Double handPositionRightZ;
    /**
     * 左手旋转X
     */
    @ApiModelProperty(value = "左手旋转X")
    @Column
    private Double handRotationLeftX;
    /**
     * 左手旋转Y
     */
    @ApiModelProperty(value = "左手旋转Y")
    @Column
    private Double handRotationLeftY;
    /**
     * 左手旋转Z
     */
    @ApiModelProperty(value = "左手旋转Z")
    @Column
    private Double handRotationLeftZ;
    /**
     * 右手旋转X
     */
    @ApiModelProperty(value = "右手旋转X")
    @Column
    private Double handRotationRightX;
    /**
     * 右手旋转Y
     */
    @ApiModelProperty(value = "右手旋转Y")
    @Column
    private Double handRotationRightY;
    /**
     * 右手旋转Z
     */
    @ApiModelProperty(value = "右手旋转Z")
    @Column
    private Double handRotationRightZ;
    /**
     * 心率
     */
    @ApiModelProperty(value = "心率")
    @Column
    private Double heartRate;
    /**
     * 眼动追踪X
     */
    @ApiModelProperty(value = "眼动追踪X")
    @Column
    private Double eyeTrackingX;
    /**
     * 眼动追踪Y
     */
    @ApiModelProperty(value = "眼动追踪Y")
    @Column
    private Double eyeTrackingY;
    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    @Column
    private String actionType;
    /**
     * 操作目标
     */
    @ApiModelProperty(value = "操作目标")
    @Column
    private String actionTarget;
    /**
     * 操作结果
     */
    @ApiModelProperty(value = "操作结果")
    @Column
    private Boolean actionResult;
} 