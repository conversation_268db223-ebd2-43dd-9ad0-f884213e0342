package com.sbtr.workflow.service;

import com.sbtr.workflow.model.ActMyProcessCopy;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;

/**
 * 流程抄送表
 *
 * <AUTHOR>
 * @Email sbtr@.com
 * @Date 2021-03-02 17:03:24
 */
public interface ActMyProcessCopyService extends WorkflowBaseService<ActMyProcessCopy, String> {

    /**
     * 分页
     *
     * @return
     * @Param pageParam 分页参数
     * @Param filterSort 过滤排序字段
     * @Param userNameId 工号
     */
    PageSet<ActMyProcessCopy> getPageSet(PageParam pageParam, String formName, String userNameId);

    /**
     * 批量删除
     *
     * @return
     * @Param uuid 主键
     */
    int executeDeleteBatch(String[] uuids);

    /**
     * 点击详情
     *
     * @return
     * @Param uuid 主键
     */
    Object clickDetail(String taskId, String processInstanceId, String modelKey, String processDefinitionId, String nodeId) throws Exception;

    /**
     * 根据流程定义ID删除数据
     *
     * @return
     * @Param processDefinitionId 流程定义ID
     */
    Integer deleteByProcessDefinitionId(String processDefinitionId);

    /**
     * 获取我抄送别人的数据
     *
     * @return
     * @Param processDefinitionId 流程定义ID
     */
    PageSet<ActMyProcessCopy> getMyPageSet(PageParam pageParam, String formName, String userNameId);

    /**
     * 根据uuid修改流程抄送的状态
     *
     * @param uuid
     * @param reviewStatus
     * @return Integer
     */
    Integer updateReviewStatusByUuid(String uuid, String reviewStatus);
}
