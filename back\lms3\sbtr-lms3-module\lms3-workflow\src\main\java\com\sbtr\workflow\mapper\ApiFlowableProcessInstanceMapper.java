package com.sbtr.workflow.mapper;

import com.sbtr.workflow.vo.ProcessInstanceVo;
import com.sbtr.workflow.vo.TaskVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ApiFlowableProcessInstanceMapper {

    List<ProcessInstanceVo> getProcessInstanceMySql(@Param("modelKey") String modelKey, @Param("businessKey") String businessKey);

    List<ProcessInstanceVo> getProcessInstanceMySqlByBusinessKeys(@Param("modelKey") String modelKey, @Param("businessKeys") List<String> businessKeys);

    List<ProcessInstanceVo> getProcessInstanceOracle(@Param("modelKey") String modelKey, @Param("businessKey") String businessKey);

    List<ProcessInstanceVo> getPageSetMySql(@Param("name") String name);

    List<ProcessInstanceVo> getPageSetOracle(@Param("name") String name);

    int executeInsertSql(@Param("sql") String sql);

    List<TaskVo> getProcessFreeJumpData(@Param("processInstanceId") String processInstanceId);

    List<ProcessInstanceVo> getPageSetSqlServer(@Param("name") String name);

    List<ProcessInstanceVo> getRunProcessInstByParam(@Param("businessKey") String businessKey);
}
