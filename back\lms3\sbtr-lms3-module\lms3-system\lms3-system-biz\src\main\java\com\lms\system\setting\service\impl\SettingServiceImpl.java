/**
 * FileName:	ClientSetting.java
 * Author:		<PERSON><PERSON><PERSON><PERSON>
 * Time:		2013-3-4上午11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.system.setting.service.impl;


import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.system.setting.mapper.SettingMapper;
import com.lms.system.feign.model.Setting;
import com.lms.system.setting.service.SettingService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 保存项目的相关信息
 */
@Service
public class SettingServiceImpl extends BaseServiceImpl<SettingMapper, Setting> implements SettingService {

	@Resource
	private SettingMapper settingMapper;

}
