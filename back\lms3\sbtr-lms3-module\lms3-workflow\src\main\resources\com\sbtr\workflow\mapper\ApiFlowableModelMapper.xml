<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ApiFlowableModelMapper">

	<delete id="deleteDateByModelId">
		delete from act_de_model
		WHERE id = #{modelId}
	</delete>

	<select id="getPageSet" resultType="com.sbtr.workflow.vo.ActDeModelVo">
		SELECT DISTINCT
		a.id AS id,
		a.NAME AS modelName,
		a.model_key AS modelKey,
		a.created AS createTime,
		a.last_updated AS lastUpdated,
		a.created_by AS createUserNameId,
		a.process_model_type AS processModelType
		FROM
		act_de_model a
		WHERE 1= 1
		<if test="modelKey!=null and modelKey!=''">
			and a.model_key like '%'||#{modelKey}||'%'
		</if>
		<if test="modelName!=null and modelName!=''">
			and a.name  like '%'||#{modelName}||'%'
		</if>
		order by
		a.created DESC
	</select>

    <select id="getPageSetOracle" resultType="com.sbtr.workflow.vo.ActDeModelVo">
		SELECT
		m.uuid AS id,
		a.id AS modelId,
		a.NAME AS modelName,
		cc.major_version,
		a.model_key AS modelKey,
		b.CATEGORY_ AS modelType,
		a.created AS createTime,
		a.last_updated AS lastUpdated,
		a.created_by AS createUserNameId,
		a.process_model_type AS processModelType,
		cc.VERSION_ AS modelVersion,
		m.procdef_id as procdefId,
		cc.DEPLOYMENT_ID_ as deploymentId,
		f.name as formname
		FROM
		act_de_model a
		inner join  act_my_model m on a.model_key=m.ACT_DE_MODEL_KEY
		LEFT JOIN act_re_procdef cc ON cc.key_ = a.model_key and cc.ID_=m.procdef_id
		LEFT JOIN act_re_deployment b ON cc.DEPLOYMENT_ID_ = b.ID_
		LEFT JOIN act_form_configure f ON f.uuid = m.form_uuid
		WHERE 1= 1
		<if test="modelKey!=null and modelKey!=''">
			and a.model_key like '%'||#{modelKey}||'%'
		</if>
		<if test="modelName!=null and modelName!=''">
            and a.name  like '%'||#{modelName}||'%'
		</if>
		<if test="modelType!=null and modelType!=''">
            and b.CATEGORY_ = #{modelType}
		</if>
		order by
		b.DEPLOY_TIME_ DESC
	</select>

	<select id="getPageSetMySql" resultType="com.sbtr.workflow.vo.ActDeModelVo">
		SELECT
		m.uuid AS id,
		a.id AS modelId,
		a.NAME AS modelName,
		cc.major_version,
		a.model_key AS modelKey,
		b.CATEGORY_ AS modelType,
		a.created AS createTime,
		a.last_updated AS lastUpdated,
		a.created_by AS createUserNameId,
		a.process_model_type AS processModelType,
		cc.VERSION_ AS modelVersion,
		cc.ID_ as procdefId,
		cc.DEPLOYMENT_ID_ as deploymentId,
		f.name as formname
		FROM
		act_de_model a
		inner join  act_my_model m on a.model_key=m.ACT_DE_MODEL_KEY
		LEFT JOIN act_re_procdef cc ON cc.key_ = a.model_key and cc.ID_=m.procdef_id
		LEFT JOIN act_re_deployment b ON cc.DEPLOYMENT_ID_ = b.ID_
		LEFT JOIN act_form_configure f ON f.uuid = m.form_uuid
		WHERE 1=1
		<if test="modelKey!=null and modelKey!=''">
			and a.model_key like concat('%',#{modelKey},'%')
		</if>
		<if test="modelName!=null and modelName!=''">
			and a.name  like concat('%',#{modelName},'%')
		</if>
		<if test="modelType!=null and modelType!=''">
			and b.CATEGORY_ = #{modelType}
		</if>
		order by
		b.DEPLOY_TIME_ DESC
	</select>

	<select id="getListByModelKey" resultType="com.sbtr.workflow.vo.ActDeModelVo">
SELECT DISTINCT
	a.id AS id,
	cc.NAME_ AS modelName,
	a.model_key AS modelKey,
	b.CATEGORY_ AS modelType,
	b.DEPLOY_TIME_ AS createTime,
	a.last_updated AS lastUpdated,
	created_by AS createUserNameId,
	cc.VERSION_ AS modelVersion,
	cc.ID_ AS procdefId,
	cc.major_version as majorVersion,
	cc.DEPLOYMENT_ID_ as deploymentId
FROM
	act_de_model a
	LEFT JOIN act_re_deployment b ON a.model_key = b.KEY_
	LEFT JOIN act_re_procdef cc ON cc.DEPLOYMENT_ID_ = b.ID_
WHERE
a.model_key =#{modelKey}
ORDER BY
	b.DEPLOY_TIME_ DESC
	</select>
	<select id="getDataByModelKey" resultType="com.sbtr.workflow.vo.ActDeModelVo">
		select * from  act_de_model where model_key=#{modelKey}
	</select>

    <select id="getActReDeploymenListByModelKey" resultType="com.sbtr.workflow.vo.ActReDeploymentVo">
		select
			DISTINCT
			CATEGORY_ as category
		from  act_re_deployment where KEY_=#{key}

	</select>

    <select id="getModel" resultType="org.flowable.ui.modeler.domain.Model">
		select adm.*,adm.model_key as key from ACT_DE_MODEL adm where id = #{id}
	</select>

    <select id="findByKeyAndType" resultType="org.flowable.ui.modeler.domain.Model">
		select adm.*,adm.model_key as key from ACT_DE_MODEL adm where model_key = #{processId}  and model_type=#{modelTypeBpmn}
	</select>

    <update id="updateProcessModelTypeById">
			update  act_de_model set process_model_type = #{processModelType} where id =#{id}
	</update>

	<update id="updateNameByModelKey">
		update  act_de_model set name = #{name} where model_key =#{modelKey}
	</update>
</mapper>
