package com.sbtr.workflow.service;

import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import com.sbtr.workflow.dto.*;
import com.sbtr.workflow.model.ActHiActinst;
import com.sbtr.workflow.model.MyVariable;
import com.sbtr.workflow.model.TaskCommon;
import com.sbtr.workflow.vo.ProcessDefinitionVo;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface WorkflowService extends WorkflowBaseService<MyVariable, String> {


    /**
     * 获得已安装的流程
     *
     * @return
     */
    String[] getFlows();

    /**
     * 获得已安装的流程
     *
     * @return
     */
    PageSet<TemplateDto> getFlowsPageSet();

    /**
     * 部署流程
     *
     * @param processName
     */
    void deployFlow(String processName);

    ///**
    // * 获得已部署的流程
    // *
    // * @return
    // */
    //PageSet<ProcessDefinitionDto> getDeployedPageSet(PageParam pageParam, String type, String processDefinitionName, String key);

    /**
     * 流程定义分页数据查询
     *
     * @param pageParam 分页参数
     * @param category 分类标识
     */
    PageSet<ProcessDefinitionVo> getDeployedPageSet(PageParam pageParam, String category,String processModelType,String name);


    PageSet<Deployment> getDeploymentPageSet(PageParam pageParam);

    /**
     * 启动流程实例，需要和用户和业务关联
     *
     * @param processDefinitionKey
     * @param businessId
     * @param map
     */
    void startProcess(String processDefinitionKey, String businessId, Map<String, Object> map);

    /**
     * 获得已启动的流程
     *
     * @return
     */
    PageSet<ProcessInstance> getStartedProcess(PageParam pageParam,String processDefinitionName);

    PageSet<ProcessInstanceDto> getMyNoEndProcessPageSetData(PageParam pageParam, String currentUserNameId, String processDefinitionName, String processModelType);

    /**
     * 获得所有任务
     *
     * @return
     */
    PageSet<CommonTaskDto> getTasks(PageParam pageParam, String taskName);

    /**
     * 完成任务
     *
     * @param taskId
     */
    void completeTask(String taskId);

    /**
     * 完成任务
     *
     * @param taskId
     * @param comment
     * @param userNameId
     * @param vars
     */
    void completeTask(String taskId, String comment, String userNameId, Map<String, Object> vars);

    Map<String, Object> getFlowElementByProcessDefinitionId(String processDefinitionId, String flowElementType);

    List getVarsNameByProcessDefinitionId(String processDefinitionId);

    /**
     * 删除流程定义
     *
     * @param deploymentId
     */
    void deleteProcessDefinition(String deploymentId);

    /**
     * 根据分配人和用户名获得流程
     *
     * @param assignee
     * @return
     */
    /*List<LeaveTaskDto> getTasksByAssigneeAndUserNameId(String assignee, String userNameId);*/

    PageSet<TaskCommon> getTasksByAssignee(PageParam pageParam, String assignee, HttpServletRequest request,String processModelType);

    PageSet<CommonTaskDto> getHistoryTasksByStartUserId(PageParam pageParam, String startUserId,String processDefinitionName);

    /**
     * 根据用户名获得流程
     *
     * @param userNameId
     * @return
     */
    PageSet<CommonTaskDto> getTasksByUserNameId(PageParam pageParam, String userNameId);

    /**
     * 获得任务所有入口
     *
     * @param taskId
     * @return
     */
    List<String> getTaskIncomesByTaskId(String taskId);

    /**
     * 获得任务所有出口
     *
     * @param taskId
     * @return
     */
    List<String> getTaskOutcomes(String taskId);

    /**
     * 获得所有活动的出路（包括开始节点）
     * 传入的是历史的活动节点的实例
     *
     * @param hai
     * @return
     */
    List<String> getActivityOutcomes(HistoricActivityInstance hai);

    /**
     * 获得任务详情
     *
     * @param taskId
     * @param userNameId
     * @return
     */
    CommonTaskDto getTaskBeanById(String taskId, String userNameId);

    List<CommentBean> getCommentListByTaskId(String taskId);

    /**
     * 获得评论列表
     *
     * @param taskId
     * @return
     */
    //List<CommentBeanDto> getCommentListByProcessInstanceIds(String processInstanceId);


    List<CommentBean> getCommentListByProcessInstanceId(String processInstanceId);

    List<CommentBean> getCommentBeanByTaskId(String taskId);

    Object coome(String taskId, String [] userNameId, String comment, String name,String instanceId);


    String  getProcDefIdBykey(String key);

    Map<String, Object> startFlowable(String uuid,String userNameId,String key,String reason);

    /*InputStream getDiagramByProcessInstanceId(String processInstanceId);

    InputStream getDiagramByProcessDefinitionId(String processDefinitionId);

    List<CommonTaskDto> getVacationApplyList(List<String> processInstanceIds);*/

    /**
     * @MethodName getDetailsByKey 根据key查询详情
     * @Description
     * @Param id
     * @Return cn.ewsd.workflow.model.TaskCommon
     * <AUTHOR>
     * @Date 2020-08-06 11:11
     */
    TaskCommon getDetailsByKey(String id);

    /**
     * @MethodName updateTaskCommonOpeningTimeByKey  根据key修改开封时间
     * @Description
     * @Param id
     * @Return int
     * <AUTHOR>
     * @Date 2020-08-06 11:11
     */
    int updateTaskCommonOpeningTimeByKey(String id, Date date);


    /**
     * @MethodName updateActHiActinst 根据任务id修改开封时间
     * @Description
     * @Param id
     * @Param date
     * @Return int
     * <AUTHOR>
     * @Date 2020-08-06 14:55
     */
    int updateActHiActinst(String taskId, Date date);

    /**
     * @MethodName getActRuTaskByProcInstId  查询未开封流程步骤
     * @Description
     * @Param procInstId
     * @Return cn.ewsd.workflow.model.ActRuTask
     * <AUTHOR>
     * @Date 2020-08-11 17:29
     */
    Map<String,Object> getActRuTaskByProcInstId(String procInstId);

    /**
     * @MethodName getActHiActinstByProcInstId 查询上一步骤流程数据
     * @Description
     * @Param procInstId
     * @Return java.util.Map<java.lang.String,java.lang.Object>
     * <AUTHOR>
     * @Date 2020-08-11 17:48
     */
    List<Map<String,Object>> getActHiActinstByProcInstId(String procInstId,String actType);


    /**
     * @MethodName delectActRuTaskByKey 根据key删除运行步骤表数据
     * @Description
     * @Param id
     * @Return int
     * <AUTHOR>
     * @Date 2020-08-11 17:54
     */
    int delectActRuTaskByKey(String id);


    /**
     * @MethodName addActRuTask 运行表添加数据
     */
    int addActRuTask(Map<String, Object> map);

    /**
     * @MethodName getAllByProcInstIdByActType  查询审批详情
     * @Description
     * @Param procInstId
     * @Param actType
     * @Return java.util.List<cn.ewsd.workflow.model.ActHiActinst>
     * <AUTHOR>
     * @Date 2020-08-06 15:59
     */
    List<ActHiActinst> getAllByProcInstIdByActType(String procInstId, String actType);

    List<CommentBeanDto> getCommentListByProcessInstanceIds(String processInstanceId);

    List<CommentBeanDto> getCommentListViewStepsByTaskId(String taskId);

    List<CommentBeanDto> getCommentListViewStepsByTaskIdInformation(String taskId);


    /**
     * 修改业务表流程状态
     * @param uuid
     * @param flowState
     * @return
     */
    int updateBusinessFlowStateByUuidAndTableNameStr(String uuid, String flowState,String tableName);
    TaskEntity createSubTask(TaskEntityImpl task, String userNameId);
}
