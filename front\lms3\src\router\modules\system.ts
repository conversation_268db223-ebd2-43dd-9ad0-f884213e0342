// import { $t } from "@/plugins/i18n";

export default {
  path: "/system",
  redirect: "/system/organization",
  meta: {
    icon: "mdi:computer",
    // showLink: false,
    title: "系统管理",
    rank: 9999
  },
  children: [
    {
      path: "/system/organization",
      name: "Organization",
      component: () => import("@/views/system/organization/index.vue"),
      meta: {
        // title: $t("menus.pureFourZeroOne"),
        title: "组织机构管理"
      }
    },
    {
      path: "/system/user",
      name: "User",
      component: () => import("@/views/system/user/index.vue"),
      meta: {
        // title: $t("menus.pureFourZeroOne"),
        title: "用户管理"
      }
    },
    {
      path: "/system/personnel",
      name: "Personnel",
      component: () => import("@/views/system/personnel/index.vue"),
      meta: {
        // title: $t("menus.pureFourZeroOne"),
        title: "人员管理"
      }
    },
    {
      path: "/system/dictionary",
      name: "Dictionary",
      component: () => import("@/views/system/dictionary/index.vue"),
      meta: {
        // title: $t("menus.pureFourZeroOne"),
        title: "数据字典管理"
      }
    },
    {
      path: "/system/log",
      name: "Log",
      component: () => import("@/views/system/log/index.vue"),
      meta: {
        // title: $t("menus.pureFourZeroOne"),
        title: "系统日志"
      }
    }
  ]
} satisfies RouteConfigsTable;
