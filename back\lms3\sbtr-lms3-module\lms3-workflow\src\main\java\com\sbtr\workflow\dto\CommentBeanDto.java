package com.sbtr.workflow.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import org.flowable.engine.task.Comment;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 返回通用的审批意见
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
public class CommentBeanDto {

    /**
     * 节点名称
     */
    private String activityName;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 审批意见
     */
    private Comment comment;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date openingTime;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Comment getComment() {
        return comment;
    }

    public void setComment(Comment comment) {
        this.comment = comment;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public Date getOpeningTime() {
        return openingTime;
    }

    public void setOpeningTime(Date openingTime) {
        this.openingTime = openingTime;
    }
}
