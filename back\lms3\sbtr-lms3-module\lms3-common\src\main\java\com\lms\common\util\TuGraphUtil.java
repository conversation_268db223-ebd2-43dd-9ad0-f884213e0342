package com.lms.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.antgroup.tugraph.TuGraphDbRpcClient;
import com.lms.common.constants.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * TuGraph数据库操作工具类
 */
@Component
public class TuGraphUtil {

    private static final Logger log = LoggerFactory.getLogger(TuGraphUtil.class);

    private final TuGraphDbRpcClient client;

    public TuGraphUtil(TuGraphDbRpcClient client) {
        this.client = client;
    }

    /**
     * 执行Cypher查询，带详细日志和异常处理
     */
    public String executeCypher(String cypher, String graph) throws RuntimeException {
        if (cypher == null || cypher.trim().isEmpty()) {
            throw new IllegalArgumentException("Cypher语句不能为空");
        }
        if (graph == null || graph.trim().isEmpty()) {
            throw new IllegalArgumentException("graph名称不能为空");
        }
        try {
            log.info("[TuGraph] 执行Cypher: {} | graph: {}", cypher, graph);
            String result = client.callCypher(cypher, graph, 10);
            log.info("[TuGraph] 查询结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("[TuGraph] Cypher执行失败: {} | graph: {} | 错误: {}", cypher, graph, e.getMessage(), e);
            throw new RuntimeException("Cypher执行失败: " + e.getMessage(), e);
        }
    }

    // 将参数直接嵌入到Cypher语句中（注意：仅适用于简单参数）
    public static String buildParameterizedCypher(String cypherTemplate, Map<String, Object> params) {
        String result = cypherTemplate;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            Object value = entry.getValue();
            String paramValue;

            if (value instanceof String) {
                paramValue = "'" + value.toString().replace("'", "\\'") + "'";
            } else if (value instanceof Number || value instanceof Boolean) {
                paramValue = value.toString();
            } else {
                // 复杂对象转为JSON
                paramValue = "'" + JSON.toJSONString(value).replace("'", "\\'") + "'";
            }

            result = result.replace("$" + entry.getKey(), paramValue);
        }
        return result;
    }

    /**
     * 查询节点
     *
     * @param graph      图名称
     * @param label      节点标签（可选）
     * @param properties 属性条件（可选）
     * @return 查询结果字符串
     */
    public String queryNodes(String graph, String label, Map<String, Object> properties) throws Exception {
        StringBuilder cypher = new StringBuilder("MATCH (a");
        if (label != null && !label.isEmpty()) {
            cypher.append(":").append(label);
        }
        cypher.append(")");

        if (properties != null && !properties.isEmpty()) {
            cypher.append(" WHERE ");
            List<String> conditions = new ArrayList<>();
            for (Map.Entry<String, Object> entry : properties.entrySet()) {
                if (entry.getValue() instanceof String) {
                    conditions.add(String.format("a.%s = '%s'", entry.getKey(), entry.getValue()));
                } else {
                    conditions.add(String.format("a.%s = %s", entry.getKey(), entry.getValue()));
                }
            }
            cypher.append(String.join(" AND ", conditions));
        }

        cypher.append(" RETURN a");
        return executeCypher(cypher.toString(), graph);
    }

    /**
     * 根据多个属性条件查询模型
     *
     * @param conditions 属性条件列表，每个条件包含name和value
     * @return 匹配的模型列表
     */
    public String queryModelsByAttributes(String graph, List<Map<String, String>> conditions, int skip, int limit) throws Exception {
        String cypher = String.format("MATCH (a:%s)-[e]->(b:%s)", Constants.MODEL, Constants.MODEL_ATTRIBUTE);
        StringBuilder builder = new StringBuilder(cypher);
        for (int i = 0; i < conditions.size(); i++) {
            Map<String, String> condition = conditions.get(i);
            if (i == 0) {
                builder.append(String.format(" WHERE (b.name = '%s' AND b.value = '%s')", escapeString(condition.get("name")), escapeString(condition.get("value"))));
            } else {
                builder.append(String.format(" WITH a " +
                        String.format("MATCH (a)-[e]->(b:%s) ", Constants.MODEL_ATTRIBUTE) +
                        "WHERE b.name = '%s' AND b.value = '%s'", condition.get("name"), condition.get("value")));
            }
        }

        builder.append(String.format(" RETURN a, e, b skip %d limit %d", skip, limit));
        return executeCypher(builder.toString(), graph);
    }

    /**
     * 创建点类型
     *
     * @param labelName    标签名称
     * @param primaryField 主键名称
     * @param fieldSpec    字段信息
     * @param graph        图名称
     * @return 创建结果
     */
    public String createVertexLabel(String labelName, String primaryField, List<Map<String, String>> fieldSpec, String graph) throws Exception {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < fieldSpec.size(); i++) {
            String fieldName = fieldSpec.get(i).get("fieldName");
            String fieldType = fieldSpec.get(i).get("fieldType");
            boolean isPrimary = fieldSpec.get(i).get("isPrimary").equals("true");
            stringBuilder.append("'").append(fieldName).append("', '").append(fieldType).append("', ").append(isPrimary);
            if (i != fieldSpec.size() - 1) {
                stringBuilder.append(", ");
            }
        }
        String cypher = String.format("CALL db.createVertexLabel('%s', '%s', %s)", labelName, primaryField, stringBuilder);
        return executeCypher(cypher, graph);
    }

    /**
     * 创建带指向的边类型
     * param labelName    标签名称
     *
     * @param vertexLabelFrom 源点类型
     * @param vertexLabelTo   目标点类型
     * @param fieldSpec       字段信息
     * @param graph           图名称
     */
    public String createEdgeLabel(String labelName, String vertexLabelFrom, String vertexLabelTo, List<Map<String, String>> fieldSpec, String graph) throws Exception {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < fieldSpec.size(); i++) {
            String fieldName = fieldSpec.get(i).get("fieldName");
            String fieldType = fieldSpec.get(i).get("fieldType");
            boolean isPrimary = fieldSpec.get(i).get("isPrimary").equals("true");
            stringBuilder.append("'").append(fieldName).append("', '").append(fieldType).append("', ").append(isPrimary);
            if (i != fieldSpec.size() - 1) {
                stringBuilder.append(", ");
            }
        }
        String cypher = String.format("CALL db.createEdgeLabel('%s', '[[\"%s\",\"%s\"]]', %s)", labelName, vertexLabelFrom, vertexLabelTo, stringBuilder);
        return executeCypher(cypher, graph);
    }

    /**
     * 删除整个点类型（标签）及其所有节点
     *
     * @param graph 图名称
     * @param label 要删除的点类型
     * @return 操作结果
     */
    public String deleteNodeType(String graph, String label) throws Exception {
        String cypher = String.format("CALL db.deleteLabel('vertex', '%s')", label);
        return executeCypher(cypher, graph);
    }

    /**
     * 删除整个边类型及其所有边
     *
     * @param graph    图名称
     * @param edgeType 要删除的边类型
     * @return 操作结果
     */
    public String deleteEdgeType(String graph, String edgeType) throws Exception {
        String cypher = String.format("CALL db.deleteLabel('edge', '%s')", edgeType);
        return executeCypher(cypher, graph);
    }

    /**
     * 创建节点，返回节点id
     */
    public String createNode(String graph, String label, Map<String, Object> properties) throws RuntimeException {
        if (graph == null || label == null || properties == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        StringBuilder cypher = new StringBuilder(String.format("CREATE (n:%s {", escapeString(label)));
        List<String> props = new ArrayList<>();
        for (Map.Entry<String, Object> entry : properties.entrySet()) {
            if (entry.getValue() instanceof String) {
                props.add(String.format("%s: '%s'", entry.getKey(), escapeString(entry.getValue().toString())));
            } else {
                props.add(String.format("%s: %s", entry.getKey(), entry.getValue()));
            }
        }
        cypher.append(String.join(", ", props)).append("}) RETURN id(n)");
        return executeCypher(cypher.toString(), graph);
    }

    /**
     * 修改单个节点的属性
     */
    public String modifyNode(String graph, String label, String id, Map<String, Object> properties) throws RuntimeException {
        if (graph == null || label == null || id == null || properties == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        StringBuilder cypherTemplate = new StringBuilder();
        cypherTemplate.append("MATCH (n:").append(escapeString(label)).append(" {id: $id}) SET ");
        int i = 0;
        for (Map.Entry<String, Object> entry : properties.entrySet()) {
            if (i++ > 0) cypherTemplate.append(", ");
            cypherTemplate.append("n.").append(entry.getKey()).append(" = $").append(entry.getKey());
        }
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.putAll(properties);
        String finalCypher = TuGraphUtil.buildParameterizedCypher(cypherTemplate.toString(), params);
        return executeCypher(finalCypher, graph);
    }

    /**
     * 根据ID列表批量删除节点
     *
     * @param graph 图名称
     * @param label 节点标签
     * @param ids   要删除的节点ID列表
     * @return 删除的节点数量
     */
    public String batchDeleteNodesByIds(String graph, String label, List<String> ids) {
        try {
            // 构建ID列表字符串
            String idList = ids.stream()
                    .map(s -> "'" + s + "'")
                    .collect(Collectors.joining(", "));

            // 构建Cypher语句
            String cypher = String.format(
                    "MATCH (n:%s) WHERE n.id IN [%s] DELETE n",
                    label, idList
            );

            return executeCypher(cypher, graph);
        } catch (Exception e) {
            log.info("根据ID列表批量删除节点失败");
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 创建两个节点之间的关系
     */
    public String createRelationship(String startLabel, String endLabel, String startProperty, String startValue,
                                     String endProperty, String endValue,
                                     String relationType, String graph) throws RuntimeException {
        String cypher = String.format(
                "MATCH (n:%s), (m:%s) WHERE n.%s = '%s' AND m.%s = '%s' CREATE (n)-[r:%s]->(m) RETURN r",
                escapeString(startLabel), escapeString(endLabel),
                escapeString(startProperty), escapeString(startValue),
                escapeString(endProperty), escapeString(endValue),
                escapeString(relationType)
        );
        return executeCypher(cypher, graph);
    }

    /**
     * 删除两个节点之间的指定类型关系
     */
    public String deleteRelationship(String startLabel, String endLabel, String startProperty, String startValue,
                                     String endProperty, String endValue,
                                     String relationType, String graph) {
        String cypher = String.format(
                "MATCH (n:%s)-[r:%s]->(m:%s) WHERE n.%s = '%s' AND m.%s = '%s' DELETE r RETURN COUNT(r)",
                escapeString(startLabel), escapeString(relationType), escapeString(endLabel),
                escapeString(startProperty), escapeString(startValue),
                escapeString(endProperty), escapeString(endValue)
        );
        return executeCypher(cypher, graph);
    }

    /**
     * 更灵活的关系删除方法，支持部分参数为null
     */
    public String deleteRelationship(String startLabel, String endLabel,
                                             String startProperty, String startValue, String relationType, String graph) {
        if (startLabel == null || endLabel == null || relationType == null) {
            throw new IllegalArgumentException("startLabel, endLabel, relationType不能为空");
        }
        StringBuilder cypher = new StringBuilder();
        cypher.append(String.format("MATCH (n:%s)-[r:%s]->(m:%s)",
                escapeString(startLabel), escapeString(relationType), escapeString(endLabel)));
        List<String> conds = new ArrayList<>();
        if (startProperty != null && startValue != null) {
            conds.add(String.format("n.%s = '%s'", escapeString(startProperty), escapeString(startValue)));
        }
        if (!conds.isEmpty()) {
            cypher.append(" WHERE ").append(String.join(" AND ", conds));
        }
        cypher.append(" DELETE r RETURN COUNT(r)");
        return executeCypher(cypher.toString(), graph);
    }

    /**
     * 根据id查询与a类型节点与b类型节点关系网络
     */
    public String getRelationshipById(String graph, String startLabel, String id, String endLabel) {
        String cypher = String.format("MATCH (a:%s{id:'%s'})-[e]-(b:%s ) RETURN a,b,e", startLabel, id, endLabel);
        return executeCypher(cypher, graph);
    }

    /**
     * 根据id查询关系网络
     */
    public String getAllRelationshipById(String graph, String id) throws Exception {
        String cypher = String.format("MATCH (a {id:'%s'})-[e]-(b) RETURN a,b,e", id);
        return executeCypher(cypher, graph);
    }

    /**
     * 获取具有特定标签的节点数量
     *
     * @param graph 图名称
     * @param label 节点标签
     * @return 节点数量
     */
    public String countNodesByLabel(String graph, String label) throws Exception {
        String cypher = String.format("MATCH (n:%s) RETURN count(n) as count", label);
        return executeCypher(cypher, graph);
    }

    /**
     * 批量创建节点
     *
     * @param graph      图名称
     * @return 节点ID
     */
    public String createNodeBatch(String graph, List<Map<String, Object>> updates) {
        // 方法1：使用JSON和APOC（推荐）
        String cypher = "UNWIND [" +
                "{id: 101, name: '发动机油滤(新版)', desc: '更新后的描述'}," +
                "{id: 102, name: '燃油泵(改进型)', desc: '新版本组件'}" +
                "] AS update " +
                "MATCH (m:模型 {id: update.id}) " +
                "SET m.name = update.name, m.desc = update.desc";
        return executeCypher(cypher, graph);
    }


//以下ai生成未验证-----------------------------------------------------------------------------------------------------------------------

    /**
     * 检查点类型(标签)是否存在
     *
     * @param graph 图名称
     * @param label 要检查的点类型/标签
     * @return 存在返回true，否则返回false
     */
    public boolean nodeTypeExists(String graph, String label) {
        String result = executeCypher(
                String.format("MATCH (n:%s) RETURN n LIMIT 1", label),
                graph);
        return result != null && !result.isEmpty() && !result.contains("[]");
    }

    /**
     * 检查边类型是否存在
     *
     * @param graph    图名称
     * @param edgeType 要检查的边类型
     * @return 存在返回true，否则返回false
     */
    public boolean edgeTypeExists(String graph, String edgeType) {
        String result = executeCypher(
                String.format("MATCH ()-[r:%s]-() RETURN r LIMIT 1", edgeType),
                graph);
        return result != null && !result.isEmpty() && !result.contains("[]");
    }

    /**
     * 获取特定点类型(标签)的所有属性键
     *
     * @param graph 图名称
     * @param label 点类型/标签
     * @return 属性键列表的字符串表示
     */
    public String getNodeTypeProperties(String graph, String label) throws Exception {
        return executeCypher(
                String.format("MATCH (n:%s) RETURN keys(n) LIMIT 1", label),
                graph);
    }

    /**
     * 获取特定边类型的所有属性键
     *
     * @param graph    图名称
     * @param edgeType 边类型
     * @return 属性键列表的字符串表示
     */
    public String getEdgeTypeProperties(String graph, String edgeType) throws Exception {
        return executeCypher(
                String.format("MATCH ()-[r:%s]-() RETURN keys(r) LIMIT 1", edgeType),
                graph);
    }

    /**
     * 删除所有节点和边（清空图）
     *
     * @param graph 图名称
     */
    public String clearGraph(String graph) throws Exception {
        return executeCypher("MATCH (n) DETACH DELETE n", graph);
    }

    /**
     * 删除指定标签的所有节点
     *
     * @param graph 图名称
     * @param label 节点标签
     */
    public String deleteAllNodes(String graph, String label) {
        return executeCypher(String.format("MATCH (n:%s) DETACH DELETE n", label), graph);
    }

    /**
     * 删除所有边
     *
     * @param graph    图名称
     * @param edgeType 边类型（为空则删除所有边）
     */
    public String deleteAllEdges(String graph, String edgeType) {
        if (edgeType == null || edgeType.isEmpty()) {
            return executeCypher("MATCH ()-[r]->() DELETE r", graph);
        } else {
            return executeCypher(String.format("MATCH ()-[r:%s]->() DELETE r", edgeType), graph);
        }
    }


    /**
     * 创建边
     *
     * @param graph      图名称
     * @param sourceId   源节点ID
     * @param targetId   目标节点ID
     * @param edgeType   边类型
     * @param properties 边属性
     */
    public String createEdge(String graph, String sourceId, String targetId, String edgeType,
                             Map<String, Object> properties) {
        StringBuilder cypher = new StringBuilder(
                String.format("MATCH (a), (b) WHERE id(a) = %s AND id(b) = %s ", sourceId, targetId));

        cypher.append(String.format("CREATE (a)-[r:%s {", edgeType));

        if (properties != null && !properties.isEmpty()) {
            List<String> props = new ArrayList<>();
            for (Map.Entry<String, Object> entry : properties.entrySet()) {
                if (entry.getValue() instanceof String) {
                    props.add(String.format("%s: '%s'", entry.getKey(), entry.getValue()));
                } else {
                    props.add(String.format("%s: %s", entry.getKey(), entry.getValue()));
                }
            }
            cypher.append(String.join(", ", props));
        }

        cypher.append("}]->(b)");
        return executeCypher(cypher.toString(), graph);
    }

    /**
     * 查询两点之间的路径
     *
     * @param graph     图名称
     * @param sourceId  源节点ID
     * @param targetId  目标节点ID
     * @param maxDepth  最大深度
     * @param edgeTypes 边类型限制（可选）
     * @return 路径查询结果字符串
     */
    public String findPaths(String graph, String sourceId, String targetId, int maxDepth, String... edgeTypes) {
        StringBuilder cypher = new StringBuilder(
                String.format("MATCH (a), (b) WHERE id(a) = %s AND id(b) = %s ", sourceId, targetId));

        cypher.append("MATCH path = (a)");

        if (edgeTypes != null && edgeTypes.length > 0) {
            cypher.append("-[:").append(String.join("|", edgeTypes)).append("*1..");
        } else {
            cypher.append("-[*1..");
        }

        cypher.append(maxDepth).append("]->(b) RETURN path");
        return executeCypher(cypher.toString(), graph);
    }

    /**
     * 获取图中所有的节点标签
     *
     * @param graph 图名称
     * @return 标签列表的字符串表示
     */
    public String getAllNodeLabels(String graph) {
        return executeCypher("CALL db.vertexLabels()", graph);
    }

    /**
     * 获取图中所有的边类型
     *
     * @param graph 图名称
     * @return 边类型列表的字符串表示
     */
    public String getAllEdgeTypes(String graph) {
        return executeCypher("CALL db.edgeLabels()", graph);
    }

    /**
     * 为现有节点添加新标签
     *
     * @param graph    图名称
     * @param nodeId   节点ID
     * @param newLabel 要添加的新标签
     * @return 操作结果
     */
    public String addLabelToNode(String graph, String nodeId, String newLabel) {
        String cypher = String.format("MATCH (n) WHERE id(n) = %s SET n:%s RETURN n", nodeId, newLabel);
        return executeCypher(cypher, graph);
    }

    /**
     * 从节点中移除标签
     *
     * @param graph  图名称
     * @param nodeId 节点ID
     * @param label  要移除的标签
     * @return 操作结果
     */
    public String removeLabelFromNode(String graph, String nodeId, String label) {
        String cypher = String.format("MATCH (n:%s) WHERE id(n) = %s REMOVE n:%s RETURN n", label, nodeId, label);
        return executeCypher(cypher, graph);
    }

    /**
     * 重命名标签（所有该标签的节点将被更新）
     *
     * @param graph    图名称
     * @param oldLabel 旧标签名称
     * @param newLabel 新标签名称
     * @return 操作结果
     */
    public String renameLabel(String graph, String oldLabel, String newLabel) {
        String cypher = String.format(
                "MATCH (n:%s) REMOVE n:%s SET n:%s RETURN count(n) as affectedNodes",
                oldLabel, oldLabel, newLabel);
        return executeCypher(cypher, graph);
    }

    /**
     * 检查标签是否存在
     *
     * @param graph 图名称
     * @param label 要检查的标签
     * @return 存在返回true，否则返回false
     */
    public boolean labelExists(String graph, String label) {
        String result = executeCypher(
                String.format("MATCH (n:%s) RETURN n LIMIT 1", label),
                graph);
        return result != null && !result.isEmpty() && !result.contains("[]");
    }

    /**
     * 为标签创建索引（提高查询性能）
     *
     * @param graph    图名称
     * @param label    节点标签
     * @param property 要创建索引的属性
     * @return 操作结果
     */
    public String createIndexForLabel(String graph, String label, String property) {
        String cypher = String.format("CALL db.createVertexIndex('%s', '%s', '%s')", label, property, property);
        return executeCypher(cypher, graph);
    }

    /**
     * 删除标签的索引
     *
     * @param graph    图名称
     * @param label    节点标签
     * @param property 索引属性
     * @return 操作结果
     */
    public String dropIndexForLabel(String graph, String label, String property) {
        String cypher = String.format("CALL db.dropVertexIndex('%s', '%s')", label, property);
        return executeCypher(cypher, graph);
    }

    /**
     * 辅助方法：解析id
     */
    private String parseIdFromResult(String result) {
        // 假设返回格式为 [123] 或 [{"id":"xxx"}] 或其它，按实际情况解析
        if (result == null || result.isEmpty()) return null;
        try {
            if (result.startsWith("[")) {
                List<Object> list = JSON.parseObject(result, List.class);
                if (!list.isEmpty()) {
                    Object first = list.get(0);
                    if (first instanceof Map) {
                        Map map = (Map) first;
                        Object id = map.get("id(n)");
                        if (id != null) return id.toString();
                    } else {
                        return first.toString();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("[TuGraph] 解析id失败: {}", result, e);
        }
        return null;
    }

    /**
     * 转义字符串，防止Cypher注入和特殊字符问题
     */
    public static String escapeString(String input) {
        if (input == null) return "";
        return input.replace("'", "\\'");
    }

    /**
     * 递归查所有相关节点ID，再查所有相关关系，返回完整关系结果（r, n, m）
     */
    public String getAllRelatedNodeIdsAndRelationships(String graph, String id, int maxDepth) {
        if (maxDepth <= 0) {
            maxDepth = 20;
        }
        // 递归查所有相关节点ID
        String cypher1 = String.format(
                "MATCH path = (a {id:'%s'})-[*1..%d]-(b) RETURN DISTINCT a.id AS id UNION MATCH path = (a {id:'%s'})-[*1..%d]-(b) RETURN DISTINCT b.id AS id",
                id, maxDepth, id, maxDepth
        );
        String nodeIdResult = executeCypher(cypher1, graph);
        // 解析所有ID
        List<String> allIds = new ArrayList<>();
        JSONArray arr = JSONArray.parseArray(nodeIdResult);
        for (int i = 0; i < arr.size(); i++) {
            String nid = arr.getJSONObject(i).getString("id");
            if (nid != null) allIds.add(nid);
        }
        if (allIds.isEmpty()) return "[]";
        // 拼接合法的IN列表
        String idList = allIds.stream().map(s -> "'" + s.replace("'", "\\'") + "'").collect(java.util.stream.Collectors.joining(","));
        // 第二步：查所有相关关系
        String cypher2 = String.format(
                "MATCH (n)-[r]-(m) WHERE n.id IN [%s] AND m.id IN [%s] RETURN r, n, m",
                idList, idList
        );
        return executeCypher(cypher2, graph);
    }
}