/**
 * FileName:	ClientExaminestudentrecord.java
 * Author:		ji<PERSON><PERSON><PERSON>
 * Time:		2013-3-4上午11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.examine.examinestudentrecord.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.common.feign.dto.Selectitem;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.NumberHelper;
import com.lms.common.util.StringHelper;
import com.lms.examine.coursewareexamine.service.CoursewareexamineService;
import com.lms.examine.examinestudentrecord.service.ExaminestudentrecordService;
import com.lms.examine.examinesubjecttype.service.ExaminesubjecttypeService;
import com.lms.examine.feign.model.Coursewareexamine;
import com.lms.examine.feign.model.Examinepage;
import com.lms.examine.examinepage.service.impl.ExaminePageServiceImpl;
import com.lms.examine.examinestudentrecord.mapper.ExaminestudentrecordMapper;
import com.lms.examine.feign.model.Examinestudentrecord;
import com.lms.examine.feign.model.ExaminestudentrecordViewModel;
import com.lms.examine.feign.model.Examinesubjecttype;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * 保存项目的相关信息
 */
@Service("ExaminestudentrecordService")
public class ExaminestudentrecordServiceImpl extends BaseServiceImpl<ExaminestudentrecordMapper, Examinestudentrecord> implements ExaminestudentrecordService {

    @Resource
    private ExaminestudentrecordMapper examinestudentrecordMapper;
    @Resource
    private CoursewareexamineService coursewareexamineService;
    @Resource
    private ExaminesubjecttypeService examinesubjecttypeService;
    @Resource
    @Lazy
    private ExaminePageServiceImpl examinePageService;


    public Double createRecordScore(Examinestudentrecord examinestudentrecord) {
        String answer = StringHelper.null2String(examinestudentrecord.getAnswer());
        String correctresponse = StringHelper.null2String(examinestudentrecord.getCorrectresponse());
        String type = StringHelper.null2String(examinestudentrecord.getType());
        String examineid = StringHelper.null2String(examinestudentrecord.getExamineid());
        Double score = 0.00;
        if (type.equals("3ef580ec56ae436eb79b91b25d1a078e")) {//多选
            String[] aArray = answer.split(",");
            String[] cArray = correctresponse.split(",");
            Arrays.sort(aArray);
            Arrays.sort(cArray);
            if (Arrays.equals(aArray, cArray)) {
                score = findPerpointByTypeAndId(examineid, type);
            }
        } else if (type.equals("9e47efc0ce894454856a80171e1e6efe") || type.equals("9e47efc0ce894454856a80171e1e6efe") || type.equals("23e162b9f27b4ebc9a5c93db09913693")) {//单选、填空判断
            if (answer.equals(correctresponse)) {
                score = findPerpointByTypeAndId(examineid, type);
            }
        }
		/* else if(type.equals("23e162b9f27b4ebc9a5c93db09913693")){//判断
			if((answer.equals("A")&&correctresponse.equals("0"))||(answer.equals("B")&&correctresponse.equals("1"))){
				score = examinestudentrecordDao.findPerpointByTypeAndId(examineid, type);
			}
		}*/
        return score;
    }


    public double createRecordScore2(Examinestudentrecord examinestudentrecord) {
        String answer = StringHelper.null2String(examinestudentrecord.getAnswer());
        String correctresponse = StringHelper.null2String(examinestudentrecord.getCorrectresponse());
        String type = StringHelper.null2String(examinestudentrecord.getType());
        double score = 0.00;
        if (type.equals("3ef580ec56ae436eb79b91b25d1a078e")) {//多选
            String[] aArray = answer.split(",");
            String[] cArray = correctresponse.split(",");
            Arrays.sort(aArray);
            Arrays.sort(cArray);
            if (Arrays.equals(aArray, cArray)) {
                score += 1;
            }
        } else if (type.equals("9e47efc0ce894454856a80171e1e6efe") || type.equals("9e47efc0ce894454856a80171e1e6efe") || type.equals("23e162b9f27b4ebc9a5c93db09913693")) {//单选、填空判断
            if (answer.equals(correctresponse)) {
                score += 1;
            }
        }
        return score;
    }

    public List<ExaminestudentrecordViewModel> addTitleSubject(List<Examinestudentrecord> returnRecords) {
        List<ExaminestudentrecordViewModel> sModelList = new ArrayList<ExaminestudentrecordViewModel>();
        if (returnRecords.size() > 0) {
            Examinestudentrecord esr = returnRecords.get(0);
            Examinepage ep = examinePageService.getEagerData(esr.getExamineid());
            if (ep != null && !ep.getId().isEmpty()) {
                List<Examinesubjecttype> est = ep.getExaminesubjecttypes();
                List<Selectitem> slist = commonBaseApi.getSelectitemsByType("fc657149c8d84e79a5f3043c40d7baaa").getResult();
                String extids = "";
                List<Selectitem> returnlist = new ArrayList<Selectitem>();
                for (Examinesubjecttype esubt : est) {
                    String subjecttype = StringHelper.null2String(esubt.getSubjecttype());
                    Double perpoint = NumberHelper.string2Double(esubt.getPerpoint(), 0);
                    Double qty = NumberHelper.string2Double(esubt.getQty(), 0);
                    if (perpoint > 0 && qty > 0) {
                        extids = extids + "," + subjecttype;
                    }
                }
                for (int i = 0; i < slist.size(); i++) {
                    Selectitem s = slist.get(i);
                    String sid = s.getId();
                    if (extids.indexOf(sid) >= 0) {
                        returnlist.add(s);
                    }
                }
                for (Examinestudentrecord sub : returnRecords) {
                    Examinepage examinepage = examinePageService.getById(sub.getExamineid());
                    ExaminestudentrecordViewModel s = new ExaminestudentrecordViewModel(sub, examinepage, returnlist);
                    sModelList.add(s);
                }
            } else {
                Coursewareexamine ce = coursewareexamineService.getById(esr.getExamineid());
                if (ce != null && !ce.getId().isEmpty()) {
                    for (Examinestudentrecord sub : returnRecords) {
                        ExaminestudentrecordViewModel s = new ExaminestudentrecordViewModel(sub);
                        sModelList.add(s);
                    }
                }
            }

        }
        return sModelList;
    }

    public List<Examinestudentrecord> getExaminestudentrecordsByExamineId(String examined, String personid) {
        LambdaQueryWrapper<Examinestudentrecord> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(examined)){
            wrapper.eq(Examinestudentrecord::getExamineid, examined);
        }
        if (StringUtils.isNotEmpty(personid)){
            wrapper.eq(Examinestudentrecord::getPersonid, personid);
        }
        return this.list(wrapper);
    }

    public Double findPerpointByTypeAndId(String examineid, String type) {
        LambdaQueryWrapper<Examinesubjecttype> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Examinesubjecttype::getExamineid, examineid);
        wrapper.eq(Examinesubjecttype::getSubjecttype, type);
        Examinesubjecttype one = examinesubjecttypeService.getOne(wrapper);
        return one.getPerpoint();
    }

}
