<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sbtr-lms3-module</artifactId>
        <groupId>com.cepreitrframework.boot</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>lms3-base</artifactId>
    <packaging>pom</packaging>
    <name>lms3-base</name>
    <description>系统业务元数据管理模块，包含人员、部门、课件(教材)、课程、题目、数据字典等基础信息的管理</description>

    <modules>
        <module>lms3-base-api</module>
        <module>lms3-base-start</module>
        <module>lms3-base-biz</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.cepreitrframework.boot</groupId>
            <artifactId>lms3-common</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>
