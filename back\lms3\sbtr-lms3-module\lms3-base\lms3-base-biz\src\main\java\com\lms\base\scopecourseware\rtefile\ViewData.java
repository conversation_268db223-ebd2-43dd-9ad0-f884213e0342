package com.lms.base.scopecourseware.rtefile;

import lms.rte.Common.LaunchData;

public class ViewData {
	private String dubug;
	private String ServerHost;
	private String OrginalCourseId;
	private String ScormStoragePath;
	private LaunchData launchData;
	private String ContentUrl;
	private String scriptContent;

	public ViewData() {
	}

	public String getDubug() {
		return dubug;
	}

	public void setDubug(String dubug) {
		this.dubug = dubug;
	}

	public String getServerHost() {
		return ServerHost;
	}

	public void setServerHost(String serverHost) {
		ServerHost = serverHost;
	}

	public String getOrginalCourseId() {
		return OrginalCourseId;
	}

	public void setOrginalCourseId(String orginalCourseId) {
		OrginalCourseId = orginalCourseId;
	}

	public String getScormStoragePath() {
		return ScormStoragePath;
	}

	public void setScormStoragePath(String scormStoragePath) {
		ScormStoragePath = scormStoragePath;
	}

	public LaunchData getLaunchData() {
		return launchData;
	}

	public void setLaunchData(LaunchData launchData) {
		this.launchData = launchData;
	}

	public String getContentUrl() {
		return ContentUrl;
	}

	public void setContentUrl(String contentUrl) {
		ContentUrl = contentUrl;
	}

	public String getScriptContent() {
		return scriptContent;
	}

	public void setScriptContent(String scriptContent) {
		// TODO Auto-generated method stub
		this.scriptContent = scriptContent;
	}
}
