package com.lms.common.filter;

import cn.hutool.core.util.StrUtil;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @MethodName
 * @Description   针对document权限拦截
 * 注意
 * 1.请求地址后携带.api默认放开拦截(例如: @PostMapping("/getDisableDaysInfo.api" ))
 * 2.如果放开js/css/或请求地址不携带.api等文件则在 utils ---> DefaultSkipUrl 中增加 DEFAULT_SKIP_URL.add("/v2/api-docs");
 * 3.放开接口地址时需要在gateway(/system/v2/api-docs)项目以及接口所在项目(/v2/api-docs)同时放开
 * <AUTHOR>
 * @Date 2020-11-6 10:28
 */
@Component
public class JwtFilter extends OncePerRequestFilter {
    // jwt相关配置
    @Resource
    private ContextUtil contextUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        contextUtil.loadJwtUser(request);
        filterChain.doFilter(request, response);
    }

}
