package com.lms.common.util;

import cn.hutool.core.convert.Convert;
import com.lms.common.model.JwtUser;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Component
public class TokenUtil {


    /***
     * 获取token
     * @return
     */
    public String getToken() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            HttpServletRequest request = requestAttributes.getRequest();
            String token = request.getHeader(ConstParamUtil.X_ACCESS_TOKEN);
            return token;
        }
        return null;
    }
}
