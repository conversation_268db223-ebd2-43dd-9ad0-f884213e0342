<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.form.mapper.FormDatabaseMapper">

    <!-- refid id 引用   使用get_dic_item_text 函数可以显示字典text -->
    <sql id="Base_Column_List">
        uuid,
        create_time,
        creator,
        creator_id,
        creator_org_id,
        modifier_id,
        modifier,
        modify_time,
        basic_configure_uuid,
        type,
        database_json
    </sql>
    <insert id="formSave">
        ${sql}
    </insert>

    <update id="formUpdate">
        ${sql}
    </update>

    <!-- 获取分页集 -->
    <select id="getPageSet" resultType="com.sbtr.form.model.FormDatabase">
        select
        <include refid="Base_Column_List"></include>
        from form_database
        <where>

            <if test="filterSort != null">
                ${filterSort}
            </if>
        </where>
    </select>

    <select id="selectByBasicConfigureUuid" resultType="com.sbtr.form.model.FormDatabase">
        select
        <include refid="Base_Column_List"></include>
        from form_database where basic_configure_uuid=#{basicConfigureUuid}
    </select>

    <!-- 批量删除 -->
    <delete id="executeDeleteBatch">
        delete from form_database where uuid in
        <foreach item="uuid" collection="array" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </delete>

    <delete id="deleteByBasicConfigureUuid">
        delete
        from form_database
        where basic_configure_uuid = #{basicConfigureUuid}
    </delete>
    <delete id="formDelete">
        delete from ${tableName} where uuid in
        <foreach item="list" collection="list" open="(" separator="," close=")">
            #{list}
        </foreach>
    </delete>

    <delete id="formDeleteSub">
        delete
        from ${tableName}
        where ${associatedFields} = #{uuid}
    </delete>

    <delete id="formDeleteMain">
        delete
        from ${tableName}
        where ${associatedFields} in <foreach item="list" collection="list" open="(" separator="," close=")">
        #{list}
    </foreach>
    </delete>

    <select id="formGetPageSet" resultType="java.util.Map">
        select
        ${filed}uuid
        from ${tableName}
        <where>
            <if test="filterSort != null">
                ${filterSort}
            </if>
        </where>
    </select>


    <select id="formGetDetail" resultType="java.util.Map">
        SELECT *
        FROM ${tableName}
        WHERE uuid = #{uuid}
    </select>

    <select id="formGetDetail11" resultType="java.util.Map">
        SELECT *
        FROM ${tableName}
        WHERE ${associatedFields} = #{uuid}
    </select>

</mapper>
