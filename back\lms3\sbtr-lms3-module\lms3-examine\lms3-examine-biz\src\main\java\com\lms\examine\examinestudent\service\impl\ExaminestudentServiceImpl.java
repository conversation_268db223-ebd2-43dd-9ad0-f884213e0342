/**
 * FileName:	ClientExaminestudent.java
 * Author:		ji<PERSON><PERSON><PERSON>
 * Time:		2013-3-4上午11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.examine.examinestudent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.base.feign.api.LmsBaseApi;
import com.lms.base.feign.model.Subject;
import com.lms.common.feign.dto.Selectitem;
import com.lms.common.model.Result;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.StringHelper;
import com.lms.examine.examinestudent.service.ExaminestudentService;
import com.lms.examine.examinesubjecttype.service.ExaminesubjecttypeService;
import com.lms.system.feign.api.AttachApi;
import com.lms.examine.feign.model.Examinecourse;
import com.lms.examine.examinestudent.mapper.VUExaminestudentMapper;
import com.lms.examine.feign.model.VUExaminestudent;
import com.lms.examine.feign.model.Examinestudentrecord;
import com.lms.examine.examinestudentrecord.service.impl.ExaminestudentrecordServiceImpl;
import com.lms.examine.feign.model.Examinesubject;
import com.lms.examine.examinesubject.service.impl.ExaminesubjectServiceImpl;
import com.lms.examine.feign.model.Examinepage;
import com.lms.examine.feign.model.Examinesubjecttype;
import com.lms.examine.examinepage.service.impl.ExaminePageServiceImpl;
import com.lms.examine.examinestudent.mapper.ExaminestudentMapper;
import com.lms.examine.feign.model.Examinestudent;
import com.lms.system.feign.model.Attach;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 保存项目的相关信息
 */
@Service("ExaminestudentService")
public class ExaminestudentServiceImpl extends BaseServiceImpl<ExaminestudentMapper, Examinestudent> implements ExaminestudentService {

    @Resource
    private ExaminestudentMapper examinestudentMapper;
    @Resource
    private ExaminesubjecttypeService examinesubjecttypeService;
    @Resource
    private ExaminestudentrecordServiceImpl examinestudentrecordService;
    @Resource
    private ExaminesubjectServiceImpl examinesubjectService;
    @Resource
    @Lazy
    private ExaminePageServiceImpl examinePageService;

    @Resource
    private LmsBaseApi lmsBaseApi;
    @Resource
    private AttachApi attachApi;
    @Resource
    private VUExaminestudentMapper iVUExaminestudentMapper;

    public List<Examinestudent> getExaminestudentsByExamId(String id) {
        return this.examinestudentMapper.getExaminestudentsByExamId(id);
    }


    public Result batchReleaseExaminepage(List<String> idList) {
        String errorMsg = "";
        String errorIds = "";
        // 校验试卷是否已添加考试学员
        for (int i = 0; i < idList.size(); i++) {
            String examineid = idList.get(i);
            Examinepage ep = examinePageService.getById(examineid);
            String name = ep.getName();
            if (ep.getExaminetype().equals("1")) {
                List<Examinestudent> estudents = getExaminestudentsByExamId(examineid);
                if (estudents.size() == 0) {
                    if (errorIds.isEmpty()) {
                        errorMsg = name;
                        errorIds = examineid;
                    } else {
                        errorMsg = errorMsg + "," + name;
                        errorIds = errorIds + "," + examineid;
                    }
                }
            }
        }
        if (!errorIds.isEmpty()) {
            errorMsg = commonSystemApi.translateContent("试卷([?])未添加考试学员，无法下发试卷！", "", errorMsg);
            return Result.error(errorMsg);
        }
        String errorIdsRam = "";
        String errorMsgRam = "";
        for (int i = 0; i < idList.size(); i++) {
            String examineid = idList.get(i);
            Examinepage ep = examinePageService.getEagerData(examineid);
            String name = ep.getName();
            String modal = ep.getModal();
            String etype = ep.getType();
            boolean noSubject = modal
                    .equals("12058eacd08a454fb12ebbf19a5df651");
            List<Examinesubjecttype> estSet = ep.getExaminesubjecttypes();
            if (modal.equals("bfd41a28350847019428224e7785eb82")) {// 固定试题
                // 检验固定试题试卷设置的题量是否和实际添加的题量一致
                for (Examinesubjecttype est : estSet) {
                    String subjecttype = est.getSubjecttype();
                    int qty = est.getQty();
                    List<Examinesubject> ess = examinesubjectService.getExaminesubjectsByExamType(examineid,
                            subjecttype);
                    if (ess.size() != qty) {
                        if (errorIds.isEmpty()) {
                            errorMsg = name;
                            errorIds = examineid;
                        } else if (errorIds.indexOf(examineid) < 0) {
                            errorMsg = errorMsg + "," + name;
                            errorIds = errorIds + "," + examineid;
                        }
                    }
                }
            } else if (noSubject) {
                Map<String, List> subjectMap = new HashMap();
                String checkMsg = chekcSubjectAmountByExamine(ep, 1, subjectMap);
                if (checkMsg.indexOf("createSuccess") < 0) {
                    if (errorIdsRam.isEmpty()) {
                        errorMsgRam = name;
                        errorIdsRam = examineid;
                    } else if (errorIds.indexOf(examineid) < 0) {
                        errorMsgRam = errorMsgRam + "," + name;
                        errorIdsRam = errorIdsRam + "," + examineid;
                    }
                }
            }
        }

        if (!errorIds.isEmpty()) {
            errorMsg = commonSystemApi.translateContent("固定试题试卷([?])题目未设置完全，无法下发！", "", errorMsg);
            return Result.error(errorMsg);
        } else if (!errorIdsRam.isEmpty()) {
            errorMsgRam = commonSystemApi.translateContent("随机试题试卷([?])题目库数量不足，无法下发！", "", errorMsgRam);
            return Result.error(errorMsgRam);
        }
        examinestudentMapper.batchReleaseExaminepage(idList);
        examinePageService.batchReleaseExaminepage(idList);
        return Result.OK(idList);
    }


    public Examinestudent getExaminestudentByRecord(Examinestudentrecord examinestudentrecord) {
        return getById(examinestudentrecord.getStudentid());
    }


    public void createStudentRecordByExamine(Examinestudent examinestudent,
                                             String examineid, Map<String, List> subjectMap) {
        for (Map.Entry<String, List> entry : subjectMap.entrySet()) {
            List<String> subjectids = entry.getValue();
            if (subjectids.size() > 0) {
                for (int j = 0; j < subjectids.size(); j++) {
                    Examinestudentrecord record = new Examinestudentrecord();
                    record.setExamineid(examineid);
                    String sid = subjectids.get(j);
                    Subject sub = lmsBaseApi.getSubjectById(sid).getResult();
                    if (sub == null) {//若试题不存在，从试卷试题中获取题目内容
                        List<Examinesubject> examineSubjects = examinesubjectService.getExaminesubjectsBySubjectId(examineid, sid);
                        if (examineSubjects.size() > 0) {
                            Examinesubject es = examineSubjects.get(0);
                            record.setExaminesubjectid(sid);
                            record.setContent(es.getContent());
                            record.setCorrectresponse(es.getCorrectresponse());
                            record.setExaminetype(1);
                            record.setPersonid(examinestudent.getPersonid());
                            record.setSeqno(j + 1);
                            record.setTitle(es.getTitle());
                            record.setType(es.getType());
                            record.setStudentid(examinestudent.getId());
                        } else {
                            continue;
                        }
                    } else {
                        record.setExaminesubjectid(sid);
                        record.setContent(sub.getContent());
                        record.setCorrectresponse(sub.getCorrectresponse());
                        record.setExaminetype(1);
                        record.setPersonid(examinestudent.getPersonid());
                        record.setSeqno(j + 1);
                        record.setTitle(sub.getTitle());
                        record.setType(sub.getType());
                        record.setStudentid(examinestudent.getId());
                    }
                    examinestudentrecordService.saveOrUpdate(record);
                }
            }
        }
    }

    @SuppressWarnings("finally")

    public String chekcSubjectAmount(Examinepage examinepage, int onlyexam) {
        String resMsg = "";
        String errortype = "";
        try {
            List<Examinesubjecttype> examinesubjecttypes = new ArrayList<Examinesubjecttype>(examinepage.getExaminesubjecttypes());
            for (int i = 0; i < examinesubjecttypes.size(); i++) {
                Examinesubjecttype esubtype = examinesubjecttypes.get(i);
                String subjecttype = esubtype.getSubjecttype();
                int qty = esubtype.getQty();
                Double perpoint = esubtype.getPerpoint();
                if (qty != 0 && perpoint != 0) {
                    HashMap map = new HashMap();
                    map.put("subjecttype", subjecttype);
                    map.put("onlyexam", onlyexam);//仅判断用于考试的试题是否满足数量要求
                    map.put("equipmentid", examinepage.getEquiptypeid());
                    map.put("specialid", examinepage.getSpecialid());
                    map.put("qty", qty);
                    List<String> subjectids = lmsBaseApi.getRandomIds(map);
                    if (qty > subjectids.size()) {
                        Selectitem si = commonBaseApi.getSelectitemById(subjecttype).getResult();
                        errortype = errortype.isEmpty() ? si.getObjname() : errortype
                                + "," + si.getObjname();
                    }
                }
            }
            if (!errortype.isEmpty()) {
                resMsg = commonSystemApi.translateContent("试题库([?])题目数量不足，无法生成试卷进行考试!", "", errortype);
            }
        } catch (Exception e) {
            e.printStackTrace();
            resMsg = e.getMessage();
        } finally {
            return resMsg;
        }
    }


    public String chekcSubjectAmountByExamine(Examinepage ep, int onlyexam, Map<String, List> subjectMap) {
        String resMsg = "";
        try {
            LambdaQueryWrapper<Examinesubjecttype> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Examinesubjecttype::getExamineid, ep.getId());
            List<Examinesubjecttype> examinesubjecttypes = examinesubjecttypeService.list(wrapper);
            List<Examinecourse> exminepoints = ep.getExaminecourses();
            Iterator it = exminepoints.iterator();
            List<String> courseids = new ArrayList<>();
            while (it.hasNext()) {
                Examinecourse examinecourse = (Examinecourse) it.next();
                courseids.add(examinecourse.getCourseid());
            }
            String errortype = "";
            for (int i = 0; i < examinesubjecttypes.size(); i++) {
                Examinesubjecttype esubtype = examinesubjecttypes.get(i);
                String subjecttype = esubtype.getSubjecttype();
                int qty = esubtype.getQty();
                Double perpoint = esubtype.getPerpoint();
                if (qty != 0 && perpoint != 0) {
                    HashMap map = new HashMap();
                    map.put("subjecttype", subjecttype);
                    map.put("onlyexam", onlyexam);
                    map.put("courseids", courseids);
                    map.put("qty", qty);
                    List<String> subjectids = lmsBaseApi.getRandomIds(map);
                    subjectMap.put(subjecttype, subjectids);
                    // List<String> subjectids =
                    // chekcSubjectAmountByExamine(esubtype,examineid,onlyexam);
                    if (subjectids.size() > 0 && qty == subjectids.size()) {
                        resMsg = resMsg.isEmpty() ? "createSuccess" : resMsg;
                    } else {
                        Selectitem si = commonBaseApi.getSelectitemById(subjecttype).getResult();
                        errortype = errortype.isEmpty() ? si.getObjname() : errortype
                                + "," + si.getObjname();
                    }
                }
            }
            if (!errortype.isEmpty()) {
                resMsg = commonSystemApi.translateContent("试题库([?])题目数量不足，无法生成试卷进行考试!", "", errortype);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resMsg;
    }


    public List<Map<String, Object>> getScoreRank(String examineid, String personid) {
        //支持oracle sql server 和MySQL 8.0以上版本
        String hql = "select id, score_rank, personid from (select id, rank() over(order by nvl(score,0) desc) score_rank, personid from  " +
                "u_examinestudent where examineid= " + examineid + ") where personid= " + personid;
        //MySQL 8 以下版本的解决方法
		/*String hql = "select rank_table.id, rank_table.score_rank, rank_table.personid FROM" +
				" (" +
				"SELECT" +
				" s.id," +
				" @rank_account \\:=(@rank_account + 1) AS serial_rank," +
				" s.personid," +
				"IF" +
				" ( @pre_score = IFNULL( s.score, 0 ), @cur_rank, @cur_rank \\:=@rank_account ) score_rank, @pre_score \\:=IFNULL( s.score, 0 ) AS score " +
				"FROM" +
				" u_examinestudent s," +
				"( SELECT @cur_rank \\:=1, @pre_score \\:=NULL, @rank_account \\:=0 ) r " +
				"WHERE" +
				" examineid = :examineid " +
				"ORDER BY" +
				" IFNULL( s.score, 0 ) DESC" +
				") AS rank_table where rank_table.personid= :personid";*/
        return listSQLQuery(hql);
    }


    public List<Examinestudent> getStudentByExamine(String personid,
                                                    String examineid) {
        return examinestudentMapper.getStudentByExamine(personid, examineid);
    }


    public List<Examinestudent> getLearnStatusById(String examId, String personId) {
        personId = StringHelper.isEmpty(personId) ? ContextUtil.getPersonId() : personId;
        return examinestudentMapper.getLearnStatusById(examId, personId);
    }


    public Map<String, Object> getStudentExamineStatistics(String personId) {
        //examinestudentDao.listSQLQuery(" set sql_mode='STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' ");
        List<Map<String, Object>> list = listSQLQuery(" select \n" +
                "a.name  examinName ,IFNULL(a.score,0) score from V_U_EXAMINESTUDENT a where a.STUDENTSTATUS=1 and a.EXAMINETYPE=1 and a.PERSONID='" + personId + "'  ");
        List<Map<String, Object>> lists = listSQLQuery(" select COUNT(personname) examinNum,sum(IFNULL(a.score,0))  sumScore, avg(IFNULL(a.score,0)) as avgScore\n" +
                "  from V_U_EXAMINESTUDENT a where a.STUDENTSTATUS=1 and a.EXAMINETYPE=1 and a.PERSONID='" + personId + "'  ");
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        map.put("sum", lists);
        return map;
    }

    private List<Map<String, Object>> listSQLQuery(String sql) {
        return examinestudentMapper.listSQLQuery(sql);
    }

    public List<Examinestudent> getValidExaminestudents() {
        return examinestudentMapper.getValidExaminestudents();
    }

    public List<VUExaminestudent> setAttachByEsts(List<VUExaminestudent> vuexamiestudentList) {
        List<String> attachids = vuexamiestudentList.stream().map(VUExaminestudent::getAttachid).collect(Collectors.toList());
        Result<List<Attach>> attachResult = attachApi.getByIdList(attachids);
        if (attachResult != null && attachResult.getResult() != null) {
            List<Attach> attachList = attachResult.getResult();
            vuexamiestudentList.forEach(change -> {
                Optional<Attach> optional = attachList.stream().filter(attach -> attach.getId().equals(change.getAttachid())).findFirst();
                optional.ifPresent(change::setAttachinfo);
            });
        }
        return vuexamiestudentList;
    }
}
