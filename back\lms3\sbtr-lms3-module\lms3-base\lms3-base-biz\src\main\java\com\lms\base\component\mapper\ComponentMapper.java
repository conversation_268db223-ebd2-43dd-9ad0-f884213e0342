package com.lms.base.component.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.base.feign.model.Component;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface ComponentMapper extends BaseMapper<Component> {

    @Select(value = "select * from b_component where if(#{parentID} = '' or #{parentID} is null, " +
            "( parentid is null or parentid='' ), parentid=#{parentID}) and status=1 order by seqno")
    List<Component> getAllComponentsByPid(String parentID);

    @Select(value = "select * from b_component where if(#{parentid} = '' or #{parentid} is null, (parentid is null or parentid='') , parentid=#{parentid}) and " +
            "if(#{id} = '' or #{id} = null, 1 = 1 , id != #{id})  and name = #{name} ")
    List<Component> existName(@Param("name") String name, @Param("id") String id, @Param("parentid") String parentid);

    @Select(value = "select * from b_component where if(#{parentid} = '' or #{parentid} is null, (parentid is null or parentid='') , parentid=#{parentid}) and " +
            "if(#{id} = '' or #{id} = null, 1 = 1 , id != #{id})  and code = #{code} and status = 1 ")
    List<Component> existCode(String code, String id, String parentid);

    @Select(value = "select * from b_component where  status=1 and name = #{objname}")
    List<Component> getComponetByName(String objname);

    @Select(value = "select id from b_component where parentid= #{parentId} and status=1")
    List<String> hasChildren(String parentId);

    @Select(value = "select * from b_component where name = #{name} ")
    List<Component> getByName(String name);

    @Select(value = "select id,parentid as pid,name,courseFlag,'' as courseType,status from b_component where status=1 and find_in_set(id,getComponentChildList(#{parentId})) order by seqno")
    List<Map> getCteComponentsByPid(@Param("parentId") String parentId);

    @Select("${sql}")
    List getAllComponentsByPid2(@Param("sql") String sql);
}
