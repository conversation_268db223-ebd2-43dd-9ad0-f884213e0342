<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import {
  ReloadOutlined,
  SearchOutlined,
  SyncOutlined,
  DeleteOutlined,
  PlusOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined,
  InboxOutlined
} from "@ant-design/icons-vue";
import { paginationLocale } from "@/plugins/i18n";
import type {
  QueryItem,
  CustomButton,
  OperationButton,
  TableColumn,
  TableRecord,
  PaginationState,
  SortState,
  TableState
} from "./types";
import {
  CONSTANTS,
  formatTime,
  getRowKey,
  // getPagedData,
  calculateIndex,
  resetQueryForm,
  hasColumn,
  getStatusColor,
  getStatusText,
  getDefaultComponentProps
} from "./utils";
import { translateText } from "@/utils/translation";

// Props 定义
const props = withDefaults(
  defineProps<{
    columns: TableColumn[];
    dataSource: TableRecord[];
    rowKey?: string | ((record: TableRecord) => string);
    queryItems?: QueryItem[];
    customButtons?: CustomButton[];
    operationButtons?: OperationButton[];
    scroll?: { x?: string | number; y?: string | number };
    showQuery?: boolean;
    showBtns?: boolean;
    showRefresh?: boolean;
    showBatchDelete?: boolean;
    showAdd?: boolean;
    showImport?: boolean;
    showExport?: boolean;
    showView?: boolean;
    showEdit?: boolean;
    showDelete?: boolean;
    showIndex?: boolean;
    showCheckbox?: boolean;
    showOperation?: boolean; // 是否显示操作列
    // 核心功能 props
    loading?: boolean;
    bordered?: boolean;
    size?: "small" | "middle" | "default";
    tableLayout?: "auto" | "fixed";
    title?: string | ((currentPageData: TableRecord[]) => string);
    footer?: string | ((currentPageData: TableRecord[]) => string);
    summary?: (currentPageData: TableRecord[]) => any;
    id?: string;
    rowClassName?: string | ((record: TableRecord, index: number) => string);
    rowSelection?: Record<string, any>;
    customRow?: (record: TableRecord, index: number) => Record<string, any>;
    customHeaderRow?: (
      column: TableColumn,
      index: number
    ) => Record<string, any>;
    pagination?: PaginationState;
    resizable?: boolean;
    onResizeColumn?: (w: number, col: TableColumn) => void;
    // 新增属性
    paginationMode?: "server" | "client";
  }>(),
  {
    rowKey: CONSTANTS.DEFAULT_ROW_KEY,
    queryItems: () => [],
    customButtons: () => [],
    operationButtons: () => [],
    scroll: () => ({ x: CONSTANTS.DEFAULT_SCROLL_X, y: 600 }),
    showQuery: true,
    showBtns: true,
    showRefresh: false,
    showBatchDelete: true,
    showAdd: true,
    showImport: false,
    showExport: true,
    showView: true,
    showEdit: true,
    showDelete: true,
    showIndex: false,
    showCheckbox: true,
    showOperation: true,
    bordered: true,
    size: "default",
    pagination: () => ({}),
    resizable: true,
    paginationMode: "server"
  }
);

// 事件定义
const emit = defineEmits<{
  // 自定义事件
  query: [queryForm: Record<string, any>];
  reset: [];
  refresh: [];
  batchDelete: [keys: any[], rows: TableRecord[]];
  add: [];
  export: [keys: any[], rows: TableRecord[]];
  edit: [record: TableRecord];
  delete: [record: TableRecord];
  customButton: [key: string];
  operationButton: [{ key: string; record: TableRecord }];
  // 新增：查询项组件事件
  queryItemEvent: [{ key: string; eventName: string; value: any; args: any[] }];
  // a-table 事件
  change: [pagination: any, filters: any, sorter: any, extra: any];
  expand: [expanded: boolean, record: TableRecord];
  expandedRowsChange: [expandedRows: TableRecord[]];
  row: [record: TableRecord, index: number];
  headerRow: [column: TableColumn, index: number];
  paginationChange: [pagination: PaginationState];
  sortChange: [sort: SortState];
  stateChange: [state: TableState];
  resizeColumn: [width: number, column: TableColumn];
}>();

// 响应式数据
const queryForm = ref<Record<string, any>>({});
const selectedRowKeys = ref<any[]>([]);
const selectedRows = ref<TableRecord[]>([]);

// 内部状态管理
const internalPaginationState = ref<PaginationState>({
  pageIndex: 1,
  pageSize: 10,
  total: 0
});

const internalSortState = ref<SortState>({
  field: "id",
  order: "desc"
});

// 新增：前端分页和排序处理
const pagedData = computed(() => {
  if (props.paginationMode === "client") {
    let data = [...props.dataSource];
    // 排序
    if (internalSortState.value.field && internalSortState.value.order) {
      data.sort((a, b) => {
        const field = internalSortState.value.field as string;
        const order = internalSortState.value.order === "ascend" ? 1 : -1;
        if (a[field] > b[field]) return order;
        if (a[field] < b[field]) return -order;
        return 0;
      });
    }
    // 分页
    const start =
      (internalPaginationState.value.pageIndex - 1) *
      internalPaginationState.value.pageSize;
    const end = start + internalPaginationState.value.pageSize;
    return data.slice(start, end);
  } else {
    return props.dataSource;
  }
});
onMounted(() => {
  handleQuery();
});
// 监听外部total/pagination变化，驱动内部状态
watch(
  () => props.dataSource,
  val => {
    if (
      Array.isArray(val) &&
      props.pagination &&
      typeof props.pagination.total === "number"
    ) {
      internalPaginationState.value.total = props.pagination.total;
    }
  },
  { immediate: true, deep: true }
);

watch(
  () => props.pagination,
  val => {
    if (val) {
      internalPaginationState.value = {
        ...internalPaginationState.value,
        ...val
      };
    }
  },
  { immediate: true, deep: true }
);

const rowSelection = computed(() => {
  if (!props.showCheckbox) return undefined;

  return {
    type: "checkbox",
    showSelectAll: true,
    showCheckedStrategy: "SHOW_PARENT",
    selectedRowKeys: selectedRowKeys.value,
    onChange: (keys: any[], rows: TableRecord[]) => {
      selectedRowKeys.value = keys;
      selectedRows.value = rows;
    },
    preserveSelectedRowKeys: true,
    columnWidth: 50,
    fixed: true,
    ...(props.rowSelection || {})
  };
});

const computedColumns = computed(() => {
  const cols = [...props.columns].map(col => ({
    ...col,
    align: col.align || "center" // 如果用户没有设置align，则默认居中
  }));

  // 添加序号列
  if (props.showIndex && !hasColumn(cols, CONSTANTS.INDEX_COLUMN_KEY)) {
    cols.unshift({
      ...CONSTANTS.DEFAULT_INDEX_COLUMN,
      fixed: "left" as const,
      width: 60
    });
  }

  // 添加操作列（受 showOperation 控制）
  if (props.showOperation && !hasColumn(cols, CONSTANTS.OPERATION_COLUMN_KEY)) {
    cols.push({
      ...CONSTANTS.DEFAULT_OPERATION_COLUMN,
      fixed: "right" as const,
      width: 180
    });
  }

  return cols;
});

const computedQueryItems = computed(() => {
  return props.queryItems?.map(item => {
    const baseProps = getDefaultComponentProps(
      item.component || "a-input",
      item.label,
      item.props
    );

    // 事件处理器，专门用于 v-on 绑定
    const eventHandlers: Record<string, (...args: any[]) => void> = {};
    if (item.events) {
      Object.keys(item.events).forEach(eventName => {
        eventHandlers[eventName] = (...args: any[]) => {
          // 先调用用户自定义事件
          if (item.events && item.events[eventName]) {
            item.events[eventName](...args);
          }
          // 向上 emit 统一事件
          emit("queryItemEvent", {
            key: item.key,
            eventName,
            value: args[0],
            args
          });
        };
      });
    }

    return {
      ...item,
      props: baseProps,
      _eventHandlers: eventHandlers
    };
  });
});

// 事件处理函数
const handleQuery = () => {
  emit("query", queryForm.value);
};
const handleReset = () => {
  resetQueryForm(queryForm.value);
  emit("reset");
};
const handleRefresh = () => emit("refresh");
const handleBatchDelete = () =>
  emit("batchDelete", selectedRows.value, selectedRowKeys.value);
const handleAdd = () => emit("add");
const handleExport = () =>
  emit("export", selectedRowKeys.value, selectedRows.value);
const handleImport = () => emit("customButton", "import");
const handleEdit = (record: TableRecord) => emit("edit", record);
const handleDelete = (record: TableRecord) => emit("delete", record);
const handleCustomButton = (key: string) => emit("customButton", key);
const handleOperationButton = (key: string, record: TableRecord) =>
  emit("operationButton", { key, record });

// 状态更新函数
const updatePaginationState = (newPagination: PaginationState) => {
  internalPaginationState.value = {
    ...internalPaginationState.value,
    ...newPagination
  };
  emit("paginationChange", { ...internalPaginationState.value });
};

const updatePaginationTotal = (total: number) => {
  internalPaginationState.value.total = total;
};

const updateSortState = (newSort: SortState) => {
  internalSortState.value = { ...newSort };
  emit("sortChange", newSort);
};

// a-table 事件处理
const handleTableChange = (
  pagination: any,
  filters: any,
  sorter: any,
  extra: any
) => {
  // 处理排序变化
  if (sorter.field !== undefined) {
    const sortState: SortState = {
      field: sorter.field || "id",
      order: sorter.order || "desc"
    };
    updateSortState(sortState);
    emit("query", { ...queryForm.value });
  }

  // 处理分页变化
  if (pagination.current !== undefined || pagination.pageSize !== undefined) {
    const paginationState: PaginationState = {
      pageIndex: pagination.current || internalPaginationState.value.pageIndex,
      pageSize: pagination.pageSize || internalPaginationState.value.pageSize,
      total: internalPaginationState.value.total
    };
    updatePaginationState(paginationState);
    emit("query", {
      ...queryForm.value,
      pageIndex: paginationState.pageIndex,
      pageSize: paginationState.pageSize
    });
  }

  // 发出组合状态变化事件
  const tableState: TableState = {
    pagination: { ...internalPaginationState.value },
    sort: {
      field: sorter.field || undefined,
      order: sorter.order || null
    }
  };
  emit("stateChange", tableState);
  emit("change", pagination, filters, sorter, extra);
};

const handleExpand = (expanded: boolean, record: TableRecord) =>
  emit("expand", expanded, record);

const handleExpandedRowsChange = (expandedRows: TableRecord[]) =>
  emit("expandedRowsChange", expandedRows);

const handleRow = (record: TableRecord, index: number) =>
  props.customRow ? props.customRow(record, index) : {};

const handleHeaderRow = (column: TableColumn, index: number) =>
  props.customHeaderRow ? props.customHeaderRow(column, index) : {};

const handleResizeColumn = (w: number, col: TableColumn) => {
  emit("resizeColumn", w, col);
  if (props.onResizeColumn) {
    props.onResizeColumn(w, col);
  }
};

// 行样式计算
const tableRowClassName = (record: TableRecord, index: number) => {
  const rowKey = getRowKey(record, props.rowKey);
  let baseClassName = selectedRowKeys.value.includes(rowKey)
    ? CONSTANTS.SELECTED_ROW_CLASS
    : "";

  if (props.rowClassName) {
    const customClassName =
      typeof props.rowClassName === "function"
        ? props.rowClassName(record, index)
        : props.rowClassName;
    return `${baseClassName} ${customClassName}`.trim();
  }

  if (baseClassName === "" && index % 2 === 1) {
    baseClassName = "table-striped";
  }
  // console.log(baseClassName);
  return baseClassName;
};

// 暴露给父组件的方法和状态
defineExpose({
  // 状态获取
  getPaginationState: () => ({ ...internalPaginationState.value }),
  getSortState: () => ({ ...internalSortState.value }),
  getTableState: () => ({
    pagination: { ...internalPaginationState.value },
    sort: { ...internalSortState.value }
  }),

  // 状态更新
  setPaginationState: (pagination: PaginationState) => {
    updatePaginationState(pagination);
  },
  setSortState: (sort: SortState) => {
    updateSortState(sort);
  },
  setTableState: (state: TableState) => {
    updatePaginationState(state.pagination);
    updateSortState(state.sort);
  },
  setTotal: (total: number) => {
    updatePaginationTotal(total);
  },
  // 状态重置
  resetPagination: () => {
    updatePaginationState({ pageIndex: 1, pageSize: 10, total: 0 });
  },
  resetSort: () => {
    updateSortState({ field: undefined, order: null });
  },
  resetTableState: () => {
    resetPagination();
    resetSort();
  },

  // 查询表单相关
  getQueryForm: () => ({ ...queryForm.value }),
  setQueryForm: (form: Record<string, any>) => {
    Object.assign(queryForm.value, form);
  },
  resetQueryForm: () => {
    resetQueryForm(queryForm.value);
  },

  // 选中行相关
  getSelectedRows: () => selectedRows.value,
  getSelectedRowKeys: () => selectedRowKeys.value,
  clearSelection: () => {
    selectedRowKeys.value = [];
    selectedRows.value = [];
  },

  // 分页跳转
  goToPage: (pageIndex: number) => {
    updatePaginationState({
      ...internalPaginationState.value,
      pageIndex
    });
  },
  setPageSize: (pageSize: number) => {
    updatePaginationState({
      ...internalPaginationState.value,
      pageSize,
      pageIndex: 1
    });
  }
});
</script>

<template>
  <div class="tr-table-wrapper">
    <!-- 查询区 -->
    <div v-if="showQuery" class="tr-table-query-section">
      <a-form :model="queryForm" layout="inline" class="tr-table-query-form">
        <div
          style="
            display: flex;
            flex-direction: row;
            justify-content: space-around;
          "
        >
          <div>
            <a-row>
              <template v-for="item in computedQueryItems" :key="item.key">
                <a-col>
                  <a-form-item style="margin: 10px 0" :label="item.label">
                    <component
                      :is="
                        item.componentInstance || item.component || 'a-input'
                      "
                      v-model:value="queryForm[item.key]"
                      style="width: 150px"
                      v-bind="item.props"
                      v-on="item._eventHandlers"
                    />
                  </a-form-item>
                </a-col>
              </template>
            </a-row>
            <!-- 查询区扩展插槽 - 独立行，提供更灵活的布局 -->
            <a-row v-if="$slots.queryArea">
              <a-col :span="24">
                <slot
                  name="queryArea"
                  :queryForm="queryForm"
                  :pagination="internalPaginationState"
                  :sort="internalSortState"
                />
              </a-col>
            </a-row>
          </div>
          <div style="flex: 1; min-width: 200px">
            <a-row style="margin: 10px 0; float: right">
              <a-col>
                <a-button
                  class="tr-btn reset-btn"
                  style="margin-right: 15px"
                  @click="handleReset"
                >
                  <template #icon><ReloadOutlined /></template>
                  {{ translateText("重置") }}
                </a-button>
              </a-col>
              <a-col>
                <a-button
                  type="primary"
                  class="tr-btn search-btn"
                  @click="handleQuery"
                >
                  <template #icon><SearchOutlined /></template>
                  {{ translateText("查询") }}
                </a-button>
              </a-col>
            </a-row>
          </div>
        </div>
      </a-form>
    </div>
    <div v-if="showBtns" class="tr-table-btns-section">
      <a-row>
        <a-col>
          <!-- 按钮区 -->
          <div class="tr-table-btns">
            <a-button v-if="showRefresh" class="tr-btn" @click="handleRefresh">
              <template #icon><SyncOutlined /></template>
              {{ translateText("刷新") }}
            </a-button>
            <a-button v-if="showAdd" class="tr-btn" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              {{ translateText("新增") }}
            </a-button>
            <a-button
              v-if="showBatchDelete"
              class="tr-btn"
              danger
              :disabled="selectedRowKeys.length === 0"
              @click="handleBatchDelete"
            >
              <template #icon><DeleteOutlined /></template>
              {{ translateText("批量删除") }}
            </a-button>
            <a-button v-if="showImport" class="tr-btn" @click="handleImport">
              <template #icon><InboxOutlined /></template>
              {{ translateText("导入") }}
            </a-button>
            <a-button v-if="showExport" class="tr-btn" @click="handleExport">
              <template #icon><DownloadOutlined /></template>
              {{ translateText("导出") }}
            </a-button>
            <template v-for="btn in customButtons" :key="btn.key">
              <a-button
                v-bind="btn.props"
                class="tr-btn"
                @click="() => handleCustomButton(btn.key)"
              >
                {{ translateText(btn.label) }}
              </a-button>
            </template>
            <slot name="buttonArea" />
          </div>
        </a-col>
      </a-row>
    </div>
    <a-divider
      style="
        height: 1px;
        background-color: var(--gray-200, #e5e7eb);
        margin: 24px 0 0 0;
      "
    />
    <!-- 表格区 -->
    <div class="tr-table-table-section">
      <a-table
        :id="id"
        :columns="computedColumns"
        :data-source="pagedData"
        :row-key="rowKey"
        :pagination="false"
        :row-selection="rowSelection"
        :custom-row="handleRow"
        :custom-header-row="handleHeaderRow"
        :scroll="scroll"
        :loading="loading"
        :bordered="bordered"
        :size="size"
        :table-layout="tableLayout"
        :title="title"
        :footer="footer"
        :summary="summary"
        :row-class-name="tableRowClassName"
        :resizable="resizable"
        class="tr-table-table"
        v-bind="$attrs"
        @change="handleTableChange"
        @expand="handleExpand"
        @expanded-rows-change="handleExpandedRowsChange"
        @resize-column="handleResizeColumn"
      >
        <template #bodyCell="{ column, record, text, index }">
          <!-- 序号列 -->
          <template v-if="column.dataIndex === CONSTANTS.INDEX_COLUMN_KEY">
            <span>{{
              Math.max(
                1,
                (internalPaginationState.pageIndex - 1) *
                  internalPaginationState.pageSize +
                  index +
                  1
              )
            }}</span>
          </template>

          <!-- 自定义 render 函数 -->
          <template v-else-if="column.render">
            <component
              :is="column.render(text, record, index)"
              v-if="typeof column.render(text, record, index) === 'object'"
            />
            <span v-else>{{ column.render(text, record, index) }}</span>
          </template>

          <!-- 图片渲染 -->
          <template v-else-if="column.renderType === 'img'">
            <img
              :src="text"
              alt="img"
              style="
                width: 40px;
                height: 40px;
                object-fit: cover;
                border-radius: 6px;
                border: 1px solid #f0f0f0;
              "
            />
          </template>

          <!-- 标签渲染 -->
          <template v-else-if="column.renderType === 'tag'">
            <a-tag :color="column.tagColor || 'blue'">{{ text }}</a-tag>
          </template>

          <!-- 时间渲染 -->
          <template v-else-if="column.renderType === 'time'">
            <span>{{ formatTime(text) }}</span>
          </template>

          <!-- HTML渲染 -->
          <template v-else-if="column.renderType === 'html'">
            <span v-html="text" />
          </template>

          <!-- 状态渲染 -->
          <template v-else-if="column.renderType === 'status'">
            <a-tag :color="getStatusColor(text, column.statusMap)">
              {{ getStatusText(text, column.statusMap) }}
            </a-tag>
          </template>

          <!-- 操作列 -->
          <template
            v-else-if="column.dataIndex === CONSTANTS.OPERATION_COLUMN_KEY"
          >
            <slot name="operation" :record="record">
              <div class="tr-table-op-btns">
                <a-button
                  v-if="showView"
                  type="link"
                  class="tr-btn"
                  @click="() => handleOperationButton('view', record)"
                >
                  <template #icon><EyeOutlined /></template>
                  查看
                </a-button>
                <a-button
                  v-if="showEdit"
                  type="link"
                  class="tr-btn"
                  @click="() => handleEdit(record)"
                >
                  <template #icon><EditOutlined /></template>
                  编辑
                </a-button>
                <a-button
                  v-if="showDelete"
                  type="link"
                  danger
                  class="tr-btn"
                  @click="() => handleDelete(record)"
                >
                  <template #icon><DeleteOutlined /></template>
                  删除
                </a-button>
                <template v-for="btn in operationButtons" :key="btn.key">
                  <a-button
                    v-bind="btn.props"
                    class="tr-btn"
                    @click="() => handleOperationButton(btn.key, record)"
                  >
                    {{ btn.label }}
                  </a-button>
                </template>
              </div>
            </slot>
          </template>

          <!-- 默认渲染 -->
          <template v-else>
            {{ text }}
          </template>
        </template>
      </a-table>
    </div>

    <!-- 分页区 -->
    <div v-if="props.dataSource.length > 0" class="tr-table-pagination-section">
      <a-pagination
        :current="internalPaginationState.pageIndex"
        :page-size="internalPaginationState.pageSize"
        :total="internalPaginationState.total"
        :show-size-changer="true"
        :show-quick-jumper="true"
        :show-total="
          (total, range) =>
            `共${total}条记录，当前第${internalPaginationState.pageIndex}/${Math.ceil(total / internalPaginationState.pageSize)}页`
        "
        :page-size-options="CONSTANTS.PAGE_SIZE_OPTIONS"
        :locale="paginationLocale"
        @change="
          (page, pageSize) =>
            handleTableChange({ current: page, pageSize }, {}, {}, {})
        "
        @show-size-change="
          (page, pageSize) =>
            handleTableChange({ current: page, pageSize }, {}, {}, {})
        "
      />
    </div>
  </div>
</template>

<style scoped>
.tr-table-wrapper {
  height: 100%;
  background: #fff;
  border-radius: var(--border-radius-md, 8px);
  box-shadow: none;
  border: 1px solid var(--gray-200, #e5e7eb);
  display: flex;
  flex-direction: column;
}

.tr-table-query-section {
  padding: 16px 24px 12px 24px;
  background: #fff;
}

.tr-table-query-form {
  display: block;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 0;
}

.tr-table-btns-section {
  padding: 0px 24px;
  background: #fff;
}

.tr-table-btns {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.tr-btn {
  border-radius: var(--border-radius-sm, 6px) !important;
  font-size: 14px !important;
  height: 32px !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reset-btn {
  background: #fff !important;
  color: #4b5563 !important;
  border: 1px solid #e5e7eb !important;
}

.search-btn {
  margin-left: 0;
}

.tr-table-table-section {
  flex: 1;
  background: #fff;
  padding: 24px;
  overflow: auto;
}

.tr-table-table {
  border-radius: var(--border-radius-md, 8px);
  overflow: hidden;
  border: 1px solid var(--gray-200, #e5e7eb);
  width: 100%;
}

.tr-table-pagination-section {
  padding: 5px;
  background: #fff;
  border-top: 1px solid var(--gray-200, #e5e7eb);
  display: flex;
  align-items: center;
  justify-content: center;
}

.tr-table-op-btns {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  width: 100%;
}

.tr-table-op-btns .tr-btn {
  padding: 0 6px !important;
  min-width: 0 !important;
}

/* 穿透样式 */
:deep(.tr-table-table .ant-table-thead > tr > th) {
  background-color: #eff7fd !important;
  font-size: 14px;
  font-weight: 600;
  color: #4b5563;
  border-bottom: 1px solid var(--gray-200, #e5e7eb);
  padding: 10px 16px;
  position: sticky;
  top: 0;
  z-index: 2;
}

:deep(.tr-table-table .ant-table-tbody > tr > td) {
  font-size: 14px;
  color: #4b5563;
  border-bottom: 1px solid var(--gray-200, #e5e7eb);
  padding: 2px 16px;
  height: 40px;
  line-height: 40px;
}

:deep(.tr-table-table .ant-table-tbody > tr.tr-table-row-selected > td) {
  background-color: #e6f7ff !important;
}

:deep(.tr-table-table .ant-table-tbody > tr:hover > td) {
  background-color: #f5f6fa;
}

:deep(.tr-table-table .ant-table) {
  border: 0;
  border-radius: var(--border-radius-md, 8px);
  overflow: hidden;
}

:deep(.ant-table-selection-column .ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

/* 固定列样式优化 */
:deep(.ant-table-cell-fix-left),
:deep(.ant-table-cell-fix-right) {
  z-index: 1;
  background: #fff;
}

:deep(.ant-table-cell-fix-left-first) {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.12);
}

:deep(.ant-table-cell-fix-right-last) {
  box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.12);
}

/* 表头固定列样式 */
:deep(.ant-table-thead .ant-table-cell-fix-left),
:deep(.ant-table-thead .ant-table-cell-fix-right) {
  z-index: 3;
  background: #eff7fd !important;
}

:deep(.ant-table-thead .ant-table-cell-fix-left-first) {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.12);
}

:deep(.ant-table-thead .ant-table-cell-fix-right-last) {
  box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.12);
}

/* 确保表格容器正确处理滚动 */
:deep(.ant-table-container) {
  overflow: auto;
}

/* 修复表头和数据列对齐问题 */
:deep(.ant-table-thead > tr > th),
:deep(.ant-table-tbody > tr > td) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.ant-tag) {
  border-radius: var(--border-radius-sm, 6px);
  font-size: 12px;
  padding: 2px 8px;
  margin: 0;
}

:deep(.ant-pagination) {
  .ant-pagination-item {
    border-color: var(--gray-300, #d1d5db);
    border-radius: var(--border-radius-sm, 6px);
    font-size: 14px;

    &.ant-pagination-item-active {
      background-color: var(--primary-color, #1890ff);
      border-color: var(--primary-color, #1890ff);
      color: #fff !important;
    }

    &:hover {
      border-color: var(--primary-color, #1890ff);
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    border-color: var(--gray-300, #d1d5db);
    border-radius: var(--border-radius-sm, 6px);

    &:hover {
      border-color: var(--primary-color, #1890ff);
    }
  }

  .ant-pagination-options {
    .ant-select-selector {
      font-size: 14px;
    }

    .ant-select-selection-item {
      font-size: 14px;
    }
  }

  .ant-pagination-options-quick-jumper {
    font-size: 14px;
  }
}

:deep(.ant-pagination-item-active),
:deep(.ant-pagination-item-active a) {
  color: #fff !important;
}

:deep(.main-content) {
  height: 100%;
}

/* 响应式查询区设计 */
@media (max-width: 768px) {
  .tr-table-query-section {
    padding: 16px 16px 0 16px;
  }

  .tr-btn {
    padding: 0 16px !important;
    font-size: 13px !important;
  }
}
</style>
