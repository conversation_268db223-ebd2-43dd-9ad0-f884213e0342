<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.SysRoleMapper">


    <sql id="Base_Column_List">*</sql>
    <update id="updateBatchRoleId">
        UPDATE sys_user
        <set>
            user_group = #{userInfo.roleId}
        </set>
        <where>
            uuid = #{userInfo.uuid}
        </where>
    </update>
    <delete id="deleteSysAuthAccessByroleId">
        DELETE
        FROM sys_auth_access
        WHERE role_id = #{id}

    </delete>

    <select id="getPageSet" resultType="com.sbtr.workflow.model.SysRole">
        select
        <include refid="Base_Column_List"></include>
        from sys_auth_group
        <where>
            <if test="filterSort != null">
                ${filterSort}
            </if>
        </where>
    </select>
    <select id="getListByGroupStr" resultType="com.sbtr.workflow.model.SysRole">
        select * FROM sys_auth_group WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getListByPid" resultType="com.sbtr.workflow.model.SysRole">
        select *
        FROM sys_auth_group
        WHERE pid = #{param1}
          AND level_id !=0
        ORDER BY sort ASC
    </select>

    <select id="getListByCodeSetIdLevelId" resultType="com.sbtr.workflow.model.SysRole">
        select *
        FROM sys_auth_group
        WHERE code_set_id = #{param1}
          AND level_id = #{param2}
        ORDER BY sort ASC
    </select>

    <select id="getListByLevelId" resultType="com.sbtr.workflow.model.SysRole">
        select *
        FROM sys_auth_group
        WHERE level_id = #{param1}
        ORDER BY sort ASC
    </select>

    <select id="getListBySort" resultType="com.sbtr.workflow.model.SysRole">
        select *
        FROM sys_auth_group
        ORDER BY sort ASC
    </select>

    <select id="getMaxById" resultType="java.lang.Integer">
        SELECT MAX(t.id)
        FROM sys_auth_group t
    </select>
    <select id="getListByUuid" resultType="com.sbtr.workflow.model.SysRole">
        select * FROM sys_auth_group WHERE uuid in
        <foreach collection="list" index="index" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getListByUserNameIds" resultType="cn.ewsd.common.bean.UserInfo">
        select uuid,user_group as roleId,user_name as userName , user_name_id as userNameId,org_id AS orgId from
        sys_user where user_name_id in
        <foreach collection="userNameIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_del != 1
    </select>
    <select id="getPageSetData" resultType="com.sbtr.workflow.model.SysRole">
        select
        <include refid="Base_Column_List"></include>
        from sys_role
        where 1=1
        <if test="roleName!=null and roleName!=''">
            and role_name like '%'||#{roleName}||'%'
        </if>
        order by role_sort asc
    </select>
    <select id="getListByUserUuid" resultType="com.sbtr.workflow.model.SysRole">
        SELECT g.*
        FROM sys_auth_group g
                 RIGHT JOIN sys_auth_group_access a ON g.id = a.role_id
        WHERE g.level_id !=0 and a.user_name_uuid =#{userUuid}
        ORDER BY sort ASC

    </select>
    <select id="getList" resultType="com.sbtr.workflow.model.SysRole">
        SELECT *
        FROM sys_auth_group
        WHERE level_id !=0
        ORDER BY sort ASC
    </select>
    <select id="getListByText" resultType="com.sbtr.workflow.model.SysRole">
        SELECT *
        FROM sys_auth_group
        WHERE text = #{text}
    </select>
    <select id="getListByNameAndUuid" resultType="java.lang.Integer">
        SELECT count(text)
        FROM sys_auth_group
        WHERE text = #{text}
          AND uuid != #{uuid}
    </select>
    <select id="roleIdExistsUser" resultType="java.lang.Integer">
        SELECT count(1)
        FROM sys_user
        WHERE FIND_IN_SET(#{id}, user_group)
          AND is_del != 1
    </select>
    <select id="getChildIds" parameterType="map" statementType="CALLABLE" resultType="java.lang.String">
        {
            call p_get_child_ids(
                #{p1,mode=IN,jdbcType=NUMERIC},
                #{p2,mode=IN,jdbcType=NUMERIC},
                #{p3,mode=OUT,jdbcType=VARCHAR}
            )
            }
    </select>

    <select id="roleIdExistsUserOracle" resultType="java.lang.Integer">

        SELECT
            count( 1 )
        FROM
            sys_user
        WHERE
            instr(user_group,#{id}) > 0
          AND is_del != 1
    </select>


</mapper>
