package com.lms.system.feign.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lms.common.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Users entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)

@TableName("v_p_user")
@Data
@ApiModel(value = "人员用户视图实体类")
public class VUsers extends BaseModel {

    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 登陆名
     **/
    @ApiModelProperty("登陆名")
    private String name;

    /**
     * 密码
     **/
    @JsonIgnore
    @ApiModelProperty("密码")
    private String password;

    /**
     * 用户类型
     **/
    @JsonIgnore
    @ApiModelProperty("用户类型")
    private Integer usertype;

    @ApiModelProperty("人员id")
    private String personid;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("用户名称")
    private String personname;

    @ApiModelProperty("用户部门")
    private String departmentid;

    @ApiModelProperty("用户部门名称")
    private String deptname;

    @ApiModelProperty("角色名称")
    private String rolename;

    @ApiModelProperty("角色id")
    private String roleid;

    @JsonIgnore
    @ApiModelProperty("是否首次登录")
    private boolean isfirstlogin;

    @ApiModelProperty("是否被锁定")
    private boolean islocked;
    /**
     * 密级,对应S1000D中securityClassification属性值
     **/
    @ApiModelProperty("密级")
    private String usecurity;
    /**
     * 是否允许用户登录系统
     **/
    @ApiModelProperty("是否允许用户登录系统")
    private boolean enable;
    /**
     * 错误密码输入次数
     **/
    @JsonIgnore
    @ApiModelProperty("错误密码输入次数")
    private Integer psderrortimes;
    /**
     * 密码修改日期
     **/
    @JsonIgnore
    @ApiModelProperty("密码修改日期")
    private String psdupdatedate;
    /**
     * 上次登录时间，可用于错误密码输入次数的重置
     **/
    @JsonIgnore
    @ApiModelProperty("上次登录时间")
    private String lastlogintime;

    /**
     * 限制用户登录ip地址
     */
    @ApiModelProperty("限制用户登录ip地址")
    private String ipaddr;

    /**
     * 限制用户登录浏览器类型
     */
    @ApiModelProperty("限制用户登录浏览器类型")
    private String clientagent;
}
