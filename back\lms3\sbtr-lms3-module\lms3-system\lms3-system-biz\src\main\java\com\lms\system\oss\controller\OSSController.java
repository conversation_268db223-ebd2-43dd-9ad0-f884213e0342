package com.lms.system.oss.controller;

import com.lms.common.model.Result;
import com.lms.system.oss.service.OSSService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@RestController
@CrossOrigin
@RequestMapping("/oss")
@Api(value = "OSS文件上传管理", tags = "OSS文件上传管理")
public class OSSController {

    @Autowired
    private OSSService ossService;

    // 上传文件
    @PostMapping("/upload")
    @ApiOperation(value = "上传文件", httpMethod = "POST")
    @ApiImplicitParam(name = "file", value = "文件", required = true, dataType = "MultipartFile")
    public Result upload(@RequestParam("file") MultipartFile multipartFile) {
        try {
            if (multipartFile == null || multipartFile.isEmpty()) {
                return Result.error("文件不能为空");
            }
            String objectName = this.ossService.upload(multipartFile);
            return Result.OK(objectName);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    // 上传文件（返回详细信息）
    @PostMapping("/uploadFile")
    @ApiOperation(value = "上传文件（返回详细信息）", httpMethod = "POST")
    @ApiImplicitParam(name = "file", value = "文件", required = true, dataType = "MultipartFile")
    public Result uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                return Result.error("文件不能为空");
            }
            Object result = this.ossService.uploadFile(file, file.getOriginalFilename());
            return Result.OK(result);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    // 下载文件
    @GetMapping("/download")
    @ApiOperation(value = "下载文件", httpMethod = "GET")
    public void download(@RequestParam("fileName") String fileName, HttpServletResponse response) {
        try {
            if (StringUtils.isEmpty(fileName)) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("文件名不能为空");
                return;
            }
            this.ossService.download(fileName, response);
        } catch (Exception e) {
            log.error("文件下载失败", e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("文件下载失败: " + e.getMessage());
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    // 删除一个对象
    @PostMapping("/removeObject")
    @ApiOperation(value = "删除一个对象", httpMethod = "POST")
    public Result removeObject(@RequestParam("objectName") String objectName) {
        try {
            if (StringUtils.isEmpty(objectName)) {
                return Result.error("对象名称不能为空");
            }
            boolean result = this.ossService.removeObject(objectName);
            return result ? Result.OK("删除成功") : Result.error("删除失败");
        } catch (Exception e) {
            log.error("删除对象失败", e);
            return Result.error("删除对象失败: " + e.getMessage());
        }
    }

    // 文件访问路径
    @PostMapping("/getPreviewUrl")
    @ApiOperation(value = "获取文件访问路径", httpMethod = "POST")
    public Result getPreviewUrl(@RequestParam("objectName") String objectName) {
        try {
            if (StringUtils.isEmpty(objectName)) {
                return Result.error("对象名称不能为空");
            }
            String url = this.ossService.getPreviewUrl(objectName);
            return StringUtils.isNotEmpty(url) ? Result.OK(url) : Result.error("获取预览URL失败");
        } catch (Exception e) {
            log.error("获取预览URL失败", e);
            return Result.error("获取预览URL失败: " + e.getMessage());
        }
    }

    // 上传本地文件
    @PostMapping("/uploadLocalFile")
    @ApiOperation(value = "上传本地文件")
    public Result uploadLocalFile(@RequestParam("fileLocation") String fileLocation,
                                  @RequestParam("bucketName") String bucketName,
                                  @RequestParam("objectName") String objectName) {
        try {
            if (StringUtils.isEmpty(fileLocation) || StringUtils.isEmpty(bucketName) || StringUtils.isEmpty(objectName)) {
                return Result.error("参数不能为空");
            }
            this.ossService.uploadLocalFile(fileLocation, bucketName, objectName);
            return Result.OK("上传成功");
        } catch (Exception e) {
            log.error("本地文件上传失败", e);
            return Result.error("本地文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 捅相关
     **/

    // 列出所有存储桶名称
    @PostMapping("/listBucketNames")
    @ApiOperation(value = "列出所有存储桶名称", httpMethod = "POST")
    public Result listBucketNames() {
        try {
            return Result.OK(this.ossService.listBucketNames());
        } catch (Exception e) {
            log.error("列出存储桶失败", e);
            return Result.error("列出存储桶失败: " + e.getMessage());
        }
    }

    // 创建存储桶
    @PostMapping("/createBucket")
    @ApiOperation(value = "创建存储桶", httpMethod = "POST")
    public Result createBucket(@RequestParam("bucketName") String bucketName) {
        try {
            if (StringUtils.isEmpty(bucketName)) {
                return Result.error("存储桶名称不能为空");
            }
            String result = this.ossService.makeBucket(bucketName);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("创建存储桶失败", e);
            return Result.error("创建存储桶失败: " + e.getMessage());
        }
    }

    // 删除存储桶
    @PostMapping("/deleteBucket")
    @ApiOperation(value = "删除存储桶", httpMethod = "POST")
    public Result deleteBucket(@RequestParam("bucketName") String bucketName) {
        try {
            if (StringUtils.isEmpty(bucketName)) {
                return Result.error("存储桶名称不能为空");
            }
            boolean result = this.ossService.removeBucket(bucketName);
            return result ? Result.OK("删除成功") : Result.error("删除失败，存储桶不为空或不存在");
        } catch (Exception e) {
            log.error("删除存储桶失败", e);
            return Result.error("删除存储桶失败: " + e.getMessage());
        }
    }

    // 列出存储桶中的所有对象名称
    @PostMapping("/listObjectNames")
    @ApiOperation(value = "列出存储桶中的所有对象名称", httpMethod = "POST")
    public Result listObjectNames(@RequestParam(value = "bucketName", required = false) String bucketName) {
        try {
            return Result.OK(this.ossService.listObjectNames(bucketName));
        } catch (Exception e) {
            log.error("列出对象失败", e);
            return Result.error("列出对象失败: " + e.getMessage());
        }
    }

} 