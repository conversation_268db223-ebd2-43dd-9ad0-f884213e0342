package com.lms.system.menu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.system.feign.model.MenuRoleLink;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 角色菜单数据库操作接口
 *
 * <AUTHOR>
 */
@Mapper
public interface MenuRoleLinkMapper extends BaseMapper<MenuRoleLink> {

	@Transactional
	@Select(value = "delete from p_menurolelink where menuid = #{menuid} and roleid = #{roleid}")
	void deleteMenuRole(@Param("menuid") String menuid, @Param("roleid") String roleid);

	@Select("select p.id,p.menuid,p.roleid,m.url from p_menurolelink p left join p_menu m on p.menuid=m.id")
	List<MenuRoleLink> getRoleLinks();
}
