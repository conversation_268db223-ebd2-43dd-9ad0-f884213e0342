<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ActMyButtonMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sbtr.workflow.model.ActMyButton" id="flowButtonMap">
        <result property="uuid" column="uuid"/>
        <result property="createTime" column="create_time"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorOrgId" column="creator_org_id"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="buttonName" column="button_name"/>
        <result property="buttonCode" column="button_code"/>
        <result property="buttonSort" column="button_sort"/>
    </resultMap>

    <sql id="Base_Column_List" >
        uuid,
        create_time,
        creator,
        creator_id,
        creator_org_id,
        modifier_id,
        modifier,
        modify_time,
        button_name,
        button_code,
        button_sort,
        button_type
    </sql>

    <select id="getListByCodeAndUuid" resultType="java.lang.Integer">
        		SELECT count(button_code) FROM act_my_button WHERE button_code = #{buttonCode} AND uuid != #{uuid}
    </select>

    <delete id="executeDeleteBatch">
		delete from act_my_button where uuid in
		<foreach item="uuid" collection="array" open="(" separator="," close=")">
			#{uuid}
		</foreach>
	</delete>

</mapper>
