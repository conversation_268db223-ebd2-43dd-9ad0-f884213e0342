/**
 * FileName:DepartmentController.java
 * Author:<PERSON><PERSON><PERSON><PERSON>
 * Time  :2013-3-7下午4:18:10
 * CopyRight: SBTR LTD.
 * decription:to-do
 */
package com.lms.base.department.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.lms.base.feign.model.Department;
import com.lms.base.department.service.impl.DepartmentServiceImpl;
import com.lms.base.feign.model.Person;
import com.lms.base.person.service.impl.PersonServiceImpl;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.feign.dto.DepartmentTree;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.StringHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> 人员信息操作控制层
 */
@RestController
@RequestMapping("/department")
@Api(value = "部门管理", tags = "部门管理")
public class DepartmentController extends BaseController<Department> {
    @Resource
    private DepartmentServiceImpl departmentService;
    @Resource
    private PersonServiceImpl personService;

    /*
     * 获取所有有效人员信息(分页)
     */
    @ApiOperation(value = "查询所有部门", httpMethod = "GET")
    @RequestMapping(value = {"/listAll"}, method = RequestMethod.GET)
    public Result<List<Department>> getAllDepartment() {
        // super.setParameter(request);
        List<Department> departments = this.departmentService.listAll();
        return Result.OK(departments);
    }

    /*
     * 获取所有有效人员信息(分页)
     */
    @ApiOperation(value = "分页查询部门信息", httpMethod = "POST")
    @LMSLog(desc = "查询组织部门", otype = LogType.List, order = 1, objname = "组织部门列表")
    @PostMapping(value = {"/listpage"})
    public Result<Page<Department>> getAllDepartment(@RequestBody JSONObject request) {
        PageInfo pageInfo = super.getPageInfo(request);
        Page<Department> departments = this.departmentService.listByCondition(pageInfo);
        return Result.OK(departments);
    }

    @ApiOperation(value = "分页查询部门信息", httpMethod = "POST")
    @PostMapping(value = "/listTrainOrg")
    public Result<List<Department>> listTrainOrgDepartmentList(@RequestBody JSONObject jsonObject) {
        //设置分页参数,查询参数
        PageInfo pageInfo = super.getPageInfo(jsonObject);
        List<Department> trainOrgList = departmentService.listByCondition(pageInfo).getRecords();
        return Result.OK(trainOrgList);
    }

    @ApiOperation(value = "查询父节点下的所有部门信息", httpMethod = "GET")
    @RequestMapping(value = {"/list/{pid}"}, method = RequestMethod.GET)
    public Result getValidDepartment(@PathVariable("pid") String pid) {
        List<DepartmentTree> departmentTrees = this.departmentService.setObjectTree(pid);
        return Result.OK(departmentTrees);
    }


    /*
     * 根据ID获取部门信息
     */
    @ApiOperation(value = "根据id查询部门信息", httpMethod = "GET")
    @RequestMapping(value = {"/getDepartmentById"}, method = RequestMethod.GET)
    public Result<Department> getDepartmentById(@RequestParam("id") String did) {
        Department department = this.departmentService.getById(did);
        return Result.OK(department);
    }

    @PostMapping("/getDepartmentByIdList")
    @ApiOperation(value = "根据id列表查询部门信息", httpMethod = "POST")
    public Result<List<Department>> getDepartmentByIdList(@RequestBody List<String> idList) {
        return Result.OK(departmentService.listIds(idList));
    }

    @ApiOperation(value = "查询部门下的所有人员信息", httpMethod = "GET")
    @RequestMapping(value = {"/getPersons/{ids}"}, method = RequestMethod.GET)
    public Result<List<Person>> getValidPersonByDepartmentIds(@PathVariable("ids") String ids) { // 增加分页?
        // 暂时不分
        String[] pids = ids.split(",");
        ArrayList<String> arrayPids = new ArrayList<>(Arrays.asList(pids));
        List<Person> ps = personService.getValidPersonByDepartmentIds(arrayPids);
        return Result.OK(ps);
    }

    // 更新Action
    @ApiOperation(value = "编辑部门信息", httpMethod = "POST")
    @LMSLog(desc = "编辑组织部门", otype = LogType.Update, order = 1, method = "setDepartmentLog")
    @RequestMapping(value = {"/modify"}, method = RequestMethod.POST)
    public Result<Department> updateDepartment(@RequestBody Department department) {
        departmentService.saveOrUpdate(department);
        generateFullPath(department);
        departmentService.saveOrUpdate(department);
        return Result.OK(department);
    }

    // 删除
    @ApiOperation(value = "删除部门信息", httpMethod = "DELETE")
    @LMSLog(desc = "删除组织部门", otype = LogType.Delete, method = "setDepartmentLog", order = 1)
    @RequestMapping(value = {"/del/{id}"}, method = RequestMethod.DELETE)
    public Result<Void> deleteDepartment(@PathVariable("id") String id) {
        List<Department> ds = departmentService.getValidDepartmentsByPid(id);
        if (ds.size() > 0) {
            String tips = commonSystemApi.translateContent("只能删除没有子部门的组织单位！");
            return Result.error(tips, null);
        } else {
            List<String> dl = new ArrayList<>();
            dl.add(id);
            this.departmentService.batchDeleteDepartment(dl);
            return Result.OK(null);
        }

    }

    // 人员新增的处理
    @ApiOperation(value = "新增部门信息", httpMethod = "POST")
    @LMSLog(desc = "新增组织部门", otype = LogType.Save, method = "setDepartmentLog", order = 1)
    @RequestMapping(value = {"/add"}, method = RequestMethod.POST)
    public Result<Department> saveDepartment(@RequestBody Department department) {
        String name = StringHelper.null2String(department.getName());
        String id = StringHelper.null2String(department.getId());
        String pid = StringHelper.null2String(department.getPid());
        int result = this.departmentService.existDepartType(name, id, pid);
        if (result == 1) {
            String tips = commonSystemApi.translateContent("系统中已存在该部门名称");
            return Result.error(tips, null);
        } else if (result == 2) {
            String tips = commonSystemApi.translateContent("同一级别上已存在相同的部门名称");
            return Result.error(tips, null);
        }

        // 所有新增的用户都是普通用户
        department.setStatus(1);
        departmentService.saveOrUpdate(department);
        generateFullPath(department);
        departmentService.saveOrUpdate(department);
        return Result.OK(department);
    }

    /**
     * 生成构型树(型号) id全路径
     *
     * @param department
     */
    private void generateFullPath(Department department) {
        if (StringUtils.isNotEmpty(department.getPid())) {
            Department pdepartment = departmentService.getById(department.getPid());
            generateFullPath(pdepartment);
            department.setFullpath(pdepartment.getFullpath() + "/" + department.getId());
            department.setFullpathname(pdepartment.getFullpathname() + "/" + department.getName());
        } else {
            department.setFullpath(department.getId());
            department.setFullpathname(department.getName());
        }
    }


    public String setDepartmentLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.List)) {
            objname = "组织部门列表";
        } else if (lmslog.otype().equals(LogType.Save)) { //新增数据日志
            Department p = (Department) (args[0]);
            objname = StringHelper.null2String(p.getName());
        } else if (lmslog.otype().equals(LogType.Update)) { //新增数据日志
            Department p = (Department) (args[0]);
            objname = StringHelper.null2String(p.getName());
        } else if (lmslog.otype().equals(LogType.Delete)) { //删除数据日志
            String id = (String) (args[0]);
            Department p = departmentService.getById(id);
            objname = StringHelper.null2String(p.getName());
        }
        return objname;
    }
}
