package com.lms.examine.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * UExaminestudentrecord entity. <AUTHOR> Persistence Tools
 */

@Data
@TableName("u_examinestudentrecord")
@ApiModel("试卷学员考试题目对象")
public class Examinestudentrecord extends BaseModel {

    // Fields
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("试卷学员考试题目唯一标识")
    private String id;

    @ApiModelProperty("试卷id")
    private String examineid;

    @ApiModelProperty("试卷题目id")
    private String examinesubjectid;

    @ApiModelProperty("人员id")
    private String personid;

    @ApiModelProperty("答题结果")
    private String answer;

    @ApiModelProperty("得分")
    private Double score;

    @ApiModelProperty("试卷类型")
    private Integer examinetype;

    @ApiModelProperty("题目标题")
    private String title;

    @ApiModelProperty("题目内容")
    private String content;

    @ApiModelProperty("题目类型")
    private String type;

    @ApiModelProperty("参考答案")
    private String correctresponse;

    @ApiModelProperty("序号")
    private Integer seqno;

    @ApiModelProperty("试卷学员id")
    private String studentid;

    @ApiModelProperty("是否标记")
    private Integer ismark;

    @TableField(exist = false)
    private String softcode;

}
