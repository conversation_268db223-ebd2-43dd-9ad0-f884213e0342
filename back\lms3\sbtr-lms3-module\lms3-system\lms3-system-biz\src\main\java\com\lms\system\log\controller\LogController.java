/**
 * FileName:SettingController.java
 * Author:<PERSON><PERSON><PERSON><PERSON>
 * Time  :2013-3-7下午4:18:10
 * CopyRight: SBTR LTD.
 * decription:to-do
 */
package com.lms.system.log.controller;

import com.alibaba.fastjson.JSONObject;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.enums.RoleEum;
import com.lms.common.model.JwtUser;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.*;
import com.lms.system.languagelabel.service.LanguagelabelService;
import com.lms.system.feign.model.Log;
import com.lms.system.log.service.LogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * <AUTHOR> 系统日志控制层
 */
@RestController
@RequestMapping("/log")
@Api(value = "系统操作日志", tags = {"系统操作日志"})
public class LogController extends BaseController<Log> {
    @Resource
    private LogService logService;
    @Resource
    private LanguagelabelService languagelabelService;

    @PostMapping(value = {"/listpage"})
    @ApiOperation("操作日志查询")
    public Result listpage(@RequestBody JSONObject request) {
        String role = ContextUtil.getRoleId();
        if (role.equals(RoleEum.admin.getValue())) {//培训管理员，仅查看普通用户日志
            request.put("PARAMETER_I_LT_usertype", 50);
        } else if (role.equals("b5c3192bfb0d44779151689f320381ba")) {//保密只看系统管理员和普通用户
            request.put("PARAMETER_I_LE_usertype", 50);
        } else if (role.equals("2708a87b2c31492fa23d4052c0b852c9")) {//审计只看保密，和系统管理员
            request.put("PARAMETER_I_GE_usertype", 50);
            request.put("PARAMETER_I_LE_usertype", 60);
        }
        request.put("PARAMETER_S_NE_logtype", LogType.Exception);

        if (StringUtils.isNotEmpty(request.getString("startDate")) &&
                StringUtils.isNotEmpty(request.getString("endDate"))) {
            request.put("PARAMETER_S_LE_submitdate", request.getString("endDate")); //小于等于
            request.put("PARAMETER_S_GE_submitdate", request.getString("startDate")); //大于等于
        }

        PageInfo pageInfo = super.getPageInfo(request);
        Page<Log> logList = logService.listByCondition(pageInfo);
        return Result.OK(logList);
    }

    @PostMapping(value = {"/listExpage"})
    @ApiOperation("异常日志查询")
    public Result listExpage(@RequestBody JSONObject request) {
        request.put("PARAMETER_S_EQ_logtype", LogType.Exception);
        PageInfo pageInfo = super.getPageInfo(request);
        Page<Log> logList = logService.listByCondition(pageInfo);
        return Result.OK(logList);
    }

    @PostMapping(value = {"/saveByParam"})
    @ApiOperation("保存操作日志（根据参数）")
    public Result saveLog(@RequestParam("objname") String objname, @RequestParam("desc") String desc, @RequestParam("logtype") String logtype, @RequestParam("result") String result) {
        JwtUser jwtUser = Optional.ofNullable(ContextUtil.getCurrentUser()).orElse(new JwtUser());
        // 常见日志实体对象
        Log log = new Log();
        log.setLogobj(jwtUser.getUserid());
        log.setSubmitdate(DateHelper.getCurrentDate());
        log.setObjname(objname);
        log.setDescription(languagelabelService.translateContent(desc, languagelabelService.translateContent("操作日志")));
        log.setSubmitor(jwtUser.getUsername());
        log.setSubmitdate(DateHelper.getCurrentDate());
        log.setSubmittime(DateHelper.getCurDateTime());
        log.setSubmitip(ServletUtil.getIpAddr());
        log.setLogtype(languagelabelService.translateContent(logtype));
        log.setResult(languagelabelService.translateContent(result));
        log.setRole(jwtUser.getRoleid());
        log.setUsertype(jwtUser.getUsertype());
        logService.saveOrUpdate(log);
        return Result.OK();
    }

    @PostMapping(value = {"/saveByLog"})
    @ApiOperation("保存操作日志（根据对象）")
    public Result saveLog(@RequestBody Log log) {
        JwtUser jwtUser = ContextUtil.getCurrentUser();
        // 常见日志实体对象
        if (jwtUser == null) {
            return Result.error("解析用户失败");
        }
        log.setLogobj(jwtUser.getUserid());
        log.setSubmitdate(DateHelper.getCurrentDate());
        log.setSubmitor(jwtUser.getUsername());
        log.setSubmitdate(DateHelper.getCurrentDate());
        log.setSubmittime(DateHelper.getCurDateTime());
        log.setSubmitip(ServletUtil.getIpAddr());
        log.setRole(jwtUser.getRoleid());
        log.setUsertype(jwtUser.getUsertype());
        logService.saveOrUpdate(log);
        return Result.OK();
    }

    @PostMapping(value = {"/export"})
    @ApiOperation("导出日志")
    @LMSLog(desc = "导出日志", otype = LogType.List, order = 1, objname = "日志列表")
    public void exportLog(@RequestBody JSONObject request, HttpServletResponse response) {
        String role = ContextUtil.getRoleId();
        if (role.equals(RoleEum.admin.getValue())) {//培训管理员，仅查看普通用户日志
            request.put("PARAMETER_I_LT_usertype", 50);
        } else if (role.equals("b5c3192bfb0d44779151689f320381ba")) {//保密只看系统管理员和普通用户
            request.put("PARAMETER_I_LE_usertype", 50);
        } else if (role.equals("2708a87b2c31492fa23d4052c0b852c9")) {//审计只看保密，和系统管理员
            request.put("PARAMETER_I_GE_usertype", 50);
            request.put("PARAMETER_I_LE_usertype", 60);
        }
        request.put("PARAMETER_S_NE_logtype", LogType.Exception);
        if (StringUtils.isNotEmpty(request.getString("startDate")) &&
                StringUtils.isNotEmpty(request.getString("endDate"))) {
            request.put("PARAMETER_S_LE_submitdate", request.getString("endDate")); //小于等于
            request.put("PARAMETER_S_GE_submitdate", request.getString("startDate")); //大于等于
            PageInfo pageInfo = super.getPageInfo(request);
            pageInfo.setPageSize(9999);
            pageInfo.setPageIndex(1);
            Page<Log> page = logService.listByCondition(pageInfo);
            if (page != null && page.getRecords() != null && CollectionUtils.isNotEmpty(page.getRecords())) {
                List<Log> logs = page.getRecords();
                ArrayList<Log> DataList = new ArrayList<>();
                DataList.addAll(logs);
                ExcelUtils.exportExcel(DataList, "用户日志信息导出", "用户信息列表", Log.class, "用户日志信息导出" + DateHelper.getCurrentDate(), response);
            }
        }
    }

}
