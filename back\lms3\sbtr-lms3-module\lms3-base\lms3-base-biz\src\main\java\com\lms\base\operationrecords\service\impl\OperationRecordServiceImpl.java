package com.lms.base.operationrecords.service.impl;

import com.lms.base.operationrecords.dao.OperationRecordDao;
import com.lms.base.operationrecords.model.OperationRecord;
import com.lms.base.operationrecords.service.OperationRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 操作记录表Service实现。
 */
@Service
public class OperationRecordServiceImpl implements OperationRecordService {

    private final OperationRecordDao operationRecordDao;

    public OperationRecordServiceImpl(OperationRecordDao operationRecordDao) {
        this.operationRecordDao = operationRecordDao;
    }

    @Override
    public void save(OperationRecord record) {
        operationRecordDao.save(record);
    }

    @Override
    public void deleteById(String id) {
        operationRecordDao.deleteById(id);
    }

    @Override
    public OperationRecord findById(String id) {
        return operationRecordDao.findById(id);
    }

    @Override
    public List<OperationRecord> queryList(java.util.Map<String, Object> conditions, int page, int size) {
        return operationRecordDao.queryList(conditions, page, size);
    }

    @Override
    public void saveBatch(List<OperationRecord> records) {
        for (OperationRecord record : records) {
            operationRecordDao.save(record);
        }
    }

    @Override
    public void deleteBatch(List<String> ids) {
        for (String id : ids) {
            operationRecordDao.deleteById(id);
        }
    }

    @Override
    public List<OperationRecord> findByIds(List<String> ids) {
        return operationRecordDao.findByIds(ids);
    }
} 