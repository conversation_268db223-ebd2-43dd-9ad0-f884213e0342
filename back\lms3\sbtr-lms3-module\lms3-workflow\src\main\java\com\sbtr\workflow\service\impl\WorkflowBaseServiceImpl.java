package com.sbtr.workflow.service.impl;

import cn.ewsd.common.common.Constants;
import cn.ewsd.common.service.impl.MybatisBaseServiceImpl;
import cn.ewsd.common.utils.JsonUtils;
import com.sbtr.workflow.properties.Druid;
import com.sbtr.workflow.service.WorkflowBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;

/**
 * 基础服务实现
 * ============================================================================
 * <AUTHOR>
 * @date    2015-06-01
 * @version 0.1
 * ============================================================================
 */

@Service("systemBaseServiceImpl")
public abstract class WorkflowBaseServiceImpl<T, PK extends Serializable> extends MybatisBaseServiceImpl<T, Serializable> implements WorkflowBaseService<T, PK> {
    public Object failure(String message) {
        return JsonUtils.messageJson(300,  Constants.OPERATE_TIPS, message);
    }
}
