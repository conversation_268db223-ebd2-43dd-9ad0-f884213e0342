package com.lms.base.subject.controller; /**
 * 类文件说明：
 * Title:		CoursewareController.java
 * Author:		<PERSON><PERSON><PERSON><PERSON>
 * Time:		2017-11-15 下午04:32:41
 * CopyRight: 	SBTR LTD.
 * description:	TODO
 */

import com.alibaba.fastjson.JSONObject;
import com.lms.base.component.service.ComponentService;
import com.lms.base.feign.model.Component;
import com.lms.base.feign.model.Subject;
import com.lms.base.subject.model.SubjectEdit;
import com.lms.base.subject.model.SubjectViewModel;
import com.lms.base.feign.model.Wrongsubject;
import com.lms.base.subject.service.SubjectService;
import com.lms.base.subject.service.WrongsubjectService;
import com.lms.common.config.LMSConfiguration;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.dao.query.Parameter;
import com.lms.common.model.Result;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.DateHelper;
import com.lms.common.util.ExcelUtils;
import com.lms.common.util.StringHelper;
import com.lms.examine.feign.model.Examinesubject;
import com.lms.system.feign.api.AttachApi;
import com.lms.examine.feign.api.ExaminesubjectApi;
import com.lms.system.feign.model.Attach;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.*;


/**
 * 类说明：
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/subject")
@Api(value = "试题管理", tags = "试题管理")
public class SubjectController extends BaseController<Subject> {

    @Resource
    private SubjectService subjectService;

    @Resource
    private WrongsubjectService wrongsubjectService;

    @Resource
    private ComponentService componentService;

    @Resource
    private ExaminesubjectApi examinesubjectApi;

    @Resource
    private AttachApi attachApi;

    @Value("${business.newCodePrefix.subject}")
    private String newCodePrefix;

    @Resource
    private LMSConfiguration lmsConfiguration;

    // 根据节点请求资源
    @PostMapping(value = {"/list/{componentid}"})
    @ApiOperation(value = "查询当前课程下的所有试题", httpMethod = "POST")
    public Result subjectList(@ApiParam(value = "课程id") @PathVariable("componentid") String componentid,
                              @ApiParam(value = "查询条件") @RequestBody JSONObject request) {
        PageInfo pageInfo = super.getPageInfo(request);
//        pageInfo.setOrderName("modifydate");
//        pageInfo.setSort(pageInfo.DESC);
        if (!StringHelper.isEmpty(componentid) && !componentid.equals("listAll")) {
            Component component = componentService.getById(componentid);
            if (component != null){
                Parameter componentpar = Parameter.getParameter(
                        "S_LIKE_equipmentid", componentid);
                pageInfo.getParameters().add(componentpar);
            }else {
                Parameter componentpar = Parameter.getParameter(
                        "S_EQ_componentid", componentid);
                pageInfo.getParameters().add(componentpar);
            }
        }
        Page<Subject> subjectPage = subjectService.listByCondition(pageInfo);
        componentService.adaptComponent(subjectPage.getRecords());
        List<SubjectViewModel> sModelList = new ArrayList<>();
        for (Subject sub : subjectPage.getRecords()) {
            if (!StringHelper.isEmpty(sub.getAttach())) {
                Result<Attach> result = attachApi.get(sub.getAttach());
                if (result != null) {
                    Attach attach = result.getResult();
                    sub.setImgUrl(attach.getFiledir());
                }
            }
            SubjectViewModel s = new SubjectViewModel(sub);
            s.setId(sub.getId());
            sModelList.add(s);
        }
        return Result.OK(createPage(sModelList, subjectPage));
    }

    //获取试题选择列表，判断试题是否被选择；
    @PostMapping(value = {"/selectList"})
    @ApiOperation(value = "获取试题选择列表，判断试题是否被选择", httpMethod = "POST")
    public Result selectList(@RequestBody JSONObject request) {
        PageInfo pageInfo = super.getPageInfo(request);
        String componentid = StringHelper.null2String(request.getString("parentid"));
        String examineid = StringHelper.null2String(request.getString("examineid"));
        pageInfo.setOrderName("modifydate");
        pageInfo.setSort(pageInfo.DESC);
        if (!StringHelper.isEmpty(componentid)) {
            Parameter componentpar = Parameter.getParameter(
                    "S_Like_componentid", componentid);
            pageInfo.getParameters().add(componentpar);
        }
        Page<Subject> subjectPage = subjectService.listByCondition(pageInfo);
        componentService.adaptComponent(subjectPage.getRecords());
        List<SubjectViewModel> sModelList = new ArrayList<SubjectViewModel>();
        for (Subject subject : subjectPage.getRecords()) {
            if (examinesubjectApi.getExaminesubjectsBySubjectId(examineid, subject.getId()).getResult().size() > 0) {
                subject.setSelectedFlag(true);
            } else {
                subject.setSelectedFlag(false);
            }
            SubjectViewModel s = new SubjectViewModel(subject);
            s.setId(subject.getId());
            sModelList.add(s);
        }
        return Result.OK(createPage(sModelList, subjectPage));
    }

    @PostMapping(value = {"/listpage"})
    @ApiOperation(value = "分页查询", httpMethod = "POST")
    public Result courseWareListpage(@RequestBody JSONObject request) {
        // super.setParameter(request);
        PageInfo pageInfo = super.getPageInfo(request);
        Page<Subject> resList = this.subjectService.listByCondition(pageInfo);
        for(Subject subject : resList.getRecords()){
            subjectService.parseItems(subject);
        }
        componentService.adaptComponent(resList.getRecords());
        return Result.OK(resList);
    }

    /*
     * 新建保存或者更新
     */
    @RequestMapping(value = {"/save"}, method = RequestMethod.POST)
    @ApiOperation(value = "新建保存或者更新", httpMethod = "POST")
    public Result createSubject(@RequestBody Subject subject) {
        if (StringUtils.isNotEmpty(subject.getComponentid())) {
            Component c = componentService.getById(subject.getComponentid());
            if(c!=null){
                subject.setEquipmentid(c.getFullpath());
            }
        }
        subjectService.resetKKD(subject);
        subjectService.parseContent(subject);
        if (StringHelper.isEmpty(subject.getId())) {
            subject.setModifydate(DateHelper.getCurDateTime());
            subject.setCreatedate(DateHelper.getCurrentDate());
            subject.setCreatedeptid(ContextUtil.getDepartId());
            subject.setCreatorid(ContextUtil.getPersonId());
            subject.setNumber(commonSystemApi.getNewCode(newCodePrefix));
            this.subjectService.saveOrUpdate(subject);
            commonSystemApi.saveLog(StringHelper.null2String(subject.getTitle()), "新增题目", LogType.Save, "操作成功！");
        } else {
            subject.setModifydate(DateHelper.getCurDateTime());
            this.subjectService.saveOrUpdate(subject);
            commonSystemApi.saveLog(StringHelper.null2String(subject.getTitle()), "修改题目", LogType.Update, "操作成功！");
        }
        return Result.OK(subject);
    }

    @RequestMapping(value = {"/get/{subjectid}"}, method = RequestMethod.GET)
    @ApiOperation(value = "根据ID查询", httpMethod = "GET")
    public Result getSubject(@ApiParam(value = "试卷id") @PathVariable("subjectid") String subjectid) {
        Subject subject = this.subjectService.getById(subjectid);
        componentService.adaptComponent(Collections.singletonList(subject));
        if (subject == null || StringHelper.isEmpty(subject.getId())) {
            return Result.error("不存在的题目！");
        }

        if (subject.getAttach() != null) {
            Attach attach = attachApi.get(subject.getAttach()).getResult();
            if (attach != null) {
                subject.setImgUrl(attach.getFiledir());
            }
        }
        SubjectViewModel viewModel = new SubjectViewModel(subject);
        return Result.OK(viewModel);
    }

    @LMSLog(desc = "删除题目", otype = LogType.Delete, order = 2, method = "setSubjectLog")
    @RequestMapping(value = {"/delete"}, method = RequestMethod.DELETE)
    @ApiOperation(value = "删除题目", httpMethod = "DELETE")
    public Result deleteSubject(@RequestParam("ids") String ids) {
        String[] idArray = StringHelper.string2Array(ids, ",");
        if (idArray != null) {
            for (String id : idArray) {
                List<Examinesubject> list = examinesubjectApi.isExistBySubjectId(id).getResult();//使用的题目不删除
                if (list != null && list.size() > 0) {
                    continue;
                }
                subjectService.removeById(id);
            }

        }
        return Result.OK();
    }

    @RequestMapping(value = {"/subjectAnalysis"}, method = RequestMethod.GET)
    @ApiOperation(value = "根据指定字段统计试题数量", httpMethod = "GET")
    public Result getSubjectAnalysisByType(@RequestParam("analysetype") String analysetype) {
        List subjects = this.subjectService.getSubjectAnalysisByType(analysetype);
        componentService.adaptComponent(subjects);
        return Result.OK(subjects);
    }

    @RequestMapping(value = {"/placticelist/{ordertype}"}, method = RequestMethod.POST)
    @ApiOperation(value = "获取练习题目", httpMethod = "POST")
    public Result getPlacticeList(
            @ApiParam(value = "是否是随机") @PathVariable("ordertype") String ordertype,
            @RequestBody JSONObject jsonObject) {
        PageInfo pageInfo = super.getPageInfo(jsonObject);
//		pageInfo.setOrderName("modifydate");
//		pageInfo.setSort(pageInfo.DESC);
        // List<Subject> subjectList = subjectService.listByCondition(pageInfo);
//        String couseids = request.getParameter("couseids");
        List<String> courseidList = new ArrayList<>();
        List<Subject> subjectList = subjectService.getPlacticeSubjectList(ordertype, courseidList);
        componentService.adaptComponent(subjectList);
        List<SubjectViewModel> sModelList = new ArrayList<SubjectViewModel>();
        for (Subject sub : subjectList) {
            SubjectViewModel s = new SubjectViewModel(sub);
            sModelList.add(s);
        }
        return Result.OK(sModelList);
    }

    //错题列表请求
    @RequestMapping(value = {"/wronglist"}, method = RequestMethod.POST)
    @ApiOperation(value = "错题列表请求", httpMethod = "POST")
    public Result wrongListpage(@RequestBody JSONObject jsonObject) {
        PageInfo pageInfo = super.getPageInfo(jsonObject);
        List<String> courseidList = new ArrayList<>();
        List<Wrongsubject> resList = this.wrongsubjectService.getWrongSubjectList(courseidList);
        List<SubjectViewModel> sModelList = new ArrayList<SubjectViewModel>();
        for (int i = 0; i < resList.size(); i++) {
            Wrongsubject wrongsubject = resList.get(i);
            Subject sub = subjectService.getById(StringHelper.null2String(wrongsubject.getSubjectid()));
            componentService.adaptComponent(Collections.singletonList(sub));
            if (sub != null) {
                SubjectViewModel s = new SubjectViewModel(sub);
                sModelList.add(s);
            }
        }
        return Result.OK(sModelList);
    }

    @GetMapping("/getSubjectById")
    @ApiOperation(value = "根据ID查询", httpMethod = "GET")
    public Result<Subject> getSubjectById(@RequestParam("id") String id) {
        Subject subject = subjectService.getById(id);
        componentService.adaptComponent(Collections.singletonList(subject));
        return Result.OK(subject);
    }

    @PostMapping("/getRandomIds")
    @ApiOperation(value = "获取随机试题id列表", httpMethod = "POST")
    public List<String> getRandomIds(@RequestBody HashMap map) throws Exception {
        return subjectService.getRandomIds(map);
    }

    /*
     * 添加错题记录
     */
    @RequestMapping(value = {"/saveWrongsubject"}, method = RequestMethod.POST)
    @ApiOperation(value = "添加错题记录", httpMethod = "POST")
    public Result createWrongSubject(
            @RequestBody Wrongsubject wrongsubject) throws SQLException {
        String personid = ContextUtil.getPersonId();
        String subjectid = StringHelper.null2String(wrongsubject.getSubjectid());
        boolean isexist = wrongsubjectService.existSubject(personid, subjectid);
        if (!isexist) {
            int currentno = this.wrongsubjectService.getMaxSeqno(personid);
            currentno++;
            wrongsubject.setSeqno(currentno);
            wrongsubject.setCreatetime(DateHelper.getCurDateTime());
            wrongsubject.setPersonid(personid);
            this.wrongsubjectService.saveOrUpdate(wrongsubject);
        }
        return Result.OK(wrongsubject);
    }

    /**
     * 移除错题记录
     */
    @RequestMapping(value = {"/removewrong/{id}"}, method = RequestMethod.DELETE)
    @ApiOperation(value = "移除错题记录", httpMethod = "DELETE")
    public Result deleteNotice(@PathVariable("id") String id) {
        String personid = ContextUtil.getPersonId();
        List<Wrongsubject> wsList = wrongsubjectService.getByPersonAndSubject(personid, id);
        for (Wrongsubject ws : wsList) {
            this.wrongsubjectService.removeById(ws);
        }
        return Result.OK();
    }

    /*导入excel表放制定位置存起来，方便后面操作读取*/
    @RequestMapping(value = {"/importExcel"}, method = RequestMethod.POST)
    @ApiOperation(value = "读取excel保存试题", httpMethod = "POST")
    public Result uploadfile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("cklen") int cklen,
            @RequestParam("courseId") String courseId) throws Exception {
        if (file != null && !file.isEmpty()) {
            //开始调用底层函数，读取excel表里面的内容
            List<Subject> subjectList;
            try {
                subjectList = ExcelUtils.importExcel(file, 1, 1, 0, Subject.class);
            } catch (Exception e) {
                return Result.error("导入文件上传失败");
            }
            String msg = subjectService.checkImportData(subjectList, cklen);
            if (msg.isEmpty()) {
                int result = subjectService.saveImportData(subjectList, courseId);
                msg = commonSystemApi.translateContent("导入成功，共插入[?]个题目！", "", String.valueOf(result));
            }
            List<String> resultList = new ArrayList<String>();
            resultList.add(msg);
            return Result.OK(resultList);
        }
        return Result.error("文件不存在");
    }


    @RequestMapping(value = {"/isexsitfile"}, method = RequestMethod.POST)
    @ApiOperation(value = "判断文件是否存在", httpMethod = "POST")
    public Result isexsitfile(@ApiParam(value = "附件id") @RequestParam("id") String id) {
        if (id == null) {
            Result.OK(false);
        }
        Attach attach = this.attachApi.get(id).getResult();
        if (attach == null) {
            Result.OK(false);
        }
        Boolean attachFile = attachApi.checkFileIsExist(attach.getObjname()).getResult();
        if (!attachFile) {
            return Result.OK(false);
        }
        return Result.OK(attach.getFiledir());
    }

    @PostMapping(value = {"/calcSubjectKKD"})
    @ApiOperation(value = "更新试题可靠度", httpMethod = "POST")
    Result<String> calcSubjectKKD(@RequestBody List list) {
        subjectService.calcSubjectKKD(list);
        return Result.OK("试题可靠度更新完成");
    }

    public String setSubjectLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.Delete)) { //删除数据日志
            String ids = (String) (args[0]);
            String[] idarray = ids.split(",");
            for (String id : idarray) {
                Subject p = subjectService.getById(id);
                objname = objname.isEmpty() ? StringHelper.null2String(p.getTitle()) : objname + "," + StringHelper.null2String(p.getTitle());
            }
        }
        return objname;
    }


}
