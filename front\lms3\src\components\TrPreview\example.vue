<template>
  <div class="tr-preview-example">
    <h2>TrPreview 组件示例</h2>

    <div class="example-grid">
      <!-- 图片预览示例 -->
      <div class="example-item">
        <h3>图片预览</h3>
        <TrPreview
          :file="imageFile"
          width="400px"
          height="300px"
          @load="handleImageLoad"
          @error="handleError"
        />
      </div>

      <!-- 视频预览示例 -->
      <div class="example-item">
        <h3>视频预览</h3>
        <TrPreview
          :file="videoFile"
          width="400px"
          height="300px"
          @load="handleVideoLoad"
          @error="handleError"
        />
      </div>

      <!-- 3D模型预览示例 -->
      <div class="example-item">
        <h3>3D模型预览</h3>
        <TrPreview
          :file="modelFile"
          width="400px"
          height="300px"
          :show-edit-button="true"
          :show-fullscreen-button="true"
          @load="handleModelLoad"
          @error="handleError"
          @edit="handleEdit"
          @fullscreen="handleFullscreen"
        />
      </div>

      <!-- 其他文件预览示例 -->
      <div class="example-item">
        <h3>其他文件预览</h3>
        <TrPreview
          :file="otherFile"
          width="400px"
          height="300px"
          @load="handleOtherLoad"
          @error="handleError"
          @download="handleDownload"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { TrPreview, type FileInfo } from './index';

// 示例文件数据
const imageFile: FileInfo = {
  name: '示例图片.jpg',
  url: 'https://xiaohai-lms.oss-cn-guangzhou.aliyuncs.com/%E3%80%8A%E9%AC%BC%E5%93%AD%E9%82%A6(ONINAKI)%E3%80%8B%E6%B8%B8%E6%88%8F%E5%8E%9F%E7%94%BB5k%E5%A3%81%E7%BA%B85120x2880_%E5%9B%BE%E7%89%87%E7%BC%96%E5%8F%B7320132_%E5%A3%81%E7%BA%B8%E7%BD%91.jpeg',
  type: 'image'
};

const videoFile: FileInfo = {
  name: '示例视频.mp4',
  url: 'https://example.com/model.mp4',
  type: 'video'
};

const modelFile: FileInfo = {
  name: '3D模型.vrp',
  url: 'https://example.com/model.vrp',
  type: 'model3d'
};

const otherFile: FileInfo = {
  name: '文档.pdf',
  url: 'https://example.com/document.pdf',
  type: 'other'
};

// 事件处理函数
const handleImageLoad = (file: FileInfo) => {
  message.success('图片加载完成');
};

const handleVideoLoad = (file: FileInfo) => {
  message.success('视频加载完成');
};

const handleModelLoad = (file: FileInfo) => {
  message.success('3D模型加载完成');
};

const handleOtherLoad = (file: FileInfo) => {
  message.success('文件加载完成');
};

const handleError = (error: Error, file: FileInfo) => {
  message.error(`加载失败: ${error.message}`);
};

const handleDownload = (file: FileInfo) => {
  message.success('开始下载文件');
};

const handleEdit = (file: FileInfo) => {
  message.info('打开模型编辑器');
};

const handleFullscreen = (file: FileInfo) => {
  message.info('进入全屏预览模式');
};
</script>

<style lang="scss" scoped>
.tr-preview-example {
  padding: var(--spacing-6);
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    text-align: center;
    margin-bottom: var(--spacing-6);
    color: var(--gray-900);
  }
}

.example-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(420px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.example-item {
  h3 {
    margin-bottom: var(--spacing-3);
    color: var(--gray-700);
    font-size: var(--text-lg);
  }
}


</style>
