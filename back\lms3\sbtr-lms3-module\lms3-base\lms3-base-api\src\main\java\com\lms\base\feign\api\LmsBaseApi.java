package com.lms.base.feign.api;


import com.lms.base.feign.fallback.LmsBaseApiFallbackFactory;
import com.lms.base.feign.model.Courseware;
import com.lms.base.feign.model.Subject;
import com.lms.common.config.FeignConfig;
import com.lms.common.feign.dto.Department;
import com.lms.common.feign.dto.Person;
import com.lms.common.feign.dto.Selectitem;
import com.lms.common.model.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: quanjinghua
 * @Date: 2024/7/9 19:10
 * @Description: LMS-SYSTEM服务对外接口，对比较多服务使用到的接口，统一在common模块提供接口
 */
@FeignClient(contextId = "lmsBaseApi",value = "lms-base",configuration = FeignConfig.class,fallbackFactory = LmsBaseApiFallbackFactory.class )
public interface LmsBaseApi {

    @GetMapping("/person/getUserNamesBySysRoleLink")
    Result<List<String>> getUserNamesBySysRoleLink(@RequestParam("roleid") String roleid);

    @PostMapping(value = {"/getPersonByCardNum"})
    Result<Person> findPersonByCardNum(@RequestParam("cardNum") String cardNum);

    @GetMapping("/person/getRoleIdByUserName")
    Result<List<String>> getRoleIdByUserName(@RequestParam("userName") String userName);

    @GetMapping("/courseware/getCoursewareList")
    Result<List<Courseware>> getCoursewareList(@RequestParam String componentid);

    @GetMapping("/courseware/get/{coursewareid}")
    Result<Courseware> getCourseware(@PathVariable("coursewareid") String coursewareid);

    @GetMapping("/courseware/getCourseWareByCourseIdList")
    Result<List<Courseware>> getCourseWareByCourseIdList(@RequestParam List<String> idList);

    @GetMapping("/selectitem/existName")
    Result<Boolean> existName(@RequestParam String id, @RequestParam String objname, @RequestParam String typeid);

    @GetMapping("/selectitem/getSelectitemByTypeName")
    Result<Selectitem> getSelectitemByTypeName(@RequestParam String name, @RequestParam String typeid);

    @PostMapping(value = {"/selectitem/getByIdList"})
    Result<List<Selectitem>> getByIdList(@RequestBody List<String> idList);

    @RequestMapping(value = {"/selectitem/getSelectitemByIdWidthEmpty/{id}"}, method = RequestMethod.GET)
    Result<List<Selectitem>> getSelectitemByIdWidthEmpty(@PathVariable("id") String typeid);

    @GetMapping("/importExcel/readExcelFile")
    Result<Map> readExcelFile(@RequestParam("path") String path);

    @GetMapping("/subject/getSubjectById")
    Result<Subject> getSubjectById(@RequestParam("id") String id);

    @PostMapping("/subject/getRandomIds")
    List<String> getRandomIds(@RequestBody HashMap map);

    @PostMapping("/department/getDepartmentByIdList")
    Result<List<Department>> getDepartmentByIdList(@RequestBody List<String> idList);

    @PostMapping(value = {"/subject/calcSubjectKKD"})
    Result<String> calcSubjectKKD(@RequestBody List list);

}
