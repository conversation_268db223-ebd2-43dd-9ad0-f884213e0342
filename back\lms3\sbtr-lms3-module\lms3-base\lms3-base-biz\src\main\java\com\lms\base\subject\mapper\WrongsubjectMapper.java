package com.lms.base.subject.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.base.feign.model.Wrongsubject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface WrongsubjectMapper extends BaseMapper<Wrongsubject> {

	@Select("${sql}")
	Map getMaxSeqno(@Param("sql") String sql);
}
