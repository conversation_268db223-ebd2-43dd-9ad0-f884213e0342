package com.sbtr.workflow.utils;

import cn.hutool.core.convert.Convert;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;


/**
 * 文件相关工具类
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
public class FileUtil {

    /**
     * 获取文件内容
     *
     * @param multipartFile 文件
     * @return
     */
    public static String getFileContent(MultipartFile multipartFile) throws IOException {
        StringBuffer content = new StringBuffer();
        InputStream is = null;
        InputStreamReader inputStreamReader = null;
        BufferedReader bufferedReader = null;
        try {
            is = multipartFile.getInputStream();
            inputStreamReader = new InputStreamReader(is, StandardCharsets.UTF_8);
            bufferedReader = new BufferedReader(inputStreamReader);
            //开始读取进行append
            while (bufferedReader.ready()) {
                content.append(bufferedReader.readLine());
            }
        } catch (IOException e) {
        } finally {
            is.close();
            inputStreamReader.close();
            bufferedReader.close();
        }
        return Convert.toStr(content);
    }
}
