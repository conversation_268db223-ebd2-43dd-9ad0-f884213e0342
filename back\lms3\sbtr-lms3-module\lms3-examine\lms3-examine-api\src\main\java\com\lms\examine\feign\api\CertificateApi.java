package com.lms.examine.feign.api;

import com.lms.common.config.FeignConfig;
import com.lms.common.model.Result;
import com.lms.examine.feign.fallback.CertificateApiFallbackFactory;
import com.lms.examine.feign.model.Certificate;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "certificateApi",value = "lms-examine", path = "/certificate",configuration = FeignConfig.class,fallbackFactory = CertificateApiFallbackFactory.class)
public interface CertificateApi {

    @GetMapping("/getByPlanId")
    Result<List<Certificate>> getByPlanId(@RequestParam String planId);

}
