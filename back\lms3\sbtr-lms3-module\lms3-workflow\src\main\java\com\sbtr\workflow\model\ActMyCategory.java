package com.sbtr.workflow.model;

import cn.ewsd.common.model.MCoreBase;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;

/**
 * 流程分类表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-12 06:00:42
 */
@Table(name = "act_my_category")
public class ActMyCategory extends MCoreBase {

    /**
     * 分类唯一标识后续存放到流程
     */
    @NotBlank(message = "分类标识不能为空")
    private String categoryCode;

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空")
    private String categoryName;


    /**
     * 设置：分类唯一标识后续存放到流程
     */
    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    /**
     * 获取：分类唯一标识后续存放到流程
     */
    public String getCategoryCode() {
        return categoryCode;
    }

    /**
     * 设置：分类名称
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    /**
     * 获取：分类名称
     */
    public String getCategoryName() {
        return categoryName;
    }


}
