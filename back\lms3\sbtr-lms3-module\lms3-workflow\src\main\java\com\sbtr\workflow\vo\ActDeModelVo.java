package com.sbtr.workflow.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

/**
 * 流程模型查询实体类
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2019-02-19 14:20:45
 */
@Data
public class ActDeModelVo {

    /**
     * 版本模型Id
     */
    private String id;

    /**
     * 模型Id
     */
    private String modelId;

    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 模型key
     */
    private String modelKey;

    /**
     * 流程定义版本
     */
    private String modelVersion;

    /**
     * 模型创建人Id
     */
    private String createUserNameId;
    //模型创建时间
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    /**
     * 模型更新时间
     */
    private Date createTime;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date lastUpdated;
    //模型类型
    private String modelType;
    //流程定义Id
    private String procdefId;
    //是否主版本
    private String majorVersion;
    //部署Id
    private String deploymentId;
    //自定义表单还是
    private String processModelType;

    /**
     * 表单名称
     */
    private String formname;

}
