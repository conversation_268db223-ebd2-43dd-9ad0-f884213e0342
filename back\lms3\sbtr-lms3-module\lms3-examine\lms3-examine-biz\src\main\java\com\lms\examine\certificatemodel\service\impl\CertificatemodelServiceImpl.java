/**
 * FileName:	ClientCertificatemodel.java
 * Author:		ji<PERSON><PERSON><PERSON>
 * Time:		2013-3-4����11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.examine.certificatemodel.service.impl;

import com.lms.common.model.Result;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.examine.certificatemodel.service.CertificatemodelService;
import com.lms.system.feign.api.AttachApi;
import com.lms.examine.certificatemodel.mapper.CertificatemodelMapper;
import com.lms.examine.feign.model.Certificatemodel;
import com.lms.system.feign.model.Attach;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("CertificatemodelService")
public class CertificatemodelServiceImpl extends BaseServiceImpl<CertificatemodelMapper, Certificatemodel> implements CertificatemodelService {

	@Resource
	private CertificatemodelMapper certificatemodelMapper;

	@Resource
	private AttachApi attachApi;


	public List existName(Certificatemodel certificatemodel) {
		return  certificatemodelMapper.getByName(certificatemodel.getName(),certificatemodel.getId());
	}

	public Certificatemodel getAttachInfo(String id){
		if (StringUtils.isEmpty(id)){
			return null;
		}
		Certificatemodel certificatemodel = getById(id);
		if (StringUtils.isNotEmpty(certificatemodel.getBackgroundimg())){
			Result<Attach> bgResult = attachApi.get(certificatemodel.getBackgroundimg());
			if (null != bgResult && null != bgResult.getResult()){
				certificatemodel.setBgsrc(bgResult.getResult().getFiledir());
			}
		}
		if (StringUtils.isNotEmpty(certificatemodel.getSealimg())){
			Result<Attach> sealResult = attachApi.get(certificatemodel.getSealimg());
			if (null != sealResult && null != sealResult.getResult()){
				certificatemodel.setSealsrc(sealResult.getResult().getFiledir());
			}
		}
		return certificatemodel;
	}
}
