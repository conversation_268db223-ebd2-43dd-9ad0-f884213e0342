package com.sbtr.workflow.dto;

/**
 * 解析前端传过来的每个节点所对应的字段是否可编辑是否可以查看
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
public class FormFieldListDto {

    //字段code
    private String filed;
    //字段名
    private String filedName;
    //是否可以编辑
    private String isEdit;
    //节点Id
    private String id;
    //模型key
    private String modalKey;
    //是否可以编辑
    private String isLook;
    private String formUuid;

    //判断是自己写的表单还是通过拖动出来的表单  1自己写的表单  2拖动设计的
    private String modelType;
    private Integer fieldIndex;

    /**
     * 表单布局
     */
    private String formLayout;

    public String getFormLayout() {
        return formLayout;
    }

    public void setFormLayout(String formLayout) {
        this.formLayout = formLayout;
    }

    public Integer getFieldIndex() {
        return fieldIndex;
    }

    public void setFieldIndex(Integer fieldIndex) {
        this.fieldIndex = fieldIndex;
    }
    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }
    public String getFormUuid() {
        return formUuid;
    }

    public void setFormUuid(String formUuid) {
        this.formUuid = formUuid;
    }

    public String getFiled() {
        return filed;
    }

    public void setFiled(String filed) {
        this.filed = filed;
    }

    public String getFiledName() {
        return filedName;
    }

    public void setFiledName(String filedName) {
        this.filedName = filedName;
    }

    public String getIsEdit() {
        return isEdit;
    }

    public void setIsEdit(String isEdit) {
        this.isEdit = isEdit;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getModalKey() {
        return modalKey;
    }

    public void setModalKey(String modalKey) {
        this.modalKey = modalKey;
    }

    public String getIsLook() {
        return isLook;
    }

    public void setIsLook(String isLook) {
        this.isLook = isLook;
    }
}
