package com.sbtr.workflow.listener.globallistener;

import org.flowable.common.engine.api.delegate.event.FlowableEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEventListener;
import org.flowable.engine.delegate.event.impl.FlowableEntityEventImpl;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 任务完成事件监听
 *
 * <AUTHOR>
 * @Version 5.5.0
 * @Email <EMAIL>
 * @Date 2023-07-31 11:14:53
 */
@Component
public class GlobalTaskCompletedListener implements FlowableEventListener {

    private static final Logger log = LoggerFactory.getLogger(GlobalTaskCompletedListener.class);

    @Override
    public void onEvent(FlowableEvent flowableEvent) {
        log.info("进入任务完成事件监听------------------------Start---------------------->");
        TaskEntity taskEntity = (TaskEntity) ((FlowableEntityEventImpl) flowableEvent).getEntity();
        String taskId = taskEntity.getId();
        log.info("任务ID{}", taskId);
        log.info("任务名{}", taskEntity.getName());
        log.info("任务Assignee处理人{}", taskEntity.getAssignee());
        log.info("任务实例id{}", taskEntity.getProcessInstanceId());
        log.info(taskEntity.toString());
        log.info("任务完成事件监听------------------------End---------------------->");
    }

    @Override
    public boolean isFailOnException() {
        return false;
    }

    @Override
    public boolean isFireOnTransactionLifecycleEvent() {
        return false;
    }

    @Override
    public String getOnTransaction() {
        return null;
    }
}
