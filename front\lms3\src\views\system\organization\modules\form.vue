<script setup lang="ts">
// import AntDesignPlusOutlined from "~icons/ant-design/plus-outlined";
// import AntDesignEditOutlined from "~icons/ant-design/edit-outlined";
// import AntDesignDeleteOutlined from "~icons/ant-design/delete-outlined";
// import AntDesignFileTextOutlined from "~icons/ant-design/file-text-outlined";
// import AntDesignWarningOutlined from "~icons/ant-design/warning-outlined";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  WarningOutlined
} from "@ant-design/icons-vue";
import AntDesignFileTextOutlined from "~icons/ant-design/file-text-outlined";
import type { FormInstance } from "ant-design-vue";

import type { Organization, OrganizationForm } from "../data.ts";

import { computed, reactive, ref } from "vue";

import { message } from "ant-design-vue";
import {
  addDepartment,
  editDepartment,
  removeDepartment
} from "@/api/system.ts";

interface Props {
  currentOrganization: null | Organization;
  organizations: Organization[];
}

interface Emits {
  (e: "add", data: OrganizationForm): void;
  (e: "edit", id: string, data: OrganizationForm): void;
  (e: "delete", id: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const modalVisible = ref(false);
const deleteModalVisible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const isAddingRoot = ref(false);
const formRef = ref<FormInstance>();

const formData = reactive<OrganizationForm>({
  name: "",
  shortname: "",
  code: "",
  seqno: 1,
  pid: undefined
});

const rules = {
  name: [{ required: true, message: "请输入单位名称" }],
  shortname: [{ required: true, message: "请输入单位简称" }],
  code: [{ required: true, message: "请输入编码" }],
  seqno: [{ required: true, message: "请输入顺序号" }]
};

// 计算属性：组织树数据（用于上级单位选择）
const organizationTreeData = computed(() => {
  if (!props.organizations || !Array.isArray(props.organizations)) {
    return [];
  }

  const filterCurrentOrg = (orgs: Organization[]): Organization[] => {
    if (!orgs || !Array.isArray(orgs)) {
      return [];
    }

    return orgs
      .filter(org => {
        if (!org || typeof org !== "object") {
          return false;
        }

        // 编辑时过滤掉当前组织及其子组织
        if (isEdit.value && props.currentOrganization) {
          if (org.id === props.currentOrganization.id) {
            return false;
          }
          // 检查是否是当前组织的子组织
          if (isDescendant(org, props.currentOrganization.id)) {
            return false;
          }
        }
        return true;
      })
      .map(org => ({
        ...org,
        children:
          org.children && Array.isArray(org.children)
            ? filterCurrentOrg(org.children)
            : undefined
      }));
  };

  return filterCurrentOrg(props.organizations);
});

// 检查是否是子组织
const isDescendant = (org: Organization, ancestorId: string): boolean => {
  if (!org || !ancestorId) return false;

  if (org.pid === ancestorId) return true;

  const parent = findOrganizationById(props.organizations, org.pid || "");
  if (parent) {
    return isDescendant(parent, ancestorId);
  }
  return false;
};

// 查找组织
const findOrganizationById = (
  organizations: Organization[],
  id: string
): null | Organization => {
  if (!organizations || !Array.isArray(organizations) || !id) {
    return null;
  }

  for (const org of organizations) {
    if (!org || typeof org !== "object") {
      continue;
    }

    if (org.id === id) {
      return org;
    }
    if (org.children && Array.isArray(org.children)) {
      const found = findOrganizationById(org.children, id);
      if (found) return found;
    }
  }
  return null;
};

// 获取上级单位名称
const getParentName = (organization: Organization): string => {
  if (!organization || !organization.pid) return "无上级单位";

  const parent = findOrganizationById(props.organizations, organization.pid);
  return parent ? parent.name : "未知上级单位";
};

// 检查是否有子组织
const hasChildren = computed(() => {
  return (
    props.currentOrganization?.children &&
    Array.isArray(props.currentOrganization.children) &&
    props.currentOrganization.children.length > 0
  );
});

const resetForm = () => {
  Object.assign(formData, {
    name: "",
    shortname: "",
    code: "",
    seqno: 1,
    pid: undefined
  });
};

const handleAdd = () => {
  isEdit.value = false;
  isAddingRoot.value = false;
  resetForm();
  // 如果当前选中了组织，默认设置为其上级单位
  if (props.currentOrganization) {
    formData.pid = props.currentOrganization.id;
  }
  modalVisible.value = true;
};

const handleAddRoot = () => {
  isEdit.value = false;
  isAddingRoot.value = true;
  resetForm();
  // 根单位不设置上级单位
  formData.pid = undefined;
  modalVisible.value = true;
};

const handleEdit = () => {
  if (!props.currentOrganization) return;

  isEdit.value = true;
  isAddingRoot.value = false;
  Object.assign(formData, {
    id: props.currentOrganization.id,
    name: props.currentOrganization.name,
    shortname: props.currentOrganization.shortname,
    code: props.currentOrganization.code,
    seqno: props.currentOrganization.seqno,
    pid: props.currentOrganization.pid
  });
  modalVisible.value = true;
};

const handleDelete = () => {
  if (!props.currentOrganization) return;
  deleteModalVisible.value = true;
};

const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 如果是新增根单位，确保pid为undefined
    if (isAddingRoot.value) {
      formData.pid = undefined;
    }
    if (isEdit.value && props.currentOrganization) {
      let res = await editDepartment(formData);
      if (res.success) {
        emit("edit", props.currentOrganization!.id, res.result);
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    } else {
      let res = await addDepartment(formData);
      if (res.success) {
        emit("add", res.result);
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    }
    modalVisible.value = false;
    resetForm();
  } catch (error) {
    console.error("Form validation failed:", error);
  } finally {
    loading.value = false;
  }
};

const handleCancel = () => {
  modalVisible.value = false;
  isAddingRoot.value = false;
  resetForm();
  formRef.value?.clearValidate();
};

const confirmDelete = async () => {
  if (!props.currentOrganization) return;

  try {
    loading.value = true;
    let res = await removeDepartment(props.currentOrganization.id);
    if (res.success) {
      emit("delete", props.currentOrganization.id);
      message.success(res.message);
    } else {
      message.error(res.message);
    }
    deleteModalVisible.value = false;
  } catch {
    message.error("删除失败");
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <div class="organization-form">
    <div class="form-header">
      <!-- <h3 class="form-title">
        {{ currentOrganization ? "组织信息" : "选择组织" }}
      </h3> -->

      <div class="action-buttons">
        <a-button type="primary" class="action-btn" @click="handleAdd">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </a-button>
        <a-button
          :disabled="!currentOrganization"
          class="action-btn"
          @click="handleEdit"
        >
          <template #icon>
            <EditOutlined />
          </template>
          编辑
        </a-button>
        <a-button
          :disabled="!currentOrganization"
          danger
          class="action-btn"
          @click="handleDelete"
        >
          <template #icon>
            <DeleteOutlined />
          </template>
          删除
        </a-button>
      </div>
    </div>

    <div v-if="currentOrganization" class="organization-details">
      <div class="organization-info-container">
        <!--        <div class="organization-info-header">上级单位</div>-->

        <div class="organization-detail-form">
          <div class="form-row">
            <div class="form-label">上级单位</div>
            <div class="form-value">
              {{ getParentName(currentOrganization) }}
            </div>
          </div>

          <div class="form-row">
            <div class="form-label">单位简称</div>
            <div class="form-value">
              {{ currentOrganization.shortname }}
            </div>
          </div>

          <div class="form-row">
            <div class="form-label">单位名称</div>
            <div class="form-value">
              {{ currentOrganization.name }}
            </div>
          </div>

          <div class="form-row">
            <div class="form-label">编码</div>
            <div class="form-value">
              {{ currentOrganization.code }}
            </div>
          </div>

          <div class="form-row">
            <div class="form-label">顺序号</div>
            <div class="form-value">
              {{ currentOrganization.seqno }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="empty-state">
      <div class="empty-content">
        <!-- <ant-design-file-text-outlined class="empty-icon" /> -->
        <AntDesignFileTextOutlined class="empty-icon" />
        <p>请从左侧选择一个组织查看详情</p>
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑组织' : isAddingRoot ? '新增根单位' : '新增组织'"
      :confirm-loading="loading"
      width="600px"
      okText="提交"
      cancelText="取消"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="horizontal"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        class="modal-form"
      >
        <a-form-item v-if="!isAddingRoot" label="上级单位" name="pid">
          <a-tree-select
            v-model:value="formData.pid"
            :tree-data="organizationTreeData"
            :field-names="{ children: 'children', label: 'name', value: 'id' }"
            placeholder="请选择上级单位"
            allow-clear
            tree-default-expand-all
            :disabled="isEdit && currentOrganization?.id === formData.pid"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item v-if="isAddingRoot" label="上级单位">
          <a-input value="无上级单位" readonly class="readonly-input" />
        </a-form-item>

        <a-form-item label="单位简称" name="shortname">
          <a-input
            v-model:value="formData.shortname"
            placeholder="请输入单位简称"
          />
        </a-form-item>

        <a-form-item label="单位名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入单位名称" />
        </a-form-item>

        <a-form-item label="编码" name="code">
          <a-input v-model:value="formData.code" placeholder="请输入编码" />
        </a-form-item>

        <a-form-item label="顺序号" name="seqno">
          <a-input-number
            v-model:value="formData.seqno"
            :min="1"
            style="width: 100%"
            placeholder="请输入顺序号"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 删除确认弹窗 -->
    <a-modal
      v-model:open="deleteModalVisible"
      title="确认删除"
      :confirm-loading="loading"
      okText="确认"
      cancelText="取消"
      @ok="confirmDelete"
      @cancel="deleteModalVisible = false"
    >
      <p>确定要删除组织"{{ currentOrganization?.name }}"吗？</p>
      <p v-if="hasChildren" class="delete-warning">
        <WarningOutlined />
        注意：该组织下还有子组织，删除后子组织也将被删除！
      </p>
    </a-modal>
  </div>
</template>

<style lang="scss" scoped>
.organization-form {
  display: flex;
  flex-direction: column;
  padding: var(--spacing-6);
  background-color: white;
  height: 100%;
}

.form-header {
  margin-bottom: var(--spacing-6);
}

.form-title {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--gray-900);
  margin: 0 0 var(--spacing-4) 0;
}

.action-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: var(--spacing-3);
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
}

.organization-details {
  flex: 1;
}

.organization-info-container {
  // background-color: #eff7fd;
  padding: var(--spacing-6);
  border-radius: var(--border-radius-md);
  // border: 1px solid var(--gray-200);
}

.organization-info-header {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-600);
  margin-bottom: var(--spacing-4);
}

.organization-detail-form {
  display: flex;
  flex-direction: column;
}

.form-row {
  display: flex;
  border: 1px solid var(--gray-200);
  background-color: white;

  &:not(:last-child) {
    border-bottom: 0;
  }

  &:first-child {
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
  }

  &:last-child {
    border-bottom-left-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
  }
}

.form-label {
  width: 120px;
  min-width: 120px;
  padding: 12px 16px;
  background-color: #eff7fd;
  border-right: 1px solid var(--gray-200);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-value {
  flex: 1;
  padding: 0 20px;
  line-height: 3em;

  .readonly-input {
    :deep(.ant-input) {
      width: 100%;
      height: 100%;
      border: 0;
      box-shadow: none;
      background-color: transparent;
      padding: 12px 16px;

      &:focus {
        box-shadow: none;
        border: 0;
      }
    }
  }
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 256px;
  color: var(--gray-500);
}

.empty-content {
  text-align: center;

  .empty-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto var(--spacing-4) auto;
    color: var(--gray-300);
  }

  p {
    margin: 0;
  }
}

.modal-form {
  margin-top: var(--spacing-4);
}

.delete-warning {
  color: var(--error-color);
  margin-top: var(--spacing-2);
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--spacing-1);

  .warning-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }
}

// 表单样式
:deep(.ant-form-item) {
  margin-bottom: var(--spacing-4);
}

:deep(.ant-form-item-label) {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
}

:deep(.ant-input),
:deep(.ant-input-number),
:deep(.ant-tree-select) {
  width: 100%;
  height: 100%;
}
:deep(.ant-input) {
  border-radius: 0;
  // border: 0;
}

:deep(.ant-tree-select-dropdown) {
  max-height: 256px;
}

// 按钮样式
:deep(.ant-btn) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn .anticon) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn-icon) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn > span) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
}

// 弹窗中的输入框样式
:deep(.ant-modal .ant-input),
:deep(.ant-modal .ant-input-number),
:deep(.ant-modal .ant-tree-select) {
  width: 100%;
}
</style>
