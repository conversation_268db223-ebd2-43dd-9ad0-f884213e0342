package com.lms.system.languagelabel.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.alibaba.fastjson.JSONObject;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.StringHelper;
import com.lms.system.feign.model.Languagelabel;
import com.lms.system.languagelabel.service.LanguagelabelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/languagelabel")
@Api(value = "语言标签管理", tags = "语言标签管理")
public class LanguagelabelController extends BaseController<Languagelabel> {
    protected static final Logger logger = LoggerFactory
            .getLogger(LanguagelabelController.class);

    @Resource
    private LanguagelabelService languagelabelService;

    @LMSLog(desc = "查询多语言标签", otype = LogType.List, order = 1, method = "setLanguagelabelLog")
    @PostMapping(value = {"/listpage"})
    @ApiOperation(value = "查询多语言标签", httpMethod = "POST")
    public Result getLanguagelabelList(@RequestBody JSONObject request) {
        PageInfo pageInfo = super.getPageInfo(request);
        Page<Languagelabel> languagelabelList = languagelabelService.listByCondition(pageInfo);
        return Result.OK(languagelabelList);
    }

    @RequestMapping(value = {"/listAll"}, method = RequestMethod.GET)
    @ApiOperation(value = "查询全部多语言标签", httpMethod = "GET")
    public Result getAllLanguagelabel() {
        List<Languagelabel> languagelabelList = this.languagelabelService.list();
        JSONObject jo = new JSONObject();
        for (Languagelabel languagelabel : languagelabelList) {
            jo.put(languagelabel.getKeyword(), languagelabel.getContent());
        }
        return Result.OK(jo);
    }

    @LMSLog(desc = "新增多语言标签", otype = LogType.Save, order = 1, method = "setLanguagelabelLog")
    @RequestMapping(value = {"/add"}, method = RequestMethod.POST)
    @ApiOperation(value = "新增多语言标签", httpMethod = "POST")
    public Result saveLanguagelabel(@RequestBody Languagelabel languagelabel) {
        this.languagelabelService.saveOrUpdate(languagelabel);
        return Result.OK(languagelabel);
    }

    @RequestMapping(value = {"/translate"}, method = RequestMethod.GET)
    @ApiOperation(value = "获取翻译内容", httpMethod = "GET")
    public Result translate(HttpServletRequest request) {
        String keyword = StringHelper.null2String(request.getParameter("keyword"));
        String module = StringHelper.null2String(request.getParameter("module"));
        String[] variables = StringHelper.null2String(request.getParameter("variables")).split(",");//eg: a || a,b,c
        String content = languagelabelService.translateContent(keyword, module, variables);
        return Result.OK(content);
    }

    @GetMapping(value = {"/translatedContent"})
    @ApiOperation(value = "获取翻译内容", httpMethod = "GET")
    public String translatedContent(@RequestParam("keyword") String keyword, @RequestParam("module") String module, @RequestParam("variables") String variables) {
        String content = languagelabelService.translateContent(keyword, module, variables);
        return content;
    }

    @GetMapping(value = {"/translatedContent2"})
    @ApiOperation(value = "获取翻译内容", httpMethod = "GET")
    public String translateContent(String keyword, String module, String[] variables) {
        String content = languagelabelService.translateContent(keyword, module, variables);
        return content;
    }

    @GetMapping(value = {"/translatedByKeyword"})
    @ApiOperation(value = "根据关键词获取翻译内容", httpMethod = "GET")
    public String translatedContent(@RequestParam("keyword") String keyword) {
        String content = languagelabelService.translateContent(keyword);
        return content;
    }

    @GetMapping(value = {"/translateContentNotCreated"})
    @ApiOperation(value = "根据关键词获取翻译内容", httpMethod = "GET")
    public String translateContentNotCreated(@RequestParam("keyword") String keyword, @RequestParam("module") String module) {
        String content = languagelabelService.translateContentNotCreated(keyword, module);
        return content;
    }

    @LMSLog(desc = "编辑多语言标签", otype = LogType.Update, order = 1, method = "setLanguagelabelLog")
    @RequestMapping(value = {"/modify"}, method = RequestMethod.POST)
    @ApiOperation(value = "编辑多语言标签", httpMethod = "POST")
    public Result updateLanguagelabel(@RequestBody Languagelabel languagelabel) {
        languagelabelService.saveOrUpdate(languagelabel);
        return Result.OK(languagelabel);

    }

    @LMSLog(desc = "删除多语言标签", otype = LogType.Delete, order = 1, method = "setLanguagelabelLog")
    @RequestMapping(value = {"/delete"}, method = RequestMethod.DELETE)
    @ApiOperation(value = "删除多语言标签", httpMethod = "DELETE")
    public Result batchDeleteLanguagelabel(@RequestParam("ids") String ids) {
        String[] idList = ids.split(",");
        for (String id : idList) {
            this.languagelabelService.removeById(id);
        }
        return Result.OK();
    }

    public String setLanguagelabelLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.List)) {
            objname = "多语言标签列表";
        } else if (lmslog.otype().equals(LogType.Save)) {
            Languagelabel obj = (Languagelabel) (args[0]);
            objname = StringHelper.null2String(obj.getContent());
        } else if (lmslog.otype().equals(LogType.Update)) {
            Languagelabel obj = (Languagelabel) (args[0]);
            objname = StringHelper.null2String(obj.getContent());
        } else if (lmslog.otype().equals(LogType.Delete)) {
            String ids = (String) (args[0]);
            String[] idarray = ids.split(",");
            for (String id : idarray) {
                Languagelabel obj = languagelabelService.getById(id);
                objname = objname.isEmpty() ? StringHelper.null2String(obj.getContent()) : objname + "," + StringHelper.null2String(obj.getContent());
            }
        }
        return objname;
    }
}
