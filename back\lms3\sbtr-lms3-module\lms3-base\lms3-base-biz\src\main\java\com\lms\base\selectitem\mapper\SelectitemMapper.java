package com.lms.base.selectitem.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.base.feign.model.Selectitem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 下拉框选项数据库操作接口
 *
 * <AUTHOR>
@Mapper
public interface SelectitemMapper extends BaseMapper<Selectitem> {


    /**
     * 根据下拉框ID获取选项列表。
     *
     * @param typeid 下拉框ID
     * @return 选项列表
     */
    @Select(value = "select * from b_selectitem where status=1 and if(#{typeid}='',1=1,typeid=#{typeid}) order by seqno asc,objname asc")
    List<Selectitem> getSelectitemListByTypeid(String typeid);

    @Select(value = "select * from b_selectitem where if(#{typeid}='',1=1,typeid=#{typeid}) order by seqno asc,objname asc")
    List<Selectitem> getAllSelectitemsByType(String typeid);

    @Select(value = "select * from b_selectitem where status=1 and objname = #{objname} and typeid=#{typeid} limit 1")
    Selectitem getSelectitemByTypeName(@Param("objname") String objname, @Param("typeid") String typeid);

}
