server:
  # 项目端口
  port: 8998
spring:
  application:
    name: lms-base
  profiles:
    # 当前激活环境
    active: dev
  main:
    allow-bean-definition-overriding: true #当遇到同样名字的时候，是否允许覆盖注册
    allow-circular-references: true
  web:
    resources:
      static-locations: file:./static/,classpath:/static/,classpath:/templates/
  cloud:
    #配置Bus id(远程推送事件)
    #    bus:
    #      id: ${spring.application.name}:${server.port}
    nacos:
      config:
        # Nacos 认证用户
        username: nacos
        # Nacos 认证密码
        password: nacos
        # 命名空间 常用场景之一是不同环境的配置的区分隔离，例如开发测试环境和生产环境的资源（如配置、服务）隔离等
        namespace: bf1899c3-676d-43bc-8272-3435124e0b37
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        # 配置对应的分组
        group: DEFAULT_GROUP
        # 配置文件后缀
        file-extension: yaml
        shared-configs[0]:
          data-id: lms-common.yaml # 配置文件名-Data Id
          #group: @config.group@   # 默认为DEFAULT_GROUP
          #refresh: false   # 是否动态刷新，默认为false
      discovery:
        namespace: bf1899c3-676d-43bc-8272-3435124e0b37
        server-addr: 127.0.0.1:8848
        # Nacos 认证用户
        username: nacos
        # Nacos 认证密码
        password: nacos
        group: DEFAULT_GROUP
        watch: #nacos类似长连接推送服务变化的功能
          enabled: true
        watch-delay: 15000
#ribbon的超时时间
#ribbon:
#  ReadTimeout: 30000
#  ConnectTimeout: 30000
#  eureka:
#    enabled: true # 开启eureka负载均衡策略

#  datasource:
#    type: com.alibaba.druid.pool.DruidDataSource
#    url: **********************************************************************************************************************************************************************
#    username: ceprei_cloud_dev
#    password: Isuperone#123
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    # dm.jdbc.driver.DmDriver
#    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭
#    # seata: true
#    druid:
#      # 连接池的配置信息
#      # 初始化大小，最小，最大
#      initial-size: 5
#      min-idle: 5
#      maxActive: 20
#      # 配置获取连接等待超时的时间
#      maxWait: 60000
#      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
#      timeBetweenEvictionRunsMillis: 60000
#      # 配置一个连接在池中最小生存的时间，单位是毫秒
#      minEvictableIdleTimeMillis: 300000
#      validationQuery: SELECT 1
#      testWhileIdle: true
#      testOnBorrow: false
#      testOnReturn: false
#      # 打开PSCache，并且指定每个连接上PSCache的大小
#      poolPreparedStatements: true
#      maxPoolPreparedStatementPerConnectionSize: 20
#      stat-view-servlet:
#        enabled: true
#        allow:
#        loginUsername: admin
#        loginPassword: 123456
#  redis:
#    database: 0
#    host: 127.0.0.1
#    lettuce:
#      pool:
#        max-active: 8   #最大连接数据库连接数,设 -1 为没有限制
#        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
#        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
#        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
#      shutdown-timeout: 100ms
#    password:
#    port: 6379
#
#
#  mvc:
#    pathmatch:
#      matching-strategy: ant_path_matcher
#  jpa:
#    show-sql: true
#    properties:
#      hibernate:
#        format_sql: false
#
#jackson:
#  serialization:
#    fail-on-empty-beans: false
## #actuator健康检查配置
## management:
##   security:
##     enabled: false
##   endpoints:
##     web:
##       exposure:
##         #加载所有的端点，默认只加载了info、health
##         include: '*'
##   endpoint:
##     health:
##       show-details: always
##     #可以关闭指定的端点
##     shutdown:
##       enabled: false
#feign:
#  sentinel:
#    enabled: true
#hystrix:
#  command:
#    default:
#      execution:
#        isolation:
#          strategy: SEMAPHORE
#  data:
#    redis:
#      repositories:
#        enabled: false
#jwt:
#  audience:
#    clientId: 098f6bcd4621d373cade4e832627b4f6
#    base64Secret: MDk4ZjZiY2Q0NjIxZDM3M2NhZGU0ZTgzMjYyN2I0ZjY=
#    name: systemJwt
#    expiresSecond: 172800
#logging:
#  level:
#    org.hibernate.SQL: DEBUG
#    # root: DEBUG
#    org.hibernate.type.descriptor.sql.BasicBinder: trace