<template>
  <a-select
    :value="modelValue"
    :placeholder="placeholder"
    :allow-clear="allowClear"
    :style="{ width: '100%' }"
    @change="handleChange"
  >
    <a-select-option value="男">
      <div class="gender-option">
        <span class="gender-icon male">👨</span>
        <span class="gender-text">男</span>
      </div>
    </a-select-option>
    <a-select-option value="女">
      <div class="gender-option">
        <span class="gender-icon female">👩</span>
        <span class="gender-text">女</span>
      </div>
    </a-select-option>
  </a-select>
</template>

<script setup lang="ts">
interface Props {
  modelValue?: string;
  placeholder?: string;
  allowClear?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: "请选择性别",
  allowClear: true
});

const emit = defineEmits<{
  "update:modelValue": [value: string];
  change: [value: string];
}>();

const handleChange = (value: string) => {
  emit("update:modelValue", value);
  emit("change", value);
};
</script>

<style scoped>
.gender-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.gender-icon {
  font-size: 16px;
}

.gender-icon.male {
  color: #1890ff;
}

.gender-icon.female {
  color: #eb2f96;
}

.gender-text {
  font-size: 14px;
  color: #333;
}
</style>
