package com.lms.base.operationanalysis.model;

import com.influxdb.annotations.Column;
import com.influxdb.annotations.Measurement;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.Instant;

@Data
@Measurement(name = "u_operation_analysis")
@ApiModel(value = "OperationAnalysis", description = "操作结果分析表实体")
public class OperationAnalysis {
    @ApiModelProperty(value = "唯一标识")
    @Column(tag = true)
    private String id;

    @ApiModelProperty(value = "时间戳")
    @Column(name = "_time", timestamp = true)
    private Instant timestamp;

    @ApiModelProperty(value = "用户ID")
    @Column(tag = true)
    private String userId;

    @ApiModelProperty(value = "课程ID")
    @Column(tag = true)
    private String courseId;

    @ApiModelProperty(value = "课件ID")
    @Column(tag = true)
    private String sceneId;

    @ApiModelProperty(value = "开始时间")
    @Column
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    @Column
    private String endTime;

    @ApiModelProperty(value = "总时长(秒)")
    @Column
    private Double totalDuration;

    @ApiModelProperty(value = "完成率")
    @Column
    private Double completionRate;

    @ApiModelProperty(value = "准确率")
    @Column
    private Double accuracyRate;

    @ApiModelProperty(value = "平均速度")
    @Column
    private Double averageSpeed;

    @ApiModelProperty(value = "错误次数")
    @Column
    private Integer errorCount;

    @ApiModelProperty(value = "动作流畅度")
    @Column
    private Double motionSmoothness;

    @ApiModelProperty(value = "注意力得分")
    @Column
    private Double attentionScore;

    @ApiModelProperty(value = "疲劳度")
    @Column
    private Double fatigueLevel;

} 