/**
 * FileName:SelectitemController.java
 * Author:<PERSON><PERSON><PERSON><PERSON>
 * Time  :2013-3-7下午4:18:10
 * CopyRight: SBTR LTD.
 * decription:to-do
 */
package com.lms.base.selectitem.controller;

import com.alibaba.fastjson.JSONObject;
import com.lms.base.feign.model.Selectitem;
import com.lms.base.selectitem.service.SelectitemService;
import com.lms.base.selectitem.service.SelectitemTypeService;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.StringHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;


/**
 * <AUTHOR> 选择项信息操作控制层
 */
@RestController
@RequestMapping("/selectitem")
@Api(value = "数据字典管理", tags = "数据字典管理")
public class SelectitemController extends BaseController<Selectitem> {
    @Resource
    private SelectitemService selectitemService;
    @Resource
    private SelectitemTypeService selectitemTypeService;
    /*
     * 获取所有有效选择项子项(分页)
     */
    @PostMapping(value = {"/listpage"})
    @ApiOperation(value = "获取所有有效数据字典信息", httpMethod = "POST")
    public Result<Page<Selectitem>> getSelectitems(@RequestBody JSONObject request) throws URISyntaxException {
        PageInfo pageInfo = super.getPageInfo(request);
        LinkedHashMap orderMap = new LinkedHashMap();
        if (pageInfo.getOrderName() != null && pageInfo.getSort() != null) {
            orderMap.put(pageInfo.getOrderName(), pageInfo.getSort());
        }
        orderMap.put("seqno", pageInfo.ASC);
        pageInfo.setOrderMap(orderMap);
        Page<Selectitem> selectitems = this.selectitemService.listByCondition(pageInfo);
        for (Selectitem selectitem : selectitems.getRecords()) {
            String typeid = StringHelper.null2String(selectitem.getTypeid());
            String parentname = StringHelper
                    .null2String(this.selectitemTypeService.getById(typeid)
                            .getObjname());

            if (!parentname.isEmpty()) {
                selectitem.setParentname(parentname);
            }
        }
        return Result.OK(selectitems);
    }

    /*
     * 获取所有数据字典信息
     */
    @RequestMapping(value = {"/list"}, method = RequestMethod.GET)
    @ApiOperation(value = "获取所有数据字典信息", httpMethod = "GET")
    public Result<List<Selectitem>> getValidSelectitem() {
        List<Selectitem> selectitems = this.selectitemService.list();
        // List<Selectitem> selectitems = this.selectitemService.findAll();
        return Result.OK(selectitems);
    }

    /*
     * 根据id获取数据字典信息
     */
    @GetMapping(value = {"/getSelectitemById/{id}"})
    @ApiOperation(value = "根据type id获取数据字典信息", httpMethod = "GET")
    public Result getSelectitemsByType(@ApiParam(value = "数据字典类型id") @PathVariable("id") String id) {
        List<Selectitem> selectitems = this.selectitemService.getSelectitemsByType(id);
        return Result.OK(selectitems);
    }

    @GetMapping(value = {"/getById"})
    @ApiOperation(value = "根据id获取数据字典信息", httpMethod = "GET")
    public Result getById(@ApiParam(value = "数据字典id") @RequestParam("id") String id) {
        Selectitem selectitem = this.selectitemService.getById(id);
        return Result.OK(selectitem);
    }

    @PostMapping(value = {"/getByIdList"})
    @ApiOperation(value = "根据id列表获取数据字典信息", httpMethod = "GET")
    public Result<List<Selectitem>> getByIdList(@RequestBody List<String> idList) {
        return Result.OK(selectitemService.listIds(idList));
    }

    @RequestMapping(value = {"/getSelectitemByIdWidthEmpty/{id}"}, method = RequestMethod.GET)
    @ApiOperation(value = "根据type id获取所有数据字典信息", httpMethod = "GET")
    public Result<List<Selectitem>> getSelectitemByIdWidthEmpty(@PathVariable("id") String typeid) {
        List<Selectitem> selectitems = this.selectitemService.getAllSelectitemsByType(typeid);
        return Result.OK(selectitems);
    }

    @RequestMapping(value = {"/getSelectitemByIdForDisplay/{id}"}, method = RequestMethod.GET)
    @ApiOperation(value = "根据type id获取所有数据字典信息", httpMethod = "GET")
    public Result getSelectitemByIdForDisplay(@PathVariable("id") String typeid) {
        List<Selectitem> selectitems = this.selectitemService.getAllSelectitemsByType(typeid);
        return Result.OK(selectitems);
    }

    // 更新Action
    @LMSLog(desc = "编辑选择项子项信息", otype = LogType.Update, order = 2, method =
            "createSelectitemLog")
    @RequestMapping(value = {"/modify"}, method = RequestMethod.POST)
    @ApiOperation(value = "编辑数据字典信息", httpMethod = "POST")
    public Result updateSelectitem(@RequestBody Selectitem selectitem) {
        if (this.selectitemService.existName(selectitem.getId(),
                selectitem.getObjname(), selectitem.getTypeid())) {
            return Result.error("该选择项名称已存在！");
        }
        selectitemService.saveOrUpdate(selectitem);
        return Result.OK(selectitem);
    }


    // 删除
    @LMSLog(desc = "删除选择项子项", otype = LogType.Delete, method = "createSelectitemLog", order = 2)
    @RequestMapping(value = {"/del/{id}"}, method = RequestMethod.DELETE)
    @ApiOperation(value = "删除数据字典信息", httpMethod = "DELETE")
    public Result deleteSelectitem(@PathVariable("id") String id) {
        Selectitem selectitem = selectitemService.getById(id);
        this.selectitemService.removeById(selectitem);
        return Result.OK();
    }

    // 人员新增的处理
    @LMSLog(desc = "添加选择项子项", otype = LogType.Save, order = 2, method =
            "createSelectitemLog")
    @RequestMapping(value = {"/add"}, method = RequestMethod.POST)
    @ApiOperation(value = "添加数据字典信息", httpMethod = "POST")
    public Result saveSelectitem(@RequestBody Selectitem selectitem) {

        if (this.selectitemService.existName(selectitem.getId(),
                selectitem.getObjname(), selectitem.getTypeid())) {
            return Result.error("该选择项名称已存在！");
        }
        selectitem.setStatus(1);
        this.selectitemService.saveOrUpdate(selectitem);
        return Result.OK(selectitem);
    }


    // 批量删除
    @LMSLog(desc = "删除选择项子项", otype = LogType.Delete, order = 2, method =
            "createSelectitemLog")
    @RequestMapping(value = {"/batchremove"}, method = RequestMethod.DELETE)
    @ApiOperation(value = "批量删除数据字典信息", httpMethod = "DELETE")
    public Result batchDeleteSelectitem(@RequestParam("ids") String ids) {
        String[] idarray = ids.split(",");
        List<String> idList = new ArrayList<String>();
        for (int i = 0; i < idarray.length; i++) {
            String id = idarray[i];
            idList.add(id);
        }
        this.selectitemService.removeBatchByIds(idList);
        return Result.OK();
    }

    public String createSelectitemLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.Save)) { //新增数据日志
            Selectitem p = (Selectitem) (args[0]);
            objname = StringHelper.null2String(p.getObjname());
        } else if (lmslog.otype().equals(LogType.Update)) { //编辑数据日志
                Selectitem p = (Selectitem) (args[0]);
                objname = StringHelper.null2String(p.getObjname());
        } else if (lmslog.otype().equals(LogType.Delete)) { //删除数据日志
            String ids = (String) (args[0]);
            String[] idarray = ids.split(",");
            for (String id : idarray) {
                Selectitem p = selectitemService.getById(id);
                objname = objname.isEmpty() ? StringHelper.null2String(p.getObjname()) : objname + "," + StringHelper.null2String(p.getObjname());
            }
        }
        return objname;
    }

    @GetMapping("/existName")
    @ApiOperation(value = "根据名称与typeid判断数据字典信息是否存在", httpMethod = "GET")
    public Result existName(String id, String objname, String typeid) {
        return Result.OK(selectitemService.existName(id, objname, typeid));
    }

    @GetMapping("/getSelectitemByTypeName")
    @ApiOperation(value = "根据名称与typeid获取数据字典信息", httpMethod = "GET")
    public Result getSelectitemByTypeName(@RequestParam String name, @RequestParam String typeid) {
        return Result.OK(selectitemService.getSelectitemByTypeName(name, typeid));
    }

}
