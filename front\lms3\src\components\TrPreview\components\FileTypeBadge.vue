<template>
  <div
    class="file-type-badge"
    :style="{ backgroundColor: config.badgeColor }"
  >
    <component :is="iconComponent" />
    {{ config.displayName }}
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  PictureOutlined,
  PlayCircleOutlined,
  AppstoreOutlined,
  FileOutlined
} from '@ant-design/icons-vue';
import type { FileType, FileTypeConfig } from '../types';

interface Props {
  type: FileType;
  config: FileTypeConfig;
}

const props = defineProps<Props>();

// 图标组件映射
const iconMap = {
  PictureOutlined,
  PlayCircleOutlined,
  AppstoreOutlined,
  FileOutlined
};

const iconComponent = computed(() => {
  return iconMap[props.config.icon as keyof typeof iconMap] || FileOutlined;
});
</script>

<style lang="scss" scoped>
.file-type-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: white;
}
</style>
