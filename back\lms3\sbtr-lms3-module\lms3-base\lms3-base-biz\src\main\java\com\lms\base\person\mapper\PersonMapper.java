package com.lms.base.person.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.base.feign.model.Person;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.repository.query.Param;


/**
 * <AUTHOR>
 * 人员信息存储层接口
 */
@Mapper
public interface PersonMapper extends BaseMapper<Person> {

    @Select("${sql}")
    List getTeacherAnalysis(@Param("sql") String sql);
}
