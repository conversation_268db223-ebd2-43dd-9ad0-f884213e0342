package com.sbtr.workflow.mapper;

import com.sbtr.workflow.model.SysUser;
import com.sbtr.workflow.bean.WorkflowMapper;
import com.sbtr.workflow.model.SysUser;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 用户 Mapper
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-08-01 14:55:46
 */
public interface SysUserMapper extends WorkflowMapper<SysUser> {

    List<SysUser> getPageSet(@Param("filterSort") String filterSort, @Param("ids") String ids);

    Integer updateByUuidAndIsDel(String uuid);


    SysUser getListByUserNameIdAndUuid(@Param("userNameId") String userNameId, @Param("uuid") String uuid);

    String getDataAuth(String currentUserNameId);


    List<SysUser> getLimitedListByQ(String q);


    SysUser getUserByOpenid(@Param("openid") String openid);

    Integer updateCellphone(@Param("openid") String openid, @Param("cellphone") String cellphone);

    List<SysUser> getDataByTablesAndField(@Param("tables") String tables, @Param("field") String field);

    List<Map<String, Object>> getUserList();

    List<SysUser> getUserByUserNameIds(@Param("userNameIds") String[] userNameIds);

    List<SysUser> selectAllListByUuids(@Param("uuids") String[] uuids);


    int sysUserInsertData(@Param("userList") List<SysUser> userList);

    List<SysUser> selectByUserNameIdIsExist(@Param("userNameId") String[] userNameId);

    String getUserNameIdByRoleId(@Param("roleId") String roleId);

    String getUserNameIdByPost(@Param("post") String post);

    String selectUserNameByuuid(@Param("uuid") String uuid);

    int updateUserByQyweixinUser(@Param("list") List<SysUser> list);

    List<SysUser> getPageSetUserByRoleId(@Param("filterSort") String filterSort, @Param("roleId") String roleId, @Param("userName") String userName);

    List<SysUser> getPageSetOracle(@Param("filterSort") String filterSort, @Param("ids") String ids);

    List<SysUser> getPageSetUserByRoleIdOracle(@Param("filterSort") String filterSort,
                                               @Param("roleId") String roleId,
                                               @Param("userName") String userName);

    List<SysUser> getPageSetUserByRoleIdSqlServer(@Param("filterSort") String filterSort,
                                                  @Param("roleId") String roleId,
                                                  @Param("userName") String userName);

    List<SysUser> getPageSetSqlServer(@Param("filterSort") String filterSort, @Param("ids") String ids);

    List<String> getEngineerName();

    String getUserPosition(String userNameId);
}
