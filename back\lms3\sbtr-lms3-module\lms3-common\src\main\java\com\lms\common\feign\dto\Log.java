package com.lms.common.feign.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode
public class Log implements Serializable {

    private String id;

    private String objname;

    private String description;

    private String logtype;

    private String submitdate;

    private String submittime;

    private String submitip;

    private String submitor;

    private String result;

    private String logobj;

    private String role;

    private Integer usertype;

    public Log(String objname, String description, String logtype, String result) {
        this.objname = objname;
        this.description = description;
        this.logtype = logtype;
        this.result = result;
    }
}
