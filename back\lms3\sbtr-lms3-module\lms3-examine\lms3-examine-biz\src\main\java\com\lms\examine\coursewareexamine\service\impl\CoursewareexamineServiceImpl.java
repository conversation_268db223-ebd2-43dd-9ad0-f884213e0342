
/**
 * FileName:	ClientCoursewareexamine.java
 * Author:		ji<PERSON><PERSON><PERSON>
 * Time:		2013-3-4锟斤拷锟斤拷11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.examine.coursewareexamine.service.impl;

import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.examine.coursewareexamine.mapper.CoursewareexamineMapper;
import com.lms.examine.coursewareexamine.service.CoursewareexamineService;
import com.lms.examine.feign.model.Coursewareexamine;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CoursewareexamineServiceImpl extends BaseServiceImpl<CoursewareexamineMapper, Coursewareexamine> implements CoursewareexamineService {

	@Resource
	private CoursewareexamineMapper coursewareexamineMapper;

	public List<Coursewareexamine> getByCourseware(String coursewareid) {
		return this.coursewareexamineMapper.getByCourseware(coursewareid);
	}

	public int getMaxExamSecondByCourseware(Coursewareexamine coursewareexamine) {
		return this.coursewareexamineMapper.getMaxExamSecondByCourseware(coursewareexamine);
	}

	public int existSeqno(Coursewareexamine coursewareexamine) {
		return this.coursewareexamineMapper.existSeqno(coursewareexamine);
	}

	public int getMinExamSecondByCourseware(Coursewareexamine coursewareexamine) {
		return this.coursewareexamineMapper.getMinExamSecondByCourseware(coursewareexamine);
	}


}
