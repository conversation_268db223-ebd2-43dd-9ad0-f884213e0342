package com.lms.common.feign.dto;

import com.lms.common.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * Role entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Role extends BaseModel {

    private String id;

    private String name;

    private String description;

    private Integer rolekind;// 角色类型,见{@link RoleTypeEnum}

    private List<Users> users;


}
