package com.sbtr.workflow.client;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @MethodName
 * @Description   调用sbtr-hr相关接口
 * @Description   本地开发时可以指定 ip+网关端口进行访问  在configuration中配置 url = "http://*************:9774/hr"
 * @Param null
 * @Return
 * <AUTHOR>
 * @Date 2020-11-23 14:37
 */
//@FeignClient(name = "hr", configuration = FeignConfig.class)
public interface HrClient {

    //流程审批之后进行人员调动接口
    @RequestMapping(value = "/hrEmployeesMobilize/transfer", method = RequestMethod.POST)
    Object transfer(@RequestParam("businessId") String businessId, @RequestParam("state") String state);

    //流程审批之后进行请假管理接口
    @RequestMapping(value = "/hrLeave/leaveTransfer", method = RequestMethod.POST)
    Object leaveTransfer(@RequestParam("businessId") String businessId, @RequestParam("processStatus") String processStatus);

    //流程审批之后进行请假管理接口
    @RequestMapping(value = "/hrLeave/userLeaveTransfer", method = RequestMethod.POST)
    List<String> userLeaveTransfer(@RequestParam("businessId") String businessId, @RequestParam("uesrtSate") String uesrtSate);

    //流程审批之后进行出差登记接口
    @RequestMapping(value = "/hrOnBusiness/onBusinessTransfer", method = RequestMethod.POST)
    Object onBusinessTransfer(@RequestParam("businessId") String businessId, @RequestParam("processStatus") String processStatus);

    //流程审批之后进行出差登记接口
    @RequestMapping(value = "/hrOnBusiness/userOnBusinessTransfer", method = RequestMethod.POST)
    List<String> userOnBusinessTransfer(@RequestParam("businessId") String businessId, @RequestParam("uesrtSate") String uesrtSate);

    //流程审批之后更新报名状态
    @RequestMapping(value = "/hrTrainingApplication/apply", method = RequestMethod.POST)
    Object apply(@RequestParam("businessId") String businessId, @RequestParam("state") String state);

    //报名获取部门负责人
    @RequestMapping(value = "/hrTrainingApplication/getDepartmentHead", method = RequestMethod.POST)
    Object getDepartmentHead(@RequestParam("businessId") String businessId);

    //流程审批之后进行员工调动接口
    @RequestMapping(value = "/hrEmployeesMobilize/getBeforeDepartmentHead", method = RequestMethod.POST)
    Object employeesMobilizeTransfer(@RequestParam("businessId") String businessId,@RequestParam("leaderType") String leaderType);

}
