/**
 * FileName:SelectitemController.java
 * Author:<PERSON><PERSON><PERSON><PERSON>
 * Time  :2013-3-7下午4:18:10
 * CopyRight: SBTR LTD.
 * decription:to-do
 */
package com.lms.base.selectitem.controller;

import com.alibaba.fastjson.JSONObject;
import com.lms.base.feign.model.SelectitemType;
import com.lms.base.selectitem.service.SelectitemTypeService;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.StringHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR> 选择项信息操作控制层
 */
@RestController
@RequestMapping("/selectitemtype")
@Api(value = "数据字典类型管理", tags = "数据字典类型管理")
public class SelectitemTypeController extends BaseController<SelectitemType> {
    @Resource
    private SelectitemTypeService selectitemTypeService;

    /*
     * 获取所有有效选择项(分页)
     */
    @PostMapping(value = {"/listpage"})
    @ApiOperation(value = "获取数据字典类型信息", httpMethod = "POST")
    public Result getAllSelectitemType( @RequestBody JSONObject request) {
        PageInfo pageInfo = super.getPageInfo(request);
        LinkedHashMap orderMap = new LinkedHashMap();
        if (pageInfo.getOrderName() != null && pageInfo.getSort() != null) {
            orderMap.put(pageInfo.getOrderName(), pageInfo.getSort());
        }
        orderMap.put("seqno", pageInfo.ASC);
        pageInfo.setOrderMap(orderMap);
        Page<SelectitemType> selectitemtypes = this.selectitemTypeService
                .listByCondition(pageInfo);
        return Result.OK(selectitemtypes);
    }



    // 更新Action
    @LMSLog(desc = "编辑选择项信息", otype = LogType.Update, order = 1, method =
            "createSelectitemTypeLog")
    @RequestMapping(value = {"/modify"}, method = RequestMethod.POST)
    @ApiOperation(value = "编辑数据字典类型信息", httpMethod = "POST")
    public Result updateSelectitemtype(@RequestBody SelectitemType selectitemtype) {
        // Selectitem p = this.selectitemService.get(id);
        selectitemTypeService.saveOrUpdate(selectitemtype);
        return Result.OK(selectitemtype);
    }

    // 删除
    @LMSLog(desc = "删除选择项", otype = LogType.Delete, method = "createSelectitemTypeLog", order = 1)
    @RequestMapping(value = {"/del/{id}"}, method = RequestMethod.DELETE)
    @ApiOperation(value = "删除数据字典类型信息", httpMethod = "DELETE")
    public Result deleteSelectitemtype(@PathVariable("id") String id) {
        SelectitemType selectitemtype = selectitemTypeService.getById(id);
        this.selectitemTypeService.removeById(selectitemtype);
        // 删除用户时，需要修改selectitemCache
        // selectitemCache.removeSelectitemFromCache(selectitems.getName());
        return Result.OK();
    }


    // 新增数据字典类型信息
    @RequestMapping(value = {"/add"}, method = RequestMethod.POST)
    @ApiOperation(value = "新增数据字典类型信息", httpMethod = "POST")
    public Result saveSelectitemtype(@RequestBody SelectitemType selectitemtype) throws SQLException {
        selectitemtype.setStatus(1);
        int currentno = this.selectitemTypeService.getMaxSeqno();
        currentno++;
        selectitemtype.setSeqno(currentno);
        this.selectitemTypeService.saveOrUpdate(selectitemtype);
        return Result.OK(selectitemtype);
    }


    // 批量删除
    @RequestMapping(value = {"/batchremove"}, method = RequestMethod.DELETE)
    @ApiOperation(value = "批量删除数据字典类型信息", httpMethod = "POST")
    public Result batchDeleteSelectitemtype(@RequestParam("ids") String ids) {
        String[] idarray = ids.split(",");
        List<String> idList = new ArrayList<String>();
        for (int i = 0; i < idarray.length; i++) {
            String id = idarray[i];
            idList.add(id);
        }
        this.selectitemTypeService.removeBatchByIds(idList);
        return Result.OK();
    }


    public String createSelectitemTypeLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.Save)) { //新增数据日志
            SelectitemType p = (SelectitemType) (args[0]);
            objname = StringHelper.null2String(p.getObjname());
        } else if (lmslog.otype().equals(LogType.Update)) { //编辑数据日志
                SelectitemType p = (SelectitemType) (args[0]);
                objname = StringHelper.null2String(p.getObjname());
        } else if (lmslog.otype().equals(LogType.Delete)) { //删除数据日志
            String ids = (String) (args[0]);
            String[] idarray = ids.split(",");
            for (String id : idarray) {
                SelectitemType p = selectitemTypeService.getById(id);
                objname = objname.isEmpty() ? StringHelper.null2String(p.getObjname()) : objname + "," + StringHelper.null2String(p.getObjname());
            }
        }
        return objname;
    }
}
