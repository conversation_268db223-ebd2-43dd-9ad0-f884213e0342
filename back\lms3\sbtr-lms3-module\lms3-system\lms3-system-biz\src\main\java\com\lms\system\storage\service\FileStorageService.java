package com.lms.system.storage.service;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.util.Map;

/**
 * 文件存储服务统一接口
 * 提供所有文件操作的抽象定义，支持多种存储策略
 */
public interface FileStorageService {
    
    // ==================== 文件上传操作 ====================
    
    /**
     * 上传文件到默认存储桶
     * 
     * @param file 要上传的文件
     * @return 文件存储后的唯一标识符
     * @throws Exception 上传异常
     */
    String upload(MultipartFile file) throws Exception;
    
    /**
     * 上传文件到指定存储桶
     * 
     * @param bucketName 存储桶名称
     * @param file 要上传的文件
     * @return 文件存储后的唯一标识符
     * @throws Exception 上传异常
     */
    String upload(String bucketName, MultipartFile file) throws Exception;
    
    /**
     * 上传文件并返回详细信息
     * 
     * @param file 要上传的文件
     * @param objectName 对象名称
     * @return 包含文件URL、大小等信息的Map
     * @throws Exception 上传异常
     */
    Map<String, String> uploadFile(MultipartFile file, String objectName) throws Exception;
    
    /**
     * 上传本地文件
     * 
     * @param fileLocation 本地文件路径
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @throws Exception 上传异常
     */
    void uploadLocalFile(String fileLocation, String bucketName, String objectName) throws Exception;
    
    // ==================== 文件下载操作 ====================
    
    /**
     * 下载文件到HTTP响应流
     * 
     * @param fileName 文件名
     * @param response HTTP响应对象
     * @throws Exception 下载异常
     */
    void download(String fileName, HttpServletResponse response) throws Exception;
    
    /**
     * 下载文件到HTTP响应流（指定存储桶）
     * 
     * @param bucketName 存储桶名称
     * @param fileName 文件名
     * @param response HTTP响应对象
     * @throws Exception 下载异常
     */
    void download(String bucketName, String fileName, HttpServletResponse response) throws Exception;
    
    /**
     * 获取文件输入流
     * 
     * @param fileName 文件名
     * @return 文件输入流
     * @throws Exception 下载异常
     */
    InputStream download(String fileName) throws Exception;
    
    /**
     * 获取文件输入流（指定存储桶）
     * 
     * @param bucketName 存储桶名称
     * @param fileName 文件名
     * @return 文件输入流
     * @throws Exception 下载异常
     */
    InputStream download(String bucketName, String fileName) throws Exception;
    
    // ==================== 文件管理操作 ====================
    
    /**
     * 检查文件是否存在
     * 
     * @param fileName 文件名
     * @return 文件是否存在
     */
    boolean checkFileIsExist(String fileName);
    
    /**
     * 检查文件是否存在（指定存储桶）
     * 
     * @param bucketName 存储桶名称
     * @param fileName 文件名
     * @return 文件是否存在
     */
    boolean checkFileIsExist(String bucketName, String fileName);
    
    /**
     * 删除文件
     * 
     * @param fileName 文件名
     * @return 删除是否成功
     */
    boolean deleteFile(String fileName);
    
    /**
     * 删除文件（指定存储桶）
     * 
     * @param bucketName 存储桶名称
     * @param fileName 文件名
     * @return 删除是否成功
     */
    boolean deleteFile(String bucketName, String fileName);
    
    // ==================== 存储桶操作 ====================
    
    /**
     * 检查存储桶是否存在
     * 
     * @param bucketName 存储桶名称
     * @return 存储桶是否存在
     */
    boolean bucketExists(String bucketName);
    
    /**
     * 创建存储桶
     * 
     * @param bucketName 存储桶名称
     * @return 创建是否成功
     */
    boolean makeBucket(String bucketName);
    
    // ==================== URL生成操作 ====================
    
    /**
     * 获取文件预览URL
     * 
     * @param fileName 文件名
     * @return 预览URL
     */
    String getPreviewUrl(String fileName);
    
    /**
     * 获取文件预览URL（指定存储桶）
     * 
     * @param bucketName 存储桶名称
     * @param fileName 文件名
     * @return 预览URL
     */
    String getPreviewUrl(String bucketName, String fileName);
    
    /**
     * 生成文件访问URL
     * 
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 文件访问URL
     */
    String generateFileUrl(String bucketName, String objectName);
    
    // ==================== 存储策略信息 ====================
    
    /**
     * 获取当前存储策略类型
     * 
     * @return 存储策略类型
     */
    String getStorageType();
    
    /**
     * 获取存储策略描述
     * 
     * @return 存储策略描述
     */
    String getStorageDescription();
}
