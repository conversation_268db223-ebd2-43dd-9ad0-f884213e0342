package com.lms.examine.feign.model;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.lms.common.feign.api.CommonSystemApi;
import com.lms.common.model.BaseModel;
import com.lms.common.util.SpringContextUtils;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

public class ExaminestudentrecordViewModel extends BaseModel {
	/**
	 *
	 */
	private static final long serialVersionUID = -3616943288084994218L;
	private Examinestudentrecord examinestudentrecord;
	private Examinepage examinepage = new Examinepage();
	private List items = new ArrayList();
	private List selectitems = new ArrayList();
	private CommonSystemApi languagelabelService;

	public ExaminestudentrecordViewModel(Examinestudentrecord examinesubject, Examinepage examinepage, List items) {
		this.examinestudentrecord = examinesubject;
		this.examinepage = examinepage;
		this.selectitems = items;
		languagelabelService = SpringContextUtils.getBean(CommonSystemApi.class);
		if ("9e47efc0ce894454856a80171e1e6efe".equals(examinesubject.getType())
				 || "3ef580ec56ae436eb79b91b25d1a078e".equals(examinesubject.getType())) {
					this.setItems(parse(this.examinestudentrecord.getContent()));
				} else if ("23e162b9f27b4ebc9a5c93db09913693".equals(examinesubject.getType())) {
					this.items.add(languagelabelService.translateContentNotCreated("正确",""));
					this.items.add(languagelabelService.translateContentNotCreated("错误",""));
				}
	}
	public ExaminestudentrecordViewModel(Examinestudentrecord examinesubject,Examinepage examinepage) {
		this.examinestudentrecord = examinesubject;
		this.examinepage = examinepage;
		languagelabelService = SpringContextUtils.getBean(CommonSystemApi.class);
		if ("9e47efc0ce894454856a80171e1e6efe".equals(examinesubject.getType())
		 || "3ef580ec56ae436eb79b91b25d1a078e".equals(examinesubject.getType())) {
			this.setItems(parse(this.examinestudentrecord.getContent()));
		} else if ("23e162b9f27b4ebc9a5c93db09913693".equals(examinesubject.getType())) {
			this.items.add(languagelabelService.translateContentNotCreated("正确",""));
			this.items.add(languagelabelService.translateContentNotCreated("错误",""));
		}
	}
	public ExaminestudentrecordViewModel(Examinestudentrecord examinesubject) {
		this.examinestudentrecord = examinesubject;
		languagelabelService = SpringContextUtils.getBean(CommonSystemApi.class);
		if ("9e47efc0ce894454856a80171e1e6efe".equals(examinesubject.getType())
		 || "3ef580ec56ae436eb79b91b25d1a078e".equals(examinesubject.getType())) {
			this.setItems(parse(this.examinestudentrecord.getContent()));
		} else if ("23e162b9f27b4ebc9a5c93db09913693".equals(examinesubject.getType())) {
			this.items.add(languagelabelService.translateContentNotCreated("正确",""));
			this.items.add(languagelabelService.translateContentNotCreated("错误",""));
		}
	}

	private List parse(String content) {
		List<String> items = new ArrayList<String>();
		try {
			org.dom4j.Document doc = DocumentHelper.parseText(content);
			Element root = doc.getRootElement();
			Element child;
			for(Iterator it = root.elementIterator("item");it.hasNext();)
			{
				child=(Element)it.next();
				if(!child.getStringValue().isEmpty()){
				  items.add(child.getStringValue());
				}
			}
		} catch (Exception ex) {
		}
		return items;
	}

	public List getItems() {
		return items;
	}

	public void setItems(List items) {
		this.items = items;
	}

	public Examinestudentrecord getExaminestudentrecord() {
		return examinestudentrecord;
	}

	public void setExaminestudentrecord(Examinestudentrecord examinesubject) {
		this.examinestudentrecord = examinesubject;
	}

	public Examinepage getExaminepage() {
		return examinepage;
	}

	public void setExaminepage(Examinepage examinepage) {
		this.examinepage = examinepage;
	}
	public List getSelectitems() {
		return selectitems;
	}
	public void setSelectitems(List selectitems) {
		this.selectitems = selectitems;
	}
}
