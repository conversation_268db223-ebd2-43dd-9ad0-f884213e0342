package com.lms;

import com.lms.common.config.LMSConfiguration;
import com.lms.common.util.ConstParamUtil;
import lms.rte.DataManager.DefaultDataProvider;
import lms.rte.DataManager.RTEDataManager;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;


/**
 *  公共模块启动类
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2024-07-22 10:31:39
 */
@SpringBootApplication
@ComponentScan(basePackages = {ConstParamUtil.totalLmsPackage})
@EnableFeignClients(basePackages = {ConstParamUtil.commonFeignPackage})
//@MapperScan({"com.sbtr.*.mapper"})
public class LmsBaseApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(LmsBaseApplication.class, args);
        LMSConfiguration lmsConfiguration = context.getBean(LMSConfiguration.class);
        System.out.println("===================> [ rte environment init start]");
        System.out.println(String.format("===================> [ rte environment init : set path %s", lmsConfiguration.getScormStoragePath()));
        RTEDataManager.getInstance().RegisterNormal(new DefaultDataProvider(lmsConfiguration.getScormStoragePath()));
        RTEDataManager.getInstance().RegisterFree(new DefaultDataProvider(lmsConfiguration.getScormStoragePath()));
        System.out.println("===================> [ rte environment finished ]");
    }

}
