<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sbtr-lms3-module</artifactId>
        <groupId>com.cepreitrframework.boot</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>lms3-system</artifactId>
    <packaging>pom</packaging>
    <name>lms3-system</name>
    <description>系统底层管理模块，包含系统配置、三员管理、日志、菜单、缓存管理等基本功能</description>
    <modules>
        <module>lms3-system-api</module>
        <module>lms3-system-start</module>
        <module>lms3-system-biz</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.cepreitrframework.boot</groupId>
            <artifactId>lms3-common</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <!-- 2.0系统解决异常引入的包，后期根据需要保留 end-->
    </dependencies>


</project>
