package com.lms.examine.feign.model;

import java.util.ArrayList;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import com.lms.common.model.SearchContent;
import com.lms.common.model.SearchName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * UExaminepage entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)

@Data
@TableName("u_examinepage")
@ApiModel("试卷对象")
public class Examinepage extends BaseModel {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("试卷唯一标识")
    private String id;

    @ApiModelProperty("试卷名称")
    @SearchName
    private String name;

    @ApiModelProperty("计划id")
    private String planid;

    @ApiModelProperty("编号")
    private String number;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("专业")
    private String specialid;

    @ApiModelProperty("人员层级")
    private String levelid;

    @ApiModelProperty("总分")
    private Double totalpoint;

    @ApiModelProperty("及格分")
    private Double pass;

    @ApiModelProperty("良好分")
    private Double choiceness;

    @ApiModelProperty("优秀分")
    private Double excellence;

    @ApiModelProperty("下发日期")
    private String senddowndate;

    @ApiModelProperty("结束日期")
    private String finishdate;

    @ApiModelProperty("考试时长")
    private Integer examinetime;

    @ApiModelProperty("考试开始时间")
    private String examinedate;

    @ApiModelProperty("考试截止时间")
    private String examineendtime;

    @ApiModelProperty("备注")
    @SearchContent
    private String remark;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("创建人")
    private String creatorid;

    @ApiModelProperty("创建日期")
    private String createdate;

    @SearchContent
    @ApiModelProperty("考题类型")
    private String modal;

    @SearchContent
    @ApiModelProperty("考核方式")
    private String way;

    @SearchContent
    @ApiModelProperty("考核单位")
    private String examinedept;

    @ApiModelProperty("负责人")
    private String principal;

    @ApiModelProperty("考核内容")
    private String content;

    @ApiModelProperty("层级部门")
    private String createdept;

    @ApiModelProperty("型号id")
    private String equiptypeid;

    @ApiModelProperty("型号")
    @TableField(exist = false)
    private String equipmentname;

    @ApiModelProperty("最终型号")
    @TableField(exist = false)
    private String lastequipmentid;

    @SearchContent
    @ApiModelProperty("试卷类型")
    private String examinetype;

    @ApiModelProperty("试卷课程")
    @TableField(exist = false)
    private List<Examinecourse> examinecourses;

    @ApiModelProperty("试卷题型")
    @TableField(exist = false)
    private List<Examinesubjecttype> examinesubjecttypes;

    @ApiModelProperty("题目是否乱序")
    private Integer titledisturb;

    @ApiModelProperty("选项是否乱序")
    private Integer answerdisturb;

    @ApiModelProperty("成绩发布日期")
    private String publishdate;

    @ApiModelProperty("成绩发布类型")
    private String publishtype;

    @ApiModelProperty("是否允许查看")
    private Integer allowview;

    @ApiModelProperty("是否允许显示排名")
    private Integer showranking;

    @ApiModelProperty("课程id")
    private String courseid;

    @ApiModelProperty("负责人名称")
    @TableField(exist = false)
    private String principalname;

    @ApiModelProperty("部门名称")
    @TableField(exist = false)
    private String deptname;

    @ApiModelProperty("计划名称")
    @TableField(exist = false)
    private String planname;

    @ApiModelProperty("学员")
    @TableField(exist = false)
    private Examinestudent student;

    public Examinepage() {
        this.examinecourses = new ArrayList<>();
        this.examinesubjecttypes = new ArrayList<>();
    }
}
