<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.mdata.mapper.SysAuthGroupAccessMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sbtr.mdata.model.SysAuthGroupAccess" id="sysAuthGroupAccessMap">
        <result property="uuid" column="uuid"/>
        <result property="create_time" column="create_time"/>
        <result property="creator" column="creator"/>
        <result property="creator_id" column="creator_id"/>
        <result property="is_del" column="is_del"/>
        <result property="modifier" column="modifier"/>
        <result property="modifier_id" column="modifier_id"/>
        <result property="modify_time" column="modify_time"/>
        <result property="role_id" column="role_id"/>
        <result property="user_name_uuid" column="user_name_uuid"/>
    </resultMap>

  <sql id="Base_Column_List" >
  	  	     			 uuid
		  ,  	  	     			 create_time
		  ,  	  	     			 creator
		  ,  	  	     			 creator_id
		  ,  	  	     			 is_del
		  ,  	  	     			 modifier
		  ,  	  	     			 modifier_id
		  ,  	  	     			 modify_time
		  ,  	  	     			 role_id
		  ,  	  	     			 user_name_uuid
		    	  </sql>

    <select id="getPageSet" resultType="com.sbtr.mdata.model.SysAuthGroupAccess">
        select
        <include refid="Base_Column_List"></include>
        from sys_auth_group_access
        <where>
            <if test="filterSort != null">
				${filterSort}
            </if>
        </where>
    </select>

	<select id="queryObject" resultType="com.sbtr.mdata.model.SysAuthGroupAccess">
		select  <include refid="Base_Column_List" />  from sys_auth_group_access where uuid = #{value}
	</select>

	<select id="queryList" resultType="com.sbtr.mdata.model.SysAuthGroupAccess">
		select  <include refid="Base_Column_List" />  from sys_auth_group_access
        <choose>
            <when test="sidx != null and sidx.trim() != ''">
                order by ${sidx} ${order}
            </when>
			<otherwise>
                order by uuid desc
			</otherwise>
        </choose>
		<if test="offset != null and limit != null">
			limit #{offset}, #{limit}
		</if>
	</select>

 	<select id="queryTotal" resultType="int">
		select count(*) from sys_auth_group_access
	</select>
	<select id="getRepeatRoleIdByUserUuidAndInRoleId" resultType="java.lang.String">
		select role_id  from sys_auth_group_access where user_name_uuid = #{userUuid} and
		role_id in
		<foreach item="roleId" collection="roleId" open="(" separator="," close=")">
			#{roleId}
		</foreach>

	</select>

	<insert id="executeSave" parameterType="com.sbtr.mdata.model.SysAuthGroupAccess">
		insert into sys_auth_group_access
		(
			`uuid`,
			`create_time`,
			`creator`,
			`creator_id`,
			`is_del`,
			`modifier`,
			`modifier_id`,
			`modify_time`,
			`role_id`,
			`user_name_uuid`
		)
		values
		(
			#{uuid},
			#{create_time},
			#{creator},
			#{creator_id},
			#{is_del},
			#{modifier},
			#{modifier_id},
			#{modify_time},
			#{role_id},
			#{user_name_uuid}
		)
	</insert>
	<insert id="saveBatch">
		insert into sys_auth_group_access
		(uuid, creator_id, creator,create_time,role_id,user_name_uuid)
		values
		<foreach collection="list" item="list" index="index" separator=",">
		(
		#{list.uuid},
		#{list.creatorId},
		#{list.creator},
		#{list.createTime},
		#{list.roleId},
		#{list.userNameUuid}
		)
		</foreach>
	</insert>

	<update id="executeUpdate" parameterType="com.sbtr.mdata.model.SysAuthGroupAccess">
		update sys_auth_group_access
		<set>
			<if test="create_time != null">`create_time` = #{create_time}, </if>
			<if test="creator != null">`creator` = #{creator}, </if>
			<if test="creator_id != null">`creator_id` = #{creator_id}, </if>
			<if test="is_del != null">`is_del` = #{is_del}, </if>
			<if test="modifier != null">`modifier` = #{modifier}, </if>
			<if test="modifier_id != null">`modifier_id` = #{modifier_id}, </if>
			<if test="modify_time != null">`modify_time` = #{modify_time}, </if>
			<if test="role_id != null">`role_id` = #{role_id}, </if>
			<if test="user_name_uuid != null">`user_name_uuid` = #{user_name_uuid}</if>
		</set>
		where uuid = #{uuid}
	</update>

	<delete id="executeDelete">
		delete from sys_auth_group_access where uuid = #{value}
	</delete>

	<delete id="executeDeleteBatch">
		delete from sys_auth_group_access where uuid in
		<foreach item="uuid" collection="array" open="(" separator="," close=")">
			#{uuid}
		</foreach>
	</delete>
	<delete id="deleteByUserUuid">
		delete from sys_auth_group_access where user_name_uuid = #{userUuid}
	</delete>

</mapper>
