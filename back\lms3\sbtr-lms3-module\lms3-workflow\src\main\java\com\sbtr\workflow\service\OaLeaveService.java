package com.sbtr.workflow.service;

import com.sbtr.workflow.model.OaLeave;
import com.sbtr.workflow.model.OaLeaveCommon;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;


/**
 * 休假表服务接口
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:35:23
 */
public interface OaLeaveService extends WorkflowBaseService<OaLeave, String> {

    /**
     * 获取待办信息
     *
     * @param pageParam 分页参数
     * @param filterSort 过来参数
     * @param userNmeId 工号
     */
    PageSet<OaLeaveCommon> getToDoTasks(PageParam pageParam, String filterSort, String userNmeId);

    /**
     * 获取在办信息
     *
     * @param pageParam 分页参数
     * @param filterSort 过来参数
     * @param userNmeId 工号
     */
    PageSet<OaLeaveCommon> getTasksInProgress(PageParam pageParam, String filterSort, String userNmeId);

    /**
     * 获取历史任务信息
     *
     * @param pageParam 分页参数
     * @param filterSort 过来参数
     * @param userNmeId 工号
     */
    PageSet<OaLeaveCommon> getHistoryTasks(PageParam pageParam, String filterSort, String userNmeId);

    /**
     * 获取请假信息
     *
     * @param pageParam 分页参数
     * @param filterSort 过来参数
     * @param title 标题
     */
    PageSet<OaLeave> getPageSet(PageParam pageParam, String filterSort, String title);

    /**
     * 批量删除请假信息
     *
     * @param uuid 主键
     */
    int executeDeleteBatch(String[] uuid);

    /**
     * @MethodName updateStateByUuid  根据uuid更新state值
     * @Description
     * @Param businessKey
     * @Param state
     * @Return java.lang.Integer
     * <AUTHOR>
     * @Date 2021-1-22 13:24
     */
    Integer updateStateByUuid(String businessKey, String state);
}
