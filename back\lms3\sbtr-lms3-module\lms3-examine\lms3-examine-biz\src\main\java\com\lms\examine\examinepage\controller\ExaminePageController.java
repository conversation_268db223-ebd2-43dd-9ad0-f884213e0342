/**
 * FileName:ExaminepageController.java
 * Author:<PERSON><PERSON><PERSON><PERSON>
 * Time  :2013-3-7下午4:18:10
 * CopyRight: SBTR LTD.
 * decription:to-do
 */
package com.lms.examine.examinepage.controller;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.lms.base.feign.api.LmsBaseApi;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.feign.dto.Department;
import com.lms.common.feign.dto.Person;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.DateHelper;
import com.lms.common.util.StringHelper;
import com.lms.examine.examinepage.mapper.ExaminePageMapper;
import com.lms.examine.examinepage.service.ExaminePageService;
import com.lms.examine.examinestudent.service.ExaminestudentService;
import com.lms.examine.examinesubjecttype.service.ExaminesubjecttypeService;
import com.lms.examine.feign.model.ExaminePageViewModel;
import com.lms.examine.feign.model.Examinecourse;
import com.lms.examine.feign.model.Examinepage;
import com.lms.examine.examinepage.service.impl.ExamineCourseServiceImpl;
import com.lms.examine.feign.model.Examinesubjecttype;
import com.lms.examine.feign.model.Examinestudent;
import org.apache.commons.collections.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR> 试题信息操作控制层
 */
@RestController
@RequestMapping("/examinepage")
public class ExaminePageController extends BaseController<Examinepage> {
    @Resource
    private ExaminePageService examinepageService;

    @Resource
    private ExaminesubjecttypeService examinesubjecttypeService;

    @Resource
    private ExaminestudentService examinestudentService;

    @Resource
    private ExaminePageMapper examinePageMapper;

    @Resource
    private LmsBaseApi lmsBaseApi;

    @Resource
    private ExamineCourseServiceImpl examineCourseService;

    @LMSLog(desc = "查询试卷列表", otype = LogType.List, order = 1, method = "setExaminepageLog")
    @PostMapping(value = {"/listpage"})
    public Result getExaminepageList(@RequestBody JSONObject request) {
        // 设置分页参数,查询参数
        // super.setParameter(request);
        PageInfo pageInfo = super.getPageInfo(request);
        String getLearnStatus = StringHelper.null2String(request.getString("getLearnStatus"));
        String studentId = StringHelper.null2String(request.getString("studentId"));
        Page<Examinepage> examinepages = examinepageService.listByCondition(pageInfo);
        List listForAdaptComponent = examinepages.getRecords();
        examinepageService.adaptComponent(listForAdaptComponent);
        for (Examinepage examinepage : examinepages.getRecords()) {
            String principal = StringHelper.null2String(examinepage
                    .getPrincipal());
            String examinedept = StringHelper.null2String(examinepage
                    .getExaminedept());

            if (!principal.isEmpty()) {
                Person person = commonBaseApi.getPersonById(principal).getResult();
                if (person != null) examinepage.setPrincipalname(StringHelper.null2String(person.getName()));
            }
            if (!examinedept.isEmpty()) {
                Department dept = commonBaseApi.getDepartmentById(examinedept).getResult();
                if (dept != null) examinepage.setDeptname(StringHelper.null2String(dept.getName()));
            }
            if (!getLearnStatus.isEmpty()) {
                List<Examinestudent> list = examinestudentService.getLearnStatusById(examinepage.getId(), studentId);
                if (list.size() > 0) {
                    examinepage.setStudent(list.get(0));
                } else {
                    Examinestudent student = new Examinestudent();
                    examinepage.setStudent(student);
//					student.setScore(0.00);
//					student.setResult("17ebb78046e44967a5c3bc9338342062");
//					examinepage.setLearnstatus(0);
                }

            }
        }
        return Result.OK(examinepages);
    }

    @GetMapping(value = {"/getExaminepage"})
    public Result getExaminepage(@RequestParam String id) {
        Examinepage examinepage = examinepageService.getEagerData(id);
        examinepageService.adaptComponent(Collections.singletonList(examinepage));
        return Result.OK(examinepage);
    }

    @RequestMapping(value = {"/passratelist"}, method = RequestMethod.POST)
    public Result getExaminepagePassRateList(@RequestBody JSONObject request) {
        // 设置分页参数,查询参数
//		super.setParameter(request);
        PageInfo pageInfo = super.getPageInfo(request);
        Page<Examinepage> examinepages = examinepageService.listByCondition(pageInfo);
        List listForAdaptComponent = examinepages.getRecords();
        examinepageService.adaptComponent(listForAdaptComponent);
        List<ExaminePageViewModel> sModelList = new ArrayList<ExaminePageViewModel>();
        for (Examinepage examinepage : examinepages.getRecords()) {
            String examineid = StringHelper.null2String(examinepage.getId());
            List<Examinestudent> students = examinestudentService
                    .getExaminestudentsByExamId(examineid);
            ExaminePageViewModel s = new ExaminePageViewModel(examinepage,
                    students);
            sModelList.add(s);
        }
        Map<String, Object> map = BeanUtil.beanToMap(examinepages);
        map.put("content", sModelList);
        return Result.OK(map);
    }

    @RequestMapping(value = {"/getSubjecttype/{id}"}, method = RequestMethod.GET)
    public Result getExaminesubjecttype(@PathVariable("id") String id) {
        Examinepage examinepage = this.examinepageService.getEagerData(id);
        examinepageService.adaptComponent(Collections.singletonList(examinepage));
        List<Examinesubjecttype> subjecttype = examinepage.getExaminesubjecttypes();
        List<Examinesubjecttype> subjecttypelist = new ArrayList<Examinesubjecttype>(subjecttype);
        Collections.sort(subjecttypelist, new Comparator<Examinesubjecttype>() {
            public int compare(Examinesubjecttype t1, Examinesubjecttype t2) {
                return t1.getSeqno().compareTo(t2.getSeqno());
            }
        });
        return Result.OK(subjecttypelist);
    }

    /*
     * 获取所有试题信息
     */
    @RequestMapping(value = {"/list"}, method = RequestMethod.GET)
    public Result getValidExaminepage() {
        List<Examinepage> examinepages = this.examinepageService.list();
        return Result.OK(examinepages);
    }

    /*
     * 根据ID获取试题信息
     */
    @RequestMapping(value = {"/getInfo/{id}"}, method = RequestMethod.GET)
    public Result getExaminepageInfo(@PathVariable("id") String pid) {
        Examinepage examinepage = this.examinepageService.getById(pid);
        examinepageService.adaptComponent(Collections.singletonList(examinepage));
        return Result.OK(examinepage);
    }

    // 更新Action
    @LMSLog(desc = "修改试卷信息", otype = LogType.Update, order = 1, method =
            "setExaminepageLog")
    @RequestMapping(value = {"/modify"}, method = RequestMethod.POST)
    public Result updateExaminepage(@RequestBody Examinepage examinepage) {
        String planid = StringHelper.null2String(examinepage.getPlanid());
        String resMsg = this.examinestudentService.chekcSubjectAmount(examinepage, planid.isEmpty() ? 2 : 1);
        if (!StringHelper.isEmpty(resMsg)) {
            return Result.error(resMsg);
        }
        List<Examinesubjecttype> examineSubjectTypes = examinepage.getExaminesubjecttypes();
        if (!examineSubjectTypes.isEmpty()) {
            this.examinesubjecttypeService.saveOrUpdateBatch(examineSubjectTypes);
        }
        this.examinepageService.saveOrUpdate(examinepage);
        String examinepageid = examinepage.getId();
        examineCourseService.batchDeleteByExaminePageId(examinepageid);
        List<Examinecourse> examinecourses = examinepage.getExaminecourses();
        if (CollectionUtils.isNotEmpty(examinecourses)) {
            for (Examinecourse examinecourse : examinecourses) {
                examinecourse.setExamineid(examinepageid);
                examinecourse.setCourseid(examinecourse.getPointid());
            }
            examineCourseService.saveOrUpdateBatch(examinecourses);
        }
        List<Examinestudent> students = examinestudentService.getExaminestudentsByExamId(examinepage.getId());
        for (int i = 0; i < students.size(); i++) {
            Examinestudent examinestudent = students.get(i);
            if (Integer.getInteger(examinestudent.getExaminestatus(), 0) < 3) {
                updateTimeByPage(examinepage, examinestudent);
            }
        }
        return Result.OK(examinepage);
    }


    // 批量删除
    @LMSLog(desc = "删除试卷", otype = LogType.Delete, method = "setExaminepageLog", order = 1)
    @RequestMapping(value = {"/batchremove"}, method = RequestMethod.DELETE)
    public Result batchDeleteExaminepage(@RequestParam("ids") String ids) {
        String[] idarray = ids.split(",");
        List<String> idList = new ArrayList<String>();
        String errorName = "";
        String errorMsg = "";
        for (int i = 0; i < idarray.length; i++) {
            String id = idarray[i];
            idList.add(id);
            String name = this.examinepageService.isRelease(id);
            if (!name.isEmpty()) {
                errorName = errorName.isEmpty() ? name : errorName + "," + name;
            }
        }
        if (!errorName.isEmpty()) {
            errorMsg = commonSystemApi.translateContent("选中的试卷([?])已下发或已结束，无法删除！", "", errorName);
            return Result.error(errorMsg);
        }
        this.examinepageService.removeBatchByIds(idList);
        return Result.OK();
    }

    @LMSLog(desc = "新增试卷", otype = LogType.Save, method = "setExaminepageLog", order = 1)
    @RequestMapping(value = {"/add"}, method = RequestMethod.POST)
    public Result saveExaminepage(@RequestBody Examinepage examinepage) {
        String planid = StringHelper.null2String(examinepage.getPlanid());
        String resMsg = this.examinestudentService.chekcSubjectAmount(examinepage, planid.isEmpty() ? 2 : 1);
        if (!StringHelper.isEmpty(resMsg)) {
            return Result.error(resMsg);
        }
        if (!planid.isEmpty()) {
            String courseid = StringHelper.null2String(examinepage.getCourseid());
            String examId = examinepageService.findExamineIdByCourse(courseid, planid);
            if (!StringHelper.isEmpty(examId)) {
                Result.error(commonSystemApi.translateContent("该课程计划已存在试卷，无法新增！"));
            }
        }
//		// 所有新增的用户都是普通用户
//		if(StringHelper.null2String(examinepage.getType()).equals("4028828a6005533d0160055343e90000")){//在线考试
//			examinepage.setStatus(1);
//		}else{
//			examinepage.setStatus(2);
//		}
        String examineId = StringHelper.IDGenerator();
        examinepage.setId(examineId);
        examinepage.setStatus(1);
        String personID = ContextUtil.getPersonId();
        examinepage.setCreatorid(personID);
        examinepage.setCreatedate(DateHelper.getCurrentDate());
        List<Examinesubjecttype> examineSubjectTypes = examinepage.getExaminesubjecttypes();
        if (!examineSubjectTypes.isEmpty()) {
            for (Examinesubjecttype est : examineSubjectTypes) {
                est.setExamineid(examineId);
            }
            this.examinesubjecttypeService.saveOrUpdateBatch(examineSubjectTypes);
        }
        this.examinepageService.saveOrUpdate(examinepage);
        String examinepageid = examinepage.getId();
        List<Examinecourse> examinecourses = examinepage.getExaminecourses();
        if (CollectionUtils.isNotEmpty(examinecourses)) {
            for (Examinecourse examinecourse : examinecourses) {
                examinecourse.setExamineid(examinepageid);
                examinecourse.setCourseid(examinecourse.getPointid());
            }
            examineCourseService.saveOrUpdateBatch(examinecourses);
        }
        if (!planid.isEmpty()) {
            //如果学习计划不为空，同步学习计划里的计划学习记录表到考试学员表
            //计划试卷考虑可以重复考试，设置为培训学员考试时自动生成试卷考生by qjh
            //examinepageService.sysPlanLearnRecord(examinepageNew.getId(),planid);
        }
        return Result.OK(examinepage);
    }

    @RequestMapping(value = {"/getSelfTestRecord/{id}"}, method = RequestMethod.GET)
    public Result getSelfExaminepage(@PathVariable("id") String examineid) {
        Examinepage examinepage = this.examinepageService.getById(examineid);
        List<Examinecourse> examinecourseList = examineCourseService.getbyExaminePageId(examinepage.getId());
        examinepage.setExaminecourses(examinecourseList);
        HashMap<String, List> subjectMap = new HashMap();
        Examinestudent examinestudent = new Examinestudent();
        examinestudent.setExamineid(examineid);
        examinestudent.setPersonid(ContextUtil.getPersonId());
        examinestudent.setStatus(1);
        examinestudent.setExaminestatus("3");
        String checkMsg = examinestudentService.chekcSubjectAmountByExamine(examinepage, 2, subjectMap);
        if (checkMsg.contains("createSuccess")) {
            examinestudentService.saveOrUpdate(examinestudent);
            examinestudentService.createStudentRecordByExamine(examinestudent, examineid, subjectMap);
            return Result.OK(examinestudent);
        } else {
            return Result.error(checkMsg);
        }
    }

    @RequestMapping(value = {"/getSelfTeststudent/{id}"}, method = RequestMethod.GET)
    public Result getSelfExamineStudent(
            @PathVariable("id") String examineid) {
        Examinepage examinepage = this.examinepageService.getById(examineid);
        List<Examinestudent> examinestudents = examinestudentService
                .getExaminestudentsByExamId(examineid);
        Examinestudent examinestudent = new Examinestudent();
        if (examinestudents.size() > 0) {
            examinestudent = examinestudents.get(0);
        }
        return Result.OK(examinestudent);
    }

    /*
     * 结束考试
     * */
    @LMSLog(desc = "结束考试", otype = LogType.Update, order = 2, method =
            "setExaminepageLog")
    @RequestMapping(value = {"/finishExaminepage"}, method = RequestMethod.GET)
    public Result finishExaminepage(@RequestParam("ids") String ids) {
        String[] idarray = ids.split(",");
        for (String id : idarray) {
            Examinepage examinepage = this.examinepageService.getById(id);
            examinepage.setStatus(3);
            examinepageService.saveOrUpdate(examinepage);
            examinepageService.calcSubjectKKD(id);
        }

        return Result.OK();
    }

    /*@LMSLog(desc = "导出试卷", otype = LogType.Export, order = 1, method =
            "setExaminepageLog")*/
    @RequestMapping(value = {"/downLoadExaminepage"})
    public void downLoadExaminepage(HttpServletResponse response,
                                    @RequestParam("examineid") String examineid) throws IOException {
        Examinepage examine = examinepageService.getById(examineid);
        String fileDir = examinepageService.createWord(examine);
        String filename = examine.getName() + ".doc";
        byte data[] = new byte[4096];
        int byteread;
        InputStream file = null;
        ServletOutputStream out = response.getOutputStream();

        File thefile = new File(fileDir);
        if (thefile.exists()) {
            file = new BufferedInputStream(new FileInputStream(thefile));
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader(
                    "content-disposition",
                    "attachment; filename="
                            + new String(filename.getBytes("GBK"),
                            "ISO8859_1"));
            while ((byteread = file.read(data)) != -1) {
                out.write(data, 0, byteread);
            }
            out.flush();
            file.close();
            out.close();
            thefile.delete();
        }
    }

    public String setExaminepageLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.List)) {
            objname = "试卷列表";
        } else if (lmslog.otype().equals(LogType.Save)) { //新增数据日志
            Examinepage p = (Examinepage) (args[0]);
            objname = StringHelper.null2String(p.getName());
        } else if (lmslog.otype().equals(LogType.Update)) { //编辑数据日志
            if (lmslog.order() == 1) {
                Examinepage p = (Examinepage) (args[0]);
                objname = StringHelper.null2String(p.getName());
            } else if (lmslog.order() == 2) {
                String ids = (String) (args[0]);
                String[] idarray = ids.split(",");
                for (String id : idarray) {
                    Examinepage p = examinepageService.getById(id);
                    objname = objname.isEmpty() ? StringHelper.null2String(p.getName()) : objname + "," + StringHelper.null2String(p.getName());
                }
            }

        } else if (lmslog.otype().equals(LogType.Export)) { //编辑数据日志
            Examinepage p = examinepageService.getById(args[0].toString());
            objname = StringHelper.null2String(p.getName());
        } else if (lmslog.otype().equals(LogType.Delete)) { //删除数据日志
            String ids = (String) (args[0]);
            String[] idarray = ids.split(",");
            for (String id : idarray) {
                Examinepage p = examinepageService.getById(id);
                objname = objname.isEmpty() ? StringHelper.null2String(p.getName()) : objname + "," + StringHelper.null2String(p.getName());
            }
        }
        return objname;
    }

    public void updateTimeByPage(Examinepage examinepage, Examinestudent examinestudent) {
        examinestudent.setExaminedtime(StringHelper.null2String(examinepage
                .getExamineendtime()));// 考试截止时间
        String begintime = StringHelper.null2String(examinepage
                .getExaminedate());// 考试开始时间
        int examtime = examinepage.getExaminetime();// 考试时长
        if (!begintime.isEmpty()) {// 根据考试时长和考试开始时间，计算考试结束时间
            examinestudent.setBegintime(begintime);
            String endtime = DateHelper.dayMoveDateTime(begintime, 0, 0, 0,
                    0, examtime, 0);
            examinestudent.setEndtime(endtime);
            if (examinepage.getWay().equals(
                    "8363606ac3ac4f89a3e002ca093fec92")) {
                examinestudent.setExaminedtime(endtime);
            }
        }
    }

    @GetMapping("/listExaminepage")
    public Result<List<Examinepage>> listExaminepage(@RequestParam String planId) {
        return Result.OK(examinepageService.listExaminepage(planId));
    }

    @GetMapping("/listSQLQuery")
    public Result<List<Map<String, Object>>> listSQLQuery(@RequestParam String sql) {
        return Result.OK(examinePageMapper.listQuery(sql));
    }

    @GetMapping("/calcSubjectScoreRate")
    public Result<JSONObject> calcSubjectScoreRate(@RequestParam String subjectid) {
        return Result.OK(examinepageService.calcSubjectScoreRate(subjectid));
    }

    @PostMapping("/saveAllForImport")
    public Result saveAllForImport(@RequestBody List<Examinepage> examinepageList) {
        examinepageService.saveOrUpdateBatch(examinepageList);
        return Result.OK();
    }

}
