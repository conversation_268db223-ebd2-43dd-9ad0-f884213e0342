package com.sbtr.workflow.service.impl;

import cn.hutool.core.util.StrUtil;
import com.sbtr.workflow.mapper.ActMyCategoryMapper;
import com.sbtr.workflow.model.ActMyCategory;
import com.sbtr.workflow.service.ActMyCategoryService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.PageUtil.PageUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * 我分类服务impl行动
 *
 * <AUTHOR>
 * @Version V5.4.21
 * @Email <EMAIL>
 * @Date 2023/03/20
 */
@Service("workflowCategoryServiceImpl")
public class ActMyCategoryServiceImpl extends WorkflowBaseServiceImpl<ActMyCategory, String> implements ActMyCategoryService {
	@Autowired
	private ActMyCategoryMapper flowCategoryMapper;

    /**
     * 获取页面设置
     *
     * @param pageParam    页面参数
     * @param categoryName 类别名称
     * @return {@link PageSet}<{@link ActMyCategory}>
     */
    @Override
    public PageSet<ActMyCategory> getPageSet(PageParam pageParam , String categoryName) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        Example example = new Example(ActMyCategory.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (StrUtil.isNotBlank(categoryName)) {
            criteria.andLike("categoryName", "%" + categoryName + "%");
        }
        List<ActMyCategory> list = flowCategoryMapper.selectByExample(example);
        PageInfo<ActMyCategory> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    /**
     * 执行删除批处理
     *
     * @param uuids uuid
     * @return int
     */
    @Override
	public int executeDeleteBatch(String[] uuids){
		return flowCategoryMapper.executeDeleteBatch(uuids);
	}

    /**
     * 得到列表代码和uuid
     *
     * @param categoryCode 类别代码
     * @param uuid         uuid
     * @return {@link Boolean}
     */
    @Override
    public Boolean getListByCodeAndUuid(String categoryCode, String uuid) {
        return flowCategoryMapper.getListByCodeAndUuid(categoryCode, uuid) > 0;
    }


}
