<template>
  <a-modal
    :open="visible"
    :confirm-loading="loading"
    width="600px"
    :destroy-on-close="true"
    class="personnel-add-modal"
    @cancel="handleCancel"
  >
    <!-- 自定义标题 -->
    <template #title>
      <div class="modal-title">
        <h3>{{ isEdit ? "编辑人员" : "新增人员" }}</h3>
      </div>
    </template>

    <div class="personnel-add-form">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="horizontal"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="姓名" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入姓名" />
        </a-form-item>
        <a-form-item label="证件号(账号)" name="cardNum">
          <a-input
            v-model:value="formData.cardNum"
            placeholder="请输入证件号(账号)"
          />
        </a-form-item>
        <a-form-item label="性别">
          <a-select
            v-model:value="formData.sex"
            placeholder="请选择性别"
            :options="genderOptions"
          />
        </a-form-item>

        <a-form-item label="出生日期">
          <a-date-picker
            v-model:value="formData.birthday"
            format="YYYY-MM-DD"
            valueFormat="YYYY-MM-DD"
            placeholder="请选择出生日期"
            class="w-full"
          />
        </a-form-item>

        <a-form-item label="工作单位" name="departmentid">
          <dept-tree
            :tree-value="formData.departmentid"
            @change="
              data => {
                formData.departmentid = data.value;
              }
            "
          />
        </a-form-item>

        <a-form-item label="所在岗位">
          <a-select
            v-model:value="formData.duty"
            placeholder="请选择所在岗位"
            :options="positionOptions"
          />
        </a-form-item>

        <a-form-item label="专业">
          <a-select
            v-model:value="formData.specialityid"
            placeholder="请选择专业"
            :options="specialtyOptions"
          />
        </a-form-item>

        <a-form-item label="角色" name="persontype">
          <a-select
            v-model:value="formData.persontype"
            placeholder="请选择角色"
            :options="roleOptions"
          />
        </a-form-item>
      </a-form>
    </div>

    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" :loading="loading" @click="handleSubmit">
          提交
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { message } from "ant-design-vue";
import type { FormInstance } from "ant-design-vue";
import deptTree from "@/components/Selector/departmetTree.vue";
import {
  genderOptions,
  positionOptions,
  specialtyOptions,
  roleOptions
} from "@/data/system";
import { addPerson, editPerson } from "@/api/system.ts";

interface Props {
  visible: boolean;
  personnel?: object;
  isEdit: boolean;
}

interface Emits {
  (e: "close", visible: boolean): void;
  (e: "submit", data: object): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const loading = ref(false);

// 表单数据
const formData = ref<any>({});

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入姓名" }],
  cardNum: [{ required: true, message: "请输入人员证件号(账号)" }],
  departmentid: [{ required: true, message: "请选择工作单位" }],
  persontype: [{ required: true, message: "请选择角色" }]
};

// 监听弹窗显示状态，初始化表单数据
watch(
  () => props.visible,
  visible => {
    if (visible) {
      if (props.personnel) {
        formData.value = props.personnel;
      }
    }
  }
);

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;
    let res = formData.value.id
      ? await editPerson(formData.value)
      : await addPerson(formData.value);
    if (res.success) {
      message.success(res.message);
      emit("submit", { ...formData });
      handleCancel();
    } else {
      message.error(res.message);
    }
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit("close", false);
};
</script>

<style lang="scss" scoped>
.modal-title {
  background-color: var(--gray-100);
  margin: -24px -24px 24px -24px;
  padding: var(--spacing-6);

  h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
    color: var(--gray-900);
    margin: 0;
  }
}

.personnel-add-form {
  max-height: 500px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-3);
}

// 表单样式
:deep(.ant-form-item) {
  margin-bottom: var(--spacing-4);
}

:deep(.ant-form-item-label) {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
}

:deep(.ant-input),
:deep(.ant-select),
:deep(.ant-date-picker) {
  width: 100%;
}

// 自定义标题样式
:deep(.ant-modal-header) {
  padding: 0;
  border: 0;
}

:deep(.ant-modal-title) {
  padding: 0;
}
</style>
