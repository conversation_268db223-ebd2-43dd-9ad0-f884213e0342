package com.lms.base.subject.service;

import com.lms.base.feign.model.Wrongsubject;
import com.lms.common.service.BaseService;

import java.sql.SQLException;
import java.util.List;

public interface WrongsubjectService extends BaseService<Wrongsubject> {

    List<Wrongsubject> getWrongSubjectList(List<String> courseidList);

    boolean existSubject(String personid, String subjectid);

    int getMaxSeqno(String personid) throws SQLException;

    List<Wrongsubject> getByPersonAndSubject(String personid, String id);
}
