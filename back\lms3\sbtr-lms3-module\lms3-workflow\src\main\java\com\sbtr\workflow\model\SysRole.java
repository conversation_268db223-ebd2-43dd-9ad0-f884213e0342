package com.sbtr.workflow.model;

import cn.ewsd.common.model.MCoreBase;

import javax.persistence.Table;

/**
 * 角色表
 *
 * <AUTHOR>
 * @Version V5.4.21
 * @Email <EMAIL>
 * @Date 2022-09-15 17:57:53
 */
@Table(name = "sys_role")
public class SysRole{

    /**
     * 角色id
     */
    private String id;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 排序
     */
    private Integer roleSort;
    /**
     * 备注
     */
    private String roleRemark;
    /**
     * 组织机构ID
     */
    private String orgId;

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Integer getRoleSort() {
        return roleSort;
    }

    public void setRoleSort(Integer roleSort) {
        this.roleSort = roleSort;
    }

    public String getRoleRemark() {
        return roleRemark;
    }

    public void setRoleRemark(String roleRemark) {
        this.roleRemark = roleRemark;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "SysRole{" +
                "id='" + id + '\'' +
                ", roleName='" + roleName + '\'' +
                ", roleSort=" + roleSort +
                ", roleRemark='" + roleRemark + '\'' +
                ", orgId=" + orgId +
                '}';
    }
}
