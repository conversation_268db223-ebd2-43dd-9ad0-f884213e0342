package com.lms.system.minio.config;

import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MinIO配置类
 *
 * <AUTHOR>
 * @version 2023/04/21
 * @since JDK8
 */
@Configuration
public class MinioConfig {

    @Value(value = "${minio.host}")
    private String host;

    @Value(value = "${minio.access-key}")
    private String accessKey;

    @Value(value = "${minio.secret-key}")
    private String secretKey;

    // 注册MinIO实例
    @Bean
    public MinioClient buildMinioClient() {
        return MinioClient
                .builder()
                .credentials(accessKey, secretKey)
                .endpoint(host)
                .build();
    }
}

