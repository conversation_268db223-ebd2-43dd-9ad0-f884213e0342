<script setup lang="ts">
import {
  ref,
  computed,
  watch,
  nextTick,
  onMounted,
  onBeforeUnmount
} from "vue";
import {
  FullscreenOutlined,
  FullscreenExitOutlined
} from "@ant-design/icons-vue";
import type { TrDialogProps, TrDialogEmits } from "./types";
import {
  CONSTANTS,
  getFullscreenStyle,
  joinClassNames,
  clampDragPosition,
  calculateDragPosition,
  getDragBoundariesWithElement
} from "./utils";

// 组件定义
defineOptions({ name: "TrDialog" });

// Props 定义
const props = withDefaults(defineProps<TrDialogProps>(), {
  width: CONSTANTS.DEFAULT_WIDTH,
  closable: CONSTANTS.DEFAULT_CLOSABLE,
  maskClosable: CONSTANTS.DEFAULT_MASK_CLOSABLE,
  keyboard: CONSTANTS.DEFAULT_KEYBOARD,
  draggable: CONSTANTS.DEFAULT_DRAGGABLE,
  fullscreen: CONSTANTS.DEFAULT_FULLSCREEN,
  showFooter: CONSTANTS.DEFAULT_SHOW_FOOTER,
  showFullscreenButton: CONSTANTS.DEFAULT_SHOW_FULLSCREEN_BUTTON,
  destroyOnClose: CONSTANTS.DEFAULT_DESTROY_ON_CLOSE
});

// Events 定义
const emit = defineEmits<TrDialogEmits>();

// 响应式数据
const visible = computed({
  get: () => props.open || false,
  set: (value: boolean) => emit("update:open", value)
});

// 全屏相关
const isFullscreen = ref(props.fullscreen);

// 拖拽相关 - 重新设计
const modalTitleRef = ref<HTMLElement | null>(null);
const modalRef = ref<HTMLElement | null>(null);
const isDragging = ref(false);
const dragStartPos = ref({ x: 0, y: 0 });
const dragStartOffset = ref({ x: 0, y: 0 });
const currentDragPos = ref({ x: 0, y: 0 });

// 拖拽事件处理函数
const handleMouseDown = (e: MouseEvent) => {
  if (!props.draggable || isFullscreen.value) return;

  e.preventDefault();
  e.stopPropagation();

  isDragging.value = true;
  dragStartPos.value = { x: e.clientX, y: e.clientY };
  dragStartOffset.value = { ...currentDragPos.value };

  // 添加拖拽样式
  document.body.style.cursor = "move";
  document.body.style.userSelect = "none";

  // 绑定全局事件
  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
};

const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging.value) return;

  // 使用工具函数计算精确的拖拽位置
  const { x: boundedX, y: boundedY } = calculateDragPosition(
    e.clientX,
    e.clientY,
    dragStartPos.value.x,
    dragStartPos.value.y,
    dragStartOffset.value.x,
    dragStartOffset.value.y
  );

  currentDragPos.value = { x: boundedX, y: boundedY };
};

const handleMouseUp = () => {
  if (!isDragging.value) return;

  isDragging.value = false;

  // 恢复样式
  document.body.style.cursor = "";
  document.body.style.userSelect = "";

  // 移除全局事件
  document.removeEventListener("mousemove", handleMouseMove);
  document.removeEventListener("mouseup", handleMouseUp);
};

// 拖拽位置重置
const resetDragPosition = () => {
  currentDragPos.value = { x: 0, y: 0 };
  dragStartPos.value = { x: 0, y: 0 };
  dragStartOffset.value = { x: 0, y: 0 };
  isDragging.value = false;
};

// 监听 props 变化
watch(
  () => props.fullscreen,
  value => (isFullscreen.value = value)
);

// 全屏切换
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  emit("fullscreen-change", isFullscreen.value);
};

// 动态样式计算
const modalStyle = computed(() => {
  if (isFullscreen.value) {
    return getFullscreenStyle();
  }

  // 响应式宽度处理
  let width = props.width;
  if (typeof width === 'number') {
    const viewportWidth = window.innerWidth;
    if (viewportWidth < 768) {
      width = Math.min(width, viewportWidth * 0.95);
    } else if (viewportWidth < 1200) {
      width = Math.min(width, viewportWidth * 0.9);
    } else {
      width = Math.min(width, viewportWidth * 0.8);
    }
  }

  return {
    maxWidth: '100vw',
    maxHeight: '100vh'
  };
});

// 响应式body样式
const responsiveBodyStyle = computed(() => {
  const baseStyle = props.bodyStyle || {};
  const viewportHeight = window.innerHeight;

  // 根据屏幕高度动态计算最大高度
  let maxHeight;
  if (viewportHeight < 600) {
    maxHeight = viewportHeight * 0.6;
  } else if (viewportHeight < 900) {
    maxHeight = viewportHeight * 0.65;
  } else {
    maxHeight = viewportHeight * 0.7;
  }

  return {
    ...baseStyle,
    maxHeight: `${maxHeight}px`,
    overflow: 'auto'
  };
});

// 包装类名计算
const wrapClassName = computed(() =>
  joinClassNames(
    CONSTANTS.WRAP_CLASS_NAME,
    props.wrapClassName,
    isFullscreen.value ? "fullscreen" : ""
  )
);

// 事件处理函数
const handleOk = (e: MouseEvent) => {
  emit("ok", e);
};

const handleCancel = (e: MouseEvent) => {
  emit("cancel", e);
  emit("update:open", false);
};

const handleAfterClose = () => {
  emit("afterClose");
};

// 监听 visible 变化重置状态
watch(visible, value => {
  if (!value) {
    isFullscreen.value = props.fullscreen;
    // 对话框关闭时重置拖拽位置
    resetDragPosition();
  }
});

// 监听全屏状态变化，重置拖拽位置
watch(isFullscreen, value => {
  if (value) {
    // 进入全屏时重置拖拽位置
    resetDragPosition();
  }
});

// 计算transform样式 - 重新设计
const transformStyle = computed(() => {
  if (!props.draggable || isFullscreen.value) {
    return {};
  }

  return {
    transform: `translate(${currentDragPos.value.x}px, ${currentDragPos.value.y}px)`,
    transition: isDragging.value ? "none" : "transform 0.1s ease-out"
  };
});

// 视口变化监听
const handleViewportResize = () => {
  // 当视口变化时，重新计算样式
  // 这里可以触发响应式更新
};

// 生命周期
onMounted(() => {
  nextTick(() => {
    // 组件挂载后的初始化逻辑
    // 确保拖拽位置重置
    resetDragPosition();
  });

  // 添加视口变化监听
  window.addEventListener('resize', handleViewportResize);
});

onBeforeUnmount(() => {
  // 清理拖拽事件
  document.removeEventListener("mousemove", handleMouseMove);
  document.removeEventListener("mouseup", handleMouseUp);
  document.body.style.cursor = "";
  document.body.style.userSelect = "";

  // 移除视口变化监听
  window.removeEventListener('resize', handleViewportResize);
});

onBeforeUnmount(() => {
  // 清理拖拽事件
  document.removeEventListener("mousemove", handleMouseMove);
  document.removeEventListener("mouseup", handleMouseUp);
  document.body.style.cursor = "";
  document.body.style.userSelect = "";
});
</script>

<template>
  <a-modal
    v-model:open="visible"
    :width="props.width"
    :confirm-loading="props.confirmLoading"
    :ok-text="props.okText"
    :cancel-text="props.cancelText"
    :ok-type="props.okType"
    :ok-button-props="props.okButtonProps"
    :cancel-button-props="props.cancelButtonProps"
    :closable="props.closable"
    :mask-closable="props.maskClosable"
    :destroy-on-close="props.destroyOnClose"
    :centered="props.centered"
    :z-index="props.zIndex"
    :body-style="responsiveBodyStyle"
    :mask-style="props.maskStyle"
    :wrap-class-name="wrapClassName"
    :keyboard="props.keyboard"
    :style="modalStyle"
    :wrap-style="{ overflow: 'hidden' }"
    @ok="handleOk"
    @cancel="handleCancel"
    @after-close="handleAfterClose"
  >
    <!-- Title 区域 -->
    <template #title>
      <slot name="title">
        <div
          ref="modalTitleRef"
          class="trdialog-header"
          :class="{
            'trdialog-header-draggable': props.draggable && !isFullscreen
          }"
          :style="props.draggable && !isFullscreen ? { cursor: 'move' } : {}"
          @mousedown="handleMouseDown"
        >
          <!-- <div class="trdialog-header"> -->
          <div class="trdialog-title">{{ props.title }}</div>
          <div class="trdialog-actions">
            <a-button
              v-if="props.showFullscreenButton"
              type="text"
              size="small"
              @click="toggleFullscreen"
              class="trdialog-fullscreen-btn"
            >
              <template v-if="isFullscreen">
                <FullscreenExitOutlined />
              </template>
              <template v-else>
                <FullscreenOutlined />
              </template>
            </a-button>
          </div>
        </div>
      </slot>
    </template>

    <!-- 内容区域 -->
    <slot />

    <!-- Footer 区域 -->
    <template #footer>
      <slot name="footer">
        <template v-if="props.footer !== undefined">
          <component :is="props.footer" />
        </template>
        <template v-else-if="props.showFooter">
          <div class="trdialog-footer">
            <template
              v-if="props.footerButtons && props.footerButtons.length > 0"
            >
              <a-button
                v-for="(btn, idx) in props.footerButtons"
                :key="idx"
                :type="btn.type || 'default'"
                :loading="btn.loading"
                :danger="btn.danger"
                @click="btn.onClick"
                v-bind="btn.props"
              >
                {{ btn.text }}
              </a-button>
            </template>
            <template v-else>
              <a-button @click="handleCancel">
                {{ props.cancelText || CONSTANTS.DEFAULT_CANCEL_TEXT }}
              </a-button>
              <a-button
                :type="props.okType || 'primary'"
                :loading="props.confirmLoading"
                @click="handleOk"
              >
                {{ props.okText || CONSTANTS.DEFAULT_OK_TEXT }}
              </a-button>
            </template>
          </div>
        </template>
      </slot>
    </template>

    <!-- Modal渲染插槽 - 用于拖拽 -->
    <template #modalRender="{ originVNode }">
      <div ref="modalRef" :style="transformStyle">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>
</template>

<style lang="scss" scoped>
.trdialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  height: 100%;
  background: transparent;
  border-bottom: none;
  font-size: 16px;
  font-weight: 600;
  user-select: none;
  color: rgba(0, 0, 0, 0.88);

  &.trdialog-header-draggable {
    cursor: move;
  }
}

.trdialog-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 20px;
}

.trdialog-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.trdialog-fullscreen-btn {
  margin-right: 18px;
}

.trdialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  //padding: 16px 24px 0 24px;
  //border-top: 1px solid var(--gray-200, #e5e7eb);
  background: #fff;
}
</style>

<style lang="scss">
// 全屏模式样式 - 全局样式(不能放在scope中，会失效)
.trdialog-modal-wrap.fullscreen .ant-modal {
  height: 100vh !important;
  max-height: 100vh !important;
}

.trdialog-modal-wrap.fullscreen .ant-modal-content {
  height: 100vh !important;
  max-height: 100vh !important;
  display: flex;
  flex-direction: column;
}

.trdialog-modal-wrap.fullscreen .ant-modal-body {
  flex: 1;
  overflow: auto;
  padding: 0;
}

.trdialog-modal-wrap.fullscreen .ant-modal-header {
  flex-shrink: 0;
}

.trdialog-modal-wrap.fullscreen .ant-modal-footer {
  flex-shrink: 0;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .trdialog-modal-wrap .ant-modal {
    margin: 8px;
    max-width: calc(100vw - 16px);
  }

  .trdialog-modal-wrap .ant-modal-content {
    border-radius: 8px;
  }

  .trdialog-modal-wrap .ant-modal-body {
    padding: 12px;
  }

  .trdialog-modal-wrap .ant-modal-footer {
    padding: 12px 24px;
  }
}

@media (max-width: 480px) {
  .trdialog-modal-wrap .ant-modal {
    margin: 4px;
    max-width: calc(100vw - 8px);
  }

  .trdialog-modal-wrap .ant-modal-body {
    padding: 8px;
  }

  .trdialog-modal-wrap .ant-modal-footer {
    padding: 8px 16px;
  }

  .trdialog-title {
    font-size: 14px;
    margin-bottom: 16px;
  }
}
</style>
