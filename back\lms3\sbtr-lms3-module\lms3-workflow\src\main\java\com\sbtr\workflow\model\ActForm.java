package com.sbtr.workflow.model;

import cn.ewsd.common.model.MCoreBase;

import javax.persistence.Table;

/**
 * 常用外置表单数据主表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2019-08-16 17:33:52
 */
@Table(name = "act_form")
public class ActForm extends MCoreBase {
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    private String name;


    /**
     * 设置：名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取：名称
     */
    public String getName() {
        return name;
    }


}
