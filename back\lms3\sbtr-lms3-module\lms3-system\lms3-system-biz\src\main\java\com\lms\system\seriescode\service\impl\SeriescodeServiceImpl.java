/**
 * FileName:	ClientSeriescode.java
 * Author:		ji<PERSON><PERSON><PERSON>
 * Time:		2013-3-4����11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.system.seriescode.service.impl;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.DateHelper;
import com.lms.system.seriescode.mapper.SeriescodeMapper;
import com.lms.system.feign.model.Seriescode;
import com.lms.system.seriescode.service.SeriescodeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * seriescode服务类
 */
@Service("SeriescodeService")
public class SeriescodeServiceImpl extends BaseServiceImpl<SeriescodeMapper, Seriescode> implements SeriescodeService {

    @Resource
    private SeriescodeMapper seriescodeMapper;

    public String getNewCode(String key) {
        StringBuilder noStr = new StringBuilder();
        String DateStr = DateHelper.getCurrentDate().replaceAll("-", "");
        String deptCode = "";
        int codeLength = 5;
        synchronized (SeriescodeMapper.class) {
            LambdaQueryWrapper<Seriescode> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Seriescode::getNokey, key);
            wrapper.eq(Seriescode::getDay, DateStr);
            if (ObjectUtil.isNotEmpty(deptCode)) {
                wrapper.eq(Seriescode::getDeptcode, deptCode);
            }
            List<Seriescode> list = seriescodeMapper.selectList(wrapper);
            Seriescode seriescode;
            int newno = 1;
            if (list.size() > 0) {
                seriescode = list.get(0);
                newno = seriescode.getCurrentno() + 1;
                seriescode.setCurrentno(newno);
            } else {
                seriescode = new Seriescode(null, newno, key, DateStr, deptCode);
            }
            saveOrUpdate(seriescode);
            noStr = new StringBuilder(newno + "");
            while (noStr.length() < codeLength) {
                noStr.insert(0, "0");
            }
        }
		return key + DateStr + noStr;
    }

}
