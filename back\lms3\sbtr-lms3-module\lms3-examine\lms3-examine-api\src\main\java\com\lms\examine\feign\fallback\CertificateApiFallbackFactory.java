package com.lms.examine.feign.fallback;

import com.lms.common.model.Result;
import com.lms.examine.feign.api.CertificateApi;
import com.lms.examine.feign.model.Certificate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * misboot-mdata相关接口 模块降级处理
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
@Component
public class CertificateApiFallbackFactory implements FallbackFactory<CertificateApi> {

    private static final Logger log = LoggerFactory.getLogger(CertificateApiFallbackFactory.class);


    @Override
    public CertificateApi create(Throwable throwable) {
        log.error("certificateApi模块服务调用失败:{}", throwable.getMessage());
        return new CertificateApi() {

            @Override
            public Result<List<Certificate>> getByPlanId(String planId) {
                return null;
            }
        };
    }
}
