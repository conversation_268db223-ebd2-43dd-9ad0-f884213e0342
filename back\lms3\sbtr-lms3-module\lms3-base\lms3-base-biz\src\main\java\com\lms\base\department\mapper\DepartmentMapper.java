package com.lms.base.department.mapper;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.base.feign.model.Department;
import com.lms.common.feign.dto.DepartmentTree;
import io.lettuce.core.dynamic.annotation.Param;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.elasticsearch.annotations.Query;

/**
 * <AUTHOR>
 * 人员信息存储层接口
 */
@Mapper
public interface DepartmentMapper extends BaseMapper<Department> {

    @Select("${sql}")
    List<Department> getValidDepartmentsByPid(@Param("sql") String sql);

    @Select("${sql}")
    List<Department> getAllDepartmentsByPid(@Param("sql") String sql);
}
