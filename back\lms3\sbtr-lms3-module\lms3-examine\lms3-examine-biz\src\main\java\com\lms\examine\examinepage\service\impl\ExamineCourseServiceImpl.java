/**
 * FileName:	ClientExaminepage.java
 * Author:		ji<PERSON><PERSON><PERSON>
 * Time:		2013-3-4上午11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.examine.examinepage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.examine.examinepage.mapper.ExamineCourseMapper;
import com.lms.examine.examinepage.service.ExamineCourseService;
import com.lms.examine.feign.model.Examinecourse;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 保存项目的相关信息
 */
@Service("ExamineCourseService")
public class ExamineCourseServiceImpl extends BaseServiceImpl<ExamineCourseMapper, Examinecourse> implements ExamineCourseService {

    @Resource
    private ExamineCourseMapper examineCourseMapper;

    public List<Examinecourse> getbyExaminePageId(String examineid) {
        LambdaQueryWrapper<Examinecourse> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Examinecourse::getExamineid, examineid);
        return this.list(wrapper);
    }

    public void batchDeleteByExaminePageId(String examinepageid) {
        LambdaQueryWrapper<Examinecourse> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Examinecourse::getExamineid, examinepageid);
        List<Examinecourse> list = this.list(wrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> ids = list.stream().map(Examinecourse::getId).collect(Collectors.toList());
            this.removeBatchByIds(ids);
        }
    }
}

