export default class CustomPalette {
  constructor(create, elementFactory, palette, translate) {
    this.create = create;
    this.elementFactory = elementFactory;
    this.translate = translate;
    palette.registerProvider(this);
  }
  getPaletteEntries() {
    const { create, elementFactory, translate } = this;

    function createServiceTask(event) {
      const shape = elementFactory.createShape({ type: "bpmn:UserTask" });
      create.start(event, shape);
    }
    // 创建流程设计页左侧palette面板流程活动节点
    // function createCallActivity(event) {
    //     const shape = elementFactory.createShape({ type: 'bpmn:CallActivity' });
    //     console.log(event,elementFactory,shape);
    //     create.start(event, shape);
    // }

    function createAction(type, group, className, title, options) {
      function createListener(event) {
        const shape = elementFactory.createShape({ type, ...options });
        create.start(event, shape);
      }
      return {
        group,
        className,
        title: title || translate(`Create ${type.replace("bpmn:", "")}`),
        action: { dragstart: createListener, click: createListener }
      };
    }

    return {
      // "create.user-task": {
      //   group: "activity",
      //   className: "bpmn-icon-user-task",
      //   title: translate("Create UserTask"),
      //   action: {
      //     dragstart: createServiceTask,
      //     click: createServiceTask
      //   }
      // }
    };
  }
}

CustomPalette.$inject = ["create", "elementFactory", "palette", "translate"];
