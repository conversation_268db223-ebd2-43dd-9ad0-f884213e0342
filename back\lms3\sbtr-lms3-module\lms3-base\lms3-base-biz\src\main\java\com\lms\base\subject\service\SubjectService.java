package com.lms.base.subject.service;

import com.lms.base.feign.model.Subject;
import com.lms.common.service.BaseService;

import java.util.List;
import java.util.Map;

public interface SubjectService extends BaseService<Subject> {

    void resetKKD(Subject subject);

    List<String> getRandomIds(Map conditonMap) throws Exception;

    String checkImportData(List<Subject> subjectList, int cklen);

    int saveImportData(List<Subject> subjectList, String courseId);

    void calcSubjectKKD(List list);

    List<Subject> getPlacticeSubjectList(String ordertype, List<String> courseidList);

    List getSubjectAnalysisByType(String analysetype);

    void parseContent(Subject subject);

    void parseItems(Subject subject);
}
