# TrTable 通用表格组件

## 组件简介

`TrTable` 是基于 Ant Design Vue 封装的高扩展性表格组件，支持查询、批量操作、自定义按钮、分页、排序、行选择、操作列、插槽扩展等功能，适用于中后台管理系统的通用数据展示与操作场景。

---

## 快速上手

```vue
<template>
  <TrTable
    :columns="columns"
    :dataSource="data"
    @query="onQuery"
    @edit="onEdit"
    @delete="onDelete"
  />
</template>

<script setup lang="ts">
import TrTable from './index.vue';
import { ref } from 'vue';

const columns = [
  { title: 'ID', dataIndex: 'id' },
  { title: '姓名', dataIndex: 'name' },
  { title: '年龄', dataIndex: 'age' }
];
const data = ref([
  { id: 1, name: '张三', age: 18 },
  { id: 2, name: '李四', age: 20 }
]);

function onQuery(params) { /* ... */ }
function onEdit(record) { /* ... */ }
function onDelete(record) { /* ... */ }
</script>
```

---

## Props 参数

| 参数名              | 类型                                      | 默认值         | 说明 |
|---------------------|-------------------------------------------|----------------|------|
| columns             | TableColumn[]                             | 必填           | 表格列配置 |
| dataSource          | TableRecord[]                             | 必填           | 数据源 |
| rowKey              | string \| (record) => string               | 'id'           | 行唯一标识 |
| queryItems          | QueryItem[]                               | []             | 查询表单项配置 |
| customButtons       | CustomButton[]                            | []             | 自定义按钮区按钮 |
| operationButtons    | OperationButton[]                         | []             | 操作列自定义按钮 |
| scroll              | { x?: string\|number; y?: string\|number }| { x: 1200 }    | 表格滚动配置 |
| showQuery           | boolean                                   | true           | 是否显示查询区 |
| showRefresh         | boolean                                   | false          | 是否显示刷新按钮 |
| showBatchDelete     | boolean                                   | true           | 是否显示批量删除按钮 |
| showAdd             | boolean                                   | true           | 是否显示新增按钮 |
| showImport          | boolean                                   | false          | 是否显示导入按钮 |
| showExport          | boolean                                   | true           | 是否显示导出按钮 |
| showView            | boolean                                   | true           | 是否显示查看按钮（操作列） |
| showEdit            | boolean                                   | true           | 是否显示编辑按钮（操作列） |
| showDelete          | boolean                                   | true           | 是否显示删除按钮（操作列） |
| showIndex           | boolean                                   | true           | 是否显示序号列 |
| showCheckbox        | boolean                                   | true           | 是否显示多选框 |
| showOperation       | boolean                                   | true           | 是否显示操作列 |
| loading             | boolean                                   | false          | 加载中状态 |
| bordered            | boolean                                   | true           | 是否显示边框 |
| size                | 'small'\|'middle'\|'default'             | 'default'      | 表格尺寸 |
| tableLayout         | 'auto'\|'fixed'                          | 'auto'         | 表格布局 |
| title               | string \| (data) => string                 | -              | 表格标题 |
| footer              | string \| (data) => string                 | -              | 表格底部 |
| summary             | (data) => any                             | -              | 表格汇总行 |
| id                  | string                                    | -              | 表格 id |
| rowClassName        | string \| (record, idx) => string          | -              | 行样式类名 |
| rowSelection        | object                                    | -              | 行选择配置（透传） |
| customRow           | (record, idx) => object                   | -              | 自定义行属性 |
| customHeaderRow     | (column, idx) => object                   | -              | 自定义表头行属性 |
| pagination          | PaginationState                           | {pageIndex:1,pageSize:10,total:0} | 分页配置 |
| resizable           | boolean                                   | true           | 是否可拖拽列宽 |
| onResizeColumn      | (w, col) => void                          | -              | 列宽调整回调 |

> 详细类型定义见 `types.ts`，如 TableColumn、QueryItem、CustomButton、OperationButton、PaginationState、TableRecord 等。

---

## 事件说明

| 事件名             | 参数                                      | 说明 |
|--------------------|-------------------------------------------|------|
| query              | (queryForm)                               | 查询按钮点击/分页/排序时触发，参数为当前查询表单数据 |
| reset              | ()                                        | 重置按钮点击时触发 |
| refresh            | ()                                        | 刷新按钮点击时触发 |
| batchDelete        | (rows, keys)                              | 批量删除按钮点击时触发，参数为选中行和 key |
| add                | ()                                        | 新增按钮点击时触发 |
| export             | (keys, rows)                              | 导出按钮点击时触发，参数为选中 key 和行 |
| edit               | (record)                                  | 编辑按钮点击时触发 |
| delete             | (record)                                  | 删除按钮点击时触发 |
| customButton       | (key)                                     | 自定义按钮点击时触发 |
| operationButton    | ({ key, record })                         | 操作列自定义按钮点击时触发 |
| queryItemEvent     | ({ key, eventName, value, args })          | 查询项组件事件透传 |
| change             | (pagination, filters, sorter, extra)       | 表格 change 事件透传 |
| expand             | (expanded, record)                        | 行展开事件 |
| expandedRowsChange | (expandedRows)                            | 展开行变化事件 |
| row                | (record, idx)                             | 行属性事件 |
| headerRow          | (column, idx)                             | 表头行属性事件 |
| paginationChange   | (pagination)                              | 分页变化事件 |
| sortChange         | (sort)                                    | 排序变化事件 |
| stateChange        | (state)                                   | 表格状态变化事件（分页+排序） |
| resizeColumn       | (width, column)                           | 列宽调整事件 |

---

## 插槽说明

| 插槽名         | 说明 |
|----------------|------|
| queryArea      | 查询区扩展插槽，参数：{ queryForm, pagination, sort } |
| buttonArea     | 按钮区扩展插槽 |
| operation      | 操作列自定义插槽，参数：{ record } |
| 默认插槽       | 表格内容自定义 |

---

## 暴露方法（defineExpose）

| 方法名              | 参数/返回值                | 说明 |
|---------------------|---------------------------|------|
| getPaginationState  | () => PaginationState     | 获取当前分页状态 |
| getSortState        | () => SortState           | 获取当前排序状态 |
| getTableState       | () => TableState          | 获取表格完整状态（分页+排序） |
| setPaginationState  | (pagination)              | 设置分页状态 |
| setSortState        | (sort)                    | 设置排序状态 |
| setTableState       | (state)                   | 设置表格完整状态 |
| setTotal            | (total)                   | 设置总条数 |
| resetPagination     | ()                        | 重置分页 |
| resetSort           | ()                        | 重置排序 |
| resetTableState     | ()                        | 重置表格状态 |
| getQueryForm        | () => object              | 获取查询表单数据 |
| setQueryForm        | (form)                    | 设置查询表单数据 |
| resetQueryForm      | ()                        | 重置查询表单 |
| getSelectedRows     | () => TableRecord[]       | 获取选中行数据 |
| getSelectedRowKeys  | () => any[]               | 获取选中行 key |
| clearSelection      | ()                        | 清空选中行 |
| goToPage            | (pageIndex)               | 跳转到指定页 |
| setPageSize         | (pageSize)                | 设置每页条数并跳转到第一页 |

---

## 常见用法示例

### 1. 自定义操作列按钮

```vue
<TrTable
  :columns="columns"
  :dataSource="data"
  :operationButtons="[
    { key: 'custom', label: '自定义', props: { type: 'link' } }
  ]"
  @operationButton="({ key, record }) => handleCustom(key, record)"
/>
```

### 2. 查询区自定义扩展

```vue
<template #queryArea="{ queryForm }">
  <a-button @click="doSomething(queryForm)">自定义查询</a-button>
</template>
```

### 3. 自定义渲染列

```js
const columns = [
  {
    title: '头像',
    dataIndex: 'avatar',
    renderType: 'img'
  },
  {
    title: '状态',
    dataIndex: 'status',
    renderType: 'status',
    statusMap: { 1: '启用', 0: '禁用' }
  },
  {
    title: '操作',
    dataIndex: 'operation',
    // ...
  }
];
```

### 4. 分页与排序

```vue
<TrTable
  :columns="columns"
  :dataSource="data"
  :pagination="{ pageIndex: 1, pageSize: 20, total: 100 }"
  @paginationChange="onPageChange"
  @sortChange="onSortChange"
/>
```

---

## 样式与响应式

- 组件自带基础样式。
- 查询区、按钮区、表格区、分页区均有独立样式。
- 支持移动端响应式布局。
- 支持样式穿透（如表头、选中行、固定列等）。

---

## 注意事项

- `columns`、`dataSource` 必填。
- 若需自定义渲染，建议使用 `renderType` 或 `render`。
- 分页、排序、查询等均为受控，需配合事件和外部数据源使用。
- 详细类型定义请参考 `types.ts` 文件。

---
