package com.sbtr.workflow.mapper;

import com.sbtr.workflow.bean.WorkflowMapper;
import com.sbtr.workflow.model.SysRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统角色  Mapper
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-12-15 17:10:30
 */
public interface SysRoleMapper extends WorkflowMapper<SysRole> {


    SysRole getListByGroupStr(@Param("filterSort") List<String> list);

    Integer getMaxById();

    List<SysRole> getListBySort();

    List<SysRole> getListByLevelId(String levelId);

    List<SysRole> getListByCodeSetIdLevelId(String codeSetId, String levelId);

    List<SysRole> getListByPid(String pid);

    List<SysRole> getListByUuid(@Param("list") List<String> list);

    Integer deleteSysAuthAccessByroleId(@Param("id") Integer id);

    List<SysRole> getPageSetData(@Param("roleName") String roleName);

    List<SysRole> getList();

    List<SysRole> getListByUserUuid(@Param("userUuid") String currentUuid);

    List<SysRole> getListByText(@Param("text") String text);

    Integer getListByNameAndUuid(@Param("text") String text, @Param("uuid") String uuid);

    Integer roleIdExistsUser(@Param("id") Integer id);


    Integer roleIdExistsUserOracle(@Param("id") Integer id);
}
