import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 查询课件信息
export const getCoursewareList = (data?: any) => {
  return http.request<any>("post", baseUrlApi("courseware/listpage/"), {
    data
  });
};

// 新增课件信息
export const addCourseware = (data?: any) => {
  return http.request<any>("post", baseUrlApi("courseware/add"), { data });
};

// 更新课件信息
export const updateCourseware = (data?: any) => {
  return http.request<any>("post", baseUrlApi("courseware/save"), { data });
};

// 删除课件信息
export const deleteCourseware = (ids: string) => {
  return http.request<any>(
    "delete",
    baseUrlApi("courseware/delete?ids=" + ids)
  );
};

// 导出课件信息
export const exportCourseware = (data?: string[]) => {
  return http.request<any>(
    "post",
    baseUrlApi("courseware/exportCourseWareByIds"),
    { data }
  );
};

// 查询课程信息
export const getCourseList = (data?: any) => {
  return http.request<any>("post", baseUrlApi("course/list"), { data });
};

//  分页查询课程信息
// export const getCourseListPage = (data?: any) => {
//   return http.request<any>(
//     "post",
//     baseUrlApi("course/listByConditionCourseware"),
//     { data }
//   );
// };

// 新增课程信息
export const addCourse = (data?: any) => {
  return http.request<any>("post", baseUrlApi("course/add"), { data });
};

// 编辑课程信息
export const editCourse = (data?: any) => {
  return http.request<any>("post", baseUrlApi("course/edit"), { data });
};

// 批量删除课程信息
export const deleteCourse = (ids: string) => {
  return http.request<any>("delete", baseUrlApi("course/delete?ids=" + ids));
};

// 发布课程
export const releaseCourse = (data?: any) => {
  return http.request<any>(
    "get",
    baseUrlApi("course/batchrelease?ids=" + data)
  );
};
