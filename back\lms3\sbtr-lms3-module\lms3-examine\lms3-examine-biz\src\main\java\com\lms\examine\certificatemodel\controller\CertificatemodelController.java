package com.lms.examine.certificatemodel.controller;

import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.StringHelper;
import com.lms.examine.certificatemodel.service.CertificatemodelService;
import com.lms.system.feign.api.AttachApi;
import com.lms.examine.feign.model.Certificate;
import com.lms.examine.certificate.service.impl.CertificateServiceImpl;
import com.lms.examine.feign.model.Certificatemodel;
import com.lms.examine.certificatemodel.service.impl.CertificatemodelServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/certificatemodel")
@Api(value = "证书模板管理", tags = "证书模板管理")
public class CertificatemodelController extends BaseController<Certificatemodel> {

    @Resource
    private CertificatemodelService certificatemodelService;

    @Resource
    private CertificateServiceImpl certificateService;

    protected static final Logger logger = LoggerFactory
            .getLogger(CertificatemodelController.class);

    @LMSLog(desc = "查询证书模板", otype = LogType.List, order = 1, method = "createCertificatemodelLog")
    @PostMapping(value = {"/listpage"})
    @ApiOperation(value = "查询证书模板", httpMethod = "POST")
    public Result getCertificatemodelList(@RequestBody JSONObject request) {
        PageInfo pageInfo = super.getPageInfo(request);
        Page<Certificatemodel> certificatemodelList = certificatemodelService.listByCondition(pageInfo);
        return Result.OK(certificatemodelList);
    }

    @RequestMapping(value = {"/listAll"}, method = RequestMethod.GET)
    @ApiOperation(value = "查询全部证书模板", httpMethod = "GET")
    public Result getAllExaminepage() {
        List<Certificatemodel> certificatemodelList = this.certificatemodelService.list();
        return Result.OK(certificatemodelList);
    }

    @LMSLog(desc = "新增证书模板", otype = LogType.Save, order = 1, method = "createCertificatemodelLog")
    @RequestMapping(value = {"/add"}, method = RequestMethod.POST)
    @ApiOperation(value = "新增证书模板", httpMethod = "POST")
    public Result saveCertificatemodel(@RequestBody Certificatemodel certificatemodel) {
        boolean isEmpty = certificatemodelService.existName(certificatemodel).size() == 0;
        if (!isEmpty) {
            return Result.error(commonSystemApi.translateContent("操作失败，已存在相同模板名称！"));
        } else {
            certificatemodel.setCreator(ContextUtil.getPersonId());
            this.certificatemodelService.saveOrUpdate(certificatemodel);
        }
        return Result.OK(certificatemodel);
    }


    @LMSLog(desc = "编辑证书模板", otype = LogType.Update, order = 1, method = "createCertificatemodelLog")
    @RequestMapping(value = {"/modify"}, method = RequestMethod.POST)
    @ApiOperation(value = "编辑证书模板", httpMethod = "POST")
    public Result updateCertificatemodel(@RequestBody Certificatemodel certificatemodel) {
        boolean isEmpty = certificatemodelService.existName(certificatemodel).size() == 0;
        if (!isEmpty) {
            return Result.error(commonSystemApi.translateContent("操作失败，已存在相同模板名称！"));
        } else {
            certificatemodelService.saveOrUpdate(certificatemodel);
        }
        return Result.OK(certificatemodel);

    }

    @LMSLog(desc = "删除证书模板", otype = LogType.Delete, order = 1, method = "createCertificatemodelLog")
    @RequestMapping(value = {"/delete"}, method = RequestMethod.DELETE)
    @ApiOperation(value = "删除证书模板", httpMethod = "DELETE")
    public Result batchDeleteCertificatemodel(@RequestParam("ids") String ids) {
        String[] idList = ids.split(",");
        String errorMsg = "";
        for (String id : idList) {
            Certificatemodel certificatemodel = certificatemodelService.getById(id);
            String name = certificatemodel.getName();
            List<Certificate> list = certificateService.getByModelId(id);
            if (list.size() != 0) {// 校验模板是否已添加证书
                if (errorMsg.isEmpty()) {
                    errorMsg = name;
                } else {
                    errorMsg = errorMsg + "," + name;
                }
            } else {
                this.certificatemodelService.removeById(id);
            }
        }
        if (!StringHelper.isEmpty(errorMsg)) {
            errorMsg = commonSystemApi.translateContent("[?]模板还关联证书，不能删除。", "", errorMsg);
        }
        if (!StringHelper.isEmpty(errorMsg)) {
            return Result.error(errorMsg);
        } else {
            return Result.OK();
        }
    }

    public String createCertificatemodelLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.List)) {
            objname = "证书模板列表";
        } else if (lmslog.otype().equals(LogType.Save)) {
            Certificatemodel obj = (Certificatemodel) (args[0]);
            objname = StringHelper.null2String(obj.getName());
        } else if (lmslog.otype().equals(LogType.Update)) {
            Certificatemodel obj = (Certificatemodel) (args[0]);
            objname = StringHelper.null2String(obj.getName());
        } else if (lmslog.otype().equals(LogType.Delete)) {
            String ids = (String) (args[0]);
            String[] idarray = ids.split(",");
            for (String id : idarray) {
                Certificatemodel obj = certificatemodelService.getById(id);
                objname = objname.isEmpty() ? StringHelper.null2String(obj.getName()) : objname + "," + StringHelper.null2String(obj.getName());
            }
        }
        return objname;
    }
}
