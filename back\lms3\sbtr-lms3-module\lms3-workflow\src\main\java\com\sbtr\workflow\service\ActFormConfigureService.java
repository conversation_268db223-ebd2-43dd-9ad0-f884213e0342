package com.sbtr.workflow.service;

import com.sbtr.workflow.model.ActFormConfigure;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;

/**
 * 常用外置表单数据主表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:35:23
 */
public interface ActFormConfigureService extends WorkflowBaseService<ActFormConfigure, String> {

    /**
     * 分页
     *
     * @return
     * @Param pageParam 分页参数
     * @Param filterSort 过滤排序字段
     * @Param puuid 关联字段
     * @Param name 名称
     */
    PageSet<ActFormConfigure> getPageSet(PageParam pageParam, String filterSort,String puuid,String name,String status);

    /**
     * 批量删除
     *
     * @return
     * @Param uuid 主键
     */
    int executeDeleteBatch(String[] uuid);

    /**
     * 根据uuid更新状态
     *
     * @Param uuid
     * @Param status
     * @return int
     */
    int updateStatusByUuid(String uuid, String status);
}
