package com.lms.common.feign.dto;

import com.lms.common.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;

/**
 * PPerson entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Person extends BaseModel {

    private String id;

    private String name; //姓名

    private String departmentid; //部门id

    private String departmentidfullpath; //部门id全路径

    private String departmentids; //部门

    private String duty; //岗位

    private String specialityid; //专业

    private String personlevelid;

    private String specialitylevelid;

    private String description;

    private String sex;

    private String nation;

    private String tel;

    private String email;

    private String graduate;

    private String cardNum;

    private String persontype;

    private String image;

    private String imageUrl;

    private String trainorg;

    private String username;

    private String password;

    /**
     * 限制用户登录ip地址
     */
    private String ipaddr;

    /**
     * 限制用户登录浏览器类型
     */
    private String clientagent;

    private String education; //学位

    private String job; //职务

    private Department department;

    private String departmentname;

    private Integer status;

    private Set<Department> departments;

    private Integer trainnum;

    private String traincontent;

    private String modelnum; //维护型号

    private Integer slevel;

    /**
     * 出生年月
     */
    private String birthday;

    /**
     * 工作年份
     */
    private String seniority;


    /**
     * 备注
     */
    private String remark;

    /**
     * 工作年限
     */
    private String years;

    /**
     * 专业及主要成果
     */
    private String majors;

    /**
     * 教学型号
     */
    private String teachingmodel;

    private List<String> lastteachingmodellist;

    private String teachingmodelname;

    private String managemodel;

    private List<String> lastmanagemodellist;

    private String managemodelname;

    private String equipmentid;

    private String equipmentname;

    private List<String> lastequipmentidlist;

    /**
     * 教员类别 理论 实操
     */
    private String teachercategory;

    /**
     * 教员等级  初级教员、中级教员、高级教员
     */
    private String teacherlevel;

    /**
     * 教学课程分类
     */
    private String teachercourse;

    /**
     * 教学课程分类
     */
    private Integer examinefailtimes;

}
