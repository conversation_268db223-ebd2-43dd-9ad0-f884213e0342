package com.sbtr.workflow.mapper;

import com.sbtr.workflow.bean.WorkflowMapper;
import com.sbtr.workflow.model.SysPosition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 岗位表 Mapper
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-08-01 14:55:46
 */
public interface SysPositionMapper extends WorkflowMapper<SysPosition> {

    List<String> getPositionText(String[] uuid);

    String getPosition(String uuid);

    List<SysPosition> getPageSetData(@Param("positionName") String positionName);
}
