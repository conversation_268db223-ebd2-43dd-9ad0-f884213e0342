/**
 * FileName:	ClientPerson.java
 * Author:		<PERSON><PERSON><PERSON><PERSON>
 * Time:		2013-3-4上午11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.base.person.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.base.component.service.ComponentService;
import com.lms.base.department.service.DepartmentService;
import com.lms.base.feign.model.Department;
import com.lms.base.feign.model.Component;
import com.lms.base.person.mapper.PersonMapper;
import com.lms.base.feign.model.Person;
import com.lms.base.person.service.PersonService;
import com.lms.base.person.vo.Student;
import com.lms.base.selectitem.mapper.SelectitemMapper;
import com.lms.base.feign.model.Selectitem;
import com.lms.base.selectitem.service.SelectitemService;
import com.lms.base.feign.model.Sysroleuserlink;
import com.lms.base.sysroleuserlink.service.SysroleuserlinkService;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.dao.query.Parameter;
import com.lms.common.enums.RoleEum;
import com.lms.common.feign.dto.Setting;
import com.lms.common.feign.dto.Users;
import com.lms.common.model.Result;
import com.lms.common.model.SettingConfig;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.ConstParamUtil;
import com.lms.common.util.DateHelper;
import com.lms.common.util.EncryptHelper;
import com.lms.common.util.StringHelper;
import com.lms.system.feign.api.AttachApi;
import com.lms.system.feign.api.LmsSystemApi;
import com.lms.system.feign.model.Attach;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 保存项目的相关信息
 */
@Service("PersonService")
public class PersonServiceImpl extends BaseServiceImpl<PersonMapper, Person> implements PersonService {

    @Resource
    private PersonMapper personDao;
    @Resource
    private SysroleuserlinkService sysroleuserlinkService;
    @Resource
    private SelectitemService selectitemService;
    @Resource
    private SelectitemMapper selectitemMapper;
    @Resource
    private AttachApi attachApi;
    @Resource
    private ComponentService componentService;
    @Resource
    private DepartmentService departmentService;
    @Resource
    private LmsSystemApi lmsSystemApi;

    public void setPersonImage(Person person) {
        if (person != null) {
            String imgid = StringHelper.null2String(person.getImage());
            if (!imgid.isEmpty()) {
                Result<Attach> attachResult = attachApi.get(imgid);
                if (attachResult == null) {
                    return;
                }
                Attach image = attachResult.getResult();
                if (image != null) {
                    String imgsrc = StringHelper.null2String(image.getFiledir());
                    person.setImageUrl(imgsrc);
                }
            }
        }
    }

    public List<Person> findByPersonName(String personname) {
        LambdaQueryWrapper<Person> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Person::getName, personname);
        wrapper.eq(Person::getStatus, 1);
        return this.list(wrapper);
    }

    public Person getPersonById(String id) {
        return this.getById(id);
    }

    public List<Person> getValidPersons() {
        LambdaQueryWrapper<Person> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Person::getStatus, 1);
        return this.list(wrapper);
    }


    public boolean existPersonByCardNum(String cardNum, String personId) {
        return getPersonByCardNum(cardNum, personId).size() > 0;
    }

    public List<Person> getPersonByCardNum(String cardNum, String personId) {
        PageInfo pageInfo = new PageInfo(10, 1);
        Parameter cardNumParam = Parameter.getParameter(
                "S_EQ_cardNum", cardNum);
        Parameter statusParam = Parameter.getParameter(
                "I_EQ_status", 1);
        Parameter idParam = Parameter.getParameter(
                "S_NE_id", personId);
        if (!StringHelper.isEmpty(personId)) {
            pageInfo.getParameters().add(idParam);
        }
        pageInfo.getParameters().add(cardNumParam);
        pageInfo.getParameters().add(statusParam);
        Page<Person> page = this.listByCondition(pageInfo);
        return page.getRecords();
    }

    public boolean existPersonByCardNumAndDepartmentId(String cardNum, String departmentId) {
        PageInfo pageInfo = new PageInfo(10, 1);
        Parameter cardNumParam = Parameter.getParameter(
                "S_EQ_cardNum", cardNum);
        Parameter departmentIdParam = Parameter.getParameter(
                "S_EQ_departmentid", departmentId);
        Parameter statusParam = Parameter.getParameter(
                "I_EQ_status", 1);
        pageInfo.getParameters().add(cardNumParam);
        pageInfo.getParameters().add(departmentIdParam);
        pageInfo.getParameters().add(statusParam);
        List<Person> personList = this.listByCondition(pageInfo).getRecords();
        return personList.size() > 0;
    }

    public List getTeacherAnalysis(String analysetype) {
        String sql = "select "
                + analysetype
                + " ,count(id) as amount from p_person  where status=1 and persontype='" + RoleEum.teacher.getValue() + "' group by "
                + analysetype + " order by " + analysetype;
        if (analysetype.equals("departmentid")) {
            sql = "select p.departmentid,count(p.id) amount, d.name from p_person p left join p_department d on d.id=p.departmentid where p.status=1 and p.persontype='" + RoleEum.teacher.getValue() + "' group by p.departmentid,d.name order by d.name ";
        }
        return personDao.getTeacherAnalysis(sql);
    }

    public List<Object> readExcelFile(String path, String departmentId) {
        File excel = new File(path);
        return readExcelFile(excel, departmentId);
    }

    public List<Object> readExcelFile(File excel, String departmentId) {
        int colNum;
        List<Object> resultInformation = new ArrayList<Object>();
        HSSFWorkbook workbook_2003; //2003以下；
        XSSFWorkbook workBook_2007;    //2007以上
        try {
            if (excel.isFile() && excel.exists()) {
                String[] split = excel.getName().split("\\.");
                if ("xls".equals(split[1])) {
                    FileInputStream fileStream = new FileInputStream(excel);
                    workbook_2003 = new HSSFWorkbook(fileStream);
                    HSSFSheet sheet = workbook_2003.getSheetAt(0);
                    //workbook_2003.getS
                    HSSFRow row = sheet.getRow(1);//列名
                    //1.获取标题总列数和列名
                    colNum = row.getPhysicalNumberOfCells();
                    //总列数为4:模板提供的是4列；
                    if (colNum == 11) {
                        //得到总行数
                        int rowNum = sheet.getLastRowNum();
                        //2.读取excel表中的内容
                        for (int i = 2; i <= rowNum; ++i) {
                            row = sheet.getRow(i);
                            //证件号
                            Cell cell_cardNum = row.getCell(1);
                            Object object_cardNum = getCellFormatValue(cell_cardNum);
                            if (!object_cardNum.toString().isEmpty()) {
                                String cardNum = object_cardNum.toString();
                                if (this.existPersonByCardNumAndDepartmentId(cardNum, departmentId)) {
                                    //当前部门目录下存在重复证件号信息；导入失败！，只能导入新的人员信息！更新信息通过修改按钮；
                                    resultInformation.add(commonSystemApi.translateContent("证件号编号为[?]条目导入失败，当前目录下存在重复证件号信息，请联系管理员!", "", cardNum));
                                } else {
                                    //如果其他目录下存在人员信息，将其状态设置为无效；
                                    //证件号重复的很少。

                                    String name = getCellFormatValue(row.getCell(0)).toString();
                                    String sexName = getCellFormatValue(row.getCell(2)).toString();
//                                    String nation = getCellFormatValue(row.getCell(3)).toString();
                                    Selectitem es = selectitemMapper.getSelectitemByTypeName(//维护机型
                                            StringHelper.null2String(row.getCell(3)),
                                            "d89ac263bd614b93af0dd9a4a7faef25");
                                    Selectitem xh = selectitemMapper.getSelectitemByTypeName(//型号
                                            StringHelper.null2String(row.getCell(4)),
                                            "d89ac263bd614b93af0dd9a4a7faef78");
                                    Selectitem ss = selectitemMapper.getSelectitemByTypeName(//专业
                                            StringHelper.null2String(row.getCell(5)),
                                            "bdce8a6593884df785dd26ec70c6f6ef");
                                    Selectitem duty = selectitemMapper.getSelectitemByTypeName(//专业
                                            StringHelper.null2String(row.getCell(6)),
                                            "77176e0fb83543a6b823f051833c1ef5");

                                    String job = getCellFormatValue(row.getCell(7)).toString();
                                    String tel = getCellFormatValue(row.getCell(8)).toString();
                                    String trainnum = getCellFormatValue(row.getCell(9)).toString();
                                    String traincontent = getCellFormatValue(row.getCell(10)).toString();
                                    if (es.getObjname() == null) {
                                        resultInformation.add(commonSystemApi.translateContent("装备机型名称为[?]条目导入失败，代码表无此数据!", "", StringHelper.null2String(row.getCell(3))));
                                    }
                                    if (xh.getObjname() == null) {
                                        resultInformation.add(commonSystemApi.translateContent("型号名称为[?]条目导入失败，代码表无此数据!", "", StringHelper.null2String(row.getCell(4))));
                                    }
                                    if (ss.getObjname() == null) {
                                        resultInformation.add(commonSystemApi.translateContent("专业名称为[?]条目导入失败，代码表无此数据!", "", StringHelper.null2String(row.getCell(5))));
                                    }
                                    if (duty.getObjname() == null) {
                                        resultInformation.add(commonSystemApi.translateContent("岗位名称为[?]条目导入失败，代码表无此数据!", "", StringHelper.null2String(row.getCell(6))));
                                    }

                                    if (StringHelper.isEmpty(name) || StringHelper.isEmpty(cardNum)) {
                                        resultInformation.add(commonSystemApi.translateContent("第[?]条数据姓名或者证件号为空，当前条目导入失败", "", String.valueOf(i + 1)));
                                    } else {
                                        String sexId = "";
                                        if (!StringHelper.isEmpty(sexName)) {
                                            Selectitem selectitem = selectitemService.getSelectitemByTypeName(sexName, ConstParamUtil.SexTypeId);
                                            if (selectitem != null) {
                                                sexId = selectitem.getId();
                                            }
                                        }
                                        List<Person> personList = this.getPersonByCardNum(cardNum, "");
                                        if (personList != null && personList.size() > 0) {//当前证件号人员已存在，更新人员信息（部门信息要相加）
                                            for (Person person : personList) {
                                                person.setName(name);
//                                                person.setNation(nation);
                                                person.setSex(sexId);
                                                person.setEquipmentid(es.getId());
                                                person.setModelnum(xh.getId());
                                                person.setSpecialityid(ss.getId());
                                                person.setDuty(duty.getId());
                                                person.setJob(job);
                                                person.setTel(tel);
                                                person.setTrainnum(Integer.parseInt(trainnum));
                                                person.setTraincontent(traincontent);
                                                String departmentids = person.getDepartmentids();
                                                person.setDepartmentid(departmentId);
                                                if (departmentids.indexOf(departmentId) < 0) {
                                                    departmentids = departmentids.isEmpty() ? departmentId : departmentId + "," + departmentids;
                                                    person.setDepartmentids(departmentids);
                                                }
                                                this.saveOrUpdate(person);
                                                Users user = Optional.ofNullable(lmsSystemApi.getUsersByUserName(cardNum).getResult()).orElse(new Users());
                                                String userPassword = StringHelper.null2String(person.getPassword(), EncryptHelper.md5Password("123456"));
                                                user.setName(cardNum);
                                                user.setPassword(userPassword);
                                                user.setPsdupdatedate(DateHelper.getCurrentDate());
                                                user.setIpaddr(person.getIpaddr());
                                                user.setClientagent(person.getClientagent());
                                                user.setPersonid(person.getId());//关联信息为当前有效ID，学习考核信息关联证件号
                                                user.setIslocked(false);
                                                user.setEnable(true);
                                                user.setUsertype(1);
                                                user.setIsfirstlogin(true);
                                                user.setStatus(1);
                                                user.setPsderrortimes(0);
                                                lmsSystemApi.saveUser(user);
                                            }
                                        } else {
                                            // 当前证件号人员不存在，新增人员。
                                            Person person = new Person();
                                            person.setName(name);
//                                            person.setNation(nation);
                                            person.setEquipmentid(es.getId());
                                            person.setModelnum(xh.getId());
                                            person.setSpecialityid(ss.getId());
                                            person.setDuty(duty.getId());
                                            person.setJob(job);
                                            person.setTel(tel);
                                            person.setTrainnum(Integer.parseInt(trainnum));
                                            person.setTraincontent(traincontent);


                                            person.setCardNum(cardNum);
                                            person.setDepartmentid(departmentId);
                                            person.setDepartmentids(departmentId);
                                            person.setSex(sexId);
                                            person.setPersontype(RoleEum.student.getValue());
                                            person.setStatus(1);
                                            if (ObjectUtil.isEmpty(person.getId())) {
                                                person.setId(StringHelper.IDGenerator());
                                            }
                                            this.saveOrUpdate(person);
                                            String userPassword = StringHelper.null2String(person.getPassword(), EncryptHelper.md5Password("123456"));
                                            Users user = Optional.ofNullable(lmsSystemApi.getUsersByUserName(cardNum).getResult()).orElse(new Users());
                                            user.setName(cardNum);
                                            user.setPassword(userPassword);
                                            user.setPsdupdatedate(DateHelper.getCurrentDate());
                                            user.setIpaddr(person.getIpaddr());
                                            user.setClientagent(person.getClientagent());
                                            user.setPersonid(person.getId());//关联信息为当前有效ID，学习考核信息关联证件号
                                            user.setIslocked(false);
                                            user.setEnable(true);
                                            user.setUsertype(1);
                                            user.setIsfirstlogin(true);
                                            user.setStatus(1);
                                            user.setPsderrortimes(0);
                                            lmsSystemApi.saveUser(user);
                                        }
                                    }
                                }
                            } else {
                                resultInformation.add(commonSystemApi.translateContent("第[?]条数据姓名或者一卡通编号为空，当前条目导入失败", "", String.valueOf(i + 1)));
                            }
                        }
                    } else {
                        resultInformation.add(commonSystemApi.translateContent("当前导入的模板不正确,请联系管理员！"));
                    }
                } else if ("xlsx".equals(split[1])) {
                    FileInputStream fileStream = new FileInputStream(excel);
                    workBook_2007 = new XSSFWorkbook(fileStream);
                    XSSFSheet sheet = workBook_2007.getSheetAt(0);
                    //workbook_2003.getS
                    XSSFRow row = sheet.getRow(0);//列名
                    //1.获取标题总列数和列名
                    colNum = row.getPhysicalNumberOfCells();
                    if (colNum == 9) {
                        //
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (resultInformation.size() > 0) {
            resultInformation.add(commonSystemApi.translateContent("请使用系统提供的模板编辑导入人员信息"));
        }
        return resultInformation;
    }

    public List<Object> readExcelFile(MultipartFile multipartFile, String departmentId) {
        int colNum;
        List<Object> resultInformation = new ArrayList<Object>();
        HSSFWorkbook workbook_2003; //2003以下；
        XSSFWorkbook workBook_2007;    //2007以上
        try {
            String[] split = multipartFile.getOriginalFilename().split("\\.");
            if ("xls".equals(split[1])) {
                FileInputStream fileStream = (FileInputStream) multipartFile.getInputStream();
                workbook_2003 = new HSSFWorkbook(fileStream);
                HSSFSheet sheet = workbook_2003.getSheetAt(0);
                //workbook_2003.getS
                HSSFRow row = sheet.getRow(1);//列名
                //1.获取标题总列数和列名
                colNum = row.getPhysicalNumberOfCells();
                //总列数为4:模板提供的是4列；
                if (colNum == 13) {
                    //得到总行数
                    int rowNum = sheet.getLastRowNum();
                    //2.读取excel表中的内容
                    for (int i = 2; i <= rowNum; ++i) {
                        row = sheet.getRow(i);
                        //证件号
                        Cell cell_cardNum = row.getCell(1);
                        Object object_cardNum = getCellFormatValue(cell_cardNum);
                        if (!object_cardNum.toString().isEmpty()) {
                            String cardNum = object_cardNum.toString();
                            if (this.existPersonByCardNumAndDepartmentId(cardNum, departmentId)) {
                                //当前部门目录下存在重复证件号信息；导入失败！，只能导入新的人员信息！更新信息通过修改按钮；
                                resultInformation.add(commonSystemApi.translateContent("证件号编号为[?]条目导入失败，当前目录下存在重复证件号信息，请联系管理员!", "", cardNum));
                            } else {
                                //如果其他目录下存在人员信息，将其状态设置为无效；
                                //证件号重复的很少。

                                String name = getCellFormatValue(row.getCell(0)).toString();
                                String sexName = getCellFormatValue(row.getCell(2)).toString();
//                                    String nation = getCellFormatValue(row.getCell(3)).toString();
                                Selectitem es = selectitemMapper.getSelectitemByTypeName(//维护机型
                                        StringHelper.null2String(row.getCell(3)),
                                        "d89ac263bd614b93af0dd9a4a7faef25");
                                Selectitem xh = selectitemMapper.getSelectitemByTypeName(//型号
                                        StringHelper.null2String(row.getCell(4)),
                                        "d89ac263bd614b93af0dd9a4a7faef78");
                                Selectitem ss = selectitemMapper.getSelectitemByTypeName(//专业
                                        StringHelper.null2String(row.getCell(5)),
                                        "bdce8a6593884df785dd26ec70c6f6ef");
                                Selectitem duty = selectitemMapper.getSelectitemByTypeName(//专业
                                        StringHelper.null2String(row.getCell(6)),
                                        "77176e0fb83543a6b823f051833c1ef5");


                                String job = getCellFormatValue(row.getCell(7)).toString();
                                String tel = getCellFormatValue(row.getCell(8)).toString();
                                String trainnum = getCellFormatValue(row.getCell(9)).toString();
                                String traincontent = getCellFormatValue(row.getCell(10)).toString();
                                Selectitem mlevel = selectitemMapper.getSelectitemByTypeName(//密级
                                        StringHelper.null2String(row.getCell(11)),
                                        "D336B34A7FA94FC1AD690CD7B041E117");
                                String mlimit = getCellFormatValue(row.getCell(12)).toString();
                                if (es.getObjname() == null) {
                                    resultInformation.add(commonSystemApi.translateContent("装备机型名称为[?]条目导入失败，代码表无此数据!", "", StringHelper.null2String(row.getCell(3))));
                                }
                                if (xh.getObjname() == null) {
                                    resultInformation.add(commonSystemApi.translateContent("型号名称为[?]条目导入失败，代码表无此数据!", "", StringHelper.null2String(row.getCell(4))));
                                }
                                if (ss.getObjname() == null) {
                                    resultInformation.add(commonSystemApi.translateContent("专业名称为[?]条目导入失败，代码表无此数据!", "", StringHelper.null2String(row.getCell(5))));
                                }
                                if (duty.getObjname() == null) {
                                    resultInformation.add(commonSystemApi.translateContent("岗位名称为[?]条目导入失败，代码表无此数据!", "", StringHelper.null2String(row.getCell(6))));
                                }
                                if (mlevel.getObjname() == null) {
                                    resultInformation.add(commonSystemApi.translateContent("人员涉密等级为[?]条目导入失败，代码表无此数据!", "", StringHelper.null2String(row.getCell(6))));
                                }
                                if (StringHelper.isEmpty(name) || StringHelper.isEmpty(cardNum)) {
                                    resultInformation.add(commonSystemApi.translateContent("第[?]条数据姓名或者证件号为空，当前条目导入失败", "", String.valueOf(i + 1)));
                                } else {
                                    String sexId = "";
                                    if (!StringHelper.isEmpty(sexName)) {
                                        Selectitem selectitem = selectitemService.getSelectitemByTypeName(sexName, ConstParamUtil.SexTypeId);
                                        if (selectitem != null) {
                                            sexId = selectitem.getId();
                                        }
                                    }
                                    List<Person> personList = this.getPersonByCardNum(cardNum, "");
                                    if (personList != null && personList.size() > 0) {//当前证件号人员已存在，更新人员信息（部门信息要相加）
                                        for (Person person : personList) {
                                            person.setName(name);
//                                                person.setNation(nation);
                                            person.setSex(sexId);
                                            person.setEquipmentid(es.getId());
                                            person.setModelnum(xh.getId());
                                            person.setSpecialityid(ss.getId());
                                            person.setDuty(duty.getId());
                                            person.setJob(job);
                                            person.setTel(tel);
                                            person.setTrainnum(Integer.parseInt(trainnum));
                                            person.setTraincontent(traincontent);
                                            String departmentids = person.getDepartmentids();
                                            person.setDepartmentid(departmentId);
                                            if (StringUtils.isNotEmpty(person.getDepartmentid())) {
                                                Department department = departmentService.getById(person.getDepartmentid());
                                                person.setDepartmentidfullpath(department.getFullpath());
                                            }
                                            if (departmentids.indexOf(departmentId) < 0) {
                                                departmentids = departmentids.isEmpty() ? departmentId : departmentId + "," + departmentids;
                                                person.setDepartmentids(departmentids);
                                            }
                                            person.setMlevel(Integer.parseInt(mlevel.getCode()));
                                            person.setMlimit(mlimit);
                                            this.saveOrUpdate(person);
                                            Users user = Optional.ofNullable(lmsSystemApi.getUsersByUserName(cardNum).getResult()).orElse(new Users());
                                            String userPassword = StringHelper.null2String(person.getPassword(), EncryptHelper.md5Password("123456"));
                                            user.setName(cardNum);
                                            user.setPassword(userPassword);
                                            user.setPsdupdatedate(DateHelper.getCurrentDate());
                                            user.setIpaddr(person.getIpaddr());
                                            user.setClientagent(person.getClientagent());
                                            user.setPersonid(person.getId());//关联信息为当前有效ID，学习考核信息关联证件号
                                            user.setIslocked(false);
                                            user.setEnable(true);
                                            user.setUsertype(1);
                                            user.setIsfirstlogin(true);
                                            user.setStatus(1);
                                            user.setPsderrortimes(0);
                                            user.setMlevel(Integer.parseInt(mlevel.getCode()));
                                            user.setMlimit(mlimit);
                                            lmsSystemApi.saveUser(user);
                                        }
                                    } else {
                                        // 当前证件号人员不存在，新增人员。
                                        Person person = new Person();
                                        person.setName(name);
//                                            person.setNation(nation);
                                        person.setEquipmentid(es.getId());
                                        person.setModelnum(xh.getId());
                                        person.setSpecialityid(ss.getId());
                                        person.setDuty(duty.getId());
                                        person.setJob(job);
                                        person.setTel(tel);
                                        person.setTrainnum(Integer.parseInt(trainnum));
                                        person.setTraincontent(traincontent);


                                        person.setCardNum(cardNum);
                                        person.setDepartmentid(departmentId);
                                        person.setDepartmentids(departmentId);
                                        person.setSex(sexId);
                                        person.setPersontype(RoleEum.student.getValue());
                                        person.setStatus(1);
                                        person.setMlevel(Integer.parseInt(mlevel.getCode()));
                                        person.setMlimit(mlimit);
                                        if (ObjectUtil.isEmpty(person.getId())) {
                                            person.setId(StringHelper.IDGenerator());
                                        }
                                        this.saveOrUpdate(person);
                                        String userPassword = StringHelper.null2String(person.getPassword(), EncryptHelper.md5Password("123456"));
                                        Users user = Optional.ofNullable(lmsSystemApi.getUsersByUserName(cardNum).getResult()).orElse(new Users());
                                        user.setName(cardNum);
                                        user.setPassword(userPassword);
                                        user.setPsdupdatedate(DateHelper.getCurrentDate());
                                        user.setIpaddr(person.getIpaddr());
                                        user.setClientagent(person.getClientagent());
                                        user.setPersonid(person.getId());//关联信息为当前有效ID，学习考核信息关联证件号
                                        user.setIslocked(false);
                                        user.setEnable(true);
                                        user.setUsertype(1);
                                        user.setIsfirstlogin(true);
                                        user.setStatus(1);
                                        user.setPsderrortimes(0);
                                        user.setMlevel(Integer.parseInt(mlevel.getCode()));
                                        user.setMlimit(mlimit);
                                        lmsSystemApi.saveUser(user);
                                    }
                                }
                            }
                        } else {
                            resultInformation.add(commonSystemApi.translateContent("第[?]条数据姓名或者一卡通编号为空，当前条目导入失败", "", String.valueOf(i + 1)));
                        }
                    }
                } else {
                    resultInformation.add(commonSystemApi.translateContent("当前导入的模板不正确,请联系管理员！"));
                }
            } else if ("xlsx".equals(split[1])) {
                FileInputStream fileStream = (FileInputStream) multipartFile.getInputStream();
                workBook_2007 = new XSSFWorkbook(fileStream);
                XSSFSheet sheet = workBook_2007.getSheetAt(0);
                //workbook_2003.getS
                XSSFRow row = sheet.getRow(0);//列名
                //1.获取标题总列数和列名
                colNum = row.getPhysicalNumberOfCells();
                if (colNum == 9) {
                    //
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (resultInformation.size() > 0) {
            resultInformation.add(commonSystemApi.translateContent("请使用系统提供的模板编辑导入人员信息"));
        }
        return resultInformation;
    }

    //根据cell类型设置数据
    public Object getCellFormatValue(Cell cell) {
        Object cellvalue = "";
        if (cell != null) {
            switch (cell.getCellType()) {
                case NUMERIC:
                    cell.setCellType(CellType.STRING);
                    cellvalue = cell.getRichStringCellValue().getString();
                    break;
                case FORMULA: {
                    if (DateUtil.isCellDateFormatted(cell)) {
                        cellvalue = cell.getDateCellValue();
                    } else {
                        cellvalue = String.valueOf((Double) cell.getNumericCellValue());
                    }
                    break;
                }
                case STRING:
                    cellvalue = cell.getRichStringCellValue().getString();
                    break;
                default:
                    cellvalue = "";
            }
        } else {
            cellvalue = "";
        }
        return cellvalue;
    }

    public List<Person> getValidPersonByDepartmentIds(List<String> ids) {
        LambdaQueryWrapper<Person> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Person::getDepartmentid, ids);
        wrapper.eq(Person::getStatus, 1);
        return this.list(wrapper);
    }

    public void saveRoleLinks(List<Person> persons) {
        for (Person person : persons) {
            saveRoleLink(person);
        }
    }

    public void saveRoleLink(Person person) {
        String personType = StringHelper.null2String(person.getPersontype());
        Optional<Users> usersOptional = Optional.ofNullable(lmsSystemApi.getUsersByPersonId(person.getId()).getResult());
        if (!usersOptional.isPresent()) return;
        Users users = usersOptional.get();
        if (!personType.isEmpty()) {
            String userId = users.getId();
            sysroleuserlinkService.deleteRoleLinkByUser(userId);
            String[] ids = personType.split(",");
            if (ids.length > 0) {
                List<Sysroleuserlink> sysroleuserlinks = new ArrayList<>();
                for (String id : ids) {
                    Sysroleuserlink sysroleuserlink = new Sysroleuserlink(null, userId, id);
                    sysroleuserlinks.add(sysroleuserlink);
                }
                sysroleuserlinkService.saveOrUpdateBatch(sysroleuserlinks);
            }
        }
    }

    public List<String> importStudent(List<Student> studentList) {
        List<String> resultInformation = new ArrayList<>();
        // 获取所有数据字典
        Set<String> objNames = new HashSet<>();
        studentList.forEach(s -> {
            objNames.add(s.getSex());
            objNames.add(s.getSpeciality());
            objNames.add(s.getEducation());
        });

        Map<String, Selectitem> selectitemMap = new HashMap<>();
        List<Selectitem> selectitemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(objNames)) {
            selectitemList = selectitemService.getByNameList(new ArrayList<>(objNames));
        }
        if (CollectionUtils.isNotEmpty(selectitemList)) {
            selectitemMap = selectitemList.stream().collect(Collectors.toMap(Selectitem::getObjname, s -> s, (s1, s2) -> s1));
        }

        List<Selectitem> mlevelList = selectitemService.getAllSelectitemsByType("047DA36C359F4FB5ABF276A7008438F4");
        Map<String, Selectitem> mlevelMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(mlevelList)) {
            mlevelMap = mlevelList.stream().collect(Collectors.toMap(Selectitem::getObjname, s -> s, (s1, s2) -> s1));
        }
        List<Department> departmentList = departmentService.getValidDepartments();
        Map<String, Department> departmentMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(departmentList)) {
            departmentMap = departmentList.stream().collect(Collectors.toMap(d -> d.getId() + "~" + d.getName(), d -> d, (d1, d2) -> d1));
        }


        for (int i = 0; i < studentList.size(); i++) {
            Student student = studentList.get(i);
            /**
             * 新增人员
             */
            Person person = new Person();
            person.setName(student.getName());
            person.setSex(Optional.ofNullable(selectitemMap.get(student.getSex())).orElse(new Selectitem()).getId());
            if (StringUtils.isEmpty(person.getSex())) {
                resultInformation.add(String.format("第[%s]条数据导入失败 , 性别名称为[%s]条目导入失败，代码表无此数据!", i + 1, student.getSex()));
                continue;
            }
            person.setBirthday(student.getBirthday());
            person.setTrainorg(student.getDepartmentname());
            person.setYears(student.getSeniority());
            person.setSpecialityid(Optional.ofNullable(selectitemMap.get(student.getSpeciality())).orElse(new Selectitem()).getId());
            if (StringUtils.isEmpty(person.getSpecialityid())) {
                resultInformation.add(String.format("第[%s]条数据导入失败 , 从事专业名称为[%s]条目导入失败，代码表无此数据!", i + 1, student.getSpeciality()));
                continue;
            }
            person.setEducation(Optional.ofNullable(selectitemMap.get(student.getEducation())).orElse(new Selectitem()).getId());
            if (StringUtils.isEmpty(person.getEducation())) {
                resultInformation.add(String.format("第[%s]条数据导入失败 , 学历名称为[%s]条目导入失败，代码表无此数据!", i + 1, student.getEducation()));
                continue;
            }
            person.setJob(student.getJob());
            person.setTel(student.getPhone());
            String equipment = student.getEquipment();
            StringBuilder equipmentId = new StringBuilder();
            if (StringUtils.isNotEmpty(equipment)) {
                String[] split = equipment.split(",");
                for (int j = 0; j < split.length; j++) {
                    Component component = componentService.getByName(split[i]);
                    if (component != null && StringUtils.isNotEmpty(component.getFullpath())) {
                        if (j != split.length - 1) {
                            equipmentId.append(component.getFullpath()).append(",");
                        } else {
                            equipmentId.append(component.getFullpath());
                        }
                    }
                }
            }
            person.setEquipmentid(equipmentId.toString());
            person.setRemark(student.getRemark());
            person.setMlevel(Integer.parseInt(Optional.ofNullable(mlevelMap.get(ObjectUtils.toString(student.getMlevel()))).orElse(new Selectitem()).getCode()));
            if (person.getMlevel() == null) {
                resultInformation.add(String.format("第[%s]条数据导入失败 , 密级名称为[%s]条目导入失败，代码表无此数据!", i + 1, student.getMlevel()));
                continue;
            }
            if (StringUtils.isNotEmpty(student.getDepartmentname())) {
                departmentMap.forEach((k, v) -> {
                    if (StringUtils.isNotEmpty(k)) {
                        String[] split = k.split("~");
                        if (split.length >= 1) {
                            if (StringUtils.equals(student.getDepartmentname(), split[1])) {
                                person.setDepartmentid(v.getId());
                                person.setDepartmentidfullpath(v.getFullpath());
                            }
                        }
                    }
                });
            } else {
                person.setDepartmentid("");
                person.setDepartmentidfullpath("");
            }
            if (StringUtils.isEmpty(person.getDepartmentid())) {
                resultInformation.add(String.format("第[%s]条数据导入失败 , 部门名称为[%s]条目导入失败，代码表无此数据!", i + 1, student.getDepartmentname()));
                continue;
            }
            String cardNum = "";
            if (StringUtils.isEmpty(student.getStudentName())) {
                cardNum = commonSystemApi.getNewCode("User");
            } else {
                cardNum = student.getStudentName();
            }
            person.setCardNum(cardNum);
            person.setPersontype(RoleEum.student.getValue());
            person.setStatus(1);
            person.setSlevel(80);
            if (ObjectUtil.isEmpty(person.getId())) {
                person.setId(StringHelper.IDGenerator());
            }
            this.saveOrUpdate(person);
            String defaultPassword = "Sbtr.123";
            Result<Setting> defaultPasswordResult = commonSystemApi.getById(SettingConfig.CONFIG_DEFAULT_PASSWORD);
            if (defaultPasswordResult != null && defaultPasswordResult.getResult() != null) {
                Setting defaultPasswordSetting = defaultPasswordResult.getResult();
                defaultPassword = defaultPasswordSetting.getItemvalue();
            }
            String userPassword = StringHelper.null2String(person.getPassword(), EncryptHelper.md5Password(defaultPassword));
            Users user = Optional.ofNullable(lmsSystemApi.getUsersByUserName(cardNum).getResult()).orElse(new Users());
            user.setName(cardNum);
            user.setPassword(userPassword);
            user.setPsdupdatedate(DateHelper.getCurrentDate());
            user.setIpaddr(person.getIpaddr());
            user.setClientagent(person.getClientagent());
            user.setPersonid(person.getId());//关联信息为当前有效ID，学习考核信息关联证件号
            user.setIslocked(false);
            user.setEnable(true);
            user.setUsertype(1);
            user.setIsfirstlogin(true);
            user.setStatus(1);
            user.setPsderrortimes(0);
            user.setMlevel(person.getMlevel());
            user.setPersonid(person.getId());
            lmsSystemApi.saveUser(user);
        }
        if (resultInformation.size() > 0) {
            resultInformation.add("请使用系统提供的模板编辑导入人员信息");
        }
        return resultInformation;
    }

    /**
     * 适配教员教学型号
     *
     * @param personList
     */
    public void adaptTeacheingModel(List<Person> personList) {
        Set<String> componentIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(personList)) {
            for (Person person : personList) {
                if (person == null) {
                    continue;
                }
                List<String> lastComponentIdList = new ArrayList<>();
                String teachingmodel = person.getTeachingmodel();
                if (StringUtils.isNotEmpty(teachingmodel)) {
                    String[] componentArray = teachingmodel.split(",");
                    if (componentArray.length > 0) {
                        for (String componentId : componentArray) {
                            if (StringUtils.isNotEmpty(componentId)) {
                                String[] split = componentId.split("/");
                                componentIdSet.add(split[split.length - 1]);
                                lastComponentIdList.add(split[split.length - 1]);
                            }
                        }
                    }
                }
                person.setLastteachingmodellist(lastComponentIdList);
            }
        }
        Map<String, Component> componentMap = new HashMap<>();
        List<Component> componentList = componentService.listIds(new ArrayList<>(componentIdSet));
        if (CollectionUtils.isNotEmpty(componentList)) {
            componentMap = componentList.stream().collect(Collectors.toMap(Component::getId, c -> c));
        }

        if (CollectionUtils.isNotEmpty(personList)) {
            for (Person person : personList) {
                if (person == null) {
                    continue;
                }
                List<String> lastComponentIdList = person.getLastteachingmodellist();
                if (CollectionUtils.isNotEmpty(lastComponentIdList)) {
                    StringBuilder teachingmodelname = new StringBuilder();
                    for (int i = 0; i < lastComponentIdList.size(); i++) {
                        Component component = Optional.ofNullable(componentMap.get(lastComponentIdList.get(i))).orElse(new Component());
                        teachingmodelname.append(component.getName());
                        if (i != lastComponentIdList.size() - 1) {
                            teachingmodelname.append(",");
                        }
                    }
                    person.setTeachingmodelname(teachingmodelname.toString());
                }
            }
        }
    }

    /**
     * 适配学员所属型号
     *
     * @param personList
     */
    public void adaptPersonModel(List<Person> personList) {
        Set<String> componentIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(personList)) {
            for (Person person : personList) {
                if (person == null) {
                    continue;
                }
                List<String> lastComponentIdList = new ArrayList<>();
                String equipmentid = person.getEquipmentid();
                if (StringUtils.isNotEmpty(equipmentid)) {
                    String[] componentArray = equipmentid.split(",");
                    if (componentArray.length > 0) {
                        for (String componentId : componentArray) {
                            if (StringUtils.isNotEmpty(componentId)) {
                                String[] split = componentId.split("/");
                                componentIdSet.add(split[split.length - 1]);
                                lastComponentIdList.add(split[split.length - 1]);
                            }
                        }
                    }
                }
                person.setLastequipmentidlist(lastComponentIdList);
            }
        }
        Map<String, Component> componentMap = new HashMap<>();
        List<Component> componentList = componentService.listIds(new ArrayList<>(componentIdSet));
        if (CollectionUtils.isNotEmpty(componentList)) {
            componentMap = componentList.stream().collect(Collectors.toMap(Component::getId, c -> c));
        }

        if (CollectionUtils.isNotEmpty(personList)) {
            for (Person person : personList) {
                if (person == null) {
                    continue;
                }
                List<String> lastComponentIdList = person.getLastequipmentidlist();
                if (CollectionUtils.isNotEmpty(lastComponentIdList)) {
                    StringBuilder equipmentname = new StringBuilder();
                    for (int i = 0; i < lastComponentIdList.size(); i++) {
                        Component component = Optional.ofNullable(componentMap.get(lastComponentIdList.get(i))).orElse(new Component());
                        equipmentname.append(component.getName());
                        if (i != lastComponentIdList.size() - 1) {
                            equipmentname.append(",");
                        }
                    }
                    person.setEquipmentname(equipmentname.toString());
                }
            }
        }
    }


    /**
     * 适配管理员管理型号
     *
     * @param personList
     */
    public void adaptManageModel(List<Person> personList) {
        Set<String> componentIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(personList)) {
            for (Person person : personList) {
                if (person == null) {
                    continue;
                }
                List<String> lastComponentIdList = new ArrayList<>();
                String managemodel = person.getManagemodel();
                if (StringUtils.isNotEmpty(managemodel)) {
                    String[] componentArray = managemodel.split(",");
                    if (componentArray.length > 0) {
                        for (String componentId : componentArray) {
                            if (StringUtils.isNotEmpty(componentId)) {
                                String[] split = componentId.split("/");
                                componentIdSet.add(split[split.length - 1]);
                                lastComponentIdList.add(split[split.length - 1]);
                            }
                        }
                    }
                }
                person.setLastmanagemodellist(lastComponentIdList);
            }
        }
        Map<String, Component> componentMap = new HashMap<>();
        List<Component> componentList = componentService.listIds(new ArrayList<>(componentIdSet));
        if (CollectionUtils.isNotEmpty(componentList)) {
            componentMap = componentList.stream().collect(Collectors.toMap(Component::getId, c -> c));
        }

        if (CollectionUtils.isNotEmpty(personList)) {
            for (Person person : personList) {
                if (person == null) {
                    continue;
                }
                List<String> lastComponentIdList = person.getLastmanagemodellist();
                if (CollectionUtils.isNotEmpty(lastComponentIdList)) {
                    StringBuilder managemodelname = new StringBuilder();
                    for (int i = 0; i < lastComponentIdList.size(); i++) {
                        Component component = Optional.ofNullable(componentMap.get(lastComponentIdList.get(i))).orElse(new Component());
                        managemodelname.append(component.getName());
                        if (i != lastComponentIdList.size() - 1) {
                            managemodelname.append(",");
                        }
                    }
                    person.setManagemodelname(managemodelname.toString());
                }
            }
        }
    }

    public List<String> getUserNamesBySysRoleLink(String roleId) {
        return sysroleuserlinkService.getUserNamesBySysRoleLink(roleId);
    }

    public List<String> getRoleIdByUserName(String userName) {
        List roleList = new ArrayList<>();
        Optional<Users> usersOptional = Optional.ofNullable(lmsSystemApi.getUsersByUserName(userName).getResult());
        if (usersOptional.isPresent()) {
            Users users = usersOptional.get();
            Person person = this.getById(users.getPersonid());
            String roleIds = ObjectUtil.isEmpty(person) ? "" : StringHelper.null2String(person.getPersontype());
            if (!roleIds.isEmpty()) {
                roleList = Arrays.asList(roleIds.split(","));
            }
        }
        return roleList;
    }
}
