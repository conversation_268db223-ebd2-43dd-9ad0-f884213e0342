// ReTrTable 组件类型定义

// 表格配置接口
export interface TableConfig {
  // 基础数据配置
  tableData: any[];
  total?: number;
  pageIndex?: number;
  pageSize?: number;

  // 显示控制配置
  showSearchBar?: boolean;
  showMoreSearch?: boolean;
  showCheckBox?: boolean;
  showIndex?: boolean;
  showOperateColumn?: boolean;
  showHeader?: boolean;

  // 选择配置
  selector?: any[];

  // 操作列配置
  OperateColumnWidth?: number;
  operateColumnLabel?: string;

  // 样式配置
  stripe?: boolean;
  size?: "mini" | "small" | "default" | "large";

  // 列配置
  displayColumns: DisplayColumn[];

  // 排序状态配置
  sortField?: string;
  sortOrder?: "ascend" | "descend" | null;

  // 行键配置
  rowKey?: string;
}

// 列配置接口
export interface DisplayColumn {
  label: string;
  prop: string;
  width?: string | number;
  formatter?: (row: any, column: any, cellValue: any) => any;
  showtooltip?: boolean;
  resizable?: boolean;
  sortable?: boolean;
  fixed?: boolean | "left" | "right";
  align?: "left" | "right" | "center";
  classname?: string;
  labelclassname?: string;
  minWidth?: string | number;
  children?: DisplayColumn[];
  // 支持自定义列插槽
  scopedSlots?: {
    customRender?: string;
  };
  // 自定义渲染相关
  render?: (text: any, record: any, index: any) => any;
  renderType?: string;
  statusMap?: Record<string, { status: string; text: string; color?: string }>;
  tagColor?: string;
}

// 事件回调类型
export interface TableEvents {
  searchMethod?: () => void;
  addMethod?: () => void;
  editMethod?: (rows: any[]) => void;
  viewMethod?: (rows: any[]) => void;
  deleteMethod?: (rows: any[]) => void;
  extendMethod1?: () => void;
  extendMethod2?: () => void;
  sortMethod?: (sortinfo: { column: any; order: string; prop: string }) => void;
  select?: (selector: any[], row: any) => void;
  selectAll?: (selector: any[]) => void;
}

export interface CustomButton {
  key: string;
  label: string;
  icon?: any;
  type?: "batch" | "row";
  method?: (...args: any[]) => void;
  props?: Record<string, any>; // 按钮配置（如type、danger等）
}

export interface OperationButton {
  key: string;
  label: string;
  icon?: any;
  method?: (row: any) => void;
  props?: Record<string, any>; // 按钮配置
}

// 查询项配置接口（移植自TrTable）
export interface QueryItem {
  key: string;
  label: string;
  component?: string;
  componentInstance?: any;
  props?: Record<string, any>;
  events?: Record<string, (value: any, ...args: any[]) => void>;
}

// 查询配置接口（合并items、事件、初始值等）
export interface QueryConfig {
  items: QueryItem[];
  onQuery?: (form: Record<string, any>, params?: any) => void;
  onReset?: (form: Record<string, any>) => void;
  // 查询表单数据（支持双向绑定）
  queryForm?: Record<string, any>;
}

export type BtnConfig = CustomButton | OperationButton;
export type PaginationMode = "server" | "client";
