package com.lms.base.coursewarelearnrecord.controller;

import com.alibaba.fastjson.JSONObject;
import com.lms.base.courseware.service.CoursewareService;
import com.lms.base.coursewarelearnrecord.service.CoursewareLearnRecordService;
import com.lms.base.feign.model.Courseware;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.*;
import com.lms.base.feign.model.CoursewareLearnRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@RestController
@RequestMapping("/coursewarelearnrecord")
@Api(value = "课件学习记录", tags = {"课件学习记录"})
public class CoursewareLearnRecordController extends BaseController<CoursewareLearnRecord> {

    @Resource
    private CoursewareLearnRecordService coursewareLearnRecordService;

    @Resource
    private CoursewareService coursewareService;


    @LMSLog(desc = "查询课件学习记录", otype = LogType.List, order = 1, method = "setCourselearnrecordLog")
    @PostMapping("/listpage")
    @ApiOperation("查询课件学习记录")
    public Result getCourselearnrecordList(@RequestBody JSONObject jsonObject) {
        PageInfo pageInfo = super.getPageInfo(jsonObject);
        return Result.OK(coursewareLearnRecordService.listByCondition(pageInfo));
    }

    @GetMapping("/listAll")
    @ApiOperation("查询全部课件学习记录")
    public Result getAllExaminepage() {
        return Result.OK(coursewareLearnRecordService.list());
    }

    @PostMapping("/courselearnlist")
    @ApiOperation("根据参数获取课件学习记录状态")
    public Result getCourselearnlist(@RequestBody Map<String, Object> map) {
        int status = 0;
        CoursewareLearnRecord coursewareLearnRecord = this.coursewareLearnRecordService.getCoursewareLearnRecord(map);
        if (coursewareLearnRecord != null) {
            status = coursewareLearnRecord.getStatus();
        }
        return Result.OK(status);
    }

    @PostMapping("/getmincourseware")
    @ApiOperation("根据参数获取课程未学习课件序号")
    public Result getLearnno(@RequestBody Map<String, Object> map) {
        return Result.OK(coursewareLearnRecordService.getMinCoursewareByCourse(map));
    }

    @LMSLog(desc = "新增课件学习记录", otype = LogType.Save, method = "setCourselearnrecordLog")
    @PostMapping("/add")
    @ApiOperation("新增课件学习记录")
    public Result saveCourselearnrecord(@RequestBody CoursewareLearnRecord coursewareLearnRecord) {
        coursewareLearnRecordService.saveOrUpdate(coursewareLearnRecord);
        return Result.OK();
    }

    @LMSLog(desc = "更新课件、课程及计划学习记录", otype = LogType.Update, method = "setCourselearnrecordLog")
    @PostMapping("/modify")
    @ApiOperation("更新课件、课程及计划学习记录")
    public Result updateCourselearnrecord(@RequestBody CoursewareLearnRecord coursewareLearnRecord) throws Exception {
        if (coursewareLearnRecord.getId() != null) {
            CoursewareLearnRecord coursewareLearnRecord1 = coursewareLearnRecordService.getById(coursewareLearnRecord.getId());
            coursewareLearnRecord1.setLearnDate(DateHelper.getCurrentDate());
            coursewareLearnRecord1.setSpeed(coursewareLearnRecord.getSpeed());
            coursewareLearnRecord1.setStatus(coursewareLearnRecord.getStatus());
            coursewareLearnRecord1.setStudynum(coursewareLearnRecord1.getStudynum() + 1);
            BeanUtils.copyProperties(coursewareLearnRecord, coursewareLearnRecord1);
            coursewareLearnRecordService.saveOrUpdate(coursewareLearnRecord1); //根据更新
        } else {
            coursewareLearnRecord.setStudynum(1);
            coursewareLearnRecord.setLearnDate(DateHelper.getCurrentDate());
            coursewareLearnRecordService.saveOrUpdate(coursewareLearnRecord);
        }

        return Result.OK(coursewareLearnRecord);

    }

    @LMSLog(desc = "删除课件学习记录", otype = LogType.Delete, order = 1, method = "setCourselearnrecordLog")
    @DeleteMapping("/delete")
    @ApiOperation("删除课件学习记录")
    public Result batchDeleteCourselearnrecord(@RequestParam("ids") String ids) {
        String[] idList = ids.split(",");
        for (String id : idList) {
            this.coursewareLearnRecordService.removeById(id);
        }
        return Result.OK();
    }

    @GetMapping("/getStartArg")
    @ApiOperation("获取请求IP信息")
    public void getStartArg(HttpServletRequest request,
                            HttpServletResponse response) {
        String startArg = CookieHelper.getCookie(request, "startArg");
        startArg = LMSCacheManager.GetDataFromCach(request.getRemoteAddr()).toString();
        CookieHelper.setCookie(response, "startArg", startArg, 60 * 1000 * 5);
    }

    @ApiOperation("课件学习记录日志处理方法")
    public String setCourselearnrecordLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.List)) {
            objname = "课程学习记录列表";
        } else if (lmslog.otype().equals(LogType.Save)) {
            CoursewareLearnRecord obj = (CoursewareLearnRecord) (args[0]);
            String coursewareid = StringHelper.null2String(obj.getCoursewareid());
            if (StringUtils.isNotEmpty(coursewareid)) {
                Courseware result = coursewareService.getById(coursewareid);
                if (result != null) {
                    objname = result.getName();
                }
            }
        } else if (lmslog.otype().equals(LogType.Update)) {
            CoursewareLearnRecord obj = (CoursewareLearnRecord) (args[0]);
            String coursewareid = StringHelper.null2String(obj.getCoursewareid());
            if (StringUtils.isNotEmpty(coursewareid)) {
                Courseware result = coursewareService.getById(coursewareid);
                if (result != null) {
                    objname = result.getName();
                }
            }
        } else if (lmslog.otype().equals(LogType.Delete)) {
            String ids = (String) (args[0]);
            String[] idarray = ids.split(",");
            for (String id : idarray) {
                CoursewareLearnRecord obj = coursewareLearnRecordService.getById(id);
                objname = objname.isEmpty() ? StringHelper.null2String(obj.getCoursewareid()) : objname + "," + StringHelper.null2String(obj.getCoursewareid());
            }
        }
        return objname;
    }

    /**
     * 以下是远程调用暴露接口
     */
    @ApiOperation("根据参数获取课件学习记录对象")
    @PostMapping("/getCoursewareLearnRecord")
    public Result<CoursewareLearnRecord> getCoursewareLearnRecord(@RequestBody Map<String, Object> map) {
        return Result.OK(coursewareLearnRecordService.getCoursewareLearnRecord(map));
    }
}
