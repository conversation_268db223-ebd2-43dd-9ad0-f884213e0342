package com.lms.system.feign.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import com.lms.common.model.LogObjname;
import com.lms.common.model.SearchContent;
import com.lms.common.model.SearchName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)

@TableName("p_user")
@Data
@ApiModel(value = "用户实体类")
public class Users extends BaseModel {

    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private String id;
    /**
     * 登陆名
     **/
    @LogObjname
    @SearchName
    @ApiModelProperty("登陆名")
    private String name;//为用户的证件号，唯一！
    /**
     * 密码
     **/
    @ApiModelProperty("密码")
    private String password;
    /**
     * 用户类型
     **/
    @ApiModelProperty("用户类型")
    private Integer usertype;

    /**
     * 人员id
     */
    @ApiModelProperty("人员id")
    private String personid;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private Integer status;

    /**
     * 描述
     */
    @SearchContent
    @ApiModelProperty("描述")
    private String description;

    @TableField(exist = false)
    @ApiModelProperty("用户名称")
    private String personname;

    @TableField(exist = false)
    @ApiModelProperty("用户部门")
    private String department;

    /**
     * 是否首次登录
     */
    @ApiModelProperty("是否首次登录")
    private boolean isfirstlogin;

    /**
     * 是否被锁定
     */
    @ApiModelProperty("是否被锁定")
    private boolean islocked;

    /**
     * 密级,对应S1000D中securityClassification属性值
     **/
    @ApiModelProperty("密级")
    private String usecurity;

    /**
     * 是否允许用户登录系统
     **/
    @ApiModelProperty("是否允许用户登录系统")
    private boolean enable;

    /**
     * 错误密码输入次数
     **/
    @ApiModelProperty("错误密码输入次数")
    private Integer psderrortimes;

    /**
     * 密码修改日期
     **/
    @ApiModelProperty("密码修改日期")
    private String psdupdatedate;

    /**
     * 上次登录时间，可用于错误密码输入次数的重置
     **/
    @ApiModelProperty("上次登录时间")
    private String lastlogintime;

    /**
     * 限制用户登录ip地址
     */
    @ApiModelProperty("限制用户登录ip地址")
    private String ipaddr;

    /**
     * 限制用户登录浏览器类型
     */
    @ApiModelProperty("限制用户登录浏览器类型")
    private String clientagent;

    /**
     * 角色id
     */
    @ApiModelProperty("角色id")
    private String roleid;

    /**
     * 角色列表
     */
    @TableField(exist = false)
    @ApiModelProperty("角色列表")
    private List<Role> roles = new ArrayList<>();
}
