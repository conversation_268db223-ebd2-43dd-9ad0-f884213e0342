package com.lms.common.model;

public class ContextUser {
    public static final ThreadLocal<JwtUser> jwtUserTL = new ThreadLocal();

    /**
     * 设置用户数据
     * @param jwtUser 用户信息
     */
    public static void set(JwtUser jwtUser)
    {
        jwtUserTL.set(jwtUser);
    }

    /**
     * 获取当前用户
     * @return userId
     */
    public static JwtUser getCurrentUser()
    {
        return jwtUserTL.get();
    }

    public static String getUserId()
    {
        return jwtUserTL.get().getUserid();
    }

    public static String getUserName()
    {
        return jwtUserTL.get().getUsername();
    }

    public static String getPersonName()
    {
        return jwtUserTL.get().getPersonname();
    }

    public static String getDepartId()
    {
        return jwtUserTL.get().getDepartid();
    }

    public static String getRoleId()
    {
        return jwtUserTL.get().getRoleid();
    }

    public static String getEquipmentId()
    {
        return jwtUserTL.get().getEquipmentid();
    }

    /**
     * 清理当前用户信息
     */
    public static void clear()
    {
        jwtUserTL.remove();
    }
}
