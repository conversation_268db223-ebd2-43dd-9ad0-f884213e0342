# 文件存储统一配置
# 支持OSS、MinIO、本地存储三种策略的配置化切换
lms:
  file-storage:
    # 默认存储策略 (oss/minio/local)
    default-strategy: oss
    # 是否启用多存储策略模式
    multi-strategy-enabled: false
    # 文件上传通用配置
    upload:
      # 最大文件大小 (100MB)
      max-file-size: 104857600
      # 允许的文件扩展名
      allowed-extensions:
        - .jpg
        - .jpeg
        - .png
        - .gif
        - .bmp
        - .webp
        - .pdf
        - .doc
        - .docx
        - .xls
        - .xlsx
        - .ppt
        - .pptx
        - .txt
        - .csv
        - .zip
        - .rar
        - .7z
        - .tar
        - .gz
        - .mp4
        - .avi
        - .mov
        - .wmv
        - .flv
        - .mkv
        - .mp3
        - .wav
        - .flac
        - .aac
        - .vrp
        - .vrpc
      # 是否启用文件类型检查
      enable-type-check: true
      # 是否启用文件大小检查
      enable-size-check: true
      # 文件名生成策略 (uuid/timestamp/original)
      file-name-strategy: uuid
    # OSS存储配置
    oss:
      # 是否启用OSS存储
      enabled: true
      # OSS访问端点
      endpoint: ${OSS.endpoint}
      # 访问密钥ID
      access-key-id: ${OSS.accessKeyId}
      # 访问密钥Secret
      access-key-secret: ${OSS.accessKeySecret}
      # 区域
      region: ${OSS.region}
      # 默认存储桶名称
      default-bucket: ${OSS.bucketName}
      # 是否使用HTTPS
      use-https: true
      # 连接超时时间（毫秒）
      connection-timeout: 30000
      # 读取超时时间（毫秒）
      socket-timeout: 30000
    # MinIO存储配置
    minio:
      # 是否启用MinIO存储
      enabled: false
      # MinIO服务地址
      host: ${minio.host:http://localhost:9000}
      # 访问密钥
      access-key: ${minio.access-key:minioadmin}
      # 密钥
      secret-key: ${minio.secret-key:minioadmin}
      # 默认存储桶名称
      default-bucket: ${lms.bucketName:lms-bucket}
      # 是否使用HTTPS
      use-https: false
      # 连接超时时间（毫秒）
      connection-timeout: 30000
      # 读取超时时间（毫秒）
      read-timeout: 30000
      # 写入超时时间（毫秒）
      write-timeout: 30000
    # 本地存储配置
    local:
      # 是否启用本地存储
      enabled: false
      # 存储根路径
      base-path: ${lms.storagepath:./storage}
      # 临时文件路径
      temp-path: ${lms.temppath:./temp}
      # 是否创建日期目录
      create-date-dir: true
      # 文件访问URL前缀
      url-prefix: /files
      # 是否启用文件压缩
      enable-compression: false
      # 压缩质量（0.0-1.0）
      compression-quality: 0.8
      # 是否启用缩略图生成
      enable-thumbnail: false
      # 缩略图尺寸
      thumbnail-size: 200x200