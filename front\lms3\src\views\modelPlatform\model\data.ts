export interface Model {
  id: string;
  name: string;
  category: string;
  code: string;
  workNumber: string;
  applicableModel: string;
  ataMark: string;
  equipmentNumber: string;
  applicableStage: string;
  effectiveness: string;
  imageUrl: string;
  tags: string[];
  dataCount: number;
}

export interface ModelFilter {
  modelType?: string;
  applicableModel?: string;
  ataMark?: string;
  equipmentNumber?: string;
  workNumber?: string;
  applicableStage?: string;
  effectiveness?: string;
}
