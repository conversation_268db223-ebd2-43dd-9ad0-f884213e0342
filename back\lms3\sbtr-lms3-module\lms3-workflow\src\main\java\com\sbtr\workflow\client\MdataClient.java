package com.sbtr.workflow.client;

import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import org.springframework.web.bind.annotation.*;
import java.util.Map;


/**
 * 主数据中心 调用sbtr-mdata相关接口
 * 本地开发时可以指定 ip+网关端口进行访问  在configuration中配置 url = "http://*************:9774/mdata"
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:34
 */
//@FeignClient(name = FeignClientName.MDATA_SERVER_NAME, configuration = FeignConfig.class)
public interface MdataClient {

    /**
     * @MethodName getPageSet
     * @Description
     * @Param pageParam
     * @Param filterSort
     * @Return cn.ewsd.common.utils.easyui.PageSet<cn.ewsd.system.model.User>
     * <AUTHOR>
     * @Date 2018-12-29 14:43
     */
    @GetMapping("/user/getPageSet")
    PageSet<Map<String, Object>> getPageSet(@RequestParam("pageParam") PageParam pageParam, @RequestParam("filterSort") String filterSort);

    /**
     * @MethodName getUserByUserNameId
     * @Description
     * @Param userNameId
     * @Return cn.ewsd.system.model.User
     * <AUTHOR>
     * @Date 2018-12-29 14:20
     */
    @RequestMapping(value = "/user/getUserByUserNameId", method = RequestMethod.POST)
    Map<String, Object> getUserByUserNameId(@RequestParam("userNameId") String userNameId);



    @ResponseBody
    @RequestMapping("/getGroupUuidsByIds")
    String getGroupUuidsByIds(@RequestParam("ids") String ids);

    @ResponseBody
    @PostMapping("/organization/getUserInfo")
    String getUserInfo(@RequestParam("orgId") String orgId, @RequestParam("orgType") String orgType);


    /**
     * 根据角色id查询用户工号
     *
     * @param roleId
     * @return String
     */
    @PostMapping("/user/getUserNameIdByRoleId")
    String getUserNameIdByRoleId(@RequestParam("roleId") String roleId);

    /**
     * 根据岗位id查询用户工号
     *
     * @param post
     * @return String
     */
    @PostMapping("/user/getUserNameIdByPost")
    String getUserNameIdByPost(@RequestParam("post") String post);


    /**
     * 根据工号获取部门负责人
     *
     * @param userNameId
     * @return String
     */
    @PostMapping("/organization/getleaderIdByUserNameId")
    String getleaderIdByUserNameId(@RequestParam("userNameId") String userNameId);

    /**
     * 根据工号获取直属主管
     *
     * @param userNameId
     * @return String
     */
    @PostMapping("/user/getReportsToByUserNameId")
    String getReportsToByUserNameId(@RequestParam("userNameId") String userNameId);

    String selectUserNameByuuid(String toStr);

    String getListByIdData(String toStr);
}
