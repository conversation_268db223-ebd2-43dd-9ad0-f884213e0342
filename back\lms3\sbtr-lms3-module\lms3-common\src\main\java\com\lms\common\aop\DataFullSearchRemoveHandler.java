package com.lms.common.aop;

import com.lms.common.model.DataSearchRemove;
import com.lms.common.service.FileService;
import com.lms.common.util.StringHelper;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.List;

@Aspect
@Component
public class DataFullSearchRemoveHandler {

    @Resource
    private FileService fileService;

    /**
     * 配置织入点
     */
    @Pointcut("@annotation(com.lms.common.model.DataSearchRemove)")
    public void logPointCut() {
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "logPointCut()", returning = "jsonResult")
    public void doDataToFile(JoinPoint joinPoint, Object jsonResult) throws IOException {
        Object result = null;
        // 拦截的方法参数
        Object[] args = joinPoint.getArgs();
        // 拦截的放参数类型
        Signature sig = joinPoint.getSignature();
        MethodSignature msig = null;
        if (!(sig instanceof MethodSignature)) {
            throw new IllegalArgumentException("该注解只能用于方法");
        }
        msig = (MethodSignature) sig;
        // 获得被拦截的方法
        Method method = msig.getMethod();
        if (method.isAnnotationPresent(DataSearchRemove.class)) {
            Object objects = args[0];
            if (objects instanceof String) {
                String fileId = StringHelper.null2String(objects);
                fileService.delete(fileId);
            } else if (objects instanceof List) {
                List ids = (List) objects;
                fileService.batchDelete(ids);
            }
        }
    }

}
