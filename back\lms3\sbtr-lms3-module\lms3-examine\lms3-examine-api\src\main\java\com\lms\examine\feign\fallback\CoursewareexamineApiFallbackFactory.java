package com.lms.examine.feign.fallback;

import com.lms.common.model.Result;
import com.lms.examine.feign.api.CoursewareexamineApi;
import com.lms.examine.feign.model.Coursewareexamine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * misboot-mdata相关接口 模块降级处理
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
@Component
public class CoursewareexamineApiFallbackFactory implements FallbackFactory<CoursewareexamineApi> {

    private static final Logger log = LoggerFactory.getLogger(CoursewareexamineApiFallbackFactory.class);


    @Override
    public CoursewareexamineApi create(Throwable throwable) {
        log.error("CoursewareexamineApi模块服务调用失败:{}", throwable.getMessage());
        return new CoursewareexamineApi() {

            @Override
            public Result<List<Coursewareexamine>> getByCourseware(String coursewareid) {
                return null;
            }

            @Override
            public Result save(Coursewareexamine newexamine) {
                return null;
            }
        };
    }
}
