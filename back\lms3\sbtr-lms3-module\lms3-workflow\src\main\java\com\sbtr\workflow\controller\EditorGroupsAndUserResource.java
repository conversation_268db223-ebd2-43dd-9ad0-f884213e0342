package com.sbtr.workflow.controller;

import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.service.MyVariableService;
import com.sbtr.workflow.model.Organization;
import org.apache.commons.lang.StringUtils;
import org.flowable.engine.ManagementService;
import org.flowable.idm.api.Group;
import org.flowable.idm.api.IdmIdentityService;
import org.flowable.idm.api.User;
import org.flowable.idm.engine.impl.persistence.entity.GroupEntityImpl;
import org.flowable.idm.engine.impl.persistence.entity.UserEntityImpl;
import org.flowable.ui.common.model.GroupRepresentation;
import org.flowable.ui.common.model.ResultListDataRepresentation;
import org.flowable.ui.common.model.UserRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.ArrayList;
import java.util.List;

/**
 * 原始流程界面获取组织机构用户
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
@RestController
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/app")
public class EditorGroupsAndUserResource {


    @Autowired
    protected IdmIdentityService idmIdentityService;

    @RequestMapping(value = "/rest/editor-groups", method = RequestMethod.GET)
    public ResultListDataRepresentation getGroups(@RequestParam(required = false, value = "filter") String filter) {
        if (StringUtils.isNotBlank(filter)) {
            filter = filter.trim();
            List<Organization> matchingUsers1 = myVariableService.getListByText(filter);
            List<GroupRepresentation> result = new ArrayList<>();
            Group group=null;
            for (Organization groups : matchingUsers1) {
                group=new GroupEntityImpl();
                group.setId(groups.getUuid());
                group.setName(groups.getText());
                result.add(new GroupRepresentation(group));
            }
            return new ResultListDataRepresentation(result);
        }
        return null;
    }


    @Autowired
    protected ManagementService managementService;
    @Autowired
    protected MyVariableService myVariableService;

    @RequestMapping(value = "/rest/editor-users", method = RequestMethod.GET)
    public ResultListDataRepresentation getUsers(@RequestParam(value = "filter", required = false) String filter) {
        if (StringUtils.isNotBlank(filter)) {
            filter = filter.trim();
            List<com.sbtr.workflow.model.User> matchingUsers1 = myVariableService.getListByUserNamdIdOrUserName(filter);
            List<UserRepresentation> userRepresentations = new ArrayList<>();
            User user=null;
            for (com.sbtr.workflow.model.User users : matchingUsers1) {
                user =new UserEntityImpl();
                user.setId(users.getUuid());
                user.setFirstName(users.getUserNameId());
                user.setLastName(users.getUserName());
                userRepresentations.add(new UserRepresentation(user));
            }
            return new ResultListDataRepresentation(userRepresentations);
        }
        return null;
    }
}
