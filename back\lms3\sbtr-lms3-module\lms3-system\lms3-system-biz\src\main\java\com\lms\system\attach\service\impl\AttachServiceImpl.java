package com.lms.system.attach.service.impl;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.*;
import com.lms.system.attach.mapper.AttachMapper;
import com.lms.system.attach.service.AttachService;
import com.lms.system.feign.model.Attach;
import com.lms.system.storage.service.impl.UnifiedFileStorageServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Service("AttachService")
@Slf4j
public class AttachServiceImpl extends BaseServiceImpl<AttachMapper, Attach> implements AttachService {

    @Resource
    private AttachMapper attachDao;

    @Resource
    private UnifiedFileStorageServiceImpl fileStorageService;

    @Value("${spring.servlet.multipart.max-file-size}")
    private String maxSize;

    @Value("${minio.previewUrlExpiry}")
    private String previewUrlExpiry;

    @Resource
    private LogUtil logUtil;

    @Autowired(required = false)
    private RestHighLevelClient restHighLevelClient;

    @Value("${spring.elasticsearch.enabled:false}")
    private boolean elasticsearchEnabled;


    /**
     * 上传附件
     */
    @Transactional
    public Result uploadFile(MultipartFile file) throws Exception {
        String objectName = file.getOriginalFilename();
        // 上传文件，返回加密的文件名(yyyy-MM-dd-HH-mm-ss-UUID.扩展名)
        String encryptionName = fileStorageService.upload(file); //当日日期-uuid.后缀名
        // 构建并保存附件信息
        String suffix = null;//文件后缀名
        if (objectName != null) {
            suffix = objectName.substring(objectName.lastIndexOf("."));
        }
        Attach attach = new Attach();
        attach.setObjname(objectName); // 原始文件名
        attach.setEncryptname(encryptionName); // 加密文件名 (yyyy-MM-dd-HH-mm-ss-UUID.扩展名)
        attach.setFiledir(fileStorageService.getPreviewUrl(encryptionName));
        attach.setFiledirexpiretime(DateHelper.dayMoveDateTime(DateHelper.getCurDateTime(), 0, 0, 0, Integer.parseInt(previewUrlExpiry), 0, 0));
        attach.setFilesize(file.getSize());
        attach.setFiletype(file.getContentType());
        attach.setIsdelete(false);
        attach.setIsencrypt(false);
        attach.setIspublic(false);
        attach.setIszip(false);
        attach.setSuffix(suffix);
        attach.setUploadtime(DateHelper.getCurDateTime());
        InputStream in = null;
        in = file.getInputStream();
        BufferedImage sourceImg = ImageIO.read(in);
        if(sourceImg!=null){
            attach.setWidth(sourceImg.getWidth());
            attach.setHeight(sourceImg.getHeight());
        }
        in.close();
        // 原始文件名加密 查询时需要解密
        String attachName = attach.getObjname();
        if(!StringHelper.isEmpty(attachName) && StringHelper.isEmpty(attach.getId())){
            attachName = RsaUtils.encryptByPublicKey(attachName);
            attach.setObjname(attachName);
        }
        saveOrUpdate(attach);
        return Result.OK(attach);
    }

    private Result checkFileSize(MultipartFile file) {
        Long fileSize = file.getSize();
        Long ms = 0L;
        if(maxSize.indexOf("MB")>0){
            ms = Convert.toLong(maxSize.replace("MB","")) * 1024;
        }else if(maxSize.indexOf("GB")>0){
            ms = Convert.toLong(maxSize.replace("MB","")) * 1024 * 1024;
        }else{
            ms = NumberHelper.findNumberOnly(maxSize);
        }
        if(fileSize > ms){
            return Result.error("上传文件大小不能超过"+maxSize+"!");
        }else{
            return Result.OK();
        }
    }

    public Result getFileLimit(String filename) {
        String limitStr = "";
        int Lindex = filename.lastIndexOf("★");
        int Rindex = Math.max(filename.lastIndexOf(")"),filename.lastIndexOf("）"));
        if(Lindex > -1 && Rindex > -1 && Lindex < Rindex){
            //没有星号位置，或者括号位置左右顺序不对，当作无MJ
            limitStr = filename.substring(Lindex+1,Rindex);
        }
        return Result.OK(limitStr);
    }

    public String saveAttachByFilePath(File file) {
        String attachId = "";
        if (file.exists()) {
            String fileName = file.getName();
            String[] names = StringHelper.string2Array(fileName, ".");
            String suffix = names.length > 1 ? names[names.length - 1] : "";
            Attach attach = new Attach();
            attach.setObjname(fileName);
            attach.setFiledir(file.getAbsolutePath().substring(lmsConfiguration.getStoragepath().length()));
            attach.setFiledirexpiretime(DateHelper.dayMoveDateTime(DateHelper.getCurDateTime(), 0, 0, 0, Integer.parseInt(previewUrlExpiry), 0, 0));
            attach.setFilesize(file.length());
            attach.setFiletype("video/mp4");
            attach.setIsdelete(false);
            attach.setIsencrypt(false);
            attach.setIspublic(false);
            attach.setIszip(false);
            attach.setSuffix(suffix);
            attach.setUploadtime(DateHelper.getCurDateTime());
            String attachName = attach.getObjname();
            if(!StringHelper.isEmpty(attachName) && StringHelper.isEmpty(attach.getId())){
                attachName = RsaUtils.encryptByPublicKey(attachName);
                attach.setObjname(attachName);
            }
            this.saveOrUpdate(attach);
            attachId = attach.getId();
            commonSystemApi.saveLog(fileName, "保存附件信息", LogType.Save, "操作成功");
        }
        return attachId;
    }

    /**
     * 获取图片访问地址
     */
    public String getImageSrc(String imgid) {
        String imgsrc = "";
        if (!StringHelper.isEmpty(imgid)) {
            Attach attach = attachDao.selectById(imgid);
            if (attach != null) {
                imgsrc = fileStorageService.getPreviewUrl(attach.getEncryptname());
            }
        }
        return imgsrc;
    }

    /**
     * 判断文件是否存在 true存在
     */
    public boolean checkFileIsExist(String objName) {
        return fileStorageService.checkFileIsExist(objName);
    }

    public void downLoadFile(String filename, HttpServletResponse response) throws Exception {
        fileStorageService.download(filename, response);
    }

    public InputStream downLoadStream(String filename) throws Exception {
        return fileStorageService.download(filename);
    }

    public List getByIdList(List<String> attachIdList) {
        if (CollectionUtils.isEmpty(attachIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Attach> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Attach::getId, attachIdList);
        List<Attach> list = this.list(wrapper);
        if (CollectionUtils.isNotEmpty(list)){
            List<Attach> saveAttach = new ArrayList<>();
            for (Attach attach : list) {
                String curDateTime = DateHelper.getCurDateTime(); //当前时间
                String filedirexpiretime = attach.getFiledirexpiretime(); //预览url过期时间
                if (null == filedirexpiretime || DateHelper.compare(curDateTime, filedirexpiretime)) {
                    // 过期时间增加 1小时
                    attach.setFiledirexpiretime(DateHelper.dayMoveDateTime(filedirexpiretime == null ? curDateTime : filedirexpiretime, 0, 0, 0, Integer.parseInt(previewUrlExpiry), 0, 0));
                    // 获取最新的预览地址
                    attach.setFiledir(fileStorageService.getPreviewUrl(attach.getEncryptname()));
                    saveAttach.add(attach);
                }
            }
            if (CollectionUtils.isNotEmpty(saveAttach)){
                saveBatch(saveAttach);
            }
        }
        if (CollectionUtils.isNotEmpty(list)){
            for (Object o : list) {
                Attach attach = (Attach) o;
                String attachName =  attach.getObjname();
                if(!StringHelper.isEmpty(attachName)){
                    attachName = RsaUtils.decryptByPrivateKey(attachName);
                    attach.setObjname(attachName);
                }
            }
        }
        return list;

    }


    @Override
    public Attach getById(Serializable id) {
        Attach attach = super.getById(id);
        if (attach != null){
            String curDateTime = DateHelper.getCurDateTime(); //当前时间
            String filedirexpiretime = attach.getFiledirexpiretime(); //预览url过期时间
            if (null == filedirexpiretime || DateHelper.compare(filedirexpiretime, curDateTime)){
                // 过期时间增加 1小时
                attach.setFiledirexpiretime(DateHelper.dayMoveDateTime(filedirexpiretime == null ? curDateTime : filedirexpiretime, 0, 0, 0, Integer.parseInt(previewUrlExpiry), 0, 0));
                // 获取最新的预览地址
                attach.setFiledir(fileStorageService.getPreviewUrl(attach.getEncryptname()));
                this.updateById(attach);
            }
            String attachName =  attach.getObjname();
            if(!StringHelper.isEmpty(attachName)){
                try{
                    attachName = RsaUtils.decryptByPrivateKey(attachName);
                    attach.setObjname(attachName);
                }catch(Exception e){
                }
            }
        }
        return attach;
    }

    /**
     * 保存全文检索的文件
     */
    public void saveFile(String attachId) {
        Attach attach = this.getById(attachId);
        if (!(attach.getEncryptname().contains(".pdf")
                ||attach.getEncryptname().contains(".ppt")
                ||attach.getEncryptname().contains(".pptx")
                ||attach.getEncryptname().contains(".md")
                ||attach.getEncryptname().contains(".txt")
                ||attach.getEncryptname().contains(".xls")
                ||attach.getEncryptname().contains(".xlsx")
                ||attach.getEncryptname().contains(".docx")
                ||attach.getEncryptname().contains(".doc")
        )){
            return;
        }
        // 检查ES是否启用
        if (!elasticsearchEnabled || restHighLevelClient == null) {
            log.info("Elasticsearch已禁用，跳过文件内容索引: {}", attach.getObjname());
            return;
        }
        try(InputStream inputStream= fileStorageService.download(attach.getEncryptname())){
            String fileType = attach.getSuffix();
            // 读取文件内容，上传到es，方便后续的检索
            byte[] bytes = getContent(inputStream);
            String base64 = Base64.getEncoder().encodeToString(bytes);
            com.lms.common.model.File file = new com.lms.common.model.File();
            file.setId(attach.getId());
            file.setContent(base64);
            file.setFileName(attach.getObjname());
            file.setFilePath(attach.getFiledir());
            file.setFileType(fileType);
            file.setFileCategory("附件对象");
            file.setCreateBy(ContextUtil.getCurrentUser().getPersonid());
            file.setCreateTime(DateHelper.getCurDateTime());
            file.setMlevel(attach.getMlevel());
            IndexRequest indexRequest = new IndexRequest().index("file")
                    .source(JSON.toJSONString(file), XContentType.JSON)
                    .setPipeline("attachment")
                    .timeout(TimeValue.timeValueMinutes(10));
            restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
        }catch (Exception exception){
            logUtil.Error(exception);
        }
    }

    private byte[] getContent(InputStream inputStream) {
        try (ByteArrayOutputStream buffer = new ByteArrayOutputStream();){
            int bytesRead;
            byte[] data = new byte[1024];
            while ((bytesRead = inputStream.read(data, 0, data.length)) != -1){
                buffer.write(data, 0, bytesRead);
            }
            buffer.flush();
            return buffer.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }


    public Result uploadScormFile(MultipartFile file) {
        String attachId = "";
        String objectName = file.getOriginalFilename();
        if (file != null && !file.isEmpty()) {
            String filename2 = file.getOriginalFilename();
            String storePath = DateHelper.getCurrentYear() + "/"
                    + DateHelper.getCurrentMonth().replace("-", "") + "/"
                    + DateHelper.getCurrentDate().replace("-", "") + "/"
                    + commonSystemApi.getNewCode("sf");
            String absolutePath = lmsConfiguration.getScormStoragePath() + "/"
                    + storePath;
            File temp = new File(absolutePath, filename2);
            if (!temp.getParentFile().exists()) {
                temp.getParentFile().mkdirs();
            }
            try {
                file.transferTo(temp);
                String fileName = file.getOriginalFilename();
                String[] names = StringHelper.string2Array(fileName, ".");
                String suffix = names.length > 1 ? names[names.length - 1] : "";
                Attach attach = new Attach();
                attach.setObjname(fileName);
                attach.setEncryptname(fileName);
                attach.setFiledir(storePath + "/" + file.getOriginalFilename());
                attach.setFilesize(file.getSize());
                attach.setFiletype(file.getContentType());
                attach.setIsdelete(false);
                attach.setIsencrypt(false);
                attach.setIspublic(false);
                attach.setIszip(false);
                attach.setSuffix(suffix);
                attach.setUploadtime(DateHelper.getCurDateTime());
                attach.setFiledirexpiretime(DateHelper.dayMoveDateTime(DateHelper.getCurDateTime(), 20, 0, 0, 0, 0, 0));
                FileInputStream in = null;
                try {
                    in = new FileInputStream(temp);
                    BufferedImage sourceImg = ImageIO.read(in);
                    attach.setWidth(sourceImg.getWidth());
                    attach.setHeight(sourceImg.getHeight());
                    in.close();
                } catch (Exception ex) {
                    if (in != null) {
                        in.close();
                    }
                }
                saveOrUpdate(attach);
                attachId = attach.getId();

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return Result.OK(attachId);
    }

    public Boolean deleteFile(Attach attach) {
        boolean result = false;
        if (checkFileIsExist(attach.getEncryptname())){
            result = fileStorageService.deleteFile(attach.getEncryptname());
        }
        return result;
    }

}
