package com.lms.base.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import com.lms.common.model.LogObjname;
import com.lms.common.model.SearchContent;
import com.lms.common.model.SearchName;
import com.lms.common.model.SearchSelectItemContent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;

/**
 * PPerson entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)
@TableName("p_person")
@Data
@ApiModel(value = "人员实体类")
public class Person extends BaseModel {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private String id;

    @LogObjname
    @SearchContent
    @ApiModelProperty("姓名")
    private String name; //姓名

    @ApiModelProperty("部门")
    private String departmentid; //部门id

    @ApiModelProperty("部门全路径")
    private String departmentidfullpath; //部门id全路径

    @ApiModelProperty("部门")
    private String departmentids; //部门

    @ApiModelProperty("岗位")
    @SearchSelectItemContent
    private String duty; //岗位

    @ApiModelProperty("专业")
    @SearchSelectItemContent
    private String specialityid; //专业

    @TableField("PERSONLEVELID")
    @ApiModelProperty("人员层级")
    private String personlevelid;

    @ApiModelProperty("专业级别")
    private String specialitylevelid;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("民族")
    private String nation;

    @ApiModelProperty("电话")
    private String tel;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("毕业院校")
    @SearchContent
    private String graduate;

    @TableField("CARDNUM")
    @SearchName
    @ApiModelProperty("证件号")
    private String cardNum;

    @ApiModelProperty("所属角色")
    private String persontype;

    @ApiModelProperty("头像id")
    private String image;

    @TableField(exist = false)
    @ApiModelProperty("头像文件地址")
    private String imageUrl;

    @ApiModelProperty("工作单位")
    private String trainorg;

    @ApiModelProperty("账号")
    private String username;

    @ApiModelProperty("密码")
    private String password;
    /**
     * 限制用户登录ip地址
     */
    @ApiModelProperty("限制用户登录ip地址")
    private String ipaddr;

    /**
     * 限制用户登录浏览器类型
     */
    @ApiModelProperty("限制用户登录浏览器类型")
    private String clientagent;

    @ApiModelProperty("学位")
    @SearchSelectItemContent
    private String education; //学位

    @ApiModelProperty("职务")
    private String job; //职务

    @TableField(exist = false)
    @ApiModelProperty("部门")
    private Department department;

    @TableField(exist = false)
    @ApiModelProperty("部门名称")
    private String departmentname;

    @ApiModelProperty("状态")
    private Integer status;

    @TableField(exist = false)
    @ApiModelProperty("部门集合")
    private Set<Department> departments;

    // Constructors
    @ApiModelProperty("培训次数")
    private Integer trainnum;

    @ApiModelProperty("培训经历")
    private String traincontent;

    @ApiModelProperty("维护型号")
    private String modelnum; //维护型号

    @ApiModelProperty("人员密级")
    private Integer slevel;

    /**
     * 出生年月
     */
    @ApiModelProperty("出生年月")
    private String birthday;

    /**
     * 工作年份
     */
    @ApiModelProperty("工作年份")
    private String seniority;


    /**
     * 备注
     */
    @SearchContent
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 工作年限
     */
    @ApiModelProperty("工作年限")
    private String years;

    /**
     * 专业及主要成果
     */
    @ApiModelProperty("专业及主要成果")
    private String majors;

    /**
     * 教学型号
     */
    @ApiModelProperty("教学型号")
    private String teachingmodel;

    @TableField(exist = false)
    @ApiModelProperty("教学型号列表")
    private List<String> lastteachingmodellist;

    @TableField(exist = false)
    @ApiModelProperty("教学型号名称")
    private String teachingmodelname;

    @ApiModelProperty("管理型号")
    private String managemodel;

    @TableField(exist = false)
    @ApiModelProperty("教学型号列表")
    private List<String> lastmanagemodellist;

    @TableField(exist = false)
    @ApiModelProperty("教学型号名称")
    private String managemodelname;


    @ApiModelProperty("所属型号")
    private String equipmentid;

    @TableField(exist = false)
    @ApiModelProperty("所属型号名称")
    private String equipmentname;

    @TableField(exist = false)
    @ApiModelProperty("所属型号列表")
    private List<String> lastequipmentidlist;

    /**
     * 教员类别 理论 实操
     */
    @ApiModelProperty("教员类别")
    private String teachercategory;

    /**
     * 教员等级  初级教员、中级教员、高级教员
     */
    @ApiModelProperty("教员等级")
    private String teacherlevel;

    /**
     * 教学课程分类
     */
    @ApiModelProperty("教学课程分类")
    private String teachercourse;

    /**
     * 教学课程分类
     */
    @ApiModelProperty("考核不合格次数")
    private Integer examinefailtimes;

}
