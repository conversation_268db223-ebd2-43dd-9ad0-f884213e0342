package com.lms.base.course.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lms.base.component.service.ComponentService;
import com.lms.base.course.mapper.CourseMapper;
import com.lms.base.course.service.CourseService;
import com.lms.base.feign.model.Component;
import com.lms.base.feign.model.Course;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.dao.query.Parameter;
import com.lms.common.enums.RoleEum;
import com.lms.common.feign.dto.TreeNode;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.StringHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@Service("CourseService")
public class CourseServiceImpl extends BaseServiceImpl<CourseMapper, Course> implements CourseService {

    @Resource
    private CourseMapper courseMapper;
    @Resource
    private ComponentService componentService;

    public List<Course> getCourseListByComponentId(String componentId) {
        PageInfo pageInfo = new PageInfo();
        if (!StringHelper.isEmpty(componentId)) {
            Parameter attachIdParam = Parameter.getParameter(
                    "S_EQ_componentId", componentId);
            pageInfo.getParameters().add(attachIdParam);
        }
        return getBean().listByCondition(pageInfo).getRecords();
    }

    public List<Course> getCourseListByComponentIdList(List<String> componentIdList) {
        if (CollectionUtils.isEmpty(componentIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Course> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Course::getComponentId, componentIdList);
        return this.list(wrapper);
    }

    public List<TreeNode> getCourseStructureTree(PageInfo pageInfo) {
        boolean isStudent = RoleEum.student.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
        List<TreeNode> jbcArray = new ArrayList<>();
        String pid = "";
        if (isStudent) {
            String equipmentId = ContextUtil.getEquipmentId();
            if (StringHelper.isEmpty(equipmentId)) {
                // 当前用户为学员，没有从事型号信息返回空值
                return jbcArray;
            }
            String[] equipmentIds = ContextUtil.getEquipmentId().split(",");
            for (String eqid : equipmentIds) {
                String[] ids = eqid.split("/");
                int length = ids.length;
                pid = length < 2 ? eqid : ids[length - 1];
//                Component component = componentService.get(pid);
//                JSONObject jsonObject = setObjetDetails(component, pageInfo);
                jbcArray.addAll(componentService.getComponentTree2(pid, true));
            }
        } else {
            List<TreeNode> componentList = new ArrayList<>();
            boolean isAdmin = RoleEum.admin.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
            boolean isLeader = RoleEum.leader.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
            boolean isTeacher = RoleEum.teacher.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
            String equipmentId = isTeacher ? ContextUtil.getCurrentUser().getTeachingmodel() : ContextUtil.getCurrentUser().getManagemodel();
            if ((isAdmin | isLeader | isTeacher) && org.apache.commons.lang3.StringUtils.isEmpty(pid) && org.apache.commons.lang3.StringUtils.isNotEmpty(equipmentId)) {
                String[] componentArray = equipmentId.split(",");
                if (componentArray.length > 0) {
                    for (String componentId : componentArray) {
                        if (org.apache.commons.lang3.StringUtils.isNotEmpty(componentId)) {
                            String[] split = componentId.split("/");
//                            Component component = componentService.get(split[split.length - 1]);
//                            componentList.add(component);
                            componentList.addAll(componentService.getComponentTree2(split[split.length - 1], true));
                        }
                    }
                }
            } else {
                componentList.addAll(componentService.getComponentTree2("", true));
            }
            jbcArray = componentList;
//            for (Component component : componentList) {
//                JSONObject jb = setObjetDetails(component, pageInfo);
//                if (jb != null && jb.size() > 0) {
//                    jbcArray.add(jb);
//                }
//            }
        }
        return jbcArray;
    }

    public JSONObject setObjetDetails(Component component, PageInfo pageInfo) {
        boolean isStudent = RoleEum.student.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
        boolean isTeacher = RoleEum.teacher.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
        String equipmentid = "";
        if (isStudent) { //学员
            equipmentid = ContextUtil.getCurrentUser().getEquipmentid();
        } else if (isTeacher) { //教员
            equipmentid = ContextUtil.getCurrentUser().getTeachingmodel();
        } else {
            equipmentid = ContextUtil.getCurrentUser().getManagemodel();
        }
        // 如果当前用户不是学员并且当前所处的教学型号或管理型号未设置,不进行数据过滤
        List<String> currentusercomponentid = ContextUtil.getCurrentUser().getCurrentusercomponentid();
        if (!isStudent && !StringUtils.isEmpty(equipmentid)
                && (CollectionUtils.isNotEmpty(currentusercomponentid) && !currentusercomponentid.contains(component.getId()))) {
            return new JSONObject();
        }
        JSONObject jsonObject = new JSONObject();
        String pid = StringHelper.null2String(component.getParentid());
        jsonObject.put("pid", pid);
        jsonObject.put("id", StringHelper.null2String(component.getId()));
        jsonObject.put("name", StringHelper.null2String(component.getName()));
        jsonObject.put("courseFlag", component.getCourseFlag());
        jsonObject.put("courseType", "");
        jsonObject.put("status", component.getStatus());
        String componentId = StringHelper.null2String(component.getId());
        if (component.getCourseFlag().equals("0")) {
            //不是课程构型节点标志；
            List<Component> componentList = componentService.getAllComponentsByPid(componentId);
            if (componentList.size() > 0) {
                JSONArray jbcArray = new JSONArray();
                for (Component temp : componentList) {
                    JSONObject jbc = setObjetDetails(temp, pageInfo);
                    if (jbc != null && jbc.size() > 0) {
                        jbcArray.add(jbc);
                    }
                }
                jsonObject.put("children", jbcArray);
            }
        } else {
            PageInfo newPage = SerializationUtils.clone(pageInfo);
            if (!StringHelper.isEmpty(componentId)) {
                Parameter attachIdParam = Parameter.getParameter(
                        "S_EQ_componentId", componentId);
                newPage.getParameters().add(attachIdParam);
            }
            List<Course> courseList = getBean().listByCondition(newPage).getRecords();
            if (courseList.size() > 0) {
                JSONArray jbcArray = new JSONArray();
                for (Course course : courseList) {
                    JSONObject jbc = new JSONObject();
                    jbc.put("pid", componentId);
                    jbc.put("id", StringHelper.null2String(course.getId()));
                    jbc.put("name", StringHelper.null2String(course.getCourseName()));
                    jbc.put("courseFlag", "2");
                    jbc.put("courseType", StringHelper.null2String(course.getCourseType()));
                    jbc.put("status", course.getStatus());
                    jbcArray.add(jbc);
                }
                jsonObject.put("children", jbcArray);
            }
        }
        return jsonObject;
    }

    public void batchReleaseCourse(List<String> idList) {
        LambdaUpdateWrapper<Course> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Course::getStatus, 5);
        updateWrapper.in(Course::getId, idList);
        this.update(updateWrapper);
    }

    public Boolean existCourse(Course course) {
        LambdaQueryWrapper<Course> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(course.getId())) {
            wrapper.ne(Course::getId, course.getId());
        }
        wrapper.eq(Course::getCourseName, course.getCourseName());
        wrapper.eq(Course::getVersion, course.getVersion());
        wrapper.select(Course::getId);
        List<Course> list = this.list(wrapper);
        return CollectionUtils.isNotEmpty(list);
    }

    public Boolean existCourseNo(Course course) {
        LambdaQueryWrapper<Course> wrapper = new LambdaQueryWrapper<>();
        String courseid = StringHelper.null2String(course.getId());
        if (StringUtils.isNotEmpty(courseid)){
            wrapper.ne(Course::getId, courseid);
        }
        wrapper.eq(Course::getNumber, course.getNumber());
        return CollectionUtils.isNotEmpty(this.list(wrapper));
    }

    @Override
    public Long getTotalCourse() {
        return this.count();
    }

    public List<Course> getByCourseIdList(List<String> courseIdList) {
        if (CollectionUtils.isNotEmpty(courseIdList)) {
            return this.listByIds(courseIdList);
        } else {
            return null;
        }
    }

    public List<Course> selectByTeacherIdList(List<String> personIdList) {
        LambdaQueryWrapper<Course> wrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isNotEmpty(personIdList)) {
            wrapper.in(Course::getTeacherId, personIdList);
            return this.list(wrapper);
        } else {
            return null;
        }
    }

}
