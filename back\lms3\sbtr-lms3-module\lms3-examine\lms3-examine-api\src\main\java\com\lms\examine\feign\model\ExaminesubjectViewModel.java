package com.lms.examine.feign.model;

import com.lms.common.feign.api.CommonSystemApi;
import com.lms.common.model.BaseModel;
import com.lms.common.util.SpringContextUtils;
import com.lms.common.util.StringHelper;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class ExaminesubjectViewModel extends BaseModel {
	private static final long serialVersionUID = -3616943288084994218L;
	private Examinesubject examinesubject;
	private String id;
	private List<String> items = new ArrayList<String>();
	private List selectitems = new ArrayList();
	private CommonSystemApi languagelabelService;
	public ExaminesubjectViewModel(Examinesubject examinesubject) {
		this.examinesubject = examinesubject;
		this.id = StringHelper.null2String(examinesubject.getId());
		languagelabelService = SpringContextUtils.getBean(CommonSystemApi.class);
		if ("9e47efc0ce894454856a80171e1e6efe".equals(examinesubject.getType())
		 || "3ef580ec56ae436eb79b91b25d1a078e".equals(examinesubject.getType())) {
			this.setItems(parse(this.examinesubject.getContent()));
		} else if ("23e162b9f27b4ebc9a5c93db09913693".equals(examinesubject.getType())) {
			this.items.add(languagelabelService.translateContentNotCreated("正确",""));
			this.items.add(languagelabelService.translateContentNotCreated("错误",""));
		}
	}
	public ExaminesubjectViewModel(Examinesubject examinesubject, int count) {
		this.examinesubject = setIndex(examinesubject,count);
		this.id = StringHelper.null2String(examinesubject.getId());
		languagelabelService = SpringContextUtils.getBean(CommonSystemApi.class);
		if ("9e47efc0ce894454856a80171e1e6efe".equals(examinesubject.getType())
		 || "3ef580ec56ae436eb79b91b25d1a078e".equals(examinesubject.getType())) {
			this.setItems(parse(this.examinesubject.getContent(),true));
		} else if ("23e162b9f27b4ebc9a5c93db09913693".equals(examinesubject.getType())) {
			this.items.add(languagelabelService.translateContentNotCreated("A、正确",""));
			this.items.add(languagelabelService.translateContentNotCreated("B、错误",""));
		}
	}
	private Examinesubject setIndex(Examinesubject examinesubject, int count) {
			String index = count + 1 + "";
			String title = StringHelper.null2String(examinesubject.getTitle());
			title = index + "、" + title;
			examinesubject.setFulltitle(title);
			return examinesubject;
		}
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	private List<String> parse(String content) {
		List<String> items = new ArrayList<String>();
		try {
			org.dom4j.Document doc = DocumentHelper.parseText(content);
			Element root = doc.getRootElement();
			Element child;
			for(Iterator it = root.elementIterator("item");it.hasNext();)
			{
				child=(Element)it.next();
				if(!child.getStringValue().isEmpty()){
				items.add(child.getStringValue());
				}
			}


		} catch (Exception ex) {
		}
		return items;
	}
	private List<String> parse(String content, boolean isExport) {
		List<String> items = new ArrayList<String>();
		try {
			org.dom4j.Document doc = DocumentHelper.parseText(content);
			Element root = doc.getRootElement();
			Element child;
//			for(Iterator it = root.elementIterator("item");it.hasNext();)
//			{
//				child=(Element)it.next();
//				items.add(child.getStringValue());
//			}
			for(int i=0;i<root.elements("item").size();i++){
				child=(Element)root.elements("item").get(i);
				String index = "";
				switch (i) {
				case 0:
					index = "A、";
					break;
				case 1:
					index = "B、";
					break;
				case 2:
					index = "C、";
					break;
				case 3:
					index = "D、";
					break;
				case 4:
					index = "E、";
					break;
				case 5:
					index = "F、";
					break;
				case 6:
					index = "G、";
					break;
				default:
					break;
				}
				items.add(index+child.getStringValue());
			}

		} catch (Exception ex) {
		}
		return items;
	}
	public List getSelectitems() {
		return selectitems;
	}

	public void setSelectitems(List selectitems) {
		this.selectitems = selectitems;
	}

	public List<String> getItems() {
		return items;
	}

	public void setItems(List<String> items) {
		this.items = items;
	}

	public Examinesubject getExaminesubject() {
		return examinesubject;
	}

	public void setExaminesubject(Examinesubject examinesubject) {
		this.examinesubject = examinesubject;
	}

}
