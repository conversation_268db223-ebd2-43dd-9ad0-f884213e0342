package com.sbtr.workflow.utils.tree;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 树节点，所有需要实现树节点的，都需要继承该类
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-05-18 13:40:36
 */
public class TreeNode<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String id;
    /**
     * 上级ID
     */
    private String pid;
    /**
     * 子节点列表
     */
    private List<T> children = new ArrayList<>();


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public List<T> getChildren() {
        return children;
    }

    public void setChildren(List<T> children) {
        this.children = children;
    }
}