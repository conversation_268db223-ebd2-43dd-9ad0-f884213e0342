package com.lms.base.selectitem.service;

import com.lms.base.feign.model.Selectitem;
import com.lms.common.service.BaseService;

import java.util.ArrayList;
import java.util.List;

public interface SelectitemService extends BaseService<Selectitem> {

    List<Selectitem> getSelectitemsByType(String id);

    List<Selectitem> getAllSelectitemsByType(String typeid);

    boolean existName(String id, String objname, String typeid);

    Selectitem getSelectitemByTypeName(String name, String typeid);

    List<Selectitem> getByNameList(ArrayList<String> strings);
}
