package com.sbtr.workflow.enums;

/**
 * 业务操作类型
 *
 * <AUTHOR>
 * @date 20240627
 */
public enum BusinessType {
    /**
     * 其它
     */
    OTHER,
    /**
     * 新增
     */
    INSERT,
    /**
     * 修改
     */
    UPDATE,
    /**
     * 删除
     */
    DELETE,
    /**
     * 授权
     */
    GRANT,
    /**
     * 导出
     */
    EXPORT,
    /**
     * 导入
     */
    IMPORT,
    /**
     * 签入
     */
    CHECKIN,
    /**
     * 签出
     */
    CHECKOUT,
    /**
     * 发布
     */
    ISSUE,
    /**
     * 登入
     */
    LOGIN,
    /**
     * 登出
     */
    LOGOUT,
    /**
     * 上传
     */
    UPLOAD,
    /**
     * 流程部署
     */
    DEPLOY,
    /**
     * 流程启动
     */
    START,
    /**
     * 流程中断
     */
    INTERRUPT,
    /**
     * 流程激活
     */
    ACTIVATE,
    /**
     * 同意
     */
    AGREE,
    /**
     * 驳回
     */
    REJECT,
    /**
     * 转办
     */
    TRANSFER,
    /**
     * 传阅
     */
    CIRCULATION,
    /**
     * 委派
     */
    DELEGATE,
    /**
     * 加签
     */
    SIGNATURE,
    /**
     * 催办
     */
    PROCESSURGING,
    /**
     * 退回上一步
     */
    GOBACKTOTHEPREVIOUSSTEP,
    /**
     * 指定处理人
     */
    ASSIGNEDTO,
    /**
     * 审批
     */
    APPROVE,
    /**
     * 终止
     */
    FINISH,
    /**
     * 撤回
     */
    REVOCATION;
}
