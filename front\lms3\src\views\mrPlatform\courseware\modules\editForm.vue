<template>
  <div class="edit-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      layout="horizontal"
    >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="课件名称" name="name">
            <a-input
              v-model:value="formData.name"
              placeholder="请输入课件名称"
              allow-clear
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="课件编号" name="number">
            <a-input
              v-model:value="formData.number"
              placeholder="请输入课件编号"
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="课件类型" name="coursewaretype">
            <a-select
              v-model:value="formData.coursewaretype"
              placeholder="请选择课件类型"
              allow-clear
            >
              <a-select-option
                v-for="item in coursewaretypeOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="版本" name="version">
            <a-input
              v-model:value="formData.version"
              placeholder="请输入版本号"
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="责任人" name="principal">
            <a-input
              v-model:value="formData.principal"
              placeholder="请输入责任人"
              allow-clear
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="责任单位" name="dutyunit">
            <a-input
              v-model:value="formData.dutyunit"
              placeholder="请输入责任单位"
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="语言" name="language">
            <a-select
              v-model:value="formData.language"
              placeholder="请选择语言"
              allow-clear
            >
              <a-select-option value="zh-CN">中文</a-select-option>
              <a-select-option value="en-US">英文</a-select-option>
              <a-select-option value="ja-JP">日文</a-select-option>
              <a-select-option value="ko-KR">韩文</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="状态" name="status">
            <a-select v-model:value="formData.status" placeholder="请选择状态">
              <a-select-option :value="1">启用</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="关键词" name="keyword">
        <a-input
          v-model:value="formData.keyword"
          placeholder="请输入关键词，多个关键词用逗号分隔"
          allow-clear
        />
      </a-form-item>

      <a-form-item label="描述" name="description">
        <a-textarea
          v-model:value="formData.description"
          placeholder="请输入课件描述"
          :rows="4"
          allow-clear
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, defineProps, defineEmits } from "vue";
import type { FormInstance } from "ant-design-vue";

import { coursewaretypeOptions } from "@/data/mrPlatform";
interface Props {
  model: any;
  mode: "add" | "edit";
  coursewaretypeOptions: Array<{ value: string; label: string }>;
}

interface Emits {
  (e: "submit", data: any): void;
  (e: "cancel"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive<any>({
  name: "",
  number: "",
  coursewaretype: "",
  principal: "",
  dutyunit: "",
  version: "",
  description: "",
  keyword: "",
  language: "",
  status: 1
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: "请输入课件名称", trigger: "blur" },
    { max: 100, message: "课件名称不能超过100个字符", trigger: "blur" }
  ],
  number: [
    { required: true, message: "请输入课件编号", trigger: "blur" },
    { max: 50, message: "课件编号不能超过50个字符", trigger: "blur" }
  ],
  coursewaretype: [
    { required: true, message: "请选择课件类型", trigger: "change" }
  ],
  principal: [
    { required: true, message: "请输入责任人", trigger: "blur" },
    { max: 50, message: "责任人不能超过50个字符", trigger: "blur" }
  ],
  dutyunit: [
    { required: true, message: "请输入责任单位", trigger: "blur" },
    { max: 100, message: "责任单位不能超过100个字符", trigger: "blur" }
  ],
  version: [
    { required: true, message: "请输入版本号", trigger: "blur" },
    { max: 20, message: "版本号不能超过20个字符", trigger: "blur" }
  ]
};

// 监听props.model变化，更新表单数据
watch(
  () => props.model,
  newModel => {
    if (newModel) {
      Object.assign(formData, newModel);
    }
  },
  { immediate: true, deep: true }
);

// 提交表单
async function submit() {
  try {
    await formRef.value?.validate();
    emit("submit", { ...formData });
  } catch (error) {
    console.error("表单验证失败:", error);
  }
}

// 重置表单
function reset() {
  formRef.value?.resetFields();
}

// 暴露方法给父组件
defineExpose({
  submit,
  reset
});
</script>

<style lang="scss" scoped>
.edit-form {
  padding: 16px 0;
}
</style>
