<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sbtr.workflow.mapper.OaLeaveMapper">


    <select id="getToDoTasks" resultType="com.sbtr.workflow.model.OaLeaveCommon">
        SELECT DISTINCT
        t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,t2.PROC_DEF_ID_ as processDefinitionId,
        t1.CREATE_TIME_ AS startTime,
        t1.ASSIGNEE_ as assage,ol.title,ol.days,ol.begin_Time,ol.end_Time,ol.reason, ol.item
        FROM
        act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN oa_leave ol on ol.uuid=t2.BUSINESS_KEY_
        <where>
            t2.BUSINESS_KEY_ IS NOT NULL
            AND t1.ASSIGNEE_ = #{userNmeId} AND
            <if test="filterSort != null">
                ${filterSort}
            </if>
        </where>
    </select>

    <select id="getTasksInProgress" resultType="com.sbtr.workflow.model.OaLeaveCommon">
        SELECT DISTINCT
        RES.*,
        P.KEY_ AS ProcessDefinitionKey,
        RES.PROC_INST_ID_ AS processInstanceId,
        P.NAME_ AS ProcessDefinitionName,
        P.VERSION_ AS ProcessDefinitionVersion,RES.PROC_DEF_ID_ as processDefinitionId,
        P.DEPLOYMENT_ID_ AS DeploymentId ,ol.title,
        ol.days,
        ol.begin_Time,
        ol.end_Time,
        ol.reason,
        ol.item
        FROM
        ACT_RU_EXECUTION RES
        INNER JOIN ACT_RE_PROCDEF P ON RES.PROC_DEF_ID_ = P.ID_
        LEFT JOIN oa_leave ol ON ol.uuid = RES.BUSINESS_KEY_

        <where>
            RES.PARENT_ID_ IS NULL
            AND RES.START_USER_ID_ =#{userNmeId} AND
            <if test="filterSort != null">
                ${filterSort}
            </if>
        </where>


    </select>

    <select id="getHistoryTasks" resultType="com.sbtr.workflow.model.OaLeaveCommon">
        SELECT DISTINCT
        RES.BUSINESS_KEY_,
        DEF.KEY_ AS PROC_DEF_KEY_,
        DEF.NAME_ AS PROC_DEF_NAME_,
        DEF.VERSION_ AS PROC_DEF_VERSION_,
        DEF.DEPLOYMENT_ID_ AS DEPLOYMENT_ID_,RES.PROC_DEF_ID_ as processDefinitionId, RES.PROC_INST_ID_ AS
        processInstanceId,
        ol.title,
        ol.days,
        ol.begin_Time,
        ol.end_Time,
        ol.reason,
        ol.item,RES.END_TIME_
        FROM
        ACT_HI_PROCINST RES
        LEFT OUTER JOIN ACT_RE_PROCDEF DEF ON RES.PROC_DEF_ID_ = DEF.ID_
        LEFT JOIN oa_leave ol ON ol.uuid = RES.BUSINESS_KEY_

        <where>
            RES.END_TIME_ IS NOT NULL
            AND RES.START_USER_ID_ =#{userNmeId} AND
            <if test="filterSort != null">
                ${filterSort}
            </if>
        </where>

    </select>

    <sql id="Base_Column_List">
  	  	     			 uuid
		  ,  	  	     			 creator_id
		  ,  	  	     			 creator
		  ,  	  	     			 create_time
		  ,  	  	     			 modifier_id
		  ,  	  	     			 modifier
		  ,  	  	     			 modify_time
		  ,  	  	     			 begin_time
		  ,  	  	     			 end_time
		  ,  	  	     			 item
		  ,  	  	     			 reason
		  ,  	  	     			 title
		  ,  	  	     			 creator_org_id
		  ,  	  	     			 days,state
		    	  </sql>


    <update id="updateStateByUuid">
            update oa_leave set state=#{state} where uuid = #{businessKey}
    </update>

    <delete id="executeDeleteBatch">
        delete from oa_leave where uuid in
        <foreach item="uuid" collection="array" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </delete>


</mapper>
