package com.sbtr.workflow.utils.StringUtil;

import cn.hutool.core.text.CharSequenceUtil;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串处理工具类
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Date 2016/5/13 9:53
 */
public class StringUtils extends CharSequenceUtil {

    /**
     * 转换null字符串为空值
     *
     * @param str
     * @return
     */
    public static String convertNullToEmpty(Object str) {
        return str == null ? "" : str.toString();
    }
    /**
     * 替换字符串中的空格
     *
     * @param str 要替换的字符串
     * @return 返回替换后的字符串
     */
    public static String replaceWithBlank(String str) {
        if (str != null && !"".equals(str)) {
            Pattern p = Pattern.compile("\\s*|\t|\r|\n");
            Matcher m = p.matcher(str);
            String strNoBlank = m.replaceAll("");
            return strNoBlank;
        } else {
            return str;
        }

    }

    /**
     * 将以逗号分割的字段字符串转成带单引号的逗号分割的字符串
     *
     * @param str
     * @return
     */
    public static String str2SqlInStr(String str) {
        String[] strArray = str.split(",");
        String sqlInStr = "";
        for (int i = 0; i < strArray.length; i++) {
            if (i == strArray.length - 1) {
                sqlInStr += "'" + strArray[i] + "'";
            } else {
                sqlInStr += "'" + strArray[i] + "',";
            }
        }
        return sqlInStr;
    }

    /**
     * 检测以分隔符分开的字符串中是否包含某个字符串
     *
     * @param containStr 包含的字符串
     * @param str        检测的字符串
     * @param delimiter  分隔符
     * @return 返回boolean值
     */
    public static boolean isContainInStrByDelimiter(String containStr, String str, String delimiter) {
        return isContainInArray(convertToArray(str, delimiter), containStr);
    }

    /**
     * 字符串数组中是否包含某个字符串
     *
     * @param strArray   字符串数组
     * @param containStr 包含的字符串
     * @return 返回boolean值
     */
    public static boolean isContainInArray(String[] strArray, String containStr) {
        // 利用list的包含方法,进行判断
        if (Arrays.asList(strArray).contains(containStr)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 以固定分隔符分开的字符串转换为数组
     *
     * @param str       字符串
     * @param delimiter 分隔符
     * @return 返回数组
     */
    public static String[] convertToArray(String str, String delimiter) {
        String[] strArray = null;
        //拆分字符为"," ,然后把结果交给数组strArray
        strArray = str.split(delimiter);
        return strArray;
    }

    /**
     * 把第一个字母变为小写<br>
     * 如：<br>
     * <code>str = "UserDao";</code><br>
     * <code>return "userDao";</code>
     *
     * @param str
     * @return
     */
    public static String convertFirstCharToLowercase(String str) {
        if (!(str.isEmpty() || str.equals(""))) {
            return str.substring(0, 1).toLowerCase() + str.substring(1);
        } else {
            return "";
        }
    }

    /**
     * 把第一个字母变为小写<br>
     * 如：<br>
     * <code>str = "UserDao";</code><br>
     * <code>return "userDao";</code>
     *
     * @param str
     * @return
     */
    public static String convertFirstCharToUppercase(String str) {
        if (!(str.isEmpty() || str.equals(""))) {
            return str.substring(0, 1).toUpperCase() + str.substring(1);
        } else {
            return "";
        }
    }

    public static boolean hasLength(String str) {
        return !(str.equals(null) || str.equals(""));
    }

    /**
     * str转list
     *
     * @param str
     * @param separator
     * @return
     */
    public static List<String> str2List(String str, String separator) {
        return Arrays.asList(str.replace("'", "").split(separator));
    }

    /**
     * str转list字符，逗号分隔
     *
     * @param str
     * @return
     */
    public static List<String> str2ListByComma(String str) {
        return str2List(str, ",");
    }

    /**
     * str转list对象，逗号分隔
     *
     * @param str
     * @return
     */
    public static List<Object> str2ListObjByComma(String str) {
        return (List<Object>) (List<?>) str2ListByComma(str);
    }


    /**
     * 将格林威治时间字符串转换为yyyy-MM-dd HH:mm:ss字符串，JDK1.7以上版本支持该方法
     *
     * @param s
     * @return
     */
    public static String DateString2formatString(String s) {
        String str = "";
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX");
            Date date = sd.parse(s);
            str = sdf.format(date);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            return str;
        }
        return str;
    }


    /**
     * 判断一个字符是否是中文
     */
    public static boolean isChineseCharacter(char c) {
        // 根据字节码判断
        return c >= 0x4E00 && c <= 0x9FA5;
    }

    /**
     * 判断一个字符串是否包含中文
     */
    public static boolean hasChineseCharacter(String str) {
        if (str == null) {
            return false;
        } ;
        for (char c : str.toCharArray()) {
            // 有一个中文字符就返回
            if (isChineseCharacter(c)){
                return true;
            }
        }
        return false;
    }

    /**
     * 判断表名只能小写字母 下划线 中划线
     *
     * @param str
     * @return true为包含，false为不包含
     */
    public static boolean isTableName(String str) {
        String regEx = "^[a-z][a-z_]*$";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.find();
    }

    /**
     * 判断数字和字母，且长度要在4-50位之间
     *
     * @param str
     * @return true为包含，false为不包含
     */
    public static boolean isAlphanumeric(String str) {
        String regEx = "^[0-9A-Za-z]{4,50}$";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.find();
    }
    /**
     * 字符串是否包含中文
     *
     * @param str 待校验字符串
     * @return true 包含中文字符 false 不包含中文字符
     */
    public static boolean isContainChinese(String str)  {
        Pattern p = Pattern.compile("[\u4E00-\u9FA5|\\！|\\，|\\。|\\（|\\）|\\《|\\》|\\“|\\”|\\？|\\：|\\；|\\【|\\】]");
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        }
        return false;
    }
    /**
     * 驼峰转换为下划线
     *
     * @param camelCaseName
     * @return
     */
    public static String underscoreName(String camelCaseName) {
        StringBuilder result = new StringBuilder();
        if (camelCaseName != null && camelCaseName.length() > 0) {
            result.append(camelCaseName.substring(0, 1).toLowerCase());
            for (int i = 1; i < camelCaseName.length(); i++) {
                char ch = camelCaseName.charAt(i);
                if (Character.isUpperCase(ch)) {
                    result.append("_");
                    result.append(Character.toLowerCase(ch));
                } else {
                    result.append(ch);
                }
            }
        }
        return result.toString();
    }

    /**
     * 下划线转换为驼峰
     *
     * @param underscoreName
     * @return
     */
    public static String camelCaseName(String underscoreName) {
        StringBuilder result = new StringBuilder();
        if (underscoreName != null && underscoreName.length() > 0) {
            boolean flag = false;
            for (int i = 0; i < underscoreName.length(); i++) {
                char ch = underscoreName.charAt(i);
                if ("_".charAt(0) == ch) {
                    flag = true;
                } else {
                    if (flag) {
                        result.append(Character.toUpperCase(ch));
                        flag = false;
                    } else {
                        result.append(ch);
                    }
                }
            }
        }
        return result.toString();
    }
    /**
     * 判断字符串是否为null或空值
     *
     * @param str
     * @return boolean 为null或空值返回true
     */
    public static boolean isNullOrEmpty(String str) {
        return (str == null || "".equals(str)) ? true : false;
    }


    //oracle 下划线转驼峰
    public static String UnderlineToHump(String para){
        StringBuilder result=new StringBuilder();
        String a[]=para.split("_");
        for(String s:a){
            if (!para.contains("_")) {
                result.append(s.toLowerCase());
                continue;
            }
            if(result.length()==0){
                result.append(s.toLowerCase());
            }else{
                result.append(s.substring(0, 1).toUpperCase());
                result.append(s.substring(1).toLowerCase());
            }
        }
        return result.toString();
    }

    /**
     *
     *  @return true为包含，false为不包含 */
    /**
     * 判断是否含有特殊字符
     *
     * @param str
     * @return true为包含，false为不包含
     */
    public static boolean isSpecialChar(String str) {
        String regEx = "[ _`~!@#$%^&*()+=|{}':;',\\[\\]<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]|\n|\r|\t";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.find();
    }

    /**
     * 判断字符串数组中是否存在某个字段
     *
     * @param arr
     * @param targetValue
     * @return boolean
     */
    public static boolean useLoop(String[] arr, String targetValue) {
        for (String s : arr) {
            if (s.equals(targetValue)){
                return true;
            }
        }
        return false;
    }


    private static final Pattern linePattern = Pattern.compile("_(\\w)");

    /** 下划线转驼峰 */
    public static String lineToHump(String str) {
        str = str.toLowerCase();
        Matcher matcher = linePattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }


    /**
     * 截取字符串str中指定字符 strStart、strEnd之间的字符串
     *
     * @param str
     * @param strStart
     * @param strEnd
     * @return
     */
    public static String subString(String str, String strStart, String strEnd) {

        /* 找出指定的2个字符在 该字符串里面的 位置 */
        int strStartIndex = str.indexOf(strStart);
        int strEndIndex = str.indexOf(strEnd);

        /* index 为负数 即表示该字符串中 没有该字符 */
        if (strStartIndex < 0) {
            return "-1";
            //return "字符串 :---->" + str + "<---- 中不存在 " + strStart + ", 无法截取目标字符串";
        }
        if (strEndIndex < 0) {
            return "-1";
            //return "字符串 :---->" + str + "<---- 中不存在 " + strEnd + ", 无法截取目标字符串";
        }
        /* 开始截取 */
        String result = str.substring(strStartIndex, strEndIndex).substring(strStart.length());
        return result;
    }
}
