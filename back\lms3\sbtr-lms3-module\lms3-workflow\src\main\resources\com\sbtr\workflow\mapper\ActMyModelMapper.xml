<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ActMyModelMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sbtr.workflow.model.ActMyModel" id="flowModelMap">
        <result property="uuid" column="uuid"/>
        <result property="actDeModelId" column="act_de_model_id"/>
        <result property="formDesign" column="form_design"/>
        <result property="flowDesign" column="flow_design"/>
        <result property="createTime" column="create_time"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorOrgId" column="creator_org_id"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="actDeModelKey" column="act_de_model_key"/>
        <result property="formTableName" column="form_table_name"/>
    </resultMap>

    <sql id="Base_Column_List">
  	  	     			 uuid
		  ,  	  	     			 act_de_model_id
		  ,  	  	     			 form_design
		  ,  	  	     			 app_page_path
		  ,  	  	     			 flow_design
		  ,  	  	     			 create_time
		  ,  	  	     			 creator
		  ,  	  	     			 creator_id
		  ,  	  	     			 creator_org_id
		  ,  	  	     			 modifier_id
		  ,  	  	     			 modifier
		  ,  	  	     			 modify_time
		  ,  	  	     			 act_de_model_key
		  ,  	  	     			 form_uuid
		  ,  	  	     			 form_table_name,procdef_id,form_model,act_de_model_name,model_type,permission_type,permission_value
		    	  </sql>
    <update id="updateFormDesign">
        update act_my_model set form_design=#{formDesign},form_model=#{formModel} where uuid =#{uuid}
    </update>

    <update id="updateFormDesignByActDeModelKey">
            update act_my_model set form_design=#{formDesign},form_model=#{formModel} where act_de_model_key =#{actDeModelKey}
    </update>
    <update id="updateFormFieldJson">
         update form_field_json set field_json=#{formJson},form_model=#{formModel} where `table_name` =#{formTableName}
    </update>

    <update id="updateProcdefIdByModelKeyAndProcdef">
        update act_my_model set procdef_id=#{procdefId}  where act_de_model_key = #{key} and procdef_id =#{id}
    </update>

    <update id="updateProcdefIdIsNull">
          update act_my_model set procdef_id='' where act_de_model_key =#{key} and procdef_id = #{id}
    </update>

    <update id="updateActReProcdefNameByKey">
        update act_re_procdef set NAME_=#{actDeModelName} where major_version =#{majorVersion} and KEY_ = #{modelKey}
    </update>

    <select id="getPageSet" resultType="com.sbtr.workflow.model.ActMyModel">
        select
        <include refid="Base_Column_List"></include>
        from act_my_model
        <where>
            <if test="filterSort != null">
                ${filterSort}
            </if>
        </where>
    </select>
    <select id="getListByModelKeyAndProcdefIdIsNull" resultType="com.sbtr.workflow.model.ActMyModel">
        select
        <include refid="Base_Column_List"></include>
        from act_my_model where act_de_model_key = #{key} and (coalesce(procdef_id ,N'') = '')
    </select>

    <select id="getListByActDeModelKeyAndProcdefId" resultType="com.sbtr.workflow.model.ActMyModel">
        select
        <include refid="Base_Column_List"></include>
        from act_my_model where act_de_model_key = #{key} and procdef_id = #{procdefIds}
    </select>

    <select id="selectCountInteger" resultType="java.lang.Integer">
        select count(1) from  act_my_model where  act_de_model_key = #{key}
    </select>


    <delete id="executeDeleteBatch">
        delete from act_my_model where uuid in
        <foreach item="uuid" collection="array" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </delete>

    <delete id="deleteByModelKeyAnProcdefId">
         delete from act_my_model where act_de_model_key = #{key} and procdef_id = #{id}
    </delete>

    <select id="getDesignJson" resultType="java.lang.String">
        select
            design_json
        from form_design where basic_configure_uuid = #{formUuid}
    </select>
    <select id="getMajorModelByKey" resultType="com.sbtr.workflow.model.ActMyModel">
		select <include refid="Base_Column_List"></include>
		 from  act_my_model m
		 inner join act_re_procdef p on p.ID_=m.procdef_id
		 where p.major_version='是' and m.ACT_DE_MODEL_KEY=#{modelKey}
	</select>
</mapper>
