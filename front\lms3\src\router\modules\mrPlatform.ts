// import { $t } from "@/plugins/i18n";

export default {
  path: "/mrPlatform",
  redirect: "/mrPlatform/course",
  meta: {
    icon: "mdi:video-3d-variant",
    // showLink: false,
    title: "混合现实课程平台",
    rank: 2
  },
  children: [
    {
      path: "/mrPlatform/courseware",
      name: "courseware",
      component: () => import("@/views/mrPlatform/courseware/index.vue"),
      meta: {
        // title: $t("menus.pureFourZeroOne"),
        title: "课件管理"
      }
    },
    {
      path: "/mrPlatform/course",
      name: "course",
      component: () => import("@/views/mrPlatform/course/index.vue"),
      meta: {
        // title: $t("menus.pureFourZeroOne"),
        title: "课程管理"
      }
    },
    // {
    //   path: "/mrPlatform/presentation",
    //   name: "presentation",
    //   component: () => import("@/views/mrPlatform/presentation/index.vue"),
    //   meta: {
    //     // title: $t("menus.pureFourZeroOne"),
    //     title: "演示文稿"
    //   }
    // },
    // {
    //   path: "/mrPlatform/presentation",
    //   name: "presentation",
    //   component: () => import("@/views/mrPlatform/presentation/index.vue"),
    //   meta: {
    //     // title: $t("menus.pureFourZeroOne"),
    //     title: "我的课程"
    //   }
    // },
    // {
    //   path: "/mrPlatform/presentation",
    //   name: "presentation",
    //   component: () => import("@/views/mrPlatform/presentation/index.vue"),
    //   meta: {
    //     // title: $t("menus.pureFourZeroOne"),
    //     title: "学习记录"
    //   }
    // },
    // {
    //   path: "/mrPlatform/presentation",
    //   name: "presentation",
    //   component: () => import("@/views/mrPlatform/presentation/index.vue"),
    //   meta: {
    //     // title: $t("menus.pureFourZeroOne"),
    //     title: "我的考试"
    //   }
    // },
    // {
    //   path: "/mrPlatform/presentation",
    //   name: "presentation",
    //   component: () => import("@/views/mrPlatform/presentation/index.vue"),
    //   meta: {
    //     // title: $t("menus.pureFourZeroOne"),
    //     title: "考试记录"
    //   }
    // }
  ]
} satisfies RouteConfigsTable;
