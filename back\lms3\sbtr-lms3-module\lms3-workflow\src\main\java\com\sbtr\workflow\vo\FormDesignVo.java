package com.sbtr.workflow.vo;


import java.util.List;

/**
 * 前端传过来的表单设计json model
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:34
 */
public class FormDesignVo {


    private List<ListDTO> list;

    private ConfigDTO config;

    public ConfigDTO getConfig() {
        return config;
    }

    public void setConfig(ConfigDTO config) {
        this.config = config;
    }

    public List<ListDTO> getList() {
        return list;
    }

    public void setList(List<ListDTO> list) {
        this.list = list;
    }

    public static class ListDTO {
        private String type;
        private String label;
        private List<ListDTO3> list;
        private OptionsDTO options;
        private String model;
        private String oldModel;
        private String tableName;
        private String tableField;
        private String key;
        private String prefix;
        private String suffix;
        private List<RulesDTO> rules;

        public List<RulesDTO> getRules() {
            return rules;
        }

        public void setRules(List<RulesDTO> rules) {
            this.rules = rules;
        }

        private List<ColumnsDTO> columns;

        //表格组件
        private List<TrsDTO> trs;


        public List<TrsDTO> getTrs() {
            return trs;
        }

        public void setTrs(List<TrsDTO> trs) {
            this.trs = trs;
        }

        public List<ListDTO3> getList() {
            return list;
        }

        public void setList(List<ListDTO3> list) {
            this.list = list;
        }

        public List<ColumnsDTO> getColumns() {
            return columns;
        }

        public void setColumns(List<ColumnsDTO> columns) {
            this.columns = columns;
        }

        public String getTableName() {
            return tableName;
        }

        public void setTableName(String tableName) {
            this.tableName = tableName;
        }

        public String getTableField() {
            return tableField;
        }

        public void setTableField(String tableField) {
            this.tableField = tableField;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public OptionsDTO getOptions() {
            return options;
        }

        public void setOptions(OptionsDTO options) {
            this.options = options;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public String getOldModel() {
            return oldModel;
        }

        public void setOldModel(String oldModel) {
            this.oldModel = oldModel;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getPrefix() {
            return prefix;
        }

        public void setPrefix(String prefix) {
            this.prefix = prefix;
        }

        public String getSuffix() {
            return suffix;
        }

        public void setSuffix(String suffix) {
            this.suffix = suffix;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public static class OptionsDTO {
            private String type;
            private String width;
            private Boolean range;
            private String defaultValue;
            private String placeholder;
            private Boolean clearable;
            private Boolean multiple;
            private Integer maxRows;

            private Integer minRows;
            private Object maxLength;
            private Boolean hidden;
            private Boolean disabled;
            private Integer colCountKey;
            private String helpTips;
            private List<EventDataDTO> eventData;
            private Integer labelWidth;
            private String executeCode;
            private String eventValue;
            private String url;
            private String dynamicKey;
            private List<Options> options;
            private String valueFeild;
            private String textFeild;
            private String method;
            private String dataType;
            private String ruleCode;
            private String saveCode;
            private String showCode;
            private Parameter parameter;
            private String dictCode;

            public Boolean getMultiple() {
                return multiple;
            }

            public void setMultiple(Boolean multiple) {
                this.multiple = multiple;
            }

            public Integer getMaxRows() {
                return maxRows;
            }

            public void setMaxRows(Integer maxRows) {
                this.maxRows = maxRows;
            }

            public Integer getMinRows() {
                return minRows;
            }

            public void setMinRows(Integer minRows) {
                this.minRows = minRows;
            }

            public String getDictCode() {
                return dictCode;
            }

            public void setDictCode(String dictCode) {
                this.dictCode = dictCode;
            }

            public Boolean getRange() {
                return range;
            }

            public void setRange(Boolean range) {
                this.range = range;
            }

            public List<EventDataDTO> getEventData() {
                return eventData;
            }

            public void setEventData(List<EventDataDTO> eventData) {
                this.eventData = eventData;
            }

            public String getRuleCode() {
                return ruleCode;
            }

            public void setRuleCode(String ruleCode) {
                this.ruleCode = ruleCode;
            }

            public String getSaveCode() {
                return saveCode;
            }

            public void setSaveCode(String saveCode) {
                this.saveCode = saveCode;
            }

            public String getShowCode() {
                return showCode;
            }

            public void setShowCode(String showCode) {
                this.showCode = showCode;
            }

            public Parameter getParameter() {
                return parameter;
            }

            public void setParameter(Parameter parameter) {
                this.parameter = parameter;
            }

            public static class Parameter{
                private String uuid;

                public String getUuid() {
                    return uuid;
                }

                public void setUuid(String uuid) {
                    this.uuid = uuid;
                }
            }

            public static class Options {
                private String value;
                private String text;
                private String dicItemCode;
                private String dicItemName;

                public String getDicItemCode() {
                    return dicItemCode;
                }

                public void setDicItemCode(String dicItemCode) {
                    this.dicItemCode = dicItemCode;
                }

                public String getDicItemName() {
                    return dicItemName;
                }

                public void setDicItemName(String dicItemName) {
                    this.dicItemName = dicItemName;
                }

                public String getValue() {
                    return value;
                }

                public void setValue(String value) {
                    this.value = value;
                }

                public String getText() {
                    return text;
                }

                public void setText(String text) {
                    this.text = text;
                }
            }

            public List<Options> getOptions() {
                return options;
            }

            public void setOptions(List<Options> options) {
                this.options = options;
            }

            public String getUrl() {
                return url;
            }

            public String getDynamicKey() {
                return dynamicKey;
            }

            public void setDynamicKey(String dynamicKey) {
                this.dynamicKey = dynamicKey;
            }

            public void setUrl(String url) {
                this.url = url;
            }

            public String getValueFeild() {
                return valueFeild;
            }

            public void setValueFeild(String valueFeild) {
                this.valueFeild = valueFeild;
            }

            public String getTextFeild() {
                return textFeild;
            }

            public void setTextFeild(String textFeild) {
                this.textFeild = textFeild;
            }

            public String getMethod() {
                return method;
            }

            public void setMethod(String method) {
                this.method = method;
            }

            public String getDataType() {
                return dataType;
            }

            public void setDataType(String dataType) {
                this.dataType = dataType;
            }

            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public String getWidth() {
                return width;
            }

            public void setWidth(String width) {
                this.width = width;
            }

            public String getDefaultValue() {
                return defaultValue;
            }

            public void setDefaultValue(String defaultValue) {
                this.defaultValue = defaultValue;
            }

            public String getPlaceholder() {
                return placeholder;
            }

            public void setPlaceholder(String placeholder) {
                this.placeholder = placeholder;
            }

            public Boolean getClearable() {
                return clearable;
            }

            public void setClearable(Boolean clearable) {
                this.clearable = clearable;
            }

            public Object getMaxLength() {
                return maxLength;
            }

            public void setMaxLength(Object maxLength) {
                this.maxLength = maxLength;
            }

            public Boolean getHidden() {
                return hidden;
            }

            public void setHidden(Boolean hidden) {
                this.hidden = hidden;
            }

            public Boolean getDisabled() {
                return disabled;
            }

            public void setDisabled(Boolean disabled) {
                this.disabled = disabled;
            }

            public Integer getColCountKey() {
                return colCountKey;
            }

            public void setColCountKey(Integer colCountKey) {
                this.colCountKey = colCountKey;
            }

            public String getHelpTips() {
                return helpTips;
            }

            public void setHelpTips(String helpTips) {
                this.helpTips = helpTips;
            }

            public Integer getLabelWidth() {
                return labelWidth;
            }

            public void setLabelWidth(Integer labelWidth) {
                this.labelWidth = labelWidth;
            }

            public String getExecuteCode() {
                return executeCode;
            }

            public void setExecuteCode(String executeCode) {
                this.executeCode = executeCode;
            }

            public String getEventValue() {
                return eventValue;
            }

            public void setEventValue(String eventValue) {
                this.eventValue = eventValue;
            }
        }


        public class ListDTO3 {
            private String type;
            private String label;
            private String icon;
            private OptionsDTO options;
            private String model;
            private String oldModel;
            private String tableName;
            private String tableField;
            private Integer tableIndex;
            private String key;
            private String prefix;
            private String suffix;
            private List<RulesDTO> rules;


            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public String getLabel() {
                return label;
            }

            public void setLabel(String label) {
                this.label = label;
            }

            public String getIcon() {
                return icon;
            }

            public void setIcon(String icon) {
                this.icon = icon;
            }

            public OptionsDTO getOptions() {
                return options;
            }

            public void setOptions(OptionsDTO options) {
                this.options = options;
            }

            public String getModel() {
                return model;
            }

            public void setModel(String model) {
                this.model = model;
            }

            public String getOldModel() {
                return oldModel;
            }

            public void setOldModel(String oldModel) {
                this.oldModel = oldModel;
            }

            public String getTableName() {
                return tableName;
            }

            public void setTableName(String tableName) {
                this.tableName = tableName;
            }

            public String getTableField() {
                return tableField;
            }

            public void setTableField(String tableField) {
                this.tableField = tableField;
            }

            public Integer getTableIndex() {
                return tableIndex;
            }

            public void setTableIndex(Integer tableIndex) {
                this.tableIndex = tableIndex;
            }

            public String getKey() {
                return key;
            }

            public void setKey(String key) {
                this.key = key;
            }

            public String getPrefix() {
                return prefix;
            }

            public void setPrefix(String prefix) {
                this.prefix = prefix;
            }

            public String getSuffix() {
                return suffix;
            }

            public void setSuffix(String suffix) {
                this.suffix = suffix;
            }

            public List<RulesDTO> getRules() {
                return rules;
            }

            public void setRules(List<RulesDTO> rules) {
                this.rules = rules;
            }

            public class OptionsDTO {
                private String width;
                private String defaultValue;
                private String placeholder;
                private Boolean clearable;
                private Object maxLength;
                private Boolean hidden;
                private Boolean disabled;
                private Integer colCountKey;
                private String helpTips;
                private Integer labelWidth;
                private List<EventDataDTO> eventData;

                public String getWidth() {
                    return width;
                }

                public void setWidth(String width) {
                    this.width = width;
                }

                public String getDefaultValue() {
                    return defaultValue;
                }

                public void setDefaultValue(String defaultValue) {
                    this.defaultValue = defaultValue;
                }

                public String getPlaceholder() {
                    return placeholder;
                }

                public void setPlaceholder(String placeholder) {
                    this.placeholder = placeholder;
                }

                public Boolean getClearable() {
                    return clearable;
                }

                public void setClearable(Boolean clearable) {
                    this.clearable = clearable;
                }

                public Object getMaxLength() {
                    return maxLength;
                }

                public void setMaxLength(Object maxLength) {
                    this.maxLength = maxLength;
                }

                public Boolean getHidden() {
                    return hidden;
                }

                public void setHidden(Boolean hidden) {
                    this.hidden = hidden;
                }

                public Boolean getDisabled() {
                    return disabled;
                }

                public void setDisabled(Boolean disabled) {
                    this.disabled = disabled;
                }

                public Integer getColCountKey() {
                    return colCountKey;
                }

                public void setColCountKey(Integer colCountKey) {
                    this.colCountKey = colCountKey;
                }

                public String getHelpTips() {
                    return helpTips;
                }

                public void setHelpTips(String helpTips) {
                    this.helpTips = helpTips;
                }

                public Integer getLabelWidth() {
                    return labelWidth;
                }

                public void setLabelWidth(Integer labelWidth) {
                    this.labelWidth = labelWidth;
                }

                public List<EventDataDTO> getEventData() {
                    return eventData;
                }

                public void setEventData(List<EventDataDTO> eventData) {
                    this.eventData = eventData;
                }

                public class EventDataDTO {
                    private String text;
                    private String value;
                    private String code;

                    public String getText() {
                        return text;
                    }

                    public void setText(String text) {
                        this.text = text;
                    }

                    public String getValue() {
                        return value;
                    }

                    public void setValue(String value) {
                        this.value = value;
                    }

                    public String getCode() {
                        return code;
                    }

                    public void setCode(String code) {
                        this.code = code;
                    }
                }
            }

            public class RulesDTO {
                private Boolean required;
                private String message;

                public Boolean getRequired() {
                    return required;
                }

                public void setRequired(Boolean required) {
                    this.required = required;
                }

                public String getMessage() {
                    return message;
                }

                public void setMessage(String message) {
                    this.message = message;
                }
            }
        }

        public class ColumnsDTO {

            private Integer span;
            private List<ListDTO2> list;

            //标签页布局
            private String value;
            private String label;

            public Integer getSpan() {
                return span;
            }

            public void setSpan(Integer span) {
                this.span = span;
            }

            public List<ListDTO2> getList() {
                return list;
            }

            public void setList(List<ListDTO2> list) {
                this.list = list;
            }

            public String getValue() {
                return value;
            }

            public void setValue(String value) {
                this.value = value;
            }

            public String getLabel() {
                return label;
            }

            public void setLabel(String label) {
                this.label = label;
            }

            public class ListDTO2 {
                private String type;
                private String label;
                private String icon;
                private OptionsDTO options;
                private String model;
                private String oldModel;
                private String tableName;
                private String tableField;
                private Integer tableIndex;
                private Boolean subTable;
                private String key;
                private String prefix;
                private String suffix;
                private List<RulesDTO> rules;


                public String getType() {
                    return type;
                }

                public void setType(String type) {
                    this.type = type;
                }

                public String getLabel() {
                    return label;
                }

                public void setLabel(String label) {
                    this.label = label;
                }

                public String getIcon() {
                    return icon;
                }

                public void setIcon(String icon) {
                    this.icon = icon;
                }

                public OptionsDTO getOptions() {
                    return options;
                }

                public void setOptions(OptionsDTO options) {
                    this.options = options;
                }

                public String getModel() {
                    return model;
                }

                public void setModel(String model) {
                    this.model = model;
                }

                public String getOldModel() {
                    return oldModel;
                }

                public void setOldModel(String oldModel) {
                    this.oldModel = oldModel;
                }

                public String getTableName() {
                    return tableName;
                }

                public void setTableName(String tableName) {
                    this.tableName = tableName;
                }

                public String getTableField() {
                    return tableField;
                }

                public void setTableField(String tableField) {
                    this.tableField = tableField;
                }

                public Integer getTableIndex() {
                    return tableIndex;
                }

                public void setTableIndex(Integer tableIndex) {
                    this.tableIndex = tableIndex;
                }

                public Boolean getSubTable() {
                    return subTable;
                }

                public void setSubTable(Boolean subTable) {
                    this.subTable = subTable;
                }

                public String getKey() {
                    return key;
                }

                public void setKey(String key) {
                    this.key = key;
                }

                public String getPrefix() {
                    return prefix;
                }

                public void setPrefix(String prefix) {
                    this.prefix = prefix;
                }

                public String getSuffix() {
                    return suffix;
                }

                public void setSuffix(String suffix) {
                    this.suffix = suffix;
                }

                public List<RulesDTO> getRules() {
                    return rules;
                }

                public void setRules(List<RulesDTO> rules) {
                    this.rules = rules;
                }

                public class OptionsDTO {
                    private String width;
                    private String defaultValue;
                    private String placeholder;
                    private Boolean clearable;
                    private Object maxLength;
                    private Boolean hidden;
                    private Boolean disabled;
                    private Integer colCountKey;
                    private String helpTips;
                    private Integer labelWidth;
                    private String executeCode;
                    private String eventValue;
                    private List<EventDataDTO> eventData;

                    public String getWidth() {
                        return width;
                    }

                    public void setWidth(String width) {
                        this.width = width;
                    }

                    public String getDefaultValue() {
                        return defaultValue;
                    }

                    public void setDefaultValue(String defaultValue) {
                        this.defaultValue = defaultValue;
                    }

                    public String getPlaceholder() {
                        return placeholder;
                    }

                    public void setPlaceholder(String placeholder) {
                        this.placeholder = placeholder;
                    }

                    public Boolean getClearable() {
                        return clearable;
                    }

                    public void setClearable(Boolean clearable) {
                        this.clearable = clearable;
                    }

                    public Object getMaxLength() {
                        return maxLength;
                    }

                    public void setMaxLength(Object maxLength) {
                        this.maxLength = maxLength;
                    }

                    public Boolean getHidden() {
                        return hidden;
                    }

                    public void setHidden(Boolean hidden) {
                        this.hidden = hidden;
                    }

                    public Boolean getDisabled() {
                        return disabled;
                    }

                    public void setDisabled(Boolean disabled) {
                        this.disabled = disabled;
                    }

                    public Integer getColCountKey() {
                        return colCountKey;
                    }

                    public void setColCountKey(Integer colCountKey) {
                        this.colCountKey = colCountKey;
                    }

                    public String getHelpTips() {
                        return helpTips;
                    }

                    public void setHelpTips(String helpTips) {
                        this.helpTips = helpTips;
                    }

                    public Integer getLabelWidth() {
                        return labelWidth;
                    }

                    public void setLabelWidth(Integer labelWidth) {
                        this.labelWidth = labelWidth;
                    }

                    public String getExecuteCode() {
                        return executeCode;
                    }

                    public void setExecuteCode(String executeCode) {
                        this.executeCode = executeCode;
                    }

                    public String getEventValue() {
                        return eventValue;
                    }

                    public void setEventValue(String eventValue) {
                        this.eventValue = eventValue;
                    }

                    public List<EventDataDTO> getEventData() {
                        return eventData;
                    }

                    public void setEventData(List<EventDataDTO> eventData) {
                        this.eventData = eventData;
                    }

                    public class EventDataDTO {
                        private String text;
                        private String value;
                        private String code;

                        public String getCode() {
                            return code;
                        }

                        public void setCode(String code) {
                            this.code = code;
                        }

                        public String getText() {
                            return text;
                        }

                        public void setText(String text) {
                            this.text = text;
                        }

                        public String getValue() {
                            return value;
                        }

                        public void setValue(String value) {
                            this.value = value;
                        }
                    }
                }

                public class RulesDTO {
                    private Boolean required;
                    private String message;

                    public Boolean getRequired() {
                        return required;
                    }

                    public void setRequired(Boolean required) {
                        this.required = required;
                    }

                    public String getMessage() {
                        return message;
                    }

                    public void setMessage(String message) {
                        this.message = message;
                    }
                }
            }

        }

        public  class TrsDTO {
            private List<TdsDTO> tds;

            public List<TdsDTO> getTds() {
                return tds;
            }

            public void setTds(List<TdsDTO> tds) {
                this.tds = tds;
            }

            public  class TdsDTO {
                private Integer colspan;
                private Integer rowspan;
                private List<ListDTO4> list;


                public Integer getColspan() {
                    return colspan;
                }

                public void setColspan(Integer colspan) {
                    this.colspan = colspan;
                }

                public Integer getRowspan() {
                    return rowspan;
                }

                public void setRowspan(Integer rowspan) {
                    this.rowspan = rowspan;
                }

                public List<ListDTO4> getList() {
                    return list;
                }

                public void setList(List<ListDTO4> list) {
                    this.list = list;
                }

                public  class ListDTO4 {
                    private String type;
                    private String label;
                    private String icon;
                    private OptionsDTO options;
                    private String model;
                    private String oldModel;
                    private String tableName;
                    private String tableField;
                    private Integer tableIndex;
                    private String key;
                    private String prefix;
                    private String suffix;
                    private List<RulesDTO> rules;

                    public String getType() {
                        return type;
                    }

                    public void setType(String type) {
                        this.type = type;
                    }

                    public String getLabel() {
                        return label;
                    }

                    public void setLabel(String label) {
                        this.label = label;
                    }

                    public String getIcon() {
                        return icon;
                    }

                    public void setIcon(String icon) {
                        this.icon = icon;
                    }

                    public OptionsDTO getOptions() {
                        return options;
                    }

                    public void setOptions(OptionsDTO options) {
                        this.options = options;
                    }

                    public String getModel() {
                        return model;
                    }

                    public void setModel(String model) {
                        this.model = model;
                    }

                    public String getOldModel() {
                        return oldModel;
                    }

                    public void setOldModel(String oldModel) {
                        this.oldModel = oldModel;
                    }

                    public String getTableName() {
                        return tableName;
                    }

                    public void setTableName(String tableName) {
                        this.tableName = tableName;
                    }

                    public String getTableField() {
                        return tableField;
                    }

                    public void setTableField(String tableField) {
                        this.tableField = tableField;
                    }

                    public Integer getTableIndex() {
                        return tableIndex;
                    }

                    public void setTableIndex(Integer tableIndex) {
                        this.tableIndex = tableIndex;
                    }

                    public String getKey() {
                        return key;
                    }

                    public void setKey(String key) {
                        this.key = key;
                    }

                    public String getPrefix() {
                        return prefix;
                    }

                    public void setPrefix(String prefix) {
                        this.prefix = prefix;
                    }

                    public String getSuffix() {
                        return suffix;
                    }

                    public void setSuffix(String suffix) {
                        this.suffix = suffix;
                    }

                    public List<RulesDTO> getRules() {
                        return rules;
                    }

                    public void setRules(List<RulesDTO> rules) {
                        this.rules = rules;
                    }

                    public  class OptionsDTO {
                        private String width;
                        private String defaultValue;
                        private String placeholder;
                        private Boolean clearable;
                        private Object maxLength;
                        private Boolean hidden;
                        private Boolean disabled;
                        private Integer colCountKey;
                        private String helpTips;
                        private Integer labelWidth;
                        private List<EventDataDTO> eventData;

                        public  class EventDataDTO {
                            private String text;
                            private String value;
                            private String code;

                            public String getText() {
                                return text;
                            }

                            public void setText(String text) {
                                this.text = text;
                            }

                            public String getValue() {
                                return value;
                            }

                            public void setValue(String value) {
                                this.value = value;
                            }

                            public String getCode() {
                                return code;
                            }

                            public void setCode(String code) {
                                this.code = code;
                            }
                        }
                    }

                    public  class RulesDTO {
                        private Boolean required;
                        private String message;

                        public Boolean getRequired() {
                            return required;
                        }

                        public void setRequired(Boolean required) {
                            this.required = required;
                        }

                        public String getMessage() {
                            return message;
                        }

                        public void setMessage(String message) {
                            this.message = message;
                        }
                    }
                }
            }
        }
    }
}
