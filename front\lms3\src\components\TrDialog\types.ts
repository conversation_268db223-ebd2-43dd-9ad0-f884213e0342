// 按钮配置接口
export interface ButtonConfig {
  text: string;
  type?: "primary" | "default" | "dashed" | "link" | "text";
  loading?: boolean;
  danger?: boolean;
  onClick: (e: MouseEvent) => void;
  props?: Record<string, any>;
}

// 全屏样式接口
export interface FullscreenStyle {
  position: "fixed";
  top: number;
  left: number;
  width: string;
  height: string;
  margin: number;
  padding: number;
  maxWidth: string;
  maxHeight: string;
  zIndex: number;
}

// TrDialog Props 接口
export interface TrDialogProps {
  // a-modal 原生属性
  open?: boolean;
  title?: string | Record<string, any>;
  width?: string | number;
  confirmLoading?: boolean;
  okText?: string;
  cancelText?: string;
  okType?: string;
  okButtonProps?: Record<string, any>;
  cancelButtonProps?: Record<string, any>;
  closable?: boolean;
  maskClosable?: boolean;
  destroyOnClose?: boolean;
  centered?: boolean;
  zIndex?: number;
  bodyStyle?: Record<string, any>;
  maskStyle?: Record<string, any>;
  wrapClassName?: string;
  keyboard?: boolean;
  footer?: string | Record<string, any>;

  // 扩展属性
  draggable?: boolean;
  fullscreen?: boolean;
  showFooter?: boolean;
  showFullscreenButton?: boolean;
  footerButtons?: ButtonConfig[];
}

// TrDialog Events 接口
export interface TrDialogEmits {
  "update:open": [value: boolean];
  ok: [e: MouseEvent];
  cancel: [e: MouseEvent];
  "fullscreen-change": [value: boolean];
  afterClose: [];
}
