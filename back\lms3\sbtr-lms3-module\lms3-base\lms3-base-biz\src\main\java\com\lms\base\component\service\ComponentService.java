package com.lms.base.component.service;

import com.lms.base.feign.model.Component;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.feign.dto.TreeNode;
import com.lms.common.service.BaseService;

import java.util.List;

public interface ComponentService extends BaseService<Component> {

    List<TreeNode> getComponentTree2(String pid, boolean includeCourse);

    List<Component> getAllComponentsByPid(String pid);

    List<TreeNode> getCourseStructureTree(PageInfo pageInfo);

    List<Component> getAllChildComponentsByPidFullPath(String pidFullPath);

    boolean exsitUseCourseware(String id);

    boolean exsitUseSubject(String id);

    boolean exsitSubNodes(String id);

    boolean hasChildren(String id);

    boolean existName(String name, String id, String parentid);

    void adaptComponent(List content);

    Component getByName(String s);
}
