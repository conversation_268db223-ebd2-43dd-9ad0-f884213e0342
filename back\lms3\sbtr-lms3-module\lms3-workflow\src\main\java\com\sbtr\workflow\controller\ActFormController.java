package com.sbtr.workflow.controller;

import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.utils.BaseUtils;
import com.sbtr.workflow.model.ActForm;
import com.sbtr.workflow.service.ActFormService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 常用外置表单数据主表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:33:52
 */
@Api(tags = {"常用外置表单数据主表"})
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/actForm")
public class ActFormController extends WorkflowBaseController {

    @Autowired
    private ActFormService actFormService;

    @ApiOperation(value = "获得ActForm分页集数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String"),
    })
    @ResponseBody
    @RequestMapping(value = "/getPageSet", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam) {
        String filterSort = "";
        filterSort = BaseUtils.filterSort(request, filterSort );
        PageSet<ActForm> pageSet = actFormService.getPageSet(pageParam, filterSort);
        return pageSet;
    }

    @ApiOperation(value = "获得ActForm模块详细数据")
    @ApiImplicitParam(name = "uuid", value = "获得ActForm模块详细数据", required = true, dataType = "String")
    @ResponseBody
    @RequestMapping(value = "/getDetailByUuid")
    public Object getDetailByUuid(String uuid) {
        ActForm actForm = actFormService.selectByPrimaryKey(uuid);
        return actForm;
    }


    @Log(title = "ActForm-保存模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @ApiOperation(value = "保存ActForm模块数据")
    @ApiImplicitParam(name = "actForm", value = "保存ActForm模块数据", required = true, dataType = "ActForm")
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Object save(@ModelAttribute ActForm actForm) {
        setLogMessage(actForm.getName(),"");
        int result = actFormService.insertSelective(getSaveData(actForm));
        return result > 0 ? success("保存成功！") : failure("保存失败！");
    }


    @Log(title = "ActForm-更新模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.UPDATE, method = "getLogMessage")
    @ApiOperation(value = "更新ActForm模块数据")
    @ApiImplicitParam(name = "actForm", value = "更新ActForm模块数据", required = true, dataType = "ActForm")
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Object update(@ModelAttribute ActForm actForm) {
        setLogMessage(actForm.getName(),"");
        int result = actFormService.updateByPrimaryKeySelective(getUpdateData(actForm));
        return result > 0 ? success("更新成功！") : failure("更新失败！");
    }

    @Log(title = "ActForm-删除模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ApiOperation(value = "删除ActForm模块数据")
    @ApiImplicitParam(name = "actForm", value = "删除ActForm模块数据", required = true, dataType = "ActForm")
    @ResponseBody
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.POST)
    public Object deleteBatch(@RequestParam String[] uuid) {
        ActForm actForm = actFormService.selectByPrimaryKey(uuid[0]);
        if(actForm!=null)
            setLogMessage(actForm.getName(),"");
        int result = actFormService.executeDeleteBatch(uuid);
        return result > 0 ? success("删除成功！") : failure("删除失败！");
    }

}
