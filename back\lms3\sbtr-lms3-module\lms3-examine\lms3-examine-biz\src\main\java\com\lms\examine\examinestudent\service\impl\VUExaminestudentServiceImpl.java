/**
 * FileName:	ClientVUExaminestudent.java
 * Author:		ji<PERSON><PERSON><PERSON>
 * Time:		2013-3-4上午11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.examine.examinestudent.service.impl;

import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.DateHelper;
import com.lms.examine.examinestudent.mapper.VUExaminestudentMapper;
import com.lms.examine.examinestudent.service.VUExaminestudentService;
import com.lms.examine.feign.model.VUExaminestudent;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 保存项目的相关信息
 */
@Service("VUExaminestudentService")
public class VUExaminestudentServiceImpl extends BaseServiceImpl<VUExaminestudentMapper, VUExaminestudent> implements VUExaminestudentService {


	@Resource
	private VUExaminestudentMapper vuExaminestudentMapper;

	public List<Object[]> getDayExamine(String personId) {
		String day = DateHelper.getCurrentDate();
		return vuExaminestudentMapper.getDayExamine(personId,day);
	}
}
