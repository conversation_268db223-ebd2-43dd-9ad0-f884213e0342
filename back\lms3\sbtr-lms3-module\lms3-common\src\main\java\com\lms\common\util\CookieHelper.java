/**
 * SBTR LTD.
 */
package com.lms.common.util;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Cookie常用工具方法类
 */
public final class CookieHelper {

    /**
     * 获取Cookie中指定对象的属性值
     * @param req    用户请求
     * @param key    Cookie中的对象名称
     * @return 指定对象的属性值, 如果没有指定对象, 返回null
     */
    public static String getCookie(HttpServletRequest req, String key) {
        Cookie[] cookies = req.getCookies();
        if (cookies != null) {
            for (int i = 0; i < cookies.length; i++) {
                if (cookies[i].getName().equals(key)) {
                    return cookies[i].getValue();
                }
            }
        }
        return null;
    }


    /**
     * 设置Cookie中对象值
     * @param res        用户请求
     * @param key        对象名称
     * @param value    对象值
     * @param age        Cookie存放时间
     * @param domain    Cookie域名
     */
    public static void setCookie(HttpServletResponse res, String key,
                                 String value, int age, String domain) {

        Cookie newCookie = new Cookie(key, value);
        newCookie.setMaxAge(age);
        newCookie.setDomain(domain);
        newCookie.setPath("/");

        res.addCookie(newCookie);

    }

    /**
     * 设置Cookie中对象值
     * @param res        用户请求
     * @param key        对象名称
     * @param value    对象值
     * @param age        Cookie存放时间
     */
    public static void setCookie(HttpServletResponse res, String key,
                                 String value, int age) {

        Cookie newCookie = new Cookie(key, value);
        newCookie.setMaxAge(age);
        newCookie.setPath("/");
        res.addCookie(newCookie);

    }


    /**
     * 设置Cookie中对象值
     * @param res        用户请求
     * @param key        对象名称
     * @param value    对象值
     */
    public static void setCookie(HttpServletResponse res, String key,
                                 String value) {
        setCookie(res, key, value, -1);
    }
}
