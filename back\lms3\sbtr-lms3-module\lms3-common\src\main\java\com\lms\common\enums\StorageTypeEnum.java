package com.lms.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件存储类型枚举
 */
@Getter
@AllArgsConstructor
public enum StorageTypeEnum {
    
    /**
     * 阿里云OSS存储
     */
    OSS("oss", "阿里云OSS存储", "com.lms.system.storage.strategy.OSSStorageStrategy"),
    
    /**
     * MinIO对象存储
     */
    MINIO("minio", "MinIO对象存储", "com.lms.system.storage.strategy.MinIOStorageStrategy"),
    
    /**
     * 本地文件存储
     */
    LOCAL("local", "本地文件存储", "com.lms.system.storage.strategy.LocalStorageStrategy");
    
    /**
     * 存储类型代码
     */
    private final String code;
    
    /**
     * 存储类型描述
     */
    private final String description;
    
    /**
     * 策略实现类全限定名
     */
    private final String strategyClass;
    
    /**
     * 根据代码获取存储类型
     * 
     * @param code 存储类型代码
     * @return 存储类型枚举
     */
    public static StorageTypeEnum getByCode(String code) {
        for (StorageTypeEnum type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("不支持的存储类型: " + code);
    }
    
    /**
     * 检查是否为有效的存储类型
     * 
     * @param code 存储类型代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        try {
            getByCode(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
