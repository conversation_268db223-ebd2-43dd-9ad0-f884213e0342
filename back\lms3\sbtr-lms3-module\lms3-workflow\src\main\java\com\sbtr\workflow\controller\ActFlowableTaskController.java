package com.sbtr.workflow.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.lms.common.util.DateHelper;
import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.dto.CommonTaskDto;
import com.sbtr.workflow.dto.ProcessDefinitionDto;
import com.sbtr.workflow.dto.StartedDto;
import com.sbtr.workflow.service.ApiFlowableTaskService;
import com.sbtr.workflow.service.BusinessSystemDataService;
import com.sbtr.workflow.service.ImageService;
import com.sbtr.workflow.utils.OkHttpClientUtil;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.Result;
import com.sbtr.workflow.vo.AddTaskNewVo;
import com.sbtr.workflow.vo.FlowNodeVo;
import com.sbtr.workflow.vo.TaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.flowable.common.engine.impl.util.IoUtil;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * 流程任务
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
@Api(tags = {"流程任务"})
@RestController
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/apiFlowableTask")
public class ActFlowableTaskController extends WorkflowBaseController {

    @Autowired
    private ApiFlowableTaskService apiFlowableTaskService;
    @Resource
    public BusinessSystemDataService businessSystemDataService;
    @Autowired
    public HttpServletRequest request;
    @Autowired
    private TaskService taskService;

    @Autowired
    public ImageService imageService;

    @Resource
    private OkHttpClientUtil okHttpClientUtil;


    @ApiOperation(value = "待办任务分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "pageSize", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "formName", value = "流程标题", defaultValue = "modelName", required = true, dataType = "String"),
            @ApiImplicitParam(name = "modelName", value = "流程名称", defaultValue = "modelKey", required = true, dataType = "String"),
            @ApiImplicitParam(name = "processModelType", value = "流程模型类型  1 自定义流程界面  2 托拉拽界面", defaultValue = "1", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/getToDoTasks", method = RequestMethod.POST)
    public Object getToDoTasks(PageParam pageParam, String formName, String modelName, String processModelType,String startTime) {
        PageSet<TaskVo> pageSet = apiFlowableTaskService.getToDoTasks(pageParam, businessSystemDataService.getUserNameId(), formName, modelName, processModelType,startTime);
        return pageSet;
    }

    @ApiOperation(value = "代理任务分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "pageSize", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "formName", value = "流程标题", defaultValue = "modelName", required = true, dataType = "String"),
            @ApiImplicitParam(name = "modelName", value = "流程名称", defaultValue = "modelKey", required = true, dataType = "String"),
            @ApiImplicitParam(name = "processModelType", value = "流程模型类型  1 自定义流程界面  2 托拉拽界面", defaultValue = "1", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/getAgentTasks", method = RequestMethod.POST)
    public Object getAgentTasks(PageParam pageParam, String formName, String modelName, String processModelType,String startTime) {
        PageSet<TaskVo> pageSet = apiFlowableTaskService.getAgentTasks(pageParam, businessSystemDataService.getUserNameId(), formName, modelName, processModelType,startTime);
        return pageSet;
    }

    @ResponseBody
    @RequestMapping(value = "/getFinanceUuidByTankName", method = RequestMethod.POST)
    public List<String> getFinanceUuidByTankName(String formName, String modelName, String processModelType,String startTime) {
        List<String> list = apiFlowableTaskService.getFinanceUuidByTankName(businessSystemDataService.getUserNameId(), formName, modelName, processModelType,startTime);
        return list;
    }


    @ApiOperation(value = "历史任务分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "pageSize", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "name", value = "模型名称", defaultValue = "modelName", required = true, dataType = "String"),
            @ApiImplicitParam(name = "procDefName", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String"),
            @ApiImplicitParam(name = "processModelType", value = "流程模型类型  1 自定义流程界面  2 托拉拽界面", defaultValue = "1", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/getMyHistoryPageSet", method = RequestMethod.POST)
    public Object getMyHistoryPageSet(PageParam pageParam, String name, String procDefName, String processModelType, String startTime) {
        PageSet<CommonTaskDto> pageSet = apiFlowableTaskService.getMyHistoryPageSet(
                pageParam,
                businessSystemDataService.getUserNameId(),
                name,
                procDefName,
                processModelType,startTime);
        return pageSet;
    }

    @ApiOperation(value = "历史任务点击详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "模型名称", defaultValue = "modelName", required = true, dataType = "String"),
            @ApiImplicitParam(name = "modelKey", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/historyClickDetails", method = RequestMethod.POST)
    public Object historyClickDetails(String processInstanceId, String modelKey, String processDefinitionId, String businessKey, String nodeId) throws Exception {
        Object object = apiFlowableTaskService.historyClickDetails(
                processInstanceId,
                modelKey,
                processDefinitionId,
                businessKey,
                nodeId);
        return object;
    }

    @Log(title = "流程任务-查看",module = LogModule.WORKFLOW,businessType = BusinessType.APPROVE, method = "getLogMessage")
    @ResponseBody
    @RequestMapping(value = "/getHistoryDetails", method = RequestMethod.POST)
    public Object getHistoryDetails(String taskId) throws Exception {
        List<TaskVo> taskVos = apiFlowableTaskService.getActHiTaskVoById("","",taskId);
        Object object = null;
        if(taskVos.size()>0){
            TaskVo taskVo = taskVos.get(0);
            object = apiFlowableTaskService.historyClickDetails(
                    taskVo.getProcessInstanceId(),
                    taskVo.getModelKey(),
                    taskVo.getProcessDefinitionId(),
                    taskVo.getBusinessKey(),
                    taskVo.getNodeId());
            setLogMessage(taskVo.getTaskName(),"");
        }
        return object;
    }
    @ApiOperation(value = "历史任务点击删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "instanceId", value = "模型名称", defaultValue = "modelName", required = true, dataType = "String"),
    })
    @ResponseBody
    @RequestMapping(value = "/deleteHistoricProcessInstance", method = RequestMethod.POST)
    public Object deleteHistoricProcessInstance(String instanceId) {
        Object object = apiFlowableTaskService.deleteHistoricProcessInstance(instanceId);
        return object;
    }

    @Autowired
    protected HistoryService historyService;

    /**
     * 通过流程实例ID获取流程中已经执行的节点，按照执行先后顺序排序
     *
     * @param procInstId
     * @return
     */
    public List<HistoricActivityInstance> getHistoricActivityInstAsc(String procInstId) {
        return historyService.createHistoricActivityInstanceQuery().processInstanceId(procInstId)
                .orderByHistoricActivityInstanceStartTime().asc().list();
    }

    @ApiOperation(value = "在办任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "pageSize", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "formName", value = "模型名称", defaultValue = "modelName", required = true, dataType = "String"),
            @ApiImplicitParam(name = "modelName", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String"),
            @ApiImplicitParam(name = "processModelType", value = "流程模型类型  1 自定义流程界面  2 托拉拽界面", defaultValue = "1", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/getMyNoEndProcessPageSetData", method = RequestMethod.POST)
    public Object getMyNoEndProcessPageSetData(PageParam pageParam, String formName, String modelName, String processModelType,String startTime) {
        PageSet<TaskVo> pageSet = apiFlowableTaskService.getMyNoEndProcessPageSetData(pageParam, businessSystemDataService.getUserNameId(), formName, modelName, processModelType,startTime);

//        for (int i = 0; i < pageSet.getRows().size(); i++) {
//            pageSet.getRows().get(i).setProcessProgress("30");
//
//            // 查询历史节点表 ACT_HI_ACTINST
//            List<HistoricActivityInstance> historicActivityInstanceList = getHistoricActivityInstAsc(pageSet.getRows().get(i).getProcessInstanceId());
//            //1 正在执行的节点
//            List<String> runningActivitiIdList = new ArrayList<String>();
//            //2 获取已流经的流程线
//            List<String> highLightedFlowIds = new ArrayList<>();
//            //3.已执行历史节点
//            List<String> executedActivityIdList = new ArrayList<String>();
//            historicActivityInstanceList.forEach(historicActivityInstance -> {
//                //1
//                if (null == historicActivityInstance.getEndTime()) {
//                    runningActivitiIdList.add(historicActivityInstance.getActivityId());
//                }
//                //2
//                if (historicActivityInstance.getActivityType().equals("sequenceFlow")) {
//                    highLightedFlowIds.add(historicActivityInstance.getActivityId());
//                }
//                //3
//                if (null != historicActivityInstance.getEndTime() && !historicActivityInstance.getActivityType().equals("sequenceFlow")) {
//                    executedActivityIdList.add(historicActivityInstance.getActivityId());
//                }
//
//            });
//        }

        return pageSet;
    }


    @ApiOperation(value = "已办任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "pageSize", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "formName", value = "模型名称", defaultValue = "modelName", required = true, dataType = "String"),
            @ApiImplicitParam(name = "modelName", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String"),
            @ApiImplicitParam(name = "processModelType", value = "流程模型类型  1 自定义流程界面  2 托拉拽界面", defaultValue = "1", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/getApplyedTasks", method = RequestMethod.POST)
    public Object getApplyedTasks(PageParam pageParam, String formName, String modelName, String processModelType) {
        PageSet<TaskVo> pageSet = apiFlowableTaskService.getApplyedTasks(pageParam, businessSystemDataService.getUserNameId(), formName, modelName, processModelType);
        return pageSet;
    }

    //历史任务点击详情
    @ResponseBody
    @RequestMapping(value = "/applyedTasksClickDetails", method = RequestMethod.POST)
    public Object applyedTasksClickDetails(String processInstanceId, String modelKey) {
        Object object = apiFlowableTaskService.applyedTasksClickDetails(processInstanceId, modelKey);
        return object;
    }

    //已部署流程分页列表
    @ResponseBody
    @RequestMapping(value = "/getDeployedPageSet", method = RequestMethod.POST)
    public Object getDeployedPageSet(PageParam pageParam, String modelName, String modelKey) {
        PageSet<ProcessDefinitionDto> pageSet = apiFlowableTaskService.getDeployedPageSet(pageParam, modelName, modelKey);
        return pageSet;
    }

    //已部署流程删除
    @ResponseBody
    @RequestMapping(value = "/deployedDelete", method = RequestMethod.POST)
    public Object deployedDelete(String deploymentId, String id, String key) {
        Object object = apiFlowableTaskService.deployedDelete(deploymentId, id, key);
        return object;
    }


    //已启动流程
    @ResponseBody
    @RequestMapping(value = "/getStartedPageSet", method = RequestMethod.POST)
    public Object getStartedPageSet(PageParam pageParam, String modelName, String modelKey) {
        PageSet<StartedDto> pageSet = apiFlowableTaskService.getStartedPageSet(pageParam, modelName, modelKey);
        return pageSet;
    }

    //点击已启动流程查看流程图
    @ResponseBody
    @RequestMapping(value = "/clickStartedViewFlowchart", method = RequestMethod.POST)
    public Object clickStartedViewFlowchart(String processInstanceId, String modelKey, String processDefinitionId) {
        Object object = apiFlowableTaskService.clickStartedViewFlowchart(processInstanceId, modelKey, processDefinitionId);
        return object;
    }


    @Autowired
    private RuntimeService runtimeService;

    //进行中流程
    @ResponseBody
    @RequestMapping(value = "/getTaskPageSet", method = RequestMethod.POST)
    public Object getTaskPageSet(PageParam pageParam, String formName, String modelName) {
        PageSet<CommonTaskDto> pageSet = apiFlowableTaskService.getTaskPageSet(pageParam, formName, modelName);
        return pageSet;
    }

    //已完成流程
//    @ResponseBody
//    @RequestMapping(value = "/getCompletedTaskPageSet", method = RequestMethod.POST)
//    public Result getCompletedTaskPageSet(PageParam pageParam, String formName, String modelName) {
//        PageSet<CommonTaskDto> pageSet = apiFlowableTaskService.getCompletedTaskPageSet(pageParam, formName, modelName);
//        return Result.ofSuccess(pageSet);
//    }


    //进行中流程点击详情
    @ResponseBody
    @RequestMapping(value = "/getTaskPageSetClickDetails", method = RequestMethod.POST)
    public Object getTaskPageSetClickDetails(String processInstanceId, String modelKey) {
        Object object = apiFlowableTaskService.getTaskPageSetClickDetails(processInstanceId, modelKey);
        return object;
    }


    @Log(title = "流程任务-同意",module = LogModule.WORKFLOW,businessType = BusinessType.AGREE, method = "getLogMessage")
    @ApiOperation(value = "同意")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "实例Id", defaultValue = "1", required = true, dataType = "String"),
            @ApiImplicitParam(name = "taskId", value = "任务Id", defaultValue = "20", required = true, dataType = "String"),
            @ApiImplicitParam(name = "message", value = "同意原因", defaultValue = "modelName", required = true, dataType = "String"),
            @ApiImplicitParam(name = "processDefinitionId", value = "定义Id", defaultValue = "modelKey", required = true, dataType = "String"),
            @ApiImplicitParam(name = "params", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/agree", method = RequestMethod.POST)
    public Object agree(String processInstanceId, String taskId, String message, String processDefinitionId, @RequestParam Map<String, Object> params) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (!ObjectUtil.isEmpty(task)) {
            setLogMessage(task.getName(),"");
        }
        Object object = apiFlowableTaskService.agree(processInstanceId, taskId, message, processDefinitionId, params);
        List<Task> taskQueryList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        // 流程流转到下一步 推送门户待办
        businessSystemDataService.pushCompleteTask("", task.getId());
        businessSystemDataService.pushAddTaskNew(CollectionUtils.isNotEmpty(taskQueryList)?  taskQueryList.get(0).getId(): "");
        return object;
    }

    @Log(title = "流程任务-同意",module = LogModule.WORKFLOW,businessType = BusinessType.AGREE, method = "getLogMessage")
    @ApiOperation(value = "同意")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "实例Id", defaultValue = "1", required = true, dataType = "String"),
            @ApiImplicitParam(name = "taskId", value = "任务Id", defaultValue = "20", required = true, dataType = "String"),
            @ApiImplicitParam(name = "message", value = "同意原因", defaultValue = "modelName", required = true, dataType = "String"),
            @ApiImplicitParam(name = "processDefinitionId", value = "定义Id", defaultValue = "modelKey", required = true, dataType = "String"),
            @ApiImplicitParam(name = "params", value = "模型key", defaultValue = "modelKey", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/agreeForApi", method = RequestMethod.POST)
    public com.lms.common.model.Result agreeForApi(String processInstanceId, String taskId, String message, String processDefinitionId, @RequestParam Map<String, Object> params) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (!ObjectUtil.isEmpty(task)) {
            setLogMessage(task.getName(),"");
        }
        HashMap resultMap = (HashMap) apiFlowableTaskService.agree(processInstanceId, taskId, message, processDefinitionId, params);
        Integer statusCode = (Integer) resultMap.get("statusCode");
        String errMessage = (String) resultMap.get("message");
        Task taskquery = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("taskId", Optional.ofNullable(taskquery).orElse(task).getId());
        hashMap.put("processInstanceId", processInstanceId);
        // 流程流转到下一步 推送门户待办
        businessSystemDataService.pushCompleteTask("", task.getId());
        businessSystemDataService.pushAddTaskNew(taskquery != null ? taskquery.getId() : "");
        if (200 == statusCode){
            return com.lms.common.model.Result.OK(hashMap);
        }else {
            return com.lms.common.model.Result.error(errMessage);
        }
    }

    @ApiOperation(value = "财务审核出纳接口")
    @ResponseBody
    @RequestMapping(value = "/financeAgree", method = RequestMethod.POST)
    public Object financeAgree(String uuid) {
        TaskVo taskVo = apiFlowableTaskService.getToDoTaskByUuid(businessSystemDataService.getUserNameId(),uuid);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("processInstanceId",taskVo.getProcessInstanceId());
        map.put("taskId",taskVo.getTaskId());
        map.put("message","同意");
        map.put("processDefinitionId",taskVo.getProcessDefinitionId());
        map.put("outcome","同意");
        map.put("tableName","");
        map.put("uuid",uuid);
        Object object = apiFlowableTaskService.agree(taskVo.getProcessInstanceId(),taskVo.getTaskId(),"同意",taskVo.getProcessDefinitionId(),map);
        return object;
    }


    @Log(title = "流程任务-驳回",module = LogModule.WORKFLOW,businessType = BusinessType.REJECT, method = "getLogMessage")
    @ApiOperation(value = "驳回")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "实例Id", defaultValue = "processInstanceId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "taskId", value = "任务Id", defaultValue = "taskId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "message", value = "驳回原因", defaultValue = "message", required = true, dataType = "String"),
            @ApiImplicitParam(name = "distFlowElementId", value = "要驳回的节点", defaultValue = "distFlowElementId", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/reject", method = RequestMethod.POST)
    public Object reject(String processInstanceId, String taskId, String message, String distFlowElementId, @RequestParam Map<String, Object> params) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (!ObjectUtil.isEmpty(task)) {
            setLogMessage(task.getName(),"");
        }
        Object object = apiFlowableTaskService.reject(processInstanceId, taskId, message, distFlowElementId, params);
        List<Task> taskQueryList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        // 流程流转到下一步 推送门户待办
        businessSystemDataService.pushCompleteTask("", task.getId());
        businessSystemDataService.pushAddTaskNew(CollectionUtils.isNotEmpty(taskQueryList)?  taskQueryList.get(0).getId(): "");
        return object;
    }

    @RequestMapping("/getFlowImgByExecutionId")
    public void getFlowImgByExecutionId(String executionId, OutputStream outputStream, HttpServletResponse response) throws Exception {
        try {
            Task task = taskService.createTaskQuery().executionId(executionId).singleResult();
            InputStream inputStream = imageService.generateImageByProcInstId(task.getProcessInstanceId());
            byte[] b = IoUtil.readInputStream(inputStream, "image inputStream name");
            response.getOutputStream().write(b);
        } catch (Exception e) {
            return;
        }
    }


    @ApiOperation(value = "获取可驳回节点列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "实例Id", defaultValue = "processInstanceId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "taskId", value = "任务Id", defaultValue = "taskId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "nodelId", value = "当前节点Id", defaultValue = "nodelId", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/getBackNodesByProcessInstanceId", method = RequestMethod.POST)
    public Object getBackNodesByProcessInstanceId(String processInstanceId, String taskId, String nodelId) {
        List<FlowNodeVo> list = apiFlowableTaskService.getBackNodesByProcessInstanceId(processInstanceId, taskId, nodelId);
        return list;
    }


    @Log(title = "流程任务-转办",module = LogModule.WORKFLOW,businessType = BusinessType.TRANSFER, method = "getLogMessage")
    @ApiOperation(value = "转办")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId", value = "任务Id", defaultValue = "taskId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "comment", value = "转办原因", defaultValue = "comment", required = true, dataType = "String"),
            @ApiImplicitParam(name = "instanceId", value = "实例Id", defaultValue = "instanceId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "userNameId", value = "转办人userNameId", defaultValue = "userNameId", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/transfer", method = RequestMethod.POST)
    public Object transfer(String taskId, String comment, String instanceId, String userNameId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (!ObjectUtil.isEmpty(task)) {
            setLogMessage(task.getName(),"");
        }
        Object object = apiFlowableTaskService.transfer(taskId, comment, instanceId, null,userNameId);
        List<Task> taskQueryList = taskService.createTaskQuery().processInstanceId(instanceId).list();
        // 流程流转到下一步 推送门户待办
        businessSystemDataService.pushCompleteTask("", task.getId());
        businessSystemDataService.pushAddTaskNew(CollectionUtils.isNotEmpty(taskQueryList)?  taskQueryList.get(0).getId(): "");
        return object;
    }


    @Log(title = "流程任务-传阅",module = LogModule.WORKFLOW,businessType = BusinessType.CIRCULATION, method = "getLogMessage")
    @ApiOperation(value = "传阅")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId", value = "任务Id", defaultValue = "taskId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "comment", value = "转办原因", defaultValue = "comment", required = true, dataType = "String"),
            @ApiImplicitParam(name = "instanceId", value = "实例Id", defaultValue = "instanceId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "userNameId", value = "转办人userNameId", defaultValue = "userNameId", required = true, dataType = "String")
    })

    @ResponseBody
    @RequestMapping(value = "/circulation", method = RequestMethod.POST)
    public Object circulation(String taskId, String comment, String instanceId, String userNameId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (!ObjectUtil.isEmpty(task)) {
            setLogMessage(task.getName(),"");
        }
        Object object = apiFlowableTaskService.transfer(taskId, comment, instanceId, null,userNameId);
        return object;
    }


    @Log(title = "流程任务-委派",module = LogModule.WORKFLOW,businessType = BusinessType.DELEGATE, method = "getLogMessage")
    @ApiOperation(value = "委派", notes = "是将任务节点分给其他人处理，等其他人处理好之后，委派任务会自动回到委派人的任务中")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId", value = "任务Id", defaultValue = "taskId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "comment", value = "转办原因", defaultValue = "comment", required = true, dataType = "String"),
            @ApiImplicitParam(name = "instanceId", value = "实例Id", defaultValue = "instanceId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "userNameId", value = "委派人userNameId", defaultValue = "userNameId", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/delegate", method = RequestMethod.POST)
    public Object delegate(String taskId, String comment, String instanceId, String userNameId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (!ObjectUtil.isEmpty(task)) {
            setLogMessage(task.getName(),"");
        }
        Object object = apiFlowableTaskService.delegate(taskId, comment, instanceId, userNameId);
        return object;
    }


    @Log(title = "流程任务-撤回第一步",module = LogModule.WORKFLOW,businessType = BusinessType.REVOCATION, method = "getLogMessage")
    @ApiOperation(value = "撤回第一步,注意撤回第一步的话，第一步任务节点名称必须叫 发起人")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId", value = "任务Id", defaultValue = "taskId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "comment", value = "撤回原因", defaultValue = "comment", required = true, dataType = "String"),
            @ApiImplicitParam(name = "instanceId", value = "实例Id", defaultValue = "instanceId", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/revokeProcess", method = RequestMethod.POST)
    public Object revokeProcess(String taskId, String comment, String instanceId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (!ObjectUtil.isEmpty(task)) {
            setLogMessage(task.getName(),"");
        }
        Object object = apiFlowableTaskService.revokeProcess(taskId, comment, instanceId);
        return object;
    }


    @Log(title = "流程任务-加签",module = LogModule.WORKFLOW,businessType = BusinessType.SIGNATURE, method = "getLogMessage")
    @ApiOperation(value = "加签")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId", value = "任务Id", defaultValue = "taskId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "comment", value = "转办原因", defaultValue = "comment", required = true, dataType = "String"),
            @ApiImplicitParam(name = "instanceId", value = "实例Id", defaultValue = "instanceId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "userNameId", value = "委派人userNameId", defaultValue = "userNameId", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/signature", method = RequestMethod.POST)
    public Object signature(String taskId, String comment, String instanceId, String userNameId, String signature) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (!ObjectUtil.isEmpty(task)) {
            setLogMessage(task.getName(),"");
        }
        Object object = apiFlowableTaskService.signature(taskId, comment, instanceId, userNameId, signature);
        List<Task> taskQueryList = taskService.createTaskQuery().processInstanceId(instanceId).list();
        // 流程流转到下一步 推送门户待办
        businessSystemDataService.pushCompleteTask("", task.getId());
        businessSystemDataService.pushAddTaskNew(CollectionUtils.isNotEmpty(taskQueryList)?  taskQueryList.get(0).getId(): "");
        return object;
    }


    @Log(title = "流程任务-加签",module = LogModule.WORKFLOW,businessType = BusinessType.SIGNATURE, method = "getLogMessage")
    @ApiOperation(value = "加签")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId", value = "任务Id", defaultValue = "taskId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "comment", value = "转办原因", defaultValue = "comment", required = true, dataType = "String"),
            @ApiImplicitParam(name = "instanceId", value = "实例Id", defaultValue = "instanceId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "userNameId", value = "委派人userNameId", defaultValue = "userNameId", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/signatureForApi", method = RequestMethod.POST)
    public com.lms.common.model.Result signatureForApi(String taskId, String comment, String instanceId, String userNameId, String signature) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (!ObjectUtil.isEmpty(task)) {
            setLogMessage(task.getName(),"");
        }
        Object object = apiFlowableTaskService.signature(taskId, comment, instanceId, userNameId, signature);
        return com.lms.common.model.Result.OK(object);
    }

    @Log(title = "流程任务-催办",module = LogModule.WORKFLOW,businessType = BusinessType.PROCESSURGING, method = "getLogMessage")
    @ApiOperation(value = "催办")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "催办类型", defaultValue = "taskId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "comment", value = "转办原因", defaultValue = "comment", required = true, dataType = "String"),
            @ApiImplicitParam(name = "instanceId", value = "实例Id", defaultValue = "instanceId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "userNameId", value = "委派人userNameId", defaultValue = "userNameId", required = true, dataType = "String")
    })
    @ResponseBody
    @RequestMapping(value = "/processUrging", method = RequestMethod.POST)
    public Object processUrging(String type, String content, String formName, String processDefinitionId, String taskId, String assignee, String startUserId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (!ObjectUtil.isEmpty(task)) {
            setLogMessage(task.getName(),"");
        }
        Object object = apiFlowableTaskService.processUrging(type, content, formName, processDefinitionId, taskId, assignee, startUserId);
        return object;
    }


    @Log(title = "流程任务-流程撤销",module = LogModule.WORKFLOW,businessType = BusinessType.REVOCATION, method = "getLogMessage")
    @ApiOperation(value = "流程撤销")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstanceId", value = "实例ID", defaultValue = "taskId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "message", value = "流程撤销原因", defaultValue = "comment", required = true, dataType = "String"),
            @ApiImplicitParam(name = "taskId", value = "任务id", defaultValue = "instanceId", required = true, dataType = "String")
    })
    @ResponseBody
    @PostMapping(value = "/processRevocation")
    public Object processRevocation(String processInstanceId, String message, String taskId) {
        Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
        if (!ObjectUtil.isEmpty(task)) {
            setLogMessage(task.getName(),"");
        }
        Object object = apiFlowableTaskService.processRevocation(processInstanceId, message, taskId);
        return object;
    }

    @Log(title = "流程任务-退回上一步",module = LogModule.WORKFLOW,businessType = BusinessType.GOBACKTOTHEPREVIOUSSTEP, method = "getLogMessage")
    @ApiOperation(value = "退回上一步")
    @ResponseBody
    @PostMapping(value = "/goBackToThePreviousStep")
    public Object goBackToThePreviousStep(String processInstanceId, String currentActivityId, String processDefinitionId, String taskId) {
        Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
        if (!ObjectUtil.isEmpty(task)) {
            setLogMessage(task.getName(),"");
        }
        Object object = apiFlowableTaskService.goBackToThePreviousStep(processInstanceId, currentActivityId, processDefinitionId,taskId);
        return object;
    }

    @Log(title = "流程任务-指定任务节点处理人",module = LogModule.WORKFLOW,businessType = BusinessType.ASSIGNEDTO, method = "getLogMessage")
    @ApiOperation(value = "指定任务节点处理人")
    @ResponseBody
    @PostMapping(value = "/assignedTo")
    public Result goBackToThePreviou1sStep(String assignUser, String processInstanceId, String modelKey) {
        Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
        if (!ObjectUtil.isEmpty(task)) {
            setLogMessage(String.format("指定%s处理%s",assignUser,task.getName()),"");
        }
        Result object = apiFlowableTaskService.assignedTo(assignUser, processInstanceId,modelKey);
        return object;
    }

    // 接收代理任务（系统执行任务转办）
    @Log(title = "流程任务-代理任务接收",module = LogModule.WORKFLOW,businessType = BusinessType.TRANSFER, method = "getLogMessage")
    @ApiOperation(value = "接收代理任务")
    @ResponseBody
    @RequestMapping(value = "/agentTask", method = RequestMethod.POST)
    public Object agentTask(String taskId,String processInstanceId,String userNameId) {
        return apiFlowableTaskService.agent( taskId, processInstanceId, userNameId);
    }

    // 批量接收代理任务
    @Log(title = "流程任务-代理任务接收",module = LogModule.WORKFLOW,businessType = BusinessType.TRANSFER, method = "getLogMessage")
    @ApiOperation(value = "接收代理任务")
    @ResponseBody
    @PostMapping(value = "/agentTasks")
    public Object agentTasks(@RequestBody TaskVo[] taskVos) {
        int failedNum = 0;
        int sucessNum = 0;
        for(int i=0;i<taskVos.length;i++){
            TaskVo taskVo = taskVos[i];
            Map map = (Map)apiFlowableTaskService.agent(taskVo.getTaskId(),taskVo.getProcessInstanceId(),taskVo.getUserNameId());
            if(map.get("statusCode").equals(300)){
                failedNum++;
            }else{
                sucessNum++;
            }
        }
        Map map = new HashMap();
        map.put("statusCode", 200);
        map.put("message", "操作成功，"+sucessNum+"个任务成功接收，"+failedNum+"个任务接收失败");
        return map;
    }


    @GetMapping("/getTaskInfoByBusinessKeyAndModelKey")
    @ResponseBody
    public com.lms.common.model.Result<HashMap<String, HashMap<String, TaskVo>>> getTaskInfoByBusinessKeyAndModelKey(@RequestParam String[] businessKeys, @RequestParam String modelKey){
        return com.lms.common.model.Result.OK(apiFlowableTaskService.getTaskInfoByBusinessKeyAndModelKey(businessKeys, modelKey));
    }

    @GetMapping("/getTaskInfoByBusinessKey")
    @ResponseBody
    public com.lms.common.model.Result<HashMap<String, HashMap<String, TaskVo>>> getTaskInfoByBusinessKey(@RequestParam String[] businessKeys){
        return com.lms.common.model.Result.OK(apiFlowableTaskService.getTaskInfoByBusinessKey(businessKeys));
    }

    @GetMapping("/getTaskInfoByModelKey")
    @ResponseBody
    public com.lms.common.model.Result<HashMap<String, HashMap<String, TaskVo>>> getTaskInfoByModelKey(@RequestParam String modelKey){
        return com.lms.common.model.Result.OK(apiFlowableTaskService.getTaskInfoByModelKey(modelKey));
    }

    @ResponseBody
    @RequestMapping(value = "/getActNameByProcessInstanceIdListForApi", method = RequestMethod.POST)
    public com.lms.common.model.Result<Map<String, Object>> getActNameByProcessInstanceIdListForApi(@RequestBody List<String> processInstanceIdList){
        return com.lms.common.model.Result.OK(apiFlowableTaskService.getActNameByProcessInstanceIdList(processInstanceIdList));
    }

    @ResponseBody
    @RequestMapping(value = "/geCurrentAssigneeByTaskIdListForApi", method = RequestMethod.POST)
    public com.lms.common.model.Result<Map<String, Object>> geCurrentAssigneeByTaskIdListForApi(@RequestBody List<String> taskIdList){
        return com.lms.common.model.Result.OK(apiFlowableTaskService.geCurrentAssigneeByTaskIdList(taskIdList));
    }

}
