/**
 *
 *
 * SBTR LTD.
 *
 */
package com.lms.common.util;

import java.io.*;
import java.util.zip.ZipOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.Enumeration;

/**
 * 文件常用工具方法类。
 * <AUTHOR>
 */
public final class FileHelper {

	/**
	 * 载入文本内容(UTF-8编码)
	 * @param filename 文件名
	 * @return			文件内容字符串
	 */
	public static String loadFile(String filename) {
		String docContent = "";
		try {
			File rf = new File(filename);
			InputStreamReader read = new InputStreamReader(new FileInputStream(
					rf), "utf-8");
			BufferedReader reader = new BufferedReader(read);
			String line = new String();
			while ((line = reader.readLine()) != null) {
				docContent += line;
				docContent += "\r\n";
			}
			reader.close();
			read.close();

		} catch (Exception ex) {
			System.out.println(ex.toString());
		}
		return docContent;
	}

	/**
	 * 创建目录。
	 * @param folderPath	目录路径
	 * @return				是否创建成功
	 * @throws IOException
	 */
	public static boolean createFolder(String folderPath) throws IOException {
		boolean result = false;
		File f = new File(folderPath);
		result = f.mkdirs();
		return result;
	}

	/**
	 * 删除目录下所有文件。
	 * @param directory 	File对象
	 */
	public void emptyDirectory(File directory) {
		File[] entries = directory.listFiles();
		for (int i = 0; i < entries.length; i++) {
			entries[i].delete();
		}
	}

	/**
	 * 创建文件
	 * @param filepath		文件所在目录路径,比如:c:/test/test.txt
	 * @return				是否创建成功
	 */
	public static boolean makeFile(String filepath) throws IOException {
		boolean result = false;
		File file = new File(filepath);
		result = file.createNewFile();
		file = null;
		return result;
	}

	/**
	 * 删除文件
	 * @param filepath		文件所在物理路径
	 * @return				是否创建成功
	 */
	public static boolean deleteFile(String filepath) {
		boolean result = false;
		File file = new File(filepath);
		result = file.delete();
		file = null;
		return result;
	}


	/**
	 * 将内容写入文件中
	 * @param filepath		文件所在物理路径
	 * @param content		写入内容
	 * @throws Exception
	 */
	public static void writeFile(String filepath, String content)throws Exception {
		FileWriter filewriter = new FileWriter(filepath, true);// 写入多行
		PrintWriter printwriter = new PrintWriter(filewriter);
		printwriter.println(content);
		printwriter.flush();
		printwriter.close();
		filewriter.close();
	}

	/**
	 * 移动文件,相当于linux 中mv命令,但与平台无关:
	 * This class moves an input file to output file.
	 * @param input    input file to move from
	 * @param output    output file
	 */
	public static void moveFile(String input, String output) {
		File inputFile = new File(input);
		File outputFile = new File(output);
		inputFile.renameTo(outputFile);
	}

	/**
	 * 拷贝文件,相当于linux中cp命令,但与平台无关,可以拷贝文本 或二进制文件:
	 * This class copies an input file to output file
	 * @param input   input file to copy from
	 * @param output   output file
	 */
	public static boolean copyFile(String input, String output) throws Exception {
		InputStream inStream = null;
		OutputStream outStream = null;
		try{
			try{
				inStream = new BufferedInputStream(new FileInputStream(input));
				outStream = new BufferedOutputStream(new FileOutputStream(output));
				int readPosition = -1;
				byte[] dataArray = new byte[1024 * 10];
				while((readPosition = inStream.read(dataArray)) != -1){
					outStream.write(dataArray, 0, readPosition);
				}
				outStream.flush();
			}finally{
				if(inStream != null)
					inStream.close();
				if(outStream != null)
					outStream.close();
			}
		}catch(IOException e){
			e.printStackTrace();
			throw new RuntimeException(e);
		}
		return true;
	}

	/**
	 * 删除目录.包括目录下所有文件和目录:
	 * This class del a directory recursively,that means delete all files and directorys.
	 * @param directory   directory the directory that will be deleted.
	 */
	public static void recursiveRemoveDir(File directory) throws Exception {
		if (!directory.exists())
			throw new IOException(directory.toString() + " do not exist!");
		String[] filelist = directory.list();
		File tmpFile = null;
		for (int i = 0; i < filelist.length; i++) {
			tmpFile = new File(directory.getAbsolutePath(), filelist[i]);
			if (tmpFile.isDirectory()) {
				recursiveRemoveDir(tmpFile);
			} else if (tmpFile.isFile()) {
				tmpFile.delete();
			}
		}
		directory.delete();
	}

	/**
	 * 根据文件名后缀返回Mime类型。
	 * @param fName		文件名
	 * @return				Mime类型
	 */
	public static String getMimeType(String fName) {
		fName = fName.toLowerCase();
		if (fName.endsWith(".jpg") || fName.endsWith(".jpeg")
				|| fName.endsWith(".jpe"))
			return "image/jpeg";
		else if (fName.endsWith(".gif"))
			return "image/gif";
		else if (fName.endsWith(".pdf"))
			return "application/pdf";
		else if (fName.endsWith(".htm") || fName.endsWith(".html")
				|| fName.endsWith(".shtml"))
			return "text/html";
		else if (fName.endsWith(".avi"))
			return "video/x-msvideo";
		else if (fName.endsWith(".mov") || fName.endsWith(".qt"))
			return "video/quicktime";
		else if (fName.endsWith(".mpg") || fName.endsWith(".mpeg")
				|| fName.endsWith(".mpe"))
			return "video/mpeg";
		else if (fName.endsWith(".zip"))
			return "application/zip";
		else if (fName.endsWith(".tiff") || fName.endsWith(".tif"))
			return "image/tiff";
		else if (fName.endsWith(".rtf"))
			return "application/rtf";
		else if (fName.endsWith(".mid") || fName.endsWith(".midi"))
			return "audio/x-midi";
		else if (fName.endsWith(".xl") || fName.endsWith(".xls")
				|| fName.endsWith(".xlv") || fName.endsWith(".xla")
				|| fName.endsWith(".xlb") || fName.endsWith(".xlt")
				|| fName.endsWith(".xlm") || fName.endsWith(".xlk"))
			return "application/excel";
		else if (fName.endsWith(".doc") || fName.endsWith(".dot"))
			return "application/msword";
		else if (fName.endsWith(".png"))
			return "image/png";
		else if (fName.endsWith(".xml"))
			return "text/xml";
		else if (fName.endsWith(".svg"))
			return "image/svg+xml";
		else if (fName.endsWith(".mp3"))
			return "audio/mp3";
		else if (fName.endsWith(".ogg"))
			return "audio/ogg";
		else
			return "text/plain";
	}

	/**
	 * 将byte转换为kbytes或者Mbytes。
	 * This Method converts a byte size in a kbytes or Mbytes size, depending on the size
	 * @param size bytes
	 * @return String 大小及单位
	 */
	public static String convertFileSize(long size) {
		int divisor = 1;
		String unit = "bytes";
		if (size >= 1024 * 1024) {
			divisor = 1024 * 1024;
			unit = "MB";
		} else if (size >= 1024) {
			divisor = 1024;
			unit = "KB";
		}
		if (divisor == 1)
			return size / divisor + " " + unit;
		String aftercomma = "" + 100 * (size % divisor) / divisor;
		if (aftercomma.length() == 1)
			aftercomma = "0" + aftercomma;
		return size / divisor + "." + aftercomma + " " + unit;
	}

	/**
	 * Zip压缩文件。
	 * @param src		源文件
	 * @param dest		压缩后文件
	 */
    public static void zip(String src, String dest) {
        int BUFFER = 2048;
        try {
            BufferedInputStream origin = null;
            FileOutputStream destStream = new FileOutputStream(dest);
            ZipOutputStream out = new ZipOutputStream(new BufferedOutputStream(
                    destStream));
            byte data[] = new byte[BUFFER];
            File f = new File("e:\\test\\a\\");
            File files[] = f.listFiles();

            for (int i = 0; i < files.length; i++) {
                FileInputStream fi = new FileInputStream(files[i]);
                origin = new BufferedInputStream(fi, BUFFER);
                ZipEntry entry = new ZipEntry(files[i].getName());
                out.putNextEntry(entry);
                int count;
                while ((count = origin.read(data, 0, BUFFER)) != -1) {
                    out.write(data, 0, count);
                }
                origin.close();
            }
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 解压文件。
     * @param src		源文件
     * @param dest		解压后文件
     */
    public static void unzip(String src, String dest) {
        int BUFFER = 2048;
        try {
            ZipFile zipFile = new ZipFile(src);
            Enumeration emu = zipFile.entries();
            int i = 0;
            while (emu.hasMoreElements()) {
                ZipEntry entry = (ZipEntry) emu.nextElement();
                //会把目录作为一个file读出一次，所以只建立目录就可以，之下的文件还会被迭代到。
                if (entry.isDirectory()) {
                    new File(dest + entry.getName()).mkdirs();
                    continue;
                }
                BufferedInputStream bis = new BufferedInputStream(zipFile.getInputStream(entry));
                File file = new File(dest + entry.getName());
                //加入这个的原因是zipfile读取文件是随机读取的，这就造成可能先读取一个文件
                //而这个文件所在的目录还没有出现过，所以要建出目录来。
                File parent = file.getParentFile();
                if (parent != null && (!parent.exists())) {
                    parent.mkdirs();
                }
                FileOutputStream fos = new FileOutputStream(file);
                BufferedOutputStream bos = new BufferedOutputStream(fos, BUFFER);

                int count;
                byte data[] = new byte[BUFFER];
                while ((count = bis.read(data, 0, BUFFER)) != -1) {
                    bos.write(data, 0, count);
                }
                bos.flush();
                bos.close();
                bis.close();
            }
            zipFile.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
	public static void outPutStream2File(InputStream inputStream,String filePath){
		// 指定要写入的文件路径
		try {
			OutputStream outputStream = new FileOutputStream(filePath);
			// 将InputStream的内容写入到文件中
			byte[] buffer = new byte[1024];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}

