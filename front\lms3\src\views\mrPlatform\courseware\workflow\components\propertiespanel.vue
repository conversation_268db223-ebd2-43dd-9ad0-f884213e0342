<template>
  <div ref="propertiesPanel" class="properties-panel">
    <a-form :model="formData" style="height: 100%">
      <a-card v-if="formData.elementType === 1" title="流程信息">
        <a-form-item label="流程名称">
          <a-input
            :value="formData.name"
            @change="updateProperties('name', e.target.value)"
          />
        </a-form-item>
      </a-card>
      <a-card v-else-if="formData.elementType === 2" title="节点信息">
        <a-form-item label="节点名称">
          <a-input
            :value="formData.name"
            @change="e => updateProperties('name', e.target.value)"
          />
        </a-form-item>
      </a-card>
      <a-card v-else-if="formData.elementType === 3" title="连接线信息">
        <a-form-item label="连接线内容">
          <a-input
            :value="formData.name"
            @change="e => updateProperties('name', e.target.value)"
          />
        </a-form-item>
      </a-card>
      <a-card v-else-if="formData.elementType === 4" title="开始节点">
        <a-form-item label="节点名称">
          <a-input
            :value="formData.name"
            @change="e => updateProperties('name', e.target.value)"
          />
        </a-form-item>
      </a-card>
      <a-card v-else-if="formData.elementType === 5" title="结束节点">
        <a-form-item label="节点名称">
          <a-input
            :value="formData.name"
            @change="e => updateProperties('name', e.target.value)"
          />
        </a-form-item>
      </a-card>
      <a-card v-else-if="formData.elementType === 6" title="网关">
        <a-form-item label="网关名称">
          <a-input
            :value="formData.name"
            @change="e => updateProperties('name', e.target.value)"
          />
        </a-form-item>
      </a-card>
      <a-card v-else-if="formData.elementType === 7" title="流程信息">
        <a-form-item label="流程名称">
          <a-input
            :value="formData.name"
            @change="e => updateProperties('name', e.target.value)"
          />
        </a-form-item>
      </a-card>
      <a-card v-else-if="formData.elementType === 8" title="数据库对象">
        <a-form-item label="名称">
          <a-input
            :value="formData.name"
            @change="e => updateProperties('name', e.target.value)"
          />
        </a-form-item>
      </a-card>
      <a-card v-else-if="formData.elementType === 9" title="泳道">
        <a-form-item label="名称">
          <a-input
            :value="formData.name"
            @change="e => updateProperties('name', e.target.value)"
          />
        </a-form-item>
      </a-card>
      <a-card v-else-if="formData.elementType === 10" title="分组">
        <a-form-item label="名称">
          <a-input
            :value="formData.name"
            @change="e => updateProperties('name', e.target.value)"
          />
        </a-form-item>
      </a-card>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, toRaw } from "vue";
import "bpmn-js-properties-panel/dist/assets/properties-panel.css";

const propertiesPanel = ref(null);
const props = defineProps({
  modeler: Object,
  currentElement: Object
});

const emit = defineEmits<{
  updateProperties: [value: any];
}>();
const formData = ref({});

// 更新基础属性
const updateProperties = (key, value) => {
  emit("updateProperties", key, value);
};

// 更新扩展属性（如Flowable扩展）
const updateExtension = (key, value) => {
  const modeling = props.modeler.get("modeling");
  const extensions = {
    ...currentElement.value.businessObject.extensionElements
  };
  extensions[key] = value;
  modeling.updateProperties(props.currentElement, {
    extensionElements: extensions
  });
};

onMounted(() => {
  if (props.modeler && propertiesPanel.value) {
    props.modeler.get("propertiesPanel").attachTo(propertiesPanel.value);
  }
});

watch(
  () => props.modeler,
  newModeler => {
    if (newModeler && propertiesPanel.value) {
      newModeler.get("propertiesPanel").attachTo(propertiesPanel.value);
    }
  }
);
watch(
  () => props.currentElement,
  e => {
    if (e.businessObject) {
      let et = e.businessObject.$type;
      formData.value.name = e.businessObject.name;
      if (et.endsWith("Process")) {
        formData.value.elementType = 1;
      } else if (et.endsWith("Task")) {
        formData.value.elementType = 2;
      } else if (et.endsWith("SequenceFlow")) {
        formData.value.elementType = 3;
      } else if (et.endsWith("StartEvent")) {
        formData.value.elementType = 4;
      } else if (et.endsWith("EndEvent")) {
        formData.value.elementType = 5;
      } else if (et.endsWith("Gateway")) {
        formData.value.elementType = 6;
      } else if (et.endsWith("SubProcess")) {
        formData.value.elementType = 7;
      } else if (et.endsWith("DataStoreReference")) {
        formData.value.elementType = 8;
      } else if (et.endsWith("Participant")) {
        formData.value.elementType = 9;
      } else if (et.endsWith("Group")) {
        formData.value.elementType = 10;
      }
    }
  }
);
</script>

<style scoped>
.properties-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  overflow: auto;
  background: #f8f8f8;
}
</style>
