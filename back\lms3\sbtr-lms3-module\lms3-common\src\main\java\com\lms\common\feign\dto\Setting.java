package com.lms.common.feign.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * Setting entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode
@Data
public class Setting implements Serializable {

    private String id;

    private String itemname;

    private String itemvalue;

    private String itemdesc;

    private String itemorder;

    private Integer status;


}
