export interface Model {
  id: number;
  name: string;
  number: string;
  desc: string;
  field: Record<string, string>;
  fileUrl: string;
  previewUrl: string;
  uploadPerson: string;
  uploadTime: string | null;
}

export interface ModelListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  result: {
    records: Model[];
    total: number;
    size: number;
    current: number;
    pages: number;
    orders: any[];
    optimizeCountSql: boolean;
    searchCount: boolean;
    countId: string | null;
    maxLimit: string | null;
  };
  timestamp: number;
}

export interface ModelFilter {
  ATA章节?: string;
  SNS代码?: string;
  件号?: string;
}

export interface ModelListParams extends ModelFilter {
  pageIndex?: number;
  pageSize?: number;
}
