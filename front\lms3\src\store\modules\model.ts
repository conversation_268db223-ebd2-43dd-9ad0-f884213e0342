import { defineStore } from "pinia";

interface VideoThumbnailsState {
  videoThumbnails: Record<string | number, string>;
}

export const useModelStore = defineStore("model", {
  state: (): VideoThumbnailsState => ({
    videoThumbnails: {}
  }),
  actions: {
    setVideoThumbnail(modelId: string | number, base64: string) {
      this.videoThumbnails[modelId] = base64;
    },
    getVideoThumbnail(modelId: string | number): string | undefined {
      return this.videoThumbnails[modelId];
    }
  }
});
