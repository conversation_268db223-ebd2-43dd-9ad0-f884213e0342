<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.SysPositionMapper">
    <sql id="Base_Column_List">*</sql>
    <select id="getPositionText" resultType="java.lang.String">
        select position_text from sys_position where uuid in
        <foreach collection="array" item="uuid" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </select>

    <select id="getPosition" resultType="java.lang.String">
        select position_text from sys_position where uuid = #{uuid}
    </select>
    <select id="getPageSetData" resultType="com.sbtr.workflow.model.SysPosition">
        select
        <include refid="Base_Column_List"></include>
        from sys_position
        where 1=1
        <if test="positionName!=null and positionName!=''">
            and position_name like '%'||#{positionName}||'%'
        </if>
        order by position_sort asc
    </select>
</mapper>
