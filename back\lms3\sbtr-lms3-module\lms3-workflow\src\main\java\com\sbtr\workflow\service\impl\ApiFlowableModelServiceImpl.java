package com.sbtr.workflow.service.impl;

import com.sbtr.workflow.constants.DriverClassName;
import com.sbtr.workflow.model.*;
import com.sbtr.workflow.utils.BaseUtils;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.dto.CommentBean;
import com.sbtr.workflow.mapper.ApiFlowableModelMapper;
import com.sbtr.workflow.service.*;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.PageUtil.PageUtils;
import com.sbtr.workflow.vo.ActDeModelVo;
import com.sbtr.workflow.vo.ProcessDefinitionVo;
import com.sbtr.workflow.vo.TaskVo;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.Deployment;
import org.flowable.ui.common.util.XmlUtil;
import org.flowable.ui.modeler.domain.Model;
import org.flowable.ui.modeler.repository.ModelRepository;
import org.flowable.ui.modeler.serviceapi.ModelService;
import org.flowable.validation.ProcessValidator;
import org.flowable.validation.ProcessValidatorFactory;
import org.flowable.validation.ValidationError;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Service("apiFlowableModelServiceImpl")
public class ApiFlowableModelServiceImpl extends BaseServiceImpl implements ApiFlowableModelService {

    protected static final Logger LOGGER = LoggerFactory.getLogger(ApiFlowableModelServiceImpl.class);

    @Autowired
    private ApiFlowableProcessDefinitionService apiFlowableProcessDefinitionService;

    @Autowired
    private ModelService modelService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private ApiFlowableModelMapper apiFlowableModelMapper;

    @Autowired
    private ActMyModelService flowModelService;

    @Autowired
    private ActMyNodeButtonService flowNodeButtonService;

    @Autowired
    private ActMyNodeFieldService flowNodeFieldService;

    @Autowired
    private ActMyNodeNoticeService flowNodeNoticeService;
    @Autowired
    private ActMyNodeCodeService actMyNodeCodeService;
    //通用部署
    @Transactional
    @Override
    public Object deployModelId(String modelId, String categoryCode) {
        if (StringUtils.isBlank(modelId) || StringUtils.isBlank(categoryCode)) {
            return failure("模型Id/分类Id不能为空");
        }
        try {
            Model model=null;
            if (DriverClassName.POSTGRESQL_NAME.equals(driverClassName)) {
                model = apiFlowableModelMapper.getModel(modelId.trim());
            } else {
                model = this.modelService.getModel(modelId.trim());
            }
            BpmnModel bpmnModel = modelService.getBpmnModel(model);
            //流程租户标识 暂时不考虑这回事
            String tenantId = businessSystemDataService.getTenantId();
            //必须指定文件后缀名否则部署不成功
            Deployment deploy = repositoryService.createDeployment()
                    .name(model.getName())
                    .key(model.getKey())
                    .category(categoryCode)
                    .tenantId(tenantId)
                    .addBpmnModel(model.getKey() + ".bpmn", bpmnModel)
                    .deploy();
            return success("流程发布成功！！！");
        } catch (Exception e) {
            LOGGER.error("流程部署失败,失败信息为" + e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure("流程部署失败！！！");
        }
    }


    @Override
    public Object deleteModel(String modelId) {
        Model model=null;
        if (DriverClassName.POSTGRESQL_NAME.equals(driverClassName)) {
            model = apiFlowableModelMapper.getModel(modelId.trim());
        } else {
            model = this.modelService.getModel(modelId.trim());
        }
        try {
            modelService.deleteModel(model.getId());
            return success("删除成功！！！");
        } catch (Exception var4) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure("删除失败！！！" + var4.getMessage());
        }
    }


    @Override
    public void loadXmlByModelId(String modelId, HttpServletResponse response) {
        if (StringUtils.isBlank(modelId)) {
            return;
        }
        try {
            Model model=null;
            if (DriverClassName.POSTGRESQL_NAME.equals(driverClassName)) {
                model = apiFlowableModelMapper.getModel(modelId.trim());
            } else {
                model = this.modelService.getModel(modelId.trim());
            }
            byte[] b = modelService.getBpmnXML(model);
            response.setHeader("Content-type", "text/xml;charset=UTF-8");
            response.getOutputStream().write(b);
        } catch (Exception e) {
            LOGGER.info("查看失败：modelId=" + modelId, e);
            e.printStackTrace();
        }
    }


    @Override
    public void downLoadXmlByModelId(String modelId, HttpServletResponse response) {
        try {
            if (StringUtils.isBlank(modelId)) {
                return;
            }
            response.setContentType("application/octet-stream");
            Model model=null;
            if (DriverClassName.POSTGRESQL_NAME.equals(driverClassName)) {
                model = apiFlowableModelMapper.getModel(modelId.trim());
            } else {
                model = this.modelService.getModel(modelId.trim());
            }
            BpmnModel models = modelService.getBpmnModel(model);
            byte[] bpmnBytess = new BpmnXMLConverter().convertToXML(models);
            LOGGER.info(new String(new BpmnXMLConverter().convertToXML(models)));
            ByteArrayInputStream in = new ByteArrayInputStream(bpmnBytess);
            String filename = models.getMainProcess().getId() + ".bpmn20.xml";
            response.setHeader("Content-Disposition", "attachment; filename=" + filename);
            IOUtils.copy(in, response.getOutputStream());  //这句必须放到setHeader下面，否则10K以上的xml无法导出，
            response.flushBuffer();

        } catch (Exception e) {
            e.printStackTrace();
            return;
        }

    }

    @Override
    public PageSet<ActDeModelVo> getPageSet(PageParam pageParam, String modelName, String modelKey, String modelType) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<ActDeModelVo> list;
        if(DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)){
            list = apiFlowableModelMapper.getPageSetOracle(modelName, modelKey, modelType);
        }else {
            list = apiFlowableModelMapper.getPageSetMySql(modelName, modelKey, modelType);
        }
        PageInfo<ActDeModelVo> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public PageSet<ActDeModelVo> getListByModelKey(PageParam pageParam, String modelKey) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<ActDeModelVo> list = apiFlowableModelMapper.getListByModelKey(modelKey);
        PageInfo<ActDeModelVo> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Transactional
    @Override
    public Object setMajorVersion(String procdefId, String modelKey, String name) {
        try {
            if (StrUtil.hasBlank(procdefId, modelKey, name)) {
                return failure("流程定义Id/模型key/模型名称不能为空不能为空！！！");
            }
            String majorVersion = "是";
            List<ProcessDefinitionVo> list = apiFlowableProcessDefinitionService.getListByIdAndModelKey(modelKey, majorVersion);
            if (list.size() > 1) {
                return failure("数据异常,请联系管理员处理！！！");
            }
            //先更新为否
            Integer integer = apiFlowableProcessDefinitionService.updateMajorVersionByModelKey(modelKey, "否");
            //再更新为是
            Integer Integer = apiFlowableProcessDefinitionService.updateMajorVersion(procdefId, modelKey, majorVersion);

            //本来之前流程名称不允许修改的  设置主版本必须去更新下act_de_model表中name字段 应为该表中不会存在多个版本的数据 name字段必须和主版本保持一致
            Integer Integer11 = apiFlowableModelMapper.updateNameByModelKey(name, modelKey);


            return success("设置主版本成功！！！");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure("设置主版本失败" + e.getMessage());
        }

    }

    @Override
    public Model getModelById(String modelId){
        try{
            Model model=null;
            if (DriverClassName.POSTGRESQL_NAME.equals(driverClassName)) {
                model = apiFlowableModelMapper.getModel(modelId.trim());
            } else {
                model = this.modelService.getModel(modelId.trim());
            }
            return model;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String getModelNameById(String modelId){
        try{
            Model model=getModelById(modelId);
            if(model!=null)
                return model.getName();
        }catch (Exception e){
            e.printStackTrace();
        }
        return "";
    }

    @Transactional
    @Override
    public Object processDeployment(String modelId, String categoryCode, String procdefIds) {
        if (StringUtils.isBlank(modelId)) {
            return failure("模型Id不能为空");
        }
        try {
            //重新获取模型 解决postgresql直接调用流程api出错
            Model model=null;
            if (DriverClassName.POSTGRESQL_NAME.equals(driverClassName)) {
                model = apiFlowableModelMapper.getModel(modelId.trim());
            } else {
                model = this.modelService.getModel(modelId.trim());
            }
            //再次发布分类不允许修改，只能用之前已经发布的分类
            //根据流程modelKey去流程部署表中查询数据
//            List<ActReDeploymentVo> list1 = apiFlowableModelMapper.getActReDeploymenListByModelKey(model.getKey());
//            if (null != list1 && !list1.isEmpty()) {
//                if (!categoryCode.equals(list1.get(0).getCategory())) {
//                    return failure("再次发布时分类不允许修改为其他分类！！！");
//                }
//            }
            BpmnModel bpmnModel = modelService.getBpmnModel(model);
            // 流程租户标识 暂时先不考虑这回事 广州赛宝腾睿信息科技有限公司
            String tenantId = businessSystemDataService.getTenantId();
            //必须指定文件后缀名否则部署不成功
            Deployment deploy = repositoryService.createDeployment()
                    .name(model.getName())
                    .key(model.getKey())
                    .category(categoryCode)
                    .tenantId(tenantId)
                    .addBpmnModel(model.getKey() + ".bpmn", bpmnModel)
                    .deploy();
            //以下为特殊处理  广州赛宝腾睿信息科技有限公司
            //1.根据modelKy去流程定义表去查询是否只有一条数据 如果有则去更新为主版本
//            Integer integer = apiFlowableProcessDefinitionService.getCountSumByModelKey(model.getKey());
//            if (1 == integer) {
//                apiFlowableProcessDefinitionService.updateMajorVersionByModelKey(model.getKey(), "是");
//            }
            //2.根据模型key以及流程定义Id为空查找数据,只会有一条数据如果有多条则提示有问题
            List<ActMyModel> lists = flowModelService.getListByModelKeyAndProcdefIdIsNull(model.getKey());
            //查询流程定义Id
            String procdefId = apiFlowableProcessDefinitionService.getProdefIdByDeployId(deploy.getId());

            if (1 == lists.size()) {// 新建流程
                //去更新流程定义Id
                //1.流程表单数据
                lists.get(0).setProcdefId(procdefId);
                flowModelService.updateByPrimaryKeySelective(lists.get(0));
                //2.流程按钮数据 根据key以及流程定义Id为空更新流程定义Id
                Integer integer1 = flowNodeButtonService.updateProcdefIdByModelKey(model.getKey(), procdefId);
                // 得判断是自定义流程还是可视化流程    自定义流程没有节点字段
                if ("2".equals(lists.get(0).getModelType())) {
                    //3.节点字段可编辑不可编辑数据 根据key以及流程定义Id为空更新流程定义Id
                    Integer integer2 = flowNodeFieldService.updateProcdefIdByModelKey(model.getKey(), procdefId);
                }
                //4.节点通知 根据key以及流程定义Id为空更新流程定义Id
                Integer integer3 = flowNodeNoticeService.updateProcdefIdByModelKey(model.getKey(), procdefId);
                //5.节点权限码更新更新流程定义Id
                actMyNodeCodeService.updateProcdefIdByModelKey(model.getKey(), procdefId);
            } else if (0 == lists.size()) {
                //当查询不到数据是则直接根据流程定义Id以及模型key到act_my_model表中查询数据  再次新增数据为新版本
                //1.根据模型key以及流程定义Id查询flowModel表数据    流程模型
                List<ActMyModel> list = flowModelService.getListByActDeModelKeyAndProcdefId(model.getKey(), procdefIds);
                //2.根据模型key以及流程定义Id查询FlowNodeButton表数据   流程按钮
                List<ActMyNodeButton> flowNodeButtonList = flowNodeButtonService.getListByActDeModelKeyAndProcdefId(model.getKey(), procdefIds);
                //3.根据模型key以及流程定义Id查询FlowNodeField表数据  流程字段
                List<ActMyNodeField> flowNodeFieldList = flowNodeFieldService.getListByActDeModelKeyAndProcdefId(model.getKey(), procdefIds);
                //4.根据模型key以及流程定义Id查询FlowNodeNotice表数据  流程通知
                List<ActMyNodeNotice> flowNodeNoticeList = flowNodeNoticeService.getListByActDeModelKeyAndProcdefId(model.getKey(), procdefIds);
                List<ActMyNodeCode> actMyNodeCodes = actMyNodeCodeService.getListByActDeModelKeyAndProcdefId(model.getKey(),procdefIds);
                updateCopyModels(list.get(0), procdefId, flowNodeButtonList, flowNodeFieldList, flowNodeNoticeList,actMyNodeCodes);
            } else {
                LOGGER.error("数据出现异常,请联系管理员--- 根据模型key以及流程定义Id为空查找数据,只会有一条数据如果有多条则提示有问题");
                return failure("数据出现异常,请联系管理员!!!");
            }
            return success("流程部署成功！！！");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("流程部署失败,失败信息为---" + e.getMessage());
            return failure(e.getMessage());
        }
    }

    @Transactional
    @Override
    public Object copyProcess(String modelId, String categoryCode, String procdefIds) {
        if (StringUtils.isBlank(modelId)) {
            return failure("流程模型Id不能为空");
        }
        try {
            Model model = modelService.getModel(modelId.trim());
//            //再次发布分类不允许修改，只能用之前已经发布的分类
//            //根据流程modelKey去流程部署表中查询数据
//            List<ActReDeploymentVo> list1 = apiFlowableModelMapper.getActReDeploymenListByModelKey(model.getKey());
//            if (null != list1 && !list1.isEmpty()) {
//                if (!categoryCode.equals(list1.get(0).getCategory())) {
//                    return failure("再次发布时分类不允许修改为其他分类！！！");
//                }
//            }

            //查询流程定义Id
            String procdefId = BaseUtils.UUIDGenerator();
                //当查询不到数据是则直接根据流程定义Id以及模型key到act_my_model表中查询数据  再次新增数据为新版本
                //1.根据模型key以及流程定义Id查询flowModel表数据    流程模型
                List<ActMyModel> list = flowModelService.getListByActDeModelKeyAndProcdefId(model.getKey(), procdefIds);
                //2.根据模型key以及流程定义Id查询FlowNodeButton表数据   流程按钮
                List<ActMyNodeButton> flowNodeButtonList = flowNodeButtonService.getListByActDeModelKeyAndProcdefId(model.getKey(), procdefIds);
                //3.根据模型key以及流程定义Id查询FlowNodeField表数据  流程字段
                List<ActMyNodeField> flowNodeFieldList = flowNodeFieldService.getListByActDeModelKeyAndProcdefId(model.getKey(), procdefIds);
                //4.根据模型key以及流程定义Id查询FlowNodeNotice表数据  流程通知
                List<ActMyNodeNotice> flowNodeNoticeList = flowNodeNoticeService.getListByActDeModelKeyAndProcdefId(model.getKey(), procdefIds);
                List<ActMyNodeCode> actMyNodeCodes = actMyNodeCodeService.getListByActDeModelKeyAndProcdefId(model.getKey(),procdefIds);
                copyFlowModel(list.get(0), procdefId, flowNodeButtonList, flowNodeFieldList, flowNodeNoticeList,actMyNodeCodes);
            return success("流程复制成功！！！");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("流程复制失败,失败信息为---" + e.getMessage());
            return failure(e.getMessage());
        }
    }

    @Autowired
    protected ModelRepository modelRepository;

    @Transactional
    @Override
    public Object deleteModelByModelId(String modelId, String procdefId,int delType) {
        //重新获取模型 解决postgresql直接调用流程api出错
        Model model=null;
        if (DriverClassName.POSTGRESQL_NAME.equals(driverClassName)) {
            model = apiFlowableModelMapper.getModel(modelId.trim());
        } else {
            model = this.modelService.getModel(modelId.trim());
        }
        if (ObjectUtil.isEmpty(model)) {
            return failure("查找不到要删除的数据，请重试或刷新当前页面！！！");
        }
        try {
            String modelKey = model.getKey();
            //1.删除模型数据
            if(delType == 0)
            this.deleteModel(modelId);
            //2.删除流程与表单关联
            Example example = new Example(ActMyModel.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("actDeModelKey", modelKey);
            if (StringUtils.isNotBlank(procdefId)) {
                criteria.andEqualTo("procdefId", procdefId);
            }
            flowModelService.deleteByExample(example);
            //3.删除流程按钮关联数据
            Example examples = new Example(ActMyNodeButton.class);
            Example.Criteria criterias = examples.createCriteria();
            criterias.andEqualTo("actDeModelKey", modelKey);
            if (StringUtils.isNotBlank(procdefId)) {
                criterias.andEqualTo("procdefId", procdefId);
            }
            flowNodeButtonService.deleteByExample(examples);
            //3.删除流程字段关联数据
            Example example1 = new Example(ActMyNodeField.class);
            Example.Criteria criteria1 = example1.createCriteria();
            criteria1.andEqualTo("modelKey", modelKey);
            if (StringUtils.isNotBlank(procdefId)) {
                criteria1.andEqualTo("procdefId", procdefId);
            }
            flowNodeFieldService.deleteByExample(example1);

            //4.删除节点通知
            Example example2 = new Example(ActMyNodeNotice.class);
            Example.Criteria criteria2 = example2.createCriteria();
            criteria2.andEqualTo("actDeModelKey", modelKey);
            if (StringUtils.isNotBlank(procdefId)) {
                criteria2.andEqualTo("procdefId", procdefId);
            }
            flowNodeNoticeService.deleteByExample(example2);
            return success("删除成功！！！");
        } catch (Exception var4) {
            LOGGER.error("删除失败,失败原因为" + var4.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure("删除失败！！！");
        }


    }

    @Override
    public List<ActDeModelVo> getDataByModelKey(String modelKey) {
        return apiFlowableModelMapper.getDataByModelKey(modelKey);
    }

    public void deleteDateByModelId(String modelId){
        apiFlowableModelMapper.deleteDateByModelId(modelId);
    }

    @Value("${spring.datasource.dynamic.datasource.master.driver-class-name:null}")
    public String driverClassName;

    public void copyFlowModel(ActMyModel flowModel, String procdefId, List<ActMyNodeButton> formBtnLists, List<ActMyNodeField> flowNodeField, List<ActMyNodeNotice> flowNodeNotices,List<ActMyNodeCode> actMyNodeCodes) {
        //2.处理流程与form表单数据
        String uuid = BaseUtils.UUIDGenerator();
        flowModel.setUuid(uuid);
        flowModel.setProcdefId(procdefId);
        int result = flowModelService.insertSelective(getSaveData(flowModel));
        //2.2 更新表单设计表数据
        //   Integer integer = flowModelService.updateFormFieldJson(flowModelDto.getFormTableName(), flowModelDto.getFormJson(), flowModelDto.getFormModel());
        //3.保存按钮数据
        formBtnLists.forEach(flowNodeButton -> {
                    flowNodeButton.setUuid(BaseUtils.UUIDGenerator());
                    flowNodeButton.setProcdefId(procdefId);
                }
        );

        if(DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)){
            flowNodeButtonService.insertListOracle(formBtnLists);
        }else{
            flowNodeButtonService.insertListMySql(formBtnLists);
        }

        //4.复制流程节点字段xx
        if (null != flowNodeField && flowNodeField.size() > 0) {
            flowNodeField.forEach(flowNodeField1 -> {
                        flowNodeField1.setUuid(BaseUtils.UUIDGenerator());
                        flowNodeField1.setProcdefId(procdefId);
                    }
            );
            Integer integer1 = flowNodeFieldService.insertList(flowNodeField);
        }

        //5.复制流程节点通知信息
        if (null != flowNodeNotices && flowNodeNotices.size() > 0) {
            flowNodeNotices.forEach(flowNodeNotice -> {
                        flowNodeNotice.setUuid(BaseUtils.UUIDGenerator());
                        flowNodeNotice.setProcdefId(procdefId);
                    }
            );
            flowNodeNoticeService.insertList(flowNodeNotices);
        }

        //6.复制节点权限码信息
        if (null != actMyNodeCodes && actMyNodeCodes.size() > 0) {
            actMyNodeCodes.forEach(actMyNodeCode -> {
                actMyNodeCode.setUuid(BaseUtils.UUIDGenerator());
                actMyNodeCode.setProcdefId(procdefId);
                    }
            );
            actMyNodeCodeService.insertList(actMyNodeCodes);
        }
    }
    public void updateCopyModels(ActMyModel flowModel, String procdefId, List<ActMyNodeButton> formBtnLists, List<ActMyNodeField> flowNodeField, List<ActMyNodeNotice> flowNodeNotices,List<ActMyNodeCode> actMyNodeCodes) {
        //2.处理流程与form表单数据
        flowModel.setProcdefId(procdefId);
        flowModelService.updateByPrimaryKeySelective(getModifyData(flowModel));
        //2.2 更新表单设计表数据
        //   Integer integer = flowModelService.updateFormFieldJson(flowModelDto.getFormTableName(), flowModelDto.getFormJson(), flowModelDto.getFormModel());
        //3. 更新按钮数据procdefId
        formBtnLists.forEach(flowNodeButton -> {
                    flowNodeButton.setProcdefId(procdefId);
            flowNodeButtonService.updateByPrimaryKeySelective(flowNodeButton);
                }
        );

//        if(DriverClassName.ORACLE_NAME.equals(driverClassName)){
//            flowNodeButtonService.insertListOracle(formBtnLists);
//        }else{
//            flowNodeButtonService.insertListMySql(formBtnLists);
//        }

        //4.更新流程节点字段数据procdefId
        if (null != flowNodeField && flowNodeField.size() > 0) {
            flowNodeField.forEach(flowNodeField1 -> {
                        flowNodeField1.setProcdefId(procdefId);
                flowNodeFieldService.updateByPrimaryKeySelective(flowNodeField1);
                    }
            );
        }

        //5.更新流程节点通知信息
        if (null != flowNodeNotices && flowNodeNotices.size() > 0) {
            flowNodeNotices.forEach(flowNodeNotice -> {
                        flowNodeNotice.setProcdefId(procdefId);
                flowNodeNoticeService.updateByPrimaryKeySelective(flowNodeNotice);
                    }
            );
//            flowNodeNoticeService.insertList(flowNodeNotices);
        }

        //6.复制节点权限码信息
        if (null != actMyNodeCodes && actMyNodeCodes.size() > 0) {
            actMyNodeCodes.forEach(actMyNodeCode -> {
                        actMyNodeCode.setProcdefId(procdefId);
                actMyNodeCodeService.updateProDefByParam(procdefId,actMyNodeCode.getUuid());
                    }
            );
        }
    }

    @Autowired
    private RuntimeService runtimeService;


    @Autowired
    private HistoryService historyService;

    @Autowired
    private WorkflowService workflowService;

    @Override
    public List<TaskVo> moveUserTaskUpper(String procdefId, String nodeId, String processInstanceId) {
        List<TaskVo> taskVos = new ArrayList<>();

        BpmnModel bpmnModel = repositoryService.getBpmnModel(procdefId);
        // 获得所有流程定的节点信息
        List<org.flowable.bpmn.model.Process> processes = bpmnModel.getProcesses();
        for (int i = 0; i < processes.size(); i++) {
            List<UserTask> userTasks = processes.get(i).findFlowElementsOfType(UserTask.class);
            for (int u = 0; u < userTasks.size(); u++) {
                if (userTasks.get(u).getId().equals(nodeId)) {
                    List<CommentBean> commentBean = returnComment(nodeId, processInstanceId);
                    TaskVo taskVo = null;
                    for (CommentBean commentBean1 : commentBean) {
                        taskVo = new TaskVo();
                        taskVo.setTaskName(userTasks.get(u).getName());
                        String assignee = commentBean1.getUserName() + "(" + commentBean1.getComment().getUserId() + ")";
                        //查询流程发起人
                        HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();//获取发起人
                        //# 特殊处理 处理人 1.可能为启动人 2.可能位候用户 候选组
                        if ("${startUserNameId}".equals(userTasks.get(u).getAssignee())) {
                            assignee = "(" + commentBean1.getUserName() + ")" +  processInstance.getStartUserId();
                        }
                        taskVo.setMessage(commentBean1.getComment().getMessage());
                        taskVo.setStartTime(commentBean1.getComment().getTime());
                        taskVo.setAssignee(assignee);
                        taskVos.add(taskVo);
                    }
                    return taskVos;
                }
            }
        }
        return null;
    }

    //根据节点id查询其审批意见
    public List<CommentBean> returnComment(String nodeId, String processInstanceId) {
        List<CommentBean> commentBeansList = new ArrayList<>();
        //根据实例id查询审批意见
        List<CommentBean> commentBeans = workflowService.getCommentListByProcessInstanceId(processInstanceId);
        for (int i = 0; i < commentBeans.size(); i++) {
            //# event判断是处理并行时
            if (nodeId.equals(commentBeans.get(i).getActivityId()) && !"event".equals(commentBeans.get(i).getComment().getType())) {
                commentBeansList.add(commentBeans.get(i));
            }
        }
        return commentBeansList;
    }

    @Override
    public Object submitProcess() {


        return null;

    }

    //验证模板
    protected BpmnXMLConverter bpmnXmlConverter = new BpmnXMLConverter();

    @Override
    public Object validatorModel(String modelXml) {
        if (StringUtils.isBlank(modelXml)) {
            return failure("modelXml不能为空，请检查!!!");
        }
        InputStream inputStream = new ByteArrayInputStream(modelXml.getBytes());
        XMLInputFactory xif = XmlUtil.createSafeXmlInputFactory();
        InputStreamReader xmlIn = null;
        XMLStreamReader xtr = null;
        try {
            xmlIn = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
            xtr = xif.createXMLStreamReader(xmlIn);
            // 把XML转换成BpmnModel对象
            BpmnModel bpmnModel = bpmnXmlConverter.convertToBpmnModel(xtr);
            //模板验证
            ProcessValidator validator = new ProcessValidatorFactory().createDefaultProcessValidator();
            List<ValidationError> errors = validator.validate(bpmnModel);
            if (errors.isEmpty()) {
                return success("验证模板");
            }
            return failure("验证模板失败");
        } catch (XMLStreamException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Integer updateProcessModelTypeById(String id, String processModelType) {
        return apiFlowableModelMapper.updateProcessModelTypeById(id, processModelType);
    }

    @Override
    public PageSet<ActDeModelVo> getModleList(PageParam pageParam, String modelName, String modelKey, String modelType) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<ActDeModelVo> list;
        list = apiFlowableModelMapper.getPageSet(modelName, modelKey, modelType);
        PageInfo<ActDeModelVo> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

}
