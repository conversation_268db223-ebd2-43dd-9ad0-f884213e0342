package com.lms.examine.coursewareexamine.service;

import com.lms.common.service.BaseService;
import com.lms.examine.feign.model.Coursewareexamine;

import java.util.List;

public interface CoursewareexamineService extends BaseService<Coursewareexamine> {

    int existSeqno(Coursewareexamine coursewareexamine);

    int getMaxExamSecondByCourseware(Coursewareexamine coursewareexamine);

    int getMinExamSecondByCourseware(Coursewareexamine coursewareexamine);

    List<Coursewareexamine> getByCourseware(String coursewareid);
}
