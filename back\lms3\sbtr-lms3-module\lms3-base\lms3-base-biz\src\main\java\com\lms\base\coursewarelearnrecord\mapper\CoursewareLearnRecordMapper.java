package com.lms.base.coursewarelearnrecord.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.base.feign.model.CoursewareLearnRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

@Mapper
public interface CoursewareLearnRecordMapper extends BaseMapper<CoursewareLearnRecord> {

    @Select("${sql}")
    Map getMinCoursewareByCourse(@Param("sql") String hql);

}
