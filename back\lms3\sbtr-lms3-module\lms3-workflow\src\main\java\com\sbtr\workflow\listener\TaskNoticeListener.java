package com.sbtr.workflow.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.sbtr.workflow.client.MessageClient;
import com.sbtr.workflow.client.MdataClient;
import com.sbtr.workflow.client.SystemClient;
import com.sbtr.workflow.model.ActMyNodeNotice;
import com.sbtr.workflow.service.ActMyNodeNoticeService;
import com.sbtr.workflow.service.ApiFlowableProcessDefinitionService;
import com.sbtr.workflow.service.BusinessSystemDataService;
import com.sbtr.workflow.vo.ProcessDefinitionVo;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.identitylink.api.IdentityLinkType;
import org.flowable.task.service.delegate.DelegateTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.annotation.Resource;
import java.util.*;
//第一：任务监听有三种事件类型
//1. create ：在任务创建且所有任务属性设置完成之后才触发。
//2.assignment ：在任务被分配给某个班里人之后触发，它是在create事件触发前被触发。
//3.complete：在配置了监听器的上一个任务完成是触发，也就是说运行期任务删除之前触发。
//第二：任务监听器的三种监听器执行类型
//1.class：需要类的全路径
//2.expression：定义一个表达式，类似EL的语法
//delegateExpression：指的是一个实现监听接口

/**
 * @MethodName
 * @Description 通用消息通知监听器
 * @Description 使用消息通知监听器注意事项  1、必须是任务节点  2、任务节点上必须配置消息类型 2、必须使用任务监听器  事件:assignment 类型:类   java类名:com.sbtr.workflow.listener.TaskNoticeListener
 * @Param null
 * @Return
 * <AUTHOR> @ sbtr.com>
 * @Date 2020-12-10 13:39
 */
public class TaskNoticeListener implements TaskListener {

    private static final Logger log = LoggerFactory.getLogger(TaskNoticeListener.class);
    @Resource
    private BusinessSystemDataService businessSystemDataService;

    @Override
    public void notify(DelegateTask delegateTask) {

        //修改开关
        //得到servletAPI
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        WebApplicationContext ctx = WebApplicationContextUtils.getWebApplicationContext(requestAttributes.getRequest().getServletContext());
        ActMyNodeNoticeService flowNodeNoticeService = ctx.getBean(ActMyNodeNoticeService.class);
        ApiFlowableProcessDefinitionService apiFlowableProcessDefinitionService = ctx.getBean(ApiFlowableProcessDefinitionService.class);
        RuntimeService runtimeService = ctx.getBean(RuntimeService.class);
        TaskService taskService = ctx.getBean(TaskService.class);


        //去流程定义表查询模型key
        ProcessDefinitionVo processDefinitionVo = apiFlowableProcessDefinitionService.getProdefIdById(delegateTask.getProcessDefinitionId());

        //根据节点Id，模型key，流程定义id去查找
        List<ActMyNodeNotice> list = flowNodeNoticeService.getListByNodeIdAndModelKeyAndProcdefId(delegateTask.getTaskDefinitionKey(), processDefinitionVo.getModelKey(), delegateTask.getProcessDefinitionId());
        if (list.size() > 0) {
            //查询流程发起人
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(delegateTask.getProcessInstanceId()).singleResult();
            if (ObjectUtil.isNull(processInstance) || StrUtil.isBlank(processInstance.getStartUserId())) {
                if (!"发起人".equals(delegateTask.getName())) {
                    return;
                }
            }
            log.info("流程发起人:{}", "发起人".equals(delegateTask.getName()) ? delegateTask.getAssignee() : processInstance.getStartUserId());
            //根据userNameId查询详情
//            Map<String, Object> user = new HashMap<>();
//            try {
//                user = businessSystemDataService.getUserByUserNameId("发起人".equals(delegateTask.getName()) ? delegateTask.getAssignee() : processInstance.getStartUserId());
//            } catch (Exception e) {
//                return;
//            }
            //获取节点处理人 得考虑用户组 候选用户
            String assignee = delegateTask.getAssignee();
            log.info("assignee人:{}", assignee);
            if (StrUtil.isBlank(assignee)) {
                String taskId = delegateTask.getId();
                //获取接口带出来带候选人与候选组信息
                List<String> originalCandidateUsers = new ArrayList<>();
                List<String> originalCandidateGroups = new ArrayList<>();
                //获取实时候选信息做同步操作
                List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(taskId);
                for (IdentityLink identityLink : identityLinks) {
                    if (!IdentityLinkType.CANDIDATE.equalsIgnoreCase(identityLink.getType())) {
                        continue;
                    }
                    //不存在候选信息则同步添加
                    if (StrUtil.isNotBlank(identityLink.getUserId()) && !originalCandidateUsers.contains(identityLink.getUserId())) {
                        originalCandidateUsers.add(identityLink.getUserId());
                    } else if (StrUtil.isNotBlank(identityLink.getGroupId()) && !originalCandidateGroups.contains(identityLink.getGroupId())) {
                        originalCandidateGroups.add(identityLink.getGroupId());
                    }
                }

                //候选用户
                if (StrUtil.isBlank(assignee) && CollUtil.isEmpty(originalCandidateGroups)) {
                    if (CollUtil.isNotEmpty(originalCandidateUsers)) {
                        assignee = String.join(",", originalCandidateUsers);
                    }
                }

                //候选组
                if (StrUtil.isBlank(assignee) && CollUtil.isEmpty(originalCandidateUsers)) {
                    if (CollUtil.isNotEmpty(originalCandidateGroups)) {
                        try {
                            //角色
                            assignee = businessSystemDataService.getUserNameIdByRoleId(String.join(",", originalCandidateGroups));
                            //岗位
                            if (StrUtil.isBlank(assignee)) {
                                assignee = businessSystemDataService.getUserNameIdByPost(String.join(",", originalCandidateGroups));
                            }
                        } catch (Exception e) {
                            assignee = "";
                        }

                    }
                }
            }

            if (StrUtil.isNotBlank(assignee)) {
                String[] str = assignee.split(",");
                for (int j = 0; j < str.length; j++) {
                    try {
//                        Map<String, Object> assigneeUserInfo = businessSystemDataService.getUserByUserNameId(str[j]);
                        String type = "complete".equals(delegateTask.getEventName()) ? "完成了" : "发起了";
                        for (int i = 0; i < list.size(); i++) {
                            if ("note".equals(list.get(i).getNoticeName())) {
//                                save(user, delegateTask, messageClient, processDefinitionVo, type, assigneeUserInfo);
                            }
                        }
                    } catch (Exception e) {
                        log.info(e.getMessage());
                    }

                }
            }


        }

    }

    //微信通知
    private void sendWechat(SystemClient systemClient, Map<String, Object> user, ProcessDefinitionVo processDefinitionVo,
                            String type, Map<String, Object> assigneeUserInfo) {
        //根据user_uuid查询  需要第三方关联表的 标识 比如企业微信的表 FengKai
        Map<String, Object> userUuid = systemClient.getDetailByUserUuid(Convert.toStr(assigneeUserInfo.get("uuid")), "qyweixin");
        if (MapUtil.isNotEmpty(userUuid)) {
            log.info("发送微信通知给:{}", Convert.toStr(userUuid.get("unionid")));
            try {
                //开始发送企业微信消息
                systemClient.sendText(Convert.toStr(userUuid.get("unionid")), null, null, "您有一条待办流程，请先进行处理！！！");
            } catch (Exception e) {
                log.error("发送企业微信消息失败:{}", e.getMessage());
            }
        } else {
            log.error("账号:{}", Convert.toStr(assigneeUserInfo.get("userName")) + "/" + Convert.toStr(assigneeUserInfo.get("userNameId")) + " 在第三方关联表找不到标识");
        }

    }

    //发送邮件
    private void sendSimpleMail(Map<String, Object> user, DelegateTask delegateTask,
                                MessageClient messageClient, ProcessDefinitionVo processDefinitionVo,
                                String type, Map<String, Object> assigneeUserInfo) {
        //获取用户详情数据
        Map<String, Object> map = new HashMap<>();
        log.info("发送邮件给:{}", Convert.toStr(assigneeUserInfo.get("email")));
        map.put("toMail", Convert.toStr(assigneeUserInfo.get("email")));
        map.put("subject", "流程审批通知");
        map.put("content", Convert.toStr(user.get("userName")) + "(" + Convert.toStr(user.get("userNameId")) + ")" + type + processDefinitionVo.getName() + "流程,请前去处理！！！");
        try {
            Object object = messageClient.sendSimpleMail(map);
            if (((HashMap) object).get("statusCode").equals(300)) {
                log.error("发送邮件失败:{}", ((HashMap) object).get("message"));
            } else {
                log.info("发送邮件用成功:{}", ((HashMap) object).get("message"));
            }
        } catch (Exception e) {
            log.error("发送邮件失败:{}", e.getMessage());
        }
    }

    //发送短信   注意签名和模板code请替换为自己公司申请 详情请查看第三方文档配置
    private void sendSms(Map<String, Object> user, DelegateTask delegateTask, MessageClient messageClient, ProcessDefinitionVo processDefinitionVo, String type, Map<String, Object> assigneeUserInfo) {
        String message = "{businessStatus:'" + Convert.toStr(user.get("userName")) + "(" + Convert.toStr(user.get("userNameId")) + ")" + "发起流程！！！" + "'}";
        log.info("发送短信给:{}", Convert.toStr(assigneeUserInfo.get("cellphone")));
        try {
            Object object = messageClient.sendSms(
                    Convert.toStr(assigneeUserInfo.get("cellphone")),
                    "广州赛宝腾睿信息科技有限公司",
                    "SMS_162220712",
                    message,
                    delegateTask.getAssignee()
            );
            if (ObjectUtil.isNotNull(object)) {
                if (((HashMap) object).get("statusCode").equals(300)) {
                    log.error("发送短信调用失败:{}", ((HashMap) object).get("message"));
                } else {
                    log.info("发送短信调用成功:{}", ((HashMap) object).get("message"));
                }
            }
        } catch (Exception e) {
            log.error("发送短信调用失败:{}", e.getMessage());
        }
    }

    //站内信
    private void save(Map<String, Object> user, DelegateTask delegateTask, MessageClient messageClient, ProcessDefinitionVo processDefinitionVo, String type, Map<String, Object> assigneeUserInfo) {
        Map<String, Object> map = new HashMap<>();
        map.put("title", "流程审批通知");
        map.put("type", "流程审批");
        map.put("description", "流程审批");
        log.info("发送站内信给:{}", Convert.toStr(assigneeUserInfo.get("userNameId")));
        map.put("receiverId", Convert.toStr(assigneeUserInfo.get("userNameId")));
        map.put("url", 1);
        map.put("content", Convert.toStr(user.get("userName")) + "(" + Convert.toStr(user.get("userNameId")) + ")" + type + processDefinitionVo.getName() + "流程,请前去处理！！！");
        try {
            Object object = messageClient.save(map);
            if (ObjectUtil.isNotNull(object)) {
                if (((HashMap) object).get("statusCode").equals(300)) {
                    log.error("站内信调用失败:{}", ((HashMap) object).get("message"));
                } else {
                    log.info("站内信调用成功:{}", ((HashMap) object).get("message"));
                }
            }
        } catch (Exception e) {
            log.error("站内信调用失败:{}", e.getMessage());
        }
    }


}
