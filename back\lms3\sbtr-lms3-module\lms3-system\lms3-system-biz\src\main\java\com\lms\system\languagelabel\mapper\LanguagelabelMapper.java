
package com.lms.system.languagelabel.mapper;


import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.system.feign.model.Languagelabel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface LanguagelabelMapper extends BaseMapper<Languagelabel> {

    @Select(value = "select * from b_languagelabel l where l.keyword = #{keword} and if( #{language} ='' or #{language} is null,1=1,l.language=#{language})")
    List<Languagelabel> getByKeword(@Param("keword") String keword, @Param("language") String language);

    @Select("select * from b_languagelabel l where l.language = #{language}")
    List<Languagelabel> listAllByLanguage(String language);

    @Select(value = "select * from b_selectitem where typeid=#{typeId}")
    List<Map> getSelectitemsByTypeId(String typeId);
}
