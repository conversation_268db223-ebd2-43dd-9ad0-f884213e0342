package com.lms.examine.feign.api;

import com.lms.common.config.FeignConfig;
import com.lms.common.model.Result;
import com.lms.examine.feign.fallback.CoursewareexamineApiFallbackFactory;
import com.lms.examine.feign.model.Coursewareexamine;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@FeignClient(contextId = "coursewareexamineApi", value = "lms-examine", path = "/coursewareexamine", configuration = FeignConfig.class, fallbackFactory = CoursewareexamineApiFallbackFactory.class)
public interface CoursewareexamineApi {

    @RequestMapping(value = {"/getByCourseware/{id}"}, method = RequestMethod.GET)
    Result<List<Coursewareexamine>> getByCourseware(@PathVariable("id") String coursewareid);

    @PostMapping("/save")
    Result save(@RequestBody Coursewareexamine newexamine);

}
