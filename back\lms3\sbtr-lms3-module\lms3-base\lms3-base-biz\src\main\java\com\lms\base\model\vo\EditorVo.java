package com.lms.base.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "编辑器调用模型保存或更新数据")
public class EditorVo {

    @ApiModelProperty(value = "模型id")
    private String id;

    @ApiModelProperty(value = "模型名称")
    private String name;

    @ApiModelProperty(value = "子模型")
    private List<EditorVo> _children;

}
