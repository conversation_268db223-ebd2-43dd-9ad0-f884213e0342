package com.sbtr.workflow.utils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class OkHttpClientUtil {

    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private static final MediaType XML = MediaType.parse("application/xml; charset=utf-8");

    @Resource
    private OkHttpClient okHttpClient;

    public String doGet(String url) {
        return doGet(url, null, null);
    }

    public byte[] doGetByte(String url) {
        return doGetByte(url, null, null);
    }

    public String doGetToParams(String url, Map<String, String> params) {
        return doGet(url, params, null);
    }

    public String doGetToHeaders(String url, Map<String, String> headers) {
        return doGet(url, null, headers);
    }

    public String doGet(String url, Map<String, String> params, Map<String, String> headers) {
        Request request = getBefore(url, params, headers);
        return executeBody(request);
    }

    public byte[] doGetByte(String url, Map<String, String> params, Map<String, String> headers) {
        Request request = getBefore(url, params, headers);
        return executeByte(request);
    }

    private Request getBefore(String url, Map<String, String> params, Map<String, String> headers) {
        StringBuilder sb = new StringBuilder(url);
        if (MapUtils.isNotEmpty(params)) {
            boolean firstFlag = true;
            for (String k : params.keySet()) {
                String v = params.get(k);
                if (firstFlag) {
                    sb.append("?").append(k).append("=").append(v);
                    firstFlag = false;
                } else {
                    sb.append("&").append(k).append("=").append(v);
                }
            }
        }
        Request.Builder builder = new Request.Builder();
        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach(builder::addHeader);
        }
        return builder.url(sb.toString()).build();
    }


    public String doPost(String url) {
        return executeBody(new Request.Builder().url(url).post(new FormBody.Builder().build()).build());
    }


    public String doPostForm(String url, Map<String, String> params){
        FormBody.Builder builder = new FormBody.Builder();
        if (MapUtils.isNotEmpty(params)) {
            params.forEach(builder::add);
        }
        Request request = new Request.Builder().url(url).post(builder.build()).build();
        return executeBody(request);
    }

    public String doPostForm(String url, Map<String, String> params, Map<String, String> headers){
        FormBody.Builder builder = new FormBody.Builder();
        if (MapUtils.isNotEmpty(params)) {
            params.forEach(builder::add);
        }
        Request.Builder requestBuilder = new Request.Builder();
        if (MapUtils.isNotEmpty(headers)){
            headers.forEach(requestBuilder::addHeader);
        }
        Request request = requestBuilder.url(url).post(builder.build()).build();
        return executeBody(request);
    }

    public String doPostJson(String url, String json){
        return executePost(url, json, JSON);
    }

    public String doPostJson(String url, Object jsonObject){
        String json = com.alibaba.fastjson.JSON.toJSONString(jsonObject);
        Request request =  new Request.Builder().url(url).post(RequestBody.create(json, JSON)).build();
        return executeBody(request);
    }

    public String doPostJson(String url, Object jsonObject, Map<String, String> headers){
        String json = com.alibaba.fastjson.JSON.toJSONString(jsonObject);
        RequestBody requestBody = RequestBody.create(json, JSON);
        Request.Builder requestBuilder = new Request.Builder();
        if (MapUtils.isNotEmpty(headers)){
            headers.forEach(requestBuilder::addHeader);
        }
        Request request = requestBuilder.url(url).post(requestBody).build();
        return executeBody(request);
    }

    public String doPostJson(String url, String json, Map<String, String> headers){
        RequestBody requestBody = RequestBody.create(json, JSON);
        Request.Builder requestBuilder = new Request.Builder();
        if (MapUtils.isNotEmpty(headers)){
            headers.forEach(requestBuilder::addHeader);
        }
        Request request = requestBuilder.url(url).post(requestBody).build();
        return executeBody(request);
    }

    public String doPostXml(String url, String xml){
        return executePost(url, xml, XML);
    }

    private String executePost(String url, String data, MediaType contentType){
        RequestBody requestBody = RequestBody.create(data, contentType);
        Request request = new Request.Builder().url(url).post(requestBody).build();
        return executeBody(request);
    }


    private String executeBody(Request request){
        try(Response response = okHttpClient.newCall(request).execute()){
            if (response.body() != null){
                return Objects.requireNonNull(response.body()).string();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    private byte[] executeByte(Request request){
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.body() != null) {
                return Objects.requireNonNull(response.body()).bytes();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

}
