import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";
const config = {
  headers: {
    "Content-Type": undefined // 删除默认的application/json，让浏览器自动设置multipart/form-data
  }
};
// 新增课件流程图
export const saveFlowchart = (data?: any) => {
  // 如果是FormData，需要特殊处理Content-Type
  return http.request<any>("post", baseUrlApi("courseware/saveFlowchart"), {
    data,
    ...config
  });
};

// 修改课件流程图
export const updateFlowchart = (data?: any) => {
  return http.request<any>("post", baseUrlApi("courseware/updateFlowchart"), {
    data,
    ...config
  });
};

// 根据课件id获取课件流程图
export const getFlowchart = (data?: any) => {
  return http.request<any>(
    "get",
    baseUrlApi("courseware/getFlowchart?id=") + data
  );
};
