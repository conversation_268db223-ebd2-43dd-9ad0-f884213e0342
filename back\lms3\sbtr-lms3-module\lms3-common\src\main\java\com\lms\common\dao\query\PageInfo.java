/**
 * FileName:	Page.java
 * Author:		zhouweirong
 * Time:		2013-4-1  下午2:46:36
 * CopyRight: 	SBTR  LTD.
 * Description:	to-do
 */
package com.lms.common.dao.query;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 */
public class PageInfo implements Serializable {
	public static final String DESC = "desc";
	public static final String ASC = "asc";

	private int pageSize;
	private int pageIndex;
	private List<Parameter> parameters;
	private String orderName;// orderMap不为空时有效
	private String sort;// orderMap、orderName不为空才有效
	private Map orderMap;// 排序优化,不为空用该值
	private int total;

	public PageInfo() {
		pageSize = 20;
		pageIndex = 1;
		total = 0;
		parameters = new ArrayList<Parameter>();
		orderName = null;
		sort = null;
		orderMap = null;
	}

	public PageInfo(int size, int index) {
		pageSize = size;
		pageIndex = index;
		total = 0;
		parameters = new ArrayList<Parameter>();
		orderName = null;
		sort = null;
		orderMap = null;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public int getPageIndex() {
		return pageIndex;
	}

	public void setPageIndex(int pageIndex) {
		this.pageIndex = pageIndex;
	}

	public List<Parameter> getParameters() {
		return parameters;
	}

	public void setParameters(List<Parameter> parameters) {
		this.parameters = parameters;
	}

	public String getOrderName() {
		return orderName;
	}

	public void setOrderName(String orderName) {
		this.orderName = orderName;
	}

	public String getSort() {
		return sort;
	}

	public void setSort(String sort) {
		this.sort = sort;
	}

	public Map getOrderMap() {
		return orderMap;
	}

	public void setOrderMap(Map orderMap) {
		this.orderMap = orderMap;
	}

	public int getTotal() {
		return total;
	}

	public void setTotal(int total) {
		this.total = total;
	}
}
