package com.lms.common.config;


import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.lms.common.filter.JwtFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.util.List;

@Configuration
public class WebFilterConfig implements WebMvcConfigurer {

    @Resource
    private LMSConfiguration lmsConfiguration;

    @Bean
    public JwtFilter getJwtFilter(){return new JwtFilter();}

    /**
     * 注册JWT拦截器，可以在配置类中，也可以直接在SpringBoot的入口类中
     * @return FilterRegistrationBean
     */
    @Bean
    public FilterRegistrationBean JwtFilter() {
        final FilterRegistrationBean registrationBean = new FilterRegistrationBean();
        registrationBean.setFilter(getJwtFilter());
        //添加需要拦截的url
        List<String> urlPatterns = Lists.newArrayList();
        urlPatterns.add("*");
        registrationBean.addUrlPatterns(urlPatterns.toArray(new String[urlPatterns.size()]));
        return registrationBean;
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler(lmsConfiguration.getViewurl())
                .addResourceLocations("file:" + lmsConfiguration.getStoragepath())
                .addResourceLocations("file:" + lmsConfiguration.getScormStoragePath());
    }
}

