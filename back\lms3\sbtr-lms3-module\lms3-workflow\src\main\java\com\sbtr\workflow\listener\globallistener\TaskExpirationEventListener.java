package com.sbtr.workflow.listener.globallistener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sbtr.workflow.client.MdataClient;
import com.sbtr.workflow.client.MessageClient;
import com.sbtr.workflow.client.SystemClient;
import com.sbtr.workflow.enums.FlowEnum;
import com.sbtr.workflow.model.ActMyNodeNotice;
import com.sbtr.workflow.service.ActMyNodeNoticeService;
import org.flowable.common.engine.api.delegate.event.*;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.identitylink.api.IdentityLinkType;
import org.flowable.job.service.impl.persistence.entity.JobEntityImpl;
import org.flowable.task.api.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务过期事件监听器
 *
 * <AUTHOR>
 * @Version 5.5.0
 * @Email <EMAIL>
 * @Date 2023-08-17 14:57:04
 */
@Component
public class TaskExpirationEventListener implements FlowableEventListener {

    private final ApplicationContext applicationContext;

    public TaskExpirationEventListener(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    private static final Logger log = LoggerFactory.getLogger(TaskExpirationEventListener.class);

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private ActMyNodeNoticeService flowNodeNoticeService;

    @Override
    public void onEvent(FlowableEvent event) {
        if (event.getType().equals(FlowableEngineEventType.TIMER_FIRED)) {
            FlowableEntityEvent entityEvent = (FlowableEntityEvent) event;
            Object entity = entityEvent.getEntity();
            String processDefinitionId = ((JobEntityImpl) entity).getProcessDefinitionId();
            String jobHandlerConfiguration = ((JobEntityImpl) entity).getJobHandlerConfiguration();
            String processInstanceId = ((JobEntityImpl) entity).getProcessInstanceId();
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();
            String modelKey = processInstance.getProcessDefinitionKey();
            JSONObject jsonObject = JSONObject.parseObject(jobHandlerConfiguration);
            List<ActMyNodeNotice> list = flowNodeNoticeService.getListByNodeIdAndModelKeyAndProcdefId(
                    jsonObject.getString("activityId"),
                    modelKey,
                    processDefinitionId);
            List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
            handleExpiredTask(list, tasks.get(0));
        }
    }

    private void handleExpiredTask(List<ActMyNodeNotice> actMyNodeNotices,
                                   Task taskEntity

    ) {

        //得到servletAPI
        MdataClient userClient = applicationContext.getBean(MdataClient.class);
        List<ActMyNodeNotice> list = new ArrayList<>();
        for (int i = 0; i < actMyNodeNotices.size(); i++) {
            if (actMyNodeNotices.get(i).getNoticeName().contains("timerfired.")) {
                list.add(actMyNodeNotices.get(i));
            }
        }
        for (int h = 0; h < list.size(); h++) {
            if (list.size() > 0) {
                //查询流程发起人
                ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(taskEntity.getProcessInstanceId()).singleResult();
                if (ObjectUtil.isNull(processInstance) || StrUtil.isBlank(processInstance.getStartUserId())) {
                    if (!"发起人".equals(taskEntity.getName())) {
                        return;
                    }
                }
                log.info("流程发起人:{}", "发起人".equals(taskEntity.getName()) ? taskEntity.getAssignee() : processInstance.getStartUserId());
                //根据userNameId查询详情
                Map<String, Object> user = new HashMap<>();
                try {
                    user = userClient.getUserByUserNameId("发起人".equals(taskEntity.getName()) ? taskEntity.getAssignee() : processInstance.getStartUserId());
                } catch (Exception e) {
                    return;
                }
                //获取节点处理人 得考虑用户组 候选用户
                String assignee = taskEntity.getAssignee();
                log.info("assignee人:{}", assignee);
                if (StrUtil.isBlank(assignee)) {
                    String taskId = taskEntity.getId();
                    //获取接口带出来带候选人与候选组信息
                    List<String> originalCandidateUsers = new ArrayList<>();
                    List<String> originalCandidateGroups = new ArrayList<>();
                    //获取实时候选信息做同步操作
                    List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(taskId);
                    for (IdentityLink identityLink : identityLinks) {
                        if (!IdentityLinkType.CANDIDATE.equalsIgnoreCase(identityLink.getType())) {
                            continue;
                        }
                        //不存在候选信息则同步添加
                        if (StrUtil.isNotBlank(identityLink.getUserId()) && !originalCandidateUsers.contains(identityLink.getUserId())) {
                            originalCandidateUsers.add(identityLink.getUserId());
                        } else if (StrUtil.isNotBlank(identityLink.getGroupId()) && !originalCandidateGroups.contains(identityLink.getGroupId())) {
                            originalCandidateGroups.add(identityLink.getGroupId());
                        }
                    }

                    //候选用户
                    if (StrUtil.isBlank(assignee) && CollUtil.isEmpty(originalCandidateGroups)) {
                        if (CollUtil.isNotEmpty(originalCandidateUsers)) {
                            assignee = String.join(",", originalCandidateUsers);
                        }
                    }

                    //候选组
                    if (StrUtil.isBlank(assignee) && CollUtil.isEmpty(originalCandidateUsers)) {
                        if (CollUtil.isNotEmpty(originalCandidateGroups)) {
                            try {
                                //角色
                                assignee = userClient.getUserNameIdByRoleId(String.join(",", originalCandidateGroups));
                                //岗位
                                if (StrUtil.isBlank(assignee)) {
                                    assignee = userClient.getUserNameIdByPost(String.join(",", originalCandidateGroups));
                                }
                            } catch (Exception e) {
                                assignee = "";
                            }

                        }
                    }
                }
                if (StrUtil.isNotBlank(assignee)) {
                    String[] str = assignee.split(",");
                    for (int j = 0; j < str.length; j++) {
                        try {
                            Map<String, Object> assigneeUserInfo = userClient.getUserByUserNameId(str[j]);
                            if ("timerfired.note".equals(list.get(h).getNoticeName())) {
                                save(user, assignee, processInstance, null, assigneeUserInfo);
                            } else if ("timerfired.mail".equals(list.get(h).getNoticeName())) {
                                sendSimpleMail(user, assignee, processInstance, null, assigneeUserInfo);
                            } else if ("timerfired.message".equals(list.get(h).getNoticeName())) {
                                sendSms(user, assignee, processInstance, null, assigneeUserInfo);
                            } else if ("timerfired.wechat".equals(list.get(h).getNoticeName())) {
                                sendWechat(user, processInstance, null, assigneeUserInfo);
                            }
                        } catch (Exception e) {
                            log.info(e.getMessage());
                        }

                    }
                }
            }

            if ("timerfired.autoApproval".equals(list.get(h).getNoticeName())) {
                taskService.addComment(taskEntity.getId(), taskEntity.getProcessInstanceId(), FlowEnum.ZDSP.toString(), "同意");
                taskService.complete(taskEntity.getId());
            }


        }

    }

    @Override
    public boolean isFailOnException() {
        return false;
    }

    @Override
    public boolean isFireOnTransactionLifecycleEvent() {
        return false;
    }

    @Override
    public String getOnTransaction() {
        return null;
    }

    /**
     * 发送微信
     *
     * @param user                用户
     * @param processDefinitionVo 流程定义签证官
     * @param type                类型
     * @param assigneeUserInfo    受让人用户信息
     */
    private void sendWechat(Map<String, Object> user, ProcessInstance processDefinitionVo,
                            String type, Map<String, Object> assigneeUserInfo) {
        SystemClient systemClient = applicationContext.getBean(SystemClient.class);
        //根据user_uuid查询  需要第三方关联表的 标识 比如企业微信的表 FengKai
        String uuid = Convert.toStr(JSONObject.parseObject(JSON.toJSONString(assigneeUserInfo.get("data"))).get("uuid"));
        String userName = Convert.toStr(JSONObject.parseObject(JSON.toJSONString(assigneeUserInfo.get("data"))).get("userName"));
        String userNameId = Convert.toStr(JSONObject.parseObject(JSON.toJSONString(assigneeUserInfo.get("data"))).get("userNameId"));
        Map<String, Object> userUuid = systemClient.getDetailByUserUuid(uuid, "qyweixin");
        if (MapUtil.isNotEmpty(userUuid)) {
            log.info("发送微信通知给:{}", Convert.toStr(userUuid.get("unionid")));
            try {
                //开始发送企业微信消息
                systemClient.sendText(Convert.toStr(userUuid.get("unionid")), null, null,
                        userName + "(" + userNameId + ")" + "您有一条待办流程【" + processDefinitionVo.getName() + "】已超时，请前去进行处理！！！");
            } catch (Exception e) {
                log.error("发送企业微信消息失败:{}", e.getMessage());
            }
        } else {
            log.error("账号:{}", userName + "/" + userNameId + " 在第三方关联表找不到标识");
        }
    }

    /**
     * 发送简单邮件
     *
     * @param user                用户
     * @param
     * @param processDefinitionVo 流程定义签证官
     * @param type                类型
     * @param assigneeUserInfo    受让人用户信息
     */
    private void sendSimpleMail(Map<String, Object> user, String assignee,
                                ProcessInstance processDefinitionVo,
                                String type, Map<String, Object> assigneeUserInfo) {
        //获取用户详情数据
        MessageClient messageClient = applicationContext.getBean(MessageClient.class);
        Map<String, Object> map = new HashMap<>();
        String email = Convert.toStr(JSONObject.parseObject(JSON.toJSONString(assigneeUserInfo.get("data"))).get("userEmail"));
        log.info("发送邮件给:{}", email);
        map.put("toMail", email);
        map.put("subject", "流程待办超时通知");
        String userName = Convert.toStr(JSONObject.parseObject(JSON.toJSONString(user.get("data"))).get("userName"));
        String userNameId = Convert.toStr(JSONObject.parseObject(JSON.toJSONString(user.get("data"))).get("userNameId"));
        map.put("content", userName + "(" + userNameId + ")" + "您有一条待办流程【" + processDefinitionVo.getName() + "】已超时，请前去进行处理！！！");
        try {
            Object object = messageClient.sendSimpleMail(map);
            if (((HashMap) object).get("statusCode").equals(300)) {
                log.error("发送邮件失败:{}", ((HashMap) object).get("message"));
            } else {
                log.info("发送邮件用成功:{}", ((HashMap) object).get("message"));
            }
        } catch (Exception e) {
            log.error("发送邮件失败:{}", e.getMessage());
        }
    }

    /**
     * 发送短信
     * 注意签名和模板code请替换为自己公司申请 详情请查看第三方文档配置
     *
     * @param user                用户
     * @param
     * @param processDefinitionVo 流程定义签证官
     * @param type                类型
     * @param assigneeUserInfo    受让人用户信息
     */
    private void sendSms(Map<String, Object> user, String assignee, ProcessInstance processDefinitionVo, String type, Map<String, Object> assigneeUserInfo) {
        String userName = Convert.toStr(JSONObject.parseObject(JSON.toJSONString(user.get("data"))).get("userName"));
        String userNameId = Convert.toStr(JSONObject.parseObject(JSON.toJSONString(user.get("data"))).get("userNameId"));
        String message = "{businessStatus:'" + userName + "(" + userNameId + ")" + "您有一条待办流程已超时" + "'}";
        String cellphone = Convert.toStr(JSONObject.parseObject(JSON.toJSONString(assigneeUserInfo.get("data"))).get("userCellphone"));
        log.info("发送短信给:{}", cellphone);
        MessageClient messageClient = applicationContext.getBean(MessageClient.class);

        try {
            Object object = messageClient.sendSms(
                    cellphone,
                    "广州赛宝腾睿信息科技有限公司",
                    "SMS_162220712",
                    message,
                    assignee
            );
            if (ObjectUtil.isNotNull(object)) {
                if (((HashMap) object).get("statusCode").equals(300)) {
                    log.error("发送短信调用失败:{}", ((HashMap) object).get("message"));
                } else {
                    log.info("发送短信调用成功:{}", ((HashMap) object).get("message"));
                }
            }
        } catch (Exception e) {
            log.error("发送短信调用失败:{}", e.getMessage());
        }
    }


    /**
     * 站内信
     *
     * @param user                用户
     * @param delegateTask        委托任务
     * @param messageClient       消息客户端
     * @param processDefinitionVo 流程定义签证官
     * @param type                类型
     * @param assigneeUserInfo    受让人用户信息
     */
    private void save(Map<String, Object> user, String delegateTask, ProcessInstance processDefinitionVo, String type, Map<String, Object> assigneeUserInfo) {
        Map<String, Object> map = new HashMap<>();
        map.put("title", "流程审批通知");
        map.put("type", "流程审批");
        map.put("description", "流程审批");
        String toUserNameId = Convert.toStr(JSONObject.parseObject(JSON.toJSONString(assigneeUserInfo.get("data"))).get("userNameId"));
        log.info("发送站内信给:{}", toUserNameId);
        map.put("receiverId", toUserNameId);
        map.put("url", 1);
        String userName = Convert.toStr(JSONObject.parseObject(JSON.toJSONString(user.get("data"))).get("userName"));
        String userNameId = Convert.toStr(JSONObject.parseObject(JSON.toJSONString(user.get("data"))).get("userNameId"));
        map.put("content", userName + "(" + userNameId + ")" + "您有一条待办流程【" + processDefinitionVo.getName() + "】已超时，请前去进行处理！！！");
        MessageClient messageClient = applicationContext.getBean(MessageClient.class);
        try {
            Object object = messageClient.save(map);
            if (ObjectUtil.isNotNull(object)) {
                if (((HashMap) object).get("statusCode").equals(300)) {
                    log.error("站内信调用失败:{}", ((HashMap) object).get("message"));
                } else {
                    log.info("站内信调用成功:{}", ((HashMap) object).get("message"));
                }
            }
        } catch (Exception e) {
            log.error("站内信调用失败:{}", e.getMessage());
        }
    }

}
