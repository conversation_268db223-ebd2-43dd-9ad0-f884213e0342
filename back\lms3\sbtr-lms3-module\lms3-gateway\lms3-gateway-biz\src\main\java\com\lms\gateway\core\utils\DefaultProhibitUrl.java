package com.lms.gateway.core.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * 默认禁止请求地址
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
public class DefaultProhibitUrl {
    private static final List<String> DEFAULT_PROGIBIT_URL = new ArrayList<>();

    static {
        //基础配置
        DEFAULT_PROGIBIT_URL.add("/gateway/config/update");
        DEFAULT_PROGIBIT_URL.add("/gateway/config/delete");

        //菜单管理
        DEFAULT_PROGIBIT_URL.add("/gateway/menu/update");
        DEFAULT_PROGIBIT_URL.add("/gateway/menu/delete");
        DEFAULT_PROGIBIT_URL.add("/gateway/menu/save");

        //用户信息
        DEFAULT_PROGIBIT_URL.add("/mdata/user/delete");
        DEFAULT_PROGIBIT_URL.add("/mdata/user/update");
        DEFAULT_PROGIBIT_URL.add("/mdata/user/save");
        DEFAULT_PROGIBIT_URL.add("/mdata/user/updateModifyPassword");

        //组织机构
        DEFAULT_PROGIBIT_URL.add("/mdata/organization/delete");

        //角色授权
        DEFAULT_PROGIBIT_URL.add("/gateway/authAccess/grant");
        DEFAULT_PROGIBIT_URL.add("/gateway/authAccess/ungrant");

        //门户分类
        DEFAULT_PROGIBIT_URL.add("/gateway/sysPortalClassify/save");
        DEFAULT_PROGIBIT_URL.add("/gateway/sysPortalClassify/update");
        DEFAULT_PROGIBIT_URL.add("/gateway/sysPortalClassify/deleteBatch");
    }


    public static List<String> getDefaultProhibitUrl() {
        return DEFAULT_PROGIBIT_URL;
    }


}
