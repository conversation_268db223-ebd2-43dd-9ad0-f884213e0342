package com.lms.common.dao.query;
import java.math.BigDecimal;
import java.util.Date;

public enum PropertyType {
    S(String.class), I(Integer.class), L(Long.class), DB(Double.class),BD(BigDecimal.class), D(Date.class), B(Boolean.class);

    private Class<?> clazz;

    private PropertyType(Class<?> clazz) {
        this.clazz = clazz;
    }

    public Class<?> getValue() {
        return clazz;
    }
}
