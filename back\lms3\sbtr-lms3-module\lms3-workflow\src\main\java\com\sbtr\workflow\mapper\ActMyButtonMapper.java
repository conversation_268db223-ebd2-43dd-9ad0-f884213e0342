package com.sbtr.workflow.mapper;

import com.sbtr.workflow.bean.WorkflowMapper;
import com.sbtr.workflow.model.ActMyButton;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程按钮表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-23 08:10:59
 */
public interface ActMyButtonMapper extends WorkflowMapper<ActMyButton> {

    int executeDeleteBatch(Object[] var1);

    int getListByCodeAndUuid(@Param("buttonCode") String buttonCode, @Param("uuid") String uuid);
}
