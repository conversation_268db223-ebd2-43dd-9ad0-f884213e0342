package com.sbtr.workflow.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
/**
 * 读取配置文件注释
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-23 07:12:25
 */
@Component
@ConfigurationProperties("my")
public class MyProperties {

    /**
     * host
     */
    private String host;

    /**
     * 端口
     */
    private Integer gatewayPort;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getGatewayPort() {
        return gatewayPort;
    }

    public void setGatewayPort(Integer gatewayPort) {
        this.gatewayPort = gatewayPort;
    }
}
