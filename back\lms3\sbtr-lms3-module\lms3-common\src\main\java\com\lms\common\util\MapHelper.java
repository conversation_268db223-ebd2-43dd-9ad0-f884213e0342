/**
 *
 *
 * SBTR LTD.
 *
 */
package com.lms.common.util;

import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

/**
 * Map常用工具方法类
 */
public final class MapHelper {

	/**
	 * 打印查看Map中键值数据
	 * @param m as Map
	 */
	public static void printMap(Map m){
		Iterator it=m.keySet().iterator();
		Object obj=null;
		System.out.println("查看Map信息：");
		while(it.hasNext()){
			obj=it.next();
			System.out.println(obj+":"+m.get(obj));
		}
	}

	/**
	 * 获取Map中的键，以数组形式返回
	 * @param m as Map
	 * @return String[]
	 */
	public static String[] getMapKyes(Map m){
		if(m==null)return null;
		if(m.size()<=0)return new String[]{};
		return (String[])m.keySet().toArray(new String[m.size()]);
	}

	/**
	 * List转换为String Array
	 * @param list1
	 * @return
	 */
	public static String[] getArrays(List list1){
		if(list1==null || list1.size()<=0)return new String[]{};
		return (String[])list1.toArray(new String[list1.size()]);
	}

	public static String[] getArrays(Iterator it){
		List list1=new ArrayList();
		while(it.hasNext())list1.add(it.next());
		return getArrays(list1);
	}

	/**
	 * 返回Map的值数组
	 * @param m as Map
	 * @return String
	 */
	public static String[] getMapValues(Map m){
		if(m==null)return null;
		if(m.size()<=0)return new String[]{};
		return (String[])m.values().toArray(new String[m.size()]);
	}

	/**
	 * 将HttpServletRequest的参数与值转换为Map。
	 * @param request HttpServletRequest
	 * @return 转换后的Map对象
	 */
	public static Map getParamsMap(HttpServletRequest request){
		Map<String,String> m=new HashMap<String,String>();
		Enumeration<String> e=request.getParameterNames();
		String key=null;
		while(e.hasMoreElements()){
			key=e.nextElement();
			m.put(key, request.getParameter(key));
		}
		return m;
	}
}
