package com.lms.common.aop;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.lms.common.feign.api.CommonSystemApi;
import com.lms.common.model.BaseModel;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogObjname;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.BeanUtils;
import com.lms.common.util.SpringContextUtils;
import com.lms.common.util.StringHelper;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

@Aspect
@Component
public class LogAopHandler {

    @Resource
    private CommonSystemApi commonSystemApi;

    @Around(value = "execution(* com..controller.*.*(..))  && @annotation(nLog)")
    public Object doAround(ProceedingJoinPoint pjp, LMSLog nLog) throws Throwable {
        Object object = null;
        // 方法通知前获取时间,为什么要记录这个时间呢？当然是用来计算模块执行时间的
        long start = System.currentTimeMillis();
        // 拦截的实体类，就是当前正在执行的controller
        Object target = pjp.getTarget();
        // 拦截的方法名称。当前正在执行的方法
        String methodName = pjp.getSignature().getName();
        // 拦截的方法参数
        Object[] args = pjp.getArgs();
        // 拦截的放参数类型
        Signature sig = pjp.getSignature();
        MethodSignature msig = null;
        if (!(sig instanceof MethodSignature)) {
            throw new IllegalArgumentException("该注解只能用于方法");
        }
        msig = (MethodSignature) sig;
        Class[] parameterTypes = msig.getMethod().getParameterTypes();
        // 获得被拦截的方法
        Method method = null;
        method = target.getClass().getMethod(methodName, parameterTypes);
        if (null != method) {
            // 判断是否包含自定义的注解，说明一下这里的SystemLog就是我自己自定义的注解
            if (method.isAnnotationPresent(LMSLog.class)) {
                LMSLog systemlog = method.getAnnotation(LMSLog.class);
                StringBuilder objname = new StringBuilder(systemlog.objname());
                String resMsg = "";
                if (ObjectUtil.isEmpty(objname.toString())) {
                    if (!StringHelper.isEmpty(systemlog.method())) {
                        Method setLogMethod = target.getClass().getMethod(systemlog.method(), Object[].class, Class.forName("com.lms.common.model.LMSLog"));
                        if (setLogMethod != null) {
                            objname = new StringBuilder((String) setLogMethod.invoke(target, args, systemlog));
                            if (systemlog.otype().equals(LogType.List))
                                objname = new StringBuilder(commonSystemApi.translateContent(objname.toString()));
                        }
                    } else {
                        objname = new StringBuilder(getLogObjName(target, args, systemlog));
                    }
                }
                object = pjp.proceed();
                if (object instanceof Result) {
                    Result result = (Result) object;
                    resMsg = StringHelper.null2String(result.getMessage());
                    getLogNameForGetRequest(args, systemlog, objname, result);
                } else {
                    resMsg = "操作成功!";
                }
                // 保存进数据库
                Result result = commonSystemApi.saveLog(objname.toString(), systemlog.desc(), systemlog.otype(), resMsg);
            }
        }
        return object;
    }

    private void getLogNameForGetRequest(Object[] args, LMSLog systemlog, StringBuilder objname, Result result) throws IllegalAccessException {
        if (StringUtils.isEmpty(objname.toString()) && systemlog.otype().equals(LogType.Get)) {
            Object entity = result.getResult();
            if (null == entity){
                if (args != null && args.length > 0){
                    for (int i = 0; i < args.length; i++) {
                        if (i != args.length - 1){
                            objname.append(args[i]).append(",");
                        }else {
                            objname.append(args[i]);
                        }
                    }
                }
                return;
            }
            Field[] declaredFields = entity.getClass().getDeclaredFields();
            if (declaredFields.length > 0) {
                for (Field declaredField : declaredFields) {
                    declaredField.setAccessible(true);
                    LogObjname logObjname = declaredField.getAnnotation(LogObjname.class);
                    if (logObjname != null) {
                        Object o = declaredField.get(entity);
                        if (null != o) {
                            objname.append(o).append(",");
                        }
                    }
                }
            }
            objname.deleteCharAt(objname.length() - 1);
        }
    }

    /**
     * 日志对象名称获取的默认方法（如果controller中自定义了对象名称获取方法，则调用自定义方法），该方法适用于没有自定义的情况
     * 获取步骤：
     * 1、从lmslog的objname属性直接获取
     * 2、如果lmslog的objname为空，判断是否删除或批量更新的方法（此时参数为ids或id），如果是根据id和controller中的getById方法获取对象，再根据对象中注解lmslog的属性取值
     * 3、如果不是删除方法，判断是否为save,modify的方法且为model包中的实体，尝试直接获取参数对象中注解lmslog的属性取值，一般可以取值成功
     */
    public String getLogObjName(Object controller, Object[] args, LMSLog lmslog) throws Exception {
        String objname = lmslog.objname();
        if (StringHelper.isEmpty(objname)) {
            List<String> objnames = new ArrayList<>();
            for (Object object : args) {
                if (lmslog.otype().equals(LogType.Delete) || lmslog.otype().equals(LogType.BatchUpdate)) {
                    List idarray = new ArrayList<>();
                    if (object instanceof String) idarray = Lists.newArrayList(object.toString().split(","));
                    if (object instanceof ArrayList) idarray = (List) object;
                    for (Object o : idarray) {
                        if (o instanceof String) {
                            BaseServiceImpl baseServiceImpl = getServiceBean(controller);
                            objnames.addAll(BeanUtils.getLogFieldValues(baseServiceImpl.getById(o.toString())));
                        } else if (o instanceof BaseModel) {
                            objnames.addAll(BeanUtils.getLogFieldValues(o));
                        }
                    }
                } else if ((lmslog.otype().equals(LogType.Save) || lmslog.otype().equals(LogType.Update)) && object.getClass().getName().contains(".model")) {
                    BaseServiceImpl baseServiceImpl = getServiceBean(controller);
                    String id = StringHelper.null2String(BeanUtils.getValueByName(object, "id"));
                    if (!id.isEmpty() && baseServiceImpl != null) {
                        Object o = baseServiceImpl.getById(id);
                        if (ObjectUtil.isNotEmpty(o)) objnames.addAll(BeanUtils.getLogFieldValues(o));
                    } else {
                        objnames.addAll(BeanUtils.getLogFieldValues(object));
                    }
                }
            }
            objname = String.join(",", objnames);
        } else {
            //objname = commonSystemApi.translateContent(lmslog.objname());
        }
        return objname;
    }

    private BaseServiceImpl getServiceBean(Object controller) {
        try {
            String controllerName = controller.getClass().getSimpleName();
            if (controllerName.contains("Controller")) {
                String serviceName = controllerName.replace("Controller", "Service");
                Object service = SpringContextUtils.getBean(serviceName);
                if (service != null) {
                    BaseServiceImpl baseServiceImpl = (BaseServiceImpl) service;
                    return baseServiceImpl;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
