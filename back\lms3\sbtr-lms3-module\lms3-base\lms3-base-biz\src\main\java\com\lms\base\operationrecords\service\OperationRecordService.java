package com.lms.base.operationrecords.service;

import com.lms.base.operationrecords.model.OperationRecord;

import java.util.List;

/**
 * 操作记录表Service接口。
 */
public interface OperationRecordService {
    /**
     * 保存操作记录
     *
     * @param record 操作记录实体
     */
    void save(OperationRecord record);

    /**
     * 根据ID删除操作记录
     *
     * @param id 主键ID
     */
    void deleteById(String id);

    /**
     * 根据ID查询操作记录
     *
     * @param id 主键ID
     * @return 操作记录实体，未找到返回null
     */
    OperationRecord findById(String id);

    /**
     * 分页条件查询操作记录
     *
     * @param conditions 查询条件，Map<String, Object>
     * @param page       页码，从1开始
     * @param size       每页条数
     * @return 操作记录列表
     */
    List<OperationRecord> queryList(java.util.Map<String, Object> conditions, int page, int size);

    /**
     * 批量保存操作记录
     *
     * @param records 操作记录实体列表
     */
    void saveBatch(List<OperationRecord> records);

    /**
     * 批量删除操作记录
     *
     * @param ids 主键ID列表
     */
    void deleteBatch(List<String> ids);

    /**
     * 批量根据ID查询操作记录
     *
     * @param ids 主键ID列表
     * @return 操作记录实体列表
     */
    List<OperationRecord> findByIds(List<String> ids);
} 