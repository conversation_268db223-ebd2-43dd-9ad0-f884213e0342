package com.sbtr.workflow.model;

import cn.ewsd.common.model.MCoreBase;
import lombok.Data;

import javax.persistence.Table;

/**
 * 流程按钮表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-23 08:10:59
 */
@Table(name = "act_my_button")
@Data
public class ActMyButton extends MCoreBase {
    private static final long serialVersionUID = 1L;

    /**
     * 按钮名称
     */
    private String buttonName;
    /**
     * 按钮code
     */
    private String buttonCode;
    /**
     * 按钮类型
     */
    private String buttonType;
    /**
     * 按钮顺序
     */
    private Integer buttonSort;

}
