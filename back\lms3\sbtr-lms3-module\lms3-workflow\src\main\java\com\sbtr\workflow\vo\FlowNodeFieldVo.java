package com.sbtr.workflow.vo;

import cn.ewsd.common.model.MCoreBase;

/**
 * 流程每个节点所对字段是否可编辑以及是否可见
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-27 13:43:13
 */
public class FlowNodeFieldVo extends MCoreBase {
    private static final long serialVersionUID = 1L;

    // 字段code
    private String filed;
    // 字段名称
    private String filedName;
    // 节点id
    private String id;
    // 模型key
    private String modelKey;
    // 流程表单uuid
    private String flowModelUuid;
    // 是否可以编辑(true 是 false 否)
    private Boolean isEdit;
    // 是否可以查看(true 是 false 否)
    private Boolean isLook;
    // 模型id
    private String modelId;
    private String formUuid;
    private String modelType;
    private Integer fieldIndex;
    private String formLayout;


    public String getFormLayout() {
        return formLayout;
    }

    public void setFormLayout(String formLayout) {
        this.formLayout = formLayout;
    }

    public Integer getFieldIndex() {
        return fieldIndex;
    }

    public void setFieldIndex(Integer fieldIndex) {
        this.fieldIndex = fieldIndex;
    }

    public String getFormUuid() {
        return formUuid;
    }

    public void setFormUuid(String formUuid) {
        this.formUuid = formUuid;
    }

    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public String getModelKey() {
        return modelKey;
    }

    public void setModelKey(String modelKey) {
        this.modelKey = modelKey;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    /**
     * 设置：字段code
     */
    public void setFiled(String filed) {
        this.filed = filed;
    }

    /**
     * 获取：字段code
     */
    public String getFiled() {
        return filed;
    }

    /**
     * 设置：字段名称
     */
    public void setFiledName(String filedName) {
        this.filedName = filedName;
    }

    /**
     * 获取：字段名称
     */
    public String getFiledName() {
        return filedName;
    }

    /**
     * 设置：节点id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取：节点id
     */
    public String getId() {
        return id;
    }


    /**
     * 设置：流程表单uuid
     */
    public void setFlowModelUuid(String flowModelUuid) {
        this.flowModelUuid = flowModelUuid;
    }

    /**
     * 获取：流程表单uuid
     */
    public String getFlowModelUuid() {
        return flowModelUuid;
    }

    public Boolean getEdit() {
        return isEdit;
    }

    public void setEdit(Boolean edit) {
        isEdit = edit;
    }

    public Boolean getLook() {
        return isLook;
    }

    public void setLook(Boolean look) {
        isLook = look;
    }
}
