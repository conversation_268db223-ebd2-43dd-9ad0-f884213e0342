<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ActMyNodeFieldMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sbtr.workflow.model.ActMyNodeField" id="flowNodeFieldMap">
        <result property="uuid" column="uuid"/>
        <result property="createTime" column="create_time"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorOrgId" column="creator_org_id"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="filed" column="filed"/>
        <result property="filedName" column="filed_name"/>
        <result property="id" column="id"/>
        <result property="modalKey" column="modal_key"/>
        <result property="flowModelUuid" column="flow_model_uuid"/>
        <result property="isEdit" column="is_edit"/>
        <result property="isLook" column="is_look"/>
    </resultMap>

    <sql id="Base_Column_List">
  	  	     			 uuid
		  ,  	  	     			 create_time
		  ,  	  	     			 creator
		  ,  	  	     			 creator_id
		  ,  	  	     			 creator_org_id
		  ,  	  	     			 modifier_id
		  ,  	  	     			 modifier
		  ,  	  	     			 modify_time
		  ,  	  	     			 filed
		  ,  	  	     			 filed_name
		  ,  	  	     			 id
		  ,  	  	     			 model_key
		  ,  	  	     			 flow_model_uuid
		  ,  	  	     			 is_edit
		  ,  	  	     			 is_look
		  ,procdef_id
		  ,model_id
		  ,form_json
		  ,form_uuid
		  ,model_type
		  ,field_index
		  ,form_layout
		    	  </sql>
    <insert id="insertListOracle">
        insert into act_my_node_field (
            uuid,
            create_time,
            creator,
            creator_id,
            creator_org_id,
            filed,
            filed_name,
            id,
            model_key,
            model_id,
            flow_model_uuid,
            is_edit,
            is_look,
            procdef_id
        )
        <foreach collection="list" item="list" index="index" separator="union all">
            (
                select
                    #{list.uuid},
                    #{list.createTime},
                    #{list.creator},
                    #{list.creatorId},
                    #{list.creatorOrgId},
                    #{list.filed},
                    #{list.filedName},
                    #{list.id},
                    #{list.modelKey},
                    #{list.modelId},
                    #{list.flowModelUuid},
                    #{list.isEdit},
                    #{list.isLook},
                    #{list.procdefId,jdbcType=VARCHAR}
                from dual
            )
        </foreach>
    </insert>
    <insert id="insertList">
        insert into act_my_node_field (
            uuid,
            create_time,
            creator,
            creator_id,
            creator_org_id,
            filed,
            filed_name,
            id,
            model_key,
            model_id,
            flow_model_uuid,
            is_edit,
            is_look,
            procdef_id
        )
        values
        <foreach collection="list" item="list" index="index" separator=",">
            (#{list.uuid},
            #{list.createTime},
            #{list.creator},
            #{list.creatorId},
            #{list.creatorOrgId},
            #{list.filed},
            #{list.filedName},
            #{list.id},
            #{list.modelKey},
            #{list.modelId},
            #{list.flowModelUuid},
            #{list.isEdit},
            #{list.isLook},
            #{list.procdefId,jdbcType=VARCHAR},
            #{list.formUuid,jdbcType=VARCHAR},
            #{list.modelType,jdbcType=VARCHAR},
            #{list.fieldIndex,jdbcType=INTEGER},
            #{list.formLayout,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <update id="updateProcdefIdByModelKeyAndProcdef">
        update act_my_node_field set procdef_id=#{procdefId}  where model_key = #{key} and procdef_id =#{id}
    </update>
    <update id="updateProcdefIdByModelKey">
          update act_my_node_field set procdef_id=#{procdefId} where model_key = #{key} and (coalesce(procdef_id ,N'') = '')
    </update>
    <update id="updateProcdefIdIsNull">
          update act_my_node_field set procdef_id='' where model_key = #{key} and procdef_id =#{id}
    </update>

    <select id="getPageSet" resultType="com.sbtr.workflow.model.ActMyNodeField">
        select
        <include refid="Base_Column_List"></include>
        from act_my_node_field
    </select>
    <select id="selectByModelKeyAndId" resultType="com.sbtr.workflow.vo.FlowNodeFieldVo">
        select
        uuid
        ,  	  	     			 create_time
        ,  	  	     			 creator
        ,  	  	     			 creator_id
        ,  	  	     			 creator_org_id
        ,  	  	     			 modifier_id
        ,  	  	     			 modifier
        ,  	  	     			 modify_time
        ,  	  	     			 filed
        ,  	  	     			 filed_name
        ,  	  	     			 id
        ,  	  	     			 model_key
        ,  	  	     			 flow_model_uuid
        ,  	  	     			 is_edit
        ,  	  	     			 is_look
        ,               procdef_id
        ,               form_uuid
        ,               model_type
        ,               field_index
        ,               form_layout
        from act_my_node_field
        where  model_key=#{modelKey}
        <if test="nodeId!=null and nodeId!=''">
            and id=#{nodeId}
        </if>
        <if test="processDefinitionId!=null and processDefinitionId!=''">
          and  procdef_id =#{processDefinitionId}
        </if>
        order by field_index asc
    </select>
    <select id="getListByActDeModelKeyAndProcdefId" resultType="com.sbtr.workflow.model.ActMyNodeField">
        select
        <include refid="Base_Column_List"></include>
        from act_my_node_field where model_key =#{key} and procdef_id =#{procdefIds}
    </select>


    <delete id="executeDeleteBatch">
        delete from act_my_node_field where uuid in
        <foreach item="uuid" collection="array" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </delete>
    <delete id="deleteByModelKeyAnProcdefId">
        delete from act_my_node_field where model_key =#{key} and procdef_id =#{id}
    </delete>

</mapper>
