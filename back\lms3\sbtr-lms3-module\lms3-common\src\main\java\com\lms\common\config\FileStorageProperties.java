package com.lms.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 文件存储配置属性
 * 统一管理所有存储方式的配置参数
 */
@Data
@Component
@ConfigurationProperties(prefix = "lms.file-storage")
public class FileStorageProperties {
    
    /**
     * 默认存储策略类型
     * 可选值: oss, minio, local
     */
    private String defaultStrategy = "oss";
    
    /**
     * 是否启用多存储策略
     * 启用后可以同时使用多种存储方式
     */
    private boolean multiStrategyEnabled = false;
    
    /**
     * 文件上传配置
     */
    private Upload upload = new Upload();
    
    /**
     * OSS存储配置
     */
    private Oss oss = new Oss();
    
    /**
     * MinIO存储配置
     */
    private Minio minio = new Minio();
    
    /**
     * 本地存储配置
     */
    private Local local = new Local();
    
    /**
     * 文件上传配置类
     */
    @Data
    public static class Upload {
        /**
         * 最大文件大小（字节）
         */
        private long maxFileSize = 100 * 1024 * 1024; // 100MB
        
        /**
         * 允许的文件扩展名
         */
        private String[] allowedExtensions = {
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
            ".txt", ".csv", ".zip", ".rar", ".7z", ".tar", ".gz",
            ".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv",
            ".mp3", ".wav", ".flac", ".aac",
            ".vrp", ".vrpc"
        };
        
        /**
         * 是否启用文件类型检查
         */
        private boolean enableTypeCheck = true;
        
        /**
         * 是否启用文件大小检查
         */
        private boolean enableSizeCheck = true;
        
        /**
         * 文件名生成策略
         * 可选值: uuid, timestamp, original
         */
        private String fileNameStrategy = "uuid";
    }
    
    /**
     * OSS存储配置类
     */
    @Data
    public static class Oss {
        /**
         * 是否启用OSS存储
         */
        private boolean enabled = true;
        
        /**
         * OSS访问端点
         */
        private String endpoint;
        
        /**
         * 访问密钥ID
         */
        private String accessKeyId;
        
        /**
         * 访问密钥Secret
         */
        private String accessKeySecret;
        
        /**
         * 区域
         */
        private String region;
        
        /**
         * 默认存储桶名称
         */
        private String defaultBucket;
        
        /**
         * 是否使用HTTPS
         */
        private boolean useHttps = true;
        
        /**
         * 连接超时时间（毫秒）
         */
        private int connectionTimeout = 30000;
        
        /**
         * 读取超时时间（毫秒）
         */
        private int socketTimeout = 30000;
    }
    
    /**
     * MinIO存储配置类
     */
    @Data
    public static class Minio {
        /**
         * 是否启用MinIO存储
         */
        private boolean enabled = false;
        
        /**
         * MinIO服务地址
         */
        private String host;
        
        /**
         * 访问密钥
         */
        private String accessKey;
        
        /**
         * 密钥
         */
        private String secretKey;
        
        /**
         * 默认存储桶名称
         */
        private String defaultBucket = "lms-bucket";
        
        /**
         * 是否使用HTTPS
         */
        private boolean useHttps = false;
        
        /**
         * 连接超时时间（毫秒）
         */
        private int connectionTimeout = 30000;
        
        /**
         * 读取超时时间（毫秒）
         */
        private int readTimeout = 30000;
        
        /**
         * 写入超时时间（毫秒）
         */
        private int writeTimeout = 30000;
    }
    
    /**
     * 本地存储配置类
     */
    @Data
    public static class Local {
        /**
         * 是否启用本地存储
         */
        private boolean enabled = false;
        
        /**
         * 存储根路径
         */
        private String basePath;
        
        /**
         * 临时文件路径
         */
        private String tempPath;
        
        /**
         * 是否创建日期目录
         * 启用后会按照年/月/日创建子目录
         */
        private boolean createDateDir = true;
        
        /**
         * 文件访问URL前缀
         */
        private String urlPrefix = "/files";
        
        /**
         * 是否启用文件压缩
         */
        private boolean enableCompression = false;
        
        /**
         * 压缩质量（0.0-1.0）
         */
        private float compressionQuality = 0.8f;
        
        /**
         * 是否启用缩略图生成
         */
        private boolean enableThumbnail = false;
        
        /**
         * 缩略图尺寸
         */
        private String thumbnailSize = "200x200";
    }
    
    /**
     * 获取指定存储类型的启用状态
     * 
     * @param storageType 存储类型
     * @return 是否启用
     */
    public boolean isStorageEnabled(String storageType) {
        if (storageType == null) {
            return false;
        }
        
        switch (storageType.toLowerCase()) {
            case "oss":
                return oss.enabled;
            case "minio":
                return minio.enabled;
            case "local":
                return local.enabled;
            default:
                return false;
        }
    }
    
    /**
     * 验证配置是否有效
     * 
     * @return 验证结果
     */
    public boolean isValid() {
        // 至少要有一种存储方式启用
        if (!oss.enabled && !minio.enabled && !local.enabled) {
            return false;
        }
        
        // 默认策略必须启用
        if (!isStorageEnabled(defaultStrategy)) {
            return false;
        }
        
        // OSS配置验证
        if (oss.enabled) {
            if (oss.endpoint == null || oss.accessKeyId == null || 
                oss.accessKeySecret == null || oss.defaultBucket == null) {
                return false;
            }
        }
        
        // MinIO配置验证
        if (minio.enabled) {
            if (minio.host == null || minio.accessKey == null || 
                minio.secretKey == null || minio.defaultBucket == null) {
                return false;
            }
        }
        
        // 本地存储配置验证
        if (local.enabled) {
            if (local.basePath == null) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取配置摘要信息
     * 
     * @return 配置摘要
     */
    public String getConfigSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("文件存储配置摘要:\n");
        summary.append("默认策略: ").append(defaultStrategy).append("\n");
        summary.append("多策略模式: ").append(multiStrategyEnabled ? "启用" : "禁用").append("\n");
        summary.append("OSS存储: ").append(oss.enabled ? "启用" : "禁用").append("\n");
        summary.append("MinIO存储: ").append(minio.enabled ? "启用" : "禁用").append("\n");
        summary.append("本地存储: ").append(local.enabled ? "启用" : "禁用").append("\n");
        summary.append("最大文件大小: ").append(upload.maxFileSize / 1024 / 1024).append("MB\n");
        summary.append("允许文件类型: ").append(upload.allowedExtensions.length).append("种");
        return summary.toString();
    }
}
