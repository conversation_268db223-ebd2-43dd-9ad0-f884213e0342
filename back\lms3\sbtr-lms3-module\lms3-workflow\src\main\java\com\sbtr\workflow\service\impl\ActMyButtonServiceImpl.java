package com.sbtr.workflow.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.mapper.ActMyButtonMapper;
import com.sbtr.workflow.model.ActMyButton;
import com.sbtr.workflow.service.ActMyButtonService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.PageUtil.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service("flowButtonServiceImpl")
public class ActMyButtonServiceImpl extends WorkflowBaseServiceImpl<ActMyButton, String> implements ActMyButtonService {
	@Autowired
	private ActMyButtonMapper actMyButtonMapper;


    @Override
    public PageSet<ActMyButton> getPageSet(PageParam pageParam, String buttonCode,String buttonName) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        Example example = new Example(ActMyButton.class);
        example.orderBy("buttonSort").asc();
        Example.Criteria criteria = example.createCriteria();
        if (StrUtil.isNotBlank(buttonCode)) {
            criteria.andLike("buttonCode", "%" + buttonCode + "%");
        }
        if (StrUtil.isNotBlank(buttonName)) {
            criteria.orLike("buttonName", "%" + buttonName + "%");
        }
        List<ActMyButton> list = actMyButtonMapper.selectByExample(example);
        PageInfo<ActMyButton> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

	@Override
	public int executeDeleteBatch(String[] uuids){
		return actMyButtonMapper.executeDeleteBatch(uuids);
	}

    @Override
    public Boolean getListByCodeAndUuid(String buttonCode, String uuid) {
        return actMyButtonMapper.getListByCodeAndUuid(buttonCode, uuid) > 0;

    }

}
