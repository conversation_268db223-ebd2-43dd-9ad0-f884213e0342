package com.sbtr.workflow.service.impl;

import cn.ewsd.common.utils.StringUtils;
import com.sbtr.workflow.constants.DriverClassName;
import com.sbtr.workflow.mapper.ApiFlowableProcessDefinitionMapper;
import com.sbtr.workflow.service.ApiFlowableProcessDefinitionService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.PageUtil.PageUtils;
import com.sbtr.workflow.vo.ProcessDefinitionVo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.flowable.engine.RepositoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.List;

@Service("apiFlowableProcessDefinitionServiceImpl")
public class ApiFlowableProcessDefinitionServiceImpl extends BaseServiceImpl implements ApiFlowableProcessDefinitionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiFlowableProcessDefinitionServiceImpl.class);

    @Autowired
    private ApiFlowableProcessDefinitionMapper apiFlowableProcessDefinitionMapper;

    @Autowired
    private RepositoryService repositoryService;

    @Value("${spring.datasource.dynamic.datasource.master.driver-class-name:null}")
    public String driverClassName;

    @Override
    public PageSet<ProcessDefinitionVo> getPageSet(PageParam pageParam, String category, String processModelType, String name) {
        String majorVersion = "是";
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<ProcessDefinitionVo> list = apiFlowableProcessDefinitionMapper.getPageSetMySql(category, processModelType, name, majorVersion);
        PageInfo<ProcessDefinitionVo> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Transactional
    @Override
    public Object deleteDeployment(String deploymentId) {
        try {
            if (StringUtils.isNullOrEmpty(deploymentId)) {
                return failure("deploymentId不能为空");
            }
            repositoryService.deleteDeployment(deploymentId, true);
            return success("删除成功!");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure("删除失败!原因为===" + e.getMessage());
        }
    }

    @Transactional
    @Override
    public Object suspendOrActivateProcessDefinitionById(String processDefinitionId, int suspensionState) {
        try {
            if (suspensionState == 2) {
                repositoryService.suspendProcessDefinitionById(processDefinitionId, true, null);
                return success("挂起成功");
            } else {
                repositoryService.activateProcessDefinitionById(processDefinitionId, true, null);
                return success("激活成功");
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return failure("失败原因为：" + e.getMessage());
        }

    }

    @Override
    public List<ProcessDefinitionVo> getListByIdAndModelKey(String modelKey, String majorVersion) {
        return apiFlowableProcessDefinitionMapper.getListByIdAndModelKey(modelKey, majorVersion);
    }

    @Override
    public Integer updateMajorVersion(String procdefId, String modelKey, String majorVersion) {
        return apiFlowableProcessDefinitionMapper.updateMajorVersion(procdefId, modelKey, majorVersion);
    }

    @Override
    public Integer getCountSumByModelKey(String modelKey) {
        return apiFlowableProcessDefinitionMapper.getCountSumByModelKey(modelKey);
    }

    @Override
    public Integer updateMajorVersionByModelKey(String modelKey, String majorVersion) {
        return apiFlowableProcessDefinitionMapper.updateMajorVersionByModelKey(modelKey, majorVersion);
    }

    @Override
    public String getProdefIdByDeployId(String id) {
        return apiFlowableProcessDefinitionMapper.getProdefIdByDeployId(id);
    }

    @Override
    public ProcessDefinitionVo getProdefIdById(String processDefinitionId) {
        return apiFlowableProcessDefinitionMapper.getProdefIdById(processDefinitionId);
    }

    @Override
    public List<ProcessDefinitionVo> getCustomInterface() {
        return apiFlowableProcessDefinitionMapper.getCustomInterface();
    }

    @Override
    public List<ProcessDefinitionVo> getAllData(PageParam pageParam, String category, String processModelType, String name) {
        String majorVersion = "是";
        List<ProcessDefinitionVo> list = apiFlowableProcessDefinitionMapper.getPageSetMySql(category, processModelType, name, majorVersion);
        return list;
    }
}
