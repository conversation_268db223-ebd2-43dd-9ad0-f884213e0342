package com.lms.common.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
@Component
public class DBSqlUtil {
	@Value("${spring.datasource.database}")
	private String dbtype;
	public  String DBTYPE_ORACLE = "1";
	public  String DBTYPE_MYSQL = "2";
	public  String DBTYPE_SQLSERVER = "3";
	public  String DBTYPE_DB2 = "4";
	public  String DBTYPE_POSTGRESQL = "5";
	public  String DBTYPE_INTERBASE = "6";
	public  String DBTYPE_HSQL = "7";
	public  String SESSION_PWD_KEY = "password";

	/*public  String getDBType2() throws SQLException {
		String dialect = props.getProperty("hibernate.dialect");
		if (dialect == null)
			throw new SQLException(
					"no dbtype is defined,please check hibernate.dialect");
		else if (dialect.indexOf("SQLServer") > 0)
			dbtype = DBTYPE_SQLSERVER;
		else if (dialect.indexOf("Oracle") > 0)
			dbtype = DBTYPE_ORACLE;
		else if (dialect.indexOf("MySQL") > 0)
			dbtype = DBTYPE_MYSQL;
		else if (dialect.indexOf("PostgreSQL") > 0)
			dbtype = DBTYPE_POSTGRESQL;
		else if (dialect.indexOf("DB2") > 0)
			dbtype = DBTYPE_DB2;
		else if (dialect.indexOf("Interbase") > 0)
			dbtype = DBTYPE_INTERBASE;
		else if (dialect.indexOf("HSQL") > 0)
			dbtype = DBTYPE_HSQL;
		else
			throw new SQLException(
					"dbtype is not supported,please check hibernate.dialect");
		return dbtype;
	}*/

	public  String getIfNullFlag() throws SQLException {
		if (dbtype.equals(DBTYPE_MYSQL)) {
			return "IFNULL";
		} else if (dbtype.equals(DBTYPE_ORACLE)) {
			return "nvl";
		} else
			throw new SQLException(
					"dbtype is not supported,please check hibernate.dialect");

	}

	public  String getRandomFlag(){
		if (dbtype.equals(DBTYPE_MYSQL)) {
			return " rand()";
		} else if (dbtype.equals(DBTYPE_ORACLE)) {
			return " dbms_random.value";
		} else {
			return "";
		}
	}

	public  String getLEFlag() throws SQLException {
		if (dbtype.equals(DBTYPE_MYSQL)) {
			return "limit";
		} else if (dbtype.equals(DBTYPE_ORACLE)) {
			return " where rownum <= ";
		} else
			throw new SQLException(
					"dbtype is not supported,please check hibernate.dialect");

	}
}
