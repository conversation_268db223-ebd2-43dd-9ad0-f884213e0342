package com.lms.base.model.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lms.base.model.vo.ModelVo;
import com.lms.common.model.Result;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface ModelService {

    List<ModelVo> listAll();

    Page<ModelVo> listPage(JSONObject jsonObject) throws Exception;

    void saveOrUpdateModel(ModelVo modelVo, boolean adaptSubModelFlag) throws Exception;

    void deleteModel(String ids) throws Exception;

    Result<Map<String, Object>> queryTuGraph(String modelId) throws Exception;

    void createModelRelationship(String startModelId, String endModelId, String relationship) throws Exception;

    ModelVo getById(String id) throws IOException;

    void updateModelByEditor(ModelVo modelVo) throws Exception;
}
