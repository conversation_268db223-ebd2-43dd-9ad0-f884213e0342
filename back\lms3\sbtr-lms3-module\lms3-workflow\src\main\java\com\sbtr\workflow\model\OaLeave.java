package com.sbtr.workflow.model;

import cn.ewsd.common.model.MCoreBase;

import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @Email sbtr@.com
 * @Date 2019-01-19 12:00:06
 */
@Table(name = "oa_leave")
public class OaLeave extends MCoreBase{
	private static final long serialVersionUID=1L;

	private String beginTime;
	private String endTime;
	private String item;
	private String reason;
	private String title;
	private String state;
	private Integer days;

	@Transient
	private String itemText;

	public String getItemText() {
		return itemText;
	}

	public void setItemText(String itemText) {
		this.itemText = itemText;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	/**
	 * 设置：${column.comments}
	 */
	public void setBeginTime(String beginTime){
		this.beginTime = beginTime;
	}

	/**
	 * 获取：${column.comments}
	 */
	public String getBeginTime(){
		return beginTime;
	}


	/**
	 * 设置：${column.comments}
	 */
	public void setEndTime(String endTime){
		this.endTime = endTime;
	}

	/**
	 * 获取：${column.comments}
	 */
	public String getEndTime(){
		return endTime;
	}

	/**
	 * 设置：${column.comments}
	 */
	public void setItem(String item){
		this.item = item;
	}

	/**
	 * 获取：${column.comments}
	 */
	public String getItem(){
		return item;
	}

	/**
	 * 设置：${column.comments}
	 */
	public void setReason(String reason){
		this.reason = reason;
	}

	/**
	 * 获取：${column.comments}
	 */
	public String getReason(){
		return reason;
	}

	/**
	 * 设置：${column.comments}
	 */
	public void setTitle(String title){
		this.title = title;
	}

	/**
	 * 获取：${column.comments}
	 */
	public String getTitle(){
		return title;
	}


	/**
	 * 设置：${column.comments}
	 */
	public void setDays(Integer days){
		this.days = days;
	}

	/**
	 * 获取：${column.comments}
	 */
	public Integer getDays(){
		return days;
	}


}
