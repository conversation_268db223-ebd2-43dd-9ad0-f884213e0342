package com.lms.system.languagelabel.service;

import com.lms.common.service.BaseService;
import com.lms.system.feign.model.Languagelabel;

public interface LanguagelabelService extends BaseService<Languagelabel> {
    String translateContent(String keyword, String module, String[] variables);

    String translateContent(String keyword, String module, String variables);

    String translateContent(String keyword);

    String translateContentNotCreated(String keyword, String module);

    String translateContent(String keyword, String moduledesc);
}
