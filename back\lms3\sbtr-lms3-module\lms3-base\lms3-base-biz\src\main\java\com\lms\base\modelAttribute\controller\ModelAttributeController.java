package com.lms.base.modelAttribute.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lms.base.modelAttribute.service.ModelAttributeService;
import com.lms.base.modelAttribute.vo.ModelAttributeVo;
import com.lms.common.util.TuGraphUtil;
import com.lms.common.constants.Constants;
import com.lms.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/modelAttribute")
@Api(value = "模型属性", tags = "模型属性")
public class ModelAttributeController {

    @Resource
    private ModelAttributeService modelAttributeService;

    @Autowired
    private TuGraphUtil tuGraphUtil;

    @ApiOperation(value = "保存模型属性", httpMethod = "POST")
    @PostMapping("/add")
    public Result<ModelAttributeVo> saveModelAttribute(@RequestBody ModelAttributeVo modelAttributeVo) throws Exception {
        modelAttributeService.saveModelAttribute(modelAttributeVo);
        return Result.OK(modelAttributeVo);
    }

    @ApiOperation(value = "查询所有模型属性", httpMethod = "POST")
    @PostMapping("/list")
    public Result<List<ModelAttributeVo>> modelList() {
        return Result.OK(modelAttributeService.listAll());
    }

    @ApiOperation(value = "分页查询所有模型属性", httpMethod = "POST")
    @PostMapping("/listPage")
    public Result<Page<ModelAttributeVo>> listPage(@RequestBody JSONObject jsonObject) throws Exception {
        Page<ModelAttributeVo> page = modelAttributeService.listPage(jsonObject);
        return Result.OK(page);
    }

    @ApiOperation(value = "查询模型的所有属性", httpMethod = "GET")
    @GetMapping("/queryModelAttribute")
    public Result<List<ModelAttributeVo>> queryModelAttribute(@RequestParam("modelId") String modelId) throws Exception {
        List<ModelAttributeVo> modelAttributeVos = modelAttributeService.queryModelAttribute(modelId);
        return Result.OK(modelAttributeVos);
    }

    @ApiOperation(value = "修改模型属性", httpMethod = "POST")
    @PostMapping("/update")
    public Result<Void> updateModelAttribute(@RequestBody ModelAttributeVo modelAttributeVo) {
        return modelAttributeService.updateModelAttribute(modelAttributeVo);
    }

    @ApiOperation(value = "删除模型属性", httpMethod = "DELETE")
    @DeleteMapping("/delete")
    public Result<Void> deleteModelAttribute(
            @ApiParam(value = "模型属性id以,分隔") @RequestParam("ids") String ids) {
        List<String> idList = Arrays.stream(ids.split(","))
                .map(String::trim)  // 去除空格
                .collect(Collectors.toList());
        String result = tuGraphUtil.batchDeleteNodesByIds("default", Constants.MODEL_ATTRIBUTE, idList);
        return !result.equals("[]") ? Result.OK() : Result.error("删除模型失败！");
    }

}
