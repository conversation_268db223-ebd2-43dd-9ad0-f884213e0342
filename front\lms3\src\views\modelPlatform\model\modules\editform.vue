<script setup lang="ts">
import { ref, reactive, watch, nextTick, computed } from "vue";
import { message } from "ant-design-vue";
import { Icon } from "@iconify/vue";
import type { FormInstance, UploadFile } from "ant-design-vue";
import {
  AppstoreOutlined,
  CloudUploadOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
  SelectOutlined,
  FileOutlined,
  PlusOutlined
} from "@ant-design/icons-vue";
import type { Model } from "@/types/model";
import { updateModel, getModelAttributeList } from "@/api/model";
import ModelattrSelector from "@/components/Selector/modelattrSelector.vue";
import PersonSelector from "@/components/Selector/personSelector.vue";
import * as directives from "@/directives";
import {
  isImage,
  isVideo,
  is3DModel,
  beforeUpload,
  uploadFile
} from "@/utils/uploadUtils";

interface Property {
  id: number | null;
  modelId: number | null;
  name: string;
  relationship: string;
  value: string;
}

interface ModelEditForm {
  id: number;
  name: string;
  number: string;
  desc: string;
  attributeList: Property[];
  fileUrl: string;
  previewUrl: string;
  uploadPerson: string;
  uploadTime: string | null;
}

interface Props {
  visible: boolean;
  model: Model | null;
}

interface Emits {
  (e: "update:visible", visible: boolean): void;
  (e: "submit", data: ModelEditForm): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const loading = ref(false);
const fileList = ref<UploadFile[]>([]);
const uploadedFile = ref<File | null>(null);
const uploading = ref(false);
const uploadProgress = ref(0);
const uploadSuccess = ref(false);
const previewUrl = ref<string>("");
const currentFileUrl = ref<string>("");

// 表单数据
const formData = reactive<ModelEditForm>({
  id: 0,
  name: "",
  number: "",
  desc: "",
  attributeList: [],
  fileUrl: "",
  fileType: "",
  previewUrl: "",
  uploadPerson: "",
  uploadTime: null
});

// 属性数据
const properties = ref<Property[]>([]);

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入模型名称" }]
  // number: [{ required: true, message: "请输入编号" }],
  // desc: [{ required: true, message: "请输入描述" }],
  // uploadPerson: [{ required: true, message: "请输入上传人" }]
};

// 属性表格列定义
const propertyColumns = [
  {
    title: "属性名",
    dataIndex: "name",
    key: "name",
    width: "30%"
  },
  {
    title: "属性值",
    dataIndex: "value",
    key: "value",
    width: "30%"
  },
  {
    title: "属性关系",
    dataIndex: "relationship",
    key: "relationship",
    width: "30%"
  },
  {
    title: "",
    key: "action",
    width: "10%",
    align: "center"
  }
];

// 判断当前文件类型
const isCurrentImage = computed(() => {
  if (!currentFileUrl.value) return false;
  const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"];
  return imageExtensions.some(ext =>
    currentFileUrl.value.toLowerCase().includes(ext)
  );
});

const isCurrentVideo = computed(() => {
  if (!currentFileUrl.value) return false;
  const videoExtensions = [".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm"];
  return videoExtensions.some(ext =>
    currentFileUrl.value.toLowerCase().includes(ext)
  );
});



// 监听弹窗显示状态，初始化表单数据
watch(
  () => props.visible,
  visible => {
    if (visible && props.model) {
      initFormData();
    }
  }
);

// 初始化表单数据
const initFormData = () => {
  if (!props.model) return;

  // 基本信息
  Object.assign(formData, {
    id: props.model.id,
    name: props.model.name,
    number: props.model.number,
    desc: props.model.desc,
    fileUrl: props.model.fileUrl,
    previewUrl: props.model.previewUrl,
    uploadPerson: props.model.uploadPerson,
    uploadTime: props.model.uploadTime
  });

  // 设置当前文件URL
  currentFileUrl.value = props.model.previewUrl || props.model.fileUrl || "";

  // 属性信息 - 从model.attributeList转换
  if (props.model.attributeList && Array.isArray(props.model.attributeList)) {
    properties.value = props.model.attributeList.map(item => ({
      id: item.id,
      modelId: item.modelId,
      name: item.name,
      relationship: item.relationship,
      value: item.value
    }));
  } else {
    // 如果没有属性，初始化默认属性
    properties.value = [
      {
        id: null,
        modelId: props.model.id,
        name: "ATA章节",
        relationship: "拥有",
        value: ""
      },
      {
        id: null,
        modelId: props.model.id,
        name: "件号",
        relationship: "位于",
        value: ""
      },
      {
        id: null,
        modelId: props.model.id,
        name: "SNS代码",
        relationship: "附有",
        value: ""
      }
    ];
  }

  // 重置上传状态
  uploadedFile.value = null;
  uploading.value = false;
  uploadProgress.value = 0;
  uploadSuccess.value = false;
  previewUrl.value = "";
  fileList.value = [];
};

// 文件上传前的处理
const beforeUploadHandler = (file: File) => {
  return beforeUpload(file);
};

// 文件变化处理
const handleFileChange = (info: any) => {
  const { file } = info;
  if (file.status !== "removed") {
    uploadedFile.value = file.originFileObj || file;
    uploadFileHandler();
  }
};



// 真实文件上传
const uploadFileHandler = async () => {
  if (!uploadedFile.value) return;

  try {
    uploading.value = true;
    uploadProgress.value = 0;
    uploadSuccess.value = false;
    previewUrl.value = "";

    // 使用统一的上传函数
    const uploadResult = await uploadFile(uploadedFile.value, (progress) => {
      uploadProgress.value = progress;
    });

    // 处理接口返回的预览地址
    if (uploadResult && uploadResult.result && uploadResult.result.fileUrl) {
      formData.fileUrl = uploadResult.result.fileUrl;
      formData.fileType = uploadResult.result.fileType || "";
      formData.previewUrl = uploadResult.result.fileUrl;
      previewUrl.value = uploadResult.result.fileUrl;
    } else {
      // 兜底：为所有文件类型创建本地预览URL，确保formData.fileUrl和previewUrl有值
      const localUrl = URL.createObjectURL(uploadedFile.value);
      previewUrl.value = localUrl;
      formData.previewUrl = localUrl;
      
      // 如果接口没有返回fileUrl，使用本地URL作为fileUrl的兜底
      if (!formData.fileUrl) {
        formData.fileUrl = localUrl;
      }
      
      // 为3D模型文件（包括VRP/VRPC）设置文件类型
      if (is3DModel(uploadedFile.value)) {
        formData.fileType = uploadedFile.value.name.split('.').pop()?.toLowerCase() || '';
      }
    }

    uploading.value = false;
    uploadSuccess.value = true;
    uploadProgress.value = 100;
    message.success("文件上传成功");
  } catch (error) {
    console.error("文件上传失败:", error);
    uploading.value = false;
    uploadSuccess.value = false;
    uploadProgress.value = 0;

    // 上传失败时，为所有文件类型创建本地预览URL作为兜底
    if (uploadedFile.value) {
      const localUrl = URL.createObjectURL(uploadedFile.value);
      previewUrl.value = localUrl;
      formData.previewUrl = localUrl;
      
      // 如果接口没有返回fileUrl，使用本地URL作为fileUrl的兜底
      if (!formData.fileUrl) {
        formData.fileUrl = localUrl;
      }
      
      // 为3D模型文件（包括VRP/VRPC）设置文件类型
      if (is3DModel(uploadedFile.value)) {
        formData.fileType = uploadedFile.value.name.split('.').pop()?.toLowerCase() || '';
      }
      
      uploadSuccess.value = true;
      message.warning("文件上传失败，但已生成本地预览");
    } else {
      message.error("文件上传失败");
    }
  }
};

// 预览错误处理
const handlePreviewError = (event: Event) => {
  console.error("预览加载失败:", event);
  message.error("预览加载失败");
};

// 移除文件
const removeFile = () => {
  // 清理本地预览URL
  if (previewUrl.value && previewUrl.value.startsWith("blob:")) {
    URL.revokeObjectURL(previewUrl.value);
  }

  uploadedFile.value = null;
  uploading.value = false;
  uploadProgress.value = 0;
  uploadSuccess.value = false;
  previewUrl.value = "";
  fileList.value = [];

  // 恢复原始文件信息
  if (props.model) {
    formData.fileUrl = props.model.fileUrl;
    formData.previewUrl = props.model.previewUrl;
    currentFileUrl.value = props.model.previewUrl || props.model.fileUrl || "";
  }
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 添加属性
const handleAddProperty = () => {
  const newProperty: Property = {
    id: null,
    modelId: formData.id,
    name: "",
    relationship: "拥有",
    value: ""
  };
  properties.value.push(newProperty);
};

const selectorVisible = ref<boolean>(false);
const handleConfirm = dataArray => {
  dataArray.forEach(item => {
    const newProperty: Property = {
      id: item.id,
      modelId: formData.id,
      name: item.name,
      relationship: "拥有",
      value: item.value
    };
    properties.value.push(newProperty);
  });
  selectorVisible.value = false;
};

const handleSelectProperty = () => {
  selectorVisible.value = true;
};

// 删除属性
const handleDeleteProperty = async (index: number) => {
  // if (properties.value.length <= 1) {
  //   message.warning("至少需要保留一个属性");
  //   return;
  // }
  const property = properties.value[index];
  // 如果属性有ID（从接口返回的数据），需要调用删除接口
  if (property.id !== null && property.id !== undefined) {
    try {
      console.log("🔄 调用删除接口，属性ID:", property.id);
      // await deleteModelAttribute(property.id);

      console.log("✅ 属性删除接口调用成功");
      message.success(`属性"${property.name}"删除成功`);
    } catch (error) {
      console.error("❌ 删除属性接口调用失败:", error);
      message.error("删除属性失败");
      return;
    } finally {
    }
  } else {
    console.log("📝 本地新增属性，直接删除，无需调用接口");
  }
  properties.value.splice(index, 1);
};

// 构建attributeList数组
const buildFieldArray = (): Property[] => {
  const validProperties: Property[] = [];

  properties.value.forEach(prop => {
    if (prop.name.trim() && prop.value.trim() && prop.relationship) {
      validProperties.push({
        id: prop.id, // 编辑时保留原有ID
        modelId: formData.id,
        name: prop.name.trim(),
        relationship: prop.relationship,
        value: prop.value.trim()
      });
    }
  });

  return validProperties;
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();

    // 校验属性表格每一行
    const hasEmptyProperty = properties.value.some(
      prop =>
        !prop.name?.trim() || !prop.value?.trim() || !prop.relationship?.trim()
    );
    if (hasEmptyProperty) {
      message.error("请完善所有属性信息");
      return;
    }

    loading.value = true;

    // 构建提交数据
    const submitData: ModelEditForm = {
      ...formData,
      attributeList: buildFieldArray()
    };

    // 调用更新模型接口
    await updateModel(submitData);
    message.success("模型编辑成功");
    emit("submit", submitData);
    handleCancel();
  } catch (error) {
    console.error("编辑模型失败:", error);
    message.error("编辑模型失败");
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit("update:visible", false);
  // 重置表单
  nextTick(() => {
    formRef.value?.resetFields();
    properties.value = [];
    removeFile();
  });
};

const propertyNameOptions = ref<any[]>([
  { label: "ATA章节", value: "ATA章节" },
  { label: "SNS代码", value: "SNS代码" },
  { label: "件号", value: "件号" }
]);

// 添加属性关系选项
const propertyRelationshipOptions = ref<any[]>([
  { label: "拥有", value: "拥有" },
  { label: "位于", value: "位于" },
  { label: "附有", value: "附有" },
  { label: "包含", value: "包含" },
  { label: "属于", value: "属于" }
]);

const propertyValueOptions = ref<any[]>([]);
// 添加属性选择处理
const handlePropertyNameSelect = (value: string, record: Property) => {
  // 选择下拉选项时，直接设置值
  record.name = value;
  console.log("选择属性名:", value);
};

// 添加属性名变化处理
const handlePropertyNameChange = (value: string, record: Property) => {
  // 值变化时，直接设置到记录中，支持手动输入
  record.name = value;
  console.log("属性名变化:", value);
};

const handlePropertyValueChange = (val: string) => {
  console.log(val);
  value.value = val;
  fetch(val, (d: any[]) => (propertyNameOptions.value = d));
};
const handleSearch = (val: string) => {
  fetch(val, (d: any[]) => (propertyNameOptions.value = d));
};

let timeout: any;
let currentValue = "";
const value = ref();
function fetch(value: string, callback: any) {
  if (timeout) {
    clearTimeout(timeout);
    timeout = null;
  }
  currentValue = value;

  function fake() {
    if (value) {
      const params = {
        pageIndex: 1,
        pageSize: 100,
        value: value
      };
      getModelAttributeList(params).then(d => {
        if (currentValue === value) {
          const result = d.result.records;
          const data: any[] = [];
          result.forEach((r: any) => {
            data.push({
              value: r.id,
              label: r.value
            });
          });
          callback(data);
        }
      });
    }
  }
  timeout = setTimeout(fake, 300);
}
// 添加属性名Enter处理
const handlePropertyNameEnter = (event: KeyboardEvent) => {
  // Enter键处理，可以在这里添加额外的逻辑
  console.log("属性名输入完成");
};

// 添加属性名失焦处理
const handlePropertyNameBlur = (event: Event, record: Property) => {
  const target = event.target as HTMLInputElement;
  if (target && target.value) {
    record.name = target.value;
  }
};

// 添加属性关系选择处理
const handlePropertyRelationshipSelect = (value: string, record: Property) => {
  // 选择下拉选项时，直接设置值
  record.relationship = value;
};

// 添加属性关系变化处理
const handlePropertyRelationshipChange = (value: string, record: Property) => {
  // 值变化时，直接设置到记录中，支持手动输入
  record.relationship = value;
};

// 添加属性关系模糊处理
const handlePropertyRelationshipBlur = (event: Event, record: Property) => {
  // 获取输入框的值
  const target = event.target as HTMLInputElement;
  if (target && target.value) {
    // 确保手动输入的值被保存
    record.relationship = target.value;
  }
};
</script>

<template>
  <a-modal
    :open="visible"
    :confirm-loading="loading"
    width="1200px"
    :destroy-on-close="true"
    class="model-edit-modal"
    @cancel="handleCancel"
  >
    <!-- 自定义标题 -->
    <template #title>
      <div class="modal-title">
        <h3>编辑模型</h3>
      </div>
    </template>

    <div class="modal-content">
      <!-- 左侧文件上传区域 -->
      <div class="upload-area">
        <a-upload-dragger
          v-model:fileList="fileList"
          name="file"
          :multiple="false"
          :before-upload="beforeUploadHandler"
          :show-upload-list="false"
          class="upload-dragger"
          @change="handleFileChange"
        >
          <div class="upload-content">
            <div
              v-if="!uploadedFile && !currentFileUrl"
              class="upload-placeholder"
            >
              <CloudUploadOutlined class="upload-icon" />
              <p class="upload-text">点击或拖拽文件到此区域重新上传</p>
              <p class="upload-hint">支持单个文件上传</p>
            </div>

            <!-- 当前文件预览 -->
            <div
              v-else-if="!uploadedFile && currentFileUrl"
              class="current-file-preview"
            >
              <!-- 图片预览 -->
              <div v-if="isCurrentImage" class="image-preview">
                <img
                  :src="currentFileUrl"
                  :alt="model?.name"
                  class="preview-image"
                  @error="handlePreviewError"
                />
              </div>

              <!-- 视频预览 -->
              <div v-else-if="isCurrentVideo" class="video-preview">
                <video
                  :src="currentFileUrl"
                  class="preview-video"
                  controls
                  preload="metadata"
                  @error="handlePreviewError"
                >
                  您的浏览器不支持视频播放
                </video>
              </div>

              <!-- 3D模型或其他文件 -->
              <div v-else class="file-preview">
                <FileOutlined class="file-preview-icon" />
                <p>当前文件</p>
                <p class="file-name">{{ model?.name }}</p>
                <p class="upload-hint">拖拽新文件到此处替换</p>
              </div>
            </div>

            <!-- 新上传文件预览 -->
            <div v-else class="upload-preview">
              <div class="file-info">
                <FileOutlined class="file-icon" />
                <div class="file-details">
                  <p class="file-name">{{ uploadedFile.name }}</p>
                  <p class="file-size">
                    {{ formatFileSize(uploadedFile.size) }}
                  </p>
                </div>
                <a-button
                  type="text"
                  danger
                  size="small"
                  class="remove-btn"
                  @click.stop="removeFile"
                >
                  <DeleteOutlined />
                </a-button>
              </div>

              <!-- 上传进度 -->
              <div v-if="uploading" class="upload-progress">
                <a-progress :percent="uploadProgress" size="small" />
                <p class="progress-text">上传中...</p>
              </div>

              <!-- 预览区域 -->
              <div
                v-else-if="uploadSuccess && previewUrl"
                class="preview-container"
              >
                <!-- 图片预览 -->
                <div v-if="isImage(uploadedFile)" class="image-preview">
                  <img
                    :src="previewUrl"
                    :alt="uploadedFile.name"
                    class="preview-image"
                    @error="handlePreviewError"
                  />
                </div>

                <!-- 视频预览 -->
                <div v-else-if="isVideo(uploadedFile)" class="video-preview">
                  <video
                    :src="previewUrl"
                    class="preview-video"
                    controls
                    preload="metadata"
                    @error="handlePreviewError"
                  >
                    您的浏览器不支持视频播放
                  </video>
                </div>

                <!-- 3D模型预览 -->
                <div v-else-if="is3DModel(uploadedFile)" class="model-preview">
                  <div class="model-preview-placeholder">
                    <AppstoreOutlined class="model-icon" />
                    <p>3D模型预览</p>
                    <p class="model-url">{{ previewUrl }}</p>
                  </div>
                </div>

                <!-- 其他文件类型 -->
                <div v-else class="file-preview">
                  <FileOutlined class="file-preview-icon" />
                  <p>文件已上传</p>
                  <p class="file-url">{{ previewUrl }}</p>
                </div>
              </div>

              <!-- 上传成功但无预览 -->
              <div v-else-if="uploadSuccess" class="upload-success">
                <CheckCircleOutlined class="success-icon" />
                <p class="success-text">上传成功</p>
              </div>
            </div>
          </div>
        </a-upload-dragger>
      </div>

      <!-- 右侧信息表单区域 -->
      <div class="form-area">
        <!-- 基本信息表单 -->
        <div class="form-section">
          <a-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            :label-col="{ span: 6 }"
          >
            <a-row style="padding: 5px">
              <a-col :span="22">
                <a-form-item label="模型名称" name="name">
                  <a-input
                    v-model:value="formData.name"
                    placeholder="请输入模型名称"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="22">
                <a-form-item label="描述" name="desc">
                  <a-textarea
                    v-model:value="formData.desc"
                    placeholder="请输入描述"
                    :rows="3"
                    :autoSize="{ minRows: 2, maxRows: 6 }"
                    style="resize: none"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <!-- 属性表格 -->
        <div class="properties-section">
          <div class="properties-header">
            <h4>模型属性</h4>
            <!-- 添加属性按钮 -->
            <a-button-group>
              <a-button
                type="primary"
                size="small"
                style="margin-right: 5px"
                class="add-property-btn"
                @click="handleAddProperty"
              >
                <template #icon>
                  <PlusOutlined />
                </template>
                新增
              </a-button>
              <a-button
                type="primary"
                size="small"
                class="add-property-btn"
                @click="handleSelectProperty"
              >
                <template #icon>
                  <SelectOutlined />
                </template>
                选择属性
              </a-button>
            </a-button-group>
          </div>

          <a-table
            :columns="propertyColumns"
            :data-source="properties"
            :pagination="false"
            :scroll="{ y: 360 }"
            size="small"
            class="property-table"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'name'">
                <a-select
                  v-model:value="record.name"
                  :options="propertyNameOptions"
                  placeholder="请输入属性名"
                  size="small"
                  show-search
                  allow-clear
                  :filter-option="true"
                  :not-found-content="null"
                  mode="combobox"
                  style="width: 100%"
                  @select="value => handlePropertyNameSelect(value, record)"
                  @change="value => handlePropertyNameChange(value, record)"
                  @blur="e => handlePropertyNameBlur(e, record)"
                />
              </template>
              <template v-else-if="column.key === 'value'">
                <a-input
                  v-model:value="record.value"
                  placeholder="请输入属性值"
                  size="small"
                />
                <!--                <a-select-->
                <!--                  v-model:value="record.value"-->
                <!--                  :options="propertyValueOptions"-->
                <!--                  placeholder="请输入或选择属性值"-->
                <!--                  size="small"-->
                <!--                  show-search-->
                <!--                  allow-clear-->
                <!--                  :filter-option="false"-->
                <!--                  :not-found-content="null"-->
                <!--                  style="width: 100%"-->
                <!--                  @search="handleSearch"-->
                <!--                  @change="handlePropertyValueChange"-->
                <!--                />-->
              </template>
              <template v-else-if="column.key === 'relationship'">
                <a-select
                  v-model:value="record.relationship"
                  :options="propertyRelationshipOptions"
                  placeholder="请选择或输入关系"
                  size="small"
                  allow-clear
                  :filter-option="true"
                  :not-found-content="null"
                  mode="combobox"
                  style="width: 100%"
                  @select="value => handlePropertyRelationshipSelect(value, record)"
                  @change="value => handlePropertyRelationshipChange(value, record)"
                  @blur="e => handlePropertyRelationshipBlur(e, record)"
                />
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button
                  type="text"
                  danger
                  size="small"
                  @click="handleDeleteProperty(index)"
                >
                  <template #icon>
                    <DeleteOutlined />
                  </template>
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" :loading="loading" @click="handleSubmit">
          保存
        </a-button>
      </div>
    </template>
    <modelattr-selector
      multiple
      :visible="selectorVisible"
      @close="selectorVisible = $event"
      @confirm="handleConfirm"
    />
  </a-modal>
</template>

<style lang="scss" scoped>
.modal-title {
  background-color: var(--gray-100);
  margin: -24px -24px 24px -24px;
  padding: var(--spacing-6);

  h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
    color: var(--gray-900);
    margin: 0;
  }
}

.modal-content {
  display: flex;
  height: 600px;
  gap: var(--spacing-6);
}

.upload-area {
  width: 65%;
  display: flex;
  flex-direction: column;
}

.upload-dragger {
  flex: 1;

  :deep(.ant-upload-drag) {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius-md);
    background-color: var(--gray-50);
    transition: all var(--transition-normal);

    &:hover {
      border-color: var(--primary-color);
      background-color: rgba(24, 144, 255, 0.05);
    }
  }
}

.upload-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-6);
}

.upload-placeholder {
  text-align: center;

  .upload-icon {
    font-size: 48px;
    color: var(--gray-400);
    margin-bottom: var(--spacing-4);
  }

  .upload-text {
    font-size: var(--text-base);
    color: var(--gray-600);
    margin-bottom: var(--spacing-2);
  }

  .upload-hint {
    font-size: var(--text-sm);
    color: var(--gray-500);
    margin: 0;
  }
}

.current-file-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-preview {
  width: 100%;
  text-align: center;
}

.file-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background-color: white;
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-200);
  margin-bottom: var(--spacing-4);

  .file-icon {
    font-size: 24px;
    color: var(--primary-color);
    flex-shrink: 0;
  }

  .file-details {
    flex: 1;
    text-align: left;

    .file-name {
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--gray-900);
      margin: 0 0 4px 0;
      word-break: break-all;
    }

    .file-size {
      font-size: var(--text-xs);
      color: var(--gray-500);
      margin: 0;
    }
  }

  .remove-btn {
    flex-shrink: 0;
    color: var(--error-color);

    &:hover {
      background-color: rgba(255, 77, 79, 0.1);
    }
  }
}

.upload-progress {
  .progress-text {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin-top: var(--spacing-2);
    margin-bottom: 0;
  }
}

.preview-container {
  width: 100%;
  max-height: 400px;
  overflow: hidden;
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-200);
  background-color: white;
}

.image-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .preview-image {
    max-width: 100%;
    max-height: 400px;
    object-fit: contain;
    border-radius: var(--border-radius);
  }
}

.video-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .preview-video {
    max-width: 100%;
    max-height: 400px;
    border-radius: var(--border-radius);
  }
}

.model-preview {
  padding: var(--spacing-8);
  text-align: center;

  .model-preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2);

    .model-icon {
      font-size: 48px;
      color: var(--primary-color);
    }

    p {
      margin: 0;
      color: var(--gray-600);

      &.model-url {
        font-size: var(--text-xs);
        color: var(--gray-500);
        word-break: break-all;
      }
    }
  }
}

.file-preview {
  padding: var(--spacing-8);
  text-align: center;

  .file-preview-icon {
    font-size: 48px;
    color: var(--gray-400);
    margin-bottom: var(--spacing-2);
    // margin-left: var(--spacing-12);
  }

  p {
    margin: 0;
    color: var(--gray-600);

    &.file-name {
      font-weight: var(--font-medium);
      color: var(--gray-900);
      margin-bottom: var(--spacing-2);
    }

    &.file-url {
      font-size: var(--text-xs);
      color: var(--gray-500);
      word-break: break-all;
    }

    &.upload-hint {
      font-size: var(--text-sm);
      color: var(--primary-color);
      margin-top: var(--spacing-2);
    }
  }
}

.upload-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);

  .success-icon {
    font-size: 32px;
    color: var(--success-color);
  }

  .success-text {
    font-size: var(--text-sm);
    color: var(--success-color);
    margin: 0;
  }
}

.form-area {
  width: 35%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.form-section {
  margin-bottom: var(--spacing-6);
}

.form-vertical {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.properties-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: var(--spacing-4);
}

.properties-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);

  h4 {
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    color: var(--gray-900);
    margin: 0;
  }
}

.add-property-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-1);
}

.property-table {
  flex: 1;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-3);
}

:deep(.form-section .ant-row) {
  padding: 5px;
}

// 属性表格样式
:deep(.property-table .ant-table-thead > tr > th) {
  background-color: #eff7fd !important;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
  padding: 8px 12px;
}

:deep(.property-table .ant-table-tbody > tr > td) {
  font-size: var(--text-sm);
  border-bottom: 1px solid var(--gray-200);
  padding: 8px 12px;
}

:deep(.property-table .ant-table-tbody > tr:hover > td) {
  background-color: var(--gray-50);
}

:deep(.property-table .ant-table) {
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

:deep(.property-table .ant-table-container) {
  border: 0;
}

// 表格内输入框和选择器样式
:deep(.property-table .ant-input),
:deep(.property-table .ant-select) {
  border: 0;
  box-shadow: none;
  background-color: transparent;

  &:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    background-color: white;
  }
}

// 属性名输入区域样式
.property-name-input {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.property-name-input .ant-input {
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);

  &:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }
}

.property-name-input .ant-select {
  .ant-select-selector {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    background-color: var(--gray-50);

    &:hover {
      border-color: var(--primary-color);
    }
  }

  &.ant-select-focused .ant-select-selector {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }
}

// 表单样式
:deep(.ant-form-item) {
  margin-bottom: 0;
}

:deep(.ant-form-item-label) {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
}

:deep(.ant-input),
:deep(.ant-textarea) {
  width: 100%;
}

// 删除按钮样式
:deep(.ant-btn-text.ant-btn-dangerous) {
  color: var(--error-color);

  &:hover {
    color: #d32f2f;
    background-color: rgba(255, 77, 79, 0.1);
  }

  &:disabled {
    color: var(--gray-300);

    &:hover {
      color: var(--gray-300);
      background-color: transparent;
    }
  }
}

// 自定义标题样式
:deep(.ant-modal-header) {
  padding: 0;
  border: 0;
}

:deep(.ant-modal-title) {
  padding: 0;
}

// 弹窗样式覆盖
:deep(.model-edit-modal .ant-modal-content) {
  overflow: hidden;
}

:deep(.model-edit-modal .ant-modal-body) {
  padding: var(--spacing-6);
  max-height: 70vh;
  overflow: auto;
}

// 禁用状态样式
.disabled-input {
  :deep(.ant-input),
  :deep(.ant-textarea),
  :deep(.ant-select-selector) {
    background-color: var(--gray-100) !important;
    color: var(--gray-500) !important;
    cursor: not-allowed !important;
    border-color: var(--gray-300) !important;

    &:hover {
      border-color: var(--gray-300) !important;
    }

    &:focus {
      border-color: var(--gray-300) !important;
      box-shadow: none !important;
    }
  }
}
</style>
