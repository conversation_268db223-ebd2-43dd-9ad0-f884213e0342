package com.sbtr.workflow.client;

import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 调用sbtr-form相关接口
 * 本地开发时可以指定 ip+网关端口进行访问  在configuration中配置 url = "http://*************:9774/document"
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
//@FeignClient(name = FeignClientName.FORM_SERVER_NAME, configuration = FeignConfig.class)
public interface FormClient {

    //自定义表单通用保存方法
    @PostMapping("/formField/commonSave")
     Object commonSave(@Param("tableName") String tableName, @RequestParam Map<String, Object> map);
}
