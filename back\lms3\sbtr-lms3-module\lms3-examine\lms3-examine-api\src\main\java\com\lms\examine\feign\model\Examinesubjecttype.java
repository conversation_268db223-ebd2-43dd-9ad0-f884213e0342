package com.lms.examine.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * UExaminesubjecttype entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)

@Data
@TableName("u_examinesubjecttype")
@ApiModel("试卷题型配置对象")
public class Examinesubjecttype extends BaseModel {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("试卷题型唯一标识")
    private String id;

    @ApiModelProperty("试卷id")
    private String examineid;

    @ApiModelProperty("题型id")
    private String subjecttype;

    @ApiModelProperty("数量")
    private Integer qty;

    @ApiModelProperty("分数")
    private Double perpoint;

    @ApiModelProperty("序号")
    private Integer seqno;

    @ApiModelProperty("题型名称")
    @TableField(exist = false)
    private String typename;
}
