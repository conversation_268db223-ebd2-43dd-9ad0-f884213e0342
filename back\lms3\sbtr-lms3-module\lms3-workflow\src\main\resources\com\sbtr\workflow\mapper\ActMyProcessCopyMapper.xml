<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ActMyProcessCopyMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sbtr.workflow.model.ActMyProcessCopy" id="actMyProcessCopyMap">
        <result property="uuid" column="uuid"/>
        <result property="createTime" column="create_time"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorOrgId" column="creator_org_id"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="userNameId" column="user_name_id"/>
        <result property="processInstanceId" column="process_instance_id"/>
        <result property="processDefinitionId" column="process_definition_id"/>
        <result property="formName" column="form_name"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid
        ,
        create_time,
        creator,
        creator_id,
        creator_org_id,
        modifier_id,
        modifier,
        modify_time,
        user_name_id,
        process_instance_id,
        process_definition_id,
        form_name,
        model_key,
        task_id,
        node_id,
        task_name,
        business_key,
        review_status,
        f_get_username ( start_user_id ) AS start_user_id
    </sql>
    <update id="updateReviewStatusByUuid">
        update act_my_process_copy
        set review_status=#{reviewStatus}
        where uuid = #{uuid}
    </update>


    <select id="getPageSetOracle" resultType="com.sbtr.workflow.model.ActMyProcessCopy">
        select
        <include refid="Base_Column_List"></include>
        from act_my_process_copy
        <where>
            <if test=" formName != null and  formName!=''">
                form_name like '%'||#{ formName}||'%' and
            </if>
            instr(','|| user_name_id ||',' , ','|| #{userNameId} || ',') &lt;&gt; 0
            ORDER BY
            create_time DESC
        </where>
    </select>

    <select id="getPageSetSqlServer" resultType="com.sbtr.workflow.model.ActMyProcessCopy">
        select
        uuid,
        create_time,
        creator,
        creator_id,
        creator_org_id,
        modifier_id,
        modifier,
        modify_time,
        user_name_id,
        process_instance_id,
        process_definition_id,
        form_name,
        model_key,
        task_id,
        node_id,
        task_name,
        business_key,
        dbo.f_get_username ( start_user_id ) AS start_user_id
        from act_my_process_copy
        <where>
            <if test=" formName != null and  formName!=''">
                form_name like '%'+#{ formName}+'%' and
            </if>
            CHARINDEX(','+#{userNameId}+',' , ','+user_name_id+',') > 0
            ORDER BY
            create_time DESC
        </where>
    </select>

    <select id="getPageSetMySql" resultType="com.sbtr.workflow.model.ActMyProcessCopy">
        select
        <include refid="Base_Column_List"></include>
        from act_my_process_copy
        <where>
            <if test=" formName != null and  formName!=''">
                form_name like concat('%',#{ formName},'%') and
            </if>
            FIND_IN_SET(#{userNameId},user_name_id)
            ORDER BY
            create_time DESC
        </where>
    </select>

    <select id="getMyPageSet" resultType="com.sbtr.workflow.model.ActMyProcessCopy">
        select
        ampc.uuid,
        ampc.create_time,
        ampc.creator,
        ampc.creator_id,
        ampc.creator_org_id,
        ampc.modifier_id,
        ampc.modifier,
        ampc.modify_time,
        ampc.user_name_id,
        ampc.process_instance_id,
        ampc.process_definition_id,
        ampc.form_name,
        ampc.model_key,
        ampc.task_id,
        ampc.node_id,
        ampc.task_name,
        ampc.business_key,
        ampc.review_status,
        ampc.start_user_id ,
        su.user_name
        from act_my_process_copy ampc left join sys_user su on ampc.user_name_id=su.user_name_id
        <where>
            <if test=" formName != null and  formName!=''">
                ampc.form_name like concat('%',#{ formName},'%') and
            </if>
            ampc.creator_id = #{userNameId}
            ORDER BY
            ampc.create_time DESC
        </where>
    </select>
    <select id="getPageSet" resultType="com.sbtr.workflow.model.ActMyProcessCopy">
        select
        ampc.uuid,
        ampc.create_time,
        ampc.creator,
        ampc.creator_id,
        ampc.creator_org_id,
        ampc.modifier_id,
        ampc.modifier,
        ampc.modify_time,
        ampc.user_name_id,
        ampc.process_instance_id,
        ampc.process_definition_id,
        ampc.form_name,
        ampc.model_key,
        ampc.task_id,
        ampc.node_id,
        ampc.task_name,
        ampc.business_key,
        ampc.review_status,
        ampc.start_user_id ,
        su.user_name
        from act_my_process_copy ampc left join sys_user su on ampc.user_name_id=su.user_name_id
        <where>
            <if test=" formName != null and  formName!=''">
                ampc.form_name =#{formName}  and
            </if>
            ampc.user_name_id = #{userNameId}
            ORDER BY
            ampc.create_time DESC
        </where>
    </select>


    <delete id="executeDeleteBatch">
        delete from act_my_process_copy where uuid in
        <foreach item="uuid" collection="array" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </delete>

    <delete id="deleteByProcessDefinitionId">
        delete
        from act_my_process_copy
        where process_definition_id = #{processDefinitionId}
    </delete>

</mapper>
