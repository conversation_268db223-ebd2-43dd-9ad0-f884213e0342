import type { UploadFile, UploadProps } from "ant-design-vue";

/**
 * 上传文件信息接口
 */
export interface UploadFileInfo {
  uid: string;
  name: string;
  status: "uploading" | "done" | "error" | "removed";
  url?: string;
  thumbUrl?: string;
  size: number;
  type: string;
  originFileObj?: File;
  response?: any;
  percent?: number;
}

/**
 * 上传配置接口
 */
export interface UploadConfig {
  /** 是否支持多文件上传 */
  multiple?: boolean;
  /** 最大文件大小（MB） */
  maxSize?: number;
  /** 接受的文件类型 */
  accept?: string[];
  /** 是否显示预览 */
  showPreview?: boolean;
  /** 是否自动上传 */
  autoUpload?: boolean;
  /** 是否显示上传列表 */
  showUploadList?: boolean;
  /** 是否支持拖拽上传 */
  drag?: boolean;
  /** 上传按钮文本 */
  buttonText?: string;
  /** 拖拽区域文本 */
  dragText?: string;
  /** 文件类型限制 */
  fileTypes?: string[];
  /** 自定义上传函数 */
  customUpload?: (file: File) => Promise<any>;
}

/**
 * 组件Props接口
 */
export interface TrUploadProps extends UploadConfig {
  /** 文件列表 */
  fileList?: UploadFile[];
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否加载中 */
  loading?: boolean;
  /** 占位符文本 */
  placeholder?: string;
  /** 提示文本 */
  hint?: string;
}

/**
 * 组件Emits接口
 */
export interface TrUploadEmits {
  /** 文件列表变化 */
  (e: "update:fileList", fileList: UploadFile[]): void;
  /** 文件变化 */
  (e: "change", info: any): void;
  /** 上传成功 */
  (e: "success", file: UploadFile, response: any): void;
  /** 上传失败 */
  (e: "error", file: UploadFile, error: any): void;
  /** 文件移除 */
  (e: "remove", file: UploadFile): void;
  /** 预览 */
  (e: "preview", file: UploadFile): void;
}

/**
 * 上传结果接口
 */
export interface UploadResult {
  fileUrl: string;
  fileType: string;
  fileName: string;
  fileSize: number;
  previewUrl?: string;
}

/**
 * 文件类型枚举
 */
export enum FileType {
  IMAGE = "image",
  VIDEO = "video",
  MODEL = "model",
  DOCUMENT = "document",
  OTHER = "other"
}

/**
 * 上传方式枚举
 */
export enum UploadMode {
  NORMAL = "normal",
  CHUNK = "chunk"
}
