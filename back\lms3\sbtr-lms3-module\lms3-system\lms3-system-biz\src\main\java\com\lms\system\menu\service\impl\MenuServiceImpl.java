package com.lms.system.menu.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.StringHelper;
import com.lms.system.menu.mapper.MenuMapper;
import com.lms.system.feign.model.Menu;
import com.lms.system.feign.model.MenuModel;
import com.lms.system.menu.service.MenuService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("MenuService")
public class MenuServiceImpl extends BaseServiceImpl<MenuMapper, Menu> implements MenuService {

	@Resource
	private MenuMapper menuMapper;

	public List<Menu> getSubjectList(String roleId) {
		return null;
	}

	public List<MenuModel> getMenuRightTree(String roleId) {
		List<Menu> menuList = menuMapper.getMenuListByRoleId(roleId);
		List<MenuModel> modelList = new ArrayList<MenuModel>();
		MenuModel model = null;
		Map<String, Menu> map = new HashMap<String, Menu>();
		List<Menu> rootList = new ArrayList<Menu>();
		for (Menu menu : menuList) {
			if (!map.containsKey(menu.getId()))
				map.put(menu.getId(), menu);
		}
		for (Menu menu : menuList) {
			if (!StringHelper.isEmpty(menu.getPid())) {
				if (map.containsKey(menu.getPid())) {
					List<Menu> childMenus = map.get(menu.getPid()).getChildMenus();
					childMenus = childMenus==null ? new ArrayList<>() : childMenus;
					childMenus.add(menu);
					map.get(menu.getPid()).setChildMenus(childMenus);
				}
			} else {
				rootList.add(menu);
			}
		}
		for (Menu m : rootList) {
			model = getMenuModelByMenu(m);
			modelList.add(model);
		}

		return modelList;
	}

	private MenuModel getMenuModelByMenu(Menu menu) {

		MenuModel model = new MenuModel();
		model.setId(menu.getId());
		model.setHilightIcon(menu.getHilighticonfile());
		model.setIcon(menu.getIconfile());
		model.setIsShowSub(false);
		String module = commonSystemApi.translateContentNotCreated("菜单","");
		String menuname = commonSystemApi.translateContentNotCreated(menu.getMenuname(), module);
		model.setTitle(menuname);
		if (StringHelper.isEmpty(menu.getUrl())) {
			model.setUrl(menu.getUrl());
			model.setMid(menu.getId());
		} else {
			model.setUrl(menu.getUrl().replaceAll("/", ""));
			model.setMid(menu.getUrl().replaceAll("/", ""));
		}
		if (ObjectUtil.isNotEmpty(menu.getChildMenus())) {
			List<Menu> childs = menu.getChildMenus();
			for (Menu cm : childs) {
				MenuModel childModel = getMenuModelByMenu(cm);
				if (childModel.getChildren().size() == 0) {
					childModel.setIcon("");
					childModel.setHilightIcon("");
				}
				model.getChildren().add(childModel);
			}
		}
		return model;
	}


	public List<String> getMenuTreeByRole(String roleId) {
		Map roleMap = new HashMap();
		List<Menu> menuRoleList = menuMapper.getMenuListByRoleId(roleId);
		List<String> selectedIds = new ArrayList<String>();
		for (Menu m : menuRoleList) {
			selectedIds.add(m.getId());
		}
		return selectedIds;
	}


	public List<MenuModel> getMenuTree() {
		LambdaQueryWrapper<Menu> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(Menu::getStatus, 1);
		wrapper.le(Menu::getMlevel, ContextUtil.getSlevel());
		wrapper.orderByDesc(Menu::getSeqno);
		List<Menu> menuList = this.list(wrapper);
		List<MenuModel> modelList = new ArrayList<MenuModel>();
		MenuModel model = null;
		Map<String, Menu> map = new HashMap<String, Menu>();
		List<Menu> rootList = new ArrayList<Menu>();
		for (Menu menu : menuList) {
			if (!map.containsKey(menu.getId()))
				map.put(menu.getId(), menu);
		}
		for (Menu menu : menuList) {
			if (!StringHelper.isEmpty(menu.getPid())) {
				if (map.containsKey(menu.getPid())) {
					List<Menu> childMenus = map.get(menu.getPid()).getChildMenus();
					childMenus = childMenus==null ? new ArrayList<>() : childMenus;
					childMenus.add(menu);
					map.get(menu.getPid()).setChildMenus(childMenus);
				}
			} else {
				rootList.add(menu);
			}
		}
		for (Menu m : rootList) {
			model = getMenuModelByMenu(m);
			modelList.add(model);
		}

		return modelList;
	}

	public void batchSetDelete(List<String> idList) {
		menuMapper.batchSetDelete(idList);
	}
}
