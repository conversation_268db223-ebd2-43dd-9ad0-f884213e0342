package com.sbtr.workflow.service.impl;

import cn.ewsd.common.utils.BaseUtils;
import cn.ewsd.common.utils.StringUtils;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.mapper.ActMyFlowAgentMapper;
import com.sbtr.workflow.model.ActMyFlowAgent;
import com.sbtr.workflow.service.ActMyFlowAgentService;
import com.sbtr.workflow.service.BusinessSystemDataService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.PageUtil.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

@Service("flowAgentServiceImpl")
public class ActMyFlowAgentServiceImpl extends WorkflowBaseServiceImpl<ActMyFlowAgent, String> implements ActMyFlowAgentService {
    @Autowired
    private ActMyFlowAgentMapper actMyFlowAgentMapper;
    @Autowired
    private BusinessSystemDataService businessSystemDataService;

    @Override
    public PageSet<ActMyFlowAgent> getPageSet(PageParam pageParam, String agentName,String mandatorName,String agent,String mandator) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        Example example = new Example(ActMyFlowAgent.class);
        example.orderBy("createTime").asc();
        Example.Criteria criteria = example.createCriteria();
        if (StrUtil.isNotBlank(agentName)) {
            criteria.andLike("agentName", "%" + agentName + "%");
        }
        if (StrUtil.isNotBlank(agent)) {
            criteria.andEqualTo("agent", agent);
        }
        if (StrUtil.isNotBlank(mandatorName)) {
            criteria.andLike("mandatorName", "%" + mandatorName + "%");
        }
        if (StrUtil.isNotBlank(mandator)) {
            criteria.andEqualTo("mandator", mandator);
        }
        List<ActMyFlowAgent> list = selectByExample(example);
        PageInfo<ActMyFlowAgent> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public int executeDeleteBatch(String[] uuids){
        return actMyFlowAgentMapper.executeDeleteBatch(uuids);
    }

    @Override
    public List<String> getMandatorsByAgent(String agent,String currentTime) {
        return actMyFlowAgentMapper.getMandatorsByAgent(agent,currentTime);
    }

    @Override
    public Integer save(ActMyFlowAgent flowAgent) {
        String currentUser = businessSystemDataService.getUserNameId();
        String currentUserName = businessSystemDataService.getUserName();
        if (StringUtils.isNullOrEmpty(flowAgent.getUuid())) {
            flowAgent.setUuid(BaseUtils.UUIDGenerator());
        }
        if(StringUtils.isNullOrEmpty(flowAgent.getCreatorId()))
            flowAgent.setCreatorId(currentUser);
        if(StringUtils.isNullOrEmpty(flowAgent.getCreator()))
            flowAgent.setCreator(currentUserName);
        if(StringUtils.isNullOrEmpty(flowAgent.getMandator()))
            flowAgent.setMandator(currentUser);
        if(StringUtils.isNullOrEmpty(flowAgent.getMandatorName()))
            flowAgent.setMandatorName(currentUserName);
        if(flowAgent.getCreatorOrgId()==null)
            flowAgent.setCreatorOrgId(Integer.parseInt(businessSystemDataService.getOrgId()));
        flowAgent.setCreateTime(new Date());
//        String currentDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        int result = actMyFlowAgentMapper.insertSelective(flowAgent);
        return result;
    }

}
