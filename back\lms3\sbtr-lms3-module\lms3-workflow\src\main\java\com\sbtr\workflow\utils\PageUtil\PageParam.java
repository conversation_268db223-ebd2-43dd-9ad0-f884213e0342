package com.sbtr.workflow.utils.PageUtil;

import java.util.Map;

/**
 * 封装获取前端分页数据
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022-05-18 13:40:36
 */
public class PageParam {

    /**
     * 第一页
     */
    private int pageNo = 1;

    /**
     * 每页数据
     */
    private int pageSize = 20;

    /**
     * 排序字段
     */
    private String sort;

    /**
     * 降序、升序
     */
    private String order;


    public PageParam() {
    }

    public PageParam(int pageNo, int pageSize) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    @Override
    public String toString() {
        return "PageParam{" +
                "pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", sort='" + sort + '\'' +
                ", order='" + order + '\'' +
                '}';
    }
}
