<template>
  <div class="bpmn-editor-container">
    <div class="toolbar">
      <a-button @click="zoom(0.05)"><ZoomInOutlined />放大</a-button>
      <a-button @click="zoom(-0.05)"><ZoomOutOutlined />缩小</a-button>
      <a-button @click="fitViewport"><PicCenterOutlined />自适应屏幕</a-button>
    </div>

    <div class="editor-wrapper">
      <div ref="container" class="canvas" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, toRaw } from "vue";
import BpmnModeler from "bpmn-js/lib/Modeler";
import "bpmn-js/dist/assets/diagram-js.css";
import "bpmn-js/dist/assets/bpmn-font/css/bpmn.css";
// 右边工具栏样式
import "bpmn-js-properties-panel/dist/assets/properties-panel.css";
import customControlsModule from "./customControls";
import {
  BpmnPropertiesPanelModule,
  BpmnPropertiesProviderModule
} from "bpmn-js-properties-panel";
import {
  customTranslate,
  moduleExtension
} from "@/views/mrPlatform/courseware/workflow/data.ts";
import { getFlowchart } from "@/api/workflow.ts";
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  PicCenterOutlined
} from "@ant-design/icons-vue";
const container = ref(null);
const modeler = ref(null);
const scale = ref(1);
const props = defineProps({
  cid: String
});
const isEditMode = ref(false);
const modelKey = "Process_" + props.cid;
// 保存从接口获取的流程模型数据
const flowModelData = ref(null);

// 初始化建模器
onMounted(() => {
  initDiagram();
});
// 清理资源
onBeforeUnmount(() => {
  if (modeler.value) {
    modeler.value.destroy();
  }
});

const initDiagram = () => {
  if (container.value) {
    modeler.value = new BpmnModeler({
      container: container.value,
      additionalModules: [
        customControlsModule,
        BpmnPropertiesPanelModule,
        BpmnPropertiesProviderModule
      ]
    });
    loadProcessDefinitions();
  }
};

// 加载流程定义列表
const loadProcessDefinitions = async () => {
  try {
    const response = await getFlowchart(modelKey);
    if (response.success) {
      // 保存完整的flowModel数据
      flowModelData.value = response.result.flowModel;
      const dataXml = flowModelData.value.flowDesign;
      if (dataXml !== null && dataXml.length > 0) {
        await modeler.value.importXML(dataXml);
      }
      isEditMode.value = true;
    } else {
      await modeler.value.createDiagram();
    }
  } catch (error) {
    console.error("Error loading process definitions:", error);
  }
};

const fitViewport = () => {
  // this.zoom = modeler.value.get("canvas").zoom("fit-viewport");
  const bbox = document.querySelector(".viewport").getBBox();
  const currentViewbox = modeler.value.get("canvas").viewbox();
  const elementMid = {
    x: bbox.x + bbox.width / 2 - 65,
    y: bbox.y + bbox.height / 2
  };
  modeler.value.get("canvas").viewbox({
    x: elementMid.x - currentViewbox.width / 2,
    y: elementMid.y - currentViewbox.height / 2,
    width: currentViewbox.width,
    height: currentViewbox.height
  });
  // this.zoom = (bbox.width / currentViewbox.width) * 1.8;
};

// 放大缩小
const zoom = val => {
  let newScale = !val
    ? 1.0
    : scale.value + val <= 0.2
      ? 0.2
      : scale.value + val;
  modeler.value.get("canvas").zoom(newScale);
  scale.value = newScale;
};
</script>

<style scoped>
.bpmn-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.toolbar {
  display: flex;
  padding: 10px;
  background: #ffffff;
  border-bottom: 1px solid #ddd;
}

.toolbar button,
.toolbar select {
  margin-right: 10px;
  padding: 5px 10px;
}

/* 新增样式 */
.editor-wrapper {
  position: relative;
  flex: 1;
  height: 500px;
  border: 1px solid #ccc;
}

.canvas {
  height: 100%;
}

.xml-viewer {
  height: 30%;
  padding: 10px;
  border-top: 1px solid #ddd;
}

.xml-viewer textarea {
  width: 100%;
  height: calc(100% - 30px);
  font-family: monospace;
}

:deep(svg:focus, svg:active) {
  outline: none !important; /* 清除黑色边框 */
  -webkit-tap-highlight-color: transparent; /* 清除移动端点击高亮 */
}
:deep(.bjs-container) {
  border-right: 1px solid #ccc;
}

:deep(.bio-properties-panel-container) {
  display: none;
}
:deep(.bjs-powered-by) {
  display: none;
}

:deep(.djs-palette) {
  display: none !important;
}
:deep(.bpp-properties-panel) {
  display: none !important;
}
</style>
