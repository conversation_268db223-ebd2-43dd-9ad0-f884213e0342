package com.lms.base.feign.fallback;

import com.lms.base.feign.api.LmsBaseApi;
import com.lms.base.feign.model.Courseware;
import com.lms.base.feign.model.Subject;
import com.lms.common.feign.dto.Department;
import com.lms.common.feign.dto.Person;
import com.lms.common.feign.dto.Selectitem;
import com.lms.common.model.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * misboot-mdata相关接口 模块降级处理
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
@Component
public class LmsBaseApiFallbackFactory implements FallbackFactory<LmsBaseApi> {

    private static final Logger log = LoggerFactory.getLogger(LmsBaseApiFallbackFactory.class);

    @Override
    public LmsBaseApi create(Throwable throwable) {
        log.error("LmsBaseApi : 系统管理远程服务调用异常 {}", throwable.getMessage());
        return new LmsBaseApi() {


            @Override
            public Result<List<String>> getUserNamesBySysRoleLink(String roleid) {
                return null;
            }

            @Override
            public Result<Person> findPersonByCardNum(String cardNum) {
                return null;
            }

            @Override
            public Result<List<String>> getRoleIdByUserName(String userName) {
                return null;
            }

            @Override
            public Result<List<Courseware>> getCoursewareList(String componentid) {
                return null;
            }

            @Override
            public Result<Courseware> getCourseware(String coursewareid) {
                return null;
            }

            @Override
            public Result<List<Courseware>> getCourseWareByCourseIdList(List<String> idList) {
                return null;
            }

            @Override
            public Result<Boolean> existName(String id, String objname, String typeid) {
                return null;
            }

            @Override
            public Result<Selectitem> getSelectitemByTypeName(String name, String typeid) {
                return null;
            }

            @Override
            public Result<List<Selectitem>> getByIdList(List<String> idList) {
                return null;
            }

            @Override
            public Result<List<Selectitem>> getSelectitemByIdWidthEmpty(String typeid) {
                return null;
            }

            @Override
            public Result<Map> readExcelFile(String path) {
                return null;
            }

            @Override
            public Result<Subject> getSubjectById(String id) {
                return null;
            }

            @Override
            public List<String> getRandomIds(HashMap map) {
                return Collections.emptyList();
            }

            @Override
            public Result<List<Department>> getDepartmentByIdList(List<String> idList) {
                return null;
            }

            @Override
            public Result<String> calcSubjectKKD(List list) {
                return null;
            }

        };
    }
}
