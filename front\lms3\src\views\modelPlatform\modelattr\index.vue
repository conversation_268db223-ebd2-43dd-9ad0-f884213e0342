<template>
  <div class="table-page">
    <TrTable
      ref="sbl"
      :columns="columns"
      :loading="loading"
      :dataSource="dataSource"
      :queryItems="queryItems"
      :show-view="false"
      :showExport="false"
      @import="handleImport"
      @query="handleSearch"
      @add="handleAdd"
      @edit="handleEdit"
      @delete="handleDel"
      @batchDelete="handleDel"
    />
    <add-dialog
      :visible="addFormVisible"
      :object="rowData"
      @submit="handleSearch"
      @close="addFormVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import TrTable from "@/components/TrTable/index.vue";
import AddDialog from "./modules/adddialog.vue";
import { message, Modal } from "ant-design-vue";
import { translateText } from "@/utils/translation";
import { queryItems, columns } from "./data.ts";
import { getModelAttributeList, deleteModelAttribute } from "@/api/model.ts";

const addFormVisible = ref(false);
// 表格组件实例
const sbl = ref<InstanceType<typeof TrTable>>();

const loading = ref(false);
const dataSource = ref([]);
const rowData = ref({});
function handleImport() {
  importFormVisible.value = true;
}
function handleAdd() {
  rowData.value = null;
  addFormVisible.value = true;
}
function handleEdit(row) {
  addFormVisible.value = true;
  rowData.value = row;
}
function handleDel(rows) {
  if (rows instanceof Array && rows.length < 1) {
    message.warning(translateText("请选择要删除的数据！"));
  } else {
    Modal.confirm({
      title: translateText("提示信息"),
      content: translateText("确认删除选中记录吗？"),
      onOk() {
        loading.value = true;
        if (rows instanceof Array) {
          batchRemove(rows);
        } else {
          let array = [];
          array.push(rows);
          batchRemove(array);
        }
      }
    });
  }
}

async function batchRemove(rows) {
  try {
    let ids = rows.map(item => item.id).toString();
    let para = { ids: ids };
    let result = await deleteModelAttribute(para);
    if (result.success) {
      message.success(result.message);
      await handleSearch();
    } else {
      message.error(result.message);
    }
  } catch (error) {
    console.error(error);
    message.error(error);
  } finally {
    loading.value = false;
  }
}

async function handleSearch(form) {
  try {
    loading.value = true;
    let tableRef = sbl.value.getTableState();
    let para = {
      pageIndex: tableRef.pagination.pageIndex,
      pageSize: tableRef.pagination.pageSize,
      orderName: tableRef.sort.field,
      sortType: tableRef.sort.order
    };
    para = Object.assign(para, form);
    const response = await getModelAttributeList(para);
    if (response.success) {
      dataSource.value = response.result.records;
      sbl.value.setTotal(response.result.total);
      sbl.value.clearSelection();
    } else {
      message.error(response.message);
    }
  } catch (error) {
    console.error(error);
    message.error(error);
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
/* 让页面容器占用100%高度 */
.table-page {
  height: 100%;
}
</style>
