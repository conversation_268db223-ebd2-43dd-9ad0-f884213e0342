package com.sbtr.workflow.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Id;
import java.util.Date;

public class ModleDto {
    // act_my_model 主键
    @Id
    private String uuid;
    private String modifierId;
    private String modifier;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date modifyTime;
    // 模型id（关联act_re_model）
    private String actDeModelId;
    // 表单设计
    private String formDesign;
    // 流程设计
    private String flowDesign;
    // 模型key
    private String actDeModelKey;
    // 数据表名
    private String formTableName;
    // 表属性
    private String formModel;
    // 模型name
    private String actDeModelName;
    //流程按钮
    private String formBtnList;
    //流程节点
    private String formFieldList;
    //流程定义Id
    private String procdefId;
    //流程分类Id
    private String category;
    //节点通知
    private String formNoticeList;

    //判断是自己写的表单还是通过拖动出来的表单  1自己写的表单  2拖动设计的
    private String modelType;

    private String nodeCodeList;
    //启动权限类型
    private String  permissionType;
    //启动权限值
    private String  permissionValue;


    // 流程模型类型  1 自定义流程界面  2 托拉拽界面
    private String processModelType;

    private String actFormConfigureUuid;
    private String sign;
    public String getActFormConfigureUuid() {
        return actFormConfigureUuid;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public void setActFormConfigureUuid(String actFormConfigureUuid) {
        this.actFormConfigureUuid = actFormConfigureUuid;
    }

    public String getProcessModelType() {
        return processModelType;
    }

    public void setProcessModelType(String processModelType) {
        this.processModelType = processModelType;
    }

    public String getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(String permissionType) {
        this.permissionType = permissionType;
    }

    public String getPermissionValue() {
        return permissionValue;
    }

    public void setPermissionValue(String permissionValue) {
        this.permissionValue = permissionValue;
    }

    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public String getFormNoticeList() {
        return formNoticeList;
    }

    public void setFormNoticeList(String formNoticeList) {
        this.formNoticeList = formNoticeList;
    }

    public String getProcdefId() {
        return procdefId;
    }

    public void setProcdefId(String procdefId) {
        this.procdefId = procdefId;
    }

    public String getFormFieldList() {
        return formFieldList;
    }

    public void setFormFieldList(String formFieldList) {
        this.formFieldList = formFieldList;
    }

    public String getFormBtnList() {
        return formBtnList;
    }

    public void setFormBtnList(String formBtnList) {
        this.formBtnList = formBtnList;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getModifierId() {
        return modifierId;
    }

    public void setModifierId(String modifierId) {
        this.modifierId = modifierId;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getActDeModelId() {
        return actDeModelId;
    }

    public void setActDeModelId(String actDeModelId) {
        this.actDeModelId = actDeModelId;
    }

    public String getFormDesign() {
        return formDesign;
    }

    public void setFormDesign(String formDesign) {
        this.formDesign = formDesign;
    }

    public String getFlowDesign() {
        return flowDesign;
    }

    public void setFlowDesign(String flowDesign) {
        this.flowDesign = flowDesign;
    }

    public String getActDeModelKey() {
        return actDeModelKey;
    }

    public void setActDeModelKey(String actDeModelKey) {
        this.actDeModelKey = actDeModelKey;
    }

    public String getFormTableName() {
        return formTableName;
    }

    public void setFormTableName(String formTableName) {
        this.formTableName = formTableName;
    }

    public String getFormModel() {
        return formModel;
    }

    public void setFormModel(String formModel) {
        this.formModel = formModel;
    }

    public String getActDeModelName() {
        return actDeModelName;
    }

    public void setActDeModelName(String actDeModelName) {
        this.actDeModelName = actDeModelName;
    }

    public String getNodeCodeList() {
        return nodeCodeList;
    }

    public void setNodeCodeList(String nodeCodeList) {
        this.nodeCodeList = nodeCodeList;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
}
