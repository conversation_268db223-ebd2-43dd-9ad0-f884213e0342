package com.sbtr.workflow.mapper;

import com.sbtr.workflow.bean.WorkflowMapper;
import com.sbtr.workflow.dto.ProcessInstanceDto;
import com.sbtr.workflow.model.*;
import com.sbtr.workflow.vo.TaskVo;
import org.apache.ibatis.annotations.Param;
import org.flowable.ui.modeler.domain.AbstractModel;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface MyVariableMapper extends WorkflowMapper<MyVariable> {

    List<MyVariable> getPageSet(@Param("filterSort") String filterStr);

    MyVariable getVariable(String processKey, String variableName);

    List<MyVariable> getListByProcessKey(@Param("processkey") String processkey);

    List<MyVariable> getVariables(String processKey);

    List<User> getListByUserNamdIdOrUserName(@Param("filter") String filter);

    List<Organization> getListByText(@Param("filter")String filter);

    List<AbstractModel> getModelsByModelType(@Param("filterSort")String filterSort,@Param("modelType") int modelType);

    Integer updateAssigneeById(@Param("userNameId")String userNameId,@Param("id") String id);

    TaskCommon getListByExecutionId(@Param("executionId")String executionId);

    HashMap<String, String> getAssings(@Param("executionId")String executionId);

    Integer updateAssignee(@Param("assignee")String assignee, @Param("id")String id);

    TaskCommon getDetailsByKey(@Param("id")String id);

    int updateTaskCommonOpeningTimeByKey(@Param("id")String id, @Param("date")Date date);

    int updateActHiActinst(@Param("taskId")String taskId, @Param("date")Date date);

    Map<String,Object> getActRuTaskByProcInstId(@Param("procInstId")String procInstId);

    int delectActRuTaskByKey(@Param("id") String id);

    int addActRuTask(@Param("map") Map<String, Object> map);

    List<Map<String,Object>> getActHiActinstByProcInstId(@Param("procInstId")String procInstId,@Param("actType") String actType);

    List<TaskCommon> taskQuery(@Param("processDefinitionName")String  processDefinitionName,@Param("assignee") String assignee,@Param("processModelType") String processModelType);

    List<Map<String, Object>> getTitle(@Param("userNameId")String userNameId);

    List<ActHiActinst> getAllByProcInstIdByActType(@Param("procInstId")String procInstId, @Param("actType")String actType);

    List<Map<String, Object>> getListBydisActivityId(@Param("disActivityId")String disActivityId, @Param("processInstanceId")String processInstanceId);

    List<Map<String, Object>> getListBydisEndTime(@Param("endTime")String endTime, @Param("processInstanceId")String processInstanceId);

    Integer deleteRunActinstsByIds(@Param("list")List<String> list);

    Integer deleteHisActinstsByIds(@Param("list")List<String> list);

    List<ProcessInstanceDto> getMyNoEndProcessPageSetData(@Param("modelName")String modelName,
                                                          @Param("userNameId") String userNameId,
                                                          @Param("formName") String formName,
                                                          @Param("processModelType") String processModelType);

    List<TaskVo> getApplyedTasks(@Param("userNameId")String userNameId,
                                 @Param("processDefinitionName") String processDefinitionName,
                                 @Param("modelName") String processDefinitionName1,
                                 @Param("processModelType") String processModelType);

    int updateBusinessFlowStateByUuidAndTableNameStr(@Param("uuid") String uuid, @Param("flowState")String flowState, @Param("tableName") String tableName);
}
