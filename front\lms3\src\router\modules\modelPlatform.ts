// import { $t } from "@/plugins/i18n";

export default {
  path: "/modelPlatform",
  redirect: "/modelPlatform/management",
  meta: {
    icon: "mdi:database",
    // showLink: false,
    title: "模型资产管理平台",
    rank: 1
  },
  children: [
    {
      path: "/modelPlatform/management",
      name: "model",
      component: () => import("@/views/modelPlatform/model/index.vue"),
      meta: {
        // title: $t("menus.pureFourZeroOne"),
        title: "模型素材管理",
        showParent: true
      }
    },
    {
      path: "/modelPlatform/attribute",
      name: "modelattr",
      component: () => import("@/views/modelPlatform/modelattr/index.vue"),
      meta: {
        title: "模型属性管理",
        showParent: true
      }
    },
    {
      path: "/modelPlatform/subject",
      name: "subject",
      component: () => import("@/views/modelPlatform/subject/index.vue"),
      meta: {
        title: "题库管理",
        showParent: true
      }
    }
  ]
} satisfies RouteConfigsTable;
