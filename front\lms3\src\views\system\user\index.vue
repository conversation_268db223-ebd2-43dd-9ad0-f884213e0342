<template>
  <div class="user-management">
    <!-- 页面标题 -->
    <!-- <div class="page-header">
      <h1 class="page-title">用户管理</h1>
    </div> -->

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-item">
          <label class="filter-label">{{ translateText("姓名") }}</label>
          <a-input
            v-model:value="filters.personname"
            :placeholder="translateText('请输入姓名')"
            class="filter-input"
            allow-clear
          />
        </div>

        <div class="filter-item">
          <label class="filter-label">{{ translateText("账号") }}</label>
          <a-input
            v-model:value="filters.name"
            :placeholder="translateText('请输入账号名')"
            class="filter-input"
            allow-clear
          />
        </div>

        <div class="filter-actions">
          <a-button class="reset-btn" @click="handleReset">{{ translateText("重置") }}</a-button>
          <a-button
            type="primary"
            class="search-btn"
            :loading="loading"
            @click="handleSearch"
          >
            {{ translateText("查询") }}
          </a-button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <div class="table-container">
        <a-table
          :columns="columns"
          :data-source="users"
          :loading="loading"
          :pagination="false"
          :row-selection="rowSelection"
          row-key="id"
          size="middle"
          class="user-table"
        >
          <!-- 序号列 -->
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'index'">
              {{ (pagination.current - 1) * pagination.size + index + 1 }}
            </template>

            <!-- 账号状态 -->
            <template v-else-if="column.key === 'islocked'">
              <a-tag :color="getStatusColor(record.enable)">
                {{ record.islocked ? translateText("锁定") : translateText("正常") }}
              </a-tag>
            </template>

            <!-- 是否启用 -->
            <template v-else-if="column.key === 'enable'">
              <a-tag :color="record.enable ? 'green' : 'red'">
                {{ record.enable ? translateText("启用") : translateText("未启用") }}
              </a-tag>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'action'">
              <a-button type="link" size="small" @click="handleEdit(record)">
                {{ translateText("编辑") }}
              </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 分页栏 -->
    <div v-if="!loading && users.length > 0" class="pagination-section">
      <div class="pagination-container">
        <a-pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="
            total =>
              translateText('当前第{current}/{total}页', {
                current: pagination.current,
                total: Math.ceil(total / pagination.size)
              })
          "
          :page-size-options="['10', '20', '50', '100']"
          :locale="paginationLocale"
          @change="handlePageChange"
          @show-size-change="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 新增/编辑用户弹窗 -->
    <UserAddForm
      :visible="addModalVisible"
      :user="currentUser"
      :is-edit="isEdit"
      @close="addModalVisible = $event"
      @submit="fetchUserList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { message } from "ant-design-vue";
import UserAddForm from "./modules/addForm.vue";
import type { User, UserFilter } from "@/types/system";
import { getUserList } from "@/api/system.ts";
import { paginationLocale } from "@/plugins/i18n";
import { translateText } from "@/utils/translation";
const loading = ref(false);
const users = ref<User[]>([]);
const addModalVisible = ref(false);
const currentUser = ref<User | null>(null);
const isEdit = ref(false);
const selectedRowKeys = ref<number[]>([]);
const accountOptions = ref<string[]>([]);

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
  pages: 0
});

// 筛选条件
const filters = reactive<UserFilter>({
  personname: "",
  name: ""
});

// 表格列定义
const columns = [
  {
    title: translateText("序"),
    key: "index",
    width: 60,
    align: "center"
  },
  {
    title: translateText("账号"),
    dataIndex: "name",
    key: "name",
    width: 120
  },
  {
    title: translateText("姓名"),
    dataIndex: "personname",
    key: "personname",
    width: 100
  },
  {
    title: translateText("单位"),
    dataIndex: "deptname",
    key: "deptname",
    width: 120
  },
  {
    title: translateText("角色"),
    dataIndex: "rolename",
    key: "rolename",
    width: 120
  },
  {
    title: translateText("账号状态"),
    dataIndex: "islocked",
    key: "islocked",
    width: 100,
    align: "center"
  },
  {
    title: translateText("是否启用"),
    dataIndex: "enable",
    key: "enable",
    width: 100,
    align: "center"
  },
  {
    title: translateText("IP地址"),
    dataIndex: "ipaddr",
    key: "ipaddr",
    width: 120
  },
  {
    title: translateText("操作"),
    key: "action",
    width: 80,
    align: "center"
  }
];

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (newSelectedRowKeys: number[]) => {
    selectedRowKeys.value = newSelectedRowKeys;
  }
};

// 获取状态颜色
const getStatusColor = (islocked: boolean) => {
  if (!islocked) {
    return "green";
  } else {
    return "red";
  }
};

// 获取用户列表
const fetchUserList = async () => {
  try {
    loading.value = true;
    let params = {
      pageIndex: pagination.current,
      pageSize: pagination.size,
      PARAMETER_S_Like_name: filters.name,
      PARAMETER_S_Like_personname: filters.personname,
      PARAMETER_I_EQ_status: 1,
      orderName: "name",
      sortType: "asc"
    };
    const response = await getUserList(params);
    if (response.success) {
      users.value = response.result.records;
      pagination.total = response.result.total;
      pagination.pages = response.result.pages;
    } else {
      message.error(response.message || translateText("获取用户信息列表失败"));
    }
  } catch (error) {
    console.error("获取用户列表失败:", error);
    message.error(translateText("获取用户列表失败"));
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchUserList();
};

// 重置筛选条件
const handleReset = () => {
  Object.assign(filters, {
    personname: "",
    name: ""
  });
  pagination.current = 1;
  fetchUserList();
};

// 编辑用户
const handleEdit = (user: User) => {
  currentUser.value = user;
  isEdit.value = true;
  addModalVisible.value = true;
};

// 处理新增/编辑提交
const handleAddSubmit = async (formData: any) => {
  try {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log("用户数据:", formData);
    message.success(isEdit.value ? translateText("用户编辑成功") : translateText("用户新增成功"));
    // 刷新列表
    fetchUserList();
  } catch (error) {
    console.error("操作失败:", error);
    message.error(translateText("操作失败"));
  }
};

// 分页变化
const handlePageChange = (page: number, pageSize: number) => {
  pagination.current = page;
  pagination.size = pageSize;
  fetchUserList();
};

// 页面大小变化
const handlePageSizeChange = (current: number, size: number) => {
  pagination.current = 1;
  pagination.size = size;
  fetchUserList();
};

// 页面加载时获取数据
onMounted(() => {
  fetchUserList();
});
</script>

<style lang="scss" scoped>
.user-management {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--gray-50);
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.page-header {
  padding: var(--spacing-6);
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
}

.page-title {
  font-size: var(--text-xl);
  font-weight: var(--font-medium);
  color: var(--gray-900);
  margin: 0;
}

.filter-section {
  padding: var(--spacing-6);
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
}

.filter-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.filter-label {
  font-size: var(--text-sm);
  color: var(--gray-600);
  white-space: nowrap;
  min-width: 40px;
}

.filter-input {
  width: 200px;
}

.filter-select {
  width: 200px;
}

.filter-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-left: auto;
}

.search-btn,
.reset-btn {
  padding: 0 var(--spacing-6);
}

.table-section {
  flex: 1;
  overflow: auto;
  background-color: white;
}

.table-container {
  padding: var(--spacing-6);
}

.pagination-section {
  padding: var(--spacing-1);
  background-color: white;
  border-top: 1px solid var(--gray-200);
}

.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 表格样式
:deep(.user-table .ant-table-thead > tr > th) {
  background-color: #eff7fd !important;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
  padding: 12px 16px;
}

:deep(.user-table .ant-table-tbody > tr > td) {
  font-size: var(--text-sm);
  color: var(--gray-600);
  border-bottom: 1px solid var(--gray-200);
  padding: 12px 16px;
}

:deep(.user-table .ant-table-tbody > tr:hover > td) {
  background-color: var(--gray-50);
}

:deep(.user-table .ant-table) {
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

:deep(.user-table .ant-table-container) {
  border: 0;
}

// 分页样式
:deep(.ant-pagination) {
  .ant-pagination-item {
    border-color: var(--gray-300);

    &:hover {
      border-color: var(--primary-color);
    }

    &.ant-pagination-item-active {
      background-color: var(--primary-color);
      border-color: var(--primary-color);

      // 选中页文字颜色调整为白色
      a {
        color: white !important;
      }
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    border-color: var(--gray-300);

    &:hover {
      border-color: var(--primary-color);
    }
  }
}

// 标签样式
:deep(.ant-tag) {
  border-radius: var(--border-radius-sm);
  font-size: var(--text-xs);
  padding: 2px 8px;
}

// 链接按钮样式
:deep(.ant-btn-link) {
  color: var(--primary-color);
  padding: 0;
  height: auto;

  &:hover {
    color: var(--primary-hover);
  }
}
</style>
