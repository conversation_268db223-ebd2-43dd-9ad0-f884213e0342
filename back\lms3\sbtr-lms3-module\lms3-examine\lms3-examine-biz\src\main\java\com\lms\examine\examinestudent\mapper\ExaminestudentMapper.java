package com.lms.examine.examinestudent.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.examine.feign.model.Examinestudent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

@Mapper
public interface ExaminestudentMapper extends BaseMapper<Examinestudent> {

	@Select("select * from u_examinestudent e where e.status= 1 and e.examinestatus<>'0'")
	List<Examinestudent> getValidExaminestudents();

	@Select(value = "update u_examinestudent set examinestatus = '1' where examineid in (:ids) and examinestatus = '0'")
	void batchReleaseExaminepage(@Param(value = "ids") List<String> ids);

	@Select("select from u_examinestudent where examineid = ?1 and status=1")
	List<Examinestudent> getExaminestudentsByExamId(String id);

	@Select("select from u_examinestudent s where s.personid = ?1 and s.examineid= ?2")
	List<Examinestudent> getStudentByExamine(String personid,String examineid);

	@Select("select from u_examinestudent where examineid= ?1 and personid= ?1 and status=1 and result<>'17ebb78046e44967a5c3bc9338342062' order by score desc")
	List<Examinestudent> getLearnStatusById(String examId,String personId);

	@Select("${sql}")
    List<Map<String, Object>> listSQLQuery(@Param("sql") String sql);
}
