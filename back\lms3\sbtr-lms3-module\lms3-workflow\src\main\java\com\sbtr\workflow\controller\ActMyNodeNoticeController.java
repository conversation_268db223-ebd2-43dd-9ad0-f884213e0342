package com.sbtr.workflow.controller;

import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import com.sbtr.workflow.annotation.Log;
import com.sbtr.workflow.enums.BusinessType;
import com.sbtr.workflow.enums.LogModule;
import com.sbtr.workflow.constants.CommonConstants;
import com.sbtr.workflow.model.ActMyNodeNotice;
import com.sbtr.workflow.service.ActMyNodeNoticeService;
import com.sbtr.workflow.utils.BaseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
/**
 * 流程每个节点所对应通知
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-12-09 11:58:35
 */
@Api(tags = {"流程每个节点所对应通知" })
@Controller
@RequestMapping(value = CommonConstants.WORKFLOW_CONTEXT_PATH+"/flowNodeNotice")
public class ActMyNodeNoticeController extends WorkflowBaseController {

    @Autowired
    private ActMyNodeNoticeService flowNodeNoticeService;


    @ApiOperation(value = "获得FlowNodeNotice分页集数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "rows", value = "一页显示多少条记录", defaultValue = "20", required = true, dataType = "Int"),
            @ApiImplicitParam(name = "sort", value = "排序", defaultValue = "createTime", required = true, dataType = "String"),
            @ApiImplicitParam(name = "order", value = "排序规则", defaultValue = "desc", required = true, dataType = "String"),
    })
    @ResponseBody
    @RequestMapping(value = "/getPageSet", method = RequestMethod.POST)
    public Object getPageSet(PageParam pageParam) {
        String filterSort = "" ;
        filterSort = BaseUtils.filterSort(request, filterSort );
        PageSet<ActMyNodeNotice> pageSet = flowNodeNoticeService.getPageSet(pageParam, filterSort);
        return pageSet;
    }

    @ApiOperation(value = "获得FlowNodeNotice模块详细数据")
    @ApiImplicitParam(name = "uuid", value = "获得FlowNodeNotice模块详细数据", required = true, dataType = "String")
    @ResponseBody
    @RequestMapping(value = "/getDetailByUuid")
    public Object getDetailByUuid(String uuid) {
        ActMyNodeNotice flowNodeNotice =flowNodeNoticeService.selectByPrimaryKey(uuid);
        return flowNodeNotice;
    }


    @Log(title = "流程节点通知-保存FlowNodeNotice模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.INSERT, method = "getLogMessage")
    @ApiOperation(value = "保存FlowNodeNotice模块数据")
    @ApiImplicitParam(name = "flowNodeNotice", value = "保存FlowNodeNotice模块数据", required = true, dataType = "FlowNodeNotice")
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Object save(@ModelAttribute ActMyNodeNotice flowNodeNotice) {
        setLogMessage(flowNodeNotice.getNoticeName()+":"+flowNodeNotice.getNodeName(),"");
        int result = flowNodeNoticeService.insertSelective(getSaveData(flowNodeNotice));
        return result > 0 ? success("保存成功！") : failure("保存失败！");
    }


    @Log(title = "流程节点通知-更新FlowNodeNotice模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.UPDATE, method = "getLogMessage")
    @ApiOperation(value = "更新FlowNodeNotice模块数据")
    @ApiImplicitParam(name = "flowNodeNotice", value = "更新FlowNodeNotice模块数据", required = true, dataType = "FlowNodeNotice")
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Object update(@ModelAttribute ActMyNodeNotice flowNodeNotice) {
        setLogMessage(flowNodeNotice.getNoticeName()+":"+flowNodeNotice.getNodeName(),"");
        int result = flowNodeNoticeService.updateByPrimaryKeySelective(getUpdateData(flowNodeNotice));
        return result > 0 ? success("更新成功！") : failure("更新失败！");
    }

    @Log(title = "流程节点通知-删除FlowNodeNotice模块数据",module = LogModule.WORKFLOW,businessType = BusinessType.DELETE, method = "getLogMessage")
    @ApiOperation(value = "删除FlowNodeNotice模块数据")
    @ApiImplicitParam(name = "flowNodeNotice", value = "删除FlowNodeNotice模块数据", required = true, dataType = "FlowNodeNotice")
    @ResponseBody
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.POST)
    public Object deleteBatch(@RequestParam String[] uuid) {
        if(uuid!=null&&uuid.length>0){
            StringBuilder logStr=new StringBuilder();
            for (String s : uuid) {
                ActMyNodeNotice actMyNodeNotice = flowNodeNoticeService.selectByPrimaryKey(s);
                if(actMyNodeNotice!=null)
                    logStr.append(actMyNodeNotice.getNoticeName()+":"+actMyNodeNotice.getNodeName()+",");
            }
            setLogMessage(logStr.toString(),"");
        }
        int result = flowNodeNoticeService.executeDeleteBatch(uuid);
        return result > 0 ? success("删除成功！") : failure("删除失败！");
    }


}
