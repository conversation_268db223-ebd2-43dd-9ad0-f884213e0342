package com.lms.base.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * Role entity. <AUTHOR> Persistence Tools
 */

@TableName("p_sysroleuserlink")
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "用户角色关系实体类")
public class Sysroleuserlink extends BaseModel {

    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private String id;


    @ApiModelProperty("用户id")
    private String userid;

    @ApiModelProperty("角色id")
    private String roleid;


}
