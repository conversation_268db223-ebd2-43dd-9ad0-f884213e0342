package com.lms.base.operationanalysis.service.impl;

import com.lms.base.operationanalysis.dao.OperationAnalysisDao;
import com.lms.base.operationanalysis.model.OperationAnalysis;
import com.lms.base.operationanalysis.service.OperationAnalysisService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OperationAnalysisServiceImpl implements OperationAnalysisService {
    private final OperationAnalysisDao operationAnalysisDao;

    public OperationAnalysisServiceImpl(OperationAnalysisDao operationAnalysisDao) {
        this.operationAnalysisDao = operationAnalysisDao;
    }

    @Override
    public void save(OperationAnalysis entity) {
        operationAnalysisDao.save(entity);
    }

    @Override
    public void saveBatch(List<OperationAnalysis> entities) {
        operationAnalysisDao.saveBatch(entities);
    }

    @Override
    public void deleteById(String id) {
        operationAnalysisDao.deleteById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        operationAnalysisDao.deleteBatch(ids);
    }

    @Override
    public OperationAnalysis findById(String id) {
        return operationAnalysisDao.findById(id);
    }

    @Override
    public List<OperationAnalysis> findByIds(List<String> ids) {
        return operationAnalysisDao.findByIds(ids);
    }

    @Override
    public List<OperationAnalysis> queryList(java.util.Map<String, Object> conditions, int page, int size) {
        return operationAnalysisDao.queryList(conditions, page, size);
    }
}
