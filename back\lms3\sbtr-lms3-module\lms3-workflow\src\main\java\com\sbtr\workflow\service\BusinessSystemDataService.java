package com.sbtr.workflow.service;

import cn.ewsd.common.utils.easyui.PageParam;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.model.*;
import org.flowable.idm.api.User;
import org.flowable.task.api.Task;
import org.flowable.ui.common.model.RemoteUser;

import java.util.List;
import java.util.Map;

/**
 * BusinessSystemDataService
 * 业务系统使用工作流引擎需要实现的接口，无法实现的可以根据返回类型返回空值或空集合
 * <AUTHOR>
 * @Date 2024-05-15 11:10:30
 */
public interface BusinessSystemDataService {
    /*
     * 获取当前租户ID
     * */
    String getTenantId();
    /*
     * 获取当前用户ID
     * */
    String getUserId();

    String getPersonId();

    void pushAddTaskNew(String taskId);

    void pushViewTask(String businessKey);

    void pushCancelTask(String businessKey);

    void pushCompleteTask(String businessKey, String taskId);

    /*
     * 获取当前用户账号名
     * */
    String getUserNameId();

    /*
     * 获取当前用户人员中文名
     * */
    String getUserName();

    String getUserSecurity();

    Long getProjectId();

    /*
     * 获取当前用户角色ID集合
     * */
    List<String> getUserRoles();

    /*
     * 根据用户Id获取用户角色ID集合
     * */
    List<String> getUserRoles(String userId);

    /*
     * 获取当前用户岗位ID集合
     * */
    List<String> getUserPositions();

    /*
     * 根据用户Id获取用户岗位ID集合
     * */
    List<String> getUserPositions(String userId);

    /*
     * 初始化当前用户的方法，可以默认设定用户信息
     * 本方法仅用作前后端分离开发调试，生产部署环境不适用
     * 生产部署只需要在以上方法(getUserId、getUserNameId、getUserName、getUserPositions、getUserRoles)
     * 写入获取用户对应信息的代码即可
     * */
    void initLoginfo(String loginUserId);

    /*
    * 查询所有角色信息
    *
    * */
    PageInfo<SysRole> getListSysRole(PageParam pageParam, String roleName);

    /*
     * 查询所有用户信息
     *
     * */
    List<SysUser> queryUsers();

    /*
     * 查询所有岗位信息
     *
     * */
    PageInfo<SysPosition> getListPosition(PageParam pageParam, String roleName);

    /*
     * 查询所有部门信息
     *
     * */
    List<SysOrganization> queryOrganizations();

    /*
     * 根据用户账号名获取用户信息
     *
     * */
    SysUser getSysUserByUserNameId(String userId);

    /*
     * 根据用户账号名获取用户部门第一主管用户id
     *
     * */
    String getleaderIdByUserNameId(String startUserId);

    /*
     * 根据角色id,返回用户账号名
     * （角色可以支持逗号拼接,eg:'a,b,c'，返回的账号名多个时也通过逗号拼接）
     * */
    String getUserNameIdByRoleId(String join);

    /*
     * 根据岗位/用户组id,返回用户账号名
     * （岗位/用户组可以支持逗号拼接,eg:'a,b,c'，返回的账号名多个时也通过逗号拼接）
     * */
    String getUserNameIdByPost(String join);

    /*
     * 根据部门id获取部门信息列表
     * (部门id可以支持逗号拼接,eg:'a,b,c')
     * */
    List<SysOrganization> getListOrganizationByOrgId(String toStr);

    /*
     * 根据用户id查询用户中文名称
     * (用户id可以支持逗号拼接,eg:'a,b,c'，返回的中文名多个时也通过逗号拼接)
     * */
    String selectUserNameByuuid(String toStr);

    /*
     * 根据多个用户id和用户表字段名fieldName查询结果集,用户id可以支持逗号拼接,eg:'a,b,c'，
     * select fieldName from user where id in(ids)
     * */
    List<SysUser> getUserDetailByIds(String ids, String fieldName);

    /*
     * 根据用户账号名获取直属主管账号名
     * select fieldName from user where id in(ids)
     * */
    String getReportsToByUserNameId(String userNameId);

    /*
     * 根据业务表、主键、业务Id获取业务数据
     * select fieldName from user where id in(ids)
     * */
    JSONObject getBusinessData(String tablename, String primarykey, String keyValue, String modelKey) throws Exception;

    /**
     * 获取流程版本标识码：procDefId在业务系统的权限码
     * @return 结果
     */
    Map<String,String> getViewCode(String procDefId);

    /*
     * 获取当前用户部门ID
     * */
    default String getOrgId() {
        // 部门id底层设计是整型，暂定默认为0，如果改成字符串底层保存功能会报错
        return "0";
    };

    default User getUser() {
        User user = new RemoteUser();
        user.setId(getUserId());
        return user;
    }

    void saveSysOperLog(String jsonStr);
}
