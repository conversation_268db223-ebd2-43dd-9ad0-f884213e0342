package com.lms.base.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import com.lms.common.model.SearchContent;
import com.lms.common.model.SearchName;
import com.lms.common.model.SearchSelectItemContent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * UCoursewareId entity. <AUTHOR> Persistence Tools
 */

@EqualsAndHashCode(callSuper = true)
@TableName("u_courseware")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "课件对象")
public class Courseware extends BaseModel {

    private static final long serialVersionUID = -3042318882548497957L;

    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private String id;

    @TableField("NAME")
    @ApiModelProperty("名称")
    @SearchContent
    private String name;

    @TableField("ISSCORM")
    @ApiModelProperty("是否是scorm课件")
    private Integer isscorm;

    @TableField("COURSEWARETYPE")
    @ApiModelProperty("课件类型")
    @SearchSelectItemContent
    private String coursewaretype;

    @TableField("LOCATION")
    @ApiModelProperty("地址")
    private String location;

    @TableField("LANGUAGE")
    @ApiModelProperty("语言")
    private String language;

    @TableField("VERSION")
    @ApiModelProperty("版本")
    private String version;

    @TableField("COURSEWARESIZE")
    @ApiModelProperty("课件大小")
    private String coursewaresize;

    @TableField("KEYWORD")
    @ApiModelProperty("关键字")
    @SearchContent
    private String keyword;

    @TableField("DESCRIPTION")
    @ApiModelProperty("描述")
    @SearchContent
    private String description;

    @TableField("CREATEDATE")
    @ApiModelProperty("创建时间")
    private String createdate;

    @TableField("CREATORID")
    @ApiModelProperty("创建人")
    private String creatorid;

    @TableField("STATUS")
    @ApiModelProperty("状态")
    private Integer status;

    @TableField("COMPONENTID")
    @ApiModelProperty("课程id")
    private String componentid;

    @TableField("CREATEDEPTID")
    @ApiModelProperty("创建部门")
    private String createdeptid;

    @TableField("SPECIALID")
    @ApiModelProperty("specialid")
    private String specialid;

    @TableField("EQUIPMENTID")
    @ApiModelProperty("型号id全路径")
    private String equipmentid;

    @TableField(exist = false)
    @ApiModelProperty("型号名称")
    private String equipmentname;

    @TableField(exist = false)
    @ApiModelProperty("型号id")
    private String lastequipmentid;

    @TableField("ATTACHID")
    @ApiModelProperty("附件id")
    private String attachid;

    @TableField("PRINCIPAL")
    @ApiModelProperty("责任人")
    private String principal;

    @TableField("DUTYUNIT")
    @ApiModelProperty("责任单位")
    private String dutyunit;

    @TableField("DUTYUNITID")
    @ApiModelProperty("责任单位id")
    private String dutyunitid;

    @TableField("MODIFYDATE")
    @ApiModelProperty("修改时间")
    private String modifydate;

    @TableField("LENGTH")
    @ApiModelProperty("主键id")
    private Integer length;

    @TableField("SOFTCODE")
    @ApiModelProperty("软件编码")
    private String softcode;

    @TableField(exist = false)
    @ApiModelProperty("学习状态")
    private Integer learnstatus;

    @TableField(exist = false)
    @ApiModelProperty("学习时长")
    private Integer speed;

    @TableField(exist = false)
    @ApiModelProperty("学习次数")
    private Integer studynum;

    @TableField(exist = false)
    @ApiModelProperty("学习日期")
    private String learnDate;

    @TableField("SEQNO")
    @ApiModelProperty("序号")
    private int seqno; //sequence number.

    @ApiModelProperty("教材子表ID")
    private String textbookdetailid;

    @TableField("NUMBER")
    @ApiModelProperty("编号")
    @SearchName
    private String number; // 编号

    @TableField("INTRO")
    @ApiModelProperty("简介")
    @SearchContent
    private String intro; //简介

    /**
     * 教材类型
     * 各单位自著教材、 集团精品教材
     */
    @ApiModelProperty("教材类型")
    @SearchSelectItemContent
    private String textbooktype;

    /**
     * 教材载体类型
     * 图形、纸质教材、音频、视频、动画、3D模型、其他
     */
    @ApiModelProperty("教材载体类型")
    private String textbookcarriertype;

    @ApiModelProperty("专业")
    @SearchSelectItemContent
    private String specialityid; //专业

    @TableField("PERSONLEVELID")
    @ApiModelProperty("人员层级")
    private String personlevelid;

}
