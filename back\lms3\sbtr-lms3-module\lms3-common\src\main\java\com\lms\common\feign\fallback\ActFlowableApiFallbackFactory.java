package com.lms.common.feign.fallback;

import com.lms.common.feign.api.ActFlowableApi;
import com.lms.common.feign.dto.FlowModelDto;
import com.lms.common.feign.dto.ModleDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class ActFlowableApiFallbackFactory implements FallbackFactory<ActFlowableApi> {

    private static final Logger log = LoggerFactory.getLogger(ActFlowableApi.class);

    @Override
    public ActFlowableApi create(Throwable cause) {

        log.info("调用失败 : {}", cause.getMessage());

        return new ActFlowableApi() {
            @Override
            public Object saveCourseWareFlowchart(FlowModelDto flowModelDto) {
                return null;
            }

            @Override
            public Object getCourseWareFlowchart(String modelKey) {
                return null;
            }

            @Override
            public Object updateCourseWareFlowchart(ModleDto modleDto) {
                return null;
            }

            @Override
            public Object deleteModel(String modelId, String procdefId, int delType) {
                return null;
            }
        };
    }
}
