<template>
  <div class="personnel-management">
    <TrTable
      ref="tableRef"
      v-model:table-config="tableConfig"
      v-model:query-config="queryConfig"
      :btn-config="btnConfig"
      @customButton="handleButtonEvent"
    >
      <template #quickSearch>
        <a-form-item style="margin: 10px 0" label="工作单位">
          <DepartmetTree
            ref="departTreeRef"
            style="width: 150px"
            @change="handleselectDepart"
          />
        </a-form-item>
      </template>
    </TrTable>
    <!-- 新增/编辑人员弹窗 -->
    <PersonnelAddForm
      :visible="addModalVisible"
      :personnel="rowData"
      :isEdit="isEdit"
      @close="addModalVisible = $event"
      @submit="handleSearch"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { message } from "ant-design-vue";
import PersonnelAddForm from "./modules/addForm.vue";
import TrTable from "@/components/ReTrTable/index.vue";
import { batchRemovePerson, getPersonListPage } from "@/api/system.ts";
import { columns, queryItems, btnConfig } from "./data.ts";
import type { QueryConfig } from "@/components/ReTrTable/types.ts";
import DepartmetTree from "@/components/Selector/departmetTree.vue";

const addModalVisible = ref(false);
const isEdit = ref(false);
const rowData = ref({});
const tableRef = ref<InstanceType<typeof TrTable>>();
const departTreeRef = ref();
const tableConfig = ref<TableConfig>({
  displayColumns: columns
});

const handleSearch = async () => {
  try {
    let params = {
      pageIndex: tableConfig.value.pageIndex,
      pageSize: tableConfig.value.pageSize,
      orderName: tableConfig.value.field,
      sortType: tableConfig.value.order
    };
    params = Object.assign(params, queryConfig.value.queryForm);
    const response = await getPersonListPage(params);
    if (response.success) {
      tableConfig.value.tableData = response.result.records;
      tableConfig.value.total = response.result.total;
    } else {
      message.error(response.message);
    }
  } catch (error) {
    console.error(error);
    message.error(error);
  } finally {
  }
};

const queryConfig = ref<QueryConfig>({
  items: queryItems,
  onQuery: () => handleSearch(),
  onReset: () => {
    if (departTreeRef.value && "value" in departTreeRef.value) {
      departTreeRef.value.value = undefined;
    }
  }
});

const handleButtonEvent = (key: string, data: any) => {
  if (key === "add") {
    handleAdd();
  } else if (key === "edit") {
    handleEdit(data);
  } else if (key === "delete") {
    tableRef.value.defaultDelete(data.id, batchRemovePerson);
  } else if (key === "batchDelete") {
    tableRef.value.defaultBatchDelete(batchRemovePerson);
  }
};

// 新增人员
const handleAdd = () => {
  rowData.value = {};
  isEdit.value = false;
  addModalVisible.value = true;
};

// 编辑单个人员
const handleEdit = row => {
  rowData.value = row;
  isEdit.value = true;
  addModalVisible.value = true;
};

const handleselectDepart = (data: any) => {
  queryConfig.value.queryForm.PARAMETER_S_EQ_departmentid = data.value;
};
</script>
<style lang="scss" scoped>
.personnel-management {
  height: 100%;
}
</style>
