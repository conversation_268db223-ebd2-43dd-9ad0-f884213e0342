package com.lms.base.scopecourseware.rtefile;

import java.util.ArrayList;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;


import lms.rte.CAM.Model.SeqActivityTree;
import lms.rte.Common.ADLValidRequests;
import lms.rte.Common.DataProviderType;
import lms.rte.DataManager.ITreeProvider;
import lms.rte.Common.ActiveTOCInfo;

public class RteTree {
	// / <summary>
	// / 学习ID
	// / </summary>
	private String m_Userid;

	// / <summary>
	// / 课程ID
	// / </summary>
	private String m_CourseID;

	// / <summary>
	// / 大纲ID
	// / </summary>
	private DataProviderType m_StateType;

	// / <summary>
	// / 是否复习
	// / </summary>
	private boolean m_Review;

	// / <summary>
	// / 树结构提供类
	// / </summary>
	private ITreeProvider m_TreeProvider;

	// / <summary>
	// / 构造函数
	// / </summary>
	public RteTree(String userid, String courseId, DataProviderType stateType,
			boolean review) throws Exception {
		if (userid == null || userid.equals("")) {
			throw new Exception("userid:获取树时用户ID为空");
		}
		if (courseId == null || courseId.equals("")) {
			throw new Exception("courseId:获取树时课程ID为空");
		}
		m_Userid = userid;
		m_CourseID = courseId;
		m_StateType = stateType;
		m_Review = review;
	}

	// / <summary>
	// / 注册树提供程序
	// / </summary>
	// / <param name="treeProvider"></param>
	public void RegisteTreeProvider(ITreeProvider treeProvider) {
		m_TreeProvider = treeProvider;
	}

	// / <summary>
	// / 获取树脚本
	// / </summary>
	// / <returns></returns>
	public JSONArray GetScriptContent() throws Exception {
		JSONArray scriptContent = new JSONArray();
		JSONArray resArray = new JSONArray();
		// 获取当前树
		SeqActivityTree tree = m_TreeProvider.GetTree(m_Userid, m_CourseID,
				m_StateType, m_Review);
		if (tree != null) {
			ADLValidRequests validRequest = tree.getValidRequest();
			ArrayList<ActiveTOCInfo> data = validRequest.getActivityTocInfos();
			scriptContent = HandleData(data);// 构造树结构
		} else {
			throw new Exception("获取不到对应的数据");
		}
		JSONObject jb = doSecondRteTree((JSONObject) scriptContent.get(0),
				scriptContent);
		resArray.add(jb);
		return resArray;

	}

	public JSONObject doSecondRteTree(JSONObject content, JSONArray treeData) {
		JSONObject jb = new JSONObject();
		jb.put("id", content.get("id"));
		jb.put("pid", content.get("pid"));
		jb.put("title", content.get("title"));
		JSONArray jbcArray = new JSONArray();
		for (int i = 0; i < treeData.size(); i++) {
			JSONObject jm = (JSONObject) treeData.get(i);
			if (jm.get("pid") == jb.get("id")) {
				JSONObject jbc = this.doSecondRteTree(
						(JSONObject) treeData.get(i), treeData);
				jbcArray.add(jbc);
			}
		}
		jb.put("children", jbcArray);
		return jb;
	}

	// / <summary>
	// / 生成合成的树的脚本
	// / </summary>
	// / <param name="treeData">树结构</param>
	// / <returns>合成树js脚本</returns>
	private JSONArray HandleData(ArrayList<ActiveTOCInfo> treeData) {
		JSONArray resArray = new JSONArray();
		// 上一个节点的深度值
		int previous_level = 0;

		// 总元素个数
		int length = 0;


		if (treeData != null && treeData.size() > 0) {
			int index = 0;
			// 构建根节点
			previous_level = treeData.get(index).getDepth();
			length = treeData.size();
			String currentPid = "";
			// 开始构建孩子节点
			JSONObject jb0 = HandleLink(treeData.get(index), "root");// 先把根节点放进来
			resArray.add(jb0);
			index++;
			while (index < length) {
				int newLevel = treeData.get(index).getDepth();
				if (newLevel > previous_level) {// 子节点
					JSONObject jb = HandleLink(treeData.get(index), treeData
							.get(index - 1).getActivityId());
					currentPid = (String) jb.get("pid");
					resArray.add(jb);

				} else if (newLevel == previous_level) {// 兄弟节点
					JSONObject jb = HandleLink(treeData.get(index), currentPid);
					resArray.add(jb);
				} else {// 与父节点平行的节点
					JSONObject jb = HandleLink(treeData.get(index), treeData
							.get(0).getActivityId());
					resArray.add(jb);
				}
				previous_level = treeData.get(index++).getDepth();
			} // end while

		} // end if

		return resArray;
	}

	// / <summary>
	// / 根据列表数据来生成相应的Href
	// / </summary>
	// / <returns></returns>
	private JSONObject HandleLink(ActiveTOCInfo treeData, String pid) {
		JSONObject jb = new JSONObject();
		jb.put("id", treeData.getActivityId());
		jb.put("title", treeData.getTitle());
		jb.put("pid", pid);
		return jb;
	}
}
