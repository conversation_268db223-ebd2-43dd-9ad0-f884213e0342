package com.lms.common.util;

//常量类
public interface ConstParamUtil {
    String OPERATE_SUCCESS_MSG = "操作成功！";
    String OPERATE_FAILURE_MSG = "操作失败！";
    String EXCEPTION_TIPS = "异常提示";
    String OPERATE_TIPS = "操作提示";
    String SAVE_SUCCESS_MSG = "保存成功！";
    String SAVE_FAILURE_MSG = "保存失败！";
    String UPDATE_SUCCESS_MSG = "更新成功！";
    String UPDATE_FAILURE_MSG = "更新失败！";
    String DELETE_SUCCESS_MSG = "删除成功！";
    String DELETE_FAILURE_MSG = "删除失败！";
    String SexTypeId = "395dd12b6adb43c2b8e8e48e8a3b270c";  //性别
    String NationTypeId = "ffaa3847911d11ebbb1f3369740ad612";   //民族
    String DutyTypeId = "03348138911e11ebbb1f3369740ad612";   //岗位类型

    /**
     * 普通用户ID
     */

    String X_ACCESS_TOKEN = "token";
    String USER_TOKEN_KEY = "REDIS-USERNAME-";
    Integer TOKEN_EXPIRATION_TIME = 120;
    String MD5SALT = "SBTR_PASSWORD_SALT";
    String JWT_SECRET = "MDk4ZjZiY2Q0NjIxZDM3M2NhZGU0ZTgzMjYyN2I0ZjY=";
    Integer SUCESSCODE = 200;
    Integer ERRORCODE = 500;

    // 包扫描路径
    String commonFeignPackage = "com.lms.**.feign";
    String commonScanPackage = "com.lms.common.**";
    String baseScanPackage = "com.lms.base.**";
    String systemScanPackage = "com.lms.system.**";
    String documentScanPackage = "com.lms.system.**";
    String statisticScanPackage = "com.lms.statistic.**";
    String trainingScanPackage = "com.lms.training.**";
    String examineScanPackage = "com.lms.examine.**";
    String totalLmsPackage = "com.lms.**";
    // 编号的前缀标识
    String CertificateNoKey = "CA";
}
