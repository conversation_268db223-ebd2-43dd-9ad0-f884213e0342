package com.lms.examine.certificate.controller;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.lms.base.feign.api.LmsBaseApi;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.feign.dto.Department;
import com.lms.common.feign.dto.Person;
import com.lms.common.feign.dto.Selectitem;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.ConstParamUtil;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.StringHelper;
import com.lms.examine.certificate.service.CertificateService;
import com.lms.examine.certificatemodel.service.CertificatemodelService;
import com.lms.examine.feign.model.Certificate;
import com.lms.examine.feign.model.Certificatemodel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/certificate")
@Api(value = "证书管理", tags = "证书管理")
public class CertificateController extends BaseController<Certificate> {
    @Resource
    private CertificateService certificateService;
    @Resource
    private CertificatemodelService certificatemodelService;
    @Resource
    private LmsBaseApi lmsBaseApi;

    protected static final Logger logger = LoggerFactory
            .getLogger(CertificateController.class);

    @LMSLog(desc = "查询证书", otype = LogType.List, order = 1, method = "setCertificateLog")
    @PostMapping(value = {"/listpage"})
    @ApiOperation(value = "查询证书", httpMethod = "POST")
    public Result<Page<Certificate>> getCertificateList(@RequestBody JSONObject request) {
        PageInfo pageInfo = super.getPageInfo(request);
        Page<Certificate> certificateList = certificateService.listByCondition(pageInfo);
        //获取所有数据字典
        Set<String> typeIds = new HashSet<>();
        Map<String, Selectitem> selectitemMap = new HashMap<>();
        for (Certificate cf : certificateList.getRecords()) {
            String model = cf.getModel();
            Certificatemodel certificatemodel = certificatemodelService.getAttachInfo(model);
            cf.setCertificatemodel(certificatemodel);
            if (StringUtils.isEmpty(cf.getPersonid())) {
                continue;
            }
            Result<Person> personResult = commonBaseApi.getPersonById(cf.getPersonid());
            if (null != personResult) {
                Person person = personResult.getResult();
                cf.setPerson(person);
                typeIds.add(person.getSex());
                typeIds.add(person.getSpecialityid());
                typeIds.add(cf.getPlanduty());
            }
		}

        List<Selectitem> selectitemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(typeIds)) {
            Result<List<Selectitem>> selectitemListResult = lmsBaseApi.getByIdList(new ArrayList<>(typeIds));
            if (null != selectitemListResult) {
                selectitemList = selectitemListResult.getResult();
            }
        }
        if (CollectionUtils.isNotEmpty(selectitemList)) {
            selectitemMap = selectitemList.stream().collect(Collectors.toMap(Selectitem::getId, s -> s));
        }
        for (Certificate cf : certificateList.getRecords()) {
            Person person = cf.getPerson();
            if (person != null){
				cf.setPersonsex(Optional.ofNullable(selectitemMap.get(person.getSex())).orElse(new Selectitem()).getObjname());
				cf.setPlandutyname(Optional.ofNullable(selectitemMap.get(cf.getPlanduty())).orElse(new Selectitem()).getObjname());
				cf.setPersonspecialityid(Optional.ofNullable(selectitemMap.get(person.getSpecialityid())).orElse(new Selectitem()).getObjname());
				cf.setPersonimage(person.getImage());
				cf.setPersondept(Optional.ofNullable(person.getDepartment()).orElse(new Department()).getName());
			}
        }

        return Result.OK(certificateList);
    }

    @GetMapping(value = {"/getByPlanId"})
    @ApiOperation(value = "根据id获取证书信息", httpMethod = "GET")
    public Result<List<Certificate>> getByPlanId(@RequestParam String planId) {
        List<Certificate> list = certificateService.getByPlanId(planId);
        return Result.OK(list);
    }


    @LMSLog(desc = "新增证书", otype = LogType.Save, order = 1, method = "setCertificateLog")
    @RequestMapping(value = {"/add"}, method = RequestMethod.POST)
    @ApiOperation(value = "新增证书", httpMethod = "POST")
    public Result saveCertificate(@RequestBody Certificate certificate) throws SQLException {
        String[] names = certificate.getName().split("/");
        String[] cardnums = certificate.getCardnum().split("/");
        for (int i = 0; i < names.length; i++) {
            Certificate newct = new Certificate();
            BeanUtils.copyProperties(certificate, newct);
            newct.setName(names[i]);
            newct.setCardnum(cardnums[i]);
            newct.setCreator(ContextUtil.getPersonId());
            newct.setCertificatemodel(null);

            Department department = commonBaseApi.getDepartmentById(Optional.ofNullable(newct.getIssuiingunit()).orElse(ContextUtil.getDepartId())).getResult();
            String deptCode = ObjectUtil.isNotEmpty(department.getCode())?department.getCode() : StringHelper.getFirstLetter(ObjectUtil.isEmpty(department.getShortname())?department.getName():department.getShortname());
            String numberKey = ConstParamUtil.CertificateNoKey + "-" + deptCode;
            String newno = commonSystemApi.getNewCode(numberKey);
            newct.setCno(newno);
            this.certificateService.saveOrUpdate(newct);
        }
        return Result.OK();
    }


    @LMSLog(desc = "编辑证书", otype = LogType.Update, order = 1, method = "setCertificateLog")
    @RequestMapping(value = {"/modify"}, method = RequestMethod.POST)
    @ApiOperation(value = "编辑证书", httpMethod = "POST")
    public Result updateCertificate(@RequestBody Certificate certificate) {
        certificateService.saveOrUpdate(certificate);
        return Result.OK(certificate);
    }

    @LMSLog(desc = "删除证书", otype = LogType.Delete, order = 1, method = "setCertificateLog")
    @RequestMapping(value = {"/delete"}, method = RequestMethod.DELETE)
    @ApiOperation(value = "删除证书", httpMethod = "DELETE")
    public Result batchDeleteCertificate(@RequestParam("ids") String ids) {
        String[] idList = ids.split(",");
		List<String> strings = Arrays.asList(idList);
        certificateService.removeBatchByIds(strings);
        return Result.OK();
    }

    public String setCertificateLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.List)) {
            objname = "证书列表";
        } else if (lmslog.otype().equals(LogType.Save)) {
            Certificate obj = (Certificate) (args[0]);
            objname = StringHelper.null2String(obj.getName());
        } else if (lmslog.otype().equals(LogType.Update)) {
            Certificate obj = (Certificate) (args[0]);
            objname = StringHelper.null2String(obj.getName());
        } else if (lmslog.otype().equals(LogType.Delete)) {
            String ids = (String) (args[0]);
            String[] idarray = ids.split(",");
            for (String id : idarray) {
                Certificate obj = certificateService.getById(id);
                objname = objname.isEmpty() ? StringHelper.null2String(obj.getName()) : objname + "," + StringHelper.null2String(obj.getName());
            }
        }
        return objname;
    }
}
