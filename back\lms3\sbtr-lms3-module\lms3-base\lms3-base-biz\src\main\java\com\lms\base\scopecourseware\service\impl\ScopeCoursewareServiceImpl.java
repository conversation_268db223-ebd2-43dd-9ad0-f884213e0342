
/**
 * FileName:	Clientscopecourseware.java
 * Author:		ji<PERSON><PERSON><PERSON>
 * Time:		2013-3-4上午11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.base.scopecourseware.service.impl;


import com.lms.base.scopecourseware.mapper.ScopeCoursewareMapper;
import com.lms.base.feign.model.ScopeCourseware;
import com.lms.base.scopecourseware.service.ScopeCoursewareService;
import com.lms.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 保存项目的相关信息
 */
@Service("scopecoursewareService")
public class ScopeCoursewareServiceImpl extends BaseServiceImpl<ScopeCoursewareMapper, ScopeCourseware> implements ScopeCoursewareService {

	@Resource
	private ScopeCoursewareMapper scopecoursewareMapper;


}
