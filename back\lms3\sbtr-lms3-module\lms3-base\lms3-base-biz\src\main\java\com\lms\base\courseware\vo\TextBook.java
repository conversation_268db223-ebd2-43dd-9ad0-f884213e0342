package com.lms.base.courseware.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 教材 用于导出
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TextBook implements Serializable {

    private String id;

    @Excel(name = "密级", width = 30)
    private Integer mlevel;

    /**
     * 下拉项
     * 各单位自著教材、 集团精品教材
     */
    @Excel(name = "教材类型", width = 30)
    private String textbooktype;

    @Excel(name = "型号", width = 30)
    private String equipment;

    @Excel(name = "教材名称", width = 50)
    private String bookname;

    @Excel(name = "教材编号", width = 30)
    private String number;

    @Excel(name = "编制单位", width = 30)
    private String creatordepartment;

    @Excel(name = "编制人", width = 30)
    private String creator;

    @Excel(name = "编制人职务", width = 30)
    private String job;

    /**
     * 图形、纸质教材、音频、视频、动画、3D模型、其他
     */
    @Excel(name = "载体类型", width = 30)
    private String tbtype;

    @Excel(name = "简介", width = 50)
    private String intro;

}
