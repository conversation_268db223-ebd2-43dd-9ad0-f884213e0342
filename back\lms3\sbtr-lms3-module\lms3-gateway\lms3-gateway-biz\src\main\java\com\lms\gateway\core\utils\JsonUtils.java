package com.lms.gateway.core.utils;


import java.util.HashMap;
import java.util.Map;

/**
 * 统一返回数据格式
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
public class JsonUtils {

    public JsonUtils() {
    }

    public static Map<String, Object> messageJson(int statusCode, String title, String message) {
        HashMap var3 = new HashMap();
        var3.put("statusCode", statusCode);
        var3.put("title", title);
        var3.put("message", message);
        return var3;
    }


}
