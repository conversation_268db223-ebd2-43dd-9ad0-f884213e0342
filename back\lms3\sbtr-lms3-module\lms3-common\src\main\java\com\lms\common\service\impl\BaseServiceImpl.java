package com.lms.common.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lms.common.config.LMSConfiguration;
import com.lms.common.dao.query.MatchType;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.dao.query.Parameter;
import com.lms.common.feign.api.CommonBaseApi;
import com.lms.common.feign.api.CommonSystemApi;
import com.lms.common.model.Component;
import com.lms.common.model.DataFilter;
import com.lms.common.model.Result;
import com.lms.common.service.BaseService;
import com.lms.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import javax.annotation.Resource;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public abstract class BaseServiceImpl<M extends BaseMapper<T>, T extends java.io.Serializable> extends ServiceImpl<M, T> implements BaseService<T> {
    @Resource
    public RedisUtil redisUtil;
    @Resource
    public CommonSystemApi commonSystemApi;
    @Resource
    public CommonBaseApi commonBaseApi;

    private Class<T> TClazz;

    private BaseMapper<T> baseMapper;

    @Resource
    public LMSConfiguration lmsConfiguration;

    // 使用构造器注入来确保在创建Bean时注入正确的仓库
    /*public BaseServiceImpl(BaseMapper<T> baseMapper) {
        this.baseMapper = baseMapper;
        Type type = getClass().getGenericSuperclass();
        ParameterizedType paramType = (ParameterizedType) type;
        TClazz = (Class<T>) paramType.getActualTypeArguments()[0];
    }*/

    public Class<T> getTClass() {
        return TClazz;
    }

    // 本方法用于获取当前service的bean,切面类方法无法拦截类内部的方法调用，需要获取bean执行内部方法
    public BaseServiceImpl<M,T> getBean() {
        return SpringContextUtils.getBean(this.getClass());
    }


    public List<T> listAll() {
        return getBean().list();
    }

    @DataFilter
    public Page<T> listByCondition(PageInfo pageInfo) {
        // 设置搜索条件
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        buildCondition(pageInfo, wrapper);
        // 设置分页参数
        Page<T> page = buildPageInfo(pageInfo, wrapper);
        page = this.page(page, wrapper);
        return page;
    }



    public List<T> listIds(Collection<? extends Serializable> idList){
        if (CollectionUtils.isEmpty(idList)){
            return new ArrayList<>();
        }else {
            return this.getBaseMapper().selectBatchIds(idList);
        }

    }

    private void buildCondition(PageInfo pageInfo, QueryWrapper<T> wrapper) {
        List<Parameter> parameterList = pageInfo.getParameters();
        if (parameterList == null || parameterList.size() == 0) {
            // 没有查询条件
        } else if (parameterList.size() == 1) {
            Parameter parameter = parameterList.get(0);
            buildSingleSpec(parameter, wrapper);
            // 只有一个条件
        } else {
            for (Parameter parameter : parameterList) {
                wrapper.and(i -> buildSingleSpec(parameter, i));
            }
        }
    }

    private void buildSingleSpec(Parameter parameter, QueryWrapper<T> wrapper) {
        for (int i = 0; i < parameter.getKeys().size(); i++) {
            String key = parameter.getKeys().get(i);
            MatchType matchType = parameter.getMatchTypes().get(i);
            Object propertyValue = parameter.getValues().get(i);
            if (ObjectUtil.isEmpty(propertyValue)) continue;
            switch (matchType) {
                case EQ:
                    wrapper.eq(key, propertyValue);
                    break;
                case NE:
                    wrapper.ne(key, propertyValue);
                    break;
                case LIKE:
                    wrapper.like(key, propertyValue);
                    break;
                case NOTLIKE:
                    wrapper.notLike(key, propertyValue);
                    break;
                case LL:
                    wrapper.likeLeft(key, propertyValue);
                    break;
                case RL:
                    wrapper.likeRight(key, propertyValue);
                    break;
                case LE:
                    wrapper.le(key, propertyValue);
                    break;
                case LT:
                    wrapper.lt(key, propertyValue);
                    break;
                case GE:
                    wrapper.ge(key, propertyValue);
                    break;
                case GT:
                    wrapper.gt(key, propertyValue);
                    break;
                case IN:
                    wrapper.in(key, Arrays.asList(propertyValue.toString().split(",")));
                    break;
                case NOTIN:
                    wrapper.notIn(key, Arrays.asList(propertyValue.toString().split(",")));
                    break;
                case ISNULL:
                    wrapper.or().isNull(key);
                    break;
                case NOTNULL:
                    wrapper.or().isNotNull(key);
                    break;
            }
        }
    }


    private Page<T> buildPageInfo(PageInfo pageInfo, QueryWrapper<T> wrapper) {
        if (!ObjectUtil.isEmpty(pageInfo.getOrderMap())) {
            Map orderMap = pageInfo.getOrderMap();
            for (Object key : orderMap.keySet()) {
                String orderNames = key.toString();
                String sortTypes = orderMap.get(orderNames).toString();
                if (!StringHelper.isEmpty(orderNames)) {
                    String[] ons = orderNames.split(",");
                    String[] sts = sortTypes.split(",");
                    for (int i = 0; i < ons.length; i++) {
                        String orderName = ons[i];
                        String sortType = sts.length > i ? sts[i] : "asc";
                        if (StringHelper.isEmpty(sortType) || sortType.equals("asc")) {
                            wrapper.orderByAsc(orderName);
                        } else if (sortType.equals("desc")) {
                            wrapper.orderByDesc(orderName);
                        }
                    }
                }
            }
        } else {
            String orderNames = pageInfo.getOrderName();
            String sortTypes = pageInfo.getSort();
            if (!StringHelper.isEmpty(orderNames)) {
                String[] ons = orderNames.split(",");
                String[] sts = sortTypes.split(",");
                for (int i = 0; i < ons.length; i++) {
                    String orderName = ons[i];
                    String sortType = sts.length > i ? sts[i] : "asc";
                    if (StringHelper.isEmpty(sortType) || sortType.equals("asc")) {
                        wrapper.orderByAsc(orderName);
                    } else if (sortType.equals("desc")) {
                        wrapper.orderByDesc(orderName);
                    }
                }
            }
        }
        int PageIndex = ObjectUtil.isEmpty(pageInfo.getPageIndex()) ? 1 : pageInfo.getPageIndex();
        int PageSize = ObjectUtil.isEmpty(pageInfo.getPageSize()) ? 20 : pageInfo.getPageSize();
        return new Page<>(PageIndex, PageSize);
    }

    public void adaptComponent(List<T> dataList) {
        Set<String> componentIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Object data : dataList) {
                Object object = BeanUtils.getValueByName(data, "equiptypeid");
                String equipmentid = ObjectUtils.toString(object);
                if (StringUtils.isEmpty(equipmentid)) {
                    equipmentid = ObjectUtils.toString(BeanUtils.getValueByName(data, "equipmentid"));
                }
                if (StringUtils.isEmpty(equipmentid)) {
                    equipmentid = ObjectUtils.toString(BeanUtils.getValueByName(data, "equmenttype"));
                }
                if (StringUtils.isNotEmpty(equipmentid)) {
                    if (equipmentid.contains("/")) {
                        String[] split = equipmentid.split("/");
                        componentIdSet.add(split[split.length - 1]);
                        BeanUtils.setValueByName(data, "lastequipmentid", split[split.length - 1]);
                    } else {
                        componentIdSet.add(equipmentid);
                        BeanUtils.setValueByName(data, "lastequipmentid", equipmentid);
                    }
                }
            }
        }
        Map<String, Component> componentMap = new HashMap<>();
        List<Component> componentList;
        if (componentIdSet.isEmpty()) {
            componentList = new ArrayList<>();
        } else {
            Result<List<Component>> componentByIdList = commonBaseApi.getComponentByIdList(new ArrayList<>(componentIdSet));
            componentList = componentByIdList.getResult();
        }
        if (CollectionUtils.isNotEmpty(componentList)) {
            componentMap = componentList.stream().collect(Collectors.toMap(Component::getId, c -> c));
        }
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Object data : dataList) {
                Object object = BeanUtils.getValueByName(data, "lastequipmentid");
                String lastequipmentid = ObjectUtils.toString(object);
                if (StringUtils.isNotEmpty(lastequipmentid)) {
                    Component component = Optional.ofNullable(componentMap.get(lastequipmentid)).orElse(new Component());
                    BeanUtils.setValueByName(data, "equipmentname", component.getName());
                }
            }
        }
    }

}
