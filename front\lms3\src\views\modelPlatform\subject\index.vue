<template>
  <div class="table-page">
    <TrTable
      ref="sbl"
      :columns="columns"
      :loading="loading"
      :dataSource="dataSource"
      :queryItems="queryItems"
      :show-export="false"
      :show-view="false"
      @import="handleImport"
      @export="handleExtend"
      @query="handleSearch"
      @add="handleAdd"
      @edit="handleEdit"
      @delete="handleDel"
      @batchDelete="handleDel"
    />
    <SubjectAddDialog
      :visible="addFormVisible"
      :object="rowData"
      :isEdit="true"
      @submit="handleSearch"
      @close="addFormVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import TrTable from "@/components/TrTable/index.vue";
import SubjectAddDialog from "./modules/adddialog.vue";
import { message, Modal } from "ant-design-vue";
import { translateText } from "@/utils/translation";
import { queryItems, columns } from "./data.ts";
import { getSubjectListPage, deleteSubject } from "@/api/subject.ts";

const addFormVisible = ref(false);
const viewFormVisible = ref(false);

// 表格组件实例
const sbl = ref<InstanceType<typeof TrTable>>();

const loading = ref(false);
const dataSource = ref([]);
const rowData = ref({});
function handleImport() {
  importFormVisible.value = true;
}
function handleAdd() {
  rowData.value = null;
  addFormVisible.value = true;
}
function handleEdit(row) {
  addFormVisible.value = true;
  rowData.value = row;
}
function handleDel(rows) {
  if (rows instanceof Array && rows.length < 1) {
    message.warning(translateText("请选择要删除的数据！"));
  } else {
    Modal.confirm({
      title: translateText("提示信息"),
      content: translateText("确认删除选中记录吗？"),
      onOk() {
        loading.value = true;
        if (rows instanceof Array) {
          batchRemove(rows);
        } else {
          let array = [];
          array.push(rows);
          batchRemove(array);
        }
      }
    });
  }
}

async function batchRemove(rows) {
  try {
    let ids = rows.map(item => item.id).toString();
    let para = { ids: ids };
    let result = await deleteSubject(para);
    if (result.success) {
      message.success(result.message);
      await handleSearch();
    } else {
      message.error(result.message);
    }
  } catch (error) {
    console.error(error);
    message.error(error);
  } finally {
    loading.value = false;
  }
}

async function handleSearch(form) {
  try {
    loading.value = true;
    let tableRef = sbl.value.getTableState();
    let para = {
      pageIndex: tableRef.pagination.pageIndex,
      pageSize: tableRef.pagination.pageSize,
      orderName: tableRef.sort.field,
      sortType: tableRef.sort.order
    };
    para = Object.assign(para, form);
    const response = await getSubjectListPage(para);
    if (response.success) {
      dataSource.value = response.result.records;
      sbl.value.setTotal(response.result.total);
      sbl.value.clearSelection();
    } else {
      message.error(response.message);
    }
  } catch (error) {
    console.error(error);
    message.error(error);
  } finally {
    loading.value = false;
  }
}

function handleView(row) {
  viewFormVisible.value = true;
  $refs.viewForm.setDataSource(row.subject);
}

function handleExtend() {
  var exportColumns = tableConfig.displayColumns;
  var excelDatas = [
    {
      tHeader: exportColumns.map(item => item.label),
      filterVal: exportColumns.map(item => item.prop.replace("subject.", "")),
      sheetName: "题目列表"
    }
  ];
  let para2 = {
    PARAMETER_S_EQ_type: filters.subjectType,
    PARAMETER_S_LIKE_title: filters.title
  };
  if (tableConfig.selector.length > 0) {
    para2["PARAMETER_S_IN_id"] = tableConfig.selector
      .map(item => item.id)
      .toString();
  }
  if (courseInfo.id !== undefined && courseInfo.id !== "") {
    getSubjectList(courseInfo.id, para2).then(res => {
      if (res.data.success) {
        excelDatas[0].tableDatas = formatData(
          exportColumns,
          res.data.result.content
        );
        exportJsonToExcels(excelDatas, "考核资源数据导出表", true, "xlsx");
      } else {
        message.error(res.data.message);
      }
    });
  } else {
    getSubjectList("listAll", para2).then(res => {
      // let newdatas = JSON.parse(JSON.stringify(res.data.result));
      // excelDatas[1].tableDatas = newdatas.map(item => {
      //   return item.subject;
      // });
      excelDatas[0].tableDatas = formatData(
        exportColumns,
        res.data.result.content
      );
      exportJsonToExcels(excelDatas, "考核资源数据导出表", true, "xlsx");
    });
  }
}

function formatData(displayColumns, tableData) {
  let subjectItemData = tableData.map(item => {
    if (
      item.subject.type === "3ef580ec56ae436eb79b91b25d1a078e" ||
      item.subject.type === "9e47efc0ce894454856a80171e1e6efe"
    ) {
      switch (item.items.length) {
        case 1:
          item.subject.itemA = item.items[0];
          break;
        case 2:
          item.subject.itemA = item.items[0];
          item.subject.itemB = item.items[1];
          break;
        case 3:
          item.subject.itemA = item.items[0];
          item.subject.itemB = item.items[1];
          item.subject.itemC = item.items[2];
          break;
        case 4:
          item.subject.itemA = item.items[0];
          item.subject.itemB = item.items[1];
          item.subject.itemC = item.items[2];
          item.subject.itemD = item.items[3];
          break;
        default:
          item.subject.itemA = "";
          item.subject.itemB = "";
          item.subject.itemC = "";
          item.subject.itemD = "";
          break;
      }
    } else {
      item.subject.itemA = "";
      item.subject.itemB = "";
      item.subject.itemC = "";
      item.subject.itemD = "";
    }
    for (let i in item.subject) {
      displayColumns.map(column => {
        if (i === column.prop.replace("subject.", "") && column.formatter) {
          let fomatMethod = column.formatter;
          if (typeof fomatMethod === "function") {
            item.subject[i] = fomatMethod(item, "", item.subject[i]);
          }
        }
      });
    }
    return item.subject;
  });
  return subjectItemData;
}
</script>

<style scoped>
/* 让页面容器占用100%高度 */
.table-page {
  height: 100%;
}
</style>
