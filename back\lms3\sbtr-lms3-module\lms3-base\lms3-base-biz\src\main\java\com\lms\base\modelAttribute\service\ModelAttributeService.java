package com.lms.base.modelAttribute.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lms.base.model.vo.ModelVo;
import com.lms.base.modelAttribute.vo.ModelAttributeVo;
import com.lms.common.model.Result;

import java.util.List;

public interface ModelAttributeService {

    List<ModelAttributeVo> queryModelAttribute(String modelId) throws Exception;

    void saveModelAttribute(ModelAttributeVo modelAttributeVo) throws Exception;

    Result<Void> updateModelAttribute(ModelAttributeVo modelAttributeVo);

    void deleteModelAttribute(String id) throws Exception;

    List<ModelAttributeVo> listAll();

    Page<ModelAttributeVo> listPage(JSONObject jsonObject) throws Exception;
}
