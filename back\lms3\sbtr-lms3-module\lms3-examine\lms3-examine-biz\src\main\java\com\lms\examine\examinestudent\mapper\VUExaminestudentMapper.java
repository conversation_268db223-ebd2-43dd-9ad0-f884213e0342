package com.lms.examine.examinestudent.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.examine.feign.model.VUExaminestudent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface VUExaminestudentMapper extends BaseMapper<VUExaminestudent> {

	@Select(value = "select count(id) from v_u_examinestudent a where  personid= ?1 and examinetype='1' and examinestatus > '0' and examinestatus < '4' and substr(begintime,0,10)= ?2")
	List<Object[]> getDayExamine(String personId,String day);
}
