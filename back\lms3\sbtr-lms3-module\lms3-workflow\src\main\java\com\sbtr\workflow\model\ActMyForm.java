package com.sbtr.workflow.model;

import cn.ewsd.common.model.MCoreBase;

import javax.persistence.Table;

/**
 * 表单信息
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2019-08-10 11:10:49
 */
@Table(name = "act_my_form")
public class ActMyForm extends MCoreBase {
    private static final long serialVersionUID = 1L;

    /**
     * 模型key
     */
    private String actDeModelKey;
    /**
     * 表名
     */
    private String formTableName;
    /**
     * 表单设计
     */
    private String formDesign;
    /**
     * 表单属性
     */
    private String formModel;
    /**
     * 模型名称
     */
    private String actDeModelName;
    /**
     * 按流程定义Id(没发布一个版本就会存在所对应的表单数据)钮名称
     */
    private String procdefId;
    /**
     * 模型id（关联act_re_model）
     */
    private String actDeModelId;


    private String processInstanceId;


    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    /**
     * 设置：模型key
     */
    public void setActDeModelKey(String actDeModelKey) {
        this.actDeModelKey = actDeModelKey;
    }

    /**
     * 获取：模型key
     */
    public String getActDeModelKey() {
        return actDeModelKey;
    }


    /**
     * 设置：表名
     */
    public void setFormTableName(String formTableName) {
        this.formTableName = formTableName;
    }

    /**
     * 获取：表名
     */
    public String getFormTableName() {
        return formTableName;
    }


    /**
     * 设置：表单设计
     */
    public void setFormDesign(String formDesign) {
        this.formDesign = formDesign;
    }

    /**
     * 获取：表单设计
     */
    public String getFormDesign() {
        return formDesign;
    }


    /**
     * 设置：表单属性
     */
    public void setFormModel(String formModel) {
        this.formModel = formModel;
    }

    /**
     * 获取：表单属性
     */
    public String getFormModel() {
        return formModel;
    }


    /**
     * 设置：模型名称
     */
    public void setActDeModelName(String actDeModelName) {
        this.actDeModelName = actDeModelName;
    }

    /**
     * 获取：模型名称
     */
    public String getActDeModelName() {
        return actDeModelName;
    }


    /**
     * 设置：流程定义Id(没发布一个版本就会存在所对应的表单数据)
     */
    public void setProcdefId(String procdefId) {
        this.procdefId = procdefId;
    }

    /**
     * 获取：流程定义Id(没发布一个版本就会存在所对应的表单数据)
     */
    public String getProcdefId() {
        return procdefId;
    }


    /**
     * 设置：模型id（关联act_re_model）
     */
    public void setActDeModelId(String actDeModelId) {
        this.actDeModelId = actDeModelId;
    }

    /**
     * 获取：模型id（关联act_re_model）
     */
    public String getActDeModelId() {
        return actDeModelId;
    }


}
