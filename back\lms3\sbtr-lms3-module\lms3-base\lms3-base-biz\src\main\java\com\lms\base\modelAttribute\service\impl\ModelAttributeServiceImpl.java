package com.lms.base.modelAttribute.service.impl;


import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lms.base.modelAttribute.service.ModelAttributeService;
import com.lms.base.modelAttribute.vo.ModelAttributeVo;
import com.lms.common.util.TuGraphUtil;
import com.lms.common.constants.Constants;
import com.lms.common.model.Result;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


@Service("ModelAttributeService")
public class ModelAttributeServiceImpl implements ModelAttributeService {

    @Autowired
    private TuGraphUtil tuGraphUtil;


    /**
     * 查询全部
     *
     * @return 模型属性列表
     */
    public List<ModelAttributeVo> listAll() {
        try {
            String result = tuGraphUtil.queryNodes("default", Constants.MODEL_ATTRIBUTE, null);
            List<Map<String, Object>> nodes = parseTuGraphResult(result);
            // 2. 转换为Model列表
            return nodes.stream()
                    .map(node -> ModelAttributeVo.fromTuGraphNode((Map<String, Object>) node.get("a")))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("从TuGraph查询模型属性失败", e);
        }
    }

    /**
     * 分页查询
     *
     * @param jsonObject 分页条件参数
     * @return 模型属性分页列表
     */
    @Override
    public Page<ModelAttributeVo> listPage(JSONObject jsonObject) throws Exception {
        int pageNum = jsonObject.containsKey("pageIndex") ? jsonObject.getIntValue("pageIndex") : 1;
        int pageSize = jsonObject.containsKey("pageSize") ? jsonObject.getIntValue("pageSize") : 20;
        // 1. 解析条件
        String nameLike = jsonObject.getString("name");
        String valueLike = jsonObject.getString("value");
        // 构建查询语句
        String cypherPrefix = "MATCH (a:属性) ";
        String cypherSuffix = String.format(" RETURN a ORDER BY a.id DESC SKIP %s LIMIT %s", pageSize * (pageNum - 1), pageSize);
        StringBuilder cypherWhere = new StringBuilder();
        List<String> conds = new ArrayList<>();
        if (valueLike != null) {
            conds.add("a.value CONTAINS '" + TuGraphUtil.escapeString(valueLike) + "'");
        }
        if (nameLike != null) {
            conds.add("a.name CONTAINS '" + TuGraphUtil.escapeString(nameLike) + "'");
        }
        if (!conds.isEmpty()) {
            cypherWhere.append("WHERE ").append(String.join(" AND ", conds));
        }
        String cypher = cypherPrefix + cypherWhere + cypherSuffix;
        // 查询总数
        StringBuilder countCypher = new StringBuilder();
        countCypher.append(cypherPrefix);
        if (!conds.isEmpty()) {
            countCypher.append("WHERE ").append(String.join(" AND ", conds));
        }
        countCypher.append(" RETURN count(a) as total");
        String countResult = tuGraphUtil.executeCypher(countCypher.toString(), "default");
        int total = 0;
        List<Map<String, Object>> countRows = parseTuGraphResult(countResult);
        if (!countRows.isEmpty() && countRows.get(0).get("total") != null) {
            Object totalObj = countRows.get(0).get("total");
            if (totalObj instanceof Integer) {
                total = (Integer) totalObj;
            } else if (totalObj instanceof Long) {
                total = ((Long) totalObj).intValue();
            } else if (totalObj instanceof String) {
                total = Integer.parseInt((String) totalObj);
            }
        }
        Map<String, ModelAttributeVo> voHashMap = new HashMap<>();
        String result = tuGraphUtil.executeCypher(cypher, "default");
        List<Map<String, Object>> rows = parseTuGraphResult(result);
        for (Map<String, Object> row : rows) {
            ModelAttributeVo vo = ModelAttributeVo.fromTuGraphNode((Map<String, Object>) row.get("a"));
            voHashMap.put(vo.getId(), vo);
        }
        Page<ModelAttributeVo> page = new Page<>(pageNum, pageSize);
        ArrayList<ModelAttributeVo> records = new ArrayList<>(voHashMap.values());
        page.setRecords(records);
        page.setTotal(total);
        return page;
    }

    /**
     * 解析 TuGraph 返回的 JSON 结果
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> parseTuGraphResult(String jsonResult) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(jsonResult);

        List<Map<String, Object>> result = new ArrayList<>();
        for (JsonNode node : rootNode) {
            result.add(mapper.convertValue(node, Map.class));
        }
        return result;
    }

    @Override
    public List<ModelAttributeVo> queryModelAttribute(String modelId) {
        String res = tuGraphUtil.getRelationshipById("default", Constants.MODEL, modelId, Constants.MODEL_ATTRIBUTE);
        List<Map<String, Object>> data = JSON.parseObject(res, List.class);
        List<ModelAttributeVo> attributeVos = convertTuGraphResponseToModelAttributeVo(data);
        return attributeVos;
    }

    /**
     * 保存模型属性 会判断属性是否存在，不存在则创建属性节点并构建关联关系，存在则只构建关联关系
     *
     * @param modelAttributeVo 属性对象
     * @return Result 操作结果
     */
    @Override
    public void saveModelAttribute(ModelAttributeVo modelAttributeVo) {
        if (ObjectUtil.isNull(modelAttributeVo.getName()) || ObjectUtil.isNull(modelAttributeVo.getValue())) {
            throw new RuntimeException("属性名称和属性值不能为空！");
        }
        if (ObjectUtil.isNull(modelAttributeVo.getId())) {
            Snowflake snowflake = new Snowflake();
            modelAttributeVo.setId(snowflake.nextIdStr());
        }
        // 1. 查询属性是否已存在
        String attributeId = modelAttributeVo.getId();
        String queryAttributeIdResult = getAttributeIdByNameAndValue(modelAttributeVo);
        if (StringUtils.isNotEmpty(queryAttributeIdResult)) {
            attributeId = queryAttributeIdResult;
            // 如果是从属性管理的方式新增的需要报错
            if (StringUtils.isEmpty(modelAttributeVo.getModelId())) {
                throw new RuntimeException("新增失败,相同属性已存在！");
            }
        }
        if (StringUtils.isEmpty(queryAttributeIdResult)) {
            // 创建属性节点
            Snowflake snowflake = new Snowflake();
            modelAttributeVo.setId(snowflake.nextIdStr());
            attributeId = modelAttributeVo.getId();
            Map<String, Object> properties = transProperties(modelAttributeVo);
            String createAttributeResult = tuGraphUtil.createNode("default", Constants.MODEL_ATTRIBUTE, properties);
            if (!createAttributeResult.contains("id")) {
                throw new RuntimeException("保存属性节点失败！");
            }
        }
        if (StringUtils.isNotEmpty(modelAttributeVo.getModelId())) {
            // 建立与模型的关系前，先判断关系是否已存在
            String checkRelCypher = String.format(
                    "MATCH (a:%s {id: '%s'})-[r]->(b:%s {id: '%s'}) RETURN r",
                    Constants.MODEL, modelAttributeVo.getModelId(),
                    Constants.MODEL_ATTRIBUTE, attributeId
            );

            String relQueryResult = tuGraphUtil.executeCypher(checkRelCypher, "default");
            List<Map<String, Object>> relList = JSON.parseObject(relQueryResult, List.class);
            if (CollectionUtils.isEmpty(relList)) {
                // 关系不存在，创建关系
                String createRelationshipResult = tuGraphUtil.createRelationship(Constants.MODEL, Constants.MODEL_ATTRIBUTE, "id", modelAttributeVo.getModelId(),
                        "id", attributeId, modelAttributeVo.getRelationship(), "default");
                if (createRelationshipResult.equals("[]")) {
                    throw new RuntimeException("保存属性关系失败！");
                }
            }
        }
    }

    private String getAttributeIdByNameAndValue(ModelAttributeVo modelAttributeVo) {
        String queryAttributeCypher = String.format("MATCH (m:%s) WHERE m.name = '%s' AND toString(m.value) = '%s' RETURN m", Constants.MODEL_ATTRIBUTE, modelAttributeVo.getName(), modelAttributeVo.getValue());
        String queryAttributeResult = tuGraphUtil.executeCypher(queryAttributeCypher, "default");
        List<Map<String, Object>> queryAttributeResultList = JSON.parseObject(queryAttributeResult, List.class);
        String attributeId = "";
        if (queryAttributeResultList != null && !queryAttributeResultList.isEmpty()) {
            Map<String, Object> node = (Map<String, Object>) queryAttributeResultList.get(0).get("m");
            if (node != null && node.get("properties") != null) {
                Map<String, Object> properties = (Map<String, Object>) node.get("properties");
                attributeId = (String) properties.get("id");
            }
        }
        return attributeId;
    }

    @Override
    public Result<Void> updateModelAttribute(ModelAttributeVo modelAttributeVo) {
        String queryAttributeIdResult = getAttributeIdByNameAndValue(modelAttributeVo);
        if (StringUtils.isNotEmpty(queryAttributeIdResult)) {
            throw new RuntimeException("更新失败,相同属性已存在！");
        }
        Map<String, Object> properties = transProperties(modelAttributeVo);
        String result = tuGraphUtil.modifyNode("default", Constants.MODEL_ATTRIBUTE, modelAttributeVo.getId(), properties);
        return result.equals("[]") ? Result.error("更新模型属性失败！", null) : Result.OK(null);
    }

    /**
     * 删除模型属性及指向模型的边
     */
    @Override
    public void deleteModelAttribute(String id) {
        String cypher = String.format("MATCH (m:%s {id: '%s'}) DETACH DELETE m", Constants.MODEL_ATTRIBUTE, id);
        String result = tuGraphUtil.executeCypher(cypher, "default");
        if (result.equals("[{\"<SUMMARY>\":\"deleted 0 vertices, deleted 0 edges.\"}]")) {
            throw new RuntimeException("删除模型属性失败！");
        }
    }

    private static Map<String, Object> transProperties(ModelAttributeVo modelAttributeVo) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("id", modelAttributeVo.getId());
        properties.put("name", modelAttributeVo.getName());
        properties.put("value", modelAttributeVo.getValue());
        properties.put("relationship", modelAttributeVo.getRelationship());
        properties.put("modelId", modelAttributeVo.getModelId());
        return properties;
    }

    public List<ModelAttributeVo> convertTuGraphResponseToModelAttributeVo(List<Map<String, Object>> res) {
        List<ModelAttributeVo> result = new ArrayList<>();
        for (Map<String, Object> item : res) {
            Map<String, Object> attributeNode = (Map<String, Object>) item.get("b");
            ModelAttributeVo attributeVo = ModelAttributeVo.fromTuGraphNode(attributeNode);
            Map<String, Object> edge = (Map<String, Object>) item.get("e");
            attributeVo.setRelationship((String) edge.get("label"));
            Map<String, Object> modelNode = (Map<String, Object>) item.get("a");
            Map<String, Object> modelProperties = (Map<String, Object>) modelNode.get("properties");
            attributeVo.setModelId((String) modelProperties.get("id"));
            result.add(attributeVo);
        }
        return result;
    }
}

