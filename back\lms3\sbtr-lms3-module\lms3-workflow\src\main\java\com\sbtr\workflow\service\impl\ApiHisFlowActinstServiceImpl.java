package com.sbtr.workflow.service.impl;

import com.sbtr.workflow.mapper.ApiHisFlowActinstMapper;
import com.sbtr.workflow.service.ApiHisFlowableActinstService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("apiHisFlowActinstServiceImpl")
public class ApiHisFlowActinstServiceImpl extends BaseServiceImpl implements ApiHisFlowableActinstService {


    @Autowired
    private ApiHisFlowActinstMapper apiHisFlowActinstMapper;


    @Override
    public void deleteHisActinstsByIds(List<String> runActivityIds) {
        apiHisFlowActinstMapper.deleteHisActinstsByIds(runActivityIds);
    }
}
