package com.lms.common.model;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({java.lang.annotation.ElementType.METHOD})
@Documented
public @interface DataCheck
{
  String[] checkNames() default {"mlevel"};
  String[] checkDescs() default {"数据级别未定义，无法保存!"};
}
