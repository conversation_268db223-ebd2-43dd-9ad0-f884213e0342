/**
 * FileName:	IBaseService.java
 * Author:		<PERSON><PERSON><PERSON><PERSON>
 * Time:		2013-3-21  上午10:58:50
 * CopyRight: 	SBTR  LTD.
 * Description:	to-do
 */
package com.lms.common.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lms.common.dao.query.PageInfo;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 *
 */
public interface BaseService<T> extends IService<T> {

    Page<T> listByCondition(PageInfo pageInfo);

    List<T> listIds(Collection<? extends Serializable> idList);

}
