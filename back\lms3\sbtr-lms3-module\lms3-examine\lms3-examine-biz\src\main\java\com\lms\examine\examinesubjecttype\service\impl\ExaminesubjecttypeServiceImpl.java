/**
 * FileName:	ClientExaminepage.java
 * Author:		ji<PERSON><PERSON><PERSON>
 * Time:		2013-3-4上午11:55:39
 * CopyRight: 	SBTR LTD.
 * Description:	to-do
 */
package com.lms.examine.examinesubjecttype.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.examine.examinesubjecttype.mapper.ExaminesubjecttypeMapper;
import com.lms.examine.examinesubjecttype.service.ExaminesubjecttypeService;
import com.lms.examine.feign.model.Examinesubjecttype;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 保存项目的相关信息
 */
@Service("ExaminesubjecttypeService")
public class ExaminesubjecttypeServiceImpl extends BaseServiceImpl<ExaminesubjecttypeMapper, Examinesubjecttype> implements ExaminesubjecttypeService {

	@Resource
	private ExaminesubjecttypeMapper examinesubjecttypeMapper;

	@Override
	public List<Examinesubjecttype> getExaminesubjecttypeByIdAndType(String examineid, String subjecttype) {
		LambdaQueryWrapper<Examinesubjecttype> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(Examinesubjecttype::getExamineid, examineid);
		wrapper.eq(Examinesubjecttype::getSubjecttype, subjecttype);
		return this.list(wrapper);
	}

	@Override
	public List<Examinesubjecttype> getExaminesubjecttypeWithoutEmpty(String examineid) {
		LambdaQueryWrapper<Examinesubjecttype> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(Examinesubjecttype::getExamineid, examineid);
		wrapper.gt(Examinesubjecttype::getQty, 0);
		wrapper.gt(Examinesubjecttype::getPerpoint, 0);
		wrapper.orderByDesc(Examinesubjecttype::getSeqno);
		return this.list(wrapper);
	}

	@Override
	public List<Examinesubjecttype> getExaminesubjecttypeById(String Id) {
		LambdaQueryWrapper<Examinesubjecttype> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(Examinesubjecttype::getExamineid, Id);
		return this.list(wrapper);
	}

	@Override
	public List<Examinesubjecttype> getByExamineIdList(List<String> examineIdList) {
		if (CollectionUtils.isEmpty(examineIdList)){
			return new ArrayList<>();
		}
		LambdaQueryWrapper<Examinesubjecttype> wrapper = new LambdaQueryWrapper<>();
		wrapper.in(Examinesubjecttype::getExamineid, examineIdList);
		return this.list(wrapper);
	}

	@Override
	public List<Map<String, Object>> listSQLQuery(String sql) {
		return examinesubjecttypeMapper.listSQLQuery(sql);
	}

}
