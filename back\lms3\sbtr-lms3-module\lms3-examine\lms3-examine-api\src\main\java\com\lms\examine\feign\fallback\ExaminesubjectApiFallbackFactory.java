package com.lms.examine.feign.fallback;

import com.lms.common.model.Result;
import com.lms.examine.feign.api.ExaminesubjectApi;
import com.lms.examine.feign.model.Examinesubject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * misboot-mdata相关接口 模块降级处理
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
@Component
public class ExaminesubjectApiFallbackFactory implements FallbackFactory<ExaminesubjectApi> {

    private static final Logger log = LoggerFactory.getLogger(ExaminesubjectApiFallbackFactory.class);


    @Override
    public ExaminesubjectApi create(Throwable throwable) {
        log.error("ExaminesubjectApi模块服务调用失败:{}", throwable.getMessage());
        return new ExaminesubjectApi() {

            @Override
            public Result<List<Examinesubject>> getSubjectByExamineId(String id) {
                return null;
            }

            @Override
            public Result save(Examinesubject examinesubject) {
                return null;
            }

            @Override
            public Result<List<Examinesubject>> getExaminesubjectsBySubjectId(String examid, String subjectid) {
                return null;
            }

            @Override
            public Result<List<Examinesubject>> isExistBySubjectId(String subjectId) {
                return null;
            }
        };
    }
}
