package com.lms.gateway.core.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * 配置默认跳过请求地址
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-16 13:31:39
 */
public class DefaultSkipUrl {
    private static final List<String> DEFAULT_SKIP_URL = new ArrayList<>();

    static {
        //document
        DEFAULT_SKIP_URL.add("/document/oss/wpsOssUpload");
        DEFAULT_SKIP_URL.add("/swagger-resources");
        DEFAULT_SKIP_URL.add("/favicon.ico");
        DEFAULT_SKIP_URL.add("/document/v2/api-docs");
        //education
        DEFAULT_SKIP_URL.add("/education/v2/api-docs");
        //erp
        DEFAULT_SKIP_URL.add("/erp/v2/api-docs");
        //form
        DEFAULT_SKIP_URL.add("/form/v2/api-docs");

    }


    public static List<String> getDefaultSkipUrl() {
        return DEFAULT_SKIP_URL;
    }


}
