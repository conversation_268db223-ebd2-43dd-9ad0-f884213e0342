package com.lms.base.component.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.base.component.mapper.ComponentMapper;
import com.lms.base.component.service.ComponentService;
import com.lms.base.courseware.mapper.CoursewareMapper;
import com.lms.base.feign.model.Component;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.enums.RoleEum;
import com.lms.base.subject.mapper.SubjectMapper;
import com.lms.common.feign.dto.TreeNode;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.BeanUtils;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.StringHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service("ComponentService")
public class ComponentServiceImpl extends BaseServiceImpl<ComponentMapper, Component> implements ComponentService {

    @Resource
    private ComponentMapper componentMapper;

    @Resource
    private CoursewareMapper coursewareDao;

    @Resource
    private SubjectMapper subjectDao;

    public List<Component> getAllComponentsByPid(String parentID) {
        return componentMapper.getAllComponentsByPid(parentID);
    }

    public List getAllComponentsByPid2(String pid, boolean includeCourse) {
        String pidfilter = StringHelper.isEmpty(pid) ? "parentid is null or parentid=''" : "id='" + pid + "'";
        String sql = "(select id,parentid as pid,name,courseFlag,'' as courseType,status from B_COMPONENT where status=1 start with " + pidfilter + " CONNECT by prior id=PARENTID order by seqno) ";
        String couseSql = " union " +
                " select id,componentid as pid,coursename as name,'2' courseFlag,courseType,status from u_Course where componentid in(" +
                " select id from B_COMPONENT where status=1 start with " + pidfilter +
                " CONNECT by prior id=PARENTID)";
        if (includeCourse) {
            sql += couseSql;
        }
        List treeNodeList = componentMapper.getAllComponentsByPid2(sql);
        return treeNodeList;
    }


    public List getCteComponentsByPid(String pid) {
        String parentId = StringHelper.isEmpty(pid) ? "1" : pid;
        return componentMapper.getCteComponentsByPid(parentId);
    }

    public List<TreeNode> getComponentTree2(String pid, boolean includeCourse) {
        List<Map> mapList = this.getAllComponentsByPid2(pid, includeCourse);
        List<TreeNode> componentList = new ArrayList<>();
        mapList.forEach(map -> {
            TreeNode treeNode = (TreeNode) BeanUtils.map2Object(map, TreeNode.class);
            /*treeNode.setCourseFlag(StringHelper.null2String(map.get("courseflag")));
            treeNode.setCourseType(StringHelper.null2String(map.get("coursetype")));
            */
            componentList.add(treeNode);
        });
        List<TreeNode> treeNodeList = TreeNode.buildTree(componentList, pid);
        return treeNodeList;
    }

    public List<TreeNode> getCourseStructureTree(PageInfo pageInfo) {
        boolean isStudent = RoleEum.student.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
        List<TreeNode> jbcArray = new ArrayList<>();
        String pid = "";
        if (isStudent) {
            String equipmentId = ContextUtil.getEquipmentId();
            if (StringHelper.isEmpty(equipmentId)) {
                // 当前用户为学员，没有从事型号信息返回空值
                return jbcArray;
            }
            String[] equipmentIds = ContextUtil.getEquipmentId().split(",");
            for (String eqid : equipmentIds) {
                String[] ids = eqid.split("/");
                int length = ids.length;
                pid = length < 2 ? eqid : ids[length - 1];
                jbcArray.addAll(getComponentTree2(pid, true));
            }
        } else {
            List<TreeNode> componentList = new ArrayList<>();
            boolean isAdmin = RoleEum.admin.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
            boolean isLeader = RoleEum.leader.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
            boolean isTeacher = RoleEum.teacher.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
            String equipmentId = isTeacher ? ContextUtil.getCurrentUser().getTeachingmodel() : ContextUtil.getCurrentUser().getManagemodel();
            if ((isAdmin | isLeader | isTeacher) && org.apache.commons.lang3.StringUtils.isEmpty(pid) && org.apache.commons.lang3.StringUtils.isNotEmpty(equipmentId)) {
                String[] componentArray = equipmentId.split(",");
                if (componentArray.length > 0) {
                    for (String componentId : componentArray) {
                        if (org.apache.commons.lang3.StringUtils.isNotEmpty(componentId)) {
                            String[] split = componentId.split("/");
                            componentList.addAll(getComponentTree2(split[split.length - 1], true));
                        }
                    }
                }
            } else {
                componentList.addAll(getComponentTree2("", true));
            }
            jbcArray = componentList;
        }
        return jbcArray;
    }


    public boolean existName(String name, String id, String parentid) {
        return componentMapper.existName(name, id, parentid).size() > 0;
    }


    public Component getByName(String name) {
        List<Component> list = componentMapper.getByName(name);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        } else {
            return null;
        }
    }


    @Override
    public boolean exsitUseCourseware(String id) {
        return coursewareDao.exsitUseCourseware(id).size() > 0;
    }

    @Override
    public boolean exsitUseSubject(String id) {
        return subjectDao.exsitUseSubject(id).size() > 0;
    }

    @Override
    public boolean exsitSubNodes(String id) {
        return componentMapper.getAllComponentsByPid(id).size() > 0;
    }

    @Override
    public boolean hasChildren(String id) {
        if (StringHelper.isEmpty(id))
            return false;
        List list = componentMapper.hasChildren(id);
        return list.size() > 0;
    }

    @Override
    public List<Component> getAllChildComponentsByPidFullPath(String pidFullPath) {
        LambdaQueryWrapper<Component> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(Component::getFullpath, pidFullPath);
        return componentMapper.selectList(wrapper);
    }

    public void adaptComponent(List dataList) {
        Set<String> componentIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Object data : dataList) {
                Object object = BeanUtils.getValueByName(data, "equiptypeid");
                String equipmentid = ObjectUtils.toString(object);
                if (StringUtils.isEmpty(equipmentid)) {
                    equipmentid = ObjectUtils.toString(BeanUtils.getValueByName(data, "equipmentid"));
                }
                if (StringUtils.isEmpty(equipmentid)) {
                    equipmentid = ObjectUtils.toString(BeanUtils.getValueByName(data, "equmenttype"));
                }
                if (StringUtils.isNotEmpty(equipmentid)) {
                    if (equipmentid.contains("/")) {
                        String[] split = equipmentid.split("/");
                        componentIdSet.add(split[split.length - 1]);
                        BeanUtils.setValueByName(data, "lastequipmentid", split[split.length - 1]);
                    } else {
                        componentIdSet.add(equipmentid);
                        BeanUtils.setValueByName(data, "lastequipmentid", equipmentid);
                    }
                }
            }
        }
        Map<String, Component> componentMap = new HashMap<>();
        List<Component> componentList = this.listIds(new ArrayList<>(componentIdSet));
        if (CollectionUtils.isNotEmpty(componentList)) {
            componentMap = componentList.stream().collect(Collectors.toMap(Component::getId, c -> c));
        }
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Object data : dataList) {
                Object object = BeanUtils.getValueByName(data, "lastequipmentid");
                String lastequipmentid = ObjectUtils.toString(object);
                if (StringUtils.isNotEmpty(lastequipmentid)) {
                    Component component = Optional.ofNullable(componentMap.get(lastequipmentid)).orElse(new Component());
                    BeanUtils.setValueByName(data, "equipmentname", component.getName());
                }
            }
        }
    }


    public List<Component> getByNameList(ArrayList<String> nameList) {
        LambdaQueryWrapper<Component> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Component::getName, nameList);
        return componentMapper.selectList(wrapper);
    }

}
