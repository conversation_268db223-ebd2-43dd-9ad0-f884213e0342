package com.sbtr.workflow.mapper;

import com.sbtr.workflow.model.ActFormConfigure;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 常用外置表单数据主表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:35:23
 */
public interface ActFormConfigureMapper extends tk.mybatis.mapper.common.Mapper<ActFormConfigure> {

    /**
     * 分页
     *
     * @return
     * @Param filterSort
     * @Param puuid
     * @Param name
     */
    List<ActFormConfigure> getPageSetOracle(@Param("filterSort") String filterSort, @Param("puuid") String puuid,
                                      @Param("name") String name, @Param("status")String status);

    List<ActFormConfigure> getPageSetMySql(@Param("filterSort") String filterSort, @Param("puuid") String puuid,
                                      @Param("name") String name, @Param("status")String status);

    /**
     * 批量删除
     *
     * @return
     * @Param uuid 主键
     */
    int executeDeleteBatch(Object[] var1);

    int updateStatusByUuid(@Param("uuid") String uuid, @Param("status") String status);

}
