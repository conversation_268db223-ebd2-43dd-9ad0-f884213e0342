<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ActMyNodeButtonMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sbtr.workflow.model.ActMyNodeButton" id="flowNodeButtonMap">
        <result property="uuid" column="uuid"/>
        <result property="createTime" column="create_time"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorOrgId" column="creator_org_id"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="actDeModelId" column="act_de_model_id"/>
        <result property="actDeModelKey" column="act_de_model_key"/>
        <result property="nodeButtonName" column="node_button_name"/>
        <result property="nodeButtonCode" column="node_button_code"/>
        <result property="nodeName" column="node_name"/>
        <result property="nodeId" column="node_id"/>
        <result property="flowModelUuid" column="flow_model_uuid"/>
        <result property="tablename" column="tablename"/>
        <result property="primarykey" column="primarykey"/>
        <result property="formUuid" column="form_uuid"/>
    </resultMap>

    <sql id="Base_Column_List">
  	  	     			 uuid
		  ,  	  	     			 create_time
		  ,  	  	     			 creator
		  ,  	  	     			 creator_id
		  ,  	  	     			 creator_org_id
		  ,  	  	     			 modifier_id
		  ,  	  	     			 modifier
		  ,  	  	     			 modify_time
		  ,  	  	     			 act_de_model_id
		  ,  	  	     			 act_de_model_key
		  ,  	  	     			 node_button_name
		  ,  	  	     			 node_button_code
		  ,  	  	     			 node_name
		  ,  	  	     			 node_id,procdef_id
		  ,  	  	     			 flow_model_uuid,node_form_path,app_page_path,node_form_edit_path,whether_update,node_form_update_path
,node_form_save_path
,  	  	     			 tablename
		  ,  	  	     			 primarykey,form_uuid
		    	  </sql>

    <insert id="insertListMySql">
        insert into act_my_node_button (
        uuid,
        create_time ,
        creator,
        creator_id ,
        creator_org_id ,
        act_de_model_id,
        act_de_model_key ,
        node_button_name ,
        node_button_code ,
        node_id ,
        flow_model_uuid,procdef_id,node_form_path,
                                        app_page_path,node_form_edit_path,whether_update,node_form_update_path,
        node_form_save_path
        ,  	  	     			 tablename
        ,  	  	     			 primarykey,form_uuid
        )
        values
        <foreach collection="list" item="list" index="index" separator=",">
            (#{list.uuid},
            #{list.createTime},
            #{list.creator},
            #{list.creatorId},
            #{list.creatorOrgId},
            #{list.actDeModelId},
            #{list.actDeModelKey},
            #{list.nodeButtonName},
            #{list.nodeButtonCode},
            #{list.nodeId},
            #{list.flowModelUuid},
            #{list.procdefId,jdbcType=VARCHAR},
            #{list.nodeFormPath,jdbcType=VARCHAR},
            #{list.appPagePath,jdbcType=VARCHAR},
            #{list.nodeFormEditPath,jdbcType=VARCHAR},
            #{list.whetherUpdate,jdbcType=VARCHAR},
            #{list.nodeFormUpdatePath,jdbcType=VARCHAR},
            #{list.nodeFormSavePath,jdbcType=VARCHAR},
            #{list.tablename,jdbcType=VARCHAR},
            #{list.primarykey,jdbcType=VARCHAR},
	    #{list.formUuid,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insertListOracle">
        insert into act_my_node_button (
        uuid,
        create_time ,
        creator,
        creator_id ,
        creator_org_id ,
        act_de_model_id,
        act_de_model_key ,
        node_button_name ,
        node_button_code ,
        node_id ,
        flow_model_uuid,procdef_id,node_form_path,app_page_path,node_form_edit_path,whether_update,node_form_update_path,
        node_form_save_path
        ,  	  	     			 tablename
        ,  	  	     			 primarykey,form_uuid
        )
        <foreach collection="list" item="list" index="index" separator="union all">
            (
                select
                    #{list.uuid},
                    #{list.createTime},
                    #{list.creator},
                    #{list.creatorId},
                    #{list.creatorOrgId},
                    #{list.actDeModelId},
                    #{list.actDeModelKey},
                    #{list.nodeButtonName},
                    #{list.nodeButtonCode},
                    #{list.nodeId},
                    #{list.flowModelUuid},
                    #{list.procdefId,jdbcType=VARCHAR},
                    #{list.nodeFormPath,jdbcType=VARCHAR},
                    #{list.appPagePath,jdbcType=VARCHAR},
                    #{list.nodeFormEditPath,jdbcType=VARCHAR},
                    #{list.whetherUpdate,jdbcType=VARCHAR},
                    #{list.nodeFormUpdatePath,jdbcType=VARCHAR},
                    #{list.nodeFormSavePath,jdbcType=VARCHAR},
                    #{list.tablename,jdbcType=VARCHAR},
                    #{list.primarykey,jdbcType=VARCHAR},
            #{list.formUuid,jdbcType=VARCHAR}
                from dual
            )
        </foreach>
    </insert>
    <update id="updateProcdefIdByModelKeyAndProcdef">
        update act_my_node_button set procdef_id=#{procdefId}  where act_de_model_key = #{key} and procdef_id =#{id}
    </update>
    <update id="updateProcdefIdByModelKey">
        update act_my_node_button set procdef_id=#{procdefId} where act_de_model_key = #{key} and (coalesce(procdef_id ,N'') = '')
    </update>

    <update id="updateProcdefIdIsNull">
                update act_my_node_button set procdef_id='' where act_de_model_key = #{key} and procdef_id =#{id}
    </update>

    <select id="getPageSet" resultType="com.sbtr.workflow.model.ActMyNodeButton">
        select
        <include refid="Base_Column_List"></include>
        from act_my_node_button
    </select>

    <select id="getListByActDeModelKeyAndProcdefId" resultType="com.sbtr.workflow.model.ActMyNodeButton">
        select
        <include refid="Base_Column_List"></include>
        from act_my_node_button where act_de_model_key = #{key} and procdef_id =#{procdefIds}
    </select>

    <select id="getDetailByModelKey" resultType="java.util.HashMap">
        SELECT DISTINCT
	    t1.node_form_path,
	    t1.app_page_path
        FROM
	    act_my_node_button t1
	    INNER JOIN act_re_procdef t2 ON ( t1.procdef_id = t2.id_ )
        WHERE
	    t1.act_de_model_key = #{modelKey}
	    AND t2.major_version = '是'
    </select>


    <select id="getListByActDeModelKeyAndProcdefIdAndNodeId"
            resultType="com.sbtr.workflow.vo.ActMyNodeButtonVo">
        select
        DISTINCT
		  node_id,
		  node_form_path,
          app_page_path,
		  node_form_edit_path,
          app_page_path,
		  whether_update,
		  tablename,
		  primarykey,
		  form_uuid
        from act_my_node_button where act_de_model_key = #{modelKey} and procdef_id =#{processDefinitionId}
    </select>
    <select id="selectByBusinessKey" resultType="java.util.HashMap">
        ${sql}
    </select>


    <delete id="executeDeleteBatch">
        delete from act_my_node_button where uuid in
        <foreach item="uuid" collection="array" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </delete>

    <delete id="deleteByModelKeyAnProcdefId">
          delete from act_my_node_button where  act_de_model_key = #{key} and procdef_id =#{id}
    </delete>

</mapper>
