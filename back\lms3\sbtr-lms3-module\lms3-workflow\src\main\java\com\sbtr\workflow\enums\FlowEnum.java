package com.sbtr.workflow.enums;


/**
 * 流程enum
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
public enum FlowEnum {

    ZDSP("自动审批"),
    BHSYB("驳回上一步"),
    SP("同意"),
    TZ("流程流转"),

    BH("驳回"),
    CH("撤回"),
    WP("委派"),
    ZB("转办"),
    QJQ("前加签"),
    HJQ("后加签"),
    TJ("提交"),
    LCZZ("流程终止"),
    LCCX("流程撤销");

    /**
     * 名称
     */
    private String name;


    /**
     * @param type
     * @return {@link String}
     */
    public static String getEnumMsgByType(String type) {
        for (FlowEnum e : FlowEnum.values()) {
            if (e.toString().equals(type)) {
                return e.name;
            }
        }
        return "";
    }

    FlowEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
