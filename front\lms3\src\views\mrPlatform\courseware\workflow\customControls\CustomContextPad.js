export default class CustomContextPad {
  constructor(
    bpmnFactory,
    config,
    contextPad,
    create,
    elementFactory,
    injector,
    translate
  ) {
    this.bpmnFactory = bpmnFactory;
    this.create = create;
    this.elementFactory = elementFactory;
    this.translate = translate;
    this.injector = injector;
    if (config.autoPlace !== false) {
      this.autoPlace = injector.get("autoPlace", false);
    }
    contextPad.registerProvider(this);
  }
}

CustomContextPad.$inject = [
  "bpmnFactory",
  "config",
  "contextPad",
  "create",
  "elementFactory",
  "injector",
  "translate"
];
