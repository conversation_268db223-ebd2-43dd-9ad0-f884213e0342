package com.sbtr.workflow.model;


import cn.ewsd.common.model.MCoreBase;

import javax.persistence.Table;

/**
 * 常用外置表单数据主表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2019-08-16 17:35:23
 */
@Table(name = "act_form_configure")
public class ActFormConfigure extends MCoreBase {
    private static final long serialVersionUID = 1L;

    /**
     * 自己手写表单保存请求地址
     */
    private String nodeFormSavePath;

    /**
     * 自己手写表单页面路径
     */
    private String nodeFormPath;

    /**
     * 自己手写表单更新接口地址
     */
    private String nodeFormUpdatePath;

    /**
     * 自己手写表单获取详情请求地址
     */
    private String nodeFormEditPath;

    /**
     * 自己手写表单获取详情请求地址
     */
    private String name;

    /**
     * 自己手写表单获取详情请求地址
     */
    private String code;

    /**
     * 自己手写表单获取详情请求地址
     */
    private String status;
    private String nodeFormCompletePath;
    private String puuid;
    /**
     * app页面地址
     */
    private String appPagePath;

    /**
     * 映射的数据表
     */
    private String tablename;

    /**
     * 表主键
     */
    private String primarykey;

    public String getAppPagePath() {
        return appPagePath;
    }

    public void setAppPagePath(String appPagePath) {
        this.appPagePath = appPagePath;
    }

    /**
     * 设置：自己手写表单保存请求地址
     */
    public void setNodeFormSavePath(String nodeFormSavePath) {
        this.nodeFormSavePath = nodeFormSavePath;
    }

    /**
     * 获取：自己手写表单保存请求地址
     */
    public String getNodeFormSavePath() {
        return nodeFormSavePath;
    }


    /**
     * 设置：自己手写表单页面路径
     */
    public void setNodeFormPath(String nodeFormPath) {
        this.nodeFormPath = nodeFormPath;
    }

    /**
     * 获取：自己手写表单页面路径
     */
    public String getNodeFormPath() {
        return nodeFormPath;
    }


    /**
     * 设置：自己手写表单更新接口地址
     */
    public void setNodeFormUpdatePath(String nodeFormUpdatePath) {
        this.nodeFormUpdatePath = nodeFormUpdatePath;
    }

    /**
     * 获取：自己手写表单更新接口地址
     */
    public String getNodeFormUpdatePath() {
        return nodeFormUpdatePath;
    }


    /**
     * 设置：自己手写表单获取详情请求地址
     */
    public void setNodeFormEditPath(String nodeFormEditPath) {
        this.nodeFormEditPath = nodeFormEditPath;
    }

    /**
     * 获取：自己手写表单获取详情请求地址
     */
    public String getNodeFormEditPath() {
        return nodeFormEditPath;
    }


    /**
     * 设置：${column.comments}
     */
    public void setPuuid(String puuid) {
        this.puuid = puuid;
    }

    /**
     * 获取：${column.comments}
     */
    public String getPuuid() {
        return puuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getNodeFormCompletePath() {
        return nodeFormCompletePath;
    }

    public void setNodeFormCompletePath(String nodeFormCompletePath) {
        this.nodeFormCompletePath = nodeFormCompletePath;
    }

    public String getTablename() {
        return tablename;
    }

    public void setTablename(String tablename) {
        this.tablename = tablename;
    }

    public String getPrimarykey() {
        return primarykey;
    }

    public void setPrimarykey(String primarykey) {
        this.primarykey = primarykey;
    }
}
