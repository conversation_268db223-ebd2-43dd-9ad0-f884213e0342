package com.lms.common.dao.query;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @fileoverview: <br>Copyright @ 2017-2020 SBTR  LTD.
 * <br> This program is protected by copyright laws.
 * <br> Project Name:SBTR-TSS
 * <br> 模块名称:com.lms.common.dao.query.Parameter
 * <br> 功能:
 * @create 2017-11-23 8:54
 */
@Data
public class Parameter implements Serializable{
    /**
     * 比较方式的泛型类.
     *
     * @see MatchType
     */
    private List<MatchType> matchTypes;

    /**
     * 连接符的泛型类.
     *
     * @see com.lms.common.dao.query.Separator;
     */
    private Separator separator;

    /**
     * 比较的数组.
     */
    private List<String> keys;

    /**
     * 比较的值
     */
    private List values;

    /**
     * 转换的类型
     */
    private List<Class<?>> clazzs;

    /**
     * 单个比较条件内默认使用OR作为连接符.
     */
    private static String OR_SEPARATOR = "_OR_";

    public Parameter() {
        this.keys = new ArrayList<>();
        this.matchTypes = new ArrayList<>();
        this.values = new ArrayList<>();
        this.clazzs = new ArrayList<>();
    }

    /**
     * 拼装参数成自定义封装格式.
     * DEMO:S_LIKE_NAME,解析成S,LIKE,NAME
     * @param paramters web过滤前缀
     * @param paramValues 比较的值
     * @return Parameter 封装好的比较参数类
     */
    public static Parameter getParameter(final String paramters, final Object paramValues) {

        String[] filterNames = paramters.split(Parameter.OR_SEPARATOR);
        String[] values = paramValues.toString().split(Parameter.OR_SEPARATOR);
        Parameter parameter = new Parameter();
        for(int i=0;i<filterNames.length;i++){
            String filterName = filterNames[i];
            String firstPart = StringUtils.substringBeforeLast(filterName.toLowerCase(), "_");
            String propertyTypeCode = StringUtils.substringBefore(firstPart, "_");
            String matchTypeCode = StringUtils.substring(firstPart, propertyTypeCode.length() + 1, firstPart.length());
            Class<?> propertyClass = null;
            MatchType matchType = null;
            Separator separator = null;
            try {
                matchType = Enum.valueOf(MatchType.class, matchTypeCode.toUpperCase());
            } catch (RuntimeException e) {
                throw new IllegalArgumentException("filter名称" + filterName + "没有按规则编写,无法得到属性比较类型.", e);
            }

            try {
                propertyClass = Enum.valueOf(PropertyType.class, propertyTypeCode.toUpperCase()).getValue();
            } catch (RuntimeException e) {
                throw new IllegalArgumentException("filter名称" + filterName + "没有按规则编写,无法得到属性值类型.", e);
            }
            String secondPart = StringUtils.substringAfterLast(filterName, "_");
            String propertyNameStr = StringUtils.substringBefore(secondPart, "_");
            parameter.getKeys().add(propertyNameStr);
            parameter.getMatchTypes().add(matchType);
            parameter.getClazzs().add(propertyClass);
            if(values.length>i){
                parameter.getValues().add(values[i]);
            }else{
                parameter.getValues().add(values[0]);
            }

        }
        return parameter;
    }

}
