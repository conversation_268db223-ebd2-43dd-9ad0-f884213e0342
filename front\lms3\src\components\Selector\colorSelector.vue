<template>
  <a-form-item label="颜色选择">
    <ColorPicker
      v-model:pureColor="selectedColor"
      format="hex"
      :disableAlpha="true"
      class="antdv-color-picker"
    />
  </a-form-item>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ColorPicker } from "vue3-colorpicker";
import "vue3-colorpicker/style.css";

const emit = defineEmits<{
  colorChange: [value: any];
}>();
const selectedColor = ref("#1677ff");
const visible = ref(false);

watch(selectedColor, () => {
  emit("colorChange", selectedColor.value);
});
</script>

<style scoped>
/* 适配 AntDV 主题 */
.antdv-color-picker :deep(.vc-color-wrap) {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}
</style>
