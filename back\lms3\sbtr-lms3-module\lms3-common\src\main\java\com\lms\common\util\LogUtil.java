package com.lms.common.util;

import cn.hutool.core.util.ObjectUtil;
import com.lms.common.feign.api.CommonSystemApi;
import com.lms.common.model.JwtUser;
import com.lms.common.feign.dto.Log;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;

@Slf4j
@Component
public class LogUtil {
    @Resource
    private CommonSystemApi commonSystemApi;
    public String ERRORTIPS = "程序出现异常，请联系系统管理员处理！";

    /**
     * 创建提示信息日志
     *
     * @param message
     * @return
     */
    public void info(String message) {
        try {
            String logDate = DateHelper.getCurDateTime();
            JwtUser jwtUser = ContextUtil.getCurrentUser();
            String user = ObjectUtil.isEmpty(jwtUser) ? "定时任务或回调程序" : jwtUser.getUsername();
            String logText = "提示日志，操作者：" + user + ",提示时间：" + logDate + "日志信息：" + message;
            log.info(logText);
            HttpServletRequest httpServletRequest = ServletUtil.getRequest();
            String objname = httpServletRequest.getRequestURL().toString();
            String desc = ServletUtil.getRequestDetail();
            commonSystemApi.saveLog(new Log(objname, desc, LogType.Info, message));
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
    }

    public Result Error(Exception exception) {
        return Error(exception, "");
    }

    /**
     * 创建异常错误信息日志
     *
     * @param exception, tips
     * @return
     */
    public Result Error(Exception exception, String tips) {
        try {
            tips = ObjectUtil.isEmpty(tips) ? ERRORTIPS : tips;
            exception.printStackTrace();
            String logDate = DateHelper.getCurDateTime();
            JwtUser jwtUser = ContextUtil.getCurrentUser();
            String user = ObjectUtil.isEmpty(jwtUser) ? "定时任务或回调程序" : jwtUser.getUsername();
            String logText = "程序发生异常，操作者：" + user + ",异常时间：" + logDate + "日志信息：" + exception;
            log.error(logText);
            HttpServletRequest httpServletRequest = ServletUtil.getRequest();
            String objname = httpServletRequest.getRequestURL().toString();
            String desc = ServletUtil.getRequestDetail();
            commonSystemApi.saveLog(new Log(objname, desc, LogType.Exception, getExceptionMessage(exception)));
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        } finally {
            return Result.error(tips, exception);
        }
    }

    public String getExceptionMessage(Exception ex) {
        StringWriter stringWriter = new StringWriter();
        PrintWriter writer = new PrintWriter(stringWriter);
        ex.printStackTrace(writer);
        StringBuffer buffer = stringWriter.getBuffer();
        return buffer.toString();
    }

}
