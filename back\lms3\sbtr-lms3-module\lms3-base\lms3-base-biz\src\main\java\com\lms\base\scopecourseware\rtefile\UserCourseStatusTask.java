package com.lms.base.scopecourseware.rtefile;

import lms.rte.Common.CourseStatus;
import lms.rte.Common.DataProviderType;
import lms.rte.DataManager.RTEDataManager;

public class UserCourseStatusTask implements ITask {
	// / <summary>
	// / 课程ID
	// / </summary>
	private String m_CourseID;

	// / <summary>
	// / 学员ID
	// / </summary>
	private String m_UserID;

	// / <summary>
	// / 主目标满意情况
	// / </summary>
	private String m_Satisfied;

	// / <summary>
	// / 进度完成情况
	// / </summary>
	private String m_Completed;

	// / <summary>
	// / 目标测量值
	// / </summary>
	private String m_Measure;

	public UserCourseStatusTask(String userId, String courseId,
                                String statisfied, String completed, String mearsure) {
		// if (string.IsNullOrEmpty(userId)) throw new
		// ArgumentNullException("userid", "用户ID为空");
		// if (string.IsNullOrEmpty(courseId)) throw new
		// ArgumentNullException("courseId", "课程ID为空");

		m_CourseID = courseId;
		m_UserID = userId;
		m_Satisfied = statisfied;
		m_Completed = completed;
		m_Measure = mearsure;
	}

	public boolean Excute() {
		boolean result = false;

		try {
			CourseStatus courseStatus = new CourseStatus();
			courseStatus.setCompleted(m_Completed);
			courseStatus.setCourseID(m_CourseID);
			courseStatus.setLearnerID(m_UserID);
			courseStatus.setMeasure(m_Measure);
			courseStatus.setSatisfied(m_Satisfied);
			result = RTEDataManager.getInstance().UpdateCourseStatus(
					courseStatus, DataProviderType.Normal);
			if (result) {
				// Log.Debug(this + "任务已经执行");
			} else {
				// Log.Debug(this + "任务已经执行但是未执行成功Userid=" + m_UserID +
				// ",CourseID=" + m_CourseID);
			}
		} catch (Exception ex) {
			// Log.Error(ex.Message + "\r\n" + ex.StackTrace);
		}
		return result;
	}
}
