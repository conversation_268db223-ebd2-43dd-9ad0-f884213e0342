package com.sbtr.workflow.service.impl;

import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import cn.ewsd.common.utils.easyui.PageUtils;
import com.sbtr.workflow.constants.DriverClassName;
import com.sbtr.workflow.mapper.ActMyNodeFieldMapper;
import com.sbtr.workflow.model.ActMyNodeField;
import com.sbtr.workflow.service.ActMyNodeFieldService;
import com.sbtr.workflow.vo.FlowNodeFieldVo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("flowNodeFieldServiceImpl")
public class ActMyNodeFieldServiceImpl extends WorkflowBaseServiceImpl<ActMyNodeField, String> implements ActMyNodeFieldService {
	@Autowired
	private ActMyNodeFieldMapper flowNodeFieldMapper;

    @Value("${spring.datasource.dynamic.datasource.master.driver-class-name:null}")
    public String driverClassName;

    @Override
    public PageSet<ActMyNodeField> getPageSet(PageParam pageParam ) {
        PageHelper.startPage(pageParam.getPage(), pageParam.getRows());
        List<ActMyNodeField> list = flowNodeFieldMapper.getPageSet();
        PageInfo<ActMyNodeField> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

	@Override
	public int executeDeleteBatch(String[] uuids){
		return flowNodeFieldMapper.executeDeleteBatch(uuids);
	}

    @Override
    public Integer insertList(List<ActMyNodeField> list1) {
        if(DriverClassName.ORACLE_NAME.equals(driverClassName) || DriverClassName.DM_NAME.equals(driverClassName)){
            return flowNodeFieldMapper.insertListOracle(list1);
        }
        return flowNodeFieldMapper.insertList(list1);
    }

    @Override
    public List<FlowNodeFieldVo> selectByModelKeyAndId(String modelKey, String nodeId,String processDefinitionId) {
        return flowNodeFieldMapper.selectByModelKeyAndId(modelKey,nodeId,processDefinitionId);
    }

    @Override
    public List<ActMyNodeField> getListByActDeModelKeyAndProcdefId(String key, String procdefIds) {
        return flowNodeFieldMapper.getListByActDeModelKeyAndProcdefId(key,procdefIds);
    }

    @Override
    public Integer updateProcdefIdByModelKey(String key, String procdefId) {
        return flowNodeFieldMapper.updateProcdefIdByModelKey(key,procdefId);
    }

    @Override
    public Integer updateProcdefIdIsNull(String key, String id) {
        return flowNodeFieldMapper.updateProcdefIdIsNull(key,id);
    }

    @Override
    public Integer deleteByModelKeyAnProcdefId(String key, String id) {
        return flowNodeFieldMapper.deleteByModelKeyAnProcdefId(key,id);
    }

    @Override
    public Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id) {
        return flowNodeFieldMapper.updateProcdefIdByModelKeyAndProcdef(procdefId,key,id);
    }


}
