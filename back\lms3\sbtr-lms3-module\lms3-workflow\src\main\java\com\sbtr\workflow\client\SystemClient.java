package com.sbtr.workflow.client;

import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @MethodName
 * @Description 调用sbtr-system相关接口
 * @Description 本地开发时可以指定 ip+网关端口进行访问  在configuration中配置 url = "http://*************:9774/system"
 * @Param null
 * @Return
 * <AUTHOR> @ sbtr.com>
 * @Date 2020-11-23 14:37
 */
//@FeignClient(name = FeignClientName.SYSTEM_SERVER_NAME,
//        configuration = FeignConfig.class, fallbackFactory =
//        SystemClientFallbackFactory.class)
public interface SystemClient {


    /**
     * 解析Excel文件内容返回map
     *
     * @MethodName analysisExecl
     * @Description
     * @Param address
     * @Return java.util.Map
     * <AUTHOR> @ sbtr.com>
     * @Date 2019-09-26 12:07
     */
    @RequestMapping(value = "/attachment/analysisExecl")
    List<Map<String, Object>> analysisExecl(@RequestParam("address") String address);

    @RequestMapping(value = "/sysDataAuth/getDataAuthList", method = RequestMethod.POST)
    List<Map<String, Object>> getDataAuthList();

    /**
     * @MethodName sendText 发送文本消息
     * @Description
     * @Param touser 指定接收消息的成员，成员ID列表（多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为”@all”，则向该企业应用的全部成员发送如：ZhuYongJing|HuangTing|FengKai
     * @Param toparty 指定接收消息的部门，部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为”@all”时忽略本参数
     * @Param totag 指定接收消息的部门，部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为”@all”时忽略本参数
     * @Param title 标题
     * @Param description 卡片内容可写标签如：<div class=\"gray\">2016年9月26日</div> <div class=\"normal\">恭喜你抽中iPhone 7一台，领奖码：xxxx</div><div class=\"highlight\">请于2016年10月10日前联系行政同事领取</div>
     * @Param url 请求地址
     * @Return java.lang.Object
     * <AUTHOR> @ sbtr.com>
     * @Date 2021-06-07 17:21
     */
    @RequestMapping(value = "/qiyeweixin/sendTextcard.api", method = RequestMethod.POST)
    Object sendTextcard(@RequestParam ("touser") String touser,
                        @RequestParam ("toparty") String toparty,
                        @RequestParam ("totag") String totag,
                        @RequestParam ("title") String title,
                        @RequestParam ("description") String description,
                        @RequestParam ("url")  String url);


    /**
     * 根据user_uuid type获取详情
     *
     * @param userUuid
     * @param type
     * @return Map<String, Object>
     */
    @PostMapping(value = "/sysUserDingding/getDetailByUserUuid")
    Map<String, Object> getDetailByUserUuid(
            @RequestParam("userUuid")String userUuid,
            @RequestParam("type") String type);


    /**
     * 发送文本消息
     *
     * @param touser   touser 指定接收消息的成员，成员ID列表（多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为”@all”，则向该企业应用的全部成员发送如：ZhuYongJing|HuangTing|FengKai
     * @param toparty  toparty 指定接收消息的部门，部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为”@all”时忽略本参数
     * @param totag   totag 指定接收消息的部门，部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为”@all”时忽略本参数
     * @param content content 消息内容，最长不超过2048个字节，超过将截断（支持id转译）可以写标签如:<a href=\"http://work.weixin.qq.com\">邮件中心视频实况</a>
     * @return Object
     */
    @RequestMapping(value = "/qiyeweixin/sendText.api", method = RequestMethod.POST)
    Object sendText(@RequestParam("touser") String touser,
                    @RequestParam("toparty")  String toparty,
                    @RequestParam("totag")  String totag,
                    @RequestParam("content")   String content);


    /**
     * 保存日志数据
     *
     * @param map
     * @return Object
     */
    @PostMapping(value = "/sysOperationLog/save")
    Object save(@RequestParam Map<String, Object> map);

    String getTextById(String toStr);

    Object getZoneFillbackData(String str);

    String getFormDataById(String toStr);

    String getTextByValue(String toStr);
}
