<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.SysOrganizationMapper">

    <sql id="Base_Column_List" >
        uuid
        ,										 create_time
		  ,											creator
				 ,										 creator_id
		  ,											modifier
				 ,										 modifier_id
		  ,										 modify_time
		  ,										 creator_org_id
		  ,										 tenant_uuid
		  ,											id
				 ,											pid
				 ,										 org_name
		  ,										 org_code
		  ,										 org_abbreviation
		  ,										 org_person
		  ,										 org_type
		  ,										 org_status
		  ,										 org_telephone
		  ,										 org_official_website
		  ,										 org_email
		  ,										 org_address
		  ,										 org_sort
		  ,										 org_brief_introduction
		  ,										 org_legal_representative
		  ,										 org_business_status
		  ,										 org_date_incorporation
		  ,										 org_registered_capital
		  ,										 org_paid_capital
		  ,										 org_audit_date
		  ,										 org_credit_code
		  ,										 org_organization_code
		  ,										 org_taxpayer_number
		  ,										 org_enterprise_type
		  ,										 org_business_term
		  ,										 org_industrial_no
		  ,										 org_industry
		  ,										 org_registration_authority
		  ,										 org_administrative_division
		  ,										 org_insured_number
		  ,										 org_before_name
		  ,										 org_registered_address
		  ,										 org_nature_business
    </sql>

    <select id="getListByOrgType" resultType="com.sbtr.workflow.model.SysOrganization">
        select *
        from (SELECT id, pid, leader, text, org_type, uuid, f_get_dic_item_text(org_type) as a
              from sys_organization
              where FIND_IN_SET(id, #{ids})) b
        where b.a = #{groupLeader}
    </select>

    <select id="getOrgList" resultType="java.util.Map">
        SELECT org.id      AS id,
               org.state   as state,
               org.pid     AS pid,
               org.text    as title,
               u.user_name as name,
               org.text    as text,
               u.cellphone as cellphone,
               u.telephone as telephone,
               u.post_text as post,
               u.email     as email
        from sys_organization org
                 left JOIN sys_user u
                           on org.leader_id = u.user_name_id
        WHERE (org.is_del != 1 and u.is_del !=1)
           or (org.leader_id is null and org.is_del != 1)
           or (u.user_name_id is null and org.is_del != 1)
        ORDER BY pid
    </select>

    <select id="getleaderIdByUserNameId" resultType="java.lang.String">
        SELECT org_person
        FROM sys_organization
        where id in (select user_org_id from sys_user where user_name_id = #{userNameId})
    </select>

    <select id="getListOrganizationByOrgId" resultType="com.sbtr.workflow.model.SysOrganization">
		select
		<include refid="Base_Column_List"></include>
		from sys_organization where
			id in
			<foreach collection="orgId.split(',')" item="item" index="index" open="(" separator="," close=")">
				#{item}
			</foreach>
	</select>


</mapper>
