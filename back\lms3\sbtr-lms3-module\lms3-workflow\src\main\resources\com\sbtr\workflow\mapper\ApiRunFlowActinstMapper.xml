<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.ApiRunFlowActinstMapper">

    <delete id="deleteRunActinstsByIds">
        delete from act_ru_actinst where ID_ in
        <foreach item="list" index="index" collection="list" open="(" separator="," close=")">
            #{list}
        </foreach>
    </delete>
</mapper>
