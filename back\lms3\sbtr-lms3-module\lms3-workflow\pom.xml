<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sbtr-lms3-module</artifactId>
        <groupId>com.cepreitrframework.boot</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>lms3-workflow</artifactId>
    <packaging>jar</packaging>
    <name>lms3-workflow</name>
    <description>系统工作流管理服务</description>
    <properties>
        <boot-jar-output>../package</boot-jar-output>
        <!-- 核心-->
        <spring-boot-starter-toolkit.version>4.11.22a</spring-boot-starter-toolkit.version>
        <!-- 定时任务-->
        <xxl-job-core.version>2.2.0</xxl-job-core.version>
        <!-- 接口文档-->
        <knife4j.version>2.0.9</knife4j.version>
        <!-- 分布式事务-->
        <seata-all.version>1.4.2</seata-all.version>
        <seata-spring-boot-starter.version>1.4.2</seata-spring-boot-starter.version>
        <liquibase-core.version>4.9.1a</liquibase-core.version>
        <!-- 流量控制-->
        <!--        <sentinel-datasource-nacos.version>1.8.2</sentinel-datasource-nacos.version>-->
        <sentinel-parameter-flow-control.version>1.8.2</sentinel-parameter-flow-control.version>
        <!-- 链路追踪-->
        <apm-toolkit-trace.version>8.7.0</apm-toolkit-trace.version>
        <apm-toolkit-logback-1.x.version>8.7.0</apm-toolkit-logback-1.x.version>
        <!-- flowable-->
        <flowable-spring-boot-starter.version>6.4.1</flowable-spring-boot-starter.version>
        <flowable-ui-modeler-rest.version>6.4.1</flowable-ui-modeler-rest.version>
        <flowable-ui-modeler-conf.version>6.4.1</flowable-ui-modeler-conf.version>
        <flowable-ui-modeler-logic.version>6.4.1</flowable-ui-modeler-logic.version>
        <!-- h2-->
        <h2.version>1.4.197</h2.version>
        <!-- groovy-->
        <groovy-all.version>3.0.8</groovy-all.version>
    </properties>

    <dependencies>
        <!-- apm-toolkit-trace 对项目中的业务方法，实现链路追踪 -->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.skywalking</groupId>-->
        <!--            <artifactId>apm-toolkit-trace</artifactId>-->
        <!--            <version>${apm-toolkit-trace.version}</version>-->
        <!--            <scope>provided</scope>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.skywalking</groupId>-->
        <!--            <artifactId>apm-toolkit-logback-1.x</artifactId>-->
        <!--            <version>${apm-toolkit-logback-1.x.version}</version>-->
        <!--        </dependency>-->
        <!-- spring-boot-starter-toolkit -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-toolkit</artifactId>
            <version>${spring-boot-starter-toolkit.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.sourceforge.jexcelapi</groupId>
                    <artifactId>jxl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.pagehelper</groupId>
                    <artifactId>pagehelper-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
                <!-- logback -->
                <!--<exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>-->
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
            <version>${liquibase-core.version}</version>
        </dependency>
        <!-- pagehelper-spring-boot-starter -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>${pagehelper-spring-boot-starter.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>jul-to-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- flowable  -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter</artifactId>
            <version>${flowable-spring-boot-starter.version}</version>
        </dependency>
        <!--flowable-ui-modeler-rest-->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-ui-modeler-rest</artifactId>
            <version>${flowable-ui-modeler-rest.version}</version>
        </dependency>
        <!--flowable-ui-modeler-conf-->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-ui-modeler-conf</artifactId>
            <version>${flowable-ui-modeler-conf.version}</version>
        </dependency>
        <!--flowable-ui-modeler-logic-->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-ui-modeler-logic</artifactId>
            <version>${flowable-ui-modeler-logic.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- h2 -->
        <!--        <dependency>-->
        <!--            <groupId>com.h2database</groupId>-->
        <!--            <artifactId>h2</artifactId>-->
        <!--            <version>${h2.version}</version>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->
        <!--guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
        <!--groovy-all -->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>${groovy-all.version}</version>
            <type>pom</type>
        </dependency>
        <!--    knife4j     -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>2.0.3</version>
        </dependency>
        <!-- 以下为新增by qjh start-->
        <!--        <dependency>-->
        <!--            <groupId>io.projectreactor</groupId>-->
        <!--            <artifactId>reactor-core</artifactId>-->
        <!--            <version>3.4.2</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>com.belerweb</groupId>-->
        <!--            <artifactId>pinyin4j</artifactId>-->
        <!--            <version>2.5.1</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>eu.bitwalker</groupId>-->
        <!--            <artifactId>UserAgentUtils</artifactId>-->
        <!--        </dependency>-->
        <!-- 以上为新增by qjh end-->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cepreitrframework.boot</groupId>
            <artifactId>lms3-base-api</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-jpa</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 拷贝项目所有依赖jar文件到构建lib目录下 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>
            <!-- Spring Boot模块jar构建 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <outputDirectory>${boot-jar-output}</outputDirectory>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
