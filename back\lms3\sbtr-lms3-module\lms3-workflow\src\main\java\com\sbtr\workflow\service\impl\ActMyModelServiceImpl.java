package com.sbtr.workflow.service.impl;

import cn.ewsd.common.common.Constants;
import cn.ewsd.common.utils.JsonUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.mapper.ActMyModelMapper;
import com.sbtr.workflow.model.ActMyModel;
import com.sbtr.workflow.service.ActMyModelService;
import com.sbtr.workflow.service.BusinessSystemDataService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.PageUtil.PageUtils;
import com.sbtr.workflow.utils.StringUtil.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

@Service("flowModelServiceImpl")
public class ActMyModelServiceImpl
        extends WorkflowBaseServiceImpl<ActMyModel, String>
        implements ActMyModelService {
    @Autowired
    private ActMyModelMapper flowModelMapper;
    @Resource
    public BusinessSystemDataService businessSystemDataService;
    @Override
    public PageSet<ActMyModel> getPageSet(PageParam pageParam, String filterSort) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<ActMyModel> list = flowModelMapper.getPageSet(filterSort);
        PageInfo<ActMyModel> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    @Override
    public int executeDeleteBatch(String[] uuids) {
        return flowModelMapper.executeDeleteBatch(uuids);
    }

    @Override
    public ActMyModel getListByModelKey(String modelKey) {
        Example example = new Example(ActMyModel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modelKey);
        List<ActMyModel> lists = flowModelMapper.selectByExample(example);
        return lists.get(0);
    }

    @Override
    public int updateFormDesign(String uuid, String formDesign, String formModel) {
        return flowModelMapper.updateFormDesign(uuid, formDesign, formModel);
    }

    @Override
    public Object updateFormDesignByActDeModelKey(String actDeModelKey, String formDesign, String formModel) {
        Integer integer = flowModelMapper.updateFormDesignByActDeModelKey(actDeModelKey, formDesign, formModel);
        return integer > 0 ? JsonUtils.messageJson(200, Constants.OPERATE_TIPS, "更新成功") :
                JsonUtils.messageJson(300, Constants.OPERATE_TIPS, "更新失败");
    }

    @Override
    public Integer updateFormFieldJson(String formTableName, String formJson, String formModel) {
        return flowModelMapper.updateFormFieldJson(formTableName, formJson, formModel);
    }

    @Override
    public List<ActMyModel> getListByModelKeyAndProcdefIdIsNull(String key) {
        return flowModelMapper.getListByModelKeyAndProcdefIdIsNull(key);
    }

    @Override
    public List<ActMyModel> getListByActDeModelKeyAndProcdefId(String key, String procdefIds) {
        return flowModelMapper.getListByActDeModelKeyAndProcdefId(key, procdefIds);
    }

    @Override
    public Integer updateProcdefIdIsNull(String key, String id) {
        return flowModelMapper.updateProcdefIdIsNull(key, id);
    }

    @Override
    public Integer selectCountInteger(String key) {
        return flowModelMapper.selectCountInteger(key);
    }

    @Override
    public Integer deleteByModelKeyAnProcdefId(String key, String id) {
        return flowModelMapper.deleteByModelKeyAnProcdefId(key, id);
    }

    @Override
    public Boolean getAuthStarted(String userNameId, String modelKey, String procdefId) {
        Example example = new Example(ActMyModel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("actDeModelKey", modelKey);
        criteria.andEqualTo("procdefId", procdefId);
        List<ActMyModel> lists = selectByExample(example);
        if(lists.size()>0){
            String permissionType = StringUtils.convertNullToEmpty(lists.get(0).getPermissionType());
            String permissionValue = StringUtils.convertNullToEmpty(lists.get(0).getPermissionValue());
            switch (permissionType) {
                case "all":
                    return true;
                case "appoint":
                    return StringUtils.useLoop(permissionValue.split(","), userNameId);
                case "role":
                    for (int i=0;i<businessSystemDataService.getUserRoles().size();i++) {
                        String s = businessSystemDataService.getUserRoles().get(i).toString();
                        if (StringUtils.useLoop(permissionValue.split(","), s)) {
                            return true;
                        }
                    }
                    return false;
                case "cannotInitiate":
                    return false;
            }
        }
        return false;
    }

    @Override
    public Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id) {
        return flowModelMapper.updateProcdefIdByModelKeyAndProcdef(procdefId,key,id);
    }

    @Override
    public Boolean canTheProcessBeStarted(String userNameId, String permissionType, String permissionValue) {
        switch (permissionType) {
            case "all":
                return true;
            case "appoint":
                return StringUtils.useLoop(permissionValue.split(","), userNameId);
            case "role":
                for (int i=0;i<businessSystemDataService.getUserRoles().size();i++) {
                    String s = businessSystemDataService.getUserRoles().get(i).toString();
                    if (StringUtils.useLoop(permissionValue.split(","), s)) {
                        return true;
                    }
                }
                return false;
            case "cannotInitiate":
                return false;
        }
        return false;
    }

    @Override
    public Integer updateActReProcdefNameByKey(String modelKey, String majorVersion, String actDeModelName) {
        return flowModelMapper.updateActReProcdefNameByKey(modelKey, majorVersion,actDeModelName);
    }

    @Override
    public String getDesignJson(String formUuid) {
        return flowModelMapper.getDesignJson(formUuid);
    }

    @Override
    public List<ActMyModel> getMajorModelByKey(String modelKey) {
        return flowModelMapper.getMajorModelByKey(modelKey);
    }


}
