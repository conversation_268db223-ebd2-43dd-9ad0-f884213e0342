/**
 *
 *
 * SBTR LTD.
 *
 */
package com.lms.common.util;

import cn.hutool.core.convert.Convert;

import java.math.BigDecimal;
import java.util.Random;

/**
 * 数字常用工具方法类
 */
public final class NumberHelper {

    /**
     * 将输入的整数转成指定长度的字符串,不足的用0填充
     * @param iIn 需要填充0的整数

     * @param iLength 转换后的字符串的长度
     * @return 用0填充过的指定长度的字符串
     */
    public static String add0(int iIn, int iLength) {
        long lv = (long) Math.pow(10, iLength);
        if(lv < iIn)	return String.valueOf(iIn);
        return String.valueOf(lv + iIn).substring(1);
    }

    /**
     * 字符串转换为整数，转换异常返回-1。
     * @param strValue 字符串
     * @return 整数
     */
    public static int string2Int(String strValue) {
        return string2Int(strValue, -1);
    }

    /**
     * 字符串转换为整数，可设置转换异常默认值。
     * @param strValue 字符串
     * @param defValue 转换异常默认值
     * @return 整数
     */
    public static int string2Int(String strValue, int defValue) {
        try {
            return Integer.parseInt(strValue);
        } catch (Exception ex) {
            return defValue;
        }
    }

    /**
     * 对象转换为整数，可设置转换异常默认值。
     * @param obj 对象
     * @param defValue 转换异常默认值
     * @return 整数
     */
    public static int string2Int(Object obj, int defValue) {
    	if(obj==null){
    		return defValue;
    	}
        try {
            return Integer.parseInt(obj.toString());
        } catch (Exception ex) {
            return defValue;
        }
    }

    /**
     * 判断一个字符串（如11.0）是否为整数，是则返回整数，否则返回-1
     * @param strValue 字符串
     * @return
     */
    public static int string2Int2(String strValue) {
    	try {
    		if(StringHelper.isEmpty(strValue)){
    			return 0;
    		}
	    	Double dvalue = Double.valueOf(strValue);
	    	int ivalue = dvalue.intValue();
	    	if(dvalue==ivalue){
	    	   return ivalue;
	    	}
    	}catch (Exception ex) {
            return -1;
        }
    	return -1;
    }

    /**
     * 判断一个字符串（11.0）是否为整数，是则返回整数，否则返回值

     * @param strValue
     * @return
     */
    public static int string2Int2(String strValue,int defValue) {
    	try {
    		if(strValue==null){
    			return 0;
    		}
	    	Double dvalue = Double.valueOf(strValue);
	    	int ivalue = dvalue.intValue();
	    	if(dvalue==ivalue){
	    	   return ivalue;
	    	}
    	}catch (Exception ex) {
            return defValue;
        }

    	return defValue;
    }

    /**
     * 对象转换为Integer对象，可设置转换异常返回值。
     * @param strValue 对象
     * @param defValue 转换异常返回值
     * @return 转换后Integer对象
     */
    public static Integer getIntegerValue(Object strValue, int defValue) {
        try {
            return Integer.valueOf(StringHelper.null2String(strValue));
        } catch (Exception ex) {
            return new Integer(defValue);
        }
    }

    /**
     * 对象转换为Integer对象，转换异常返回0。
     * @param strValue 对象
     * @return 转换后Integer对象
     */
    public static Integer getIntegerValue(Object strValue){
    	return getIntegerValue(strValue,0);
    }


    /**
     * 字符串转换为float对象，转换异常返回-1。
     * @param strValue 字符串
     * @return 转换后的float对象
     */
    public static float string2Float(String strValue) {
        return string2Float(strValue, -1);
    }


    /**
     * 字符串转换为float对象，可设置转换异常返回值。
     * @param strValue 字符串
     * @param defValue 转换异常返回值
     * @return 转换后的float对象
     */
    public static float string2Float(String strValue, float defValue) {
        try {
        	String changeValue=strValue.replace(",","");
            return Float.parseFloat(changeValue);
        } catch (Exception ex) {
            return defValue;
        }
    }

    /**
     *
     * @param strValue
     * @return
     */
    public static double string2Double(String strValue) {
        return string2Double(strValue, -1);
    }

    /**
     * 格式化金额每三位加逗号。
     * @param money double类型数字
     * @return
     */
    public static String moneyAddComma(double money) {
      return new java.text.DecimalFormat("#,##0.00").format(money);
    }

    /**
     * 格式化金额每三位加逗号。
     * @param money String类型
     * @return
     */
    public static String moneyAddComma(String money) {
      double tempmoney = string2Double(money,0);
      return new java.text.DecimalFormat("#,##0.00").format(tempmoney);
    }

    /**
     * 格式化金额每三位加逗号。
     * @param money Object类型
     * @return
     */
    public static String moneyAddComma(Object money) {
      double tempmoney = string2Double(money,0);
      return new java.text.DecimalFormat("#,##0.00").format(tempmoney);
    }

    /**
     * 格式化数据对象，显示指定的小数位
     * @param numStr 需要格式化的数据对象
     * @param digit 小数位
     * @return
     */
    public static String fixedDigits(Object numStr, int digit){
    	double tempNum = string2Double(numStr,0);
    	/*
    	NumberFormat nf = NumberFormat.getNumberInstance();
    	nf.setMaximumFractionDigits(digit);
    	nf.setMinimumFractionDigits(digit);
    	String s = nf.format(tempNum);
    	*/
    	String s = String.format("%."+digit+"f",tempNum);
    	return s;
    }
    /**
     * 字符串转换为double类型，可设置转换异常默认值。
     * @param strValue 字符串
     * @param defValue 转换异常默认值
     * @return 转换后的double类型
     */
    public static double string2Double(String strValue, double defValue) {
        try {
        	if(strValue == null)
        		return defValue;
        	String changeValue=strValue.replace(",","");
            return Double.parseDouble(changeValue);
        } catch (Exception ex) {
            return defValue;
        }
    }

    /**
     * 对象转换为double类型，可设置转换异常默认值。
     * @param obj 对象
     * @param defValue 转换异常默认值
     * @return 转换后的double类型
     */
    public static double string2Double(Object obj, double defValue) {
    	if(obj==null){
    		return defValue;
    	}
        try {
            return Double.parseDouble(obj.toString());
        } catch (Exception ex) {
            return defValue;
        }
    }

    /**
     * 获取随机数。
     * @param min 最小值
     * @param max 最大值
     * @return 生成的随机数
     */
    public static int getRandomInt(int min,int max){

    	Random random = new Random();
    	return Math.abs(random.nextInt()) % (max-min)+min;
    }

    /**
     * 浮点数四舍五入为整型。
     * @param fValue 浮点数
     * @return 四舍五入后的整数
     */
    public static int float2int(float fValue){
    	int intValue=(int)fValue;
    	float tFloat=fValue-intValue;
    	intValue=(int)(tFloat+fValue);
    	return intValue;
    }

    /**
     *
     * @param num
     * @return
     */
    public static double fixDouble(double num){
    	return fixDouble(num,2);
    }

    /**
     *
     * @param num
     * @param len
     * @return
     */
    public static double fixDouble(double num,int len){
    	if(len<0) len=2;
    	double n=Math.pow(10,len);
    	return (int)Math.floor(num*n)/n;
    }

    /**
     * t
     * @param num
     * @param len
     * @return
     */
    public static double fixDoubleCeil(double num,int len){
    	if(len<0) len=2;
    	double n=Math.pow(10, len);
    	return (int)Math.ceil(num*n)/n;
    }
    /**
     * 字符串类型转换为BigDecimal类型(运算货币使用)
     * @param strValue
     * @param defValue
     * @return
     */
    public static BigDecimal string2BigDecimal(String strValue, String defValue) {
        try {
            //return Integer.parseInt(strValue);
            return new BigDecimal(strValue);
        } catch (Exception ex) {
            return new BigDecimal(defValue);
        }
    }
    /**
     * 字符串类型相加(运算货币使用)
     * @param strValue1
     * @param strValue2
     * @return
     */
    public static String stringAdd(String strValue1, String strValue2) {
        try {
        	BigDecimal a = string2BigDecimal(strValue1,"0");
        	BigDecimal b = string2BigDecimal(strValue2,"0");
        	return StringHelper.null2String(a.add(b).setScale(2, BigDecimal.ROUND_HALF_UP));
        } catch (Exception ex) {
            return "";
        }
    }
    /**
     * 字符串类型相减(运算货币使用)
     * @param strValue1
     * @param strValue2
     * @return
     */
    public static String stringSubtract(String strValue1, String strValue2) {
        try {
        	BigDecimal a = string2BigDecimal(strValue1,"0");
        	BigDecimal b = string2BigDecimal(strValue2,"0");
        	return StringHelper.null2String(a.subtract(b).setScale(2, BigDecimal.ROUND_HALF_UP));
        } catch (Exception ex) {
            return "";
        }
    }
    /**
     * 字符串类型相乘(运算货币使用)
     * @param strValue1
     * @param strValue2
     * @return
     */
    public static String stringMu(String strValue1, String strValue2) {
        try {
        	BigDecimal a = string2BigDecimal(strValue1,"0");
        	BigDecimal b = string2BigDecimal(strValue2,"0");
        	return StringHelper.null2String((a.multiply(b)).setScale(2, BigDecimal.ROUND_HALF_UP));
        } catch (Exception ex) {
            return "";
        }
    }

    /**
     * 字符串类型相除(运算货币使用)
     * @param strValue1
     * @param strValue2
     * @return
     */
    public static String stringDivide(String strValue1, String strValue2) {
        return stringDivide(strValue1,strValue2,2);
    }

    /**
     * 字符串类型相除
     * @param strValue1
     * @param strValue2
     * @param scale
     * @return
     */
    public static String stringDivide(String strValue1, String strValue2, int scale) {
        try {
            BigDecimal a = string2BigDecimal(strValue1,"0");
            BigDecimal b = string2BigDecimal(strValue2,"0");
            return StringHelper.null2String(a.divide(b, scale, BigDecimal.ROUND_HALF_UP));
        } catch (Exception ex) {
            return "";
        }
    }

    /**
     * 数字类型相除
     * @param intValue1
     * @param intValue2
     * @return
     */
    public static int intDivide(int intValue1, int intValue2) {
        try {

            BigDecimal a = new BigDecimal(intValue1);
            BigDecimal b = new BigDecimal(intValue2);
            int res = a.divide(b,0,BigDecimal.ROUND_HALF_UP).intValue();
            return res;
        } catch (Exception ex) {
            return 0;
        }
    }

    public static boolean isNumber(String str) {
        for (int i = str.length(); --i >= 0;) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }

        }
        if (NumberHelper.getIntegerValue(str, -1) < 1) {
            return false;
        }
        return true;
    }

    public static Long findNumberOnly(String str) {
        StringBuffer digitsOnly = new StringBuffer();
        for (int i = str.length(); --i >= 0;) {
            char c = str.charAt(i);
            if (Character.isDigit(c)) {
                digitsOnly.append(c);
            }
        }
        return Convert.toLong(digitsOnly.toString());
    }
}
