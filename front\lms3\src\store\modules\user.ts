import { defineStore } from "pinia";
import {
  type userType,
  store,
  router,
  resetRouter,
  routerArrays,
  storageLocal
} from "../utils";
import {
  type UserResult,
  type RefreshTokenResult,
  // getLogin,
  refreshTokenApi
} from "@/api/user";
import { jwtLogin, getUserInfo } from "@/api/system";
import { useMultiTagsStoreHook } from "./multiTags";
import {
  type DataInfo,
  setToken,
  removeToken,
  userKey,
  getToken
} from "@/utils/auth";

export const useUserStore = defineStore("pure-user", {
  state: (): userType => ({
    // 头像
    avatar: storageLocal().getItem<DataInfo<number>>(userKey)?.avatar ?? "",
    // 用户名
    username: storageLocal().getItem<DataInfo<number>>(userKey)?.username ?? "",
    // 昵称
    nickname: storageLocal().getItem<DataInfo<number>>(userKey)?.nickname ?? "",
    // 页面级别权限
    roles: storageLocal().getItem<DataInfo<number>>(userKey)?.roles ?? [],
    // 按钮级别权限
    permissions:
      storageLocal().getItem<DataInfo<number>>(userKey)?.permissions ?? [],
    // 是否勾选了登录页的免登录
    isRemembered: false,
    // 登录页的免登录存储几天，默认7天
    loginDay: 7,
    // 用户ID
    userid: "",
    // 人员ID
    personid: "",
    // 卡号
    cardNum: "",
    // 人员姓名
    personname: "",
    // 部门ID
    deptid: "",
    // 部门名称
    deptname: "",
    // 专业ID
    specialityid: "",
    // 职责ID
    dutyid: "",
    // 设备ID
    equipmentid: "",
    // 人员类型
    persontype: "",
    // 用户类型
    usertype: null,
    // 令牌
    token: "",
    // 登录信息
    logininfo: "0",
    // 登录消息
    loginmsg: "",
    // 当前日期
    curdate: "",
    // 人员头像
    personimage: "",
    // 级别
    slevel: null,
    // 语言
    language: "",
    // 角色ID
    roleid: "",
    // 角色名称
    rolename: "",
    // 显示对话框
    showDialog: false
  }),
  actions: {
    /** 存储用户名 */
    SET_USERNAME(username: string) {
      this.username = username;
    },
    /** 存储昵称 */
    SET_NICKNAME(nickname: string) {
      this.nickname = nickname;
    },
    /** 存储角色 */
    SET_ROLES(roles: Array<string>) {
      this.roles = roles;
    },
    /** 存储按钮级别权限 */
    SET_PERMS(permissions: Array<string>) {
      this.permissions = permissions;
    },
    /** 存储是否勾选了登录页的免登录 */
    SET_ISREMEMBERED(bool: boolean) {
      this.isRemembered = bool;
    },
    /** 设置登录页的免登录存储几天 */
    SET_LOGINDAY(value: number) {
      this.loginDay = Number(value);
    },
    /** 存储用户ID */
    SET_USERID(userid: string) {
      this.userid = userid;
    },
    /** 存储人员ID */
    SET_PERSONID(personid: string) {
      this.personid = personid;
    },
    /** 存储卡号 */
    SET_CARDNUM(cardNum: string) {
      this.cardNum = cardNum;
    },
    /** 存储人员姓名 */
    SET_PERSONNAME(personname: string) {
      this.personname = personname;
    },
    /** 存储部门ID */
    SET_DEPTID(deptid: string) {
      this.deptid = deptid;
    },
    /** 存储部门名称 */
    SET_DEPTNAME(deptname: string) {
      this.deptname = deptname;
    },
    /** 存储专业ID */
    SET_SPECIALITYID(specialityid: string) {
      this.specialityid = specialityid;
    },
    /** 存储职责ID */
    SET_DUTYID(dutyid: string) {
      this.dutyid = dutyid;
    },
    /** 存储设备ID */
    SET_EQUIPMENTID(equipmentid: string) {
      this.equipmentid = equipmentid;
    },
    /** 存储人员类型 */
    SET_PERSONTYPE(persontype: string) {
      this.persontype = persontype;
    },
    /** 存储用户类型 */
    SET_USERTYPE(usertype: any) {
      this.usertype = usertype;
    },
    /** 存储令牌 */
    SET_TOKEN(token: string) {
      this.token = token;
    },
    /** 存储登录信息 */
    SET_LOGININFO(logininfo: string) {
      this.logininfo = logininfo;
    },
    /** 存储登录消息 */
    SET_LOGINMSG(loginmsg: string) {
      this.loginmsg = loginmsg;
    },
    /** 存储当前日期 */
    SET_CURDATE(curdate: string) {
      this.curdate = curdate;
    },
    /** 存储人员头像 */
    SET_PERSONIMAGE(personimage: string) {
      this.personimage = personimage;
    },
    /** 存储级别 */
    SET_SLEVEL(slevel: any) {
      this.slevel = slevel;
    },
    /** 存储语言 */
    SET_LANGUAGE(language: string) {
      this.language = language;
    },
    /** 存储角色ID */
    SET_ROLEID(roleid: string) {
      this.roleid = roleid;
    },
    /** 存储角色名称 */
    SET_ROLENAME(rolename: string) {
      this.rolename = rolename;
    },
    /** 存储显示对话框状态 */
    SET_SHOWDIALOG(showDialog: boolean) {
      this.showDialog = showDialog;
    },
    /** 登入 */
    async loginByUsername(data) {
      return new Promise<UserResult>((resolve, reject) => {
        // getLogin(data)
        jwtLogin(data)
          .then(data => {
            if (!data.success) {
              reject(data);
              return;
            }
            const result = {
              result: data.result, // token
              timestamp: data.timestamp // 过期时间
            };
            if (data?.success) setToken(result);
            resolve(data);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    async GetUserInfo(data) {
      return new Promise<any>((resolve, reject) => {
        const userid = this.token;
        getUserInfo(userid)
          .then(res => {
            const data = res.result;
            // this.SET_TOKEN(data.token)
            this.SET_USERID(data.userid);
            this.SET_USERNAME(data.username);
            this.SET_PERSONID(data.personid);
            this.SET_CARDNUM(data.cardNum);
            this.SET_PERSONNAME(data.personname);
            this.SET_DEPTID(data.departid);
            this.SET_DEPTNAME(data.departmentname);
            this.SET_SPECIALITYID(data.specialityid);
            this.SET_DUTYID(data.dutyid);
            this.SET_EQUIPMENTID(data.equipmentid);
            this.SET_ROLES(data.rights);
            this.SET_PERSONTYPE(data.persontype);
            this.SET_USERTYPE(data.usertype);
            this.SET_CURDATE(data.logindate);
            this.SET_PERSONIMAGE(data.personimage);
            this.SET_LANGUAGE(data.language);
            this.SET_ROLEID(data.roleid);
            this.SET_ROLENAME(data.rolename);
            this.SET_SLEVEL(data.slevel);
            resolve(data);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    /** 前端登出（不调用接口） */
    logOut() {
      this.username = "";
      this.roles = [];
      this.permissions = [];
      this.userid = "";
      this.personid = "";
      this.cardNum = "";
      this.personname = "";
      this.deptid = "";
      this.deptname = "";
      this.specialityid = "";
      this.dutyid = "";
      this.equipmentid = "";
      this.persontype = "";
      this.usertype = null;
      this.token = "";
      this.logininfo = "0";
      this.loginmsg = "";
      this.curdate = "";
      this.personimage = "";
      this.slevel = null;
      this.language = "";
      this.roleid = "";
      this.rolename = "";
      this.showDialog = false;
      removeToken();
      useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
      resetRouter();
      router.push("/login");
    },
    /** 刷新`token` */
    async handRefreshToken(data) {
      return new Promise<RefreshTokenResult>((resolve, reject) => {
        refreshTokenApi(data)
          .then(data => {
            if (data) {
              setToken(data.data);
              resolve(data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    }
  }
});

export function useUserStoreHook() {
  return useUserStore(store);
}
