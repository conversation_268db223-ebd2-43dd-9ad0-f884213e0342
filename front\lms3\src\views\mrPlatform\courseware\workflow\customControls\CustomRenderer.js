import BaseRenderer from "diagram-js/lib/draw/BaseRenderer";
import { is } from "bpmn-js/lib/util/ModelUtil";
import { isAny } from "bpmn-js/lib/features/modeling/util/ModelingUtil";

const HIGH_PRIORITY = 1500;

export default class CustomRenderer extends BaseRenderer {
  constructor(eventBus, bpmnRenderer) {
    super(eventBus, HIGH_PRIORITY);
    this.bpmnRenderer = bpmnRenderer;
  }

  canRender(element) {
    return (
      isAny(element, [
        "bpmn:Task",
        "bpmn:Event",
        "bpmn:Gateway",
        "bpmn:Activity",
        "bpmn:Participant",
        "bpmn:Group",
        "bpmn:DataStoreReference"
      ]) && !element.labelTarget
    );
  }

  drawShape(parentNode, element) {
    const shape = this.bpmnRenderer.drawShape(parentNode, element);
    if (is(element, "bpmn:StartEvent")) {
      shape.style.setProperty("stroke", "#000");
      return shape;
    } else if (is(element, "bpmn:EndEvent")) {
      shape.style.setProperty("stroke", "#000");
      shape.style.setProperty("stroke-width", "6px");
      return shape;
    } else if (
      is(element, "bpmn:ExclusiveGateway") ||
      is(element, "bpmn:InclusiveGateway") ||
      is(element, "bpmn:ParallelGateway") ||
      is(element, "bpmn:ComplexGateway") ||
      is(element, "bpmn:EventBasedGateway")
    ) {
      shape.style.setProperty("stroke", "#000");
      let pathList = parentNode.getElementsByTagName("path");

      Array.from(pathList)?.forEach(item => {
        item.style.setProperty("stroke", "#000");
        item.style.setProperty("fill", "#fff");
      });
      let circleList = parentNode.getElementsByTagName("circle");
      Array.from(circleList)?.forEach(item => {
        item.style.setProperty("stroke", "#000");
      });
      return shape;
    } else if (is(element, "bpmn:Activity")) {
      shape.style.setProperty("stroke", "#000");
      let pathList = parentNode.getElementsByTagName("path");
      Array.from(pathList).forEach(item => {
        item.style.setProperty("stroke", "#000");
      });

      let textList = parentNode.getElementsByTagName("text");
      Array.from(textList)?.forEach(item => {
        item.style.setProperty("fill", "#fff");
      });
      return shape;
    } else if (is(element, "bpmn:DataStoreReference")) {
      shape.style.setProperty("stroke", "#000");
      let pathList = parentNode.getElementsByTagName("path");
      Array.from(pathList)?.forEach(item => {
        item.style.setProperty("stroke", "#000");
      });
      return shape;
    }
  }
}

CustomRenderer.$inject = ["eventBus", "bpmnRenderer"];
