package com.sbtr.workflow.service;

import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import com.sbtr.workflow.model.ActFormConfigure;
import com.sbtr.workflow.model.ActMyNodeButton;
import com.sbtr.workflow.vo.ActMyNodeButtonVo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 流程每个节点所对应按钮
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-23 07:12:25
 */
public interface ActMyNodeButtonService extends WorkflowBaseService<ActMyNodeButton, String> {

    /**
     * 分页
     *
     * @return
     * @Param pageParam 分页参数
     * @Param filterSort 过滤排序字段
     */
    PageSet<ActMyNodeButton> getPageSet(PageParam pageParam);

    /**
     * 批量删除
     *
     * @return
     * @Param uuid 主键
     */
    int executeDeleteBatch(String[] uuids);

    /**
     * @MethodName insertList
     * @Description  批量插入
     * @Param list
     * @Return java.lang.Integer
     * <AUTHOR>
     * @Date 2020-10-26 20:10
     */
    Integer insertListMySql(List<ActMyNodeButton> list);

    Integer insertListOracle(List<ActMyNodeButton> list);

    /**
     * 根据模型key以及流程定义 查询详细数据
     *
     * @return
     */
    List<ActMyNodeButton> getListByActDeModelKeyAndProcdefId(String key, String procdefIds);

    /**
     * 根据模型key以及流程定义更新数据
     *
     * @return
     */
    Integer updateProcdefIdByModelKey(String key, String procdefId);

    /**
     * 根据模型key以及流程定义 吧流程定义Id置为空
     *
     * @return
     */
    Integer updateProcdefIdIsNull(String key, String id);

    /**
     * 据模型key以及流程定义 删除数据
     *
     * @return
     */
    Integer deleteByModelKeyAnProcdefId(String key, String id);


    /**
     * 根据模型key以及流程定义 节点id 查询详细数据
     *
     * @return
     */
    List<ActMyNodeButtonVo> getListByActDeModelKeyAndProcdefIdAndNodeId(String modelKey, String processDefinitionId, String nodeId);

    /**
     * 根据业务id查询sql语句
     *
     * @return
     */
    HashMap<String, Object> selectByBusinessKey(String sql);

    Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id);

    Map<String, Object> getDetailByModelKey(String emptyToNull);
}
