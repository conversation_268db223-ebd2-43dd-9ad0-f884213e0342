<template>
  <a-modal
    :open="visible"
    :confirm-loading="loading"
    width="600px"
    :destroy-on-close="true"
    class="dictionary-add-modal"
    @cancel="handleCancel"
  >
    <!-- 自定义标题 -->
    <template #title>
      <div class="modal-title">
        <h3>{{ isEdit ? "编辑数据字典选项" : "新增数据字典选项" }}</h3>
      </div>
    </template>

    <div class="dictionary-add-form">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="horizontal"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="选项名称" name="dictionaryType">
          <a-input
            v-model:value="formData.objname"
            placeholder="请输入选项名称"
          />
        </a-form-item>

        <a-form-item label="代码">
          <a-input v-model:value="formData.code" placeholder="请输入代码" />
        </a-form-item>

        <a-form-item label="描述">
          <a-textarea
            v-model:value="formData.objdesc"
            placeholder="请输入描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </div>

    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" :loading="loading" @click="handleSubmit">
          提交
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import type { FormInstance } from "ant-design-vue";
import type { DictionaryForm, DictionaryItem } from "@/types/system";
import { addSelectitem, editSelectitem } from "@/api/system.ts";
import { message } from "ant-design-vue";
interface Props {
  visible: boolean;
  code?: String;
  dictionaryitem?: DictionaryItem | null;
  isEdit?: boolean;
}

interface Emits {
  (e: "close", visible: boolean): void;
  (e: "submit", data: DictionaryForm): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const loading = ref(false);

// 表单数据
let formData = {};

// 表单验证规则
const rules = {
  objname: [{ required: true, message: "请输入选项名称" }]
};

// 监听弹窗显示状态，初始化表单数据
watch(
  () => props.visible,
  visible => {
    if (visible) {
      if (!props.dictionaryitem) return;
      formData = props.dictionaryitem;
    }
  }
);

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;
    let res = props.isEdit
      ? await editSelectitem(formData)
      : await addSelectitem(formData);
    if (res.success) {
      message.success(res.message);
      emit("submit", { ...formData });
      handleCancel();
    } else {
      message.error(res.message);
    }
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit("close", false);
};
</script>

<style lang="scss" scoped>
.modal-title {
  background-color: var(--gray-100);
  margin: -24px -24px 24px -24px;
  padding: var(--spacing-6);

  h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
    color: var(--gray-900);
    margin: 0;
  }
}

.dictionary-add-form {
  max-height: 500px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-3);
}

// 表单样式
:deep(.ant-form-item) {
  margin-bottom: var(--spacing-4);
}

:deep(.ant-form-item-label) {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
}

:deep(.ant-input),
:deep(.ant-select),
:deep(.ant-textarea) {
  width: 100%;
}

// 自定义标题样式
:deep(.ant-modal-header) {
  padding: 0;
  border: 0;
}

:deep(.ant-modal-title) {
  padding: 0;
}
</style>
