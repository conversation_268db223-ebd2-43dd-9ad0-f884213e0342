package com.lms.system.users.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.system.feign.model.Users;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.repository.query.Param;

import java.util.List;

@Mapper
public interface UsersMapper extends BaseMapper<Users> {

    @Select(value = "select * from p_user where status = 1 and name = #{userName} and if(#{id}='' or #{id} is null,1=1,id <> #{id}) limit 1")
    Users existUser(@Param("userName") String userName, @Param("id") String id);


}
