/**
 *
 */
package com.lms.common.model;

/**
 * <AUTHOR> modleMap中key的值
 */
public class ModelMapKey {

    /**
     * 标识当前结果状态的代码，一般“0”表示操作成功，“1”表示默认错误,非零正整数表示不同的错误类型（由业务对象决定）
     */

    public static final int SUCCESSCODE = 0;

    public static final int ERRORCODE = 1;

    public static final String STATUSCODE = "statusCode";

    public static final String RESULTKEY = "userList";

    public static final String RESULTDATAKEY = "userData";

    public static final String ERRORMESSAGE = "errorMessage";

    /**
     * 后端返回至前端提示信息，正确的提醒信息
     */
    public static final String PROMPTMESSAGE = "promptMessage";

    public static final String OBJECTID = "objid";

    // 所有的记录条数
    public static final String LIST_COUNT = "pageInfo";

    public static final String PAGESIZE = "pageSize";

    public static final String PAGEINDEX = "pageIndex";

    public static final String TableObject = "tableObject";

    public static final String ParameterList = "ParameterList";

}
