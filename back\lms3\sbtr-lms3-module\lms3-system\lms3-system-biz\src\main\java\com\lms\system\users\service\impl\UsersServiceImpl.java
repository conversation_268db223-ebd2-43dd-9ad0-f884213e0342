package com.lms.system.users.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lms.common.feign.dto.Department;
import com.lms.common.feign.dto.Person;
import com.lms.common.feign.dto.Selectitem;
import com.lms.system.feign.model.Role;
import com.lms.system.role.service.impl.RoleServiceImpl;
import com.lms.system.users.mapper.UsersMapper;
import com.lms.system.feign.model.Users;
import com.lms.common.feign.dto.Setting;
import com.lms.common.model.JwtUser;
import com.lms.common.model.Result;
import com.lms.common.model.SettingConfig;
import com.lms.common.service.impl.BaseServiceImpl;
import com.lms.common.util.ConstParamUtil;
import com.lms.common.util.DateHelper;
import com.lms.common.util.EncryptHelper;
import com.lms.common.util.StringHelper;
import com.lms.system.users.service.UsersService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;

@Service("UsersService")
public class UsersServiceImpl extends BaseServiceImpl<UsersMapper, Users> implements UsersService {

    @Resource
    private UsersMapper usersDao;
    @Resource
    private RoleServiceImpl roleService;


    public Boolean existUser(String userName, String id) {
        boolean flag = false;
        Users users = usersDao.existUser(userName, id);

        if (users != null) {
            flag = true;
        }
        return flag;
    }


    public Users getUsersById(String id) {
        return this.getById(id);
    }

    public Users getUsersByPersonId(String personid) {
        LambdaQueryWrapper<Users> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Users::getPersonid, personid);
        return this.getOne(wrapper);
    }


    public Users getUsersByUserName(String userName) {
        LambdaQueryWrapper<Users> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Users::getName, userName);
        return this.getOne(wrapper);
    }

    public JwtUser createJwtUser(Users users) {
        JwtUser jwtUser = new JwtUser();
        if (users != null) {
            Department departInfo = null;
            Person person = null;
            String name = "";
            if (!StringHelper.isEmpty(users.getPersonid())) {
                Optional<Person> personOptional = Optional.ofNullable(commonBaseApi.getPersonById(users.getPersonid()).getResult());
                if (personOptional.isPresent()) {
                    person = personOptional.get();
                    if (!StringHelper.isEmpty(person.getDepartmentid())) {
                        departInfo = commonBaseApi.getDepartmentById(person.getDepartmentid()).getResult();
                    }
                    name = person.getName();
                }
            }

            jwtUser.setUserid(users.getId());
            jwtUser.setUsername(users.getName());
            jwtUser.setIslocked(users.isIslocked());
            jwtUser.setPassword(users.getPassword());
            jwtUser.setUsertype(users.getUsertype());
            jwtUser.setPersonid(person.getId());
            jwtUser.setPersonname(person.getName());
            jwtUser.setSpecialityid(person.getSpecialityid());
            jwtUser.setPersonlevelid(person.getPersonlevelid());
            jwtUser.setDutyid(person.getDuty());
            jwtUser.setEquipmentid(person.getEquipmentid());
            jwtUser.setTeachingmodel(person.getTeachingmodel());
            jwtUser.setManagemodel(person.getManagemodel());
            jwtUser.setSlevel(person.getSlevel());
            jwtUser.setPersontype(person.getPersontype());
            jwtUser.setCardNum(person.getCardNum());
            jwtUser.setPersonimage(StringHelper.null2String(person.getImage()));
            if (departInfo != null) {
                jwtUser.setDepartid(departInfo.getId());
                jwtUser.setDepartmentname(departInfo.getName());
            }
            jwtUser.setLogindate(DateHelper.getCurrentDate());
            String roleid = StringHelper.null2String(users.getRoleid());
            if (!roleid.isEmpty()) {
                jwtUser.setRoleid(StringHelper.isEmpty(users.getRoleid()) ? person.getPersontype().split(",")[0] : users.getRoleid());
                Role role = roleService.getById(jwtUser.getRoleid());
                jwtUser.setPersonname(name);
                jwtUser.setRolename(role.getName());
            }
//            String languageSetting = lmsSystemApi.getSyetemLanguage().getResult();
//            if (!StringHelper.isEmpty(languageSetting))
            jwtUser.setLanguage("Chinese");
            jwtUser = setUserRoles(jwtUser);
        }
        return jwtUser;
    }

    public JwtUser setUserRoles(JwtUser jwtUser) {
        Optional<Person> personOptional = Optional.ofNullable(commonBaseApi.getPersonById(jwtUser.getPersonid()).getResult());
        String roleIds = personOptional.isPresent() ? StringHelper.null2String(personOptional.get().getPersontype()) : "";
        List roleList = new ArrayList<>();
        if (!roleIds.isEmpty()) {
            roleList = Arrays.asList(roleIds.split(","));
        }
        jwtUser.setRoles(roleList);
        return jwtUser;
    }


    public void clearUserCache(Users users) {
        JwtUser jwtUser = createJwtUser(users);
        redisUtil.del(ConstParamUtil.USER_TOKEN_KEY + users.getId());
        redisUtil.set(ConstParamUtil.USER_TOKEN_KEY + users.getId(), JSON.toJSONString(jwtUser), ConstParamUtil.TOKEN_EXPIRATION_TIME);
    }

    public Result<JwtUser> login(String userName, String password, String ipaddr, String clientagent) {
        Users users = getUsersByUserName(userName);
        JwtUser jwtUser = new JwtUser();
        if (users != null) {
            // 校验账号状态
            if (users.getStatus() == 0) {
                return Result.error("您输入的用户名或密码错误！", jwtUser);
            }
            // 校验锁定状态
            if (users.isIslocked()) {
                return Result.error("当前用户账号已被锁定，无权登录！", jwtUser);
            }
            //验证密码
            String loginPass = EncryptHelper.md5Password(password);
            boolean is_password = loginPass.equals(users.getPassword());
            int lockedTimes = 0; //锁定次数
            Result<Setting> passwordLockTimeSettingResult = commonSystemApi.getById(SettingConfig.CONFIG_PASSWORDLOCKTIME);
            if (passwordLockTimeSettingResult != null && passwordLockTimeSettingResult.getResult() != null) {
                Setting passwordLockTimeSetting = passwordLockTimeSettingResult.getResult();
                lockedTimes = Integer.parseInt(passwordLockTimeSetting.getItemvalue());
            }
            if (lockedTimes == 0) {
                return Result.error("系统配置获取失败,请重试", jwtUser);
            }
            if (!is_password) {
                // 密码错误 更新错误次数,判断是否已达到最大错误次数，达到则锁定账户
                int t = users.getPsderrortimes() + 1; // 已错误次数
                users.setPsderrortimes(t);
                if (t >= lockedTimes) {
                    users.setIslocked(true);
                }
                saveOrUpdate(users);
                return Result.error("您输入的用户名或密码错误！", jwtUser);
            } else {
                users.setPsderrortimes(0);
                users.setIslocked(false);
                saveOrUpdate(users);
            }

            // 验证密码设置更换周期
            long day = 0L;
            Result<Setting> passwordChangeSettingResult = commonSystemApi.getById(SettingConfig.CONFIG_PASSWORDCHANGE);
            if (passwordChangeSettingResult != null && passwordChangeSettingResult.getResult() != null) {
                Setting passwordChangeSetting = passwordChangeSettingResult.getResult();
                day = Long.parseLong(passwordChangeSetting.getItemvalue());
            }
            String currentDate = DateHelper.getCurrentDate();
            String psdUpdateDate = users.getPsdupdatedate();
            long daysBetween = DateHelper.getDaysBetween(currentDate, psdUpdateDate); //已修改密码天数
            if (day == 0L) {
                return Result.error("系统配置获取失败,请重试", jwtUser);
            }
//            if (daysBetween - day > 0) {
//                Result<JwtUser> error = Result.error("密码已" + daysBetween + "天未修改,请及时修改密码！", jwtUser);
//                error.setStatusCode(501);
//                return error;
//            }
            if (lmsConfiguration.getClientCheck()) {
                // 验证登录用户IP与浏览器类型
                if (!StringUtils.contains(ipaddr, users.getIpaddr())) {
                    return Result.error("当前登录客户端ip与账号设定的登录ip地址不一致！", jwtUser);
                }
                String usersClientagentIds = users.getClientagent();
                if (StringUtils.isNotEmpty(usersClientagentIds)) {
                    String[] split = usersClientagentIds.split(",");
                    for (String usersClientagentId : split) {
                        String usersClientagent = "";
                        Result<Selectitem> selectitemResult = commonBaseApi.getSelectitemById(usersClientagentId);
                        if (selectitemResult != null && selectitemResult.getResult() != null) {
                            Selectitem selectitem = selectitemResult.getResult();
                            usersClientagent = selectitem.getCode();
                        }
                        if (!StringUtils.containsIgnoreCase(clientagent, usersClientagent)) {
                            return Result.error("当前使用的浏览器与账号设定的浏览器不一致！", jwtUser);
                        }
                    }
                } else {
                    return Result.error("当前使用的浏览器与账号设定的浏览器不一致！", jwtUser);
                }

            }
            // 创建JWT用户
            jwtUser = createJwtUser(users);
            //验证用户涉密等级
            if (ObjectUtil.isEmpty(jwtUser.getSlevel())) {
                return Result.error("当前用户未分配涉密等级，无权登录！", jwtUser);
            }
            users.setPsderrortimes(0);
            users.setLastlogintime(DateHelper.getCurDateTime());
            saveOrUpdate(users);
            return Result.OK(jwtUser);
        } else {
            return Result.error("您输入的用户名或密码错误！", jwtUser);
        }
    }

    public Result checkpassword(String password) {
        int length = 0;
        Result<Setting> passwordLengthSettingResult = commonSystemApi.getById(SettingConfig.CONFIG_PASSWORDLENGTH);
        if (passwordLengthSettingResult != null && passwordLengthSettingResult.getResult() != null) {
            Setting passwordChangeSetting = passwordLengthSettingResult.getResult();
            length = Integer.parseInt(passwordChangeSetting.getItemvalue());
        }
        if (length == 0) {
            return Result.error("系统配置获取失败,请重试");
        }
        List<String> rule = new ArrayList<>();
        Result<Setting> passwordRuleSettingResult = commonSystemApi.getById(SettingConfig.CONFIG_PASSWORDRULE);
        if (passwordRuleSettingResult != null && passwordRuleSettingResult.getResult() != null) {
            Setting passwordRuleSetting = passwordRuleSettingResult.getResult();
            if (StringUtils.isNotEmpty(passwordRuleSetting.getItemvalue())) {
                String[] split = StringUtils.split(passwordRuleSetting.getItemvalue(), ",");
                rule = Arrays.asList(split);
            }
        }
        if (CollectionUtils.isEmpty(rule)) {
            return Result.error("系统配置获取失败,请重试");
        }
        HashMap<String, Object> amap = new HashMap<>();
        String a1 = "0-9"; //数字
        String a2 = "A-Z"; //大写字母
        String a3 = "a-z"; //小写字母
        String a4 = "~!@#$%^&*()_+\\-={}|\\[\\]\\\\:\";'<>?,./"; //字符
        amap.put("1", a1);
        amap.put("2", a2);
        amap.put("3", a3);
        amap.put("4", a4);
        HashMap<String, Object> bmap = new HashMap<>();
        String b1 = "(?![A-Za-z\\W]+$)"; //必须包含数字
        String b2 = "(?![0-9a-z\\W]+$)"; //必须包含大写字母
        String b3 = "(?![0-9A-Z\\W]+$)"; //必须包含小写字母
        String b4 = "(?![0-9A-Za-z]+$)"; //必须包含字符
        bmap.put("1", b1);
        bmap.put("2", b2);
        bmap.put("3", b3);
        bmap.put("4", b4);
        StringBuilder sb1 = new StringBuilder("[");
        StringBuilder sb2 = new StringBuilder("^");
        StringBuilder errmsg = new StringBuilder("密码校验失败：长度必须大于等于[").append(length).append("]位 , ");

        amap.forEach((k, v) -> {
            sb1.append(v);
        });

        for (String code : rule) {
            String b = (String) bmap.get(code);
            sb2.append(b);
            adaptPasswordCheckErrMsg(errmsg, code);
        }

        sb2.append(sb1).append("]").append("{").append(length).append(",}").append("$");
        boolean matches = Pattern.matches(sb2.toString(), password);
        if (matches) {
            return Result.OK();
        } else {
            return Result.error(errmsg.toString());
        }
    }

    private void adaptPasswordCheckErrMsg(StringBuilder errmsg, String code) {
        switch (code) {
            case "1":
                errmsg.append("必须包含数字  ");
                break;
            case "2":
                errmsg.append("必须包含大写字母  ");
                break;
            case "3":
                errmsg.append("必须包含小写字母  ");
                break;
            case "4":
                errmsg.append("必须包含字符  ");
                break;
        }
    }

    public List<Users> getUsersByPersonIds(List<String> idList) {
        LambdaQueryWrapper<Users> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Users::getPersonid, idList);
        return this.list(wrapper);
    }

    public void batchDeleteUsers(List<Users> users) {
        this.removeBatchByIds(users);
    }
}
