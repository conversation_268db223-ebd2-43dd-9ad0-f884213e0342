package com.lms.base.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * UWrongsubject entity. <AUTHOR> Persistence Tools
 */
@EqualsAndHashCode(callSuper = true)

@TableName("u_wrongsubject")
@Data
@ApiModel(value = "错误试题实体类")
public class Wrongsubject extends BaseModel {

    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private String id;

    @ApiModelProperty("用户id")
    private String personid;

    @ApiModelProperty("试题id")
    private String subjectid;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("序号")
    private Integer seqno;

    @ApiModelProperty("创建时间")
    private String createtime;


}
