package com.lms.system.attach.controller;

import com.lms.common.config.LMSConfiguration;
import com.lms.common.controller.BaseController;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.RsaUtils;
import com.lms.common.util.StringHelper;
import com.lms.system.attach.service.AttachService;
import com.lms.system.feign.model.Attach;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@RestController
@CrossOrigin
@RequestMapping(value = "/attach")
@Api(value = "附件管理", tags = "附件管理")
public class AttachController extends BaseController<Attach> {

    @Resource
    private AttachService attachService;

    @Resource
    private LMSConfiguration lmsConfiguration;

    /**
     * 以下是远程调用新增
     */
    @GetMapping("/get")
    @ApiOperation(value = "根据id获取附件信息", httpMethod = "GET")
    public Result<Attach> get(@RequestParam String id) {
        return Result.OK(attachService.getById(id));
    }

    @GetMapping("/checkFileIsExist")
    @ApiOperation(value = "检查文件是否存在", httpMethod = "GET")
    public Result<Boolean> checkFileIsExist(@ApiParam(value = "文件名称") @RequestParam("objName") String objName) {
        return Result.OK(attachService.checkFileIsExist(objName));
    }

    /**
     * 远程调用
     */
    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "保存或修改附件信息", httpMethod = "POST")
    public Result saveOrUpdate(@RequestBody Attach attach) {
        String attachName = attach.getObjname();
        commonSystemApi.saveLog(attachName, "保存附件信息", LogType.Save, "操作成功");
        if(!StringHelper.isEmpty(attachName) && StringHelper.isEmpty(attach.getId())){
            attachName = RsaUtils.encryptByPublicKey(attachName);
            attach.setObjname(attachName);
        }
        attachService.saveOrUpdate(attach);
        return Result.OK();
    }

    @PostMapping("/saveAttachByFilePath")
    @ApiOperation(value = "根据文件路径保存附件", httpMethod = "POST")
    public Result<String> saveAttachByFilePath(File file) {
        return Result.OK(attachService.saveAttachByFilePath(file));
    }


    @PostMapping(value = {"/getfile"})
    @ApiOperation(value = "根据id获取附件信息并设置是否可下载", httpMethod = "POST")
    public Result<Attach> findcoursewarefile(@RequestParam("id") String attachId, @RequestParam("browser") String browser) {
        Attach attach = attachService.getById(attachId);
        if (attach == null) {
            return Result.error(commonSystemApi.translateContent("文件不存在"), null);
        }
        if (Boolean.FALSE == attachService.checkFileIsExist(attach.getEncryptname()) &&
                !new File(lmsConfiguration.getScormStoragePath() + "/" + attach.getFiledir()).exists()) {
            return Result.error(commonSystemApi.translateContent("文件不存在"), null);
        }
        attach.setIsDownload(!ContextUtil.isSelfSupport(browser, attach.getSuffix()));
        return Result.OK(attach);
    }


    @PostMapping("/uploadFile")
    @ApiOperation(value = "上传文件并保存附件信息", httpMethod = "POST")
    public Result uploadFile(@RequestParam("file") MultipartFile file) throws Exception {
        // scorm课件
        if (StringUtils.isNotEmpty(file.getOriginalFilename())
                && StringUtils.containsIgnoreCase(file.getOriginalFilename(), ".zip")) {
            return attachService.uploadScormFile(file);
        }
        return attachService.uploadFile(file);
    }

    @PostMapping(value = {"/deleteFile"})
    @ApiOperation(value = "删除附件信息", httpMethod = "POST")
    @LMSLog(desc = "删除附件信息", otype = LogType.Delete)
    public Result deleteFile(@RequestBody Attach attach) {
        return Result.OK(attachService.deleteFile(attach));
    }

    @GetMapping(value = {"/delete"})
    @ApiOperation(value = "根据id删除附件", httpMethod = "GET")
    @LMSLog(desc = "根据id删除附件", otype = LogType.Delete)
    public Result delete(@RequestParam String id) {
        attachService.removeById(id);
        return Result.OK();
    }


    @GetMapping(value = {"/downloadFile"})
    @ApiOperation(value = "下载文件", httpMethod = "GET")
    public void downLoadFile(HttpServletResponse response, @RequestParam("attachid") String attachid) throws Exception {
        Attach attach = this.attachService.getById(attachid);
        String filename = attach.getEncryptname();
        commonSystemApi.saveLog(filename, "下载文件", LogType.Export, "操作成功");
        attachService.downLoadFile(filename, response);
    }

    @GetMapping(value = {"/downloadStream"})
    @ApiOperation(value = "下载文件返回输入流", httpMethod = "GET")
    public InputStream downLoadStream(@RequestParam("attachid") String attachid) throws Exception {
        Attach attach = this.attachService.getById(attachid);
        String filename = attach.getEncryptname();
        return attachService.downLoadStream(filename);
    }

    @PostMapping(value = {"/uploadFiles"})
    @ApiOperation(value = "批量上传文件并保存附件信息", httpMethod = "POST")
    public Result uploadFiles(@RequestParam("file") MultipartFile[] files) throws Exception {
        List<Attach> attachs = new ArrayList<Attach>();
        if (files.length > 0) {
            for (MultipartFile file : files) {
                if (file != null && !file.isEmpty()) {
                    String filecontent = StringHelper.null2String(file.getContentType());
                    if (filecontent.equals("image/jpeg") || filecontent.equals("image/png")) {
                        Result result = attachService.uploadFile(file);
                        if (result.isSuccess()) {
                            String attachId = result.getResult().toString();
                            attachs.add(attachService.getById(attachId));
                        } else {
                            return result;
                        }
                    } else {
                        return Result.error("请上传jpg或png格式图片");
                    }
                }
            }
        }
        return Result.OK(attachs);
    }

    // 批量删除
    @DeleteMapping(value = {"/delete"})
    @ApiOperation(value = "批量删除附件信息", httpMethod = "DELETE")
    @LMSLog(desc = "根据id删除附件", otype = LogType.Delete)
    public Result batchAttach(@RequestParam("ids") String ids) {
        String[] idarray = ids.split(",");
        List<String> idList = new ArrayList<String>();
        for (int i = 0; i < idarray.length; i++) {
            String id = idarray[i];
            idList.add(id);
        }
        this.attachService.removeBatchByIds(idList);
        return Result.OK("数据删除成功!");
    }

    @GetMapping(value = {"/get/{id}"})
    @ApiOperation(value = "根据id获取附件id与名称", httpMethod = "GET")
    public Result getAttachInfo(@PathVariable("id") String id) {
        String result = "";
        if (id.indexOf(",") > 0) {
            String[] data = id.split(",");
            for (int i = 0; i < data.length; i++) {
                Attach attach = this.attachService.getById(data[i]);
                if (attach != null) {
                    if (i == data.length - 1) {
                        result += attach.getId() + "_" + attach.getObjname();
                    } else {
                        result += attach.getId() + "_" + attach.getObjname() + ",";
                    }
                }
            }
        } else {
            Attach attach = this.attachService.getById(id);
            if (attach != null) {
                result += attach.getId() + "_" + attach.getObjname();
            }
        }
        return Result.OK(result);
    }

    @PostMapping(value = {"/getByIdList"})
    @ApiOperation(value = "根据id列表获取附件信息", httpMethod = "POST")
    public Result<List> getByIdList(@RequestBody List<String> attachIdList) {
        return Result.OK(attachService.getByIdList(attachIdList));
    }

    @GetMapping("/downloadTemplate")
    @ApiOperation(value = "下载系统导入模板", httpMethod = "GET")
    public void downloadStudentTemplate(HttpServletResponse response, String filename) {
        ClassPathResource resource = new ClassPathResource(filename);
        try {
            InputStream inputStream = resource.getInputStream();
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" +
                    URLEncoder.encode(filename, String.valueOf(StandardCharsets.UTF_8)));
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            OutputStream outputStream = response.getOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            inputStream.close();
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            throw new RuntimeException("文件下载失败");
        }
    }

}
