package com.sbtr.workflow.service;

import com.sbtr.workflow.vo.TaskVo;

import java.util.List;

/**
 * 工作流数据与应用系统的业务数据交换处理
 */
public interface WorkflowDataExchangeService {

    /**
     * 待办任务推送
     *
     * @param tasks 任务列表
     */
    void createTodoTasksListener(List<TaskVo> tasks);

    /**
     * 完成待办事项
     *
     * @param taskVos 任务列表
     */
    void completeTask(List<TaskVo> taskVos);
}
