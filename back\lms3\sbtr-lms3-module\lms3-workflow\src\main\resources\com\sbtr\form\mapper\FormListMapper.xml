<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.form.mapper.FormListMapper">

	<!-- refid id 引用   使用get_dic_item_text 函数可以显示字典text -->
	<sql id="Base_Column_List" >
		uuid,
		create_time,
		creator,
		creator_id,
		creator_org_id,
		modifier_id,
		modifier,
		modify_time,
		basic_configure_uuid,
		list_json
	</sql>

	<!-- 获取分页集 -->
	<select id="getPageSet" resultType="com.sbtr.form.model.FormList">
        select
        <include refid="Base_Column_List"></include>
        from form_list
        <where>
							 				 				 				 				 				 				 				 				 				 
            <if test="filterSort != null">
				${filterSort}
            </if>
        </where>
    </select>

    <select id="selectByBasicConfigureUuid" resultType="com.sbtr.form.model.FormList">
		select
		<include refid="Base_Column_List"></include>
		from form_list where basic_configure_uuid=#{basicConfigureUuid}
	</select>

    <!-- 批量删除 -->
	<delete id="executeDeleteBatch">
		delete from form_list where uuid in
		<foreach item="uuid" collection="array" open="(" separator="," close=")">
			#{uuid}
		</foreach>
	</delete>

    <delete id="deleteByBasicConfigureUuid">
		delete from form_list where basic_configure_uuid =#{basicConfigureUuid}
	</delete>

</mapper>
