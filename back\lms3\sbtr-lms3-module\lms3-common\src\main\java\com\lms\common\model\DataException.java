package com.lms.common.model;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据异常类
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2024-09-14 13:40:36
 */
@Data
@NoArgsConstructor
public final class DataException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private Integer statusCode;

    /**
     * 错误提示
     */
    private String message;

    public DataException(String message) {
        this.message = message;
        this.statusCode = 500;
    }

    public DataException(String message, Integer statusCode) {
        this.message = message;
        this.statusCode = statusCode;
    }

}