package com.sbtr.workflow.vo;

import java.io.Serializable;


/**
 * 流程定义VO
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2019-08-16 17:35:23
 */
public class ProcessDefinitionVo implements Serializable {
    //ID
    protected String id;
    //模型key
    protected String modelKey;
    //名称
    protected String name;
    //版本
    protected int version;
    //分类
    protected String category;
    //部署ID
    protected String deploymentId;
    //资源名称
    protected String resourceName;
    //激活状态 1激活 2挂起
    protected String dgrmResourceName;
    //资源名称
    protected int suspensionState;
    //租户ID
    protected String tenantId;
    //定义ID
    protected String procdefId;
    //描述
    protected String description;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getProcdefId() {
        return procdefId;
    }

    public void setProcdefId(String procdefId) {
        this.procdefId = procdefId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getModelKey() {
        return modelKey;
    }

    public void setModelKey(String modelKey) {
        this.modelKey = modelKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDeploymentId() {
        return deploymentId;
    }

    public void setDeploymentId(String deploymentId) {
        this.deploymentId = deploymentId;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getDgrmResourceName() {
        return dgrmResourceName;
    }

    public void setDgrmResourceName(String dgrmResourceName) {
        this.dgrmResourceName = dgrmResourceName;
    }

    public int getSuspensionState() {
        return suspensionState;
    }

    public void setSuspensionState(int suspensionState) {
        this.suspensionState = suspensionState;
    }
}
