package com.sbtr.workflow.model;

import cn.ewsd.common.model.MCoreBase;
import javax.persistence.Table;

/**
 * 流程每个节点所对应按钮
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-23 07:12:25
 */
@Table(name = "act_my_node_button")
public class ActMyNodeButton extends MCoreBase {
    private static final long serialVersionUID = 1L;

    /**
     * 模型id
     */
    private String actDeModelId;

    /**
     * 模型key
     */
    private String actDeModelKey;

    /**
     * 按钮名称
     */
    private String nodeButtonName;

    /**
     * 按钮code
     */
    private String nodeButtonCode;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 节点Id
     */
    private String nodeId;

    /**
     * 流程表单uuid
     */
    private String flowModelUuid;

    /**
     * 流程定义Id
     */
    private String procdefId;

    /**
     * 流程定节点表单路径义Id
     */
    private String nodeFormPath;

    /**
     * 流程定节点表单路径义Id
     */
    private String appPagePath;

    /**
     * 节点表单获取详情的路径
     */
    private String nodeFormEditPath;

    /**
     * 更新接口地址
     */
    private String nodeFormUpdatePath;

    /**
     * 保存接口地址
     */
    private String nodeFormSavePath;

    /**
     * 当是自己手写表单的时候是否可以编辑 1可以 2不可以
     */
    private String whetherUpdate;

    /**
     * 任务监听器sql语句
     */
    private String taskListenerSqlData;

    /**
     * 映射的数据表
     */
    private String tablename;

    /**
     * 表主键
     */
    private String primarykey;
	 private String formUuid;

    public String getFormUuid() {
        return formUuid;
    }

    public void setFormUuid(String formUuid) {
        this.formUuid = formUuid;
    }

    public String getAppPagePath() {
        return appPagePath;
    }

    public void setAppPagePath(String appPagePath) {
        this.appPagePath = appPagePath;
    }

    public String getNodeFormSavePath() {
        return nodeFormSavePath;
    }

    public void setNodeFormSavePath(String nodeFormSavePath) {
        this.nodeFormSavePath = nodeFormSavePath;
    }

    public String getTaskListenerSqlData() {
        return taskListenerSqlData;
    }

    public void setTaskListenerSqlData(String taskListenerSqlData) {
        this.taskListenerSqlData = taskListenerSqlData;
    }

    public String getNodeFormUpdatePath() {
        return nodeFormUpdatePath;
    }

    public void setNodeFormUpdatePath(String nodeFormUpdatePath) {
        this.nodeFormUpdatePath = nodeFormUpdatePath;
    }

    public String getWhetherUpdate() {
        return whetherUpdate;
    }

    public void setWhetherUpdate(String whetherUpdate) {
        this.whetherUpdate = whetherUpdate;
    }

    public String getNodeFormEditPath() {
        return nodeFormEditPath;
    }

    public void setNodeFormEditPath(String nodeFormEditPath) {
        this.nodeFormEditPath = nodeFormEditPath;
    }

    public String getNodeFormPath() {
        return nodeFormPath;
    }

    public void setNodeFormPath(String nodeFormPath) {
        this.nodeFormPath = nodeFormPath;
    }

    public String getProcdefId() {
        return procdefId;
    }

    public void setProcdefId(String procdefId) {
        this.procdefId = procdefId;
    }

    /**
     * 设置：模型id
     */
    public void setActDeModelId(String actDeModelId) {
        this.actDeModelId = actDeModelId;
    }

    /**
     * 获取：模型id
     */
    public String getActDeModelId() {
        return actDeModelId;
    }

    /**
     * 设置：模型key
     */
    public void setActDeModelKey(String actDeModelKey) {
        this.actDeModelKey = actDeModelKey;
    }

    /**
     * 获取：模型key
     */
    public String getActDeModelKey() {
        return actDeModelKey;
    }

    /**
     * 设置：按钮名称
     */
    public void setNodeButtonName(String nodeButtonName) {
        this.nodeButtonName = nodeButtonName;
    }

    /**
     * 获取：按钮名称
     */
    public String getNodeButtonName() {
        return nodeButtonName;
    }

    /**
     * 设置：按钮code
     */
    public void setNodeButtonCode(String nodeButtonCode) {
        this.nodeButtonCode = nodeButtonCode;
    }

    /**
     * 获取：按钮code
     */
    public String getNodeButtonCode() {
        return nodeButtonCode;
    }

    /**
     * 设置：节点名称
     */
    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    /**
     * 获取：节点名称
     */
    public String getNodeName() {
        return nodeName;
    }

    /**
     * 设置： 节点Id
     */
    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    /**
     * 获取： 节点Id
     */
    public String getNodeId() {
        return nodeId;
    }

    /**
     * 设置：流程表单uuid
     */
    public void setFlowModelUuid(String flowModelUuid) {
        this.flowModelUuid = flowModelUuid;
    }

    /**
     * 获取：流程表单uuid
     */
    public String getFlowModelUuid() {
        return flowModelUuid;
    }

    public String getTablename() {
        return tablename;
    }

    public void setTablename(String tablename) {
        this.tablename = tablename;
    }

    public String getPrimarykey() {
        return primarykey;
    }

    public void setPrimarykey(String primarykey) {
        this.primarykey = primarykey;
    }
}
