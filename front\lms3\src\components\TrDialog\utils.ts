import type { FullscreenStyle } from "./types";

// 常量定义
export const CONSTANTS = {
  // 默认值
  DEFAULT_WIDTH: 600,
  DEFAULT_CLOSABLE: true,
  DEFAULT_MASK_CLOSABLE: true,
  DEFAULT_KEYBOARD: true,
  DEFAULT_DRAGGABLE: false,
  DEFAULT_FULLSCREEN: false,
  DEFAULT_SHOW_FOOTER: true,
  DEFAULT_SHOW_FULLSCREEN_BUTTON: false,
  DEFAULT_DESTROY_ON_CLOSE: true,
  // 样式类名
  WRAP_CLASS_NAME: "trdialog-modal-wrap",

  // 按钮文本
  DEFAULT_OK_TEXT: "确定",
  DEFAULT_CANCEL_TEXT: "取消"
} as const;

// 获取全屏样式
export function getFullscreenStyle(): FullscreenStyle {
  return {
    position: "fixed",
    top: 0,
    left: 0,
    width: "100vw",
    height: "100vh",
    margin: 0,
    padding: 0,
    maxWidth: "100vw",
    maxHeight: "100vh",
    zIndex: 9999
  };
}

// 合并样式对象
export function mergeStyles(
  ...styles: Record<string, any>[]
): Record<string, any> {
  return styles.reduce((acc, style) => ({ ...acc, ...style }), {});
}

// 过滤空值并合并类名
export function joinClassNames(
  ...classNames: (string | undefined | null)[]
): string {
  return classNames.filter(Boolean).join(" ");
}

// 获取拖拽边界
export function getDragBoundaries() {
  return {
    maxX: window.innerWidth - 200, // 预留200px防止移出屏幕
    maxY: window.innerHeight - 100, // 预留100px防止移出屏幕
    minX: -window.innerWidth + 200,
    minY: -window.innerHeight + 100
  };
}

// 限制拖拽位置在边界内
export function clampDragPosition(x: number, y: number) {
  const boundaries = getDragBoundaries();
  return {
    x: Math.max(boundaries.minX, Math.min(boundaries.maxX, x)),
    y: Math.max(boundaries.minY, Math.min(boundaries.maxY, y))
  };
}

// 检查是否在拖拽边界内
export function isWithinDragBoundaries(x: number, y: number): boolean {
  const boundaries = getDragBoundaries();
  return (
    x >= boundaries.minX &&
    x <= boundaries.maxX &&
    y >= boundaries.minY &&
    y <= boundaries.maxY
  );
}

// 计算精确的拖拽位置
export function calculateDragPosition(
  mouseX: number,
  mouseY: number,
  startX: number,
  startY: number,
  startOffsetX: number,
  startOffsetY: number
) {
  // 计算鼠标移动的距离
  const deltaX = mouseX - startX;
  const deltaY = mouseY - startY;

  // 计算新的拖拽位置
  const newX = startOffsetX + deltaX;
  const newY = startOffsetY + deltaY;

  // 应用边界限制
  return clampDragPosition(newX, newY);
}

// 获取元素的当前位置
export function getElementPosition(element: HTMLElement) {
  const rect = element.getBoundingClientRect();
  return {
    x: rect.left,
    y: rect.top,
    width: rect.width,
    height: rect.height
  };
}

// 计算拖拽边界（考虑元素尺寸）
export function getDragBoundariesWithElement(
  elementWidth: number,
  elementHeight: number
) {
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  return {
    maxX: viewportWidth - elementWidth - 20, // 预留20px边距
    maxY: viewportHeight - elementHeight - 20,
    minX: -elementWidth + 20,
    minY: -elementHeight + 20
  };
}
