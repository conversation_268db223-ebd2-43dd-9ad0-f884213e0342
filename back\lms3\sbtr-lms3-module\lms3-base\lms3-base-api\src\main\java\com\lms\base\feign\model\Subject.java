package com.lms.base.feign.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import com.lms.common.model.SearchContent;
import com.lms.common.model.SearchName;
import com.lms.common.model.SearchSelectItemContent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)

@TableName("u_subject")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "试题实体类")
public class Subject extends BaseModel {

    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private String id;

    @TableField("TITLE")
    @ApiModelProperty("题目标题")
    @SearchContent

    @Excel(name = "标题")
    private String title;

    @TableField(exist = false)
    @Excel(name = "选项内容A")
    private String itemA;

    @TableField(exist = false)
    @Excel(name = "选项内容B")
    private String itemB;

    @TableField(exist = false)
    @Excel(name = "选项内容C")
    private String itemC;

    @TableField(exist = false)
    @Excel(name = "选项内容D")
    private String itemD;

    @TableField("CONTENT")
    @ApiModelProperty("选项内容")
    @SearchContent
    private String content;

    @TableField("ONLYEXAM")
    @ApiModelProperty("使用范围")
    @Excel(name = "使用范围")
    private Integer onlyexam;

    @Excel(name = "使用范围")
    @TableField(exist = false)
    private String onlyexamstring;

    @TableField("TYPE")
    @ApiModelProperty("题目类型")
    @Excel(name = "题目类型")
    private String type;

    @TableField("PRINCIPAL")
    @ApiModelProperty("责任人")
    private String principal;

    @TableField("DUTYUNIT")
    @ApiModelProperty("责任单位")
    private String dutyunit;

    @TableField("LEVELID")
    @ApiModelProperty("难易度")
    @SearchSelectItemContent
    @Excel(name = "难易度")
    private String levelid;

    @TableField("SPECIALID")
    @ApiModelProperty("专业")
    @Excel(name = "专业")
    private String specialid;

    @TableField("PERSONLEVELID")
    @ApiModelProperty("人员层级")
    @Excel(name = "适用人员业务水平")
    private String personlevelid;

    @TableField("EQUIPMENTID")
    @ApiModelProperty("型号id")
    @Excel(name = "适用机型")
    private String equipmentid;

    @TableField(exist = false)
    @ApiModelProperty("型号名称")
    private String equipmentname;

    @TableField(exist = false)
    @ApiModelProperty("主键id")
    private String lastequipmentid;

    @TableField("CORRECTRESPONSE")
    @ApiModelProperty("参考答案")
    @Excel(name = "标准答案")
    private String correctresponse;

    @TableField("COMPONENTID")
    @ApiModelProperty("课程id")
    private String componentid;

    @TableField("CONTENTTEXT")
    @ApiModelProperty("选项内容文本")
    private String contenttext;

    @TableField("REMARK")
    @ApiModelProperty("备注")
    private String remark;

    @TableField("CREATORID")
    @ApiModelProperty("创建人")
    private String creatorid;

    @TableField("CREATEDEPTID")
    @ApiModelProperty("创建人部门")
    private String createdeptid;

    @TableField("CREATEDATE")
    @ApiModelProperty("创建时间")
    private String createdate;

    @TableField("MODIFYDATE")
    @ApiModelProperty("修改时间")
    private String modifydate;

    @TableField("ATTACH")
    @ApiModelProperty("附件id")
    private String attach;

    @TableField("SOFTCODE")
    @ApiModelProperty("软件编码")
    private String softcode;

    @TableField(exist = false)
    @ApiModelProperty("是否已添加到试题库的标志位")
    private Boolean selectedFlag; //是否已添加到试题库的标志位；

    @TableField(exist = false)
    @ApiModelProperty("图片地址")
    private String imgUrl;

    @TableField("SCORERATE")
    @ApiModelProperty("得分率")
    private String scorerate;


    @TableField("NUMBER")
    @ApiModelProperty("编号")
    @SearchName
    private String number; // 编号

    @TableField("REALLEVEL")
    @ApiModelProperty("真实难易度")
    private String reallevel; // 真实难易度

    @TableField("KEKAODU")
    @ApiModelProperty("可靠度")
    private String kekaodu; // 可靠度

    @TableField(exist = false)
    @Excel(name = "密级")
    private String mlevelForImport;

}
