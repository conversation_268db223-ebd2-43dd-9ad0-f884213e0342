package com.lms.base.component.controller;

import com.alibaba.fastjson.JSONObject;
import com.lms.base.component.service.ComponentService;
import com.lms.base.feign.model.Component;
import com.lms.common.controller.BaseController;
import com.lms.common.dao.query.PageInfo;
import com.lms.common.enums.RoleEum;
import com.lms.common.feign.dto.TreeNode;
import com.lms.common.model.LMSLog;
import com.lms.common.model.LogType;
import com.lms.common.model.Result;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.StringHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/component")
@Api(value = "构型树管理", tags = "构型树管理")
public class ComponentController extends BaseController<Component> {

    private static final Logger LOGGER = LogManager.getLogger(ComponentController.class);

    @Resource
    private ComponentService componentService;

    // 通过异步的方式获取结构树
    @ApiOperation(value = "查询当前型号下的所有子型号", httpMethod = "GET")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "pid", value = "当前型号id", required = true, dataTypeClass = String.class)}
    )
    @GetMapping(value = {"/", "/getchildnodesbyid"})
    public Result<List<Component>> getChildNodesById(String pid) {
        List<Component> componentList = componentService.getAllComponentsByPid(pid);
        Component component = componentService.getById(pid);
        commonSystemApi.saveLog(Optional.ofNullable(component).orElse(new Component()).getName(), "查询当前型号下的所有型号", LogType.List, "操作成功");
        return Result.OK(componentList);
    }

    @ApiOperation(value = "获取课程型号树", httpMethod = "POST")
    @PostMapping(value = {"/getCourseStructureTree"})
    public Result getCourseStructureTree(@RequestBody JSONObject jsonObject) {
        PageInfo pageInfo = super.getPageInfo(jsonObject);
        List<TreeNode> resArray = componentService.getCourseStructureTree(pageInfo);
        return Result.OK(resArray);
    }

    // 一次列出构型树所有节点
    @ApiOperation(value = "查询构型树", httpMethod = "GET")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "pid", value = "当前型号id", required = true, dataTypeClass = String.class)}
    )
    @GetMapping("/getComponentTree")
    public Result getComponentTree(@RequestParam(value = "pid", required = false) String pid) {
        // 当前人员的管理型号
        boolean isAdmin = RoleEum.admin.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
        boolean isLeader = RoleEum.leader.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
        boolean isTeacher = RoleEum.teacher.getValue().equals(ContextUtil.getCurrentUser().getRoleid());
        String equipmentId = isTeacher ? ContextUtil.getCurrentUser().getTeachingmodel() : ContextUtil.getCurrentUser().getManagemodel();
        List<TreeNode> componentTree = new ArrayList<>();
        if ((isAdmin | isLeader | isTeacher) && StringUtils.isEmpty(pid) && StringUtils.isNotEmpty(equipmentId)) {
            String[] componentArray = equipmentId.split(",");
            if (componentArray.length > 0) {
                for (String componentId : componentArray) {
                    if (StringUtils.isNotEmpty(componentId)) {
                        String[] split = componentId.split("/");
                        Component component = componentService.getById(split[split.length - 1]);
                        String fullpath = component.getFullpath();
                        String[] fullpathArray = fullpath.split("/");
                        componentTree.addAll(componentService.getComponentTree2(fullpathArray[fullpathArray.length - 1], false));
                    }
                }
            }
        } else {
            componentTree = componentService.getComponentTree2(pid, false);
        }
        commonSystemApi.saveLog("构型树", "查询构型树", LogType.List, "操作成功");
        return Result.OK(componentTree);
    }

    @ApiOperation(value = "查询型号", httpMethod = "GET")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "id", value = "型号id", required = true, dataTypeClass = String.class)}
    )
    @GetMapping(value = {"/", "/getById"})
    @LMSLog(desc = "查询型号", otype = LogType.Get)
    public Result<Component> getById(@RequestParam("id") String id) { // 增加分页?
        // 暂时不分
        Component component = componentService.getById(id);
        if (component != null && !StringHelper.isEmpty(component.getParentid())) {
            Component pcomponent = componentService.getById(component.getParentid());
            if (pcomponent != null) {
                component.setParentname(pcomponent.getName());
            }
        }
        return Result.OK(component);
    }

    // 更新Action
    @ApiOperation(value = "新增、修改课程构型树", httpMethod = "POST")
    @LMSLog(desc = "新增、修改课程构型树", otype = LogType.Update, order = 1, method = "setComponentLog")
    @PostMapping("/editComponentItem")
    public Result<Component> editComponentItem(@RequestBody Component component) {
        String id = component.getId();
        String errorMsg;
        if (componentService.existName(component.getName(), id,
                component.getParentid())) {
            errorMsg = commonSystemApi.translateContent("父目录下名称[?]已存在！", "", component.getName());
            return Result.error(errorMsg, null);
        }
        if (componentService.hasChildren(id) && component.getCourseFlag().equals("1")) {
            errorMsg = commonSystemApi.translateContent("该目录存在子目录,课程节点标识不能设置为'是'");
            return Result.error(errorMsg, null);
        }

        if (StringHelper.isEmpty(id)) {
            String parentid = component.getParentid();
            if (!StringHelper.isEmpty(parentid)) {
                Component pcomponent = componentService.getById(component.getParentid());
                String parentFullCode = pcomponent.getFullcode();
                if (StringHelper.isEmpty(parentFullCode)) {
                    component.setFullcode(component.getCode());
                } else {
                    component.setFullcode(parentFullCode, component.getCode());
                }
            } else {
                component.setFullcode(component.getCode());
            }
            component.setStatus(1);
            componentService.saveOrUpdate(component);
            generateFullPath(component);
            componentService.saveOrUpdate(component);
            return Result.OK(component);
        } else { // 暂时不分
            Component sourcecomponent = componentService.getById(id);
            sourcecomponent.setName(component.getName());
            sourcecomponent.setCode(component.getCode());
            sourcecomponent.setParentid(component.getParentid());
            sourcecomponent.setSeqno(component.getSeqno());
            sourcecomponent.setCourseFlag(component.getCourseFlag());
            sourcecomponent.setStatus(component.getStatus());
            generateFullPath(sourcecomponent);
            this.componentService.saveOrUpdate(sourcecomponent);
            return Result.OK(sourcecomponent);
        }
    }

    /**
     * 生成构型树(型号) id全路径
     *
     * @param component 实体类
     */
    private void generateFullPath(Component component) {
        if (StringUtils.isNotEmpty(component.getParentid())) {
            Component pcomponent = componentService.getById(component.getParentid());
            generateFullPath(pcomponent);
            component.setFullpath(pcomponent.getFullpath() + "/" + component.getId());
            component.setFullpathname(pcomponent.getFullpathname() + "/" + component.getName());
        } else {
            component.setFullpath(component.getId());
            component.setFullpathname(component.getName());
        }
    }

    // 更新Action
    @ApiOperation(value = "删除课程构型树", httpMethod = "DELETE")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "id", value = "型号id", required = true, dataTypeClass = String.class)}
    )
    @LMSLog(desc = "删除课程构型树", otype = LogType.Delete, order = 1)
    @RequestMapping(value = {"/delete"}, method = RequestMethod.DELETE)
    public Result<Void> deleteById(@RequestParam("id") String id) {
        if (this.componentService.exsitUseCourseware(id)) {
            return Result.error(commonSystemApi.translateContent("该目录下存在课件，不能删除！"), null);
        }
        if (this.componentService.exsitUseSubject(id)) {
            return Result.error(commonSystemApi.translateContent("该目录下存在题目，不能删除！"), null);
        }
        if (this.componentService.exsitSubNodes(id)) {
            return Result.error(commonSystemApi.translateContent("该目录下存在子目录，不能删除！"), null);
        }
        Component sourcecomponent = componentService.getById(id);
        sourcecomponent.setStatus(0);
        this.componentService.saveOrUpdate(sourcecomponent);
        // 有子节点不能删除
        return Result.OK(null);
    }


    @ApiOperation(value = "根据id列表获取构型树列表", httpMethod = "POST")
    @PostMapping("/getComponentByIdList")
    public Result<List<Component>> getComponentByIdList(@RequestBody List<String> idList) {
        List<Component> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(idList)) {
            list = componentService.listIds(idList);
        }
        return Result.OK(list);
    }

    @PostMapping("/getAllChildComponentsByPidFullPath")
    public Result<List<Component>> getAllChildComponentsByPidFullPath(@RequestParam String pidFullPath) {
        List<Component> componentList = componentService.getAllChildComponentsByPidFullPath(pidFullPath);
        return Result.OK(componentList);
    }

    public String setComponentLog(Object[] args, LMSLog lmslog) {
        String objname = "";
        if (lmslog.otype().equals(LogType.Save)) { //新增数据日志
            Component p = (Component) (args[0]);
            objname = StringHelper.null2String(p.getName());
        } else if (lmslog.otype().equals(LogType.Update)) { //修改数据日志
            Component p = (Component) (args[0]);
            objname = StringHelper.null2String(p.getName());
        } else if (lmslog.otype().equals(LogType.Delete)) { //删除数据日志
            String id = (String) (args[0]);
            Component p = componentService.getById(id);
            objname = StringHelper.null2String(p.getName());
        }
        return objname;
    }

}
