package com.sbtr.workflow.mapper;
import com.sbtr.workflow.model.ActMyNodeField;
import com.sbtr.workflow.vo.FlowNodeFieldVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程每个节点所对字段是否可编辑以及是否可见
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-27 13:43:13
 */
public interface ActMyNodeFieldMapper extends tk.mybatis.mapper.common.Mapper<ActMyNodeField> {

    //获取分页集
    List<ActMyNodeField> getPageSet();

   int executeDeleteBatch(Object[] var1);

    Integer insertListOracle(@Param("list")  List<ActMyNodeField> list);

    Integer insertList(@Param("list")  List<ActMyNodeField> list);

    List<FlowNodeFieldVo> selectByModelKeyAndId(@Param("modelKey")String modelKey, @Param("nodeId") String nodeId, @Param("processDefinitionId") String processDefinitionId);

    List<ActMyNodeField> getListByActDeModelKeyAndProcdefId(@Param("key")String key, @Param("procdefIds") String procdefIds);

    Integer updateProcdefIdByModelKey(@Param("key") String key,@Param("procdefId") String procdefId);

    Integer updateProcdefIdIsNull(@Param("key") String key,@Param("id") String id);

    Integer deleteByModelKeyAnProcdefId(@Param("key")String key,@Param("id") String id);

    Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id);
}
