<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.workflow.mapper.SysUserMapper">

    <sql id="Base_Column_List">
        uuid
        ,										 create_time
		  ,											creator
				 ,										 creator_id
		  ,											modifier
				 ,										 modifier_id
		  ,										 modify_time
		  ,										 creator_org_id
		  ,										 user_name_id
		  ,										 user_password
		  ,										 user_name
		  ,										 nick_name
		  ,										 user_sex
		  ,										 user_age
		  ,										 user_email
		  ,										 user_org_id
		  ,										 user_position
		  ,										 user_group
		  ,										 user_reports_to
		  ,										 data_auth
		  ,										 user_entry_time
		  ,										 user_sort
		  ,										 user_status
		  ,										 user_remark
		  ,										 user_head_portrait
		  ,										 user_birthday
		  ,										 user_personal_signature
		  ,										 user_family_name
		  ,										 user_native_place
		  ,										 user_telephone
		  ,										 user_cellphone
		  ,										 political_outlook
		  ,										 user_education
		  ,										 id_card_no
		  ,										 id_card
		  ,										 deposit_bank
		  ,										 bank_accounts
		  ,											openid
				 ,											unionid
				 ,										 tenant_uuid
		  ,										 user_common_menu
		  ,										 user_language
		  ,										 user_thema
		  ,										 user_portal
		  ,										 user_administrator
		  ,										 last_log_time
		  ,										 last_log_ip
    </sql>

    <update id="updateByUuidAndIsDel">
        UPDATE sys_user
        SET is_del = 1
        WHERE uuid = #{arg0}
    </update>

    <select id="getPageSet" resultType="com.sbtr.workflow.model.SysUser">
        select
        <include refid="Base_Column_List"></include>
        from sys_user
        <where>
            <if test="ids != null and ids != ''">
                <foreach collection="ids.split(',')" index="index" item="item" open="(" separator="or" close=")">
                    FIND_IN_SET(#{item},user_org_id)
                </foreach>
            </if>
            <if test="filterSort != null">
                and ${filterSort}
            </if>
        </where>
    </select>

    <select id="getPageSetOracle" resultType="com.sbtr.workflow.model.SysUser">
        select
        <include refid="Base_Column_List"></include>
        from sys_user
        <where>
            <if test="ids != null and ids != ''">
                <foreach collection="ids.split(',')" index="index" item="item" open="(" separator="or" close=")">
                    (#{item} = org_id)

                </foreach>
            </if>
            and is_del != 1
            <if test="filterSort != null">
                and ${filterSort}
            </if>
        </where>
    </select>

    <select id="getPageSetSqlServer" resultType="com.sbtr.workflow.model.SysUser">
        select
        <include refid="Base_Column_List"></include>
        from sys_user
        <where>
            <if test="ids != null and ids != ''">
                <foreach collection="ids.split(',')" index="index" item="item" open="(" separator="or" close=")">
                    (#{item} = org_id)

                </foreach>
            </if>
            and is_del != 1
            <if test="filterSort != null">
                and ${filterSort}
            </if>
        </where>
    </select>

    <select id="getDataAuth" resultType="java.lang.String">
        select data_auth
        from sys_user
        where user_name_id = #{arg0}
    </select>

    <update id="updateCellphone">
        UPDATE sys_user
        SET cellphone = #{cellphone}
        WHERE openid = #{openid}
    </update>

    <select id="getLimitedListByQ" resultType="com.sbtr.workflow.model.SysUser">
        SELECT *
        FROM sys_user
        WHERE (user_name LIKE concat(concat('%', #{arg0}), '%') OR user_name_id LIKE
                                                                   concat(concat('%', #{arg0}), '%'))
          AND is_del <![CDATA[<>]]> 1

    </select>

    <select id="getUserByOpenid" resultType="com.sbtr.workflow.model.SysUser">
        SELECT *
        FROM sys_user
        WHERE is_del != 1 and openid=#{openid}
    </select>
    <select id="getListByUserNameIdAndUuid" resultType="com.sbtr.workflow.model.SysUser">
        SELECT *
        FROM sys_user
        WHERE user_name_id = #{userNameId}
          and uuid != #{uuid}
    </select>
    <select id="getDataByTablesAndField" resultType="com.sbtr.workflow.model.SysUser">
        SELECT ${field}
        from ${tables}
    </select>

    <select id="getUserList" resultType="java.util.Map">
        SELECT title = '成员', user_name as name, cellphone as phone, email, sex, org_id
        from sys_user
        where is_del !=1
    </select>
    <select id="getUserByUserNameIds" resultType="com.sbtr.workflow.model.SysUser">
        SELECT
        org_id,
        org_name,
        user_name,
        user_name_id,
        post_text,
        get_dic_item_text(major) AS major
        FROM
        sys_user
        where user_name_id in
        <foreach collection="userNameIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_del != 1
    </select>
    <select id="selectAllListByUuids" resultType="com.sbtr.workflow.model.SysUser">
        SELECT
        uuid,
        user_name,
        user_name_id
        FROM
        sys_user
        where uuid in
        <foreach collection="uuids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_del != 1
    </select>


    <select id="selectByUserNameIdIsExist" resultType="com.sbtr.workflow.model.SysUser">
        SELECT
        user_name,
        user_name_id
        FROM
        sys_user
        where user_name_id in
        <foreach collection="userNameId" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_del != 1
    </select>

    <select id="getUserNameIdByRoleId" resultType="java.lang.String">

        SELECT GROUP_CONCAT(user_name_id)
        FROM sys_user
        WHERE is_del != 1 and
        FIND_IN_SET(  ( SELECT id FROM sys_auth_group WHERE uuid =#{roleId} )
            , user_group)

    </select>
    <select id="getUserNameIdByPost" resultType="java.lang.String">

        SELECT GROUP_CONCAT(user_name_id)
        FROM sys_user
        WHERE is_del != 1
        AND
        FIND_IN_SET(
        user_name_id,(

        select GROUP_CONCAT(CAST(b.user_name_id AS CHAR)) AS courseIds from (
        SELECT user_name_id FROM sys_user_post WHERE post = ( select `VALUE` from sys_dic_item  WHERE uuid = #{post} )

            ) as b

            ))

    </select>
    <select id="selectUserNameByuuid" resultType="java.lang.String">
        SELECT group_concat(user_name)
        FROM sys_user
        where FIND_IN_SET(uuid, #{uuid})
    </select>

    <select id="getPageSetUserByRoleId" resultType="com.sbtr.workflow.model.SysUser">
        select
        <include refid="Base_Column_List"></include>
        from sys_user
        <where>

            FIND_IN_SET(#{roleId},user_group)
            <if test=" userName != null and  userName!=''">
                and (user_name = #{userName} or user_name_id=#{userName})
            </if>
            <if test="filterSort != null">
                and ${filterSort}
            </if>
        </where>
    </select>


    <select id="getPageSetUserByRoleIdOracle" resultType="com.sbtr.workflow.model.SysUser">
        select
        <include refid="Base_Column_List"></include>
        from sys_user
        <where>
            instr(','||user_group||',' , ','||#{roleId}||',') <![CDATA[ <> ]]> 0
            and is_del != 1
            <if test=" userName != null and  userName!=''">
                and (user_name = #{userName} or user_name_id=#{userName})
            </if>
            <if test="filterSort != null">
                and ${filterSort}
            </if>
        </where>
    </select>


    <select id="getPageSetUserByRoleIdSqlServer" resultType="com.sbtr.workflow.model.SysUser">
        select
        <include refid="Base_Column_List"></include>
        from sys_user
        <where>
            charindex( ',' + CONVERT ( VARCHAR, #{roleId} ), ',' + user_group ) &gt; 0
            and is_del != 1
            <if test=" userName != null and  userName!=''">
                and (user_name = #{userName} or user_name_id=#{userName})
            </if>
            <if test="filterSort != null">
                and ${filterSort}
            </if>
        </where>
    </select>

    <insert id="sysUserInsertData">
        insert into sys_user
        (uuid,
        create_time,
        creator,
        creator_id,
        creator_org_id,
        is_del,
        user_name_id,
        user_name,
        password,
        cellphone,
        department,
        post,
        sex,
        email,
        avatar,
        telephone,
        status,
        address,
        org_id,
        org_name,
        user_group,
        company_id)
        values
        <foreach collection="userList" item="userList" index="index" separator=",">
            (
            #{userList.uuid},
            #{userList.createTime},
            #{userList.creator},
            #{userList.creatorId},
            #{userList.creatorOrgId},
            #{userList.isDel},
            #{userList.userNameId},
            #{userList.userName},
            #{userList.password},
            #{userList.cellphone},
            #{userList.department},
            #{userList.post},
            #{userList.sex},
            #{userList.email},
            #{userList.avatar},
            #{userList.telephone},
            #{userList.status},
            #{userList.address},
            #{userList.orgId},
            #{userList.orgName},
            #{userList.userGroup},
            #{userList.companyId}
            )
        </foreach>
    </insert>
    <update id="updateUserByQyweixinUser">

        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update sys_user
            <set>
                <if test="item.userName != null">
                    user_name = #{item.userName},
                </if>
                <if test="item.cellphone != null">
                    cellphone = #{item.cellphone},
                </if>
                <if test="item.post != null">
                    post = #{item.post},
                </if>
                <if test="item.sex != null">
                    sex = #{item.sex},
                </if>
                <if test="item.email != null">
                    email = #{item.email},
                </if>
                <if test="item.avatar != null">
                    avatar = #{item.avatar},
                </if>
                <if test="item.telephone != null">
                    telephone = #{item.telephone},
                </if>
                <if test="item.status != null">
                    status = #{item.status},
                </if>
                <if test="item.orgId != null">
                    org_id = #{item.orgId},
                </if>
                <if test="item.orgName != null">
                    org_name = #{item.orgName},
                </if>
                <if test="item.userGroup != null">
                    user_group = #{item.userGroup}
                </if>
            </set>
            where uuid = #{item.uuid}
        </foreach>

    </update>

    <select id="getEngineerName" resultType="java.lang.String">
        select user_name from sys_user where user_position = (
        select uuid from sys_position where position_text = '工程师'
        )
    </select>

    <select id="getUserPosition" resultType="java.lang.String">
        select user_position from sys_user where user_name_id = #{userNameId}
    </select>

</mapper>
