package com.sbtr.workflow.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddTaskNewVo {

    /**
     * 应用系统编号
     */
    private String appID;
    /**
     * 待办事项名称
     */
    private String taskName;

    /**
     * 应用系统中的待办事项唯一的ID
     */
    private String appTaskID;

    /**
     * 所属待办类型
     */
    private String taskType;
    /**
     * 应用系统发送者ID
     */
    private String appSendUID;
    /**
     * 接收者对应的应用系统用户ID，需要处理该项待办事项的用户，多个用户用;分隔
     */
    private String appReceiveUID;
    /**
     * 待办事项信息启用时间yyyy-mm-dd hh:MM:ss
     */
    private String sendTime;
    /**
     * 待办事项信息结束时间yyyy-mm-dd hh:MM:ss
     */
    private String endTime;
    /**
     * 待办事项处理链接
     */
    private String url;
    /**
     * 待办事项描述，如果没有可与appTaskID保持一致。
     */
    private String taskDesc;
    /**
     * 待办事项信息紧急程度，越小越紧急,0:特急 1:紧急 2:一般,缺省2
     */
    private String priorityID;
    /**
     * 来源单位
     */
    private String Remark ;

}
