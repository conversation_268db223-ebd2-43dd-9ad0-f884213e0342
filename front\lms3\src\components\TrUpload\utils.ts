import { message } from "ant-design-vue";
import { uploadOssFileDetail, uploadFileChunk } from "@/api/ossController";
import { FileType, UploadMode } from "./types";

/**
 * 文件类型判断工具类
 */
export class FileTypeUtils {
  /**
   * 判断是否为图片文件
   */
  static isImage(file: File): boolean {
    return (
      file.type.startsWith("image/") ||
      /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i.test(file.name)
    );
  }

  /**
   * 判断是否为视频文件
   */
  static isVideo(file: File): boolean {
    return (
      file.type.startsWith("video/") ||
      /\.(mp4|avi|mov|wmv|flv|webm|mkv)$/i.test(file.name)
    );
  }

  /**
   * 判断是否为3D模型文件
   */
  static is3DModel(file: File): boolean {
    return (
      file.type.includes("model") ||
      /\.(obj|fbx|gltf|glb|stl|dae|3ds|max|blend|vrp|vrpc)$/i.test(file.name)
    );
  }

  /**
   * 判断是否为文档文件
   */
  static isDocument(file: File): boolean {
    return /\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|md)$/i.test(file.name);
  }

  /**
   * 获取文件类型
   */
  static getFileType(file: File): FileType {
    if (this.isImage(file)) return FileType.IMAGE;
    if (this.isVideo(file)) return FileType.VIDEO;
    if (this.is3DModel(file)) return FileType.MODEL;
    if (this.isDocument(file)) return FileType.DOCUMENT;
    return FileType.OTHER;
  }

  /**
   * 判断是否应该使用分片上传
   */
  static shouldUseChunkUpload(file: File): boolean {
    // 大文件或特定类型使用分片上传
    const largeFile = file.size > 10 * 1024 * 1024; // 10MB
    const isVrpFile = /\.(vrp|vrpc)$/i.test(file.name);
    return largeFile || isVrpFile;
  }
}

/**
 * 文件验证工具类
 */
export class FileValidationUtils {
  /**
   * 验证文件类型
   */
  static validateFileType(file: File, allowedTypes?: string[]): boolean {
    if (!allowedTypes || allowedTypes.length === 0) return true;

    const fileType = FileTypeUtils.getFileType(file);
    return allowedTypes.some(type => {
      if (type.includes("*")) {
        const baseType = type.split("/")[0];
        return file.type.startsWith(baseType);
      }
      return (
        file.type === type ||
        file.name.toLowerCase().endsWith(type.toLowerCase())
      );
    });
  }

  /**
   * 验证文件大小
   */
  static validateFileSize(file: File, maxSize?: number): boolean {
    if (!maxSize) return true;
    const fileSizeMB = file.size / 1024 / 1024;
    return fileSizeMB <= maxSize;
  }

  /**
   * 综合文件验证
   */
  static validateFile(
    file: File,
    config: {
      maxSize?: number;
      accept?: string[];
      fileTypes?: string[];
    }
  ): { valid: boolean; message?: string } {
    // 验证文件类型
    if (config.accept && !this.validateFileType(file, config.accept)) {
      return {
        valid: false,
        message: "不支持的文件类型"
      };
    }

    // 验证文件大小
    if (config.maxSize && !this.validateFileSize(file, config.maxSize)) {
      return {
        valid: false,
        message: `文件大小不能超过 ${config.maxSize}MB`
      };
    }

    return { valid: true };
  }
}

/**
 * 文件格式化工具类
 */
export class FileFormatUtils {
  /**
   * 格式化文件大小
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 B";

    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * 获取文件图标
   */
  static getFileIcon(file: File): string {
    const fileType = FileTypeUtils.getFileType(file);
    switch (fileType) {
      case FileType.IMAGE:
        return "FileImageOutlined";
      case FileType.VIDEO:
        return "VideoCameraOutlined";
      case FileType.MODEL:
        return "AppstoreOutlined";
      case FileType.DOCUMENT:
        return "FileTextOutlined";
      default:
        return "FileOutlined";
    }
  }
}

/**
 * 上传工具类
 */
export class UploadUtils {
  /**
   * 分片上传文件
   */
  static async uploadFileInChunks(
    file: File,
    onProgress?: (percent: number) => void
  ): Promise<any> {
    const shardSize = 2 * 1024 * 1024; // 2MB per chunk
    const shardCount = Math.ceil(file.size / shardSize);
    let currentChunk = 0;
    let uploadResult: any = null;

    const uploadChunk = async (chunkIndex: number): Promise<any> => {
      if (chunkIndex >= shardCount) {
        return uploadResult;
      }

      const start = chunkIndex * shardSize;
      const end = Math.min(file.size, start + shardSize);
      const chunk = file.slice(start, end);

      const formData = new FormData();
      formData.append("data", chunk);
      formData.append("lastModified", file.lastModified.toString());
      formData.append("fileName", file.name);
      formData.append("total", shardCount.toString());
      formData.append("index", (chunkIndex + 1).toString());

      try {
        const result = await uploadFileChunk(formData); // 待调整
        const parsedResult =
          typeof result === "string" ? JSON.parse(result) : result;

        if (parsedResult && parsedResult.mergeOk) {
          // 文件合并完成
          uploadResult = parsedResult;
          onProgress?.(100);
          return uploadResult;
        } else if (parsedResult && parsedResult.number !== undefined) {
          // 继续上传下一个分片
          currentChunk = parsedResult.number;
          const progress = Math.round((currentChunk / shardCount) * 100);
          onProgress?.(progress);
          return await uploadChunk(currentChunk);
        } else {
          throw new Error("上传分片失败");
        }
      } catch (error) {
        console.error("分片上传失败:", error);
        throw error;
      }
    };

    return await uploadChunk(currentChunk);
  }

  /**
   * 普通文件上传
   */
  static async uploadFileNormal(file: File): Promise<any> {
    const formData = new FormData();
    formData.append("file", file);
    return await uploadOssFileDetail(formData);
  }

  /**
   * 智能上传文件
   */
  static async uploadFile(
    file: File,
    onProgress?: (percent: number) => void
  ): Promise<any> {
    try {
      // 根据文件类型选择上传方式
      if (FileTypeUtils.shouldUseChunkUpload(file)) {
        console.log("使用分片上传:", file.name);
        return await this.uploadFileInChunks(file, onProgress);
      } else {
        console.log("使用普通上传:", file.name);
        onProgress?.(100);
        return await this.uploadFileNormal(file);
      }
    } catch (error) {
      console.error("文件上传失败:", error);
      throw error;
    }
  }

  /**
   * 处理上传结果
   */
  static processUploadResult(
    result: any,
    file: File
  ): {
    fileUrl: string;
    fileType: string;
    previewUrl?: string;
  } {
    if (result && result.result && result.result.fileUrl) {
      return {
        fileUrl: result.result.fileUrl,
        fileType: result.result.fileType || "",
        previewUrl: result.result.fileUrl
      };
    } else {
      // 兜底：为图片和视频创建本地预览URL
      let previewUrl: string | undefined;
      if (FileTypeUtils.isImage(file) || FileTypeUtils.isVideo(file)) {
        previewUrl = URL.createObjectURL(file);
      }

      return {
        fileUrl: result?.fileUrl || "",
        fileType: file.type,
        previewUrl
      };
    }
  }
}

/**
 * 创建预览URL
 */
export function createPreviewUrl(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * 释放预览URL
 */
export function revokePreviewUrl(url: string): void {
  if (url && url.startsWith("blob:")) {
    URL.revokeObjectURL(url);
  }
}
