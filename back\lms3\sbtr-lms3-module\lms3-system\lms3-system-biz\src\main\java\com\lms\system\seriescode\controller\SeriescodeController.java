package com.lms.system.seriescode.controller;

import javax.annotation.Resource;
import com.lms.common.controller.BaseController;
import com.lms.system.feign.model.Seriescode;
import com.lms.system.seriescode.service.SeriescodeService;
import com.lms.system.seriescode.service.impl.SeriescodeServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/seriescode")
@Api(value = "通用流水编码",tags = {"通用流水编码"})
public class SeriescodeController extends BaseController<Seriescode> {
	@Resource
	private SeriescodeService seriescodeService;

    @ApiOperation(value = "获取最新流水编码")
    @GetMapping(value = {"/getNewCode"})
    public String getNewCode(@RequestParam String key) {
        String newcode = seriescodeService.getNewCode(key);
        return newcode;
    }
}
