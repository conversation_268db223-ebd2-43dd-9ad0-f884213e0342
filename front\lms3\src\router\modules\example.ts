export default {
  path: "/example",
  redirect: "/example/TrDialog",
  meta: {
    icon: "mdi:database",
    // showLink: false, // 发版前这里的注释放开即可
    title: "组件示例（发版前请删除路由）",
    rank: 100000
  },
  children: [
    {
      path: "/example/TrTable",
      name: "TrTable",
      component: () => import("@/components/TrTable/example/index.vue"),
      meta: {
        title: "表格组件",
        showParent: true
      }
    },
    {
      path: "/example/ReTrTable",
      name: "ReTrTable",
      component: () => import("@/components/ReTrTable/example.vue"),
      meta: {
        title: "表格组件（重制）",
        showParent: true
      }
    },
    {
      path: "/example/TrDialog",
      name: "TrDialog",
      component: () => import("@/components/TrDialog/example.vue"),
      meta: {
        title: "对话框组件",
        showParent: true
      }
    },
    {
      path: "/example/TrPreview",
      name: "TrPreview",
      component: () => import("@/components/TrPreview/example.vue"),
      meta: {
        title: "预览组件"
      }
    },
    {
      path: "/example/TrUpload",
      name: "TrUpload",
      component: () => import("@/components/TrUpload/example.vue"),
      meta: {
        title: "上传组件"
      }
    }
  ]
} satisfies RouteConfigsTable;
