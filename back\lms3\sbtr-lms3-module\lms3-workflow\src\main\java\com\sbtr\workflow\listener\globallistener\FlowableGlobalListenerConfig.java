package com.sbtr.workflow.listener.globallistener;

import org.flowable.common.engine.api.delegate.event.FlowableEngineEventType;
import org.flowable.common.engine.api.delegate.event.FlowableEventDispatcher;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;


/**
 * Flowable添加全局监听器
 *
 * <AUTHOR>
 * @Version 5.5.0
 * @Email <EMAIL>
 * @Date 2023-07-31 09:52:44
 */
@Configuration
public class FlowableGlobalListenerConfig implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    private SpringProcessEngineConfiguration configuration;

    @Autowired
    private GlobalTaskCreateListener globalTaskCreateListener;

    @Autowired
    private GlobalTaskCompletedListener taskCompletedListener;

    @Autowired
    private TaskExpirationEventListener taskExpirationEventListener;

    @Autowired
    private GlobalTaskAssignedListener taskAssignedListener;

    @Autowired
    private GlobalProcessStartedListener globalProcessStartedListener;

    @Autowired
    private GlobalProcistEndListener globalProcistEndListener;

    /**
     * 应用程序事件
     * TIMER_FIRED （已触发） 表示定时器任务已经触发。当定时器任务到达其指定的触发时间时，引擎会触发一个"TIMER_FIRED"事件。这个事件标志着定时器已经到达了它的触发时间点，并且相应的业务逻辑应该被执行。"TIMER_FIRED"事件通常用于触发与定时器任务关联的监听器，以便执行特定的操作或任务
     *
     * @param contextRefreshedEvent 背景更新事件
     */
    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        FlowableEventDispatcher dispatcher = configuration.getEventDispatcher();
//        //流程启动事件监听
//        dispatcher.addEventListener(globalProcessStartedListener, FlowableEngineEventType.PROCESS_STARTED);
//        //流程结束全局监听
//        dispatcher.addEventListener(globalProcistEndListener, FlowableEngineEventType.PROCESS_COMPLETED);
//        //任务创建全局监听
        dispatcher.addEventListener(globalTaskCreateListener, FlowableEngineEventType.TASK_CREATED);
//        //任务分配事件全局监听
        dispatcher.addEventListener(taskAssignedListener, FlowableEngineEventType.TASK_ASSIGNED);
//        //任务任务完成事件监听
//        dispatcher.addEventListener(taskCompletedListener, FlowableEngineEventType.TASK_COMPLETED);
        //定时器任务已经触发
        dispatcher.addEventListener(taskExpirationEventListener, FlowableEngineEventType.TIMER_FIRED);
    }
}
