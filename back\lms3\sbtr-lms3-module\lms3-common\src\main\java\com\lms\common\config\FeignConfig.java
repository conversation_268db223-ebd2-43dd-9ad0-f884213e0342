package com.lms.common.config;

import cn.hutool.core.util.ObjectUtil;
import com.lms.common.util.ConstParamUtil;
import feign.Logger;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * feign config
 *
 * <AUTHOR>
 * @Version 5.4.21
 * @Email <EMAIL>
 * @Date 2022-06-13 09:39:40
 */
@Configuration
public class FeignConfig implements RequestInterceptor {


    public FeignConfig() {

    }
    @Bean
    @ConditionalOnMissingBean
    public HttpMessageConverters messageConverters(ObjectProvider<HttpMessageConverter<?>> converters) {
        return new HttpMessageConverters(converters.orderedStream().collect(Collectors.toList()));
    }
    /**
     * Openfeign调用日志级别
     * NONE 不输出日志
     * BASIC 只有请求方法、URL、响应状态代码、执行时间
     * HEADERS 基本信息以及请求和响应头
     * FULL 请求和响应 的heads、body、metadata，建议使用这个级别
     *
     * @return NONE BASIC HEADERS FULL
     */
    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.BASIC;
    }

    @Override
    public void apply(RequestTemplate requestTemplate) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null != servletRequestAttributes) {
            HttpServletRequest httpServletRequest = servletRequestAttributes.getRequest();
            //获取Cookie中token
            String token = httpServletRequest.getHeader(ConstParamUtil.X_ACCESS_TOKEN);
            requestTemplate.header(ConstParamUtil.X_ACCESS_TOKEN, new String[]{token});
            requestTemplate.header("requestType", new String[]{"feign"});
        }
    }
//    @Override
//    public void apply(RequestTemplate requestTemplate) {
//        try {
//            Map<String,String> headers = getHeaders();
//            for(String headerName : headers.keySet()){
//                requestTemplate.header(headerName, headers.get(headerName));
//            }
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }

    private Map<String, String> getHeaders(){
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Map<String, String> map = new LinkedHashMap<>();
        Enumeration<String> enumeration = request.getHeaderNames();
        while (enumeration.hasMoreElements()) {
            String key = enumeration.nextElement();
            String value = request.getHeader(key);
            map.put(key, value);
        }
        return map;
    }
}
