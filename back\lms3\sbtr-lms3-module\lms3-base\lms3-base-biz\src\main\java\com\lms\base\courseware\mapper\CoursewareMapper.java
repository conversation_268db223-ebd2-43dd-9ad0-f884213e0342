package com.lms.base.courseware.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.base.feign.model.Courseware;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface CoursewareMapper extends BaseMapper<Courseware> {
    /**
     * 逻辑删除课件。
     *
     * @param courseware
     */
    @Select(value = "update u_courseware set status = 0 where id = #{courseware.id}")
    void deleteCourseware(Courseware courseware);

    /**
     * 物理删除课件。
     *
     * @param delids 课件
     */
    @Select(value = "delete from u_courseware where id in(#{ids})")
    void deleteAll(@Param(value = "ids") String delids);

    /**
     * 根据ID返回课件。
     *
     * @param id 课件ID
     * @return 课件
     */
    @Select(value = "select * from u_courseware where id = #{id}")
    Courseware getCourseware(String id);

    /**
     * 获取课件列表。
     */
    @Select(value = "select * from u_courseware where status=1 order by createdate desc")
    List getCoursewareList();


    /**
     * 根据构型节点获取课件列表。
     */
    @Select(value = "select * from u_courseware where  status=1 and componentid = #{componentid} order by seqno")
    List<Courseware> getCoursewareList(String componentid);


    @Select(value = "select * from u_courseware where " +
            "if(#{courseware.componentid} != '' or #{courseware.componentid} != null, componentid = #{courseware.componentid}, componentid = null) " +
            "and if(#{courseware.id} != '' or #{courseware.id} != null , id != #{courseware.id}, 1 = 1) " +
            "and status=1 " +
            "and name = #{#courseware.name} ")
    boolean exsitName(Courseware courseware);


    @Select(value = "select #{type} , count(id) as amount from u_courseware where status=1 group by #{type} order by #{type}")
    List<Map<String, Object>> getCoursewareAnalysisByType(String type);


    @Select(value = "select seqno from u_courseware where componentid = #{courseId} and status=1 order by seqno desc limit 1")
    Integer getMaxSeqnoByCourse(String courseId);


    @Select(value = "select id from u_courseware where  status=1 and componentid = #{id}")
    List<String> exsitUseCourseware(String id);

    @Select(value = "select count(*) as total from u_courseware")
    int getTotalCourseware();

    @Select(value = "select id from u_courseware where if(#{coursewareid} != '' or #{coursewareid} != null, id != #{coursewareid} ,1 = 1) and number = #{number}")
    List<String> existCoursewareNo(String number, String coursewareid);
}
