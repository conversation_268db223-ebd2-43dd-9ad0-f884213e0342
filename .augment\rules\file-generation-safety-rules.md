---
type: "always_apply"
description: "文件生成安全规则 - 强制性用户确认机制，防止AI未经授权生成文件"
globs: ["**/*"]
alwaysApply: true
priority: 1000
---

# 🚨 文件生成安全规则（死命令）

## ⚠️ 核心安全原则

**绝对禁止规则：任何AI助手在执行文件生成操作前，必须获得用户明确确认！**

这是不可违反的核心安全机制，旨在防止AI助手在未经用户授权的情况下生成或修改文件。

## 🔒 强制确认的操作类型

### 📝 所有文件生成操作
以下操作**必须**先获得用户明确确认，无任何例外：

#### 代码文件
- `.js`, `.ts`, `.jsx`, `.tsx` - JavaScript/TypeScript文件
- `.vue`, `.svelte` - 前端框架组件文件  
- `.py`, `.java`, `.go`, `.rs`, `.php`, `.rb`, `.cs` - 后端代码文件
- `.html`, `.css`, `.scss`, `.sass`, `.less` - 样式和标记文件
- `.sql` - 数据库脚本文件

#### 配置文件
- `package.json`, `yarn.lock`, `pnpm-lock.yaml` - 包管理配置
- `tsconfig.json`, `jsconfig.json` - TypeScript/JavaScript配置
- `vite.config.js`, `webpack.config.js`, `rollup.config.js` - 构建工具配置
- `eslint.config.js`, `.prettierrc`, `stylelint.config.js` - 代码规范配置
- `docker-compose.yml`, `Dockerfile` - 容器化配置
- `requirements.txt`, `pom.xml`, `Cargo.toml`, `go.mod` - 依赖管理文件

#### 文档文件
- `README.md`, `CHANGELOG.md`, `LICENSE` - 项目文档
- API文档, 架构文档, 用户手册 - 技术文档
- 项目规划, 需求文档, 设计文档 - 项目管理文档

#### 项目结构文件
- 目录结构创建
- 脚手架文件生成
- 模板文件创建
- 示例代码文件

#### 部署和运维文件
- CI/CD配置文件 (`.github/workflows/`, `.gitlab-ci.yml`)
- 服务器配置文件 (`nginx.conf`, `apache.conf`)
- 监控配置文件
- 日志配置文件

#### 数据文件
- JSON, XML, YAML 数据文件
- 配置数据文件
- 测试数据文件
- 种子数据文件

## 🛡️ 执行前强制检查机制

### 检查清单
在调用任何 `write_to_file`, `replace_in_file`, 或其他文件操作工具前，AI助手必须确认：

- [ ] **用户明确指令**：用户是否明确表达了"开始开发"、"生成代码"、"创建文件"、"开始实现"等指令？
- [ ] **方案确认**：技术方案、架构设计、文件结构是否已经过用户确认？
- [ ] **内容说明**：即将生成的文件内容和作用是否已向用户详细说明？
- [ ] **影响理解**：用户是否理解文件生成对项目的影响？
- [ ] **权限确认**：用户是否具有在当前目录生成文件的权限和意愿？

### 确认方式
AI助手必须通过以下方式之一获得确认：

1. **直接询问**：
   ```
   "我准备为您生成以下文件：
   - src/components/UserCard.vue (用户卡片组件)
   - src/types/user.ts (用户类型定义)
   - package.json (项目配置文件)
   
   这些文件将实现用户卡片功能。请确认是否开始生成这些文件？"
   ```

2. **MCP反馈工具**：
   ```
   调用 interactive_feedback_mcp-feedback-enhanced 工具，
   详细说明即将生成的文件和原因，等待用户明确确认。
   ```

3. **方案展示**：
   ```
   先展示完整的技术方案和文件结构，
   明确询问用户是否同意开始实现。
   ```

## ⛔ 违规处理机制

### 违规行为定义
以下行为被视为违规：
- 在未获得用户确认的情况下直接生成文件
- 假设用户同意而跳过确认步骤
- 将需求描述误解为开发指令
- 在讨论阶段就开始生成代码文件

### 违规处理流程
如果AI助手违反了文件生成安全规则：

1. **立即停止**：停止所有文件生成操作
2. **承认错误**：向用户道歉并说明违规情况
3. **调用反馈**：使用反馈机制重新与用户沟通
4. **重新确认**：详细说明计划并等待明确指令
5. **记录教训**：将此次违规记录到memory中，避免重复

### 示例处理话术
```
"抱歉，我刚才违反了文件生成安全规则，在未获得您明确确认的情况下
尝试生成文件。让我重新来：

我理解您希望创建一个用户管理系统。在开始生成任何代码文件之前，
请允许我先为您详细说明技术方案：

[详细方案说明...]

请确认：您是否希望我开始生成这些文件？"
```

## 🎯 用户指令识别标准

### 明确的开发指令（可以开始生成文件）
- "开始开发"、"开始实现"、"开始编码"
- "生成代码"、"创建文件"、"写代码"
- "按照这个方案实现"、"就这样做"
- "确认，开始"、"同意，执行"

### 需求讨论（不能生成文件）
- "我想要一个..."、"帮我设计..."
- "如何实现..."、"什么方案比较好..."
- "分析一下..."、"给我建议..."
- "我有个想法..."、"能不能..."

### 模糊指令（需要进一步确认）
- "帮我做一个..."
- "实现这个功能"
- "按照需求开发"
- "根据描述编写"

## 📋 最佳实践建议

### 对于AI助手
1. **保守原则**：当不确定时，选择询问而不是假设
2. **详细说明**：在请求确认时，详细说明将要生成的文件
3. **分步确认**：对于复杂项目，可以分阶段确认
4. **记录决策**：将用户的确认和偏好记录到memory中

### 对于用户
1. **明确指令**：使用明确的开发指令，如"开始实现"
2. **详细需求**：提供详细的需求描述和技术偏好
3. **及时反馈**：对AI的确认请求及时回应
4. **分阶段确认**：对于大型项目，可以分阶段确认实现

## 🔧 技术实现

### 工具调用前检查
```javascript
// 伪代码示例
function beforeFileOperation(operation, filePath, content) {
    if (!userConfirmationReceived) {
        throw new Error("文件生成安全规则违规：未获得用户确认");
    }
    
    if (!isExplicitDevelopmentCommand(userInput)) {
        requestUserConfirmation(operation, filePath, content);
        return false;
    }
    
    return true;
}
```

### 确认状态管理
AI助手应该维护一个确认状态，跟踪：
- 用户是否已经给出明确的开发指令
- 哪些文件已经获得生成授权
- 当前处于讨论阶段还是实现阶段

## 📊 监控和审计

### 违规监控
- 记录所有文件生成操作
- 标记未经确认的操作尝试
- 统计违规频率和类型

### 审计报告
定期生成审计报告，包括：
- 文件生成操作统计
- 用户确认率
- 违规事件分析
- 改进建议

## 🎉 总结

这个文件生成安全规则是保护用户项目安全的重要机制。所有AI助手都必须严格遵循这些规则，确保在获得用户明确授权之前，绝不生成任何文件。

**记住：宁可多问一次，也不要未经授权就行动！**