package com.sbtr.workflow.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sbtr.workflow.mapper.ActMyFormMapper;
import com.sbtr.workflow.model.ActMyForm;
import com.sbtr.workflow.service.ActMyFormService;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;
import com.sbtr.workflow.utils.PageUtil.PageUtils;
import com.sbtr.workflow.vo.FormLayoutVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("actMyFormServiceImpl")
public class ActMyFormServiceImpl extends WorkflowBaseServiceImpl<ActMyForm, String> implements ActMyFormService {
    @Autowired
    private ActMyFormMapper actMyFormMapper;

    //分页
    @Override
    public PageSet<ActMyForm> getPageSet(PageParam pageParam, String filterSort) {
        PageHelper.startPage(pageParam.getPageNo(), pageParam.getPageSize());
        List<ActMyForm> list = actMyFormMapper.getPageSet(filterSort);
        PageInfo<ActMyForm> pageInfo = new PageInfo<>(list);
        return PageUtils.getPageSet(pageInfo);
    }

    //删除
    @Override
    public int executeDeleteBatch(String[] uuid) {
        return actMyFormMapper.executeDeleteBatch(uuid);
    }

    @Override
    public ActMyForm selectByProcessInstanceId(String processInstanceId, String modelKey, String processDefinitionId) {
        return actMyFormMapper.selectByProcessInstanceId(processInstanceId,modelKey,processDefinitionId);
    }

    @Override
    public Integer updateFormByPrimaryKeySelective(String uuid, String formDesign) {
        return actMyFormMapper.updateFormByPrimaryKeySelective(uuid,formDesign);
    }

    @Override
    public List<ActMyForm> getListDataByModelKeyAndProcdefId(String modelKey, String procdefId) {
        return actMyFormMapper.getListDataByModelKeyAndProcdefId(modelKey,procdefId);
    }

    @Override
    public String getFormLayoutFormJSON(String formLayou) {
        return actMyFormMapper.getFormLayoutFormJSON(formLayou);
    }

    @Override
    public List<FormLayoutVo> getFormLayoutForm(String formUuid) {
        return actMyFormMapper.getFormLayoutForm(formUuid);
    }

    @Override
    public Integer updateFormDesignByUuid(String json, String uuid) {
        return actMyFormMapper.updateFormDesignByUuid(json,uuid);
    }


}
