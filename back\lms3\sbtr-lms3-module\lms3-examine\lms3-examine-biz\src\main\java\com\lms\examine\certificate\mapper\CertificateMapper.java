
package com.lms.examine.certificate.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lms.examine.feign.model.Certificate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface CertificateMapper extends BaseMapper<Certificate> {

    @Select("select * from u_certificate where model = #{modelId}")
    List<Certificate> getByModelId(@Param("modelId") String modelId);

    @Select("${sql}")
    Map getMaxSeqno(@Param("sql") String sql);
}
