package com.sbtr.workflow.service;

import com.sbtr.workflow.model.ActMyModel;
import com.sbtr.workflow.utils.PageUtil.PageParam;
import com.sbtr.workflow.utils.PageUtil.PageSet;

import java.util.List;


/**
 * 流程表单模型
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-10-21 02:37:50
 */
public interface ActMyModelService extends WorkflowBaseService<ActMyModel, String> {

    /**
     * 分页
     *
     * @return
     * @Param pageParam 分页参数
     * @Param filterSort 过滤排序字段
     */
    PageSet<ActMyModel> getPageSet(PageParam pageParam, String filterSort);

    /**
     * 批量删除
     *
     * @return
     * @Param uuid 主键
     */
	int executeDeleteBatch(String[] uuids);

    /**
     * 根据模型key查询详细数据
     *
     * @return
     */
    ActMyModel getListByModelKey(String modelKey);

    /**
     * 根据uuid更新表单数据
     *
     * @return
     */
    int updateFormDesign(String uuid, String formDesign, String formModel);

    /**
     * 根据uuid更新表单数据
     *
     * @return
     */
    Object updateFormDesignByActDeModelKey(String actDeModelKey, String formDesign, String formModel);
    /**
     * 根据uuid更新表单数据
     *
     * @return
     */
    Integer updateFormFieldJson(String formTableName, String formJson, String formModel);

    /**
     * 根据模型key以及流程定义Id为空 查询详细数据
     *
     * @return
     */
    List<ActMyModel> getListByModelKeyAndProcdefIdIsNull(String key);

    /**
     * 根据模型key以及流程定义 查询详细数据
     *
     * @return
     */
    List<ActMyModel> getListByActDeModelKeyAndProcdefId(String key, String procdefIds);

    /**
     * 根据模型key以及流程定义 吧流程定义Id置为空
     *
     * @return
     */
    Integer updateProcdefIdIsNull(String key, String id);

    /**
     * 根据模型key查询有多少条记录
     *
     * @return
     */
    Integer selectCountInteger(String key);


    /**
     * 据模型key以及流程定义 删除数据
     *
     * @return
     */
    Integer deleteByModelKeyAnProcdefId(String key, String id);


    /**
     * 判断用户是否拥有启动流程的权限
     *
     * @return
     */
    Boolean canTheProcessBeStarted(String userNameId,String permissionType,String permissionValue);

    //修改流程定义表act_re_procdef name字段 根据key和主版本
    Integer updateActReProcdefNameByKey(String modelKey, String majorVersion, String actDeModelName);

    String getDesignJson(String formUuid);

    List<ActMyModel> getMajorModelByKey(String modelKey);
    Boolean getAuthStarted(String userNameId, String modelKey, String procdefId);

    Integer updateProcdefIdByModelKeyAndProcdef(String procdefId, String key, String id);
}
