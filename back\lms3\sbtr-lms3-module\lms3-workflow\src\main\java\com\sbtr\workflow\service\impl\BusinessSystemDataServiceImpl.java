package com.sbtr.workflow.service.impl;

import cn.ewsd.common.utils.easyui.PageParam;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lms.base.feign.api.LmsBaseApi;
import com.lms.common.model.Result;
import com.lms.common.util.ContextUtil;
import com.lms.common.util.DateHelper;
import com.sbtr.workflow.enums.ApiTableMappingEnum;
import com.sbtr.workflow.mapper.ApiFlowableTaskMapper;
import com.sbtr.workflow.mapper.SysOrganizationMapper;
import com.sbtr.workflow.mapper.SysPositionMapper;
import com.sbtr.workflow.mapper.SysRoleMapper;
import com.sbtr.workflow.mapper.SysUserMapper;
import com.sbtr.workflow.model.*;
import com.sbtr.workflow.service.ActMyNodeButtonService;
import com.sbtr.workflow.service.ApiFlowableTaskService;
import com.sbtr.workflow.service.BusinessSystemDataService;
import com.sbtr.workflow.utils.OkHttpClientUtil;
import com.sbtr.workflow.vo.AddTaskNewVo;
import com.sbtr.workflow.vo.TaskVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * BusinessSystemDataService
 * 业务系统使用工作流引擎需要实现的接口示例（仅用于调试和启动工作流），
 * 真实接入工作流时本类应按业务系统自身情况重新实现类中的方法,
 * 推荐在业务系统中自行创建新的class实现BusinessSystemDataService接口，并注释掉本类的@Service注解
 *
 * <AUTHOR>
 * @Date 2024-05-15 11:10:30
 */

@Service("BusinessSystemDataServiceImpl")
@Slf4j
public class BusinessSystemDataServiceImpl extends BaseServiceImpl implements BusinessSystemDataService {
    @Resource
    private SysRoleMapper sysRoleMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private SysOrganizationMapper sysOrganizationMapper;
    @Resource
    private SysPositionMapper sysPositionMapper;
    @Resource
    private LmsBaseApi lmsBaseApi;
    @Resource
    private ActMyNodeButtonService flowNodeButtonService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private OkHttpClientUtil okHttpClientUtil;
    @Resource
    private ApiFlowableTaskService apiFlowableTaskService;
    @Autowired
    private ApiFlowableTaskMapper apiFlowableTaskMapper;

    @Value("${business.sso.appID}")
    private String appID;

    @Value("${business.portals.url}")
    private String portalsUrl;

    @Value("${business.sso.serviceAddress}")
    private String serviceAddress;

    @Value("${business.portals.isOpen}")
    private boolean isOpen;

    @Override
    public String getUserNameId() {
        return ContextUtil.getUserName();
    }

    @Override
    public String getUserName() {
        return ContextUtil.getPersonName();
    }

    @Override
    public String getUserSecurity() {
        return null;
    }

    @Override
    public Long getProjectId() {
        return null;
    }

    @Override
    public List<String> getUserPositions() {
        return new ArrayList<>();
    }

    @Override
    public List<String> getUserPositions(String userId) {
        return null;
    }

    @Override
    public List<String> getUserRoles() {
        return ContextUtil.getRoles();
    }

    @Override
    public List<String> getUserRoles(String userNameIds) {
        List<String> roles = new ArrayList<>();
        String[] strs = userNameIds.split(",");
        for (String userNameId : strs) {
            List<String> userRoles = lmsBaseApi.getRoleIdByUserName(userNameId).getResult();
            roles.addAll(userRoles);
        }
        roles = roles.stream().distinct().collect(Collectors.toList());
        return roles;
    }

    @Override
    public void initLoginfo(String loginUserInfo) {
//        UserInfo userInfo = new UserInfo();
//        userInfo.setUuid(id);
//        userInfo.setUserNameId(userNameId);
//        userInfo.setUserName(userName);
//        userInfo.setOrgId("0");
//        // 初始化用户信息时需要赋值当前用户所属岗位list,如果没有则系统定义的节点使用岗位审批规则时当前用户无法查询到流程数据
//        userInfo.setPositions(userPositions==null?new ArrayList<String>():userPositions);
//        // 初始化用户信息时需要赋值当前用户所属角色list,如果没有则系统定义的节点使用角色审批规则时当前用户无法查询到流程数据
//        userInfo.setRoles(userRoles==null?new ArrayList<String>():userRoles);
////            String roles = cmsSessionContext.get.getRoles().stream().map(role -> role.getId().toString()).collect(Collectors.joining(","));
////            userInfo.setRoleId(roles);
//        LoginInfo.add(userInfo);
    }

    @Override
    public PageInfo<SysRole> getListSysRole(PageParam pageParam, String roleName) {
        PageHelper.startPage(pageParam.getPage(), pageParam.getRows());
        List<SysRole> list = sysRoleMapper.getPageSetData(roleName);
        PageInfo<SysRole> pageInfo = new PageInfo<>(list);
        return pageInfo;
//        Example example = new Example(SysRole.class);
//        List<SysRole> sysRoleList = sysRoleMapper.selectByExample(example);
//        return sysRoleList;
    }

    @Override
    public List<SysUser> queryUsers() {
        Example example = new Example(SysUser.class);
        return sysUserMapper.selectByExample(example);
    }

    @Override
    public PageInfo<SysPosition> getListPosition(PageParam pageParam, String positionName) {
        PageHelper.startPage(pageParam.getPage(), pageParam.getRows());
        List<SysPosition> list = sysPositionMapper.getPageSetData(positionName);
        PageInfo<SysPosition> pageInfo = new PageInfo<>(list);
        return pageInfo;
//        Example example = new Example(SysPosition.class);
//        List<SysPosition> sysPositions = sysPositionMapper.selectByExample(example);
//        return sysPositions;
    }

    @Override
    public List<SysOrganization> queryOrganizations() {
        Example example = new Example(SysOrganization.class);
        return sysOrganizationMapper.selectByExample(example);
    }


    @Override
    public SysUser getSysUserByUserNameId(String userNameId) {
//        SysUser sysUser = sysUserMapper.selectByPrimaryKey(userId);
        Example example = new Example(SysUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userNameId", userNameId);
        List<SysUser> lists = sysUserMapper.selectByExample(example);
        return !lists.isEmpty() ? lists.get(0) : new SysUser();
    }

    @Override
    public String getleaderIdByUserNameId(String startUserId) {
        return null;
    }

    @Override
    public String getUserNameIdByRoleId(String roles) {
        String[] roleIds = roles.split(",");
        List<String> users = new ArrayList<>();
        for (int i = 0; i < roleIds.length; i++) {
            List<String> userNames = lmsBaseApi.getUserNamesBySysRoleLink(roleIds[i]).getResult();
            users.addAll(userNames);
        }
        users = users.stream().distinct().collect(Collectors.toList());
        return String.join(",", users);
    }

    @Override
    public String getUserNameIdByPost(String join) {
        return null;
    }

    @Override
    public List<SysOrganization> getListOrganizationByOrgId(String toStr) {
        return null;
    }

    @Override
    public String selectUserNameByuuid(String userId) {
        return null;
    }

    @Override
    public List<SysUser> getUserDetailByIds(String ids, String fieldName) {
        List<String> arr = Arrays.asList(ids.split(","));
        Example example = new Example(SysUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn(fieldName, arr);
        return sysUserMapper.selectByExample(example);
    }

    @Override
    public String getReportsToByUserNameId(String userNameId) {
        return null;
    }

    @Override
    public JSONObject getBusinessData(String tablename, String primarykey, String keyValue, String modelKey) throws Exception {
        Result result;
        ApiTableMappingEnum apiTableMappingEnum = ApiTableMappingEnum.getByTableName(tablename);
        if (null != apiTableMappingEnum){
            if (apiTableMappingEnum.getTableName().equals("u_dutytrainingplan") &&
            modelKey.equals("process1728718844527")){
                result = (Result) buildSubModelTableData(apiTableMappingEnum.getApiClazz(), keyValue, "getById", modelKey);
            }else {
                result = (Result) buildSubModelTableData(apiTableMappingEnum.getApiClazz(), keyValue, apiTableMappingEnum.getMethodName(), modelKey);
            }
            return (result == null || result.getResult() == null) ? null : JSON.parseObject(JSON.toJSONString(result.getResult(), SerializerFeature.WriteNullStringAsEmpty, SerializerFeature.WriteNullListAsEmpty, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullBooleanAsFalse));
        }
        HashMap<String, Object> businessDataMap = flowNodeButtonService.selectByBusinessKey("select * from " + tablename + " where " + primarykey + "='" + keyValue + "'");
        return JSON.parseObject(JSON.toJSONString(businessDataMap));
    }

    private Object buildSubModelTableData(Class api, String keyValue, String methodName, String modelKey) throws Exception {
        Object bean = applicationContext.getBean(api);
        Object result;
        if (methodName.equals("getAllTrainFlowProcessById")){
            Method method = bean.getClass().getMethod(methodName, String.class, String.class);
            result = method.invoke(bean, keyValue, modelKey);
        }else {
            Method method = bean.getClass().getMethod(methodName, String.class);
            result = method.invoke(bean, keyValue);
        }
        return result;
    }

    @Override
    public Map<String, String> getViewCode(String procDefId) {
        return new HashMap<>();
    }

//    @Override
//    public String getOrgId() {
//        return super.businessSystemDataService.getOrgId();
//    }

    @Override
    public void saveSysOperLog(String jsonStr) {

    }

    @Override
    public String getTenantId() {
        return null;
    }

    @Override
    public String getUserId() {
        return ContextUtil.getUserId();
    }

    @Override
    public String getPersonId() {
        return ContextUtil.getPersonId();
    }

    @Override
    public void pushAddTaskNew(String taskId) {
        try {
            if(!isOpen) return;
            String businessKey = "";
            String formName = "";
            if (StringUtils.isNotEmpty(taskId)){
                List<TaskVo> taskVoList = apiFlowableTaskMapper.getBusinessInfoByTaskIdMySql(taskId);
                if (CollectionUtils.isNotEmpty(taskVoList)){
                    TaskVo taskVo = taskVoList.get(0);
                    businessKey = taskVo.getBusinessKey();
                    formName = taskVo.getFormName();
                }else {
                    List<TaskVo> taskVoList1 = apiFlowableTaskMapper.getBusinessInfoByTaskIdFromApplyedMySql(taskId);
                    if (CollectionUtils.isNotEmpty(taskVoList1)){
                        TaskVo taskVo = taskVoList1.get(0);
                        businessKey = taskVo.getBusinessKey();
                        formName = taskVo.getFormName();
                    }
                }
            }
            if (StringUtils.isEmpty(businessKey)){
                return;
            }
            List<String> assignee = apiFlowableTaskService.getCurrentAssigneeUserIdByTask(taskId);
            String assigneeStr = org.apache.commons.lang3.StringUtils.join(assignee, ";");
            String owner = businessSystemDataService.getPersonId();
            AddTaskNewVo addTaskNewVo = new AddTaskNewVo();
            addTaskNewVo.setAppID(appID);
            addTaskNewVo.setTaskName(formName);
            addTaskNewVo.setAppTaskID(businessKey);
            addTaskNewVo.setTaskType("cbt系统通知");
            addTaskNewVo.setAppSendUID(owner);
            addTaskNewVo.setAppReceiveUID(assigneeStr);
            addTaskNewVo.setSendTime(DateHelper.getCurDateTime());
            addTaskNewVo.setEndTime(DateHelper.dayMoveDateTime(DateHelper.getCurDateTime(),
                    0,3,0,0,0,0));
            addTaskNewVo.setUrl(serviceAddress + "/#/doTask?taskId="+ taskId);
            addTaskNewVo.setTaskDesc(businessKey);
            addTaskNewVo.setPriorityID("2"); //紧急程度 一般
            addTaskNewVo.setRemark(""); //来源单位
            String url = portalsUrl + "addTaskNew";
            log.info("addTaskNew url: {}  request: {}", url, JSON.toJSONString(addTaskNewVo));
            String addTaskNewVoResult = okHttpClientUtil.doPostJson(url, addTaskNewVo);
            //String addTaskNewVoResult = okHttpClientUtil.doPostForm(url, JSON.parseObject(JSON.toJSONString(addTaskNewVo), Map.class));
            log.info("addTaskNew response: {}", addTaskNewVoResult);
        } catch (Exception e) {
            log.error("addTaskNew error: {}", e.getMessage());
        }
    }

    @Override
    public void pushViewTask(String businessKey) {
        try {
            if(!isOpen) return;
            Map<String, String> viewTaskParam = new HashMap<>();
            viewTaskParam.put("appTasksID", businessKey);
            viewTaskParam.put("appID", appID);
            viewTaskParam.put("handleTime", String.valueOf(System.currentTimeMillis()));
            String url = portalsUrl + "viewTask";
            log.info("viewTask url: {}  request: {}", url, JSON.toJSONString(viewTaskParam));
            String addTaskNewVoResult = okHttpClientUtil.doPostJson(url, viewTaskParam);
            //String addTaskNewVoResult = okHttpClientUtil.doPostForm(url, viewTaskParam);
            log.info("viewTask response: {}", addTaskNewVoResult);
        } catch (Exception e) {
            log.error("viewTask error {}", e.getMessage());
        }
    }

    @Override
    public void pushCancelTask(String businessKey) {
        try {
            if(!isOpen) return;
            Map<String, String> cancelTaskVo = new HashMap<>();
            cancelTaskVo.put("appTasksID", businessKey);
            cancelTaskVo.put("appID", appID);
            cancelTaskVo.put("handleTime", String.valueOf(System.currentTimeMillis()));
            String url = portalsUrl + "cancelTask";
            log.info("cancelTask url: {}  request: {}", url, JSON.toJSONString(cancelTaskVo));
            String addTaskNewVoResult = okHttpClientUtil.doPostJson(url, cancelTaskVo);
            //String addTaskNewVoResult = okHttpClientUtil.doPostForm(url, cancelTaskVo);
            log.info("cancelTask response: {}", addTaskNewVoResult);
        } catch (Exception e) {
            log.error("cancelTask error {}", e.getMessage());
        }
    }

    @Override
    public void pushCompleteTask(String businessKey, String taskId) {
        try {
            if(!isOpen) return;
            if (StringUtils.isEmpty(businessKey) && StringUtils.isNotEmpty(taskId)){
                List<TaskVo> taskVoList = apiFlowableTaskMapper.getBusinessInfoByTaskIdMySql(taskId);
                if (CollectionUtils.isNotEmpty(taskVoList)){
                    TaskVo taskVo = taskVoList.get(0);
                    businessKey = taskVo.getBusinessKey();
                }else {
                    List<TaskVo> taskVoList1 = apiFlowableTaskMapper.getBusinessInfoByTaskIdFromApplyedMySql(taskId);
                    if (CollectionUtils.isNotEmpty(taskVoList1)){
                        TaskVo taskVo = taskVoList1.get(0);
                        businessKey = taskVo.getBusinessKey();
                    }
                }
            }
            if (StringUtils.isEmpty(businessKey)){
                return;
            }
            Map<String, String> cancelTaskVo = new HashMap<>();
            cancelTaskVo.put("appTasksID", businessKey);
            cancelTaskVo.put("appID", appID);
            cancelTaskVo.put("handleTime", String.valueOf(System.currentTimeMillis()));
            String url = portalsUrl +"completeTask";
            log.info("completeTask url: {}  request: {}", url, JSON.toJSONString(cancelTaskVo));
            String addTaskNewVoResult = okHttpClientUtil.doPostJson(url, cancelTaskVo);
            //String addTaskNewVoResult = okHttpClientUtil.doPostForm(url, cancelTaskVo);
            log.info("completeTask response: {}", addTaskNewVoResult);
        } catch (Exception e) {
            log.error("completeTask error {}", e.getMessage());
        }
    }

}
