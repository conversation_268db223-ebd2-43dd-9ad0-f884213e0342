package com.lms.common.dao.impl;

import com.lms.common.dao.BaseInfluxDBDao;
import com.lms.common.util.InfluxDBUtil;

import java.time.OffsetDateTime;
import java.util.Collections;
import java.util.List;

public abstract class BaseInfluxDBDaoImpl<T> implements BaseInfluxDBDao<T> {

    protected final String measurement;

    protected final String bucket = "cbt";

    protected final Class<T> clazz;

    protected BaseInfluxDBDaoImpl(String measurement, Class<T> clazz) {
        this.measurement = measurement;
        this.clazz = clazz;
    }

    @Override
    public void save(T entity) {
        InfluxDBUtil.writePojo(entity);
    }

    @Override
    public void saveBatch(List<T> entities) {
        InfluxDBUtil.writeBatch(entities);
    }

    @Override
    public void deleteById(String id) {
        String predicate = String.format("id=\"%s\"", id);
        InfluxDBUtil.delete(measurement, "1970-01-01T00:00:00Z", OffsetDateTime.now().toString(), predicate);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        for (String id : ids) {
            deleteById(id);
        }
    }

    @Override
    public T findById(String id) {
        String flux = String.format(
                "from(bucket: \"%s\") |> range(start: 0) |> filter(fn: (r) => r._measurement == \"%s\" and r.id == \"%s\") |> pivot(rowKey:[\"_time\"], columnKey: [\"_field\"], valueColumn: \"_value\") |> limit(n:1)",
                bucket, measurement, id);
        List<T> list = InfluxDBUtil.queryToPojo(flux, clazz);
        return list.isEmpty() ? null : list.get(0);
    }

    @Override
    public List<T> findByIds(List<String> ids) {
        if (ids == null || ids.isEmpty()) return Collections.emptyList();
        StringBuilder predicate = new StringBuilder();
        for (int i = 0; i < ids.size(); i++) {
            if (i > 0) predicate.append(" or ");
            predicate.append(String.format("r.id == \"%s\"", ids.get(i)));
        }
        String flux = String.format(
                "from(bucket: \"%s\") |> range(start: 0) |> filter(fn: (r) => r._measurement == \"%s\" and (%s)) |> pivot(rowKey:[\"_time\"], columnKey: [\"_field\"], valueColumn: \"_value\")",
                bucket, measurement, predicate);
        return InfluxDBUtil.queryToPojo(flux, clazz);
    }

    @Override
    public List<T> queryList(java.util.Map<String, Object> conditions, int page, int size) {
        StringBuilder flux = new StringBuilder();
        flux.append(String.format("from(bucket: \"%s\") |> range(start: 0) |> filter(fn: (r) => r._measurement == \"%s\"", bucket, measurement));
        if (conditions != null && !conditions.isEmpty()) {
            for (java.util.Map.Entry<String, Object> entry : conditions.entrySet()) {
                if (entry.getValue() != null && !entry.getValue().toString().isEmpty()) {
                    flux.append(String.format(" and r.%s == \"%s\"", entry.getKey(), entry.getValue()));
                }
            }
        }
        flux.append(")");
        flux.append(" |> pivot(rowKey:[\"_time\"], columnKey: [\"_field\"], valueColumn: \"_value\")");
        flux.append(String.format(" |> sort(columns:[\"_time\"], desc:true) |> limit(n:%d, offset:%d)", size, (page - 1) * size));
        return InfluxDBUtil.queryToPojo(flux.toString(), clazz);
    }
}
