<template>
  <div class="bpmn-editor-container">
    <div class="toolbar">
      <a-button type="primary" @click="saveDiagram"
        ><SaveOutlined />保存流程图</a-button
      >
      <a-button @click="zoom(0.05)"><ZoomInOutlined />放大</a-button>
      <a-button @click="zoom(-0.05)"><ZoomOutOutlined />缩小</a-button>
      <a-button @click="fitViewport"><PicCenterOutlined />自适应屏幕</a-button>
      <a-button @click="undo"><UndoOutlined />撤销</a-button>
      <font-selector
        :currentStyle="currentStyle"
        @style-change="
          value => {
            styleChange(value);
          }
        "
      />
    </div>

    <div class="editor-wrapper">
      <div ref="container" class="canvas" />
      <CustomPropertiesPanel
        :modeler="modeler"
        :currentElement="currentElement"
        @update-properties="updateProperties"
      />
    </div>
    <!--    <div class="xml-viewer">-->
    <!--      <h3>BPMN XML</h3>-->
    <!--      <textarea v-model="xml" rows="20" />-->
    <!--    </div>-->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, toRaw } from "vue";
import BpmnModeler from "bpmn-js/lib/Modeler";
import "bpmn-js/dist/assets/diagram-js.css";
import "bpmn-js/dist/assets/bpmn-font/css/bpmn.css";
// 右边工具栏样式
import "bpmn-js-properties-panel/dist/assets/properties-panel.css";
import customControlsModule from "./customControls";
import CustomPropertiesPanel from "./components/propertiespanel.vue";
import {
  BpmnPropertiesPanelModule,
  BpmnPropertiesProviderModule
} from "bpmn-js-properties-panel";
import {
  customTranslate,
  moduleExtension
} from "@/views/mrPlatform/courseware/workflow/data.ts";
import {
  getFlowchart,
  saveFlowchart,
  updateFlowchart
} from "@/api/workflow.ts";
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  UndoOutlined,
  PicCenterOutlined,
  SaveOutlined
} from "@ant-design/icons-vue";
import { message } from "ant-design-vue";
import FontSelector from "@/components/Selector/fontSelector.vue";
const container = ref(null);
const modeler = ref(null);
const currentElement = ref(null);
const currentStyle = ref(null);
const xml = ref("");
const canRedo = ref(false);
const scale = ref(1);
const props = defineProps({
  cid: String
});
const isEditMode = ref(false);
const modelKey = "Process_" + props.cid;
// 保存从接口获取的流程模型数据
const flowModelData = ref(null);

// 初始化建模器
onMounted(() => {
  initDiagram();
  loadProcessEvent();
});
// 清理资源
onBeforeUnmount(() => {
  if (modeler.value) {
    modeler.value.destroy();
  }
});

const initDiagram = () => {
  if (container.value) {
    let customTranslateModule = {
      translate: ["value", customTranslate]
    };
    modeler.value = new BpmnModeler({
      container: container.value,
      additionalModules: [
        customTranslateModule,
        customControlsModule,
        BpmnPropertiesPanelModule,
        BpmnPropertiesProviderModule
      ],
      moddleExtensions: {
        flowable: moduleExtension
      }
    });
    loadProcessDefinitions();
  }
};

// 加载流程定义列表
const loadProcessDefinitions = async () => {
  try {
    const response = await getFlowchart(modelKey);
    if (response.success) {
      // 保存完整的flowModel数据
      flowModelData.value = response.result.flowModel;
      const dataXml = flowModelData.value.flowDesign;
      if (dataXml !== null && dataXml.length > 0) {
        await modeler.value.importXML(dataXml);
      }
      isEditMode.value = true;
    } else {
      await modeler.value.createDiagram();
      const modeling = modeler.value.get("modeling");
      const rootElement = modeler.value.get("canvas").getRootElement();
      // 修改流程ID
      modeling.updateProperties(rootElement, {
        id: modelKey,
        di: { id: "BpmnPlane_" + props.cid },
        isExecutable: true
      });
      isEditMode.value = false;
    }
  } catch (error) {
    console.error("Error loading process definitions:", error);
  }
};

// 保存当前流程图
const saveDiagram = async () => {
  try {
    const rootElement = modeler.value.get("canvas").getRootElement();
    const { xml: currentXml } = await modeler.value.saveXML({
      format: true
    });
    xml.value = currentXml;
    const processName = rootElement.businessObject.name;
    const param = new FormData();
    if (isEditMode.value && flowModelData.value) {
      // 编辑模式：使用ModleDto的字段名，并传递必要的字段
      param.append("actDeModelKey", flowModelData.value.actDeModelKey);
      param.append("actDeModelName", processName);
      param.append("flowDesign", currentXml);
      // 传递其他必要字段
      param.append("uuid", flowModelData.value.uuid);
      param.append("actDeModelId", flowModelData.value.actDeModelId);
      param.append("formUuid", flowModelData.value.formUuid);
      param.append("modelType", flowModelData.value.modelType);
      param.append("permissionType", flowModelData.value.permissionType);
      param.append(
        "permissionValue",
        flowModelData.value.permissionValue || ""
      );
      param.append("formDesign", flowModelData.value.formDesign || "");
      param.append("appPagePath", flowModelData.value.appPagePath || "");
      param.append("formTableName", flowModelData.value.formTableName || "");
      param.append("formModel", flowModelData.value.formModel || "");
      param.append("procdefId", flowModelData.value.procdefId || "");
      param.append("sign", flowModelData.value.sign || "");
    } else {
      // 新增模式：使用FlowModelDto的字段名
      param.append("modelKey", modelKey);
      param.append("modelName", processName);
      param.append("flowJson", currentXml);
    }
    const response = isEditMode.value
      ? await updateFlowchart(param)
      : await saveFlowchart(param);
    if (response.success) {
      message.success(response.message);
    } else {
      message.error(response.message);
    }
    // loadProcessDefinitions();
  } catch (error) {
    console.error("Error saving diagram:", error);
  }
};

// 流程模型节点变化事件
function loadProcessEvent() {
  // 点击节点获取节点相关属性
  modeler.value.on("element.click", e => {
    currentElement.value = e.element;
  });
  modeler.value.on("selection.changed", e => {
    if (!e.newSelection || e.newSelection.length === 0) {
      currentStyle.value = {};
      return;
    }
    if (!e.newSelection || e.newSelection.length > 1) {
      return;
    }
    const element = e.newSelection[0];
    const elementRegistry = modeler.value.get("elementRegistry");
    const svgElement = elementRegistry.getGraphics(element);
    let textList = svgElement.getElementsByTagName("text");
    if (textList.length > 0) {
      let item = textList[0];
      let itemStyle = cssText2Object(item.style.cssText);
      currentStyle.value = itemStyle;
    } else {
      let pathList = svgElement.getElementsByTagName("path");
      if (pathList.length > 0) {
        const strokeColor =
          pathList[0].style.stroke ||
          pathList[0].getAttribute("stroke") ||
          "#000";
        currentStyle.value = { stroke: strokeColor };
      } else {
        const strokeColor =
          svgElement.style.stroke ||
          svgElement.getAttribute("stroke") ||
          "#000";
        currentStyle.value = { stroke: strokeColor };
      }
    }
  });
}

const styleChange = (style: any) => {
  const selection = modeler.value.get("selection");
  const elementRegistry = modeler.value.get("elementRegistry");
  const modeling = modeler.value.get("modeling");
  const selectedElements = selection.get();
  selectedElements.forEach(element => {
    const svgElement = elementRegistry.getGraphics(element);
    Object.entries(style).forEach(([key, value]) => {
      if (key === "fill") {
        // svgElement.style.setProperty("stroke", value);
        modeling.setColor(element, { stroke: value });
      }
    });
    let textList = svgElement.getElementsByTagName("text");
    Array.from(textList)?.forEach(item => {
      // let itemStyle = cssText2Object(item.style.cssText);
      // itemStyle = Object.assign(itemStyle, style);
      const styleString = Object.entries(style)
        .map(([k, v]) => `${k}:${v}`)
        .join(";");
      item.setAttribute("style", styleString);
    });
  });
};

const cssText2Object = cssText => {
  const obj = {};
  // 按分号拆分规则，过滤空值
  const rules = cssText.split(";").filter(rule => rule.trim());
  rules.forEach(rule => {
    // 拆解每条规则
    const [prop, value] = rule.split(":").map(item => item.trim());
    if (prop && value) {
      obj[prop] = value;
    }
  });
  return obj;
};

// 更新基础属性
const updateProperties = (key, value) => {
  const modeling = toRaw(modeler.value).get("modeling");
  modeling.updateProperties(toRaw(currentElement.value), { [key]: value });
};

const fitViewport = () => {
  // this.zoom = modeler.value.get("canvas").zoom("fit-viewport");
  const bbox = document.querySelector(".viewport").getBBox();
  const currentViewbox = modeler.value.get("canvas").viewbox();
  const elementMid = {
    x: bbox.x + bbox.width / 2 - 65,
    y: bbox.y + bbox.height / 2
  };
  modeler.value.get("canvas").viewbox({
    x: elementMid.x - currentViewbox.width / 2,
    y: elementMid.y - currentViewbox.height / 2,
    width: currentViewbox.width,
    height: currentViewbox.height
  });
  // this.zoom = (bbox.width / currentViewbox.width) * 1.8;
};

//撤销
const undo = () => {
  modeler.value.get("commandStack").undo();
  canRedo.value = modeler.value.get("commandStack").canRedo();
};

//恢复
const redo = () => {
  if (!canRedo.value) {
    return;
  }
  modeler.value.get("commandStack").redo();
  canRedo.value = modeler.value.get("commandStack").canRedo();
};

// 放大缩小
const zoom = val => {
  let newScale = !val
    ? 1.0
    : scale.value + val <= 0.2
      ? 0.2
      : scale.value + val;
  modeler.value.get("canvas").zoom(newScale);
  scale.value = newScale;
};
</script>

<style scoped>
.bpmn-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.toolbar {
  display: flex;
  padding: 10px;
  background: #ffffff;
  border-bottom: 1px solid #ddd;
}

.toolbar button,
.toolbar select {
  margin-right: 10px;
  padding: 5px 10px;
}

/* 新增样式 */
.editor-wrapper {
  position: relative;
  flex: 1;
  height: 500px;
  border: 1px solid #ccc;
}

.canvas {
  height: 100%;
  width: calc(100% - 300px);
}

.xml-viewer {
  height: 30%;
  padding: 10px;
  border-top: 1px solid #ddd;
}

.xml-viewer textarea {
  width: 100%;
  height: calc(100% - 30px);
  font-family: monospace;
}

:deep(svg:focus, svg:active) {
  outline: none !important; /* 清除黑色边框 */
  -webkit-tap-highlight-color: transparent; /* 清除移动端点击高亮 */
}
:deep(.bjs-container) {
  border-right: 1px solid #ccc;
}

:deep(.bio-properties-panel-container) {
  display: none;
}
:deep(.bjs-powered-by) {
  display: none;
}

:deep(.djs-palette) {
  width: 140px !important;
}
:deep(.djs-palette .entry) {
  width: 100%;
}
:deep(.djs-palette-entries .entry[class*=" bpmn-icon-"]:before) {
  margin-left: -100px;
}

:deep(.entry.bpmn-icon-hand-tool::after) {
  content: "抓手工具";
  font-size: 14px;
  position: absolute;
  left: 45px;
  white-space: nowrap;
  height: 46px;
  line-height: 46px;
}
.entry.bpmn-icon-hand-tool:hover::after {
  color: #40a9ff;
}

:deep(.entry.bpmn-icon-lasso-tool::after) {
  content: "套索工具";
  font-size: 14px;
  position: absolute;
  left: 45px;
  white-space: nowrap;
  height: 46px;
  line-height: 46px;
}
.entry.bpmn-icon-lasso-tool:hover::after {
  color: #40a9ff;
}

:deep(.djs-palette-entries .entry.bpmn-icon-connection-multi::after) {
  content: "连接线";
  font-size: 14px;
  position: absolute;
  left: 45px;
  white-space: nowrap;
  height: 46px;
  line-height: 46px;
}
.entry.bpmn-icon-connection-multi:hover::after {
  color: #40a9ff;
}

:deep(.entry.bpmn-icon-start-event-none::after) {
  content: "开始事件";
  font-size: 14px;
  position: absolute;
  left: 45px;
  white-space: nowrap;
  height: 46px;
  line-height: 46px;
}
.entry.bpmn-icon-start-event-none:hover::after {
  color: #40a9ff;
}

:deep(.djs-palette-entries .entry.bpmn-icon-intermediate-event-none::after) {
  content: "中间/边界事件";
  font-size: 14px;
  position: absolute;
  left: 45px;
  white-space: nowrap;
  height: 46px;
  line-height: 46px;
}
.entry.bpmn-icon-intermediate-event-none:hover::after {
  color: #40a9ff;
}

:deep(.djs-palette-entries .entry.bpmn-icon-end-event-none::after) {
  content: "结束事件";
  font-size: 14px;
  position: absolute;
  left: 45px;
  white-space: nowrap;
  height: 46px;
  line-height: 46px;
}
.entry.bpmn-icon-end-event-none:hover::after {
  color: #40a9ff;
}

:deep(.djs-palette-entries .entry.bpmn-icon-gateway-none::after) {
  content: "网关";
  font-size: 14px;
  position: absolute;
  left: 45px;
  white-space: nowrap;
  height: 46px;
  line-height: 46px;
}
.entry.bpmn-icon-gateway-none:hover::after {
  color: #40a9ff;
}

:deep(.entry.bpmn-icon-subprocess-expanded::after) {
  content: "子流程";
  font-size: 14px;
  position: absolute;
  left: 45px;
  white-space: nowrap;
  height: 46px;
  line-height: 46px;
}
.entry.bpmn-icon-subprocess-expanded:hover::after {
  color: #40a9ff;
}

:deep(.djs-palette-entries .entry.bpmn-icon-task::after) {
  content: "任务";
  font-size: 14px;
  position: absolute;
  left: 45px;
  white-space: nowrap;
  height: 46px;
  line-height: 46px;
}
.entry.bpmn-icon-task:hover::after {
  color: #40a9ff;
}

:deep(.djs-palette-entries .entry.bpmn-icon-data-store::after) {
  content: "数据库";
  font-size: 14px;
  position: absolute;
  left: 45px;
  white-space: nowrap;
  height: 46px;
  line-height: 46px;
}

:deep(.djs-palette-entries .entry.bpmn-icon-participant::after) {
  content: "泳道";
  font-size: 14px;
  position: absolute;
  left: 45px;
  white-space: nowrap;
  height: 46px;
  line-height: 46px;
}

:deep(.djs-palette-entries .entry.bpmn-icon-group::after) {
  content: "分组框";
  font-size: 14px;
  position: absolute;
  left: 45px;
  white-space: nowrap;
  height: 46px;
  line-height: 46px;
}

:deep(.entry.bpmn-icon-hand-tool),
:deep(.entry.bpmn-icon-space-tool),
:deep(.entry.bpmn-icon-intermediate-event-none),
:deep(.entry.bpmn-icon-data-object) {
  display: none;
}
</style>
