package com.sbtr.workflow.service;

import cn.ewsd.common.utils.easyui.PageParam;
import cn.ewsd.common.utils.easyui.PageSet;
import com.sbtr.workflow.model.*;
import com.sbtr.workflow.vo.TaskVo;
import org.apache.ibatis.annotations.Param;
import org.flowable.ui.modeler.domain.AbstractModel;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 原始的流程变量接口
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-08-16 17:35:23
 */
public interface MyVariableService extends WorkflowBaseService<MyVariable, String> {

    PageSet<MyVariable> getPageSet(PageParam pageParam, String filterStr);

    List<MyVariable> getListByProcessKey(String processkey);

    String getVariable(String processKey, String variableName);

    List<MyVariable> getVariables(String processKey);

    Integer saveGenerate(String key);

    List<User> getListByUserNamdIdOrUserName(String filter);

    List<Organization> getListByText(String filter);

    List<AbstractModel> getModelsByModelType(String filterSort, int modelType);

    Integer updateAssigneeById(String userNameId, String id);

    PageSet<TaskVo> gettoDoWithPageSetData(PageParam pageParam, String filterStr, String userNameId, String processDefinitionName);

    List<Map<String, Object>> getListBydisActivityId(String disActivityId, String processInstanceId);

    List<Map<String, Object>> getListBydisEndTime(String endTime, String processInstanceId);

    Integer deleteRunActinstsByIds(List<String> runActivityIds);

    Integer deleteHisActinstsByIds(List<String> runActivityIds);

    List<Map<String, Object>> getTitle(String processInstanceId);

}
