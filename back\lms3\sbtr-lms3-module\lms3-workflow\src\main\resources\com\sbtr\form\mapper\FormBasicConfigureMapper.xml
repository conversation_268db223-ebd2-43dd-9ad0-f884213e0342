<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sbtr.form.mapper.FormBasicConfigureMapper">

    <!-- refid id 引用   使用get_dic_item_text 函数可以显示字典text -->
    <sql id="Base_Column_List">
        uuid,
        create_time,
        creator,
        creator_id,
        creator_org_id,
        modifier_id,
        modifier,
        modify_time,
        formname,
        code,
        sortby,
        status,
        formtype,
        module_uuid,
        formversion,
        major_version
    </sql>
    <update id="updateStatusByUuid">
        update form_basic_configure
        set status=#{status}
        where uuid = #{uuid}
    </update>

    <!-- 获取分页集 -->
    <select id="getPageSetMySql" resultType="com.sbtr.form.model.FormBasicConfigure">
        select
        <include refid="Base_Column_List"></include>
        from form_basic_configure
        <where>
            major_version = 'true' and

            <if test=" name != null and  name!=''">
                name like concat('%',#{name},'%') and
            </if>
            <if test=" puuid != null and  puuid!=''">
                module_uuid = #{puuid} and
            </if>
            <if test="userNameId != null and ''!=userNameId">
                creator_id = #{userNameId} and
            </if>
            <if test="filterSort != null">
                ${filterSort}
            </if>
        </where>
    </select>

    <select id="getListDataByCode" resultType="com.sbtr.form.model.FormBasicConfigure">
        select
            uuid,
            create_time,
            creator,
            creator_id,
            creator_org_id,
            modifier_id,
            modifier,
            modify_time,
            formname,
            code,
            sortby,
            status,
            formtype,
            module_uuid,
            formdesc,
            formversion,
            major_version
        from form_basic_configure
        where code=#{code}
    </select>

    <!-- 批量删除 -->
    <delete id="executeDeleteBatch">
        delete from form_basic_configure where uuid in
        <foreach item="uuid" collection="array" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </delete>

    <select id="selectByCode" resultType="com.sbtr.form.model.FormBasicConfigure">
        select
        <include refid="Base_Column_List"></include>
        from form_basic_configure
        where code=#{code}
    </select>

    <select id="getColumnsByTableNameOracle" resultType="com.sbtr.form.vo.FormTableFieldVo">
        SELECT
            TABLE_NAME as model,
            COLUMN_NAME as columnName
        FROM
            user_tab_columns
        WHERE
            table_name = #{tableName}
    </select>

    <select id="getColumnsByTableNameSqlServer" resultType="com.sbtr.form.vo.FormTableFieldVo">
        SELECT
            #{tableName} AS model,
            name AS columnName
        FROM
            sys.columns
        WHERE
            object_id=object_id(#{tableName})
    </select>

    <select id="getColumnsByTableNameMySql" resultType="com.sbtr.form.vo.FormTableFieldVo">
        SELECT
            TABLE_NAME as model,COLUMN_NAME as columnName
        FROM
            information_schema.COLUMNS
        WHERE
                TABLE_SCHEMA = (
                SELECT DATABASE
                           ())
          AND TABLE_NAME = #{tableName}
    </select>

    <select id="getColumnsByTableNameKingBase" resultType="com.sbtr.form.vo.FormTableFieldVo">
        SELECT
            COLUMN_NAME
        FROM
            information_schema.COLUMNS
        WHERE
            table_name = #{tableName}
    </select>

    <select id="getMaxVersionByCode" resultType="java.lang.Integer">
        select
        max(formversion)
        from form_basic_configure
        where code=#{code}
    </select>


    <update id="updateMajorVersionByCode">
        update form_basic_configure
        set major_version=#{aFalse}
        where code = #{code}
    </update>

    <select id="getFatherIds" parameterType="map" statementType="CALLABLE" resultType="java.lang.String">
        {
            call p_get_father_ids(
                #{p1,mode=IN,jdbcType=NUMERIC},
                #{p2,mode=IN,jdbcType=NUMERIC},
                #{p3,mode=IN,jdbcType=NUMERIC},
                #{p4,mode=OUT,jdbcType=VARCHAR}
            )
            }
    </select>

    <!-- 获取分页集 -->
    <select id="getPageSetSqlServer" resultType="com.sbtr.form.model.FormBasicConfigure">
        select
        *
        from form_basic_configure
        <where>
            major_version = 'true' and
            <if test=" name != null and  name!=''">
                formname like '%'+#{name}+'%' and
            </if>
            <if test=" puuid != null and  puuid!=''">
                module_uuid = #{puuid} and
            </if>
            <if test="userNameId != null and ''!=userNameId">
                creator_id = #{userNameId} and
            </if>
            <if test="filterSort != null">
                ${filterSort}
            </if>
            ORDER BY sort+0 asc
        </where>
    </select>

    <!-- Oracle获取分页集 -->
    <select id="getPageSetOracle" resultType="com.sbtr.form.model.FormBasicConfigure">
        select
        *
        from form_basic_configure
        <where>
            major_version = 'true' and
            <if test=" name != null and  name!=''">
                formname like '%'||#{name}||'%' and
            </if>
            <if test=" puuid != null and  puuid!=''">
                module_uuid = #{puuid} and
            </if>
            <if test="userNameId != null and ''!=userNameId">
                creator_id = #{userNameId} and
            </if>
            <if test="filterSort != null">
                ${filterSort}
            </if>
        </where>
    </select>

    <select id="getDetailByUuidOracle" resultType="com.sbtr.form.model.FormBasicConfigure">
        SELECT
            uuid,
            creator_id,
            creator,
            create_time,
            modifier_id,
            modifier,
            modify_time,
            creator_org_id,
            formname,
            code,
            sortby,
            status,
            formtype,
            module_uuid,
            formdesc,
            formversion,
            major_version,
            tenant_uuid
        FROM
            form_basic_configure
        WHERE
            uuid = #{uuid}
    </select>

    <select id="getDetailByUuidSqlServer" resultType="com.sbtr.form.model.FormBasicConfigure">
        SELECT
            uuid,
            creator_id,
            creator,
            create_time,
            modifier_id,
            modifier,
            modify_time,
            creator_org_id,
            formname,
            code,
            sortby,
            status,
            formtype,
            module_uuid,
            formdesc,
            formversion,
            major_version,
            tenant_uuid
        FROM
            form_basic_configure
        WHERE
            uuid = #{uuid}
    </select>


</mapper>
