package com.lms.system.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@TableName("p_seriescode")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "流水编码对象")
public class Seriescode extends BaseModel {

	@TableId(type= IdType.ASSIGN_ID)
	@ApiModelProperty(value = "流水编码唯一标识")
	private String id;

	@ApiModelProperty(value = "当前编码")
	private Integer currentno;

	@ApiModelProperty(value = "编码前缀")
	private String nokey;

	@ApiModelProperty(value = "编码日期")
	private String day;

	@ApiModelProperty(value = "单位编码")
	private String deptcode;
}
