package com.lms.common.model;

import com.lms.common.feign.dto.Person;
import com.lms.common.util.ContextUtil;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({java.lang.annotation.ElementType.METHOD})
@Documented
public @interface DataFilter
{
//  String[] filterNames() default {""};
//  Object[] filterValues() default {""};
    Class<?>[] ignoreModel() default {Person.class};
}
