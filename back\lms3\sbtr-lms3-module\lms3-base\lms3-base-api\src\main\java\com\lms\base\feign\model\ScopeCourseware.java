package com.lms.base.feign.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lms.common.model.BaseModel;
import com.lms.common.model.SearchContent;
import com.lms.common.model.SearchName;
import com.lms.system.feign.model.Attach;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * scopecourseware entity. <AUTHOR> Persistence Tools
 */

@EqualsAndHashCode(callSuper = true)

@TableName("U_HELPFILE")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "scorm课件实体类")
public class ScopeCourseware extends BaseModel {

    @TableId(type= IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    @SearchName
    private String id;

    @ApiModelProperty("文件名称")
    @SearchContent
    private String filename;

    @ApiModelProperty("附件id")
    private String attach;

    @ApiModelProperty("文件描述")
    @SearchContent
    private String filedesc;

    @ApiModelProperty("创建人id")
    private String creator;

    @ApiModelProperty("创建人姓名")
    @SearchContent
    private String creatorname;

    @ApiModelProperty("创建时间")
    private String createdate;

    @ApiModelProperty("状态")
    private Integer status;

    @TableField(exist = false)
    @ApiModelProperty("附件列表")
    private List<Attach> attachList;

}
