package com.sbtr.workflow.dto;


/**
 * 解析每个节点所对应的按钮
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-03-02 17:03:24
 */
public class FormBtnListDto {

    /**
     * actDeModelKey : process1603779410654
     * nodeButtonName : 同意
     * nodeButtonCode : agree
     * nodeId : Activity_0hwqgyk
     */

    private String actDeModelKey;
    private String nodeButtonName;
    private String nodeButtonCode;
    private String id;
    //节点表单路径
    private String nodeFormPath;
    //节点表单获取详情的路径
    private String nodeFormEditPath;
    private String nodeFormSavePath;
    //当是自己手写表单的时候是否可以编辑 1可以 2不可以
    private String whetherUpdate;
    //更新接口地址
    private String nodeFormUpdatePath;

    public String getNodeFormSavePath() {
        return nodeFormSavePath;
    }

    public void setNodeFormSavePath(String nodeFormSavePath) {
        this.nodeFormSavePath = nodeFormSavePath;
    }

    public String getNodeFormUpdatePath() {
        return nodeFormUpdatePath;
    }

    public void setNodeFormUpdatePath(String nodeFormUpdatePath) {
        this.nodeFormUpdatePath = nodeFormUpdatePath;
    }

    public String getWhetherUpdate() {
        return whetherUpdate;
    }

    public void setWhetherUpdate(String whetherUpdate) {
        this.whetherUpdate = whetherUpdate;
    }
    public String getNodeFormEditPath() {
        return nodeFormEditPath;
    }

    public void setNodeFormEditPath(String nodeFormEditPath) {
        this.nodeFormEditPath = nodeFormEditPath;
    }

    public String getNodeFormPath() {
        return nodeFormPath;
    }

    public void setNodeFormPath(String nodeFormPath) {
        this.nodeFormPath = nodeFormPath;
    }

    public String getActDeModelKey() {
        return actDeModelKey;
    }

    public void setActDeModelKey(String actDeModelKey) {
        this.actDeModelKey = actDeModelKey;
    }

    public String getNodeButtonName() {
        return nodeButtonName;
    }

    public void setNodeButtonName(String nodeButtonName) {
        this.nodeButtonName = nodeButtonName;
    }

    public String getNodeButtonCode() {
        return nodeButtonCode;
    }

    public void setNodeButtonCode(String nodeButtonCode) {
        this.nodeButtonCode = nodeButtonCode;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
