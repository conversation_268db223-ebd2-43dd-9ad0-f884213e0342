package com.lms.examine.examinesubject.service;

import com.lms.common.service.BaseService;
import com.lms.examine.feign.model.Examinesubject;

import java.sql.SQLException;
import java.util.List;

public interface ExaminesubjectService extends BaseService<Examinesubject> {

    List<Examinesubject> getValidExaminesubjects();

    List<Examinesubject> getSubjectByExamineId(String examineid);

    List<Examinesubject> getExaminesubjectsByExamType(String examineid, String subjecttype);

    List<Examinesubject> getExaminesubjectById(String examineid, int titledisturb) throws SQLException;

    List<Examinesubject> getExaminesubjectsBySubjectId(String examineid, String subjectid);

    List<Examinesubject> isExistBySubjectId(String subjectId);
}
